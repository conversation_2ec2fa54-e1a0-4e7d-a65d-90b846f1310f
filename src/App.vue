<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>

export default {
  name: 'App',
  created() {
    // this.getAllTranslation()
  },
  mounted() {
    // this.reloadListener()
  },
  methods: {
    // getAllTranslation() {
    //   const locale = getLanguage()
    //   const element = locale === 'zh' ? elementZhLocale : elementEnLocale
    //   const vxe = locale === 'zh' ? zhCN : enUS
    //   getTranslationLabelList({
    //     locale
    //   }).then(res => {
    //     this.$i18n.setLocaleMessage(locale, {
    //       ...res.data,
    //       ...element,
    //       ...vxe
    //     })
    //   })
    // }
    // reloadListener() {
    //   // 刷新页面前存入companyId
    //   window.addEventListener('beforeunload', (e) => {
    //     this.$store.commit('SET_COMPANYID', this.$store.getters.companyId)
    //   })
    // }

  },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  }
}
</script>
<style>
/*tinymce与el-dialog层级覆盖*/
.tox-tinymce-aux {
  z-index: 9999 !important;
}
</style>
