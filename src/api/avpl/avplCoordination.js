import request from '@/utils/request'

export function getAVPLDataList(data) {
  return request({
    url: '/avpl/materials/page',
    method: 'post',
    data
  })
}
export function advancedSearchDetailPage(data) {
  return request({
    url: '/avpl/materials/advancedSearchDetailPage',
    method: 'post',
    data
  })
}

export function getAVPLMaterialAllIds(params) {
  return request({
    url: '/avpl/materials/get-avpl-material-Ids',
    method: 'get',
    params
  })
}

export function getAVPLLineGraph(params) {
  return request({
    url: '/avpl/materials/line-graph',
    method: 'get',
    params
  })
}

export function getAVPLMaterialPage(params) {
  return request({
    url: '/avpl/materials/material-page',
    method: 'get',
    params
  })
}

export function createMaterialsForRFQ(data) {
  return request({
    url: '/avpl/materials/create-rfq',
    method: 'post',
    data
  })
}
export function refreshHistoryHash(data) {
  return request({
    url: '/avpl/materials/refresh-hash',
    method: 'post',
    data
  })
}
// 下载AVPL浏览数据
export function exportHeaderExcel(data) {
  return request({
    url: '/avpl/materials/export-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 获取Bu总览
export function getBuOverview() {
  return request({
    url: '/avpl/materials/getBuOverview',
    method: 'get'
  })
}

// 获取品类总览
export function getCategoryOverview() {
  return request({
    url: '/avpl/materials/getCategoryOverview',
    method: 'get'
  })
}

// 获取制造商总览
export function getMfgOverview() {
  return request({
    url: '/avpl/materials/getMfgOverview',
    method: 'get'
  })
}
// 高级搜索分页
export function getAdvancedSearchPage(data) {
  return request({
    url: '/avpl/materials/getAdvancedSearchPage',
    method: 'post',
    data
  })
}
