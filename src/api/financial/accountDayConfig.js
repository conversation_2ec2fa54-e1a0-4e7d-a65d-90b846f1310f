import request from '@/utils/request'

// 更新财务协同-账期信息配置
export function saveAccountDayConfig(data) {
  return request({
    url: '/financial/account-day-config/save',
    method: 'put',
    data: data
  })
}

// 获得财务协同-账期信息配置分页
export function getAccountDayConfigPage(query) {
  return request({
    url: '/financial/account-day-config/page',
    method: 'get',
    params: query
  })
}

// 导出财务协同-账期信息配置 Excel
export function exportAccountDayConfigExcel(query) {
  return request({
    url: '/financial/account-day-config/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
