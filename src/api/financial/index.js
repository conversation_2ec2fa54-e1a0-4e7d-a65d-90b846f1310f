import request from '@/utils/request'

export function saveDiffHeadRecord(data) {
  return request({
    url: '/financial/record/head/save',
    method: 'post',
    data
  })
}
export function getFinancialDetail(data) {
  return request({
    url: '/financial/detail/page',
    method: 'post',
    data
  })
}
export function getFinancialDetailRecord(params) {
  return request({
    url: '/financial/record/get',
    method: 'get',
    params
  })
}

export function getFinancialRecord(data) {
  return request({
    url: '/financial/record/page',
    method: 'post',
    data
  })
}

export function getListInventory(data) {
  return request({
    url: '/financial/detail/listInventory',
    method: 'post',
    data
  })
}

export function getFinancialDetailPage(data) {
  return request({
    url: '/financial/detail/page',
    method: 'post',
    data
  })
}

export function saveFinancialAdd(data) {
  return request({
    url: '/financial/detail/save',
    method: 'post',
    data
  })
}

export function updateFinancialTax(data) {
  return request({
    url: '/financial/detail/update',
    method: 'post',
    data
  })
}

export function saveFinancialRecord(data) {
  return request({
    url: '/financial/record/save',
    method: 'post',
    data
  })
}
export function submitFinancialRecord(data) {
  return request({
    url: '/financial/record/submit',
    method: 'put',
    data
  })
}

export function rejectFinancialRecord(data) {
  return request({
    url: '/financial/record/reject',
    method: 'put',
    data
  })
}

export function deleteFinancialDetail(data) {
  return request({
    url: '/financial/detail/delete',
    method: 'delete',
    data
  })
}

export function cancelFinancialRecord(data) {
  return request({
    url: '/financial/record/cancel',
    method: 'delete',
    data
  })
}

export function delInvoiceInfo(params) {
  return request({
    url: '/financial/invoice-info/delete',
    method: 'delete',
    params
  })
}

export function exportExcelRecord(data) {
  return request({
    url: '/financial/record/export-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
export function exportExcelDetailRecord(params) {
  return request({
    url: '/financial/detail/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function downloadRecordRecord(params) {
  return request({
    url: '/financial/record/download-record',
    method: 'get',
    params
  })
}
export function downloadTemplate(params) {
  return request({
    url: '/financial/invoice-info/get-import-template',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function callOaAndUpdateOaPayResult(params) {
  return request({
    url: '/financial/record/call-oa-pay',
    method: 'post',
    params
  })
}

export function getStatic() {
  return request({
    url: '/financial/record/statisticians',
    method: 'get'
  })
}
