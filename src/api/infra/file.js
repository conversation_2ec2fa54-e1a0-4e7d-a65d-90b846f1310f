import request from '@/utils/request'

// 删除文件
export function deleteFile(id) {
  return request({
    url: '/infra/file/delete?id=' + id,
    method: 'delete'
  })
}

// 获得文件分页
export function getFilePage(query) {
  return request({
    url: '/infra/file/page',
    method: 'get',
    params: query
  })
}
// 根据文件id获取文件详情
export function getFileDetailByIds(query) {
  return request({
    url: `/infra/file/get-by-ids`,
    method: 'post',
    params: query
  })
}
// 获得文件分页
export function uploadFile(query) {
  return request({
    url: '/infra/file/upload',
    method: 'post',
    params: query
  })
}

export function zipByIds(query) {
  return request({
    url: '/infra/file/zip-by-ids',
    method: 'get',
    params: query,
    responseType: 'blob' // 下载文件
  })
}
