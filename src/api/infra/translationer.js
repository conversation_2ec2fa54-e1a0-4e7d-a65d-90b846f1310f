import request from '@/utils/request'

// 创建品类
export function replaceI18n(data) {
  return request({
    url: '/infra/translator/replaceTranslator',
    method: 'post',
    data: data
  })
}
export function syncSystemTranslation() {
  return request({
    url: '/infra/translator/syncSystemTranslation',
    method: 'get',
  })
}

export function updateScore(index, score) {
  return request({
    url: '/invoke/websocket/updateScore?index=' + index + '&score=' + score,
    method: 'post',
    data: {}
  })
}
