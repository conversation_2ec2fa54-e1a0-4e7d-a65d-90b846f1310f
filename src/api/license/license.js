import request from '@/utils/request'

// 生成证书
export function generateLicense(data) {
  return request({
    url: '/license/generate-license',
    method: 'post',
    data: data
  })
}

// 获取文件
export function getLicenseFileItem() {
  return request({
    url: '/license/get-license-file-item',
    method: 'get'
  })
}

// 下载证书
export function getLicensFile() {
  return request({
    url: '/license/get-license-file',
    method: 'get'
  })
}

// 上传证书文件
export function uploadLicenseFile(data) {
  return request({
    url: '/license/upload-license-file',
    method: 'post',
    data: data
  })
}

// 删除证书文件
export function deleteLicenseFile(params) {
  return request({
    url: '/license/delete-license-file',
    method: 'delete',
    params
  })
}

// 安装证书
export function installLicense() {
  return request({
    url: '/license/install-license',
    method: 'post'
  })
}

// 获取证书有效期
export function getLicensFileValidityDate() {
  return request({
    url: '/license/get-license-file-validity-date',
    method: 'get'
  })
}
