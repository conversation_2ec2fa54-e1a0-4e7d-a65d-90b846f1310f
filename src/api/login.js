import request from '@/utils/request'
import service from '@/utils/request'
import { getRefreshToken } from '@/utils/auth'
import store from '@/store'

// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid
  }
  return request({
    url: '/system/auth/login',
    method: 'post',
    data: data
  })
}

// 修改密码
export function updatePasswordWithExpired(data) {
  return request({
    url: '/system/auth/updatePasswordWithExpired',
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: `/system/auth/get-permission-info?locale=${store.getters.language}`,
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/system/auth/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/system/captcha/get-image',
    method: 'get',
    timeout: 20000
  })
}

// 社交授权的跳转
export function socialAuthRedirect(type, redirectUri) {
  return request({
    url: '/system/auth/social-auth-redirect?type=' + type + '&redirectUri=' + redirectUri,
    method: 'get'
  })
}

// 社交快捷登录，使用 code 授权码
export function socialQuickLogin(type, code, state) {
  return request({
    url: '/system/auth/social-quick-login',
    method: 'post',
    data: {
      type,
      code,
      state
    }
  })
}

// 社交绑定登录，使用 code 授权码 + + 账号密码
export function socialBindLogin(type, code, state, username, password) {
  return request({
    url: '/system/auth/social-bind-login',
    method: 'post',
    data: {
      type,
      code,
      state,
      username,
      password
    }
  })
}

// 获取登录验证码
export function sendSmsCode(mobile, scene) {
  return request({
    url: '/system/auth/send-sms-code',
    method: 'post',
    data: {
      mobile,
      scene
    }
  })
}

// 短信验证码登录
export function smsLogin(mobile, code) {
  return request({
    url: '/system/auth/sms-login',
    method: 'post',
    data: {
      mobile,
      code
    }
  })
}

// 刷新访问令牌
export function refreshToken() {
  return service({
    url: '/system/auth/refresh-token?refreshToken=' + getRefreshToken(),
    method: 'post'
  })
}

// 管理员模拟账户
export function mockAccount(userId) {
  return service({
    url: '/system/auth/mock-account?userId=' + userId,
    method: 'get'
  })
}

export function getWxMpBindCode(userId) {
  return service({
    url: `/system/auth/getWxMpBindCode?userId=${userId}`,
    method: 'get'
  })
}

export function getWxMpCode(uuid) {
  return service({
    url: `/system/auth/getWxMpCode?scene_id=weixinlogin==` + uuid,
    method: 'get'
  })
}

export function checkWxBind(userId) {
  return service({
    url: `/system/auth/checkWxBind?userId=` + userId,
    method: 'get'
  })
}

export function checkWxLogin(uuid) {
  return service({
    url: `/system/auth/scan_callback?scene_id=` + uuid,
    method: 'get'
  })
}

// ========== OAUTH 2.0 相关 ==========

export function getAuthorize(clientId) {
  return request({
    url: '/system/oauth2/authorize?clientId=' + clientId,
    method: 'get'
  })
}

//  雪融登陆页面成功之后返回的token进行查询人员信息，并返回人员所属的srm token
export function getSrmTokenForXuerong(xrToken) {
  return request({
    url: '/system/oauth2/srm/token?xrToken=' + xrToken,
    method: 'get'
  })
}

/**
 * 根据用户工号获取OA访问地址
 *
 * TODO 模拟测试，当前OA接口无法调通。后续使用该请求方式获取oa地址。前端通过button方式跳转即可
 * @param userCode 用户工号
 * @returns {*}
 */
export function getOAUrl(userCode) {
  return request({
    url: '/system/oauth2/oaUrl?userCode=' + userCode,
    method: 'get'
  })
}

export function authorize(responseType, clientId, redirectUri, state,
  autoApprove, checkedScopes, uncheckedScopes) {
  // 构建 scopes
  const scopes = {}
  for (const scope of checkedScopes) {
    scopes[scope] = true
  }
  for (const scope of uncheckedScopes) {
    scopes[scope] = false
  }
  // 发起请求
  return service({
    url: '/system/oauth2/authorize',
    headers: {
      'Content-type': 'application/x-www-form-urlencoded'
    },
    params: {
      response_type: responseType,
      client_id: clientId,
      redirect_uri: redirectUri,
      state: state,
      auto_approve: autoApprove,
      scope: JSON.stringify(scopes)
    },
    method: 'post'
  })
}
