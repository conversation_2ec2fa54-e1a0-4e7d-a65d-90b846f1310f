import request from '@/utils/request'

export function saveMaterial(data) {
  return request({
    url: '/material/material/create',
    method: 'post',
    data: data
  })
}

export function updateMaterial(data) {
  return request({
    url: '/material/material/update',
    method: 'put',
    data: data
  })
}

export function getMaterial(params) {
  return request({
    url: '/material/material/get',
    method: 'get',
    params
  })
}

// 创建物料基本信息主
export function create(data) {
  return request({
    url: '/material/material/create',
    method: 'post',
    data: data
  })
}

// 更新物料基本信息主
export function update(data) {
  return request({
    url: '/material/material/update',
    method: 'put',
    data: data
  })
}

// 获得物料基本信息主
export function get(id) {
  return request({
    url: '/material/material/get?id=' + id,
    method: 'get'
  })
}

// 获得物料基本信息主分页
export function getPage(data) {
  return request({
    url: '/material/material/page',
    method: 'post',
    data
  })
}

export function delMaterial(params) {
  return request({
    url: '/material/material/delete',
    method: 'delete',
    params
  })
}

// 获得物料基本信息主分页
export function putMaterial(data) {
  return request({
    url: '/material/material/update-status',
    method: 'put',
    data
  })
}
// 导出物料基本信息主 Excel
export function exportExcel(data) {
  return request({
    url: '/material/material/export-excel',
    method: 'post',
    data,
    responseType: 'blob',
    timeout: 600000
  })
}

// 下载物料导入模板
export function importTemplate() {
  return request({
    url: '/material/material/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
export function getMaterialInfo(params) {
  return request({
    url: '/material/material/get-materialInfo',
    method: 'get',
    params
  })
}
export function getMaterialMFGInfo(params) {
  return request({
    url: '/om/avpl-master/list-part',
    method: 'get',
    params
  })
}
