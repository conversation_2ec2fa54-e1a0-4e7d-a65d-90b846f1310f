//保存简版供应商基础信息
import request from "@/utils/request";

//save avpl info
export function saveAvplInfo(data) {
  return request({
    url: '/om/avpl-master/save',
    method: 'post',
    data: data
  })
}

// del avpl support batch
export function delAvplInfo(query) {
  return request({
    url: '/om/avpl-master/del',
    method: 'delete',
    params: query
  })
}

//get avpl info
export function getAvplInfo(id) {
  return request({
    url: '/om/avpl-master/get?id=' + id,
    method: 'get'
  })
}


//get avpl data pagination
export function getAvplPage(data) {
  return request({
    url: '/om/avpl-master/page',
    method: 'post',
    data: data
  })
}

// export avpl data
export function exportAvplInfoExcel(data) {
  return request({
    url: '/om/avpl-master/export-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}


// 获得简版供应商导入模板
export function getImportAvplTemplate() {
  return request({
    url: '/om/avpl-master/export-template',
    method: 'get',
    responseType: 'blob'
  })
}
