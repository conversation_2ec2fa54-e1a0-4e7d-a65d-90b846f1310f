import request from '@/utils/request'

// 获得收支平衡字列表
export function getCashBalanceDetailList(params) {
  return request({
    url: '/om/cash-balance-detail/list-by-BalanceId',
    method: 'get',
    params
  })
}

// 获得收支平衡字分页
export function getCashBalanceDetailPage(params) {
  return request({
    url: '/om/cash-balance-detail/page',
    method: 'get',
    params: params
  })
}

// 导出收支平衡字 Excel
export function exportCashBalanceDetailExcel(params) {
  return request({
    url: '/om/cash-balance-detail/export-excel',
    method: 'get',
    params: params,
    responseType: 'blob' // 确保 Excel 文件以二进制流的形式返回
  })
}

// 获得资金收支平衡主
export function getCashBalanceMaster(params) {
  return request({
    url: '/om/cash-balance-master/get',
    method: 'get',
    params: params
  })
}

// 获得现金流水列表分页
export function getCashBalanceMasterPage(params) {
  return request({
    url: '/om/cash-balance-master/page',
    method: 'post',
    params
  })
}

// 导出现金流水列表 Excel
export function exportCashBalanceMasterExcel(params) {
  return request({
    url: '/om/cash-balance-master/export-excel',
    method: 'get',
    params: params,
    responseType: 'blob' // 确保 Excel 文件以二进制流的形式返回
  })
}

// 获得资金类型统计
export function getCashStatistics() {
  return request({
    url: '/om/cash-balance-master/get-cash-statistics',
    method: 'get'
  })
}

// 创建资金收支平衡主
export function saveCashBalanceMaster(data) {
  return request({
    url: '/om/cash-balance-master/save',
    method: 'post',
    data: data
  })
}

// 获得应付账款的明细分页
export function getAccountsPayableDetailPage(params) {
  return request({
    url: '/om/accounts-payable-detail/page',
    method: 'get',
    params: params
  })
}

// 导出应付账款的明细数据 Excel
export function exportAccountsPayableDetailExcel(params) {
  return request({
    url: '/om/accounts-payable-detail/export-excel',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}

// 获得应付账款的发票分页
export function getAccountsPayableInvoicePage(params) {
  return request({
    url: '/om/accounts-payable-invoice/page',
    method: 'get',
    params: params
  })
}

// 创建应付款
export function createAccountsPayableMaster(data) {
  return request({
    url: '/om/accounts-payable-master/create',
    method: 'post',
    data: data
  })
}

// 更新AP单付款状态
export function updateAccountsPayableMaster(data) {
  return request({
    url: '/om/accounts-payable-master/update',
    method: 'put',
    data: data
  })
}

// 获得应付款
export function getAccountsPayableMaster(params) {
  return request({
    url: '/om/accounts-payable-master/get',
    method: 'get',
    params: params
  })
}

// 获得应付款分页
export function getAccountsPayableMasterPage(data) {
  return request({
    url: '/om/accounts-payable-master/page',
    method: 'post',
    data
  })
}

// 导出应付款模板
export function exportAccountsPayableTemplate() {
  return request({
    url: '/om/accounts-payable-master/export-ap-template',
    method: 'get',
    responseType: 'blob'
  })
}

// 获得应付账款的供应商分页
export function getAccountsPayableSupplierPage(params) {
  return request({
    url: '/om/accounts-payable-supplier/page',
    method: 'get',
    params: params
  })
}

// 上传供应商付款水单附件
export function uploadSupplierFile(data) {
  return request({
    url: '/om/accounts-payable-supplier/upload-file',
    method: 'post',
    data: data
  })
}

// 上传供应商付款水单附件
export function uploadApFile(data) {
  return request({
    url: '/om/accounts-payable-master/upload-file',
    method: 'post',
    data: data
  })
}

// 更新供应商付款状态
export function updateSupplierPaymentStatus(params) {
  return request({
    url: '/om/accounts-payable-supplier/update',
    method: 'put',
    params: params
  })
}

// 更新供应商付款状态 是否支付手续费
export function bearHandingCharge(data) {
  return request({
    url: '/om/accounts-payable-supplier/bear-handing-charge',
    method: 'put',
    data
  })
}

// 导出应付账款的供应商 Excel
export function exportAccountsPayableSupplierExcel(params) {
  return request({
    url: '/om/accounts-payable-supplier/export-excel',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}
// 应付款统计
export function accountsPayableStatistics(params) {
  return request({
    url: '/om/accounts-payable-master/accounts-payable-statistics',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}

/**
 * bulk payment
 * @param data
 * @returns {*}
 */
export function bulkPayment(data) {
  return request({
    url: '/om/accounts-payable-master/bulk-payment',
    method: 'post',
    data: data
  })
}
