import request from '@/utils/request'
// 获得OrderTracker分页
export function getOrderTrackerPage(data) {
  return request({
    url: '/om/order-tracker/page',
    method: 'post',
    data
  })
}

// update the order tracker information
export function updateOrderTracker(data) {
  return request({
    url: '/om/order-tracker/update-order-tracker',
    method: 'put',
    data: data
  })
}


// update the order tracker information
export function saveOrderTrackerUrgent(data) {
  return request({
    url: '/om/order-tracker/save-urgent',
    method: 'post',
    data: data
  })
}
// 导出OrderTracker列表
export function exportOrderTracker(data) {
  return request({
    url: '/om/order-tracker/export-order-tracker',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

export function exportMaintainDeliveryDate(data) {
  return request({
    url: '/om/order-tracker/export-maintain-delivery-date',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

export function exportMaintainPayment(data) {
  return request({
    url: '/om/order-tracker/export-maintain-payment',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 删除供应商文档库
export function deleteOrderTrackerData(params) {
  return request({
    url: '/om/order-tracker/delete-order-tracker-data',
    method: 'delete',
    params
  })
}
export function getOrderTrackerStatistics(param) {
  return request({
    url: '/om/order-tracker/get-statistics?customer='+param,
    method: 'get'
  })
}
// 下载供应商导入模板
export function exportNewOrderTracker() {
  return request({
    url: '/om/order-tracker/export-new-order-tracker',
    method: 'get',
    responseType: 'blob'
  })
}
export function getSearchResult(params) {
  return request({
    url: '/om/order-tracker/get-search-result',
    method: 'get',
    params
  })
}

export function getOrderStatistics(params) {
  return request({
    url: '/om/order-tracker/get-order-statistics',
    method: 'get',
    params
  })
}
export function getConfirmedById(params) {
  return request({
    url: '/om/po-detail/get-confirmed-by-id',
    method: 'get',
    params
  })
}
export function saveConfirmed(data) {
  return request({
    url: '/om/po-detail/save-confirmed',
    method: 'post',
    data
  })
}
// 导出资金预测excel
export function exportForecast(data) {
  return request({
    url: '/om/order-tracker/financial-forecast',
    method: 'post',
    data,
    responseType: 'blob'
  })
}


// 获取 Total Value 数据
export function getOrderStatisticsTotalValue() {
  return request({
    url: '/om/order-tracker/get-total-value',
    method: 'get'
  })
}

// 导出 Total Value Excel
export function exportTotalValueExcel() {
  return request({
    url: '/om/order-tracker/export-total-value-excel',
    method: 'get',
    responseType: 'blob'
  })
}

// 获取 Top Supplier Spend 分页数据
export function getTopSupplierSpendPage(data) {
  return request({
    url: '/om/order-tracker/page-top-supplier-spend',
    method: 'post',
    data
  })
}

// 导出 Top Supplier Spend Excel
export function exportTopSupplierSpendExcel() {
  return request({
    url: '/om/order-tracker/export-top-supplier-spend-excel',
    method: 'get',
    responseType: 'blob'
  })
}

// 获取 Parts Spend 分页数据
export function getPartsSpendPage(data) {
  return request({
    url: '/om/order-tracker/page-parts-spend',
    method: 'post',
    data
  })
}

// 导出 Parts Spend Excel
export function exportPartsSpendExcel() {
  return request({
    url: '/om/order-tracker/export-parts-spend-excel',
    method: 'get',
    responseType: 'blob'
  })
}
