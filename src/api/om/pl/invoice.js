// save invoice master info
import request from "@/utils/request";


export function getInvoiceMaster(id) {
  return request({
    url: '/om/invoice-master/get?invoiceId='+id,
    method: 'get'
  })
}

export function saveInvoiceMaster(reqVO) {
  return request({
    url: '/om/invoice-master/save',
    method: 'put',
    data: reqVO
  })
}

export function getInvoiceDetailPage(pageVO) {
  return request({
    url: '/om/invoice-detail/page',
    method: 'post',
    data: pageVO
  })
}

export function exportInvoiceDetailExcel(exportReqVO) {
  return request({
    url: '/om/invoice-detail/export-excel',
    method: 'post',
    data: exportReqVO,
    responseType: 'blob' // 下载文件
  })
}
