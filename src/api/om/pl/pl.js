import request from '@/utils/request'
// 获取采购订单明细分页
export function shipTrackerPage(pageVO) {
  return request({
    url: '/om/packing-list/ship-tracker-page',
    method: 'post',
    data: pageVO
  })
}

export function newPl(params) {
  return request({
    url: '/om/packing-list/new-pl',
    method: 'get',
    params: params
  })
}

export function notHk(params) {
  return request({
    url: '/om/packing-list/not-hk',
    method: 'get',
    params: params
  })
}

// 导出PL
export function exportShipTracker(pageVO) {
  return request({
    url: '/om/packing-list/export-ship-tracker',
    method: 'post',
    data: pageVO,
    responseType: 'blob' // 下载文件
  })
}
// start ship
export function startShip(plIds) {
  return request({
    url: '/om/packing-list/confirm-ship',
    method: 'get',
    params: { plIds }
  })
}
// 保存送货单信息
export function savePLMaster(reqVO) {
  return request({
    url: '/om/pl-master/save',
    method: 'post',
    data: reqVO
  })
}
// 获取销售订单
export function getPlMaster(plId) {
  return request({
    url: '/om/pl-master/get',
    method: 'get',
    params: { plId }
  })
}

// 获取装箱单供应商列表
export function getPlSupplierList(plIds) {
  return request({
    url: '/om/pl-supplier/list',
    method: 'get',
    params: { plIds } // 传递 plIds 作为查询参数
  });
}

// 添加零件
export function newParts(reqVO) {
  return request({
    url: '/om/pl-supplier/parts',
    method: 'post',
    data: reqVO // 发送请求体
  });
}

// 添加附加零件
export function newAttachParts(reqVO) {
  return request({
    url: '/om/pl-supplier/attach-parts',
    method: 'post',
    data: reqVO // 发送请求体
  });
}

// 删除零件
export function deleteParts(reqVO) {
  return request({
    url: '/om/pl-supplier/parts',
    method: 'delete',
    data: reqVO // 发送请求体
  });
}

// 发送邮件到 HUB 确认纸箱信息
export function sendToHub(params) {
  return request({
    url: '/om/shipping-plan/hub',
    method: 'get',
    params: params // 发送请求体
  });
}

// HUB receive
export function hubReceive(params) {
  return request({
    url: '/om/shipping-plan/hub-receive',
    method: 'get',
    params: params // 发送请求体
  });
}

// 保存零件信息
export function saveParts(reqVO) {
  return request({
    url: '/om/pl-supplier/save',
    method: 'post',
    data: reqVO // 发送请求体
  });
}

// seek support
export function seekSupport(reqVO) {
  return request({
    url: '/om/pl-supplier/seek-support',
    method: 'post',
    data: reqVO // 发送请求体
  });
}

// 获取订单跟踪信息
export function retrieveOrderTracker(reqVO) {
  return request({
    url: '/om/order-tracker/retrieve-order-tracker',
    method: 'post',
    data: reqVO // 发送请求体
  });
}

export function confirmEmail(reqVO) {
  return request({
    url: '/om/packing-list/confirm-email',
    method: 'post',
    data: reqVO // 发送请求体
  });
}




//export pl detail parts format excel
export function exportPlDetailExcel(exportReqVO) {
  return request({
    url: '/om/pl-detail/export-excel',
    method: 'post',
    data: exportReqVO,
    responseType: 'blob' // 下载文件
  })
}

export function deletePackingList(plIds) {
  return request({
    url: '/om/pl-master/delete',
    method: 'get',
    params: plIds // 发送请求体
  });
}


export function updateBox(reqVO) {
  return request({
    url: '/om/pl-supplier/update-box',
    method: 'post',
    data: reqVO // 发送请求体
  });
}

// get pl supplier
export function getPlSupplier(plSupplierId) {
  return request({
    url: '/om/pl-supplier/get',
    method: 'get',
    params: { plSupplierId }
  })
}


export function newPlFromDp(params) {
  return request({
    url: '/om/shipping-plan/new-pl-from-dp',
    method: 'get',
    params:  params
  })
}

export function confirmForwarder(data) {
  return request({
    url: '/om/packing-list/confirm-forwarder',
    method: 'post',
    data: data
  })
}

export function getTargetMail(plId) {
  return request({
    url: '/om/packing-list/get-mail',
    method: 'get',
    params: { plId }
  })
}


export function requiredAwb(plId) {
  return request({
    url: '/om/packing-list/required-awb',
    method: 'get',
    params: { plId }
  })
}

export function noticeStartShipping(plId) {
  return request({
    url: '/om/packing-list/notice-start-shipping',
    method: 'get',
    params: { plId }
  })
}





