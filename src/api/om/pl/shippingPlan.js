import request from "@/utils/request";

export function getShippingPlanPage(pageVO) {
  return request({
    url: '/om/shipping-plan/page',
    method: 'post',
    data: pageVO
  })
}

export function getDeliveryPlanPage(pageVO) {
  return request({
    url: '/om/shipping-plan/delivery-plan-page',
    method: 'post',
    data: pageVO
  })
}



export function exportShipping(pageVO) {
  return request({
    url: '/om/shipping-plan/export-excel',
    method: 'post',
    data: pageVO,
    responseType: 'blob' // 下载文件
  })
}

export function exportDeliveryPlan(pageVO) {
  return request({
    url: '/om/shipping-plan/export-delivery-plan-excel',
    method: 'post',
    data: pageVO,
    responseType: 'blob' // 下载文件
  })
}

export function exportPacking(pageVO) {
  return request({
    url: '/om/packing-list/export-packing-list',
    method: 'post',
    data: pageVO,
    responseType: 'blob' // 下载文件
  })
}

export function exportInvoice(pageVO) {
  return request({
    url: '/om/packing-list/export-invoice',
    method: 'post',
    data: pageVO,
    responseType: 'blob' // 下载文件
  })
}




export function newDp(params) {
  return request({
    url: '/om/shipping-plan/new-dp',
    method: 'get',
    params: params
  })
}

export function getDeliveryPlanPartsPage(pageVO) {
  return request({
    url: '/om/shipping-plan/delivery-plan-parts',
    method: 'post',
    data: pageVO
  })
}


export function getPlDeliveryPlanEntity(plId) {
  return request({
    url: '/om/shipping-plan/get',
    method: 'get',
    params: {plId}
  })
}


export function saveDelivery(reqVO) {
  return request({
    url: '/om/shipping-plan/save',
    method: 'post',
    data: reqVO
  })
}

export function exportPlDeliveryPlanDetailExcel(exportReqVO) {
  return request({
    url: '/om/shipping-plan/detail-export-excel',
    method: 'post',
    data: exportReqVO,
    responseType: 'blob' // 下载文件
  })
}


export function deleteParts(reqVO) {
  return request({
    url: '/om/shipping-plan/parts',
    method: 'delete',
    data: reqVO // 发送请求体
  });
}


