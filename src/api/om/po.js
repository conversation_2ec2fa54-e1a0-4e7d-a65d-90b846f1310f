import request from '@/utils/request'

// 导出采购订单
export function exportPoMaster(pageVO) {
  return request({
    url: '/om/po-master/export-po-master',
    method: 'post',
    data: pageVO,
    responseType: 'blob' // 下载文件
  })
}

// 获取采购订单分页
export function getPoMasterPage(pageVO) {
  return request({
    url: '/om/po-master/page',
    method: 'post',
    data: pageVO
  })
}

// 获得po号下拉
export function dropdownPoNumber(params) {
  return request({
    url: '/om/po-master/dropdown/po-number',
    method: 'get',
    params
  })
}

// 取消采购订单
export function cancelPo(poIds) {
  return request({
    url: '/om/po-master/cancel-po',
    method: 'get',
    params: { poIds }
  })
}

// 发布采购订单到供应商
export function releasePo(reqVO) {
  return request({
    url: '/om/po-master/release-po',
    method: 'post',
    data: reqVO
  })
}

// 获取释放订单需要发送的邮箱
export function getReleasePoMail(poId) {
  return request({
    url: '/om/po-master/get-release-po-mail?poId='+poId,
    method: 'get'
  })
}
// 获取采购订单信息
export function getPoMaster(id) {
  return request({
    url: '/om/po-master/get-po-info',
    method: 'get',
    params: { id }
  })
}

// 保存采购订单信息
export function savePoMaster(reqVO) {
  return request({
    url: '/om/po-master/po-master-save',
    method: 'post',
    data: reqVO
  })
}

// 获取采购订单明细分页
export function getPoDetailPage(pageVO) {
  return request({
    url: '/om/po-detail/page',
    method: 'post',
    data: pageVO
  })
}

// 导出采购订单明细 Excel
export function exportPoDetailExcel(exportReqVO) {
  return request({
    url: '/om/po-detail/export-excel',
    method: 'post',
    data: exportReqVO,
    responseType: 'blob' // 下载文件
  })
}

export function getPoDetailInfo(id) {
  return request({
    url: '/om/po-detail/get-detail?id=' + id,
    method: 'get'
  })
}

export function savePoDetailInfo(data) {
  return request({
    url: '/om/po-detail/update-detail',
    method: 'post',
    data: data
  })
}

// 获得简版供应商导入模板
export function exportPdf(id) {
  return request({
    url: '/om/po-master/pdf?poId='+id,
    method: 'get',
    responseType: 'blob'
  })
}
