import request from '@/utils/request'


// 获取采购订单分页
export function getShortMasterPage(pageVO) {
  return request({
    url: '/om/short-master/page',
    method: 'post',
    data: pageVO
  })
}

// 获取采购订单信息
export function getShortMaster(id) {
  return request({
    url: '/om/short-master/get',
    method: 'get',
    params: { id }
  })
}
// 保存采购订单信息
export function saveShortMaster(reqVO) {
  return request({
    url: '/om/short-master/save',
    method: 'post',
    data: reqVO
  })
}
// 获取采购订单明细分页
export function getShortMaterialDetailPage(pageVO) {
  return request({
    url: '/om/short-material-detail/page',
    method: 'post',
    data: pageVO
  })
}
