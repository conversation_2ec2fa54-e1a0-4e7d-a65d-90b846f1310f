import request from '@/utils/request'
import params from "@/store/modules/params";

export function exportBreakDown(data) {
  return request({
    url: '/om/so-detail/export-break-down',
    method: 'post',
    data,
    responseType: 'blob' // 下载文件
  })
}

// 获取销售订单主分页
export function getSoMasterPage(pageVO) {
  return request({
    url: '/om/so-master/page',
    method: 'post',
    data: pageVO })
}

// 导出销售订单模板
export function exportOrderTemplate() {
  return request({
    url: '/om/so-master/export-so-template',
    method: 'get',
    responseType: 'blob' // 下载文件
  })
}

// 取消销售订单
export function cancelSo(soIds) {
  return request({
    url: '/om/so-master/cancel-so',
    method: 'get',
    params: { soIds }
  })
}

// 获取销售订单明细分页
export function getSoMasterBreakDownPage(pageVO) {
  return request({
    url: '/om/so-detail/get-break-down-page',
    method: 'post',
    data: pageVO
  })
}

// 发送邮件
export function sendEmail(soDetailIds) {
  return request({
    url: '/om/so-detail/send-mail',
    method: 'get',
    params: { soDetailIds }
  })
}

// 导出订单主
export function exportOrderMaster(pageVO) {
  return request({
    url: '/om/so-master/export-order-master',
    method: 'post',
    data: pageVO,
    responseType: 'blob' // 下载文件
  })
}

// 获取销售订单
export function getSoMaster(soId) {
  return request({
    url: '/om/so-master/get',
    method: 'get',
    params: { soId }
  })
}

// 更新销售订单
export function updateSoMaster(updateReqVO) {
  return request({
    url: '/om/so-master/update',
    method: 'put',
    data: updateReqVO
  })
}

// 获取销售订单明细分页
export function getSoDetailPage(pageVO) {
  return request({
    url: '/om/so-detail/get-page',
    method: 'post',
    data: pageVO })
}

// 取消销售订单明细
export function cancelSoDetail(req) {
  return request({
    url: '/om/so-detail/so-detail-cancel',
    method: 'post',
    data: req
  })
}

// 检查 AVPL
export function checkAVPL(req) {
  return request({
    url: '/om/so-detail/check-avpl',
    method: 'post',
    data: req
  })
}

// 生成采购订单
export function generatedPO(req) {
  return request({
    url: '/om/so-detail/generated-po',
    method: 'post',
    data: req
  })
}

// 生成采购订单
export function saveRecommend(req) {
  return request({
    url: '/om/so-detail/save-recommend',
    method: 'post',
    data: req
  })
}
// 导出订单主
export function exportSoInfo(pageVO) {
  return request({
    url: '/om/so-detail/export-so-info',
    method: 'post',
    data: pageVO,
    responseType: 'blob' // 下载文件
  })
}

export function getSoDetailInfo(id) {
  return request({
    url: '/om/so-detail/get-detail?id=' + id,
    method: 'get'
  })
}

export function saveSoDetailInfo(data) {
  return request({
    url: '/om/so-detail/update-detail',
    method: 'post',
    data: data
  })
}
export function listNewSupplier(params) {
  return request({
    url: '/om/avpl-master/listNewSupplier?fuzzySearch=' + params,
    method: 'get'
  })
}
export function pagePlDetail(data) {
  return request({
    url: '/om/pl-detail/page',
    method: 'post',
    data
  })
}

export function coverSo(data) {
  return request({
    url: '/om/so-detail/cover-so',
    method: 'post',
    data: data
  })
}

export function deleteParts(soDetailIds) {
  return request({
    url: '/om/so-detail/delete-parts',
    method: 'get',
    params:{ soDetailIds }
  })
}
// 转为样品物料
export function SetSampleMaterial(req) {
  return request({
    url: '/om/so-detail/sample-material',
    method: 'post',
    data: req
  })
}
// 导出SO upload parts模板
export function exportSoUploadPartsTemplate() {
  return request({
    url: '/om/so-detail/export-template',
    method: 'get',
    responseType: 'blob'
  })
}