import request from '@/utils/request'

// 创建srm三方对接请求参数记录日志表（包含三方call srm & srm call三方）
export function createApiRequestLog(data) {
  return request({
    url: '/open/api-request-log/create',
    method: 'post',
    data: data
  })
}

// 更新srm三方对接请求参数记录日志表（包含三方call srm & srm call三方）
export function updateApiRequestLog(data) {
  return request({
    url: '/open/api-request-log/update',
    method: 'put',
    data: data
  })
}

// 删除srm三方对接请求参数记录日志表（包含三方call srm & srm call三方）
export function deleteApiRequestLog(id) {
  return request({
    url: '/open/api-request-log/delete?id=' + id,
    method: 'delete'
  })
}

// 获得srm三方对接请求参数记录日志表（包含三方call srm & srm call三方）
export function getApiRequestLog(id) {
  return request({
    url: '/open/api-request-log/get?id=' + id,
    method: 'get'
  })
}

// 获得srm三方对接请求参数记录日志表（包含三方call srm & srm call三方）分页
export function getApiRequestLogPage(query) {
  return request({
    url: '/open/api-request-log/page',
    method: 'get',
    params: query
  })
}

// 导出srm三方对接请求参数记录日志表（包含三方call srm & srm call三方） Excel
export function exportApiRequestLogExcel(query) {
  return request({
    url: '/open/api-request-log/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
