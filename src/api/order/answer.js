import request from '@/utils/request'

// 交期协同分页列表查询
export function pagingAnswer(data) {
  return request({
    url: '/order/answer/page',
    method: 'post',
    data
  })
}

// 答交行接受操作
export function acceptDelivery(data) {
  return request({
    url: '/order/answer/accept',
    method: 'put',
    data
  })
}
// 答交行拒绝操作
export function rejectDelivery(data) {
  return request({
    url: '/order/answer/reject',
    method: 'put',
    data
  })
}
// 答交行临时接受操作
export function temporaryReceiving(data) {
  return request({
    url: '/order/answer/provisional-acceptance',
    method: 'put',
    data
  })
}
// 答交行重新确认操作
export function reconfirm(data) {
  return request({
    url: '/order/answer/reconfirm',
    method: 'put',
    data
  })
}
// 计划行 发布计划
export function createShipmentDetail(data) {
  return request({
    url: '/order/shipment-detail/create',
    method: 'post',
    data
  })
}
// 计划行 删除计划
export function deleteShipmentDetail(id) {
  return request({
    url: '/order/shipment-detail/delete?id=' + id,
    method: 'delete'
  })
}
// 答交行 删除答交
export function deleteAnswer(id) {
  return request({
    url: '/order/answer/delete?id=' + id,
    method: 'delete'
  })
}
// 答交行 提交答交
export function submitAnswers(data) {
  return request({
    url: '/order/answer/submit-answer',
    method: 'post',
    data
  })
}
// 答交行下载
export function answerPageExport(data) {
  return request({
    url: '/order/answer/export-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
// 交期协同#邮件通知
export function email(data) {
  return request({
    url: '/order/shipment-detail/notice',
    method: 'post',
    data
  })
}
// 下载计划和导入模板
export function importTemplate() {
  return request({
    url: '/order/shipment-detail/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
// 下载计划协同-供应商-批量答交导入模板
export function exportBatchAnswerExcel(query) {
  return request({
    url: '/order/answer/export-batch-answer-excel',
    method: 'get',
    responseType: 'blob',
    params: query
  })
}

