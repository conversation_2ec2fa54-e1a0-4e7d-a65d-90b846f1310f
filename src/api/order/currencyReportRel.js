import request from '@/utils/request'

// 创建币种与订单报表关系
export function createCurrencyReportRel(data) {
  return request({
    url: '/order/currency-report-rel/create',
    method: 'post',
    data: data
  })
}

// 更新币种与订单报表关系
export function updateCurrencyReportRel(data) {
  return request({
    url: '/order/currency-report-rel/update',
    method: 'put',
    data: data
  })
}

// 删除币种与订单报表关系
export function deleteCurrencyReportRel(id) {
  return request({
    url: '/order/currency-report-rel/delete?id=' + id,
    method: 'delete'
  })
}

// 获得币种与订单报表关系
export function getCurrencyReportRel(id) {
  return request({
    url: '/order/currency-report-rel/get?id=' + id,
    method: 'get'
  })
}

// 获得币种与订单报表关系分页
export function getCurrencyReportRelPage(query) {
  return request({
    url: '/order/currency-report-rel/page',
    method: 'get',
    params: query
  })
}
