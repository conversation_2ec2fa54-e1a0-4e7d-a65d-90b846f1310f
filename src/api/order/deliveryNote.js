import request from '@/utils/request'

export function getDeliverySupplier(fuzzySearch) {
  return request({
    url: '/order/delivery-note/get-delivery-supplier?fuzzySearch=' + fuzzySearch,
    method: 'get'
  })
}

// 送货单明细列表
export function getDeliveryDetailPage(data) {
  return request({
    url: '/order/delivery-detail/page',
    method: 'post',
    data
  })
}

export function saveDeliveryHead(data) {
  return request({
    url: '/order/delivery-note/save',
    method: 'post',
    data
  })
}

// 获得送货单信息
export function getDeliveryHead(params) {
  return request({
    url: '/order/delivery-note/get',
    method: 'get',
    params
  })
}

// 按订单添加按钮根据送货单抬头里的工厂、供应商获取订单行数据
export function getDeliveryAddPage(data) {
  return request({
    url: '/order/delivery-detail/getdelivery-page',
    method: 'post',
    data
  })
}

export function createDeliveryAdd(data) {
  return request({
    url: '/order/delivery-detail/create',
    method: 'post',
    data
  })
}

export function getImportTemplate(params) {
  return request({
    url: '/order/delivery-detail/get-import-template',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function delDeliveryDetail(params) {
  return request({
    url: '/order/delivery-detail/delete',
    method: 'delete',
    params
  })
}

export function uploadDeliveryFile(data) {
  return request({
    url: '/order/delivery-note/file-bind',
    method: 'post',
    data
  })
}

export function delDeliveryFile(id) {
  return request({
    url: '/order/file-rel/delete?id=' + id,
    method: 'delete'
  })
}

export function attachments(params) {
  return request({
    url: '/order/file-rel/attachments',
    method: 'get',
    params
  })
}

export function invalidDelivery(params) {
  return request({
    url: '/order/delivery-note/invalid',
    method: 'post',
    params
  })
}

export function confirmDelivery(params) {
  return request({
    url: '/order/delivery-note/confirmDelivery',
    method: 'put',
    params
  })
}
export function confirmReceive(params) {
  return request({
    url: '/order/delivery-note/confirmReceive',
    method: 'put',
    params
  })
}
// 重新编辑
export function repeatEdit(params) {
  return request({
    url: '/order/delivery-note/repeatEdit',
    method: 'put',
    params
  })
}

export function printDeliverynote(params) {
  return request({
    url: '/order/delivery-note/print-deliverynote',
    method: 'put',
    params
  })
}

// 获得送货单分页
export function getOrderDeliveryList(data) {
  return request({
    url: '/order/delivery-note/head-page',
    method: 'post',
    data
  })
}

export function getOrderDeliveryDetailList(data) {
  return request({
    url: '/order/delivery-note/line-page',
    method: 'post',
    data
  })
}

// 下载送货单
export function exportHeaderExcel(data) {
  return request({
    url: '/order/delivery-note/export-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 下载送货单明细
export function exportDetailExcel(data) {
  return request({
    url: '/order/delivery-note/export-detail-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

