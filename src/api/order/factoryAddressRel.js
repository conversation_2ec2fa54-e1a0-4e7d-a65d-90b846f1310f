import request from '@/utils/request'

// 创建订单/工厂与地址关系配置
export function createFactoryAddressRel(data) {
  return request({
    url: '/order/factory-address-rel/create',
    method: 'post',
    data: data
  })
}

// 更新订单/工厂与地址关系配置
export function updateFactoryAddressRel(data) {
  return request({
    url: '/order/factory-address-rel/update',
    method: 'put',
    data: data
  })
}

// 删除订单/工厂与地址关系配置
export function deleteFactoryAddressRel(id) {
  return request({
    url: '/order/factory-address-rel/delete?id=' + id,
    method: 'delete'
  })
}

// 获得订单/工厂与地址关系配置
export function getFactoryAddressRel(id) {
  return request({
    url: '/order/factory-address-rel/get?id=' + id,
    method: 'get'
  })
}

// 根据工厂id获取相关的地址信息
export function getFactoryAddressWithFactoryId(id) {
  return request({
    url: '/order/factory-address-rel/get-address?factoryId=' + id,
    method: 'get'
  })
}

// 获得订单/工厂与地址关系配置分页
export function getFactoryAddressRelPage(query) {
  return request({
    url: '/order/factory-address-rel/page',
    method: 'get',
    params: query
  })
}
