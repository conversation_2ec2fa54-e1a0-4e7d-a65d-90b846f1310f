import request from '@/utils/request'

// 获取订单详情里的订单抬头信息
export function getOrderDetail(params) {
  return request({
    url: '/order/header/get',
    method: 'get',
    params
  })
}
// 获取最新的有印章pdf
export function getLatestPdfWithOrderId(id) {
  return request({
    url: '/order/header/latestPdf?id=' + id,
    method: 'get'
  })
}

// 获取订单详情里的订单行分页信息
export function pagingOrderDetails(data) {
  return request({
    url: '/order/detail/page-detail',
    method: 'post',
    data
  })
}

export function completeDelivery(data) {
  return request({
    url: '/order/detail/complete/delivery',
    method: 'put',
    data
  })
}

export function exportDetail(data) {
  return request({
    url: '/order/header/export-detail',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
// 下载订单导入模板
export function importTemplate() {
  return request({
    url: '/order/header/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
// 下载订单收货模板
export function importGoodReceivingTemplate() {
  return request({
    url: '/order/good-receiving/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
export function getOrderList(data) {
  return request({
    url: '/order/header/page',
    method: 'post',
    data
  })
}

export function getOrderDetailList(data) {
  return request({
    url: '/order/detail/page',
    method: 'post',
    data
  })
}

export function getRewardList(data) {
  return request({
    url: '/order/good-receiving/page',
    method: 'post',
    data
  })
}
export function getVersionHis(params) {
  return request({
    url: '/order/header/version-history',
    method: 'get',
    params
  })
}

// 下载订单行
export function exportDetailExcel(data) {
  return request({
    url: '/order/detail/page-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
// 下载订单头
export function exportHeaderExcel(data) {
  return request({
    url: '/order/header/export-header',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 下载收货
export function exportReceivingExcel(data) {
  return request({
    url: '/order/good-receiving/export-page',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 订单首页数字牌
export function statisticsCard() {
  return request({
    url: '/order/header/statisticsCard',
    method: 'get',
  })
}

