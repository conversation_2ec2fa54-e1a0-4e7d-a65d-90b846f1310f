import request from '@/utils/request'

// 创建订单PDF自动推送OA盖章供应商配置
export function createPdfSupplierRel(data) {
  return request({
    url: '/order/pdf-supplier-rel/create',
    method: 'post',
    data: data
  })
}

// 删除订单PDF自动推送OA盖章供应商配置
export function deletePdfSupplierRel(id) {
  return request({
    url: '/order/pdf-supplier-rel/delete?id=' + id,
    method: 'delete'
  })
}

// 获得订单PDF自动推送OA盖章供应商配置分页
export function getPdfSupplierRelPage(query) {
  return request({
    url: '/order/pdf-supplier-rel/page',
    method: 'get',
    params: query
  })
}

// 获取未添加过的供应商集合
export function getNotRelSupplierList() {
  return request({
    url: '/order/pdf-supplier-rel/supplier-list',
    method: 'get'
  })
}

// 获取导入模板
export function getTemplatePdfSupplierRel() {
  return request({
    url: '/order/pdf-supplier-rel/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
