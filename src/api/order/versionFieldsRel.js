import request from '@/utils/request'

// 更新订单升版触发的变更字段
export function updateVersionFieldsRel(data) {
  return request({
    url: '/order/version-fields-rel/update',
    method: 'put',
    data: data
  })
}

// 获得订单升版触发的变更字段
export function getVersionFieldsRelList() {
  return request({
    url: '/order/version-fields-rel/list',
    method: 'get'
  })
}

// 获得订单升版触发的变更字段
export function getFields() {
  return request({
    url: '/order/header/fields',
    method: 'get'
  })
}

