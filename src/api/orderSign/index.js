import request from '@/utils/request'

export function pageConfirmSign(data) {
  return request({
    url: '/order/confirm-sign/page',
    method: 'post',
    data
  })
}

export function orderSignFor(data) {
  return request({
    url: '/order/confirm-sign/orderSignFor',
    method: 'post',
    data
  })
}
export function orderTern(data) {
  return request({
    url: '/order/confirm-sign/orderTern',
    method: 'post',
    data
  })
}
export function exportConfirm(data) {
  return request({
    url: '/order/confirm-sign/export-confirm',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
export function getOrderConfirmFile(params) {
  return request({
    url: '/order/confirm-sign/getOrderConfirmFile',
    method: 'get',
    params
  })
}

export function getGoodReceivingConfigList(params) {
  return request({
    url: '/order/good-receiving-config/list',
    method: 'get',
    params
  })
}
export function getOrderVersionList(params) {
  return request({
    url: '/order/confirm-sign/getOrderVersionList',
    method: 'get',
    params
  })
}
export function fileBind(data) {
  return request({
    url: '/order/confirm-sign/file-bind',
    method: 'post',
    data
  })
}
export function accept(data) {
  return request({
    url: '/order/confirm-sign/accept',
    method: 'post',
    data
  })
}

export function batchModifySubmit(data) {
  return request({
    url: '/order/confirm-sign/batch-modify-submit',
    method: 'post',
    data
  })
}
export function reject(data) {
  return request({
    url: '/order/confirm-sign/reject',
    method: 'post',
    data
  })
}

export function provisionalAcceptance(data) {
  return request({
    url: '/order/confirm-sign/provisional-acceptance',
    method: 'post',
    data
  })
}
export function publish(data) {
  return request({
    url: '/order/confirm-sign/publish',
    method: 'post',
    data
  })
}
export function batchModify(data) {
  return request({
    url: '/order/confirm-sign/batch-modify',
    method: 'post',
    data
  })
}
export function orderVersion(data) {
  return request({
    url: '/order/confirm-sign/order-version',
    method: 'post',
    data
  })
}
export function getBatchModifyTemplate() {
  return request({
    url: '/order/confirm-sign/get-batch-modify-template',
    method: 'get',
    responseType: 'blob'

  })
}
export function getImportTemplateConfig() {
  return request({
    url: '/order/good-receiving-config/get-import-template',
    method: 'get',
    responseType: 'blob'

  })
}
export function deliveryConfirmSourcing(data) {
  return request({
    url: '/order/confirm-sign/delivery-confirm-sourcing',
    method: 'post',
    data
  })
}
export function exportDeliveryConfirmSupplier(data) {
  return request({
    url: '/order/confirm-sign/export-deliveryConfirm-supplier',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

export function exportDeliveryConfirmSourcing(data) {
  return request({
    url: '/order/confirm-sign/export-deliveryConfirm-sourcing',
    method: 'post',
    data,
    responseType: 'blob'

  })
}
export function getImportTemplate(ids) {
  return request({
    url: '/order/confirm-sign/get-import-template?ids=' + ids,
    method: 'get',
    responseType: 'blob'
  })
}
export function deliveryConfirmSupplier(data) {
  return request({
    url: '/order/confirm-sign/delivery-confirm-supplier',
    method: 'post',
    data
  })
}

export function saveGoodReceivingConfig(data) {
  return request({
    url: '/order/good-receiving-config/save',
    method: 'post',
    data
  })
}

export function deleteGoodReceivingConfig(params) {
  return request({
    url: '/order/good-receiving-config/delete',
    method: 'delete',
    params
  })
}

