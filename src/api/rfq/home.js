import request from '@/utils/request'

export function getInquiryTypes() {
  return request({
    url: '/rfq/inquiry-types/list',
    method: 'get'
  })
}

export function getInquiryChart(params) {
  return request({
    url: '/rfq/chart-statistics-event/list',
    method: 'get',
    params
  })
}

export function getProjectPage(data) {
  return request({
    url: '/rfq/projects/page',
    method: 'post',
    data
  })
}

export function statisticsCard() {
  return request({
    url: '/rfq/projects/statisticsCard',
    method: 'get'
  })
}

export function getMaterialPage(data) {
  return request({
    url: '/rfq/project-materials/material-page',
    method: 'post',
    data
  })
}

export function getMaterialPageTotal(data) {
  return request({
    url: '/rfq/project-materials/material-page-total',
    method: 'post',
    data
  })
}

export function getQuotationPerspectivePage(data) {
  return request({
    url: '/rfq/quotations/perspective-page',
    method: 'post',
    data
  })
}

export function getQuotationPerspectivePageTotal(data) {
  return request({
    url: '/rfq/quotations/perspective-page-total',
    method: 'post',
    data
  })
}

export function followUp(data) {
  return request({
    url: '/rfq/quotations/follow-up',
    method: 'post',
    data
  })
}

export function checkFollowUp(data) {
  return request({
    url: '/rfq/quotations/check-follow-up',
    method: 'post',
    data
  })
}

export function checkRecommend(data) {
  return request({
    url: '/rfq/quotations-recommend/check-recommend',
    method: 'post',
    data
  })
}

export function checkNoQuote(data) {
  return request({
    url: '/rfq/quotations-recommend/check-no-quote',
    method: 'post',
    data
  })
}

export function downloadMaterialFiles(data) {
  return request({
    url: '/rfq/quotations/download-material-files',
    method: 'get',
    responseType: 'blob',
    params: data
  })
}

export function cancelProject(params) {
  return request({
    url: '/rfq/projects/cancel',
    method: 'post',
    params
  })
}

export function getRfqProject(params) {
  return request({
    url: '/rfq/projects/get',
    method: 'get',
    params
  })
}

export function saveRfqProject(data) {
  return request({
    url: '/rfq/projects/save',
    method: 'post',
    data
  })
}

export function createRfqRemark(data) {
  return request({
    url: '/rfq/project-remarks/create',
    method: 'post',
    data
  })
}

export function getRemarkList(params) {
  return request({
    url: '/rfq/project-remarks/list',
    method: 'get',
    params
  })
}

export function delRfqRemark(params) {
  return request({
    url: '/rfq/project-remarks/delete',
    method: 'delete',
    params
  })
}

export function rfqFilePost(data) {
  return request({
    url: '/rfq/file-rel/create',
    method: 'post',
    data
  })
}

export function createFileRelForCreateProject(data) {
  return request({
    url: '/rfq/file-rel/project-create',
    method: 'post',
    data
  })
}
export function createFileRelListForCreateProject(data) {
  return request({
    url: '/rfq/file-rel/project-create-list',
    method: 'post',
    data
  })
}

export function getRfqMaterial(data) {
  return request({
    url: '/rfq/project-materials/material-quotation-page',
    method: 'post',
    data
  })
}

/**
 * 跨页全选的后端方法
 * @param data
 * @returns {*}
 */
export function getProjectMaterialsQuotationsLists(data) {
  return request({
    url: '/rfq/project-materials/material-quotation-page-all',
    method: 'post',
    data
  })
}

export function checkMaterialStatus(params) {
  return request({
    url: '/rfq/project-materials/check-material-status',
    method: 'post',
    data: params
  })
}

/**
 * 验证物料是否存在空品类数据
 * @param params
 * @returns {*}
 */
export function checkMaterialCategoryIsNull(params) {
  return request({
    url: '/rfq/project-materials/check-material-category-null',
    method: 'post',
    params
  })
}

export function getRfqStep(params) {
  return request({
    url: '/rfq/step-status/get',
    method: 'get',
    params
  })
}

export function shelveRfqMaterial(params) {
  return request({
    url: '/rfq/project-materials/shelve-material',
    method: 'post',
    params
  })
}

export function terminateRfqMaterial(data) {
  return request({
    url: '/rfq/project-materials/terminate-material',
    method: 'post',
    data
  })
}

export function checkTerminateMaterial(data) {
  return request({
    url: '/rfq/project-materials/check-terminate-material',
    method: 'post',
    data
  })
}

export function sumbitToSource(params) {
  return request({
    url: '/rfq/project-materials/submit-to-source',
    method: 'post',
    params
  })
}

export function checkSumbitToSource(params) {
  return request({
    url: '/rfq/project-materials/check-submit-to-source',
    method: 'post',
    params
  })
}

export function createMaterial(data) {
  return request({
    url: '/rfq/project-materials/create',
    method: 'post',
    data
  })
}

export function createQuotation(data) {
  return request({
    url: '/rfq/quotations/create',
    method: 'post',
    data
  })
}

export function checkMergedRfq(data) {
  return request({
    url: '/rfq/quotations/check-merged-rfq',
    method: 'post',
    data
  })
}

export function getRfqProjectDisplay(params) {
  return request({
    url: '/rfq/project-display-field-config/get',
    method: 'get',
    params
  })
}

export function saveRfqProjectDisplay(data) {
  return request({
    url: '/rfq/project-display-field-config/save',
    method: 'post',
    data
  })
}

/**
 * 验证物料是否有没有供应商数据
 * @param data
 * @returns {*}
 */
export function checkMaterialHasNotSupplier(params) {
  return request({
    url: '/rfq/material-suppliers-rel/check-material-supplier-null',
    method: 'get',
    params
  })
}

export function getMfgAndSupplierRel(data) {
  return request({
    url: '/rfq/material-suppliers-rel/get-mfgAndSupplierRel',
    method: 'post',
    data
  })
}

export function saveMaterialAndSupplierRel(data) {
  return request({
    url: '/rfq/material-suppliers-rel/save-materialAndSupplierRel',
    method: 'post',
    data
  })
}

export function getRfqFile(params) {
  return request({
    url: '/rfq/file-rel/list',
    method: 'get',
    params
  })
}

export function getQuoteFile(params) {
  return request({
    url: '/rfq/file-rel/quote-files-by-project',
    method: 'get',
    params
  })
}

export function getRfqMaterialList(params) {
  return request({
    url: '/rfq/material-suppliers-rel/get-materialRelByMaterialIds',
    method: 'get',
    params
  })
}

export function getMaterialInfo(params) {
  return request({
    url: '/rfq/project-materials/get-materialInfo',
    method: 'get',
    params
  })
}

export function getMasterMaterialByMaterialId(params) {
  return request({
    url: '/rfq/project-materials/get-master-material',
    method: 'get',
    params
  })
}

export function getMaterialDetail(params) {
  return request({
    url: '/rfq/project-materials/get',
    method: 'get',
    params
  })
}

export function getMaterialSupplierRelByMaterialIds(params) {
  return request({
    url: '/rfq/material-suppliers-rel/get-materialSupplierRelByMaterialIds',
    method: 'get',
    params
  })
}

export function delRfqFile(params) {
  return request({
    url: '/rfq/file-rel/delete',
    method: 'delete',
    params
  })
}

export function deleteMaterials(params) {
  return request({
    url: '/rfq/project-materials/deleteMaterials',
    method: 'delete',
    params
  })
}
export function updateSource(params) {
  return request({
    url: '/rfq/project-materials/updateSource',
    method: 'post',
    params
  })
}

export function deleteMaterialsForPriceEntry(params) {
  return request({
    url: '/rfq/project-materials/del-materials-for-price-entry',
    method: 'delete',
    params
  })
}

export function deleteMaterialsSupplier(params) {
  return request({
    url: '/rfq/material-suppliers-rel/deleteMaterialsSupplier',
    method: 'delete',
    params
  })
}

export function checkMaterialHasQuote(params) {
  return request({
    url: '/rfq/project-materials/check-material-has-quote',
    method: 'post',
    params
  })
}

export function deleteMaterialsSupplierRel(data) {
  return request({
    url: '/rfq/material-suppliers-rel/delete-materialAndSupplierRel',
    method: 'delete',
    data
  })
}

export function importTemplate() {
  return request({
    url: '/rfq/project-materials/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
export function importTemplate2(id) {
  return request({
    url: '/rfq/project-materials/temporaryMaterialToFormal-template?projectId=' + id,
    method: 'get',
    responseType: 'blob'
  })
}
export function downloadPriceDirectPriceEntry() {
  return request({
    url: '/rfq/project-materials/get-price-entry-template',
    method: 'get',
    responseType: 'blob'
  })
}

export function getQuotationConfig(params) {
  return request({
    url: '/rfq/quotation-display-field-config/get',
    method: 'get',
    params
  })
}

export function getQuotation(params) {
  return request({
    url: '/rfq/quotations/get',
    method: 'get',
    params
  })
}

export function getQuotationList(data) {
  return request({
    url: '/rfq/quotations/get-quotation-list',
    method: 'post',
    data
  })
}

export function getTemplateMaterialCount(params) {
  return request({
    url: '/rfq/quotations-template/get-template-material-count',
    method: 'get',
    params
  })
}

export function getFieldsByTemplate(params) {
  return request({
    url: '/rfq/quotations-template-fields/get-fields-by-template',
    method: 'get',
    params
  })
}

export function getFieldsByTemplatePriceEntry(params) {
  return request({
    url: '/rfq/quotations-template-fields/get-fields-by-template-price-entry',
    method: 'get',
    params
  })
}

export function getTemplateList(params) {
  return request({
    url: '/visualization/report/names',
    method: 'get',
    params
  })
}

export function downloadPriceFiles(data) {
  return request({
    url: '/rfq/price-approval/download-price-files',
    method: 'get',
    responseType: 'blob',
    params: data
  })
}

export function getQuotationReplyData(data) {
  return request({
    url: '/rfq/quotations/get-quotation-reply-data',
    method: 'post',
    data
  })
}
export function copyTokenUrl(params) {
  return request({
    url: '/rfq/quotations/copyTokenUrl',
    method: 'get',
    params
  })
}

export function getQuotationReplyDataForPriceEntry(data) {
  return request({
    url: '/rfq/quotations/get-quotation-reply-data-price-entry',
    method: 'post',
    data
  })
}

export function saveQuotationMaterial(data) {
  return request({
    url: '/rfq/quotations/save-quotation-material',
    method: 'post',
    data
  })
}

export function saveQuotationMaterialPriceEntry(data) {
  return request({
    url: '/rfq/quotations/save-quotation-material-price-entry',
    method: 'post',
    data
  })
}

export function checkMaterialStatusByQuotationId(params) {
  return request({
    url: '/rfq/quotations/check-material-status',
    method: 'post',
    params
  })
}

export function submitPart(params) {
  return request({
    url: '/rfq/quotations/submit-part',
    method: 'post',
    params
  })
}

export function submitQuo(params) {
  return request({
    url: '/rfq/quotations/submit',
    method: 'post',
    params
  })
}

export function checkMaterialquote(params) {
  return request({
    url: '/rfq/quotations/check-no-quote',
    method: 'post',
    params
  })
}

export function checkQuotationCancel(params) {
  return request({
    url: '/rfq/quotations/check-quotation-cancel',
    method: 'post',
    params
  })
}

export function checkMaterialHavequote(params) {
  return request({
    url: '/rfq/quotations/check-have-quote',
    method: 'post',
    params
  })
}

export function cancelQuo(params) {
  return request({
    url: '/rfq/quotations/cancel',
    method: 'post',
    params
  })
}
export function updateTargetPriceTag(data) {
  return request({
    url: '/rfq/quotations/update-targetprice-tag',
    method: 'post',
    data
  })
}
export function quotationTemplate(data) {
  return request({
    url: '/rfq/quotations/download-quotation-reply-data',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function downloadSupplierQuotation(data) {
  return request({
    url: '/rfq/quotations/download-supplier-quotation',
    method: 'post',
    responseType: 'blob',
    data
  })
}


export function savePriceApproval(data) {
  return request({
    url: '/rfq/price-approval/save',
    method: 'post',
    data
  })
}

export function pushPriceToBom(data) {
  return request({
    url: '/rfq/price-approval/push-price-to-bom',
    method: 'post',
    data
  })
}

export function savePriceApprovalPriceEntry(data) {
  return request({
    url: '/rfq/price-approval/save-price-entry',
    method: 'post',
    data
  })
}

export function getCustomFieldsByTemplate(params) {
  return request({
    url: '/rfq/quotations-template-fields/get-custom-fields-by-template',
    method: 'get',
    params
  })
}

export function listByBusinessIds(params) {
  return request({
    url: '/rfq/file-rel/listByBusinessIds',
    method: 'get',
    params
  })
}

export function checkReturnComments(params) {
  return request({
    url: '/rfq/price-approval/check-return-comments',
    method: 'post',
    data: params
  })
}

export function createPreviewRecord(data) {
  return request({
    url: '/rfq/preview-record/create',
    method: 'post',
    data
  })
}

export function approvalListPage(data) {
  return request({
    url: '/rfq/price-approval/approval-list-page',
    method: 'post',
    data
  })
}

export function exportDetail(data) {
  return request({
    url: '/rfq/price-approval/export-approval-list',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

export function exportDetailPriceEntry(data) {
  return request({
    url: '/rfq/price-approval/export-approval-list-price-entry',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

export function createApprovalMaterialRel(data) {
  return request({
    url: '/rfq/approval-material-rel/create',
    method: 'post',
    data
  })
}

export function getApprovalMaterialRel(params) {
  return request({
    url: '/rfq/approval-material-rel/page',
    method: 'get',
    params
  })
}

export function deleteApprovalMaterialRel(params) {
  return request({
    url: '/rfq/approval-material-rel/delete',
    method: 'delete',
    params
  })
}

export function updateApprovalMaterialRel(data) {
  return request({
    url: '/rfq/approval-material-rel/update',
    method: 'put',
    data
  })
}

export function rejectPriceApproval(data) {
  return request({
    url: '/rfq/price-approval/reject',
    method: 'post',
    data
  })
}

export function rejectAllPriceApproval(params) {
  return request({
    url: '/rfq/price-approval/reject',
    method: 'post',
    params
  })
}

export function submitPriceApproval(params) {
  return request({
    url: '/rfq/price-approval/submit',
    method: 'post',
    params
  })
}

export function getPriceApproval(params) {
  return request({
    url: '/rfq/price-approval/get',
    method: 'get',
    params
  })
}

export function getByApproval(params) {
  return request({
    url: '/rfq/price-approval-supplier-attachment-rel/get-by-approval',
    method: 'get',
    params
  })
}

export function getApprovalList(params) {
  return request({
    url: '/rfq/price-approval/list',
    method: 'get',
    params
  })
}

export function urgentData(params) {
  return request({
    url: '/rfq/price-approval/urgent',
    method: 'post',
    params
  })
}

export function cancelData(params) {
  return request({
    url: '/rfq/price-approval/cancel',
    method: 'post',
    params
  })
}

export function pageQuotationSupplier(data) {
  return request({
    url: '/rfq/supplier-quotations/page-quotation-supplier',
    method: 'post',
    data
  })
}

export function pageQuotationSupplierMaterial(data) {
  return request({
    url: '/rfq/supplier-quotations/page-quotation-supplier-material',
    method: 'post',
    data
  })
}

export function processRecordList(params) {
  return request({
    url: '/rfq/process-record/list',
    method: 'get',
    params
  })
}

export function downloadMaterialQuote(data) {
  return request({
    url: '/rfq/project-materials/download-material-quote',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function saveMaterialCodeControl(data) {
  return request({
    url: '/rfq/project-materials/update-materialCodeControl',
    method: 'put',
    data
  })
}

// 保存报价备注
export function saveQuotationRemark(data) {
  return request({
    url: '/rfq/quotations/update-remarks',
    method: 'put',
    data
  })
}

// 根据项目物料ID获取物料下图纸
export function downloadMaterialDrawings(data) {
  return request({
    url: '/rfq/project-materials/download-material-drawing',
    method: 'get',
    responseType: 'blob',
    params: data
  })
}

export function exportAnalyzeQuote(data) {
  return request({
    url: '/rfq/quotation-decision/export-analyze-quote',
    method: 'get',
    responseType: 'blob',
    params: data
  })
}

export function simulationResults(data) {
  return request({
    url: '/rfq/quotation-decision/simulation-results',
    method: 'post',
    data
  })
}

export function checkQuotationQuantity(data) {
  return request({
    url: '/rfq/quotation-decision/check-quotation-quantity',
    method: 'post',
    data
  })
}

export function applicationResults(data) {
  return request({
    url: '/rfq/quotation-decision/application-results',
    method: 'post',
    data
  })
}

export function getMaterialByProject(params) {
  return request({
    url: '/rfq/project-materials/get-material-by-project',
    method: 'get',
    params
  })
}

export function getApprovalMaterialSupplierRel(data) {
  return request({
    url: '/rfq/approval-material-supplier-rel/get-material-price-data',
    method: 'post',
    data
  })
}

export function quoteSupplier(params) {
  return request({
    url: '/rfq/material-suppliers-rel/quote-supplier',
    method: 'get',
    params
  })
}

export function terminationProject(params) {
  return request({
    url: '/rfq/projects/termination',
    method: 'post',
    params
  })
}

export function completeProject(params) {
  return request({
    url: '/rfq/projects/complete',
    method: 'post',
    params
  })
}

export function overview() {
  return request({
    url: '/rfq/chart-statistics-event/overview',
    method: 'get'
  })
}

// download rfs project
export function exportRfsList(data) {
  return request({
    url: '/rfq/projects/export-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// download rfq project
export function exportRfqList(data) {
  return request({
    url: '/rfq/quotations/export-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// download material list
export function exportMaterialList(data) {
  return request({
    url: '/rfq/project-materials/export-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}




