import request from '@/utils/request'

/**
 * 获取 首页审批单的翻页数据
 *
 * @param params 搜索条件
 * @returns 搜索结果
 */
export function getPriceApprovalPage(params) {
  return request({
    url: '/rfq/price-approval/page',
    method: 'post',
    data: params
  })
}
/**
 * 获得价格审批分页的总行数
 *
 * @param params 搜索条件
 * @returns 搜索结果
 */
export function getPriceApprovalPageTotal(params) {
  return request({
    url: '/rfq/price-approval/page-total',
    method: 'post',
    data: params
  })
}
/**
 * 价格审批-提交不审批
 *
 * @param data 搜索条件
 * @returns {*}
 */
export function savePriceApprovalCompleted(data) {
  return request({
    url: '/rfq/price-approval/submit-noSave',
    method: 'post',
    data: data
  })
}

/**
 * 价格审批-提交不审批-恢复
 *
 * @param data 搜索条件
 * @returns {*}
 */
export function submitRecover(data) {
  return request({
    url: '/rfq/price-approval/submit-recover',
    method: 'post',
    data: data
  })
}
/**
 * 加急审批单
 *
 * @param params 审批单id
 */
export function urgentPriceApproval(params) {
  return request({
    url: '/rfq/price-approval/urgent',
    method: 'post',
    params
  })
}

/**
 * 提交检查状态
 *
 * @param params 搜索条件
 * @returns 搜索结果
 */
export function checkStatus(params) {
  return request({
    url: '/rfq/price-approval/check-status',
    method: 'post',
    params
  })
}
/**
 * 审批物料的跨页全选接口，提供物料id集合
 *
 * @param params 搜索条件
 * @returns 搜索结果
 */
export function getPriceApprovalMaterialAllIds(params) {
  return request({
    url: '/rfq/price-approval/approval-list-page-all',
    method: 'post',
    data: params
  })
}
