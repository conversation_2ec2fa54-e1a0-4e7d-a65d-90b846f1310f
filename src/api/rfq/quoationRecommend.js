import request from '@/utils/request'

/**
 * 比价推荐的物料列表的跨页全选接口，提供物料id集合
 *
 * @param params 搜索条件
 * @returns 搜索结果
 */
export function getRecommendQuotationMaterialsAllIds(params) {
  return request({
    url: '/rfq/quotations-recommend/page-material-recommend-all',
    method: 'post',
    data: params
  })
}
/**
 * 比价推荐的询价单列表的跨页全选接口，提供询价单id集合
 *
 * @param data 搜索条件
 * @returns 搜索结果
 */
export function getRecommendQuotationAllIds(data) {
  return request({
    url: '/rfq/quotations-recommend/page-quotation-recommend-all',
    method: 'post',
    data: data
  })
}
/**
 * 价格分析列表搜索的跨页全选接口，提供物料id集合
 *
 * @param params 搜索条件
 * @returns 搜索结果
 */
export function getRecommendMaterialAnalyticsAllIds(params) {
  return request({
    url: '/rfq/quotations-recommend/page-material-analytics-recommend-all-ids',
    method: 'post',
    data: params
  })
}
export function getTemplateRecommend(data) {
  return request({
    url: '/rfq/quotations-recommend/get-template-recommend',
    method: 'post',
    data
  })
}

export function pageMaterialAnalyticsRecommend(data) {
  return request({
    url: '/rfq/quotations-recommend/page-material-analytics-recommend',
    method: 'post',
    data
  })
}

export function getAdvancedRecommend(data) {
  return request({
    url: '/rfq/quotations-recommend/get-advanced-recommend',
    method: 'post',
    data
  })
}

export function checkRecommendAccess(data) {
  return request({
    url: '/rfq/quotations-recommend/check-recommend-access',
    method: 'post',
    data
  })
}

export function saveAdvancedRecommend(data) {
  return request({
    url: '/rfq/quotations-recommend/save-advanced-recommend',
    method: 'post',
    data
  })
}

export function getPageQuotationRecommend(data) {
  return request({
    url: '/rfq/quotations-recommend/page-quotation-recommend',
    method: 'post',
    data
  })
}

export function getPageMaterialRecommend(data) {
  return request({
    url: '/rfq/quotations-recommend/page-material-recommend',
    method: 'post',
    data: data
  })
}

export function terminateRecommendQuotation(params) {
  return request({
    url: '/rfq/quotations-recommend/terminate-recommend-quotation',
    method: 'post',
    params
  })
}

export function returnRecommendQuotation(params) {
  return request({
    url: '/rfq/quotations-recommend/return-recommend-quotation',
    method: 'post',
    params
  })
}

export function returnRecommendMaterial(params) {
  return request({
    url: '/rfq/quotations-recommend/return-recommend-material',
    method: 'post',
    params
  })
}

export function batchReturnRecommendMaterial(params) {
  return request({
    url: '/rfq/quotations-recommend/batch-return-recommend-material',
    method: 'post',
    params
  })
}

export function batchReturnByQuoteMaterialSupplier(params) {
  return request({
    url: '/rfq/quotations-recommend/batch-return-quote-material-supplier',
    method: 'post',
    params
  })
}

export function hasContainOther(params) {
  return request({
    url: '/rfq/quotations-recommend/has-contain-other',
    method: 'post',
    params
  })
}

export function terminateRecommendMaterial(params) {
  return request({
    url: '/rfq/quotations-recommend/terminate-recommend-material',
    method: 'post',
    params
  })
}

export function exportMaterialExcel(data) {
  return request({
    url: '/rfq/quotations-recommend/export-material-excel',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function exportQuotationExcel(data) {
  return request({
    url: '/rfq/quotations-recommend/export-quotation-excel',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function updateMaterialAnalyticsRecommend(data) {
  return request({
    url: '/rfq/quotations-recommend/update-material-analytics-recommend',
    method: 'put',
    data
  })
}
// 下载价格分析导入模板
export function exportMaterialAnalyticsRecommendTemplate(data) {
  return request({
    url: '/rfq/quotations-recommend/export-material-analytics-recommend',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
export function checkSubmit(params) {
  return request({
    url: '/rfq/quotations-recommend/check-submit',
    method: 'get',
    params
  })
}

// 获取需要退回的供应商列表
export function getReturnSuppliers(params) {
  return request({
    url: '/rfq/quotations-recommend/get-return-suppliers',
    method: 'get',
    params
  })
}

export function rfqReturn(params) {
  return request({
    url: '/rfq/quotations/return',
    method: 'post',
    params
  })
}

export function getNotRecommend(data) {
  return request({
    url: '/rfq/quotations-recommend/get-not-recommend',
    method: 'post',
    data
  })
}

/**
 * 获取物料汇总统计
 * @param params
 * @returns {*}
 */
export function getMaterialSummaryReport(params) {
  return request({
    url: '/rfq/project-materials/get-material-summary-report',
    method: 'get',
    params
  })
}

/**
 * 获取项目供应商汇总询价报告
 * @param data
 * @returns {*}
 */
export function getRfqSummary(data) {
  return request({
    url: '/rfq/quotations-recommend/get-rfq-summary',
    method: 'post',
    data
  })
}

/**
 * 导出项目的询价汇总
 * @param data
 * @returns {*}
 */
export function exportRfqSummary(data) {
  return request({
    url: '/rfq/quotations-recommend/download-rfq-summary',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
/**
 * 导出MFG分析报告
 * @param data
 * @returns {*}
 */
export function exportAnalysisMfg(data) {
  return request({
    url: '/rfq/quotations-recommend/download-analysis-mfg',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

/**
 * 导出项目的情景1-最低价报告
 * @param data
 * @returns {*}
 */
export function exportBestPriceReport(data) {
  return request({
    url: '/rfq/quotations-recommend/download-best-price-report',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
