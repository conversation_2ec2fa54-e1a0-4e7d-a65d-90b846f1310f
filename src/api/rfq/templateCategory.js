import request from "@/utils/request";

/**
 * 获取 首页审批单的翻页数据
 *
 * @param params 搜索条件
 * @returns 搜索结果
 */
export function getQuotationsTemplateCategoryRelList(params) {
  return request({
    url: '/rfq/quotations-template-category-rel/list',
    method: 'post',
    params
  })
}

export function assignTemplateCategories(data) {
  return request({
    url: '/rfq/quotations-template-category-rel/create',
    method: 'post',
    data
  })
}


export function getQuotationsTemplateList() {
  return request({
    url: '/rfq/quotations-template/get-template-list',
    method: 'get'
  })
}
