import request from "@/utils/request";

export function getList(params) {
  return request({
    url: '/rfq/quotations-template/templateList',
    method: 'get',
    params
  })
}

export function saveOrEdit(data) {
  return request({
    url: '/rfq/quotations-template/saveOrEdit',
    method: 'post',
    data
  })
}

export function deleteTemp(data) {
  return request({
    url: '/rfq/quotations-template/deleteTemp',
    method: 'delete',
    data
  })
}

export function getFieldListAll(params) {
  return request({
    url: '/rfq/quotations-template-fields/getFieldListAll',
    method: 'get',
    params
  })
}
export function saveOrEditField(data) {
  return request({
    url: '/rfq/quotations-template-fields/saveOrEdit',
    method: 'post',
    data
  })
}
