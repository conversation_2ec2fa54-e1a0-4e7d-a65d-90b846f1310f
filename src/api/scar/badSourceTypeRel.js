import request from '@/utils/request'

// 创建不良来源与单据关联
export function createBadSourceTypeRel(data) {
  return request({
    url: '/scar/bad-source-type-rel/create',
    method: 'post',
    data: data
  })
}

// 更新不良来源与单据关联
export function updateBadSourceTypeRel(data) {
  return request({
    url: '/scar/bad-source-type-rel/update',
    method: 'put',
    data: data
  })
}

// 获得不良来源与单据关联
export function getBadSourceTypeRel(id) {
  return request({
    url: '/scar/bad-source-type-rel/get?id=' + id,
    method: 'get'
  })
}

// 获得不良来源与单据关联分页
export function getBadSourceTypeRelPage(query) {
  return request({
    url: '/scar/bad-source-type-rel/page',
    method: 'get',
    params: query
  })
}

