import request from '@/utils/request'
import store from '@/store'

// 创建Scar不良类型
export function createDefectType(data) {
  return request({
    url: '/scar/defect-type/create',
    method: 'post',
    data: data
  })
}

// 更新Scar不良类型
export function updateDefectType(data) {
  return request({
    url: '/scar/defect-type/update',
    method: 'put',
    data: data
  })
}

// 删除Scar不良类型
export function deleteDefectType(id) {
  return request({
    url: '/scar/defect-type/delete?id=' + id,
    method: 'delete'
  })
}

// 获得Scar不良类型
export function getDefectType(id) {
  return request({
    url: '/scar/defect-type/get?id=' + id,
    method: 'get'
  })
}

// 获得Scar不良类型分页
export function getDefectTypeList(data) {
  return request({
    url: '/scar/defect-type/list',
    method: 'post',
    data: data
  })
}

// 从缓存获得品类列表
export function getDefectTypeListCache(defectTypeIds, status, level) {
  return request({
    url: '/scar/defect-type/list-defect-type-cache',
    method: 'get',
    params: { defectTypeIds: defectTypeIds, locale: store.getters.language, status: status, level: level }
  })
}

// 导出Scar不良类型 Excel
export function exportDefectTypeExcel(query) {
  return request({
    url: '/scar/defect-type/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
