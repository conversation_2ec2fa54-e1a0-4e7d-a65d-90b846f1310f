import request from '@/utils/request'
import { getBadSourceTypeRelPage } from '@/api/scar/badSourceTypeRel'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'

export function uploadImage(data) {
  return request({
    url: '/infra/file/upload',
    method: 'post',
    data
  })
}

export function validatePassword(params) {
  return request({
    url: '/system/user/validate-password',
    method: 'post',
    params
  })
}

export function saveRecord(data) {
  return request({
    url: '/scar/record/save',
    method: 'post',
    data
  })
}
export function statisticsCard() {
  return request({
    url: '/scar/record/statisticsCard',
    method: 'get'
  })
}

// 获得scar单据主分页
export function pageRecord(data) {
  return request({
    url: '/scar/record/page',
    method: 'post',
    data
  })
}

// 导出scar单据主分页
export function exportPageRecord(data) {
  return request({
    url: '/scar/record/export-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

export function exportPageTestRecord(data) {
  return request({
    url: '/scar/record/export-test-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
export function getScarSupplierShareInvitationLink(scarId) {
  return request({
    url: '/scar/record/get-share-invitation-link?scarId=' + scarId,
    method: 'get'
  })
}
// 质量单据的延期
export function postponeRecord(data) {
  return request({
    url: '/scar/record/postpone',
    method: 'post',
    data
  })
}

// 质量单据的撤销
export function undoRecord(query) {
  return request({
    url: '/scar/record/undo',
    method: 'post',
    params: query
  })
}

// 质量单据的删除
export function deleteRecord(query) {
  return request({
    url: '/scar/record/delete',
    method: 'post',
    params: query
  })
}
// 获得SCAR模块流程操作记录集合
export function getDataOperateRecordPage(scarId) {
  return request({
    url: '/scar/data-operate-record/list?scarId=' + scarId,
    method: 'get'
  })
}

export function supplierConfirm(data) {
  return request({
    url: '/scar/supplier-reply-text-rel/supplier-confirm',
    method: 'post',
    data
  })
}

export function submitIqc(data) {
  return request({
    url: '/scar/record/submit-iqc',
    method: 'post',
    data
  })
}
export function submitSc(data) {
  return request({
    url: '/scar/record/submit-sc',
    method: 'post',
    data
  })
}
export function submitSp(data) {
  return request({
    url: '/scar/record/submit-sp',
    method: 'post',
    data
  })
}

// SA单据-提交至供应商功能
export function saSubmitToSupplier(data) {
  return request({
    url: '/scar/record/submit-sa',
    method: 'post',
    data
  })
}

export function submitContainmentAction(data) {
  return request({
    url: '/scar/record/submit-containment-action',
    method: 'post',
    data
  })
}

export function rejectContainmentAction(data) {
  return request({
    url: '/scar/record/reject-containment-action',
    method: 'post',
    data
  })
}
export function rejectGlobal(data) {
  return request({
    url: '/scar/record/reject-global',
    method: 'post',
    data
  })
}

// 发布scar单据给SQE
export function submitSqe(data) {
  return request({
    url: '/scar/record/submit-sqe',
    method: 'post',
    data
  })
}
export function saveGlobal(data) {
  return request({
    url: '/scar/record/save-global',
    method: 'post',
    data
  })
}
export function submitGlobal(data) {
  return request({
    url: '/scar/record/submit-global',
    method: 'post',
    data
  })
}

export function getRecord(params) {
  return request({
    url: '/scar/record/get-iqc',
    method: 'get',
    params
  })
}

// 获得scar会签流程处理人列表
export function getRecordApproveUserRelList(scarId) {
  return request({
    url: '/scar/approve-user-rel/list?scarId=' + scarId,
    method: 'get'
  })
}

export function getSC(params) {
  return request({
    url: '/scar/record/get-sc',
    method: 'get',
    params
  })
}
export function getSP(params) {
  return request({
    url: '/scar/record/get-sp',
    method: 'get',
    params
  })
}

// 获得scar单据-SA
export function getSARecord(params) {
  return request({
    url: '/scar/record/get-sa',
    method: 'get',
    params
  })
}

export function validRepeatDefect(params) {
  return request({
    url: '/scar/record/valid-repeat-defect',
    method: 'get',
    params
  })
}
// 获取SCAR单据的问题来源，根据单据类型获取
export async function getSourceOfProblem(type) {
  const res = await getBadSourceTypeRelPage({
    pageNo: 1,
    pageSize: 10,
    scarType: type,
    status: 0
  })
  return getDictDatas(DICT_TYPE.SCAR_DEFECT_SOURCE).filter(a => res.data.list.find(b => b.badSource === a.value))
}

// 根据模糊查询获取供应商（支持按采购组织id获取关联的供应商）
export function getSupplierDetail(params) {
  return request({
    url: '/supplier/base-info/get-supplier',
    method: 'get',
    params
  })
}

export function getSupplierDetailById(params) {
  return request({
    url: '/supplier/base-info/get-supplier-by-id',
    method: 'get',
    params
  })
}

// 获取质量模块相关的附件
export function getScarFileRelList(params) {
  return request({
    url: '/scar/file-rel/list',
    method: 'get',
    params
  })
}

// 下载单据里所有的上传附件
export function downloadScarRecordAllFilesToZip(data) {
  return request({
    url: '/scar/file-rel/download-zip',
    method: 'get',
    responseType: 'blob',
    params: data
  })
}

// 校验指定的质量单据是否允许延期
export function canPostpone(scarId) {
  return request({
    url: '/scar/record/can-postpone?scarId=' + scarId,
    method: 'get'
  })
}

// 根据文件id获取文件详情
export function getFileDetail(fileId) {
  return request({
    url: '/infra/file/get-by-id?fileId=' + fileId,
    method: 'get'
  })
}
export function updateTaskAssignee(data) {
  return request({
    url: '/scar/approve-user-rel/update-assignee',
    method: 'PUT',
    data: data
  })
}
