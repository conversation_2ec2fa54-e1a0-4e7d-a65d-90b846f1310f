import request from '@/utils/request'

export function getRecordTypeRequiredConfigPage(data) {
  return request({
    url: '/scar/record-type-required-config/list',
    method: 'post',
    data
  })
}
export function saveRecordTypeRequiredConfig(data) {
  return request({
    url: '/scar/record-type-required-config/save',
    method: 'post',
    data
  })
}

// 获得当前供应商+采购组织选择的人员
export function getRecordTypeRequiredConfig(query) {
  return request({
    url: '/scar/record-type-required-config/get',
    method: 'get',
    params: query
  })
}

