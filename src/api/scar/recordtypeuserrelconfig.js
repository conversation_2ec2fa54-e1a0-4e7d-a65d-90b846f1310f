import request from '@/utils/request'

export function getRecordTypeUserRelConfigList(data) {
  return request({
    url: '/scar/record-type-user-rel-config/list',
    method: 'post',
    data
  })
}
export function saveRecordTypeUserRelConfig(data) {
  return request({
    url: '/scar/record-type-user-rel-config/save',
    method: 'post',
    data
  })
}
// 获得当前供应商+采购组织选择的人员
export function getUsersByTypeAndOrgId(query) {
  return request({
    url: '/scar/record-type-user-rel-config/list-users',
    method: 'get',
    params: query
  })
}
// 导出供应商与人员关系 Excel
export function exportRecordTypeUserRelExcel(data) {
  return request({
    url: '/scar/record-type-user-rel-config/export-excel',
    method: 'post',
    data,
    responseType: 'blob',
    timeout: 600000
  })
}
