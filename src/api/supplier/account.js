import request from '@/utils/request'

export function createAccountRel(data) {
  return request({
    url: '/supplier/account-rel/create',
    method: 'post',
    data: data
  })
}

export function getAccountRel(params) {
  return request({
    url: '/supplier/account-rel/get',
    method: 'get',
    params
  })
}

export function getAccountRelList(params) {
  return request({
    url: '/supplier/account-rel/list',
    method: 'get',
    params
  })
}

export function getAccountRelPage(params) {
  return request({
    url: '/supplier/account-rel/page',
    method: 'get',
    params
  })
}

export function sendMailAccountRel(data) {
  return request({
    url: '/supplier/account-rel/new-send-mail',
    method: 'put',
    data
  })
}

export function updateAccountRel(data) {
  return request({
    url: '/supplier/account-rel/update',
    method: 'put',
    data
  })
}

export function updatePass(data) {
  return request({
    url: '/supplier/account-rel/update-password',
    method: 'put',
    data
  })
}

export function updateStatus(data) {
  return request({
    url: '/supplier/account-rel/update-status',
    method: 'put',
    data
  })
}

export function getSupplierName(params) {
  return request({
    url: '/supplier/base-info/get-supplierName',
    method: 'get',
    params
  })
}
