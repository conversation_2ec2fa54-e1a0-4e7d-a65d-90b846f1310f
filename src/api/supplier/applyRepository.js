import request from '@/utils/request'

// 供应商申请库的首页分页
export function getApplySupplierPage(data) {
  return request({
    url: '/supplier/apply/page',
    method: 'post',
    data: data
  })
}

// 准许引入供应商
export function introduction(data) {
  return request({
    url: '/supplier/apply/introduction',
    method: 'post',
    data: data
  })
}

// 删除供应商
export function deleteSupplier(data) {
  return request({
    url: '/supplier/apply/delete',
    method: 'delete',
    data: data
  })
}

// 邀请供应商
export function inviteSupplier(data) {
  return request({
    url: '/supplier/apply/invite',
    method: 'post',
    data: data
  })
}

// 邀请新供应商之前根据公司名称校验是否已经重复存在
export function checkBeforeInvite(data) {
  return request({
    url: '/supplier/apply/checkBeforeInvite',
    method: 'post',
    data: data
  })
}

