import request from '@/utils/request'

// 创建供应商品牌
export function createBrand(data) {
  return request({
    url: '/supplier/brand/create',
    method: 'post',
    data: data
  })
}

// 更新供应商品牌
export function updateBrand(data) {
  return request({
    url: '/supplier/brand/update',
    method: 'put',
    data: data
  })
}

// 删除供应商品牌
export function deleteBrand(id) {
  return request({
    url: '/supplier/brand/delete?id=' + id,
    method: 'delete'
  })
}

// 获得供应商品牌
export function getBrand(id) {
  return request({
    url: '/supplier/brand/get?id=' + id,
    method: 'get'
  })
}

// 获得供应商品牌分页
export function getBrandPage(query) {
  return request({
    url: '/supplier/brand/page',
    method: 'get',
    params: query
  })
}

// 获得供应商品牌列表
export function getBrandList(query) {
  return request({
    url: '/supplier/brand/list',
    method: 'get',
    params: query
  })
}

// 导出供应商品牌 Excel
export function exportBrandExcel(query) {
  return request({
    url: '/supplier/brand/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
