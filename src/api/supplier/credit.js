import request from '@/utils/request'

// 保存征信配置
export function saveCreditConfig(params, data) {
  return request({
    url: '/supplier/credit-config/save',
    method: 'post',
    params,
    data
  })
}

// 获得征信评分项配置
export function getCreditItemConfig(params) {
  return request({
    url: '/supplier/credit-config/get',
    method: 'get',
    params
  })
}

// 启用/禁用征信评分项配置
export function enabledCreditItemConfig(id, enabled) {
  return request({
    url: '/supplier/credit-item-config/enabled',
    method: 'post',
    params: { id, enabled }
  })
}

// 更新征信评分项配置
export function updateCreditItemConfig(data) {
  return request({
    url: '/supplier/credit-item-config/save',
    method: 'post',
    data
  })
}

// 创建征信评分项配置
export function createCreditItemConfig(data) {
  return request({
    url: '/supplier/credit-item-config/create',
    method: 'post',
    data
  })
}

// 删除征信评分项配置
export function deleteCreditItemConfig(id) {
  return request({
    url: '/supplier/credit-item-config/delete',
    method: 'delete',
    params: { id }
  })
}

// 获得征信评分项配置列表
export function getCreditItemConfigList(ids) {
  return request({
    url: '/supplier/credit-item-config/list',
    method: 'get',
    params: { ids }
  })
}

// 获得征信评分项配置分页
export function getCreditItemConfigPage(pageVO) {
  return request({
    url: '/supplier/credit-item-config/page',
    method: 'get',
    params: pageVO
  })
}
// 获得征信评分项
export function getCreditItemConfigById(params) {
  return request({
    url: '/supplier/credit-item-config/get',
    method: 'get',
    params
  })
}

// 导出征信评分项配置 Excel
export function exportCreditItemConfigExcel(exportReqVO) {
  return request({
    url: '/supplier/credit-item-config/export-excel',
    method: 'get',
    params: exportReqVO,
    responseType: 'blob'
  })
}

// 获得供应商征信报告
export function getSupplierCreditReport(data) {
  return request({
    url: '/supplier/credit-investigation/get',
    method: 'post',
    data: data
  })
}

// 生成供应商征信报告
export function createSupplierCreditReport(data) {
  return request({
    url: '/supplier/credit-investigation/createReport',
    method: 'post',
    data: data
  })
}

// 获取供应商报告数字统计牌
export function getSupplierReportDashboard(params) {
  return request({
    url: '/supplier/credit-investigation/getSupplierReportDashboard',
    method: 'get',
    params
  })
}
// 获取供应商公司详情
export function getSupplierCompanyInfo(params) {
  return request({
    url: '/supplier/credit-investigation/getSupplierCompanyInfo',
    method: 'get',
    params
  })
}

// 根据模糊查询获取供应商
export function getSupplierDetail(params) {
  return request({
    url: '/supplier/base-info/get-supplier',
    method: 'get',
    params
  })
}

// 生成供应商征信报告PDF
export function createSupplierCreditReportPDF(params) {
  return request({
    url: '/supplier/credit-investigation/createReportPDF',
    method: 'get',
    params
  })
}
