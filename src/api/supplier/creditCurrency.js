import request from '@/utils/request'

// 创建征信币种汇率配置
export function createCreditCurrency(data) {
  return request({
    url: '/supplier/credit-currency/create',
    method: 'post',
    data: data
  })
}

// 更新征信币种汇率配置
export function updateCreditCurrency(data) {
  return request({
    url: '/supplier/credit-currency/update',
    method: 'put',
    data: data
  })
}

// 删除征信币种汇率配置
export function deleteCreditCurrency(id) {
  return request({
    url: '/supplier/credit-currency/delete?id=' + id,
    method: 'delete'
  })
}

// 获得征信币种汇率配置
export function getCreditCurrency(id) {
  return request({
    url: '/supplier/credit-currency/get?id=' + id,
    method: 'get'
  })
}

// 获得征信币种汇率配置分页
export function getCreditCurrencyPage(query) {
  return request({
    url: '/supplier/credit-currency/page',
    method: 'get',
    params: query
  })
}

// 导出征信币种汇率配置 Excel
export function exportCreditCurrencyExcel(query) {
  return request({
    url: '/supplier/credit-currency/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
