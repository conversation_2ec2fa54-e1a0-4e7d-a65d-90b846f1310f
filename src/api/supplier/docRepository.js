import request from '@/utils/request'

// 创建供应商文档库
export function createDocRepository(data) {
  return request({
    url: '/supplier/doc-repository/create',
    method: 'post',
    data: data
  })
}

// 更新供应商文档库
export function updateDocRepository(data) {
  return request({
    url: '/supplier/doc-repository/update',
    method: 'put',
    data: data
  })
}

// 删除供应商文档库
export function deleteDocRepository(params) {
  return request({
    url: '/supplier/doc-repository/delete-folder',
    method: 'delete',
    params
  })
}

// 删除供应商文档库
export function deleteDocRepositoryFile(id) {
  return request({
    url: '/supplier/doc-repository/delete-file?fileId=' + id,
    method: 'delete'
  })
}

// 获得供应商文档库
export function getDocRepository(id) {
  return request({
    url: '/supplier/doc-repository/get?id=' + id,
    method: 'get'
  })
}

// 获得供应商文档库列表
export function listFileList(data) {
  return request({
    url: '/supplier/doc-repository/listFileList',
    method: 'get',
    params: data
  })
}

// 获得供应商文档库分页
export function getDocRepositoryPage(query) {
  return request({
    url: '/supplier/doc-repository/list',
    method: 'get',
    params: query
  })
}

// 导出供应商文档库 Excel
export function exportDocRepositoryExcel(query) {
  return request({
    url: '/supplier/doc-repository/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 创建供应商文档库
export function repositoryFileBind(data) {
  return request({
    url: '/supplier/doc-repository/file-bind',
    method: 'post',
    data: data
  })
}
