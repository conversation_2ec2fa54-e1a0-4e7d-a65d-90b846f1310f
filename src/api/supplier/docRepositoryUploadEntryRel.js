import request from '@/utils/request'

// 更新文档库和上传入口的绑定关系
export function updateDocRepositoryUploadEntryRel(data) {
  return request({
    url: '/supplier/doc-repository-upload-entry-rel/update',
    method: 'put',
    data: data
  })
}

// 获得文档库和上传入口的绑定关系分页
export function getDocRepositoryUploadEntryRelPage(query) {
  return request({
    url: '/supplier/doc-repository-upload-entry-rel/page',
    method: 'get',
    params: query
  })
}

// 获得文档库文件夹目录
export function getDocRepositoryTreeSimple() {
  return request({
    url: '/supplier/doc-repository-upload-entry-rel/get-folder-simple-tree',
    method: 'get'
  })
}
