import request from '@/utils/request'

// 创建供应商基本信息
export function createDocRepository(data) {
  return request({
    url: '/supplier/doc-repository/create',
    method: 'post',
    data: data
  })
}

// 发送验证码
export function sendEmail(params) {
  return request({
    url: '/supplier/base-info/code',
    method: 'get',
    params
  })
}

// 注册提交
export function register(data) {
  return request({
    url: '/supplier/base-info/register',
    method: 'post',
    data
  })
}

// 供应商注册页面验证邮箱编码
export function checkEmailCode(data) {
  return request({
    url: '/supplier/base-info/checkEmailCode',
    method: 'post',
    data
  })
}

// 更新供应商基本信息
export function updateSupplierInfo(data) {
  return request({
    url: '/supplier/base-info/update',
    method: 'put',
    data
  })
}

// 更新供应商基本信息
export function readTianYanChaInfo(params) {
  return request({
    url: '/supplier/base-info/getCreditOpenReqInfo',
    method: 'get',
    params
  })
}

// 更新供应商基本信息
export function updatePaymentInfo(data, params) {
  return request({
    url: '/supplier/base-info/update-payment-info',
    method: 'put',
    data,
    params
  })
}

// 提交供应商基本信息
export function submitSupplier(params) {
  return request({
    url: '/supplier/base-info/submit',
    method: 'put',
    params
  })
}

// 获得供应商品牌分页
export function getSupplierBase(query) {
  return request({
    url: 'supplier/base-info/get',
    method: 'get',
    params: query
  })
}

// 获得供应商银行信息
export function getPayment(query) {
  return request({
    url: '/supplier/base-info/get-payment-info',
    method: 'get',
    params: query
  })
}

// 获得OA供应商开户行名称集合
export function getOaOpenBankNameList(data) {
  return request({
    url: '/supplier/bank-base-info/get-open-bank-list',
    method: 'post',
    data
  })
}

// 获得OA供应商银行基础数据
export function getOaBankBaseInfo(data) {
  return request({
    url: '/supplier/bank-base-info/get-bank-base-info',
    method: 'post',
    data
  })
}

// 获得erp 公司
export function getCompanyRel(query) {
  return request({
    url: '/supplier/base-info/get-company-rel',
    method: 'get',
    params: query
  })
}

// 更新erp 公司
export function updateCompanyRel(data, params) {
  return request({
    url: '/supplier/base-info/update-company-rel',
    method: 'put',
    data,
    params
  })
}

// 获得erp
export function getPurchaseRel(query) {
  return request({
    url: '/supplier/base-info/get-purchase-org-rel',
    method: 'get',
    params: query
  })
}
export function getQualifAndCert(query) {
  return request({
    url: '/supplier/base-info/get-qualification-certification',
    method: 'get',
    params: query
  })
}

export function updateQualifAndCert(data, params) {
  return request({
    url: '/supplier/base-info/update-qualification-certification',
    method: 'put',
    data,
    params
  })
}

// 更新erp 采购组织
export function updatePurchaseRel(data, params) {
  return request({
    url: '/supplier/base-info/update-purchase-org-rel',
    method: 'put',
    data,
    params
  })
}

// 导出设备
export function exportEquip(params) {
  return request({
    url: 'supplier/base-info/export-equipment-excel',
    method: 'post',
    params,
    responseType: 'blob'
  })
}

// 导入设备
export function importEquip(params) {
  return request({
    url: 'supplier/base-info/import-equipment',
    method: 'post',
    params
  })
}

// 通过供应商获取联系信息
export function getSupplierContact(params) {
  return request({
    url: '/supplier/base-info/get-contact',
    method: 'get',
    params
  })
}
// 通过供应商获取联系信息(多人)
export function getMultiContactSimple(params) {
  return request({
    url: '/supplier/base-info/get-multi-contact',
    method: 'get',
    params
  })
}

export function detailErpView(params) {
  return request({
    url: '/supplier/edit-info/detail-erp-view',
    method: 'get',
    params
  })
}
export function submitErpView(data) {
  return request({
    url: '/supplier/edit-info/submit-erp-view',
    method: 'post',
    data
  })
}

export function rejectErpView(data) {
  return request({
    url: '/supplier/edit-info/rejectErpView',
    method: 'post',
    data
  })
}

// 变更编辑-基础信息详情
export function getEditBaseInfoView(query) {
  return request({
    url: '/supplier/edit-info/detail-base-Info-view',
    method: 'get',
    params: query
  })
}

// 变更编辑-基础信息变更提交
export function createBaseInfoView(data) {
  return request({
    url: '/supplier/edit-info/submit-base-Info-view',
    method: 'post',
    data
  })
}
export function detailBankInfo(params) {
  return request({
    url: '/supplier/edit-info/detail-bank-info',
    method: 'get',
    params
  })
}

export function inviteShare(data) {
  return request({
    url: '/supplier/info-change-record/invite-share',
    method: 'post',
    data
  })
}

export function submitBankInfo(data) {
  return request({
    url: '/supplier/edit-info/submit-bank-info',
    method: 'post',
    data
  })
}
export function rejectBankInfo(data) {
  return request({
    url: '/supplier/edit-info/reject-bank-info',
    method: 'post',
    data
  })
}

// 变更编辑-基础信息变更退回
export function rejectBaseInfoView(data) {
  return request({
    url: '/supplier/edit-info/reject-base-Info-view',
    method: 'post',
    data
  })
}

// 变更编辑-系统与资质详情
export function getEditSystemAndQualInfoView(params) {
  return request({
    url: '/supplier/edit-info/detail-sys-qual-view',
    method: 'get',
    params
  })
}

// 变更编辑-系统与资质详情提交
export function createSystemAndQualInfoView(data) {
  return request({
    url: '/supplier/edit-info/submit-sys-qual-view',
    method: 'post',
    data
  })
}

// 变更编辑-系统与资质详情退回
export function rejectSystemAndQualInfoView(data) {
  return request({
    url: '/supplier/edit-info/reject-sys-qual-view',
    method: 'post',
    data
  })
}

// 认证模块和分级模块是否同时启用。Y-需要关闭前端部分按钮入口
export function enableErpInfo() {
  return request({
    url: '/supplier/base-info/enable-erp-rel',
    method: 'get'
  })
}
