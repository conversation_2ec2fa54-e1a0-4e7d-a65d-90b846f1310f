import request from '@/utils/request'
// 获得供应商信息变更审批记录分页
export function getInfoChangeRecordPage(query) {
  return request({
    url: '/supplier/info-change-record/page',
    method: 'get',
    params: query
  })
}

// 导出供应商信息变更审批记录 Excel
export function exportInfoChangeRecordExcel(query) {
  return request({
    url: '/supplier/info-change-record/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 撤销编辑审批单
export function cancelRecord(id) {
  return request({
    url: '/supplier/info-change-record/cancel?id='+id,
    method: 'delete'
  })
}
