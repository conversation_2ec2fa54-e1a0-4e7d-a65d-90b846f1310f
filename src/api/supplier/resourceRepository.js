import request from '@/utils/request'

// 供应商资源库的首页分页
export function getResourceSupplierPage(data) {
  return request({
    url: '/supplier/resource/page',
    method: 'post',
    data: data
  })
}
export function getResourceSupplierStatistics() {
  return request({
    url: '/supplier/resource/statistics',
    method: 'get'
  })
}

// 邀请供应商继续修改信息
export function inviteModify(data) {
  return request({
    url: '/supplier/resource/inviteModify',
    method: 'post',
    data: data
  })
}
// 下载供应商导入模板
export function importSupplierTemplate() {
  return request({
    url: '/supplier/resource/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
// 批量导出
export function exportExcel(data) {
  return request({
    url: '/supplier/resource/export-excel',
    method: 'post',
    data,
    responseType: 'blob',
    timeout: 600000
  })
}

export function statisticsSupplierOverMonthExport() {
  return request({
    url: '/supplier/statistics/statistics-supplier-over-month-export',
    method: 'get',
    responseType: 'blob',
    timeout: 600000
  })
}
// 修改供应商级别
export function updateSupplierStatus(data) {
  return request({
    url: '/supplier/resource/updateSupplierStatus',
    method: 'post',
    data: data
  })
}

export function statisticsSupplierAll() {
  return request({
    url: '/supplier/statistics/statistics-supplier-all',
    method: 'get'
  })
}

export function statisticsSupplierProvince() {
  return request({
    url: '/supplier/statistics/statistics-supplier-province',
    method: 'get'
  })
}

export function statisticsSupplierCompanyCategory() {
  return request({
    url: '/supplier/statistics/statistics-supplier-company-category ',
    method: 'get'
  })
}
export function statisticsSupplierLevelDistribution() {
  return request({
    url: '/supplier/statistics/statistics-supplier-level-distribution',
    method: 'get'
  })
}
export function statisticsSupplierSp() {
  return request({
    url: '/supplier/statistics/statistics-supplier-sp ',
    method: 'get'
  })
}
export function statisticsSupplierSpExample() {
  return request({
    url: '/supplier/statistics/statistics-supplier-sp-example',
    method: 'get'
  })
}
export function statisticsSupplierLevelCategory() {
  return request({
    url: '/supplier/statistics/statistics-supplier-level-category',
    method: 'get'
  })
}
export function statisticsSupplierOverMonth() {
  return request({
    url: '/supplier/statistics/statistics-supplier-over-month',
    method: 'get'
  })
}

export function statisticsSupplierProvinceExport(data) {
  return request({
    url: '/supplier/statistics/statistics-supplier-province-export',
    method: 'get',
    data,
    responseType: 'blob',
    timeout: 600000
  })
}

// 供应商资源库-合作历史-基本信息 获得供应商基本信息
export function getBaseInfo(supplierId) {
  return request({
    url: '/supplier/base-info/get?id=' + supplierId,
    method: 'get'
  })
}

// 供应商资源库-合作历史-级别 获得供应商分级调整记录列表
export function supplierLevelLogListBySupplierId(supplierId) {
  return request({
    url: '/supplier/statistics/supplierLevelLogListBySupplierId?supplierId=' + supplierId,
    method: 'get'
  })
}

// 供应商资源库-合作历史-规模 获得供应商销售额信息
export function getTurnover(supplierId) {
  return request({
    url: '/supplier/statistics/get-turnover?supplierId=' + supplierId,
    method: 'get'
  })
}

// 供应商资源库-合作历史-规模 获取供应商年采购额
export function getPurchaseAmountBySupplier(supplierId) {
  return request({
    url: '/supplier/statistics/get-purchase-amount?supplierId=' + supplierId,
    method: 'get'
  })
}

// 供应商资源库-合作历史-订单交付 获取供应商合作历史统计数字牌
export function getSupplierStatisticsBoard(supplierId) {
  return request({
    url: '/supplier/statistics/getSupplierStatisticsBoard?supplierId=' + supplierId,
    method: 'get'
  })
}

// 供应商资源库-合作历史-订单交付 获取供应商合作历史完成订单统计列表
export function getSupplierCompleteOrderStatistics(supplierId) {
  return request({
    url: '/supplier/statistics/getSupplierCompleteOrderStatistics?supplierId=' + supplierId,
    method: 'get'
  })
}

// 供应商资源库-合作历史-物料及价格授予 年份统计列表
export function getSupplierMaterialYearList(supplierId) {
  return request({
    url: '/supplier/statistics/getSupplierMaterialYearList?supplierId=' + supplierId,
    method: 'get'
  })
}

// 供应商资源库-合作历史-物料及价格授予 获取供应商物料统计数字牌
export function getSupplierMaterialStatisticsBoard(supplierId) {
  return request({
    url: '/supplier/statistics/getSupplierMaterialStatisticsBoard?supplierId=' + supplierId,
    method: 'get'
  })
}

// 供应商资源库-合作历史-物料及价格授予 获取供应商价格授予详情列表
export function getSupplierMaterialAvplList(supplierId) {
  return request({
    url: '/supplier/statistics/getSupplierMaterialAvplList?supplierId=' + supplierId,
    method: 'get'
  })
}

// 供应商资源库-合作历史-质量事件 数字牌统计
export function getSupplierScarStatisticsBoard(supplierId) {
  return request({
    url: '/supplier/statistics/getSupplierScarStatisticsBoard?supplierId=' + supplierId,
    method: 'get'
  })
}

// 供应商资源库-合作历史-质量事件 统计列表
export function getSupplierScarStatisticsList(supplierId) {
  return request({
    url: '/supplier/statistics/getSupplierScarStatisticsList?supplierId=' + supplierId,
    method: 'get'
  })
}

// 供应商资源库-合作历史-质量事件 统计图
export function getSupplierScarStatisticsFigure(supplierId) {
  return request({
    url: '/supplier/statistics/getSupplierScarStatisticsFigure?supplierId=' + supplierId,
    method: 'get'
  })
}

// 供应商资源库-合作历史-绩效表现
export function getSupplierSP(supplierId) {
  return request({
    url: '/supplier/statistics/getSupplierSP?supplierId=' + supplierId,
    method: 'get'
  })
}
export function getSupplierLevelListBySupplierId(params) {
  return request({
    url: '/supplier/statistics/getSupplierLevelListBySupplierId',
    method: 'get',
    params
  })
}
