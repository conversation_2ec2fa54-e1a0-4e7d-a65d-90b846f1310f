import request from '@/utils/request'

// 获得简版供应商基础信息分页
export function getSupplierBaseInfoPage(data) {
  return request({
    url: '/supplier/base-info/simple-supplier-page',
    method: 'post',
    data: data
  })
}

// 获得简版供应商基础信息
export function getSupplierBaseInfo(id) {
  return request({
    url: '/supplier/base-info/get-simple-supplier?id=' + id,
    method: 'get'
  })
}

//保存简版供应商基础信息
export function saveSimpleBaseInfo(data) {
  return request({
    url: '/supplier/base-info/save-simple-supplier',
    method: 'post',
    data: data
  })
}

// 获得简版供应商导入模板
export function getImportTemplate() {
  return request({
    url: '/supplier/base-info/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}

// 导出简版供应商
export function exportSupplierBaseInfoExcel(data) {
  return request({
    url: '/supplier/base-info/export-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
