import request from '@/utils/request'

// 更新供应商与人员关系
export function saveUserRel(data) {
  return request({
    url: '/supplier/user-company-rel/save',
    method: 'post',
    data: data
  })
}

// 获得供应商与人员关系分页
export function getUserRelPage(query) {
  return request({
    url: '/supplier/user-company-rel/page',
    method: 'get',
    params: query
  })
}

// 获得当前供应商+公司选择的人员
export function getUsers(query) {
  return request({
    url: '/supplier/user-company-rel/list-users',
    method: 'get',
    params: query
  })
}

// 导出供应商与人员关系 Excel
export function exportUserRelExcel(data) {
  return request({
    url: '/supplier/user-company-rel/export-excel',
    method: 'post',
    data,
    responseType: 'blob',
    timeout: 600000
  })
}
