import request from '@/utils/request'
import store from '@/store'

// 创建品类
export function createCategories(data) {
  return request({
    url: '/system/categories/create',
    method: 'post',
    data: data
  })
}

// 创建品类定位
export function updatePosition(data) {
  return request({
    url: '/system/categories/updatePosition',
    method: 'put',
    data: data
  })
}

// 更新品类
export function updateCategories(data) {
  return request({
    url: '/system/categories/update',
    method: 'put',
    data: data
  })
}

// 删除品类
export function deleteCategories(id) {
  return request({
    url: '/system/categories/delete?id=' + id,
    method: 'delete'
  })
}

// 获得品类
export function getCategories(id) {
  return request({
    url: '/system/categories/get?id=' + id,
    method: 'get'
  })
}

// 获得品类列表
export function getCategoriesList(query) {
  return request({
    url: '/system/categories/list',
    method: 'get',
    params: { ...query, locale: store.getters.language }
  })
}

// 下载品类导入模板
export function categoriesImportTemplate() {
  return request({
    url: '/system/categories/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
// 从缓存获得品类列表
export function getCategoriesListCache(categoryIds, status) {
  return request({
    url: '/system/categories/list-category-cache',
    method: 'get',
    params: { categoryIds: categoryIds, locale: store.getters.language, status: status }
  })
}
// 导出品类 Excel
export function exportCategoriesExcel(query) {
  return request({
    url: '/system/categories/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取已有品类的level
export function getCategoryLevel() {
  return request({
    url: '/system/categories/level',
    method: 'get'
  })
}
