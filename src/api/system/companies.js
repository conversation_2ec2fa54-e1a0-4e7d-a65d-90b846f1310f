import request from '@/utils/request'
// 获得公司列表
export function getCompanyLists(params) {
  return request({
    url: '/system/companies/listForOther',
    method: 'get',
    params
  })
}
// 创建公司表信息
export function createCompanies(data) {
  return request({
    url: '/system/companies/create',
    method: 'post',
    data: data
  })
}

// 更新公司表信息
export function updateCompanies(data) {
  return request({
    url: '/system/companies/update',
    method: 'put',
    data: data
  })
}
// 获得公司表信息
export function getCompanies(id) {
  return request({
    url: '/system/companies/get?id=' + id,
    method: 'get'
  })
}

// 获得公司表信息分页
export function getCompaniesPage(data) {
  return request({
    url: '/system/companies/page',
    method: 'post',
    data
  })
}
