import request from '@/utils/request'

// 创建公司本币
export function createCompanyLocalCurrencyRel(data) {
  return request({
    url: '/system/company-local-currency-rel/create',
    method: 'post',
    data: data
  })
}

// 更新公司本币
export function updateCompanyLocalCurrencyRel(data) {
  return request({
    url: '/system/company-local-currency-rel/update',
    method: 'put',
    data: data
  })
}

// 获得公司本币
export function getCompanyLocalCurrencyRel(id) {
  return request({
    url: '/system/company-local-currency-rel/get?id=' + id,
    method: 'get'
  })
}

// 获得公司本币分页
export function getCompanyLocalCurrencyRelPage(query) {
  return request({
    url: '/system/company-local-currency-rel/page',
    method: 'get',
    params: query
  })
}

