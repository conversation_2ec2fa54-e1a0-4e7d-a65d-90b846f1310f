import request from '@/utils/request'
import store from '@/store'

// 创建国家地区
export function createCountryRegion(data) {
  return request({
    url: '/system/country-region/create',
    method: 'post',
    data: data
  })
}

// 查询国家地区列表
export function listCountryRegionAll(query) {
  return request({
    url: `/system/country-region/list-all`,
    method: 'get'
  })
}

// 查询国家地区列表
export function listCountryRegion(query) {
  return request({
    url: `/system/country-region/list`,
    method: 'get',
    params: { ...query, locale: store.getters.language }
  })
}

// 更新国家地区
export function updateCountryRegion(data) {
  return request({
    url: '/system/country-region/update',
    method: 'put',
    data: data
  })
}

// 删除国家地区
export function deleteCountryRegion(id) {
  return request({
    url: '/system/country-region/delete?id=' + id,
    method: 'delete'
  })
}

// 获得国家地区
export function getCountryRegion(id) {
  return request({
    url: '/system/country-region/get?id=' + id,
    method: 'get'
  })
}

// 获得国家地区分页
export function getCountryRegionPage(query) {
  return request({
    url: '/system/country-region/page',
    method: 'get',
    params: query
  })
}

// 导出国家地区 Excel
export function exportCountryRegionExcel(query) {
  return request({
    url: '/system/country-region/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导入国家地区模板 Excel
export function importTemplate() {
  return request({
    url: '/system/country-region/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
