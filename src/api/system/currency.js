import request from '@/utils/request'

// 创建币种
export function createCurrency(data) {
  return request({
    url: '/system/currency/create',
    method: 'post',
    data: data
  })
}

// 更新币种
export function updateCurrency(data) {
  return request({
    url: '/system/currency/update',
    method: 'put',
    data: data
  })
}

// 删除币种
export function deleteCurrency(id) {
  return request({
    url: '/system/currency/delete?id=' + id,
    method: 'delete'
  })
}

// 获得币种
export function getCurrency(id) {
  return request({
    url: '/system/currency/get?id=' + id,
    method: 'get'
  })
}

// 获得币种分页
export function getCurrencyPage(query) {
  return request({
    url: '/system/currency/page',
    method: 'get',
    params: query
  })
}

// 从缓存中获得币种列表
export function listCurrencyFromCache(query) {
  return request({
    url: '/system/currency/cache',
    method: 'get',
    params: query
  })
}

// 导出币种 Excel
export function exportCurrencyExcel(query) {
  return request({
    url: '/system/currency/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
