import request from '@/utils/request'

// 创建系统-人员级别的前端列表字段缓存数据记录
export function createFrontTableFieldsUserRel(data) {
  return request({
    url: '/system/front-table-fields-user-rel/save',
    method: 'post',
    data: data
  })
}

// 获得系统-人员级别的前端列表字段缓存数据记录
export function getFrontTableFieldsUserRel(userId, tableId) {
  return request({
    url: '/system/front-table-fields-user-rel/get',
    method: 'get',
    params: { userId, tableId }
  })
}
