import request from '@/utils/request'

// 创建自定义表单表,建议表单模板仅在同一个模块内使用
export function createCustomForm(data) {
  return request({
    url: '/system/custom-form/create',
    method: 'post',
    data
  })
}

// 更新自定义表单表,建议表单模板仅在同一个模块内使用
export function updateCustomForm(data) {
  return request({
    url: '/system/custom-form/update',
    method: 'put',
    data
  })
}

// 删除自定义表单表,建议表单模板仅在同一个模块内使用
export function deleteCustomForm(id) {
  return request({
    url: `/system/custom-form/delete?id=${id}`,
    method: 'delete'
  })
}

// 获得自定义表单表,建议表单模板仅在同一个模块内使用
export function getCustomForm(id) {
  return request({
    url: `/system/custom-form/get?id=${id}`,
    method: 'get'
  })
}

// 获得自定义表单表,建议表单模板仅在同一个模块内使用分页
export function getCustomFormPage(query) {
  return request({
    url: '/system/custom-form/page',
    method: 'get',
    params: query
  })
}

// 获得自定义表单表,建议表单模板仅在同一个模块内使用分页
export function getCustomFormList(query) {
  return request({
    url: '/system/custom-form/list',
    method: 'get'
  })
}

export function getCustomFormListDetail(query) {
  return request({
    url: '/system/custom-form/detail',
    method: 'get',
    params: query
  })
}

export function saveCustomForm(data) {
  return request({
    url: '/system/custom-form/data-save',
    method: 'post',
    data
  })
}

export function getCustomFormListValues(query) {
  return request({
    url: '/system/custom-form/values',
    method: 'get',
    params: query
  })
}

// 导出自定义表单表,建议表单模板仅在同一个模块内使用 Excel
export function exportCustomFormExcel(query) {
  return request({
    url: '/system/custom-form/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
