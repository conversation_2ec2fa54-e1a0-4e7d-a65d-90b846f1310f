import request from '@/utils/request'

// 创建自定义表单列规则
export function createCustomFormSchemas(data) {
  return request({
    url: '/system/custom-form-schemas/create',
    method: 'post',
    data: data
  })
}

// 更新自定义表单列规则
export function updateCustomFormSchemas(data) {
  return request({
    url: '/system/custom-form-schemas/update',
    method: 'put',
    data: data
  })
}

// 删除自定义表单列规则
export function deleteCustomFormSchemas(id) {
  return request({
    url: '/system/custom-form-schemas/delete?id=' + id,
    method: 'delete'
  })
}

// 获得自定义表单列规则
export function getCustomFormSchemas(id) {
  return request({
    url: '/system/custom-form-schemas/get?id=' + id,
    method: 'get'
  })
}

// 获得自定义表单列规则分页
export function getCustomFormSchemasPage(query) {
  return request({
    url: '/system/custom-form-schemas/page',
    method: 'get',
    params: query
  })
}

// 导出自定义表单列规则 Excel
export function exportCustomFormSchemasExcel(query) {
  return request({
    url: '/system/custom-form-schemas/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
