import request from '@/utils/request'

// 创建自定义表单子模块
export function createCustomSection(data) {
  return request({
    url: '/system/custom-section/create',
    method: 'post',
    data: data
  })
}

// 更新自定义表单子模块
export function updateCustomSection(data) {
  return request({
    url: '/system/custom-section/update',
    method: 'put',
    data: data
  })
}

// 删除自定义表单子模块
export function deleteCustomSection(id) {
  return request({
    url: '/system/custom-section/delete?id=' + id,
    method: 'delete'
  })
}

// 获得自定义表单子模块
export function getCustomSection(id) {
  return request({
    url: '/system/custom-section/get?id=' + id,
    method: 'get'
  })
}

// 获得自定义表单子模块分页
export function getCustomSectionPage(query) {
  return request({
    url: '/system/custom-section/page',
    method: 'get',
    params: query
  })
}

// 获得自定义表单子模块
export function getCustomSectionList(query) {
  return request({
    url: '/system/custom-section/list',
    method: 'get',
    params: query
  })
}

// 导出自定义表单子模块 Excel
export function exportCustomSectionExcel(query) {
  return request({
    url: '/system/custom-section/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
