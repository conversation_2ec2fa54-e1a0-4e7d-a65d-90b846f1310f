import request from '@/utils/request'

// 创建工厂
export function createFactory(data) {
  return request({
    url: '/system/factory/create',
    method: 'post',
    data: data
  })
}

// 更新工厂
export function updateFactory(data) {
  return request({
    url: '/system/factory/update',
    method: 'put',
    data: data
  })
}

// 删除工厂
export function deleteFactory(id) {
  return request({
    url: '/system/factory/delete?id=' + id,
    method: 'delete'
  })
}

// 获得工厂
export function getFactory(id) {
  return request({
    url: '/system/factory/get?id=' + id,
    method: 'get'
  })
}

// 获得工厂分页
export function getFactoryPage(query) {
  return request({
    url: '/system/factory/page',
    method: 'get',
    params: query
  })
}

// 获得工厂-from db
export function listFactory(query) {
  return request({
    url: '/system/factory/list',
    method: 'get',
    params: query
  })
}

// 获得工厂-from cache
export function listFactoryFromCache(query) {
  return request({
    url: '/system/factory/cache',
    method: 'get',
    params: query
  })
}

// 根据指定的工厂获取其关联的采购组织集合
export function getOrgIdByFactoryId(factoryId) {
  return request({
    url: '/system/factory/get-org-by-factoryId?factoryId=' + factoryId,
    method: 'get'
  })
}

// 导出工厂 Excel
export function exportFactoryExcel(query) {
  return request({
    url: '/system/factory/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
export function listPurchaseOrgFactoryFromCache(query) {
  return request({
    url: '/system/factory/list-purchaseorgfactory-cache',
    method: 'get',
    params: query
  })
}
