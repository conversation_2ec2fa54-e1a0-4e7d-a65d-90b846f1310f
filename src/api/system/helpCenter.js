import request from '@/utils/request'

// 创建帮助中心
export function createHelpCenter(data) {
  return request({
    url: '/system/help-center/create',
    method: 'post',
    data: data
  })
}

// 更新帮助中心
export function updateHelpCenter(data) {
  return request({
    url: '/system/help-center/update',
    method: 'put',
    data: data
  })
}

// 删除帮助中心
export function deleteHelpCenter(id) {
  return request({
    url: '/system/help-center/delete?id=' + id,
    method: 'delete'
  })
}

// 获得帮助中心
export function getHelpCenter(id) {
  return request({
    url: '/system/help-center/get?id=' + id,
    method: 'get'
  })
}

// 获得帮助中心分页
export function getHelpCenterPage(query) {
  return request({
    url: '/system/help-center/page',
    method: 'get',
    params: query
  })
}
