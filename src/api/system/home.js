import request from '@/utils/request'
// 获得公司列表
export function getSystemDataResult() {
  return request({
    url: '/ov/system-data-result/get',
    method: 'get'
  })
}
export function getCumulativeDataResult() {
  return request({
    url: '/ov/cumulative-data-result/get',
    method: 'get'
  })
}
export function getUserTodoShortcut() {
  return request({
    url: '/ov/user-todo-shortcut/get-todo-list',
    method: 'get'
  })
}

export function getShortcutList() {
  return request({
    url: '/ov/user-todo-shortcut/get-shortcut-list',
    method: 'get'
  })
}
export function getUserTodoList() {
  return request({
    url: '/ov/user-todo-shortcut-config/get-user-todo-list',
    method: 'get'
  })
}
export function getUserShortcutList() {
  return request({
    url: '/ov/user-todo-shortcut-config/get-user-shortcut-list',
    method: 'get'
  })
}

export function getSystemNoticePage(params) {
  return request({
    url: '/system/notice/page',
    method: 'get',
    params
  })
}

export function getSystemHelpCenter(params) {
  return request({
    url: '/system/help-center/page',
    method: 'get',
    params
  })
}

export function updateUserTodoShortcutConfig(data) {
  return request({
    url: '/ov/user-todo-shortcut-config/update',
    method: 'put',
    data
  })
}

