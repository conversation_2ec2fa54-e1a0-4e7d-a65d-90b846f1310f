import request from '@/utils/request'

// 获得邮件日志
export function getMailLog(id) {
  return request({
    url: '/system/mail-log/get?id=' + id,
    method: 'get'
  })
}

// 获得邮件日志分页
export function getMailLogPage(query) {
  return request({
    url: '/system/mail-log/page',
    method: 'get',
    params: query
  })
}
// 重发消息日志
export function resendMsgLog(id) {
  return request({
    url: '/system/mail-log/resend?id=' + id,
    method: 'post'
  })
}


export function batchResendMsgLog(data) {
  return request({
    url: '/system/mail-log/batchResend',
    method: 'post',
    data
  })
}
