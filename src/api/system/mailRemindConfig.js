import request from '@/utils/request'

// 创建邮件提醒配置
export function createMailRemindConfig(data) {
  return request({
    url: '/system/mail-remind-config/create',
    method: 'post',
    data: data
  })
}

// 更新邮件提醒配置
export function updateMailRemindConfig(data) {
  return request({
    url: '/system/mail-remind-config/update',
    method: 'put',
    data: data
  })
}

// 删除邮件提醒配置
export function deleteMailRemindConfig(id) {
  return request({
    url: '/system/mail-remind-config/delete?id=' + id,
    method: 'delete'
  })
}

// 获得邮件提醒配置
export function getMailRemindConfig(id) {
  return request({
    url: '/system/mail-remind-config/get?id=' + id,
    method: 'get'
  })
}

// 获得邮件提醒配置分页
export function getMailRemindConfigPage(query) {
  return request({
    url: '/system/mail-remind-config/page',
    method: 'get',
    params: query
  })
}
