import request from '@/utils/request'

// 创建系统模块
export function createModule(data) {
  return request({
    url: '/system/module/create',
    method: 'post',
    data: data
  })
}

// 更新系统模块
export function updateModule(data) {
  return request({
    url: '/system/module/update',
    method: 'put',
    data: data
  })
}

// 删除系统模块
export function deleteModule(id) {
  return request({
    url: '/system/module/delete?id=' + id,
    method: 'delete'
  })
}

// 获得系统模块
export function getModule(id) {
  return request({
    url: '/system/module/get?id=' + id,
    method: 'get'
  })
}

// 获得所有系统模块
export function getAllModule() {
  return request({
    url: '/system/module/list',
    method: 'get'
  })
}

// 获得系统模块分页
export function getModulePage(query) {
  return request({
    url: '/system/module/page',
    method: 'get',
    params: query
  })
}

// 导出系统模块 Excel
export function exportModuleExcel(query) {
  return request({
    url: '/system/module/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 赋予模块与数据权限规则的关系
export function assignModulePermission(data) {
  return request({
    url: '/system/module-permission/assign-module-permission',
    method: 'post',
    data: data
  })
}
// 根据模块编码获取选择的数据权限规则
export function getModulePermission(moduleCode) {
  return request({
    url: '/system/module-permission/get?moduleCode=' + moduleCode,
    method: 'get'
  })
}
