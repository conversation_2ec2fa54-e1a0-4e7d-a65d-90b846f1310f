import request from '@/utils/request'

// 创建采购组织
export function createPurchaseOrg(data) {
  return request({
    url: '/system/purchase-org/create',
    method: 'post',
    data: data
  })
}

// 更新采购组织
export function updatePurchaseOrg(data) {
  return request({
    url: '/system/purchase-org/update',
    method: 'put',
    data: data
  })
}

// 删除采购组织
export function deletePurchaseOrg(id) {
  return request({
    url: '/system/purchase-org/delete?id=' + id,
    method: 'delete'
  })
}

// 获得采购组织
export function getPurchaseOrg(id) {
  return request({
    url: '/system/purchase-org/get?id=' + id,
    method: 'get'
  })
}

// 绑定采购组织和工厂集合的关系
export function bindFactories(data) {
  return request({
    url: '/system/purchase-org/bind',
    method: 'post',
    data: data
  })
}

// 根据采购组织id获取绑定的工厂id集合
export function listFactoryIds(orgId) {
  return request({
    url: '/system/purchase-org/list-factory-ids?orgId=' + orgId,
    method: 'get'
  })
}

// 获得采购组织分页
export function getPurchaseOrgPage(query) {
  return request({
    url: '/system/purchase-org/page',
    method: 'get',
    params: query
  })
}

// 导出采购组织 Excel
export function exportPurchaseOrgExcel(query) {
  return request({
    url: '/system/purchase-org/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取采购组织数据
export function getPurchaseOrgCache(params) {
  return request({
    url: '/system/purchase-org/list-purchase-org-cache',
    method: 'get',
    params
  })
}

