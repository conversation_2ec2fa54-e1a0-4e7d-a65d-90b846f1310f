import request from '@/utils/request'

// 创建系统标签多语言翻译表（租户通用标签翻译数据）
export function createTranslationLabel(data) {
  return request({
    url: '/system/translation-label/create',
    method: 'post',
    data: data
  })
}

// 更新系统标签多语言翻译表（租户通用标签翻译数据）
export function updateTranslationLabel(data) {
  return request({
    url: '/system/translation-label/update',
    method: 'put',
    data: data
  })
}

// 删除系统标签多语言翻译表（租户通用标签翻译数据）
export function deleteTranslationLabel(id) {
  return request({
    url: '/system/translation-label/delete?id=' + id,
    method: 'delete'
  })
}

// 获得系统标签多语言翻译表（租户通用标签翻译数据）
export function getTranslationLabel(id) {
  return request({
    url: '/system/translation-label/get?id=' + id,
    method: 'get'
  })
}

// 获得系统标签多语言翻译表（租户通用标签翻译数据）分页
export function getTranslationLabelPage(query) {
  return request({
    url: '/system/translation-label/page',
    method: 'get',
    params: query
  })
}

export function getTranslationLabelList(query) {
  return request({
    url: '/system/translation-label/list',
    method: 'get',
    params: query
  })
}

// 导出系统标签多语言翻译表（租户通用标签翻译数据） Excel
export function exportTranslationLabelExcel(query) {
  return request({
    url: '/system/translation-label/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取多语言标签
export function getAllTranslationLabel(query) {
  return request({
    url: '/system/translation-language/select_list',
    method: 'get',
    params: query
  })
}
