import request from '@/utils/request'
import { praseStrEmpty } from '@/utils/ruoyi'

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/page',
    method: 'get',
    params: query
  })
}

// 获取用户精简信息列表
export function listSimpleUsers() {
  return request({
    url: '/system/user/list-all-simple',
    method: 'get'
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/system/user/get?id=' + praseStrEmpty(userId),
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/system/user/create',
    method: 'post',
    data: data
  })
}

// incap给用户分配客户
export function incapAssignCustomer(data) {
  return request({
    url: '/system/user/assign-customer',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/system/user/update',
    method: 'put',
    data: data
  })
}

// 创建账户
export function createAccount(data) {
  return request({
    url: '/system/user/createAccount',
    method: 'post',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: '/system/user/delete?id=' + userId,
    method: 'delete'
  })
}

// 导出用户
export function exportUser(query) {
  return request({
    url: '/system/user/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 用户密码重置
export function resetUserPwd(id, password) {
  const data = {
    id,
    password
  }
  return request({
    url: '/system/user/update-password',
    method: 'put',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/system/user/update-status',
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile/get',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile/update',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/update-password',
    method: 'put',
    data: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/system/user/profile/update-avatar',
    method: 'put',
    data: data
  })
}

// 上传签章
export function updateUserSeal(data) {
  return request({
    url: '/system/user/profile/update-seal',
    method: 'put',
    data: data
  })
}

// 下载用户导入模板
export function importTemplate() {
  return request({
    url: '/system/user/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}

// 获取人员已经选择的采购组织
export function getUserOrgs(params) {
  return request({
    url: '/system/user-purchase-org/list-user-purchase-org',
    method: 'get',
    params
  })
}
export function getUserOrgFromCache(params) {
  return request({
    url: '/system/user-purchase-org/user-orgs-cache',
    method: 'get',
    params
  })
}
// 赋予人员采购组织的关系
export function assignUserOrgs(data) {
  return request({
    url: '/system/user-purchase-org/assign-user-purchase-org',
    method: 'post',
    data
  })
}

// 缓存获取用户信息
export function getUsersCache(params) {
  return request({
    url: '/system/user/get-users-cache',
    method: 'get',
    params
  })
}

// 创建人员与上级关系
export function saveUserLeaderRel(data) {
  return request({
    url: '/system/user-leader-rel/save',
    method: 'post',
    data
  })
}

// 缓存获取用户信息
export function getUsersByOrgId(params) {
  return request({
    url: '/system/user-purchase-org/get-users-orgId',
    method: 'get',
    params
  })
}

export function listByTypeDict(params) {
  return request({
    url: '/system/dict-data/list-by-type',
    method: 'get',
    params
  })
}

export function saveHandoff(data) {
  return request({
    url: '/system/user/saveHandoff',
    method: 'post',
    data
  })
}

export function getUserCompany(params) {
  return request({
    url: '/system/user/get-users-company',
    method: 'get',
    params
  })
}
export function setPasswordExpiredPeriod(data) {
  return request({
    url: '/system/user/setPasswordExpiredPeriod',
    method: 'put',
    data
  })
}

export function sendEmail(params) {
  return request({
    url: '/system/auth/forget-pass-send-email',
    method: 'get',
    params
  })
}

export function checkEmail(params) {
  return request({
    url: '/system/auth/forget-pass',
    method: 'get',
    params
  })
}
export function forgetPassWord(data) {
  return request({
    url: '/system/auth/forget-pass-update',
    method: 'post',
    data
  })
}
