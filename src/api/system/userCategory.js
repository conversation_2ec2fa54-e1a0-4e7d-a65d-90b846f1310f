import request from '@/utils/request'

export function getUserCompanyCategory(params) {
  return request({
    url: '/system/user-category/list-user-company-categories',
    method: 'get',
    params
  })
}

/**
 * 获得人员与品类列表
 * @param params
 * @returns {*}
 */
export function getUserCategoryList(params) {
  console.log(params)
  return request({
    url: '/system/user-category/list',
    method: 'get',
    params: params
  })
}
export function assignUserCategories(data) {
  return request({
    url: '/system/user-category/assign-user-categories',
    method: 'post',
    data
  })
} export function getUsersByCategoryAndOrgId(query) {
  return request({
    url: '/system/user-category/get-user-categories',
    method: 'get',
    params: query
  })
}
