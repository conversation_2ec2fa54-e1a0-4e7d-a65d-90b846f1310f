import request from '@/utils/request'

/**
 * 获得人员与品类列表
 * @param params
 * @returns {*}
 */
export function getUserCategoryList(params) {
  return request({
    url: '/system/user-category-company/list',
    method: 'get',
    params: params
  })
}
export function assignUserCategories(data) {
  return request({
    url: '/system/user-category-company/assign-user-categories',
    method: 'post',
    data
  })
} export function getUsersByCategoryAndCompanyId(query) {
  return request({
    url: '/system/user-category-company/get-user-categories',
    method: 'get',
    params: query
  })
}
