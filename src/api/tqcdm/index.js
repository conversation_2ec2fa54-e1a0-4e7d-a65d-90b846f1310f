import request from '@/utils/request'

// 获得TQCDM评估项目分页（单据维度）
export function pageEvaluateRecord(data) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/page',
    method: 'post',
    data
  })
}

// 导出TQCDM评估项目列表 Excel
export function exportEvaluateRecord(data) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/export-excel',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 获得TQCDM评估项目分页（供应商维度）
export function pageEvaluateResultRecord(data) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/page-supplier-result',
    method: 'post',
    data
  })
}

// TQCDM评估项目附件相关-附件维护
export function saveFileRel(data) {
  return request({
    url: '/tqcdm/evaluate-file-rel/save',
    method: 'post',
    data
  })
}

// TQCDM评估项目附件相关-附件删除
export function delFileRel(fileId) {
  return request({
    url: '/tqcdm/evaluate-file-rel/del?fileId=' + fileId,
    method: 'delete'
  })
}

// 导出TQCDM供应商评估结果记录列表 Excel
export function exportEvaluateResultRecord(data) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/export-result-excel',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 获得TQCDM评估项目详情-供应商的结果列表
export function detailOfEvaluateSupplierAndTqcdmType(params) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/supplier-result/get',
    method: 'get',
    params
  })
}

// 供应商基础信息对比结果
export function supplierInfoCompareDownload(id) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/supplier-contrast?tqcdmId=' + id,
    method: 'get'
  })
}

// TQCDM评估项保存
export function saveEvaluateResult(data) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/save-evaluate',
    method: 'post',
    data
  })
}

// TQCDM评估项提交，基于指定tqcdm类型进行提交（多个供应商是一起提交）(前提是执行了保存接口，so此接口仅处理状态更新)
export function specTqcdmSubmit(params) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/submit-evaluate',
    method: 'post',
    params
  })
}

/**
 * 汇总提交
 * 1、包含评估汇总意见和 是否附带参考供应商信息
 * 2、有卡控，全部项提交后才可以执行
 * 3、更新状态
 */
export function summarySubmit(data) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/summary-submit-evaluate',
    method: 'post',
    data
  })
}

// TQCDM评估项撤回，基于指定tqcdm类型进行退回（多个供应商是一起撤回）
export function specTqcdmReject(params) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/reject-evaluate',
    method: 'post',
    params
  })
}

// 发起TQCDM评估项目之前的准备信息-下拉参考供应商
export function prepareReferenceSupplierEvaluate(params) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/pre-evaluate/dropdown-supplier',
    method: 'get',
    params
  })
}

// 能力矩阵报告下载
export function downloadReport(params) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/download-report',
    method: 'get',
    params
  })
}

// 发起TQCDM评估项目之前的准备信息-下拉模板信息
export function prepareTemplateEvaluate(params) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/pre-evaluate/dropdown-template',
    method: 'get',
    params
  })
}

// 发起TQCDM评估项目
export function startEvaluate(data) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/start-evaluate',
    method: 'post',
    data
  })
}

// 判断供应商+品类是否有TQCDM评估项目
export function hasTqcdmEvaluate(params) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/has-evaluate',
    method: 'get',
    params
  })
}

// 撤销TQCDM评估项目
export function cancelEvaluate(params) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/cancel',
    method: 'post',
    params
  })
}

// 获得TQCDM评估项目详情
export function detailEvaluate(params) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/get',
    method: 'get',
    params
  })
}

// 获得TQCDM模板配置分页结果
export function pageTqcdmTemplate(data) {
  return request({
    url: '/tqcdm/category-template-parent-config/page',
    method: 'post',
    data
  })
}

// 获得TQCDM模板配置详情
export function detailTqcdmTemplate(params) {
  return request({
    url: '/tqcdm/category-template-parent-config/detail',
    method: 'get',
    params
  })
}

// 保存TQCDM模板配置详情
export function saveTemplateDetailConfig(data) {
  return request({
    url: '/tqcdm/category-template-detail-config/save',
    method: 'post',
    data
  })
}
// 保存TQCDM模板配置详情
export function noMoreTip(data) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/no-more-tip',
    method: 'post',
    data
  })
}

// 获得TQCDM模板配置详情(没有结果）
export function getTemplateDetail(params) {
  return request({
    url: '/tqcdm/category-template-detail-config/detail',
    method: 'get',
    params
  })
}

// 删除TQCDM模板配置详情
export function deleteTemplateDetail(params) {
  return request({
    url: '/tqcdm/category-template-detail-config/delete',
    method: 'delete',
    params
  })
}

export function getReferenceSupplierDetail(params) {
  return request({
    url: '/tqcdm/evaluate-supplier-record/reference-supplier-detail/get',
    method: 'get',
    params
  })
}

export function downloadTemplate() {
  return request({
    url: '/tqcdm/category-template-detail-config/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
