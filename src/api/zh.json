{"code": 0, "data": {"tender": {"cancel": " 取消", "theProjectHasAlreadyBeenOpenedPleaseDoNotRepeatTheBidOpening": "项目已开标请勿重复开标", "theCurrentProjectNodeIsNotForEvaluationOperationIsProhibited": "当前项目节点不是评标,禁止操作", "openRequirementFile": "打开需求文件", "sendBiddingLink": "发送投标链接", "theSumOfDepartmentWeightsForScoringItemsMustBeEqualToPoints": "评分项部门权重总和必须等于100分", "score": "评分", "scoringCriteria": "评分标准", "theWinningBidDescriptionCannotBeEmpty": "中标说明不允许为空", "evaluationInformationXlsx": "评标信息.xlsx", "bidEvaluationInformation": "评标信息", "basicInformationOfProjectApproval": "立项基本信息", "invitationLetterUpload": "邀请函上传", "ratingPersonSelection": "评分人选择", "pleaseConfirmToDeleteTheCurrentBidInformation": "请确认删除当前标书信息?", "announcement": "发布公告", "theRequiredItemCategoryHasNotBeenFilledInPleaseSubmitItAfterCompletion": "必填项项目类别没有填写，请完成后提交", "thereAreNoSuppliersParticipatingInTheBiddingForTheCurrentProject": "当前项目没有供应商参与投标", "supplierInformationIsRequired": "供应商信息必填", "requirementDescription": "需求说明", "weightProportionOfBiddingDocuments": "标书类型权重占比", "theScoringRuleIsNotEmpty": "评分规则不为空", "bidDeadline": "投标截止时间", "theBidWinningAmountCannotBeEmpty": "中标金额不允许为空", "theAnswerAttributeIsNotEmpty": "回答属性不为空", "biddingRules": "招标规则", "resetCreator": "重置制定人", "theRequiredBidOpeningMethodHasNotBeenFilledOutPleaseSubmitItAfterCompletion": "必填项开标方式没有填写，请完成后提交", "otherStandardSettersHaveNotYetSubmittedTheScoringCriteriaUnableToSubmit": "其他制定标准人尚未提交评分标准，不能提交!", "startBidding": "开始投标", "doYouWantToAnswerAnyQuestions": "是否答疑", "theScoringCriteriaForTheCurrentProjectBiddingDocumentHaveBeenSubmittedOperationProhibited": "当前项目标书制定评分标准已提交,禁止操作!", "theScoreOfTheBidScoringItemMustBeGreaterThan": "标书评分项的分值必须大于0", "bidSubmission": "投标提交", "abnormalScore": "分值异常", "depositAttachment": "保证金附件", "theCurrentProjectHasBeenOpenedForBiddingAndChangesAreProhibited": "当前项目已开标,禁止更改", "theBiddingHasEnded": "投标已结束", "requirementDocument": "需求文件", "bidOpeningMethod": "开标方式", "sincerelyInviteYourCompanyTitle": "诚挚邀请贵司参加我司的招标项目，项目基本信息如下，更多信息在阅读完投标须知后的需求说明中可进行查看。", "dear": "尊敬的：", "theTotalScoreAndDepartmentWeightShouldBothBeEqualTo": "分值及部门权重总和应均等于100", "createANewProject": "创建新项目", "guaranteeAmount": "保证金额", "notificationSendingStatus": "通知发送状态", "deadlineForQa": "答疑截止时间", "doYouNeedADeposit": "是否需要保证金", "theRatingItemIsNotEmpty": "评分项不为空", "confirm": " 确认", "hasBeenSent": "已发送", "tenderRelease": "招标发布", "registrationTime": "报名时间", "pleaseFillInTheCompleteReplyContent": "请填写完整回复内容", "supplierRegistrationViewing": "供应商报名查看", "pleaseEnterTheCorrectEmailAddress": "请输入正确的邮件地址", "projectCategory": "项目类别", "biddingMethod": "招标方式", "addScoringCriteriaItemConfiguration": "添加评分标准项配置", "reply": "回复", "stepStatusError": "步骤状态错误", "invitation": "邀请函", "thePersonWhoFormulatedTheScoringCriteriaHasBeenReset": "制定评分标准人已重置", "theDeadlineForPublicRegistrationCannotBeLessThanTheCurrentTime": "公开报名截止时间不能小于当前时间", "theBiddingProjectRequiresADepositAndAVoucherToBeUploadedPleaseUploadTheVoucherFirst": "招标项目需要缴纳保证金并且需要上传凭证，请先上传凭证!", "bidContent": "投标内容", "weight": "权重", "downloadEvaluationInformation": "下载评标信息", "modifyScoringCriteriaItemConfiguration": "修改评分标准项配置", "bidOpeningInformation": "开标信息", "relatedQualificationsAndProjectCases": "相关资质及项目案例", "publicBidding": "公开招标", "pleaseEnterARatingItem": "请输入评分项", "theCurrentProjectBiddingScoreHasBeenSubmittedOperationProhibited": "当前项目标书评分已提交,禁止操作!", "textAnswerAttributeMustFillInReplyContent": "文本回答属性必填回复内容", "winningBidPrice": "中标价格", "theRequiredProjectNameHasNotBeenFilledInPleaseSubmitItAfterCompletion": "必填项项目名称没有填写，请完成后提交", "biddingProjectXlsx": "招投标项目.xlsx", "invitationToBid": "投标邀请", "theWinningSupplierCannotBeEmpty": "中标供应商不允许为空", "scoringCriteriaReviewRecord": "评分标准评审记录", "abandonedBid": "废标", "publicRegistrationDeadline": "公开报名截止时间", "notice": "通知", "noticeAttachment": "通知书附件", "selectTheWinningSupplier": "选择中标供应商", "successfulBidOpening": "开标成功", "projectNumber": "项目编号", "totalScore": "总分", "deadlineForBidding": "投标截止日期", "theSumOfBidWeightProportionsMustBeEqualToPoints": "标书权重占比之和必须等于100分", "theRequiredBiddingDocumentTypeHasNotBeenFilledInPleaseSubmitItAfterCompletion": "必填项标书类型没有填写，请完成后提交", "theDeadlineForPublicRegistrationCannotBeGreaterThanTheBiddingDeadline": "公开报名截止时间不能大于投标截止时间", "theBiddingDeadlineForMandatoryItemsHasNotBeenFilledInPleaseSubmitAfterCompletion": "必填项投标截止时间没有填写，请完成后提交", "pleaseEnterYourInterimWorkGoals": "请输入你的阶段性工作目标", "theDepositHasBeenPaidAndTheVoucherHasBeenUploaded": "已缴纳保证金并上传凭证。", "projectNumberProjectName": "项目编号、项目名称", "biddingStatus": "投标状态", "pleaseConfirmTheAboveWinningBidInformation": "请确认以上中标信息，确认后项目完成", "calibrationInformation": "定标信息", "pleaseEnterTheReason": "请输入废标原因，一旦废标，此项目不可恢复", "theBiddingDateMustBeGreaterThanTheCurrentDate": "投标日期必须大于当前日期", "bidIpAddress": "投标IP地址", "theCurrentProjectStatusProhibitsOperations": "当前项目状态禁止操作", "theCurrentUserDoesNotHaveAnAssociatedBidOrHasNotSubmittedABidCannotBeReturned": "当前用户没有关联的标书或者没有提交标书，不能退回!", "requirementSpecificationDocument": "需求说明文件", "uploadBidRequirementsDocument": " *上传标书需求文件", "ratingCriteriaCreator": "评分标准制定人", "theRatingStandardCreatorForTheRequiredItemDidNotFillItOutPleaseSubmitItAfterCompletingIt": "必填项评分标准制定人没有填写，请完成后提交", "depositAmount": "保证金金额", "theSumOfTheScoresForTheBidEvaluationItemsMustBeEqualToPoints": "标书评分项的分值之和必须等于100分", "remarksDescription": "备注说明", "theCurrentBiddingDocumentScoringItemAlreadyExists": "当前标书-评分项已存在!", "pleaseEnterTheBiddingInstructions": "请输入投标须知", "theCurrentProjectNodeIsNotPendingBidOpeningBidOpeningIsProhibited": "当前项目节点不是待开标,禁止开标", "returnToCreator": "退回制定人", "sendBiddingEmail": "发送投标邮件", "theBiddingDateHasExpired": "投标日期已截止", "bidType": "标书类型", "theBiddingProjectDocumentDoesNotExist": "招投标项目单据不存在", "addRatingItems": "添加评分项", "applicationDepartment": "申请部门", "estimatedAmount": "预计金额", "scoringRules": "评分规则", "bidWinningSituation": "中标情况", "noRaterHasBeenSetForBidScoringItems": "标书评分项的未设置评分人", "bidItems": "标书项", "proportion": "占比", "addSupplier": "添加供应商", "replyToCustomBiddingDocuments": "*回复自定义标书", "theBiddingStatusEnumerationDoesNotExist": "招投标状态枚举不存在", "theRequiredPublicRegistrationDeadlineHasNotBeenFilledOutPleaseSubmitAfterCompletingIt": "必填项公开报名截止时间没有填写，请完成后提交", "theRequiredDepositHasNotBeenFilledInPleaseSubmitItAfterCompletion": "必填项保证金没有填写，请完成后提交", "theCustomBidConfigurationDoesNotExist": "自定义标书配置不存在", "issuesAndRequirements": "问题与要求", "projectAttachments": "项目附件", "winningSupplier": "中标供应商", "emailAddressIsRequired": "邮件地址必填", "generateNotificationAttachment": "生成通知书附件", "costName": "费用名称", "projectCode": "项目编码", "biddingProgress": "投标进度", "iHaveReadAndAmAwareOfTheAboveRequirements": "我已阅读并知晓以上要求。", "calibration": "定标", "creator": "制定人", "theDocumentStatusIsInvalidPleaseDoNotPerformTheCurrentOperation": "单据状态失效，请勿当前操作", "developScoringCriteria": "制定评分标准", "supplierScoreSummary": "供应商得分汇总", "noticeToBidders": "投标须知", "taxRateCannotBeEmpty": "税率不允许为空", "theScoringStandardItemConfigurationDoesNotExist": "评分标准项配置不存在", "answerAttribute": "回答属性", "entryName": "项目名称", "theBiddingContentIsNotEmpty": "投标内容不为空", "theCurrentProjectHasBeenCalibrated": "当前项目已定标", "changeInBiddingRules": "招标规则变更", "sendNotifications": "发送通知", "theRequiredItemHasNotBeenFilledOutByTheFactoryPleaseSubmitItAfterCompletion": "必填项工厂没有填写，请完成后提交", "pleaseConfirmIfYouAreBiddingNow": "请确认是否现在投标？", "bidRequirementsDocument": "标书需求文件", "contactEmailAddress": "联系人邮箱地址", "bidOpening": "开标", "theCurrentProjectHasBeenPublished": "当前项目已发布!", "theWeightRatioOfTheBiddingDocumentScoringItemDepartmentMustBeGreaterThan": "标书-评分项-部门权重占比必须大于0", "theCurrentUserHasNotSubmittedARatingLabelAndCannotReturnIt": "当前用户未提交评分标注，不能退回!", "second": "秒", "viewRegisteredSuppliers": "报名供应商查看", "generateNotification": "生成通知书", "bidWinningInstructions": "中标说明", "theDeadlineForAnsweringMandatoryQuestionsHasNotBeenFilledOutPleaseSubmitAfterCompletingIt": "必填项答疑截止时间没有填写，请完成后提交", "tenderContent": "招标内容", "replyContent": "回复内容", "currencyCannotBeEmpty": "币种不允许为空", "theCurrentProjectHasBeenAbandonedAndInvalid": "当前项目已废标无效", "customBidTemplate": "自定义标书模板", "evaluationOfBids": "评标", "countdownToBidding": "投标倒计时", "numberOfParticipatingSuppliers": "参与供应商数量", "pleaseEnterRatingRules": "请输入评分规则", "supplierInformation": "供应商信息", "customizedBiddingDocuments": "自定义标书", "noticeAttachmentRequired": "通知书附件必填", "supplierBiddingResultNotificationNotConfiguredWithPdfTemplate": "供应商招标结果通知未配置PDF模板", "deleteRatingItems": "删除评分项", "theSumOfDepartmentWeightsForBidScoringItemsMustBeEqualToPoints": "标书评分项的部门权重之和必须等于100分", "ratingBy": "评分人", "scoringStandardScore": "评分标准分值", "recommendedWinningSupplierByTheSystem": "系统推荐中标供应商", "ratingDepartment": "评分人部门", "bidWinningFeeNameCannotBeEmpty": "中标费用名称不允许为空", "addExistingSupplier": "添加现有供应商：", "pleaseCheckIfAllTheReplyContentIsCompletedBeforeBidding": "请检查回复内容是否全部完成后再投标", "theCurrentProjectStatusHasBeenSubmittedAndCannotBeModified": "当前项目状态已提交,禁止修改!", "currentProjectStatusModificationProhibited": "当前项目状态,禁止修改!", "bidAttachmentPreview": "标书附件预览", "requiredItemCustomBiddingContentOrRequirementIsNotFilledInPleaseSubmitAfterCompletion": "必填项自定义标书内容或者要求未填写，请完成后提交", "theProportionOfBidWeightMustBeGreaterThan": "标书权重占比必须大于0", "hour": "小时", "biddingOrganizer": "招标组织人", "pleaseUploadTheBiddingRequirementDocumentBeforeSubmittingIt": "请上传标书需求文件再提交", "theRequiredItemIsNotFilledInByThePurchasingOrganizationPleaseSubmitItAfterCompletion": "必填项采购组织没有填写，请完成后提交", "bidPage": "投标页面", "functionalZone": "功能区", "contactEmail": "联系邮箱", "theProjectIsNotYetCompleted": "项目还未完成", "scoringItems": "评分项", "supplierQuantity": "供应商数量", "notSent": "未发送", "requirement": "要求", "requestIsNotEmpty": "要求不为空", "minute": "分钟", "departmentWeight": "部门权重", "theCurrentProjectHasNotYetCompletedTheCalibration": "当前项目尚未完成定标", "theCurrentProjectIsNotInTheStageOfDevelopingScoringStandardsOperationProhibited": "当前项目不在制定评分标准阶段,禁止操作!", "pleaseCheckTheRatingScoreWhichCannotExceedTheRatingItemScoreOrBeLessThan": "请检查评分分值,不能超过评分项分值,或小于0", "theCurrentBiddingDocumentScoringItemHasNotBeenCompletedAndCannotBeSubmitted": "【{0}】标书-【{1}】评分项尚未完成评分,不能提交", "suppliersFullName": "供应商全称", "theScoreIsGreaterThanTheMaximumScorePleaseCorrectIt": "评分分值大于满分分值，请修正！", "theBiddingInstructionsHaveBeenReadAndMustBeFilledOutPleaseCheck": "投标须知已阅必填，请勾选！"}, "auth": {"operationFailed": "操作失败", "attachmentRecordUpload": "附件记录上传", "authenticationDocumentCode": "单据号", "thePlannedCompletionOfAdmissionApprovalCannotBeEarlierThanThePlannedCompletionOfAdmissionApplication": "计划完成准入审批时间不能早于计划完成准入申请", "theCurrentAuthenticationStatusDoesNotSupportMultiPersonOperationPleaseConfirmAgain": "当前认证状态不支持多人操作，请重新确认", "branch": "分", "planToCompleteTheAdmissionApplication": "计划完成准入申请", "numberOfNonconformities": "不符合项数量", "supplierAdmittanceApproval": "供应商准入审批", "secondaryApproval": "二级审批", "theEmailAddressOfTheReviewerForTheWrittenReviewFormHasNotBeenSet": "书面评审单评分人邮箱未设置", "areYouSureYouWantToDelete": "确认是否删除?", "riskAssessmentIsNotReviewedInWriting": "不书面评审风险评估", "totalAuditScore": "审核总得分", "theTotalAuditScoreExceedsTheRangeAndCannotExceedPleaseReenterAllScores": "审核总得分超出范围，不得大于9999.99，请重新输入各项得分", "archivedBy": "归档人", "certificationResults": "认证结果", "invitationSucceeded": "邀请成功", "requiredSupplierCompletionTime": "要求供应商完成时间", "thereAreCurrentlyNoReturnableScoringItems": "当前没有可退回的评分项", "firstLevelAudit": "一级审核", "approveStatusIsError": "当前认证单据状态不对，请检查单据", "addAdditionalDescription": "添加其他说明", "theCurrentSupplierHasNotSetAPrimaryContactPersonPleaseContactTheAdministrator": "当前供应商未设置主要联系人，请联系管理员", "theOnsiteAuditScoreIsRequiredPleaseConfirmAgain": "现场审核得分必填，请重新确认", "theCurrentUserDoesNotHavePermissionToViewDocumentsPleaseContactTheAdministrator": "当前用户无权限查看单据，请联系管理员", "certificationChangeTypeNotSupported": "认证变更类型不支持", "pleaseSelectADataSource": "请选择数据源", "pleaseSelectASupplierAccountGroup": "请选择供应商账户组", "downloadReport": "下载报告", "pleaseSelectTheRequiredInformationRelatedToTheCompany": "请选择公司相关必填信息", "pleaseSelectASupplier": "请选择供应商", "certificationDocumentStatusDoesNotSupportRevocation": "认证单据状态不支持撤销", "currentStatusCannotBeReturned": "当前状态不能退回", "pleaseEnterTheReturnReason": "请输入退回原因", "reasonsForStartingCertification": "启动认证的理由:", "informationChange": "信息变更 | ", "onSiteAuditDate": "现场审核日期", "onlyOnePieceOfDataCanBeSelected": "仅支持选择一条数据", "thereAreUnfinishedScarDocumentsUnderThisAuthenticationPleaseCompleteTheScarDocumentsFirst": "此认证下存在未完成的SCAR单据，请先完成SCAR单据", "unqualifiedScore": "不合格分数: >=  ", "failedToGetOperationRecord": "获取操作记录失败", "uploadRiskAssessment": "上传风险评估", "pleaseSelectAPurchasingOrganization": "请选择采购组织", "see": "查看", "theFileListIsEmptySoWeWillNotFollowUpForNow": "文件列表为空，暂不跟催", "archivingRemarks": "归档备注", "certificationOverviewListxls": "认证总览列表.xlsx", "changeOfCertificationInformation": "认证信息变更", "confirmationOfAdoptionProcessIsMandatoryPleaseConfirmAgain": "确认采用流程必填，请重新确认", "itShallNotBeLessThanThePlannedCompletionTimeOfAuditwrittenReview": "不能小于计划完成审核/书面评审时间", "pleaseConfirmThatYouHaveCheckedTheInformationSubmittedByTheSupplier": "请先确认已查阅过供应商所提交的信息", "pleaseSelectAScoringItem": "请选择评分项", "theCurrentDocumentDoesNotSupportOperationPleaseConfirmTheRaterInformationOfTheCurrentDocumentAgain": "当前单据不支持操作，请重新确认当前单据的评分人信息", "expectedCompletionTime": "期望完成时间", "trialStartDate": "试用起始日期", "inviteRating": "邀请评分", "unableToObtainInformationFromThePreviousNodePleaseContactTheAdministrator": "无法获取上一节点的信息，请联系管理员", "documentName": "文件名称", "admissionApplication": "准入申请", "otherInstructionsForCertificationChange": "认证变更其他说明", "supplierCertificationStandards": "供应商认证标准", "trialScore": "   试用分数: >=  ", "askTheSupplierForAdditionalInformation": "要求供应商补充信息", "offSiteAuditRiskAssessment": "不现场审核风险评估", "areYouSureToModifyTheDataItem": "是否确认操作改数据项？", "country": "国家/地区", "theOnsiteAuditScoreTypeDoesNotExist": "现场审核得分类型不存在", "theCurrentArchivedInspectionDocumentDoesNotHaveAnAssociatedUser": "当前归档检查单据不存在关联用户", "thePlannedCompletionOfAdmissionApplicationCannotBeEarlierThanThePlannedCompletionOfReviewwrittenReview": "计划完成准入申请不能早于计划完成审核/书面评审", "addAnotherFile": "添加其他文件", "siteVisitcommunicationRecord": "现场拜访/沟通记录", "pleaseSelectTheRequiredInformationRelatedToThePurchasingOrganization": "请选择采购组织相关必填信息", "supplierDevelopmentReport": "供应商发展报告", "personnelEmailNotSet": "人员邮箱未设置", "followUpSupplierSelfassessment": "跟催供应商自评", "primaryKeyId": "主键id", "pleaseSelectTheScoringItemToBeReturned": "请选择需退回的评分项", "theSupplierIsInTheCertificationProcessPleaseConfirmAgain": "供应商处于认证流程中，请重新确认", "uploadAttachmentsToBrowse": "上传附件浏览", "operatorOfNextNode": "下一节点操作人", "certificationDocumentProcessRecordListxls": "认证单据流程记录列表.xlsx", "theCurrentDocumentDoesNotSupportDownloadingReports": "当前单据不支持下载报告", "writtenReviewScoresNotAllSubmitted": "书面评审得分项未全部提交", "supplierSelfAssessmentReport": "供应商自评报告", "demandMatchingAnalysisReport": "需求匹配性分析报告", "lastUpdatedBy": "最后更新人", "natureOfManufacturer": "厂商性质:", "thereAreRatingItemsThatHaveNotYetInvitedRatersPleaseCheck": "有评分项尚未邀请评分人,请检查", "nameOfQualificationAgreement": "资质协议名称", "pleaseSelectArchivist": "请选择归档人", "pleaseFillItOutCompletely": "请填写完整", "nodeStatus": "节点状态", "selectAll": "全选", "theAuthenticationDocumentDoesNotExist": "认证单据不存在", "initiateTheDevelopmentOfStandards": "启动制定标准", "electronicSignature": "电子签名", "modifyTheCertificationArchivist": "修改认证归档人", "processorCannotBeEmptyPleaseConfirmAgain": "处理人不能为空，请重新确认", "isItATemporarySupplier": "是否为临时供应商", "requirementsOfSupplierQualificationAgreement": "供应商资质协议要求", "categoryAttribution": "品类归属", "documentType": "单据类型", "temporaryStartDate": "临时起始日期", "supplierAdmissionApplication": "供应商准入申请", "thereIsDuplicateData": "存在重复数据", "noPreviousNodeInformationObtainedDataException": "未获取到上一节点信息,数据异常", "pleaseEnterTheSuppliersShortName": "请输入供应商简称", "theCurrentWrittenReviewDoesNotHaveASubVersionConfiguredForWrittenReviewPleaseConfirmAgain": "当前书面评审未配置书面评审得分项版本，请重新确认", "pleaseCompleteTheInformation": "请完善信息", "uploadDefaultReport": "上传缺省报告", "defaultremarks": "缺省/备注说明", "pleaseCheckTheFilingCheck": "请勾选归档检查", "turnover": "营业额", "onSiteAuditTeam": "现场审核团队", "pleaseSelectSupplierEchelonLevel": "请选择供应商梯队层级", "supplierSelfAssessmentForm": "供应商自评表", "addArchiveCheckInstructions": "添加归档检查说明", "theCurrentApprovalHasEndedAndCannotBeRepeated": "当前审批已结束，不能重复操作", "createSCAR": "创建审核改善单", "yes": "是", "supplierCertificationResultxls": "供应商认证结果.xlsx", "approvalComments": "审批意见", "demandAnalysisReport": "需求分析报告", "itemRatingNotCompletedPleaseConfirm": "项评分未完成，请确认！", "totalEvaluationScore": "评审总得分", "uploadDemandAnalysis": "上传需求分析", "confirmTheProcessAdopted": "确认采用流程", "acceptableScore": "   合格分数: >= ", "pleaseSelectTheHandler": "请选择办理人", "onSiteAudit": "现场审核", "receiptVerificationBasedOnInvoice": "基于发票的收货校验", "theCurrentAuthenticationDocumentIsNotInAProcessStatePleaseRefreshTheListAndReenter": "当前认证单据非流程状态，请刷新列表后重新进入", "pleaseSelectTheSupplierLevel": "请选择供应商等级", "reportTemplateNotConfiguredForAuthenticationPleaseContactTheAdministrator": "认证未配置报表模板，请联系管理员", "plannedTimeForCompletionOfAdmissionApproval": "计划完成准入审批时间", "theSystemRecommendationProcessIsRequiredPleaseConfirmAgain": "系统推荐流程必填，请重新确认", "planCompletionReviewwrittenReview": "计划完成审核/书面评审", "categorySelectionCriteriaAreRequiredPleaseConfirmAgain": "品类选择标准必填，请重新确认", "requestToUploadAttachments": "要求上传附件", "demandMatchingReport": "需求匹配报告", "archiverCannotBeBlank": "归档人不能为空", "theCurrentDocumentDoesNotSupportReturnPleaseConfirmAgain": "当前单据不支持退回，请重新确认", "supplierAbbreviationAlreadyExists": "供应商简称已存在", "supplierAbbreviationIsRequiredPleaseConfirmAgain": "供应商简称必填，请重新确认", "modifyAndUploadTheSpecifiedFolder": "修改上传指定的文件夹", "noLessThanThePlannedCompletionOfAdmissionApplication": "不能小于计划完成准入申请", "otherSupplementaryInformation": "其他补充信息", "keySupplierOrNot": "是否关键供应商", "uploadDemandMatchingReport": "上传需求匹配报告", "systemRecommendationProcess": "系统推荐流程", "supplierCompletionTimeRequiredPleaseConfirmAgain": "要求供应商完成时间必填，请重新确认", "informationVerification": "信息核实", "theTotalScoreOfTheWrittenReviewIsPleaseConfirmAgain": "书面评审总得分为0，请重新确认", "supplierCertificationProcessReport": "供应商认证过程报告", "onSiteAuditResults": "现场审核结果", "resetSucceeded": "重置成功", "onSiteAuditTeamLeader": "现场审核组长", "submitFailed": "提交失败", "certificationAuditConclusionConfiguration": "认证审核结论配置", "theReviewerForTheRatingItemHasBeenResetPleaseRefreshBeforeProceeding": "评分项的评审人已重置,请刷新后操作", "theLeadershipLevelSettingCannotBeLessThanLevelPleaseContactTheAdministrator": "领导层级设置不允许小于1层，请联系管理员", "pleaseSelectAPurchaseGroup": "请选择采购组", "noCorrespondingScoringItemFound": "未找到对应的评分项", "whetherToSign": "是否签署", "returnedSuccessfully": "退回成功", "otherInstructionsForAdmissionApplication": "准入申请其他说明", "explanationTheChangeTitle": "说明：供应商简称的变更属于系统全局的应用变更，请谨慎修改！", "electronicSignatureError": "电子签名错误", "entryApplicationLoginInformationIsRequiredPleaseConfirmAgain": "准入申请登录信息必填，请重新确认", "supplierAccessApplicationLoginInformation": "供应商准入申请登录信息", "onSiteAuditConclusion": "现场审核结论", "contentToBeChanged": "待变更内容", "thereIsNoSupplierSelfassessmentReportToFollowUpOn": "没有可跟催的供应商自评报告", "invoiceBasedReceiptVerification": "基于发票的收货验证", "supplierPrimaryContactEmailNotFound": "未找到供应商主联系人邮箱", "pleaseSelectReceiptVerificationBasedOnInvoice": "请选择基于发票的收货验证", "categoryCode": "品类代码", "scoreRangeSettingOfWrittenReviewConclusion": "书面评审结论分值区间设置", "reasonForInitiatingAuthenticationIsRequiredPleaseConfirmAgain": "启动认证的理由必填，请重新确认", "pleaseSelectACompany": "请选择公司", "operationNode": "操作节点", "uploadAttachments": "上传附件", "writtenReviewConclusion": "书面评审结论", "return": "退回", "detailsRequestFailed": "详情请求失败", "failInSend": "发送失败", "supplierInformationCompletionRate": "供应商信息完成率", "verificationOfSupplierInformation": "供应商信息核实", "pleaseSelectTheLoginType": "请选择登录类型", "supplementaryNotes": "补充说明", "theSpecifiedSupplierDidNotFillInPaymentInformationPleaseFillItOutAndTryAgain": "指定供应商未填写付款信息，请填写后重试", "filingInspection": "归档检查", "iHaveCheckedTheInformationSubmittedByTheSupplier": "我已查阅过供应商所提交的信息", "firstLevelApproval": "一级审批", "supplierGrade": "供应商等级", "admissionApproval": "准入审批", "returnFailed": "退回失败", "actualCompletionTime": "实际完成时间", "attachmentBrowsing": "附件浏览", "writtenReviewReport": "书面评审报告", "downloadProcessRecord": "下载流程记录", "supplierAccountGroup": "供应商账户组", "theCurrentRatingItemDoesNotSupportResettingPleaseConfirmAgain": "当前评分项不支持重置，请重新确认", "scoreRangeSettingOfOnsiteReviewConclusion": "现场评审结论分值区间设置", "thereAreNoReturnableScoringItemsThatHaveBeenSubmitted": "没有已提交的可退回评分项", "certificationDocumentType": "认证单据类型", "temporarySupplierOrNot": "是否临时供应商", "theOnsiteAuditDateOnsiteAuditTeamLeaderOnsiteAuditTeamAndAuditReportAreRequiredPleaseConfirmAgain": "现场审核日期、现场审核组长、现场审核团队、审核报告必填，请重新确认", "theWrittenReviewFormHasNotBeenSavedOrSubmitted": "书面评审单尚未保存或已提交", "invoiceInspectionBasedOnReceipt": "基于收货的发票检验", "filingInspectionInstructions": "归档检查说明", "approvalCommentsAreRequired": "审批意见必填", "thePurchaseOrganizationCannotBeBlank": "采购组织不能为空", "no": "否", "changeMethod": "变更方式", "followUpSuppliers": "跟催供应商", "pleaseSelectTheSupplierQualificationAgreementRequirements": "请选择供应商资质协议要求", "certificationChange": "认证变更", "supplierId": "供应商id", "loginType": "登录类型", "thirdLevelApproval": "三级审批", "supplierNameSupplierCode": "供应商名称、供应商编码", "categoryName": "品类名称", "multiDepartmentReviewInvitation": "多部门评审邀请", "thereAreStillRatingItemsInTheProcessPleaseResetThemBeforeReturningThem": "有尚在流程中的评分项,请重置后再退回", "newSupplierCertification": "新增供应商认证", "pleaseUploadFilesOrDefaultReports": "请上传文件或缺省报告", "startingTheProcessOfDevelopingStandardsWithoutSelectingPleaseConfirmAgain": "启动制定标准未选择流程，请重新确认", "uploadAuditReport": "上传审核报告", "writtenReview": "书面评审", "supplierName": "供应商名称", "theCurrentApproverDoesNotExistOperationNotAllowed": "当前审批人不存在,不允许操作", "supplierCertificationProgram": "供应商认证计划", "unableToObtainTheSpecifiedNumberOfLeadershipLevelsInTheSystemPleaseContactTheAdministrator": "无法获取到系统指定的领导层级数量，请联系管理员", "theCurrentAuthenticationDocumentNodeStatusDoesNotAllowOperation": "当前认证单据节点状态不允许操作", "onSiteAuditReport": "现场审核报告", "isItQuickCertification": "是否为快速认证", "pleaseEnterTheReturnDescription": "请输入退回说明", "firstCertification": "首次认证", "inviteRaters": "邀请评分人", "returnScore": "退回评分", "confirmSubmission": "确认提交？", "addCertifiedArchiver": "添加认证归档人"}, "financial": {"batchAdd": "批量添加", "paidAmountreference": "已付金额（参考）", "differenceAmountrmb": "差异金额 < 人民币 ", "viewStampedStatementOfAccount": "查看已盖章对账单", "invoiceInformationImportTemplatexlsx": "开票信息导入模板.xlsx", "taxRateRequiredForRmb": "人民币时税率必填", "paymentDateTypeCannotBeEmpty": "付款日类型不能为空", "invoicingInformation": "开票资料", "viewInvoice": "查看发票", "uploadInvoice": "上传发票", "financialDataNotExists": "{0}，该对账单不存在！", "pleaseUploadAStampedStatementOfAccount": "请上传已盖章对账单", "advance": "预付", "accountStatementHeader": "对账单抬头", "theTotalAmountOfInvoicingDataInOriginalCurrencyIncludingTaxExceedsTheAllowedDifferenceValueForSubmissionPleaseConfirmAgain": "开票资料原币价税合计总计超出允许提交的差异值，请重新确认", "theDocumentStatusDoesNotSupportRevocationPleaseTryAgain": "单据状态不支持撤销，请重试", "submissionOfDeliveryDateDataImportError": "批量提交交期操作导入报错", "theReconciliationDetailsLineDataIsEmptyPleaseInputAndResubmit": "对账明细行数据为空，请输入后再次提交", "otherAdditionAndSubtractionItems": "其他加减项目", "baseDateTypeCannotBeEmpty": "基准日类型不能为空", "relatedDocuments": "关联单据", "pleaseEnterPrepayment": "请输入预付(<=100)", "pleaseEnterTheNumberOfDays": "请输入天数", "scarSeefile": "scar.seeFile", "warehousingDate": "入库日期", "additionAndSubtractionTerms": "加减项", "totalAmountOfStatement": "对账单合计金额：", "areYouSureYouWantToCancelTheDocument": "是否确定撤销单据？", "addInboundData": "添加入库数据", "theImportDataFormatOfInvoicingInformationIsIncorrectPleaseVerifyAndTryAgain": "开票资料导入数据格式有误，请核对后重试", "baseDateType": "基准日类型", "statementXlsx": "对账单.xlsx", "handle": "办理", "preTaxAmountInOriginalCurrency": "原币税前金额", "pleaseEnterTheDifferenceAmount": "请输入差异金额", "invoiceVoucherNumberdocking": "发票凭证编号(对接)", "paymentDueDate": "付款到期日", "paymentDateType": "付款日类型", "accountPeriodInformationConfigurationXlsx": "账期信息配置.xlsx", "otherAdditionAndSubtractionAmounts": "其他加减项金额", "statementStatus": "对账单状态", "taxAmount": "税额", "theAmountCannotBeLessThanTheAmountBeforeTax": "含税金额不能小于未税金额，请重新确认", "reconciliationNumber": "对账单号", "areYouSureToExportAllAccountingPeriodInformationConfigurationDataItems": "是否确认导出所有账期信息配置数据项?", "voucherItemNumber": "凭证项目号", "totalAmountOfStatementincludingTax": "对账单总金额（含税）", "pleaseFillInTheTotalAmountOfOriginalCurrencyIncludingTax": "请填写原币价税合计", "totalPriceAndTaxInOriginalCurrency": "原币价税合计", "pleaseAddAtLeastOneInvoicingInformation": "请添加至少一条开票资料", "theNumberOfDaysCannotBeEmpty": "天数不能为空", "orderNumberReceiptVoucherNumberMaterialCode": "订单号、收货凭证号、物料编码", "scarviewattachments": "scar.viewAttachments", "total": "总计", "amountPaid": "已付金额", "receiptVoucherNumber": "入库凭证号", "pleaseSelectTheBaseDateType": "请选择基准日类型", "invoiceNumber": "发票号码", "pleaseUploadTheInvoice": "请上传发票", "pleaseEnterThePaymentPeriod": "请输入账期", "dicttypeconstantssystemWeekDicttypeconstantssystemDate": "DictTypeConstants.SYSTEM_WEEK, DictTypeConstants.SYSTEM_DATE", "modifyAccountingPeriodInformation": "修改账期信息", "taxAmountInOriginalCurrency": "原币税额", "paymentMark": "付款标记", "invoiceVoucherNumber": "发票凭证编号", "totalAmountOfOriginalStatement": "原对账单总金额：", "pleaseSelectPaymentDateType": "请选择付款日类型", "addUpToPiecesOfData": "最多添加10条数据", "whatIsTheRangeWithinTitle": "开票信息里的原币价税合计总计比对账单合计金额 可以小于多少范围以内，允许开票页面提交成功", "reportTemplateNotConfiguredPleaseContactAdministrator": "未配置报表模板，请联系管理员", "invoiceDate": "发票日期", "relatedPaymentApplication": "关联付款申请", "supplierCodeSupplierNameAbbreviation": "供应商编码、供应商名称、简称", "paymentDateCannotBeEmpty": "付款日期不能为空", "downloadStatement": "下载对账单 ", "financeConfiguration": "财务配置", "days": "天数", "uploadStampedStatementOfAccount": "上传已盖章对账单", "paymentDate": "付款日期", "statementDetailsXlsx": "对账单详情.xlsx", "finance": "财务"}, "system": {"smsChannelNumberCannotBeEmpty": "短信渠道编号不能为空", "departmentId": "部门ID", "endRequestTime": "结束请求时间", "failedToCreateUserDueToExceedingTheMaximumTenantQuota": "创建用户失败，原因：超过租户最大租户配额({0})！", "loginFailedAccountNotConfiguredWithRoles": "登录失败，账号没有配置角色", "addExchangeRate": "添加汇率", "addEmailAccount": "添加邮箱账号", "pleaseEnterTheModuleCode": "请输入模块编码", "templateSenderName": "模版发送人名称", "pleaseSelectTheCurrencyAfterConversion": "请选择转换后的币种", "taskStatus": "任务状态", "smsTypeCannotBeEmpty": "短信类型不能为空", "positionSequence": "岗位顺序", "theErrorCodePromptCannotBeEmpty": "错误码提示不能为空", "templateContentCannotBeEmpty": "模板内容不能为空", "dataLabel": "数据标签", "pleaseEnterSorting": "请输入排序", "dataStatus": "数据状态", "pleaseEnterARedirectableUriAddress": "请输入可重定向的 URI 地址", "menuDoesNotExist": "菜单不存在", "dictionaryDataIsNotInAnOpenStateAndSelectionIsNotAllowed": "字典数据({0})不处于开启状态，不允许选择", "sender": "发送人", "theOpeningStatusCannotBeEmpty": "开启状态不能为空", "operationType": "操作类型", "userPositionDoesNotExist": "用户岗位不存在", "pleaseEnterPermission": "请输入权限", "factoryCodeCannotBeEmpty": "工厂编码不能为空", "areYouSureToExportAllSmsLogDataItems": "是否确认导出所有短信日志数据项?", "role": "所属角色", "menuStatus": "菜单状态", "smsParameters": "短信参数", "systemBuiltin": "系统内置", "theImportedCategoryDataIsEmpty": "导入品类数据为空", "frequencyTypeCannotBeEmpty": "频率类型不能为空", "applicationIcon": "应用图标", "theLocalCurrencyOfCompanyAndYearDoesNotExist": "公司{0}和年份{1}的本币不存在", "addCategory": "添加品类", "result": "结果", "pleaseEnterTheKeyOfSmsApi": "请输入短信 API 的密钥", "toplevel": "顶级", "loginTime": "登录时间", "areYouSureToExportAllCountryregionDataItems": "是否确认导出所有国家地区数据项?", "addSmsChannel": "添加短信渠道", "pleaseSelectWhetherItIsFunctionalCurrency": "请选择是否为本币", "countryregionDoesNotExist": "国家地区不存在", "copyRole": "复制角色", "sendersName": "发件人名称", "modifyRole": "修改角色", "messageReceiver": "消息接收者", "processUserNumber": "处理用户编号", "abbreviation": "简称", "relationshipId": "关系id", "typeCannotBeEmpty": "类型不可以为空", "userId": "用户工号", "editDepartment": "修改部门", "builtinDataCannotBeAdded": "内置数据无法新增", "delConfirmMessage": "是否确认删除公司?", "thePasswordShouldHaveSymbol": "密码最少8位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊符号（!@$^&*?）", "theFormTitleObjectDoesNotExist": "表单标题对象不存在", "dataProcessingStatus": "数据处理状态", "userIp": "用户 IP", "information": "信息", "smsConfiguration": "短信配置", "anInternetAddressIsRequiredItStartsWithhttps": "访问的路由地址，如：`user`。如需外网地址时，则以 `http(s)://` 开头", "pleaseEnterTheLoginAddress": "请输入登录地址", "areYouSureYouWantToExportAllMessageLogDataItems": "是否确认导出所有消息日志数据项?", "modifyCustomTableSubModule": "修改自定义表单子模块", "positionSorting": "岗位排序", "companyCodeEnter": "请输入公司编码", "resultData": "结果数据", "incorrectCronExpression": "CRON 表达式不正确", "emailCannotBeEmpty": "邮箱不能为空", "theClientNumberCannotBeEmpty": "客户端编号不能为空", "addSystemModule": "添加系统模块", "allReadSuccessfully": "全部已读成功！", "menuName": "菜单名称", "mobileNumberOfSender": "发送者用户手机号", "deactivate": "停用", "pleaseSelectASuperior": "请选择上级", "enterpriseInformationRegistration": "企业信息登记", "theNameOfTheProcessor": "处理器的名字", "categoryPositioningConfiguration": "品类定位配置", "post": "岗位", "emailAccount": "邮箱账号", "female": "女", "theDepartmentIsNotInAnOpenStateAndCannotBeSelected": "部门不处于开启状态，不允许选择", "personInCharge": "负责人", "departmentName": "部门名称", "oldPassword": "旧密码", "roleNumber": "角色序号", "linkTracking": "链路追踪", "departmentNameCannotBeEmpty": "部门名称不能为空", "areYouSureToResendTheMessageLog": "是否确认重发消息日志?", "pleaseEnterTheTemplateContent": "请输入模板内容", "success": "成功", "exchangeRatexls": "汇率.xlsx", "pleaseSelectAChannelCode": "请选择渠道编码", "invalidRedirectUri": "无效 redirect_uri: {0}", "selectAllselectNone": "全选/全不选", "priceEncryption": "价格加密", "pleaseSelectAReminderType": "请选择提醒类型", "logType": "日志类型", "operationDate": "操作日期", "theCurrentPositionDoesNotExist": "当前岗位不存在", "userNo": "用户编号", "assignParent": "分配上级", "clientNo": "客户端编号", "categoryNameCannotBeEmpty": "品类名称不能为空", "purchasingOrganizationAlreadyExists": "采购组织编码已存在", "theEmailAccountDoesNotExist": "邮箱账号不存在", "roleType": "角色类型", "pleaseEnterTheDictionaryLabel": "请输入字典标签", "pleaseEnterTheSmsSendingCallbackUrl": "请输入短信发送回调 URL", "pleaseSelectTheSendingTime": "请选择发送时间", "onlineCumulativeDataResultDoesNotExist": "上线累计数据结果不存在", "parentChildLinkageselectParentNodeAutomaticallySelectChildNode": "父子联动(选中父节点，自动选择子节点)", "currencyXls": "币种.xlsx", "menuOrderCannotBeEmpty": "菜单顺序不能为空", "dataEncryption": "数据加密", "pleaseEnterTheRoutingAddress": "请输入路由地址", "pleaseSelectAModule": "请选择模块", "categoryDoesNotExist": "品类不存在", "roleSorting": "角色排序", "configurationHandover": "配置交接", "appName": "应用名", "pleaseEnterTheLoginAccount": "请输入登录账户", "socialAuthorizationFailedDueTo": "社交授权失败，原因是：{0}", "modifySensitiveWords": "修改敏感词", "sensitiveWords": "敏感词", "label": "标签", "clientIdMismatch": "client_id 不匹配", "userImportTemplatexls": "用户导入模板.xlsx", "companysLocalCurrency": "公司本币", "pleaseEnterTheResponsiblePerson": "请输入负责人", "modifyTheMultilingualTranslationTableOfSystemLabelstenantsCommonLabelTranslationData": "修改系统标签多语言翻译表（租户通用标签翻译数据）", "operationLogxls": "操作日志.xlsx", "labelCannotBeEmpty": "标签不能为空", "systemModule": "系统模块", "addDepartment": "添加部门", "dataPermissionRules": "数据权限规则", "pleaseEnterTheGrade": "请输入等级", "assignPurchaseOrganization": "分配采购组织", "pleaseEnterTheUserName": "请输入用户名称", "currencyDoesNotExist": "币种不存在", "testing": "检测", "smsTemplate": "短信模板", "pleaseEnterTheComponentPath": "请输入组件路径", "theTemplateContentCannotBeEmpty": "模版内容不能为空", "pleaseEnterTheTemplateNumberOfSmsApi": "请输入短信 API 的模板编号", "moduleCodeDuplication": "模块编码重复", "columnType": "列类型", "operationInformation": "操作信息", "factoryxls": "工厂.xlsx", "cannotSetOneselfAsTheParentMenu": "不能设置自己为父菜单", "exchangeRateDataDoesNotExist": "汇率数据不存在", "pleaseSelectATemplateType": "请选择模板类型", "pleaseEnterTheTemplateCode": "请输入模版编码", "languageEncodingCannotBeEmpty": "语言编码不能为空", "other": "其他", "socialAuthorizationFailedUnableToFindCorrespondingUser": "社交授权失败，找不到对应的用户", "theNumberOfColumnsDisplayedInOneRowCannotBeEmpty": "一行展示的列个数不能为空", "accurateQuery": "精准查询", "theDataSourceConfigurationDoesNotExist": "数据源配置不存在", "theFormTitleHasCustomColumnsAndCannotBeDeleted": "表单标题存在自定义列，不允许删除", "positionName": "岗位名称", "pleaseEnterTheDataLabel": "请输入数据标签", "areYouSureToDeleteTheNameAs": "是否确认删除名称为", "companyNameRule": "公司名称不能为空", "whetherToEnableSslCannotBeEmpty": "是否开启 SSL不能为空", "socialUnbindingFailedNotCurrentUserBound": "社交解绑失败，非当前用户绑定", "theFullNameOfTheClassWhereTheExceptionOccurred": "异常发生的类全名", "apiErrorLogDoesNotExist": "API 错误日志不存在", "aCategoryWithThisCodeAlreadyExists": "已经存在该编码的品类", "addCompanyLocalCurrency": "添加公司本币", "superiorDepartmentCannotBeBlank": "上级部门不能为空", "theTemplateNumberOfSmsApiCannotBeEmpty": "短信 API 的模板编号不能为空", "pleaseEnterCurrency": "请输入币种", "lastLoginTime": "最后登录时间", "yourCertificateIsInvalidPleaseCheckIfTheServerHasObtainedAuthorizationOrReapplyForACertificate": "您的证书无效，请核查服务器是否取得授权或重新申请证书！", "exchangeRateStartTime": "汇率开始时间", "sendStatus": "发送状态", "labelLanguageDescription": "标签语言描述", "encodingOfApiReceivingResults": "API 接收结果的编码", "addFactory": "添加工厂", "permissionId": "权限标识", "announcementTypeCannotBeEmpty": "公告类型不能为空", "cannotDeleteParameterConfigurationsWithBuiltinSystemTypes": "不能删除类型为系统内置的参数配置", "businessName": "业务名称", "categoryXls": "品类.xlsx", "positionSequenceCannotBeEmpty": "岗位顺序不能为空", "theTableAnnotationForTheDatabaseIsNotFilledIn": "数据库的表注释未填写", "systemModulexls": "系统模块.xlsx", "customerType": "用户类型", "bindingSucceeded": "绑定成功", "aDictionaryTypeWithThisNameAlreadyExists": "已经存在该名字的字典类型", "unableToDeleteThisEmailAccountStillHasAnEmailTemplate": "无法删除，该邮箱账号还有邮件模板", "announcementTitle": "公告标题", "tenantPackageDoesNotExist": "租户套餐不存在", "detectWhetherTheTextContainsSensitiveWords": "检测文本是否含有敏感词", "pleaseEnterTheUserAccount": "请输入用户账号", "companyCode": "公司编码", "aSmsTemplateWithTheCodeAlreadyExists": "已经存在编码为【{0}】的短信模板", "channelCodeCannotBeEmpty": "渠道编码不能为空", "testSendingEmails": "测试发送邮件", "changeRoleIsExternal": "修改为对内角色，将取消此角色下，供应商的相应菜单权限", "pleaseEnterTheDepartmentCode": "请输入部门编码", "emailAddressCannotBeEmpty": "邮箱地址不能为空", "companyId": "公司id", "aDepartmentWithThatNameAlreadyExists": "已经存在该名字的部门", "pleaseEnterTheSmtpServerPort": "请输入 SMTP 服务器端口", "theMessageTemplateCorrespondingToThisTemplateEncodingAlreadyExistsInThisLanguage": "该语言下此模板编码对应的消息模板已存在", "processorParameters": "处理器的参数", "errorCodeCannotBeEmpty": "错误码编码不能为空", "nameOfTheMethodWhereTheExceptionOccurred": "异常发生的方法名", "smsSignature": "短信签名", "smsLogDetails": "短信日志详细", "templateEncoding": "模版编码", "pleaseEnterTheAccountNumber": "请输入账号", "moduleDoesNotExist": "模块不存在", "unableToDeleteThisDictionaryTypeStillHasDictionaryData": "无法删除，该字典类型还有字典数据", "pleaseEnterTheRecipient": "请输入接收人", "smtpServerDomainName": "SMTP 服务器域名", "messageCcUser": "消息抄送用户", "superAdministratorDoesNotAllowConfigurationOfSuperiors": "超级管理员不允许配置上级", "invalidClientSecretR": "无效 client_secret: {0}R", "jumpAction": "跳转动作", "addDictionaryType": "添加字典类型", "interfaceName": "接口名称", "mobileNumberAlreadyExists": "手机号已经存在", "department": "归属部门", "isItVisible": "是否可见", "oauthClientDoesNotExist": "OAuth2 客户端不存在", "internationalizedDataDoesNotExist": "国际化数据 不存在", "builtin": "内置", "userTodoAndQuickEntryConfigurationsDoNotExist": "用户待办和快捷入口配置不存在", "dataTypeFirstDivideTheDataIntoTablesBasedOnModules": "数据类型：先按照模块进行数据分表依据", "monitoringTimeout": "监控超时时间", "emailAccountCannotBeEmpty": "邮箱账号不能为空", "smsLogxls": "短信日志.xlsx", "theEncodingCannotBeUsed": "编码【{0}】不能使用", "pleaseSelectAFrequencyType": "请选择频率类型", "theScheduledTaskIsAlreadyInThisStateAndDoesNotNeedToBeModified": "定时任务已经处于该状态，无需修改", "orderModule": "订单模块", "modifyErrorCode": "修改错误码", "logNo": "日志编号", "theTypeOfTheParentMenuMustBeADirectoryOrMenu": "父菜单的类型必须是目录或者菜单", "extend_value": "扩展值", "dataOrderCannotBeEmpty": "数据顺序不能为空", "theTenantIsCurrentlyUsingThisPackagePleaseResetThePackageForTheTenantBeforeAttemptingToDeleteIt": "租户正在使用该套餐，请给租户重新设置套餐后再尝试删除", "translationCannotBeEmpty": "翻译不能为空", "pleaseEnterTheSmtpServerDomainName": "请输入 SMTP 服务器域名", "markReadSuccessfully": "标记已读成功！", "templateLanguage": "模板语言", "modifyAnnouncement": "修改公告", "tableName": "表名称", "responsibleForTheCompany": "负责公司", "pleaseEnterTheFactoryCode": "请输入工厂编码", "pleaseEnterTheDepartmentName": "请输入部门名称", "areYouSureToExportAllPositionDataItems": "是否确认导出所有岗位数据项?", "externalJumpPath": "外部跳转路径", "smsTemplateDoesNotExist": "短信模板不存在", "theRecipientCannotBeEmpty": "接收人不能为空", "roleStatusNormalDisabled": "角色状态（0正常 1停用）", "functionalCurrencyCannotBeBlank": "是否为本币不能为空", "pleaseEnterThePositionCode": "请输入岗位编码", "whenDisablingTheRouteWillNotAppearInTheSidebarAndCannotBeAccessed": "选择停用时，路由将不会出现在侧边栏，也不能被访问", "pleaseEnterTheUserPassword": "请输入用户密码", "userImport": "用户导入", "cache": "缓存", "failedToObtainParameterConfigurationReasonNotAllowedToObtainInvisibleConfiguration": "获取参数配置失败，原因：不允许获取不可见配置", "dataRange": "数据范围", "addMenu": "添加菜单", "dictionaryType": "字典类型", "endTimeOfLastExecution": "最后一次执行的结束时间", "createAccount": "创建账户", "pleaseSelectAMessageType": "请选择短信类型", "theClientKeyCannotBeEmpty": "客户端密钥不能为空", "sendingResultCode": "发送结果编码", "theMessageTemplateDoesNotExist": "消息模板不存在", "companyNameEnter": "请输入公司名称", "expirationTime": "过期时间", "nextTriggerTime": "下一次触发时间", "grade": "等级", "departmentNumber": "部门编号", "abbreviationEnter": "请输入简称", "pleaseEnterTheExchangeRate": "请输入汇率", "socialPlatform": "社交平台", "errorCodeNumber": "错误码编号", "jurisdiction": "权限", "nameOfSender": "发送人名称", "theFormHasACustomTitleAndCannotBeDeleted": "表单存在自定义标题，不允许删除", "whenHideIsSelectedRoutesWillNotAppearInTheSidebarButCanStillBeAccessed": "选择隐藏时，路由将不会出现在侧边栏，但仍然可以访问", "modifyPurchaseOrganization": "修改采购组织", "pleaseEnterTheUserNumber": "请输入用户编号", "default": "默认", "individualSignature": "个人签章", "duplicateFormTitleEncoding": "表单标题编码重复", "abnormalStackTrajectory": "异常的栈轨迹", "tenantDoesNotExist": "租户不存在", "pleaseIdentifyThePermission": "请权限标识", "categoryAndCompanyRelationshipDoNotExist": "品类与公司关系不存在", "codeCannotBeUsed": "编码【{0}】不能使用", "systemDataResultDoesNotExist": "系统数据结果不存在", "currency": "币种", "testSendingSms": "测试发送短信", "theAnnouncementTitleCannotBeEmpty": "公告标题不能为空", "templateNo": "模板编号", "pleaseSelectTheReceivingStatus": "请选择接收状态", "todoType": "待办类型", "thePhoneNumberHasBeenUsed": "手机号已被使用", "areYouSureToExportAllOperationLogDataItems": "是否确认导出所有操作日志数据项?", "theSenderNameCannotBeEmpty": "发件人名称不能为空", "theUsernameCannotBeEmpty": "用户名不能为空", "pleaseEnterTheCodeOfTheLabel": "请输入标签的编码", "multilingualIdCannotBeEmpty": "多语言id不能为空", "pleaseEnterTheRoleName": "请输入角色名称", "currencyCode": "币种编码", "templateCodeCannotBeEmpty": "模版编码不能为空", "pleaseEnterTheCountryregionCode": "请输入国家、地区的编码", "factoryCode": "工厂编码", "areYouSureToDeleteTheCurrencyAs": "是否确认删除币种为", "pleaseFillInNumbersBetweenAnd": "请填写1-23之间的数字", "timeConsuming": "耗时", "smsChannelCode": "短信渠道编码", "operator": "操作人", "pleaseEnterSmsSignature": "请输入短信签名", "reminderOfSmsApiSendingFailure": "短信 API 发送失败的提示", "areYouSureToExportAllExchangeRateDataItems": "是否确认导出所有汇率数据项?", "userWithIdAlreadyExists": "编号{0}用户已经存在", "scheduledDays": "预定天数", "userTodoAndQuickEntryDoNotExist": "用户待办和快捷入口不存在", "pleaseEnterASensitiveWord": "请输入敏感词", "additionalInformation": "附加信息", "dictionaryTypexls": "字典类型.xlsx", "pleaseEnterTheSystemModule": "请输入系统模块", "tokenHasExpired": "Token 已经过期", "successfullyTransferredTask": "转派任务成功！", "editPosition": "修改岗位", "positionNameCannotBeEmpty": "岗位名称不能为空", "emailTitleParameters": "邮件标题参数", "dictionaryNameCannotBeEmpty": "字典名称不能为空", "pleaseSelectAColumnType": "请选择列类型", "helpMessageCenter": "帮助消息中心", "theImportedTableDoesNotExist": "导入的表不存在", "phoneNumber": "手机号码", "templateTitleCannotBeEmpty": "模板标题不能为空", "theFormCannotBeEmpty": "表单不能为空", "incorrectStatusInputForCategory": "品类的状态输入不正确", "operationResults": "操作结果", "name": "名字", "sendInternalMessages": "发送站内信", "tableDefinitionDoesNotExist": "表定义不存在", "responseReturnTime": "响应返回时间", "areYouSureToExportAllRoleDataItems": "是否确认导出所有角色数据项?", "pleaseEnterResources": "请输入资源", "socialInformation": "社交信息", "successfullyAllocatedCompany": "分配公司成功", "errorRequestingSuperset": "请求superset出错", "addMessageTemplate": "添加消息模板", "documentHandover": "单据交接", "basicInformation": "基本资料", "transferApprover": "转派审批人", "tenantName": "租户名", "passwordCannotBeEmpty": "密码不能为空", "pleaseEnterTheNumberOfColumnsDisplayedInOneRow": "请输入一行展示的列个数", "theAmountOfTheCurrencyToBeConvertedCannotBeBlank": "需要转换的币种的金额不能为空", "accountNotBoundRequiresBinding": "未绑定账号，需要进行绑定", "defaultCompany": "集团公司", "localCurrencyDataHasAlreadyBeenConfiguredForTheCurrentCompanysYearAndCannotBeAddedRepeatedly": "当前公司的年份下已经配置了本币数据，不能重复添加", "sendEmailAddress": "发送邮箱地址", "extendedValue": "扩展的值", "updateExistingUserData": "是否更新已经存在的用户数据", "fileIsEmpty": "文件为空", "userIdAlreadyExists": "用户编号已经存在", "localCurrencyAfterConversion": "转换后本币", "receivingEmailAddress": "接收邮箱地址", "detailed": "详细", "modifyEmailAccount": "修改邮箱账号", "roleIdCannotBeEmpty": "角色标识不能为空", "userSystem": "用户体系", "sendResults": "发送结果", "recipient": "接收人", "theValidityPeriodOfTheRefreshToken": "刷新令牌的有效期", "pleaseEnterTheLabelLanguageDescription": "请输入标签语言描述", "loginFailedAccountDisabled": "登录失败，账号被禁用", "systemModuleId": "系统模块ID", "moduleName": "模块名称", "pleaseEnterTheErrorCodePrompt": "请输入错误码提示", "theRoleOrderCannotBeEmpty": "角色顺序不能为空", "encodingForSendingResults": "发送结果的编码", "smsChannelNumber": "短信渠道编号", "required": "是否必填", "markAsRead": "标记已读", "theAccountOfSmsApiCannotBeEmpty": "短信 API 的账号不能为空", "userInformation": "用户信息", "modifySmsChannel": "修改短信渠道", "authorizationType": "授权类型", "pleaseEnterCategoryName": "请输入品类名称", "selectSuperiorDepartment": "选择上级部门", "theValidityPeriodOfTheAccessTokenCannotBeEmpty": "访问令牌的有效期不能为空", "importingUserDataCannotBeEmpty": "导入用户数据不能为空！", "formDoesNotExist": "表单不存在", "startTimeOfTheLastExecution": "最后一次执行的开始时间", "moduleIdCannotBeEmpty": "模块id不能为空", "modifyDictionaryType": "修改字典类型", "configureCategoriesAndPersonnelRelationships": "配置品类和人员关系", "userNickname": "用户昵称", "newPassword": "新密码", "accessToken": "访问令牌", "redirectableUriAddressCannotBeEmpty": "可重定向的 URI 地址不能为空", "configurationItem": "配置项", "selectExchangeRateDeadline": "选择汇率截止时间", "isSslEnabled": "是否开启 SSL", "approvalTask": "审批任务", "pleaseEnterTheCorrectMobilePhoneNumber": "请输入正确的手机号码", "theFormColumnMustSelectADictionaryDataSource": "表单列必须选择字典数据源", "aRoleWithTheCodeAlreadyExists": "已经存在编码为【{0}】的角色", "areYouSureToExportAllSmsTemplateDataItems": "是否确认导出所有短信模板数据项?", "logPrimaryKey": "日志主键", "exchangeRateCannotBeBlank": "汇率不能为空", "dictionaryTypeCannotBeEmpty": "字典类型不能为空", "loginResults": "登录结果", "incorrectBrandInputForCategory": "品类的品牌输入不正确", "smsChannelDoesNotExist": "短信渠道不存在", "dictionaryId": "字典标识", "startTime": "开始时间", "announcementType": "公告类型", "roleOrder": "角色顺序", "receivingTime": "接收时间", "pleaseEnterATemplateName": "请输入模板名称", "more": "更多", "pleaseEnterYourMobileNumber": "请输入手机号码", "pendingHandover": "待办交接", "modifySmsTemplate": "修改短信模板", "componentPath": "组件路径", "associatedCompanyCannotBeBlank": "关联公司不能为空", "frequencyType": "频率类型", "menuNameCannotBeEmpty": "菜单名称不能为空", "choice": "选择", "uniqueRequestIdReturnedBySmsApiSending": "短信 API 发送返回的唯一请求 ID", "refreshToken": "刷新令牌", "resetPassword": "重置密码", "areYouSureToExportAllDictionaryTypeDataItems": "是否确认导出所有字典类型数据项?", "operationStatus": "操作状态", "addUser": "添加用户", "pleaseEnterCurrencyCode": "请输入币种编码", "theFileConfigurationDoesNotExist": "文件配置不存在", "theSameEmailAccountAlreadyHasAnEmailTemplateWithThisCodeCreationFailed": "相同的邮箱账号已经有该编码【{0}】的邮件模板，创建失败", "helpCenterDoesNotExist": "帮助中心不存在", "errorCode": "错误码编码", "theRelationshipBetweenTheUserAndTheCompanyDoesNotExist": "用户与公司关系不存在", "theSystemWillCloseTheDocumentAndTheClosedDocumentWillBeArchivedAndSavedDoYouWantToContinue": "系统将关闭单据，关闭单据将归档保存。是否继续?", "pleaseEnterTheOperator": "请输入操作人员", "responseReturn": "响应返回", "modifyUser": "修改用户", "emailAccountNumber": "邮箱账号编号", "pleaseEnterYourUsernameItUsuallyMatchesYourEmailAddress": "请输入用户名，一般和邮箱一致", "pleaseEnterTheAuthorizationType": "请输入授权类型", "accessLogDetails": "访问日志详细", "aDepartmentWithThisCodeAlreadyExists": "已经存在该编码的部门", "pleaseEnterTestText": "请输入测试文本", "pleaseEnterTheAmountOfTheCurrencyToBeConverted": "请输入需要转换的币种的金额", "whetherToDisplay": "是否显示", "viewAll": "查看全部", "theAccessRouteAddressSuchAsUserIfAnInternetAddressIsRequiredItStartsWithhttps": "访问的路由地址，如：`user`。如需外网地址时，则以 `http(s)://` 开头", "viewSupplierInformation": "查看供应商信息", "theDataOperationRecordDoesNotExist": "数据操作记录不存在", "pleaseConfirmThePassword": "请确认密码", "senderUserNumber": "发送者用户编号", "transfer": "转办", "unbind": "(解绑)", "aRoleWithCodeAlreadyExists": "已经存在编码为【{0}】的角色", "messageAttachment": "消息附件", "theValidityPeriodOfTheAccessToken": "访问令牌的有效期", "sendResultPrompt": "发送结果提示", "parameterKeyName": "参数键名", "dictionaryStatus": "字典状态", "menuPermissions": "菜单权限", "pleaseEnterCssClass": "请输入 CSS Class", "formColumnDoesNotExist": "表单列不存在", "theQrCodeHasExpired": "二维码已过期", "pleaseEnterTheMessageTitle": "请输入消息标题", "hasItBeenRead": "是否已读", "accurateQueryFieldegOrderModuleOrderNumber": "精准查询字段1（如：订单模块-订单号）", "valueOfConfigurationItem": "配置项的值", "readingTime": "阅读时间", "theEmailTemplateHasBeenClosedAndTheSystemWillNotSendThisEmail": "邮件模板已关闭，系统不会发出该邮件", "templateLanguageCannotBeEmpty": "模板语言不能为空", "apiErrorLogProcessed": "API 错误日志已处理", "modifyCurrency": "修改币种", "rootMessagesCausedByExceptions": "异常导致的根消息", "pleaseSelectTheAssociatedDataDictionary": "请选择关联的数据字典", "recipientEmail": "收件邮箱", "pleaseEnterTheSendersName": "请输入发件人名称", "theNewPasswordCannotBeEmpty": "新密码不能为空", "modifyDictionaryData": "修改字典数据", "departmentTypeCannotBeEmpty": "部门类型不能为空", "userIdCannotBeBlank": "用户工号不能为空", "methodName": "方法名", "showStatus": "显示状态", "addCurrency": "添加币种", "theFileFormatIsIncorrectPleaseUploadAFileWithAPictureTypeSuchAsJpgOrPngSuffix": "文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。", "pleaseEnterTheClientNumber": "请输入客户端编号", "promptForSendingResults": "发送结果的提示", "countryxls": "国家地区.xlsx", "theRecipientEmailCannotBeEmpty": "收件邮箱不能为空", "dictionaryCoding": "字典编码", "dictionaryNo": "字典编号", "apiSmsNo": "API 短信编号", "pleaseSelectTheEmailReminderBusinessName": "请选择邮件提醒业务名称", "roleNo": "角色编号", "unbound": "未绑定", "pleaseEnterApprovalComments": "请输入审批意见", "pleaseEnterTheAnnouncementTitle": "请输入公告标题", "departmentCode": "部门编码", "departmentHead": "部门负责人", "currencyCodeCannotBeBlank": "币种编码不能为空", "assignResponsibleCompanies": "分配负责的公司", "homepageModule": "首页模块", "onlyOpenToSuppliers": "仅供应商开放", "templateTypeCannotBeEmpty": "模板类型不能为空", "areYouSureToExportAllSensitiveWordDataItems": "是否确认导出所有敏感词数据项?", "messageType": "消息类型", "clientKey": "客户端密钥", "addSensitiveWords": "添加敏感词", "templateContent": "模板内容", "pleaseEnterTheNumber": "请输入1-12的数字", "approvalRecord": "审批记录", "mobileNumberCannotBeEmpty": "手机号码不能为空", "positionSerialNumber": "岗位序号", "smsSendingCallbackUrl": "短信发送回调 URL", "announcement": "公告", "verificationCodeUsed": "验证码已使用", "modifyCustomFormColumnRules": "修改自定义表单列规则", "theUserAccountAlreadyExists": "用户账号已经存在", "departmentSelection": "部门选择", "messageTitle": "消息标题", "onlyTheSupplierAccountCanSeeIt": "只有供应商账户才能看到", "aRoleNamedAlreadyExists": "已经存在名为【{0}】的角色", "associativeDictionary": "关联字典", "failedToWriteLicenseFile": "写入license文件失败", "detailedInformationWithinTheStation": "站内信详细", "smsTemplatexls": "短信模板.xlsx", "pleaseEnterYourEmailAccount": "请输入邮箱账号", "theSmtpServerDomainNameCannotBeEmpty": "SMTP 服务器域名不能为空", "theAuthorizationScopeIsTooLarge": "授权范围过大", "userAccount": "用户账号", "theFactoryNameCannotBeEmpty": "工厂名称不能为空", "selectExchangeRateStartTime": "选择汇率开始时间", "roleName": "角色名称", "modifyMessageTemplate": "修改消息模板", "methodParameters": "方法参数", "icon": "图标", "templateCode": "模板编码", "sendingTime": "发送时间", "parentMenu": "上级菜单", "moduleCodeCannotBeEmpty": "模块编码不能为空", "cannotSetOnesOwnChildDepartmentAsTheParentDepartment": "不能设置自己的子部门为父部门", "accountStatus": "账号状态", "templateParameters": "模版参数", "userEmail": "用户邮箱", "deadline": "截止时间", "codeDoesNotExist": "code 不存在", "pleaseEnterTheCodeName": "请输入编码名称", "pleaseEnterTheAccountOfSmsApi": "请输入短信 API 的账号", "dontCache": "不缓存", "ToCharactersInLength": "长度在 6 到 20 个字符", "dataKeyValue": "数据键值", "roleStatus": "角色状态", "personnelHandover": "人员交接", "emailContent": "邮件内容", "modifyEmailTemplate": "修改邮件模版", "approvalCommentsCannotBeEmpty": "审批意见不能为空", "titleParameters": "标题参数", "retransmission": "重发", "taskNumber": "任务编号", "dataPermission": "数据权限", "certificateInstallationErrorReasonForError": "证书安装出错！出错原因：{0}", "language": "语言", "pleaseEnterTheFrequencyIntervalTheDefaultIs": "请输入频率间隔，默认为1", "parameterName": "参数名称", "resendSucceeded": "重发成功", "theRowOfTheMethodWhereTheExceptionOccurred": "异常发生的方法所在行", "warning": "警告", "modifyTheCompanysLocalCurrency": "修改公司本币", "yearCannotBeEmpty": "年份不能为空", "pleaseSelectALanguage": "请选择语言", "areYouSureToExportAllDockingRequestLogs": "是否确认导出所有对接请求记录日志?", "test": "测试", "templateNameCannotBeEmpty": "模板名称不能为空", "modifyHelpCenter": "修改帮助中心", "pleaseEnterTheModuleName": "请输入模块名称", "configurationItemCannotBeEmpty": "配置项不能为空", "superiorDepartment": "上级部门", "modifyForm": "修改表单", "forcedRetreat": "强退", "enterOneUserName": "请输入用户名", "addACustomFormColumnRule": "添加自定义表单列规则", "positionIsNotInAnOpenStateAndCannotBeSelected": "岗位({0}) 不处于开启状态，不允许选择", "selectTheParentCountryregion": "选择上级国家地区", "pleaseEnterTheDictionaryType": "请输入字典类型", "thereAreMultipleMessageTemplatesCorrespondingToThisTemplateEncodingInThisLanguage": "该语言下此模板编码对应的消息模板存在多条", "numberOfColumnsDisplayedInOneRow": "一行展示的列个数", "emailDoesNotExist": "邮箱不存在", "positionCodeCannotBeEmpty": "岗位编码不能为空", "explanationOfApiReceivingResults": "API 接收结果的说明", "theLabelLanguageDescriptionCannotBeEmpty": "标签语言描述不能为空", "requiredOrNotCannotBeBlank": "是否必填不能为空", "pleaseSelectALabel": "请选择标签", "encodingOfSmsApiSendingResults": "短信 API 发送结果的编码", "apiTemplateNumber": "API 的模板编号", "title": "标题", "content": "内容", "keyOfSmsApi": "短信 API 的密钥", "multiLanguageCannotBeEmpty": "多语言不能为空", "smsContent": "短信内容", "theCurrentDepartmentDoesNotExist": "当前部门不存在", "superiorLeaders": "上级领导", "errorCodePrompt": "错误码提示", "modifyEmailReminderConfiguration": "修改邮件提醒配置", "endExecutionTime": "结束执行时间", "roleId": "角色标识", "conversionAmount": "转换金额", "bound": "已绑定", "custom": "自定义", "reminderType": "提醒类型", "modifyFactory": "修改工厂", "userName": "用户名", "compressedAttachmentError": "压缩附件报错", "primaryKeyIdSelfGrowing": "主键id，自增长", "cronExpression": "CRON 表达式", "theTitleCannotBeEmpty": "标题不可以为空", "form": "表单", "pleaseSelectTheParentCategory": "请选择父级品类", "billingAddress": "开票地址", "pleaseEnterTheTemplateTitle": "请输入模板标题", "loginAddress": "登录地址", "loginAccount": "登录账户", "dataDictionary": "数据字典", "pleaseEnterTheReminderTypeDaysTheDefaultIs": "请输入提醒类型天数，默认为1", "companyName": "公司名称", "scopeOfAuthority": "权限范围", "modifyMenu": "修改菜单", "newApprover": "新审批人", "modifyOauthClient": "修改 OAuth2 客户端", "fieldMeaningDoesNotExist": "字段义不存在", "pleaseEnterTheErrorCode": "请输入错误码编码", "pleaseEnterBillingAddress": "请输入开票地址", "theScheduledTaskDoesNotExist": "定时任务不存在", "pleaseSelectAUser": "请选择用户", "incorrectVerificationCode": "验证码不正确", "errorCodeErrorPrompt": "错误码错误提示", "menuId": "菜单ID", "routingAddress": "路由地址", "id": "id", "text": "文本", "roleDataxls": "角色数据.xlsx", "unitSecond": "单位：秒", "loginFailedAccountLockedExpectedToBeUnlockedOn": "登录失败，账号被锁定，预计于{0}解锁", "pleaseEnterTheUserId": "请输入用户工号", "linkTrackingNumber": "链路追踪编号", "loginLogxls": "登录日志.xlsx", "purchaseOrganizationxls": "采购组织.xlsx", "sendingSmsTooFrequently": "短信发送过于频率", "classification": "分类", "sensitiveWordsCannotBeEmpty": "敏感词不能为空", "failedToObtainCertificateFile": "获取证书文件失败！", "exceedingTheDailyNumberOfTextMessagesSent": "超过每日短信发送数量", "categoryCodeCannotBeEmpty": "品类编码不能为空", "thereAreSubDepartmentsThatCannotBeDeleted": "存在子部门，无法删除", "areYouSureToExportAllCurrencyDataItems": "是否确认导出所有币种数据项?", "parentMenuDoesNotExist": "父菜单不存在", "errorCodexls": "错误码.xlsx", "menuType": "菜单类型", "categoryIsNotInAnOpenStateAndCannotBeSelected": "品类不处于开启状态，不允许选择", "pleaseEnterTheScopeOfAuthorization": "请输入授权范围", "authorizationTypeCannotBeEmpty": "授权类型不能为空", "userPassword": "用户密码", "userStatus": "用户状态", "updateDataCannotBeEmpty": "更新数据不可以空", "pleaseEnterPassword": "请输入密码", "builtinDataCannotBeDeleted": "内置数据无法删除", "externalJumpAction": "外部跳转动作", "startExecutionTime": "开始执行时间", "languageName": "语言名称", "confirmPasswordCannotBeEmpty": "确认密码不能为空", "pleaseSelectTheType": "请选择类型", "addMessageLog": "添加消息日志", "messageLogDetails": "消息日志详情", "bindingFactory": "绑定工厂", "submitAndSendSuccessfullySeeSendingLogNoForSendingResults": "提交发送成功！发送结果，见发送日志编号：", "newApproverCannotBeEmpty": "新审批人不能为空", "verificationCodeDoesNotExist": "验证码不存在", "theOldPasswordCannotBeEmpty": "旧密码不能为空", "templateTitle": "模版标题", "userPasswordVerificationFailed": "用户密码校验失败", "areYouSureToExportAllSystemModuleDataItems": "是否确认导出所有系统模块数据项?", "typeUsedToDistinguishBetweenTodoQuickEntryCumulativeDataAndSystemData": "类型;用于区分是待办，快捷入口，累计数据，系统数据", "dictionaryPrimaryKey": "字典主键", "theHomepageModuleDoesNotExist": "首页模块不存在", "assignDataPermission": "分配数据权限", "creator": "创建者", "theUserWithTheNameCannotBeConfiguredAsASuperiorToWhichWillCauseConflictsBetweenSuperiorsAndSubordinates": "名字为【{0}】的用户上级无法配置为{1},会造成上下级冲突", "pleaseSelectTheErrorCodeType": "请选择错误码类型", "userNameCannotBeEmpty": "用户名称不能为空", "theSerialNumberReturnedBySmsApiSending": "短信 API 发送返回的序号", "dataLabelCannotBeEmpty": "数据标签不能为空", "pleaseEnterTheOldPassword": "请输入旧密码", "requestTime": "请求时间", "pleaseSelectTheOpenState": "请选择开启状态", "theTenantPackageNamedHasBeenDisabled": "名字为【{0}】的租户套餐已被禁用", "theSmtpServerPortCannotBeEmpty": "SMTP 服务器端口不能为空", "tenantId": "租户编号", "columnName": "列名称", "parameterConfigurationNumber": "参数配置序号", "dictionarySort": "字典排序", "menuIcon": "菜单图标", "displayOrderCannotBeEmpty": "显示顺序不能为空", "main": "主要", "theCompanysLocalCurrencyDataDoesNotExist": "公司本币数据不存在", "unbindingSucceeded": "解绑成功", "pleaseEnterThePreciseQueryField": "请输入用于部门对接数据关键字段的精准查询字段1（如：订单模块-订单号）", "addAnnouncement": "添加公告", "pleaseEnterATitle": "请输入标题", "roleInternalAndExternalTagsCannotBeEmpty": "角色对内对外标记不能为空", "pleaseSelectTheTemplateType": "请选择模版类型", "exceptionHandlingerrorCode": "异常处理（错误码）", "factoryDoesNotExist": "工厂不存在", "selectTheParentMenu": "选择上级菜单", "failedToObtainPrivateKeyStore": "获取私钥库失败！", "redirectUriMismatch": "redirect_uri 不匹配", "smsApiTemplateNumber": "短信 API 模板编号", "pleaseEnterANumberFromTo": "请输入1-7的数字", "exchangeRateDataForCurrencyAndCurrencyDoesNotExist": "币种{0}和币种{1}的汇率数据不存在", "operationTime": "操作时间", "homeCurrency": "转换后币种", "revisedSeal": "修改章印", "moduleMenuConfigurationDoesNotExist": "模块菜单配置不存在", "pleaseEnterTheMenuName": "请输入菜单名称", "titleName": "标题名称", "mobilePhoneCannotBeEmpty": "手机不能为空", "pleaseEnterTheContactNumber": "请输入联系电话", "applicationDescription": "应用描述", "jumpPath": "跳转路径", "pleaseEnterTheFactoryName": "请输入工厂名称", "apiRequestNumber": "API 请求编号", "position": "所属岗位", "messageContent": "消息内容", "assignRoles": "分配角色", "code": "编码", "personalInformation": "个人信息", "routingAddressOfMatchingComponentsNeedToBeConsistent": "选择缓存时，则会被 `keep-alive` 缓存，需要匹配组件的 `name` 和路由地址保持一致", "departmentType": "部门类型", "userDataxls": "用户数据.xlsx", "fileDoesNotExist": "文件不存在", "sortCannotBeEmpty": "排序不能为空", "pleaseSelectTheArticleTag": "请选择文章标签", "pleaseEnterTheEmailTitle": "请输入邮件标题", "companyCannotBeEmpty": "公司不能为空", "successfullyCopied": "复制成功", "codeHasExpired": "code 已过期", "dataKeyValueCannotBeEmpty": "数据键值不能为空", "systemLog": "系统日志", "classFileWhereAnExceptionOccurred": "异常发生的类文件", "theUserWithTheNameHasBeenDisabled": "名字为【{0}】的用户已被禁用", "systemSensitiveWordsAlreadyExistInTheLabel": "系统敏感词已在标签中存在", "pleaseSelectAUserType": "请选择用户类型", "channelCode": "渠道编码", "positionDataxls": "岗位数据.xlsx", "affiliatedCompanies": "关联公司", "anErrorCodeWithTheCodeAlreadyExists": "已经存在编码为【{0}】的错误码", "pleaseEnterTheDisplayOrder": "请输入显示顺序", "emailTitle": "邮件标题", "lastTriggerTime": "上一次触发时间", "apiReceivingResults": "API 接收结果：", "operationName": "操作名", "theApplicationIconCannotBeEmpty": "应用图标不能为空", "pleaseEnterTheCode": "请输入编码", "showSort": "显示排序", "aPositionWithThisNameAlreadyExists": "已经存在该名字的岗位", "theUserAccountCannotBeEmpty": "用户账号不能为空", "modifyAvatar": "修改头像", "externalOrNot": "是否外部", "parentCategoryDoesNotExist": "父级品类不存在", "resultPrompt": "结果提示", "pleaseSelectTheDepartment": "请选择归属部门", "danger": "危险", "levelLevelhigherAndHigher": "级别高低（越大越高）", "abnormalOccurrenceTime": "异常发生时间", "dictionaryDataxls": "字典数据.xlsx", "sendingException": "发送异常", "loginIp": "登录 IP", "theCurrentDictionaryDataDoesNotExist": "当前字典数据不存在", "userPasswordCannotBeEmpty": "用户密码不能为空", "currencyCodeAlreadyExists": "币种编码已存在", "modular": "模块", "roleNameCannotBeEmpty": "角色名称不能为空", "templateNumberOfSmsApi": "短信 API 的模板编号", "unableToFindProcessInformation": "查询不到流程信息！", "theMessageLogDoesNotExist": "消息日志不存在", "module": "所属模块", "factoryName": "工厂名称", "superAdministratorProhibitsDeactivation": "超级管理员禁止停用", "cannotOperateOnRolesWithBuiltinSystemType": "不能操作类型为系统内置的角色", "theTestExampleDoesNotExist": "测试示例不存在", "modifyCategory": "修改品类", "pleaseSelectAForm": "请选择表单", "browserUa": "浏览器 UA", "loginFailedAccountPasswordIncorrect": "登录失败，账号密码不正确", "detectSensitiveWords": "检测敏感词", "taskName": "任务名称", "scopeOfAuthorization": "授权范围", "male": "男", "doesNotContainSensitiveWords": "不包含敏感词！", "scanCodeBinding": "扫码绑定", "addDictionaryData": "添加字典数据", "resultCode": "结果码", "messageTemplatexls": "消息模板.xlsx", "pleaseEnterTheModuleDescription": "请输入模块描述", "lastLoginIp": "最后登录IP", "moduleDescription": "模块描述", "pleaseSelectATemplateLanguage": "请选择模板语言", "thereAreEmployeesInTheDepartmentAndCannotBeDeleted": "部门中存在员工，无法删除", "theCompanysLocalCurrencyCannotBeEmpty": "公司本币不能为空", "flowChart": "流程图", "addEmailTemplate": "添加邮件模版", "theValidityPeriodOfTheRefreshTokenCannotBeEmpty": "刷新令牌的有效期不能为空", "requestInformation": "请求信息", "pleaseSelectTheEnablingStatus": "请选择启用状态", "ccEmailAddress": "抄送邮件地址", "automaticAuthorizationScope": "自动授权范围", "menuEncoding": "菜单编码", "factoryCodeAlreadyExists": "工厂编码已存在", "systemManagementModuleFileAssociationDoesNotExist": "系统管理模块文件关联不存在", "moduleCode": "模块编码", "userDoesNotExist": "用户不存在", "departmentStatus": "部门状态", "emailAlreadyExists": "邮箱已经存在", "addPosition": "添加岗位", "theCurrencyToBeConvertedCannotBeBlank": "需要转换的币种不能为空", "exchangeRateDeadlineCannotBeBlank": "汇率截止时间不能为空", "successfullyAssignedPurchaseOrganization": "分配采购组织成功", "modifyExchangeRate": "修改汇率", "pleaseEnterAdditionalInformationJsonFormatData": "请输入附加信息，JSON 格式数据", "moduleNameCannotBeEmpty": "模块名称不能为空", "contactPhone": "联系手机", "pleaseSelectAConfigurationItem": "请选择配置项", "permissionCharacter": "权限字符", "theAnnotationForTheTableFieldInTheDatabaseIsNotFilledIn": "数据库的表字段({0})注释未填写", "companyDoesNotExist": "公司不存在", "receivingEmail": "接收邮箱", "addEmailReminderConfiguration": "添加邮件提醒配置", "startTimeOfExchangeRateCannotBeBlank": "汇率开始时间不能为空", "addSystemLabelMultilingualTranslationTabletenantGeneralLabelTranslationData": "添加系统标签多语言翻译表（租户通用标签翻译数据）", "notSet": "未设置", "executionDuration": "执行时长", "redirectableUriAddress": "可重定向的 URI 地址", "displaySortingCannotBeEmpty": "显示排序不能为空", "roleAssignmentSucceeded": "分配角色成功", "aDictionaryTypeOfThisTypeAlreadyExists": "已经存在该类型的字典类型", "frontEndCannotStartWith": "前端不能以 / 开头", "columnTypeCannotBeEmpty": "列类型不能为空", "requestAddress": "请求地址", "positionNumberArray": "岗位编号数组", "details": "详情", "pleaseSelectTheSendingStatus": "请选择发送状态", "pleaseEnterTheRoleId": "请输入角色标识", "stateMismatch": "state 不匹配", "errorInUploadingFileType": "上传文件类型出错！", "cannotSetOneselfAsTheParentDepartment": "不能设置自己为父部门", "codeCannotBeEmpty": "编码不能为空", "attachmentParameters": "附件参数", "isTheScopeOfDisclosureOpenToThePublic": "公布范围是否对外", "userNicknameCannotBeEmpty": "用户昵称不能为空", "pleaseEnterTheApplicationName": "请输入应用名", "theApplicationNameCannotBeEmpty": "应用名不能为空", "frequencyInterval": "频率间隔", "dockingRequestLogXlsx": "对接请求记录日志.xlsx", "duplicateEncoding": "编码重复", "addOauthClient": "添加 OAuth2 客户端", "failedToObtainPublicKeyLibrary": "获取公钥库失败！", "accessNumber": "访问编号", "theProcessorForTheScheduledTaskAlreadyExists": "定时任务的处理器已经存在", "aMenuWithThatNameAlreadyExists": "已经存在该名字的菜单", "howManyExecutionsDidYouMake": "第几次执行", "parentCategory": "父级品类", "messageTemplateCode": "消息模板编码", "theImportedFieldDoesNotExist": "导入的字段不存在", "unableToDeleteThisSmsChannelStillHasASmsTemplate": "无法删除，该短信渠道还有短信模板", "nameTranslation": "名称翻译", "requestMethodName": "请求方法名", "systemSensitiveWordsDoNotExistInAllTags": "系统敏感词在所有标签中都不存在", "closeDocument": "关闭单据", "addAnInternalMessageTemplate": "添加站内信模板", "pleaseEnterTheReceivingEmailAddress": "请输入接收邮箱", "areYouSureToExportAllUserDataItems": "是否确认导出所有用户数据项?", "brandOnOff": "是否品牌(0:开启，1：关闭)", "pleaseEnterTheTemplateName": "请输入模版名称", "verificationCodeHasExpired": "验证码已过期", "theEnablingStatusCannotBeEmpty": "启用状态不能为空", "pleaseSelectADepartmentType": "请选择部门类型", "clickToUploadTheAvatar": "点击上传头像", "theSystemTenantCannotPerformModificationsDeletionsAndOtherOperations": "系统租户不能进行修改、删除等操作！", "verificationCodeNotUsed": "验证码未被使用", "anInternalMessageTemplateWithCodeAlreadyExists": "已经存在编码为【{0}】的站内信模板", "messagesCausedByExceptions": "异常导致的消息", "duplicateParameterConfigurationKey": "参数配置 key 重复", "normal": "正常", "tenantWithNameHasBeenDisabled": "名字为【{0}】的租户已被禁用", "areYouSureToExportAllMessageTemplateDataItems": "是否确认导出所有消息模板数据项?", "smsChannel": "短信渠道", "areYouSureToExportAllErrorCodeDataItems": "是否确认导出所有错误码数据项?", "pleaseSelectATitle": "请选择标题", "oauthClientNumberAlreadyExists": "OAuth2 客户端编号已存在", "testTextCannotBeEmpty": "测试文本不能为空", "theRangeCannotBeEmpty": "范围不能为空", "superior": "上级", "departmentCodeCannotBeEmpty": "部门编码不能为空", "addCompany": "添加公司", "thePositionWithThisIdentifierAlreadyExists": "已经存在该标识的岗位", "pleaseEnterANewPassword": "请输入新密码", "forcedWithdrawalSucceeded": "强退成功", "duplicateFormCode": "表单编码重复", "modifyTheOnsiteMessageTemplate": "修改站内信模板", "templateType": "模版类型", "pleaseEnterTheDataKeyValue": "请输入数据键值", "contentCannotBeEmpty": "内容不能为空", "duplicateFormColumnEncoding": "表单列编码重复", "messageLanguage": "消息语言", "theTenantNamedHasExpired": "名字为【{0}】的租户已过期", "binding": "(绑定)", "type": "类型", "synchronizationFailedNoChangesPresent": "同步失败，不存在改变", "addErrorCode": "添加错误码", "titleOrNot": "是否标题", "accurateQueryField": "精准查询字段2", "modifySystemModule": "修改系统模块", "exceptionName": "异常名", "emailReminderConfigurationDoesNotExist": "邮件提醒配置不存在", "systemLabelMultilingualTranslationTabletenantGeneralLabelTranslationDataXls": "系统标签多语言翻译表（租户通用标签翻译数据）.xlsx", "socialBindingFailedCannotRepeatedlyBindToTheSameUser": "社交绑定失败，不能重复绑定相同用户", "clickToUploadTheSeal": "点击上传章印", "theDataSourceConfigurationIsIncorrectAndCannotBeConnected": "数据源配置不正确，无法进行连接", "theCurrentNotificationAnnouncementDoesNotExist": "当前通知公告不存在", "agree": "同意", "pleaseSelectTheSmsChannelNumber": "请选择短信渠道编号", "theCurrentAddedValueDoesNotExistInTheDataDictionary": "当前【{0}】附加值在数据字典中不存在", "smsSignatureCannotBeEmpty": "短信签名不能为空", "processingTime": "处理时间", "colorType": "颜色类型", "fail": "失败", "theCurrentDictionaryTypeDoesNotExist": "当前字典类型不存在", "processingStatus": "处理状态", "theEmailTemplateDoesNotExist": "邮件模版不存在", "parameterConfigurationDoesNotExist": "参数配置不存在", "addRole": "添加角色", "userStatusIsDisabledAndCannotBeEnabled": "用户状态被禁用，无法启用", "pleaseEnterTheInterfaceName": "请输入接口名称", "insideAndOutsideTheRole": "角色内外标记", "gender": "性别", "exchangeRateDeadline": "汇率截止时间", "addSmsTemplate": "添加短信模板", "positionCode": "岗位编码", "theAuthorizationTypeIsNotSupported": "不支持该授权类型", "addCustomTableSubModule": "添加自定义表单子模块", "pleaseEnterTheDictionaryName": "请输入字典名称", "pleaseEnterTheRecipientEmailAddress": "请输入收件邮箱", "failedToObtainLicenseRootDirectory": "获取license根目录失败", "operators": "操作人员", "cacheOrNot": "是否缓存", "businessNameCannotBeEmpty": "业务名称不能为空", "parentCategoryCode": "父类品类编码", "theRoleWithTheNameHasBeenDisabled": "名字为【{0}】的角色已被禁用", "moduleId": "模块id", "pleaseFillInANumberBetweenAnd": "请填写1-7之间的数字", "theFileConfigurationDoesNotAllowDeletionReasonItIsTheMainConfigurationDeletionWillResultInTheInabilityToUploadTheFile": "该文件配置不允许删除，原因：它是主配置，删除会导致无法上传文件", "pleaseSelectSmsChannel": "请选择短信渠道", "parameterKeyValue": "参数键值", "systemCall": "系统调用", "responseTime": "响应时间", "labelCoding": "标签的编码", "requestParameters": "请求参数", "columnNameCannotBeEmpty": "列名称不能为空", "roleModuleFieldEncryptionPermissionDoesNotExist": "角色模块字段加密权限不存在", "toExportAllSystemLabelMultilingualTranslationTablestenantCommonLabelTranslationDataDataItems": "是否确认导出所有系统标签多语言翻译表（租户通用标签翻译数据）数据项?", "newForm": "新增表单", "modifyCountryregion": "修改国家地区", "areYouSureToExportAllDataItems": "是否确认导出所有数据项?", "allRead": "全部已读", "theSynchronizedFieldDoesNotExist": "同步的字段不存在", "nameCannotBeEmpty": "名称不能为空", "missingTemplateParameter": "模板参数({0})缺失", "emailSendingLogDetails": "邮件发送日志详细", "theSmsChannelIsNotOpenAndCannotBeSelected": "短信渠道不处于开启状态，不允许选择", "characterFlag": "角色标志", "emailParameters": "邮件参数", "areYouSureToDeleteTheEmailReminderConfigurationNumberAs": "是否确认删除邮件提醒配置编号为", "frontEndMustStartWith": "前端必须以 / 开头", "certificateFileGenerationFailed": "证书文件生成失败！", "cannotSetOneselfAsAParentCategory": "不能设置自己为父品类", "factorySelection": "工厂选择", "languageCannotBeEmpty": "语言不能为空", "sendAndReturnMessageNumber": "发送返回的消息编号", "operationModule": "操作模块", "theErrorCodeDoesNotExist": "错误码不存在", "addCountryregion": "添加国家地区", "apiSendResults": "API 发送结果", "pleaseEnterTheYear": "请输入年份", "thereIsASubmenuThatCannotBeDeleted": "存在子菜单，无法删除", "duplicateCountryregionCode": "国家地区编码重复", "onlyTasksInAnOpenStateCanBeModified": "只有开启状态的任务，才可以修改", "smtpServerPort": "SMTP 服务器端口", "areYouSureToExportAllFactoryDataItems": "是否确认导出所有工厂数据项?", "addHelpCenter": "添加帮助中心", "errorSavingTheGeneratedCertificateToTheDatabase": "生成的证书保存到数据库时出错！", "jobId": "岗位ID", "theTagCodeCannotBeEmpty": "标签的编码不能为空", "receivingStatus": "接收状态", "pleaseEnterTheTemplateNumber": "请输入模板编号", "pleaseEnterThePositionName": "请输入岗位名称", "addPurchaseOrganization": "添加采购组织", "theFunctionalCurrencyDoesNotExist": "转换后币种必填", "roleDoesNotExist": "角色不存在", "routingAddressCannotBeEmpty": "路由地址不能为空", "loginDate": "登录日期", "clickTheSelectIcon": "点击选择图标", "cannotSetOnesOwnSubcategoryAsTheParentCategory": "不能设置自己的子品类为父品类", "messageLogxls": "消息日志.xlsx", "smsApiAccount": "短信 API 的账号", "areYouSureToDeleteTheDictionaryWhoseNumberIs": "是否确认删除字典编号为", "modifyOrderGeneralConfiguration": "修改订单通用配置", "smsType": "短信类型", "parameterClassification": "参数分类", "assignDataPermissionRules": "分配数据权限规则", "areYouSureToExportAllPurchaseOrganizationDataItems": "是否确认导出所有采购组织数据项?", "oauthClientDisabled": "OAuth2 客户端已禁用", "theOnsiteMessageTemplateDoesNotExist": "站内信模版不存在", "pleaseEnterTheVersion": "请输入版本", "tableDefinitionAlreadyExists": "表定义已经存在", "isItEncrypted": "是否加密", "openState": "开启状态", "dictionaryTypeIsNotInAnOpenStateAndCannotBeSelected": "字典类型不处于开启状态，不允许选择", "resources": "资源", "pleaseEnterTheClientKey": "请输入客户端密钥", "phoneNumberDoesNotExist": "手机号不存在", "startRequestTime": "开始请求时间", "postNo": "岗位编号", "operationLog": "操作日志", "positionStatus": "岗位状态", "editCompany": "修改公司", "onlyAllowedToBeModifiedToOnOrOffStatus": "只允许修改为开启或者关闭状态", "userGender": "用户性别", "errorCodeType": "错误码类型"}, "scar": {"cancel": "取 消", "reasonForDelay": "延期原因", "scarFormTypeDoesNotExist": "SCAR表单类型不存在", "rootCauseAnalysisResponsiblePersonFieldMustBeFilledIn": "根本原因分析责任人字段需要必填交", "thisDefectTypeIsAlreadyUsedInScarDocumentsAndCannotBeDeletedIfNoLongerInUsePleaseChooseToModifyTheStatusToClosed": "已经有SCAR单据使用了这个不良类型，不允许删除。如不再使用，请选择修改状态为关闭", "theCurrentDocumentDoesNotSupportExtension": "当前单据不支持延期", "cannotSetOneselfAsAParentDefectType": "不能设置自己为父级不良类型", "relatedDocuments": "相关文档", "theTransferorCannotBeTheCurrentUser": "转办人不能为当前用户", "pleaseSelectTheParentDefectType": "请选择父级不良类型", "onlyDocumentsThatHaveBeenReleasedToTheSupplierAndHaveNotBeenApprovedCanBePostponed": "只有已经发布给供应商并且未完成批准的单据才可以延期", "selectDocument": "选择单据", "correctiveMeasuresAndImplementation": "纠正措施及执行", "causeAnalysis": "原因分析", "defectSourceAndDocumentAssociation": "不良来源和单据关联", "completionTimeOfCorrectiveMeasures": "纠正措施完成时间", "thePurchasingOrganizationHasNotConfiguredADefectType": "采购组织未配置不良类型", "qualityPersonnelEmail": "质量人员邮箱", "reviewSupplierResponse": "审查供应商回复", "theReviewDateIsBlankPleaseSubmitAfterCompletion": "审核日期为空，请完成后提交", "pleaseEnterAnElectronicSignature": "请输入电子签名", "theTransferPersonIsEmptyAndTransferIsNotAllowed": "转办人为空，不允许转办", "theTransferorCannotBeEmpty": "转办人不能为空", "batchQuantityOfIncomingMaterials": "来料批数量", "statusIsNotEmpty": "状态不为空", "viewAttachmentsRelatedToImprovementMeasures": "查看改善措施相关附件", "mrbNumber": "MRB编号", "factoryAndPurchasingOrganizationDidNotMaintainBindingRelationship": "工厂和采购组织未维护绑定关系", "documentStatusErrorOnlyNewOrPendingDocumentDeletionIsAllowed": "单据状态错误，只允许新建或待发布状态单据删除", "scarFormTypesSpAndSaDoNotSupportImport": "SCAR表单类型SP、SA不支持导入", "sqeJobNumber": "SQE工号", "permanentCorrectiveMeasures": "永久纠正措施", "pleaseEnterTheDefectTypeCode": "请输入不良类型编码", "releaseDatePlus": "发布日期加", "uploadAttachmentsRelatedToImprovementMeasures": "上传改善措施相关附件", "usageQuantity": "使用数量", "reviewer": "审核人员", "manufacturersNumber": "制造商编号", "pleaseSubmitContainmentMeasuresFirst": "请先提交围堵措施", "theProblemDescriptionFieldOfTheSaDocumentNeedsToBeFilledIn": "sa单据的问题描述字段需要必填", "viewAttachments": "查看附件", "permanentMeasureRequestResponseDate": "永久措施要求回复日期", "parentDefectType": "父级不良类型", "withoutEmailTheSupplierNotBeAbleToConfirmPleaseConfirmWhetherToSubmitOrNot": "没有质量人员邮箱 ，供应商将无法确认回复改善单，请确认是否提交。", "numberOfSamples": "样本数", "theFieldOfContainmentMeasuresMaterialDisposalMeasuresMustBeFilledIn": "围堵措施-物料处置措施字段需要必填", "thePersonResponsibleForPreventingRecurrenceFieldMustBeFilledInAndSubmitted": "预防再发生责任人字段需要必填交", "submitToSqe": "提交到SQE", "reviewSupplierResponseAndFillInAtLeastOneItem": "审查供应商回复至少填写一条", "publishToSuppliers": "发布给供应商", "theRequiredItemMaterialDescriptionHasNotBeenFilledInPleaseSubmitItAfterCompletion": "必填项物料描述没有填写，请完成后提交", "responseDateRequiredForContainmentMeasures": "围堵措施要求回复日期", "completionTime": "完成时间", "pleaseSelectAPurchasingOrganizationFirst": "请先选择采购组织", "modificationInvitationRequired": "修改邀请必填", "revokingDocuments": "撤销单据", "personLiable": "责任人", "duplicateDocumentTypeAndDefectSourceAreNotAllowed": "单据类型和不良来源不允许重复", "defectTypeCode": "不良类型编码", "addAMandatoryConfigurationForInvitingReviewers": "添加邀请评审人必填配置", "newDocument": "新建单据", "theRequiredSupplyResponseDateHasNotBeenFilledInPleaseSubmitAfterCompletion": "必填项要求供应回复日期没有填写，请完成后提交", "pleaseSelectATimeInterval": "请选择时间区间", "viewProblemDescriptionAttachment": "查看问题描述附件", "pleaseSelectASupplierFirst": "请先选择供应商", "scarBasicInformationsupplierOnlineDefectcustomerComplaintFormSc": "SCAR基本信息>供应商在线不良&客诉单-SC", "areYouSureYouWantToCancelTheCurrentDocument": "你确认要撤销当前单据吗？", "improvementMeasures": "改善措施", "confinementMeasuresResponsiblePersonFieldMustBeFilledInAndSubmitted": "围堵措施-负责人字段需要必填交", "whetherToStopTheLine": "是否停线", "confinementMeasuresCompletionTimeFieldMustBeFilledInAndSubmitted": "围堵措施-完成时间字段需要必填交", "theDocumentStatusIsIncorrectPleaseRefreshAndTryAgain": "单据状态错误，请刷新重试", "nameOfDefectType": "不良类型名称", "notPublish": "不发布", "permanentCorrectiveMeasuresAndImplementation": "永久纠正措施及执行", "rootCauseAnalysis": "根本原因分析", "suppliersAreNotAllowedToViewDataInReviewAndCountersigningStatus": "供应商不允许查看评审、会签状态下的数据", "inspectionBatchNumber": "检验批编号", "theRequiredScarCodeHasNotBeenFilledInPleaseSubmitItAfterCompletion": "必填项SCAR代码没有填写，请完成后提交", "theSampleQuantityIsGreaterThanTheIncomingMaterialQuantityPleaseConfirmAgain": "样本数量大于来料数量，请再确认", "qualityDocumentList": "质量单据列表", "passwordError": "密码错误", "scarCodeMaterialCodeSupplierName": "SCAR编码、物料编码、供应商名称", "documentProcessor": "单据处理人", "areThereAnyDuplicateDefects": "是否重复不良", "defectTypeCodeCannotBeEmpty": "不良类型编码不能为空", "materialInformation": "物料信息", "severity": "严重程度", "releaseDate": "发布日期", "approval": "批准", "badSource": "不良来源", "improvementResponsiblePerson": "改善责任人", "theFormNameIsNotEmpty": "表单名称不为空", "theDocumentHandlerOfTheScarFormCannotBeEmpty": "SCAR表单的单据处理人不能为空", "areYouSureToDeleteTheScarDefectTypeNumberAs": "是否确认删除SCAR不良类型:", "theSampleQuantityIsLessThanPleaseConfirmAgain": "样本数量小于0，请再确认", "scarCode": "scar代码", "totalQuantity": "总数量", "onlyDocumentsInSupplierReplyStatusAreAllowedForThisOperation": "只有供应商回复状态下的单据允许此操作", "theRequiredItemDefectRateHasNotBeenFilledInPleaseSubmitItAfterCompletion": "必填项不良率没有填写，请完成后提交", "currentHandler": "当前办理人", "deleteDocument": "删除单据", "seeFile": "查看文件", "defectiveRate": "不良率", "preventingRecurrence": "预防再发生", "theContainmentMeasuresHaveBeenSubmittedAndCannotBeReturned": "围堵措施已经提交，无法退回", "reviewNonConformities": "审核不符合项", "supplierConfirmation": "*供应商确认", "scarBasicInformationIncomingInspectionImprovementFormIqc": "SCAR基本信息-来料检验改善单-IQC", "theProblemDescriptionImprovementMeasuresFieldOfTheSaDocumentMustBeFilledIn": "sa单据的问题描述-改善措施字段需要必填", "theScarDocumentOwnerDoesNotExist": "scar单据主不存在", "theRequiredDefectTypeHasNotBeenFilledInPleaseSubmitItAfterCompletion": "必填项不良类型没有填写，请完成后提交", "containmentMeasuresTotalQuantityFieldMustBeFilledInAndSubmitted": "围堵措施-总数量字段需要必填交", "confirmationAndVerificationOfCorrectiveMeasures": "纠正措施的确认与验证", "theNumberOfDefectsIsGreaterThanTheNumberOfSamplesPleaseConfirmAgain": "不良数量大于样本数，请再确认", "theNumberOfDefectsIsLessThanPleaseConfirmAgain": "不良数量小于0，请再确认", "timelydelayed": "及时/延迟", "theSourceOfTheRequiredQuestionHasNotBeenFilledInPleaseSubmitItAfterCompletion": "必填项问题来源没有填写，请完成后提交", "currentusernotconfigleader": "当前用户未配置上级领导，请联系管理员", "documentExtension": "单据延期", "formTaskRecipientXlsx": "表单任务接收人.xlsx", "pleaseFillInTheReason": "请填写原因", "rootCauseAnalysisRichText": "根本原因分析富文本）", "factoryInformation": "工厂信息", "formTypes": "表单类型", "typesOfDefects": "不良类型", "pleaseKeepAtLeastOneLineOfProblemDescription": "请最少保留一行问题描述", "formName": "表单名称", "afterTheMeasuresAreImported": "措施导入后，来料合格批批号", "pleaseEnterKeywordsForFuzzySearch": "请输入关键字模糊查找", "auditDate": "审核日期", "containmentAction": "围堵措施", "theSuppliersReplyToTheScarDocumentDoesNotExist": "scar单据供应商回复不存在", "oneNaturalDayIsEqualToTheDateWhenTheSupplierIsRequiredToRespondToLongtermCountermeasures": "个自然日等于要求供应商回复长期对策日期", "permanentCountermeasureRequestReplyDate": "永久对策要求回复日期", "pleaseEnterTheNameOfTheDefectType": "请输入不良类型名称", "pleaseSelectADocument": "请选择一条单据", "pleaseSelectAFactoryFirst": "请先选择工厂", "pleaseEnterOrSelectData": "请输入或者选择数据", "latestRequestForSupplierResponseDate": "最新要求供应商回复日期", "improvementRequirements": "改善要求", "theFieldsOfCorrectiveMeasuresAndResponsiblePersonForImplementationMustBeFilledInAndSubmitted": "纠正措施及执行责任人字段需要必填交", "theRequiredPermanentCountermeasureRequestResponseDateHasNotBeenFilledInPleaseSubmitAfterCompletion": "必填项永久对策要求回复日期没有填写，请完成后提交", "theRequiredQuestionDescriptionHasNotBeenFilledInPleaseSubmitItAfterCompletingIt": "必填项问题描述没有填写，请完成后提交", "numberOfDefects": "不良数量", "theScarReportTemplateIsNotConfiguredPleaseContactTheAdministrator": "SCAR报告模板没配置，请联系管理员", "permanentCorrectiveMeasuresAndImplementationRichText": "永久纠正措施及执行富文本", "thePurchasingOrganizationHasNotConfiguredAFactoryPleaseConfigureTheFactoryFirst": "该采购组织未配置工厂，请先配置工厂", "pleaseSelectASupplierAndPurchasingOrganizationFirst": "请先选择供应商和采购组织", "reasonForExtensionIsRequired": "延期原因必填", "modifyScarDefectType": "修改Scar不良类型", "checkcontent": "checkContent", "theNumberOfIncomingBatchesIsLessThanPleaseConfirmAgain": "来料批数量小于0，请再确认", "requestSupplierResponseDate": "要求供应商回复日期", "sourceOfTheProblem": "问题来源", "confirmationOfCompletionStatus": "完成情况确认", "theTransferorAlreadyBelongsToTheCurrentUsersHandlerAndCannotBeTransferred": "转办人已经属于当前用户的办理人，不允许转办", "theCurrentDocumentHasNotBeenSavedAndDoesNotSupportOperation": "当前单据未保存，不支持操作", "scarBasicInformationAuditNonconformanceImprovementFormSa": "SCAR基本信息-审核不符合项改善单-SA", "scarStatus": "scar状态", "theQuantityOfDefectsIsGreaterThanTheQuantityOfIncomingMaterialsPleaseConfirmAgain": "不良数量大于来料数量，请再确认", "theCurrentStatusDoesNotSupportReturning": "当前状态不支持退回", "confinementMeasuresInStockDefectRateFieldMustBeFilledInAndSubmitted": "围堵措施-在库不良率字段需要必填交", "replyDateConfiguration": "回复日期配置", "newScar": "新建单据", "supplierResponse": "供应商回复", "qualityComplaintType": "质量投诉类型", "approvalDate": "批准日期", "sqe": "SQE", "evidenceOfOnsiteAuditMeasuresImplementationAndVerificationResults": "现场审核措施实施的证据和验证结果的证据", "richTextForProblemDescriptionOfScarDocuments": "scar单据的问题描述富文本", "materialLocation": "物料所在位置", "theRequiredConfigurationForInvitingReviewersDoesNotExist": "邀请评审人必填配置不存在", "theQuantityOfDefectsMustBeLessThanOrEqualToTheQuantityUsed": "不良数量必须小于等于使用数量", "selectTheTypeOfDocumentToPublish": "选择发布的单据类型", "scarDefectTypexlsx": "Scar不良类型.xlsx", "preservation": "保 存", "severityLevel": "严重等级", "areYouSureYouWantToDeleteTheCurrentDocument": "你确认要删除当前单据吗？", "reviewDate": "评审日期", "uploadProblemDescriptionAttachment": "上传问题描述附件", "configureFormsAndPersonnelRelationships": "配置表单与人员关系", "viewDocuments": "当前办理人", "sqeConfiguration": "SQE配置", "inspectionMethod": "检验方式", "scarBasicInformationsupplierPerformanceImprovementFormSp": "SCAR基本信息>供应商绩效改善单-SP", "areYouSureToCancelTheDocumentAndNotPublishItToTheSupplier": "你确定将单据撤销不发布给供应商吗？", "theRequiredResponseDateForContainmentMeasuresHasNotBeenFilledInPleaseSubmitAfterCompletion": "必填项围堵措施要求回复日期没有填写，请完成后提交", "theSelectedPersonnelDoNotHaveOperationalPermissionsForThisPurchasingOrganization": "选择的人员没有这个采购组织的操作权限", "pleaseSaveTheCurrentFillingContentFirst": "请先保存当前填写内容", "theRequiredQualityComplaintTypeHasNotBeenFilledInPleaseSubmitItAfterCompletion": "必填项质量投诉类型没有填写，请完成后提交", "theCurrentStateDoesNotSupportObtainingTheNextState": "当前状态不支持获取下一状态", "oneNaturalDayIsEqualToTheDateWhenTheSupplierIsRequiredToRespondToTheContainmentMeasures": "个自然日等于要求供应商回复围堵措施日期", "problemDescription": "问题描述", "pleaseSelectATimeType": "请选择时间类型", "areYouSureToDeleteTheDocumentAndNotPublishItToTheSupplier": "你确定将单据删除不发布给供应商吗？", "inviteReviewers": "邀请评审人", "completionDate": "完成日期", "noContainmentMeasuresHaveBeenSubmittedYet": "还未提交围堵措施", "addScarDefectType": "添加Scar不良类型", "pleaseSelectTheInvitedReviewer": "请选择邀请评审人", "materialDisposalMeasures": "物料处置措施", "suppliersFirstResponseDate": "供应商首次回复日期"}, "common": {"cancel": " 取消", "newInquiry": "新建询价", "batchExport": "批量导出", "forgetPassword": "忘记密码", "upload": "上传", "endDate": "结束日期", "del": "删除", "pleaseEnterLanguageInzh": "仅在中文环境下展示", "materialEditing": "物料编辑", "myMessage": "我的站内信", "sourcing": "寻源采购", "pleaseFillInTheCompleteData": "请填写完整数据", "password": "密码", "enable": "启用", "loginName": "登录名称", "loginPassword": "登录密码", "thePasswordsEnteredTwiceAreInconsistent": "两次输入的密码不一致", "dataUpdateTime": "数据更新时间", "welcome": "欢迎您", "todayIs": "今天是", "pleaseSelectADate": "请选择日期", "directLogin": "直接登录", "edit": "编辑", "layoutSettings": "布局设置", "newAccount": "新建账户", "savingToLocalPleaseWait": "正在保存到本地，请稍候...", "singleRoundBargaining": "单轮议价", "pleaseEnter": "请输入", "buyer": "执行采购", "loginCategory": "品类", "folder": "文件夹", "operate": "操作", "userAccount": "用户账户", "multipleRoundsOfBargaining": "多轮议价", "passiveGrant": "被动授予", "reset": "重置", "enableStatus": "启用状态", "startDate": "开始日期", "emailVerificationCode": "邮箱验证码", "status": "状态", "createdSuccessfully": "创建成功", "putAwayAll": "收起所有", "welcomeToRegister": "欢迎注册成为{companyName}的供应商", "pleaseEnterpleaseEnter": "Displayed only in Chinese environment", "successfullyCreatedQuantity": "创建成功数量", "swipeRightToUnlock": "向右滑动解锁", "save": "保存", "clearingSettingsCacheAndRefreshingPleaseWait": "正在清除设置缓存并刷新，请稍候...", "tenThousandYuan": "万元", "accumulatedGrantPrice": " 累计授予价格 ", "numberOfValidationFailures": "验证失败数量", "enabled": "是否启用", "helpCenter": "帮助中心", "theDataCannotExceednum": "数据不能超过{num}条", "dynamicTitle": "动态标题", "addSuccess": "新增成功", "sendVerificationCode": "发送验证码", "search": "搜索", "uploadPurchaserAttachments": "上传采购方附件", "4uploadDescriptionTitle": "4.如传错文件可点击x删除", "themeColors": "主题颜色", "confirmPassword": "确认密码", "areYouSureToDeleteThisDataItem": "是否确认删除该数据项?", "supplierRegistrationPage": "供应商登记页", "selectedTotal": "已选择{selectedTotal}项", "email": "账户邮箱", "logOutAndLogIn": "退出登录", "downloadTemplate": "下载模板", "uploadFile": "上传文件", "unfoldcollapse": "展开/收起", "welcomeMessage": "感谢您的时间。您将要开始填写供应商详细信息，这些信息将帮助我们更好地了解您的公司，也帮助我们在需要开发新供应商时更快找到您的公司！收集的信息包括贵司的基本信息，人员信息，商务信息，产品与制程信息，认证与资质信息。如果您还没有准备好所有信息，请先填写已知的信息，系统可保存填写的内容。您可以后续补完填写后再提交。", "notNeedInput": "暂无填写要求", "categoryCode": "品类编码", "editSuccess": "编辑成功", "saveSettings": "保存设置", "2uploadDescriptionTitle": "2.请填写下载的EXCEL文件，注意不得修改表头，格式，或增加筛选条件，只允许填入内容", "confirm": "确认", "saveConfiguration": "保存配置", "createTime": "创建时间", "disable": "禁用", "savedSuccessfully": "保存成功", "menuSearch": "菜单搜索", "looseValidation": "松开验证", "loadingPleaseWait": "加载中，请稍后", "register": "申请", "enabledStatusrequired": "启用状态必填", "pleaseEnterLanguageInen": "仅在英文环境下展示", "updateSuccessful": "更新成功", "createAStatementOfAccount": "创建对账单", "resetConfiguration": "重置配置", "emptyAll": "全部清空", "advancedSearch": "高级搜索", "systemDataaccumulatedForTheCurrentYear": "系统数据（当年累计）", "statisticians": "统计", "accumulatedSystemLaunch": "系统上线累计", "notificationAnnouncement": "通知公告", "toDoItems": "待办事项", "areYouSureToLogOutAndExitTheSystem": "确定注销并退出系统吗？", "reacquireAfterSeconds": "{countDown}秒后重新获取", "orderForm": "订单", "batchCreation": "批量创建", "fixedHeader": "固定 Header", "hideSearch": "隐藏搜索", "saveFailed": "保存失败", "modifiedSuccessfully": "修改成功", "export": "导出", "clickOrDragTheFileHereToUpload": "点击或将文件拖拽到这里上传", "clickUpload": "点击上传", "brandOrNot": "是否品牌", "add": "新增", "createBiddingProject": "创建竞标项目", "dragTheFileHereOr": "Drag and drop files here, or", "readAndAgreeTheSCPT": "我已阅读并同意服务公约与隐私条款", "expandCollapse": "展开/折叠", "displayLogo": "显示 Logo", "uploadSucceeded": "上传成功", "successfullySent": "发送成功", "verificationFailed": "验证失败", "themeStyleSettings": "主题风格设置", "sort": "排序", "logIn": "登录", "creationDate": "创建日期", "operationRecord": "操作记录", "expandAll": "展开所有", "numberOfCreationFailures": "创建失败数量", "systemPrompt": "系统提示", "systemLayoutConfiguration": "系统布局配置", "loginMessage2": "Purchasing Life Made Easy", "loginMessage1": "丰致乐采 采购人生也从容", "fileSize": "文件大小", "accumulatedQualityImprovementOrder": "累计质量改善单", "name": "名称", "welcomeMessage3": "。如果您还没有准备好所有信息，请先填写已知的信息，系统可保存填写的内容。您可以后续补完填写后再提交。", "publisher": "发布人", "welcomeMessage1": "感谢您的时间。您将要开始填写供应商详细信息，这些信息将帮助我们更好地了解您的公司，也帮助我们在需要开发新供应商时更快找到您的公司！收集的信息包括贵司的", "deleteFailed": "删除失败", "synchronizeErp": "同步ERP", "welcomeMessage2": "基本信息，人员信息，商务信息，产品与制程信息，认证与资质信息", "verificationSucceeded": "验证成功", "shareInvitationLink": "分享邀请链接", "3uploadDescriptionTitle": "3.点击或拖拽上传文件，等待下方进度条读取完毕，点击确定", "cellphoneNumber": "手机号", "submit": "提交", "copyAddress": "复制链接", "enableTopnav": "开启 TopNav", "onlyXlsXlsxFormatFilesAreAllowedToBeImported": "仅允许导入xls、xlsx格式文件。", "reload": "刷新", "supplierQuotation": "供应商报价", "showSearch": "显示搜索", "sir": "先生", "authentication": "认证", "1uploadDescriptionTitle": "1.请点击下载模板", "verificationFailedDataDownload": "验证失败数据下载", "inspectionLedgerDetails": "验货台账详情", "uploadDescription": "上传说明", "enableTagViews": "开启 Tags-Views", "ov_purchasechart_cumulativeamount": "累计采购额", "delSuccess": "删除成功", "putItAway": "收起", "createBiddingProjects": "创建招投标项目", "personalCenter": "个人中心", "userName": "用户名称", "supplierWords3": "成为客户的协作伙伴，共同合作，共同发展，丰致SRM-PRO平台帮助您与客户一起，合力实现更大的目标和成就", "notificationOfAward": "中标通知", "supplierWords2": "欢迎使用丰致SRM-PRO平台！让我们一起构建可持续发展的供应链网络", "changePassword": "修改密码", "supplierWords1": "与客户同心协力，帮助客户创造供应链的竞争优势", "home": "首页", "purchaseWords6": "所有大数据都来自于你每次一丝不苟的输入和执行，你就是数据工程师", "modify": "修改", "pleaseEnterCategoryCode": "请输入品类编码", "purchaseWords4": "正确的需求规范是采购找到正确供应商的依据", "uploadFileSizeCannotExceedm": "上传文件大小不能超过{fileSize}M", "dateOfChange": "变更日期", "purchaseWords5": "没有完美供应商，只有不断进步和协作的合作伙伴", "maam": "女士", "purchaseWords2": "采购就是做生意，供应商就是我们最好的合作伙伴", "application": "确定", "uploadTemplate": "上传模板", "purchaseWords3": "采购节约的每一份钱，都是为公司创造利润", "procurementAmount": "采购额", "purchaseWords1": "选对供应商，是管好供应商的第一步", "activelyGrant": "积极授予", "pleaseSelect": "请选择", "strictSelectionOfSuppliers": "严选供应商", "existingAccount": "已有账号？", "pleaseSignIn": "请登录", "myQuickAccess": "我的快捷入口", "remarks": "备注", "pleaseEnterAName": "请输入名称", "qualityImprovementForm": "质量改善单"}, "material": {"packageSpecification": "包装规格", "validityPeriodOfProductionLicenseProductionFilingCertificate": "生产许可证/生产备案凭证有效期", "extendedField4": "扩展字段4", "extendedField3": "扩展字段3", "extendedField2": "扩展字段2", "extendedField1": "扩展字段1", "widthmm": "宽度(mm)", "customsDeclarationInformation": "报关信息", "manufacturingInformation": "制造信息", "lengthnmm": "长\n(mm)", "degreeOfProtection": "防护等级", "materialDoesNotExist": "物料不存在", "factoryRequiredFormatFactoryAfactoryB": "工厂必填，格式：工厂A/工厂B", "model": "型号", "pleaseSelectCategory": "请选择品类", "storageTemperature": "存储温度", "brand": "品牌", "storingInformation": "存储信息", "rohsMaterialProperties": "RoHS物料属性", "pleaseEnterTheHeight": "请输入高度", "priceUnit": "价格单位", "pleaseEnterTheLength": "请输入长度", "pleaseEnterTheOldMaterialNumber": "请输入旧物料编号", "pleaseEnterTheMaterialDescription": "请输入物料描述", "generalInformation": "通用信息", "textureOfMaterial": "材质", "supplementaryDescriptionOfMaterials": "物料补充说明", "standardCost": "标准成本", "createANewItem": "创建新物料", "drawingVersion": "图纸版本", "dateOfIssuanceOfProductionLicenseProductionRecordCertificate": "生产许可证/生产备案凭证发证日期", "importedMaterialDataCannotBeEmpty": "导入物料数据不能为空", "pleaseSelectAFactory": "请选择工厂", "nonGeneralInformation": "非通用信息", "classificationCodeOfMedicalDevices": "医疗器械分类代码", "weightUnit": "重量单位", "manufacturersPartNumber": "制造商料号", "tradingMode": "交易模式", "customerCode": "客户编码", "plannedDeliveryTime": "计划交货时间", "manufacturer": "制造商", "productPurity": "产品纯度", "orderPriceTerms": "订单价格条款", "processingTechnology": "加工工艺", "standardCostCurrency": "标准成本币种", "endCustomerItemNo": "终端客户料号", "materialShop": "材质牌号", "plannedMinimumLotSizemoq": "计划最小批量大小(MOQ)", "theMaterialStatusHasNotBeenDisabled": "物料状态没有被禁用", "widthnmm": "宽\n(mm)", "shelfLife": "货架寿命", "heightmm": "高度(mm)", "plannedMinimumPackagingQuantitympq": "计划最小包装量(MPQ)", "errorMessage": "错误信息", "divisionAttributes": "事业部属性", "materialCode": "物料编码", "basicUnit": "基本单位", "lengthmm": "长度(mm)", "storageConditions": "存储条件", "materialDescription": "物料描述", "component": "成分", "hsCode": "HS Code", "isItConfidential": "是否保密", "categoryformatRequirementsFirstLevelCategorysecondLevelCategorythirdLevelCategory": "品类(格式要求:一级品类/二级品类/三级品类)", "taxClassificationCode": "税收分类编码", "purchasingUnit": "采购单位", "lifeCycle": "生命周期", "selfMadePurchased": "自制/外购", "effectiveDate": "生效日期", "commodityName": "商品名称", "selfMadeOrPurchased": "自制或者采购", "pleaseEnterTheManufacturersRemarks": "请输入制造商备注", "materialDescriptionIsRequired": "物料描述必填", "materialView": "物料查看", "qualityStandard": "质量标准", "pleaseEnterTheManufacturer": "请输入制造商", "dataItemOf": "的数据项?", "lifeUnit": "寿命单位", "productionLicenseNoProductionFilingCertificateNo": "生产许可证号/生产备案凭证编号", "isItAServiceType": "是否服务类", "detailedEditing": "详细编辑", "dataSources": "数据来源", "abcIdentification": "ABC标识", "specificationAndModel": "规格型号", "pleaseEnterTheManufacturersPartNumber": "请输入制造商料号", "packagingCategory": "包装类别", "managementInformation": "管理信息", "materialDataExportFilexls": "物料数据导出文件.xlsx", "revision": "版次", "volume": "体积", "keyMaterials": "关键物料", "pleaseEnterTheMaterialCodeMaterialDescriptionSpecificationMfgMpn": "请输入物料编码、物料描述、规格型号、MFG、MPN", "netWeight": "净重", "countryOfOriginCodeAndCountryOfOrigin": "原产国代码与原产国", "oldMaterialNo": "旧物料编号", "chemicalCsaNumber": "化学品CSA号", "position": "所属岗位", "materialStatus": "物料状态", "deactivationDate": "停用日期", "heightnmm": "高\n(mm)", "pleaseEnterTheDrawingVersion": "请输入图纸版本", "purpose": "用途", "manufacturersRemarks": "制造商备注", "volumeUnit": "体积单位", "areYouSureToDeleteTheItemNo": "是否确认删除物料编码为", "purchaseType": "采购类型", "productCategory": "产品类别", "materialRemarks": "物料备注", "explosiveNameCode": "易制爆名称代码", "materialGrade": "物料等级", "dangerousGoodsOrNot": "是否危险品", "factory": "工厂", "pleaseEnterTheVersion": "请输入版次", "pleaseEnterTheSupplementaryDescriptionOfTheMaterial": "请输入物料补充说明", "materialType": "物料类型", "pleaseEnterTheWidth": "请输入宽度", "lengthXWidthXHeightmm": "长X宽x高(mm)", "approvalNumber": "审批编号", "grossWeight": "毛重", "colour": "颜色", "generality": "通用性", "uidProductCode": "UID商品码", "processCode": "工艺代码", "pleaseEnterAColor": "请输入颜色", "storageHumidity": "存储湿度", "procurementGroup": "采购组", "category": "品类", "materialNonGeneralInformationDoesNotExist": "物料非通用信息不存在"}, "supplier": {"reason": "原因", "pleaseEnterAFolderName": "请输入文件夹名称", "theCurrentDataIsInUseInTheResourceLibraryAndCannotBeDeleted": "当前数据在资源库被使用，无法删除", "year": "年份", "termOfValidity": "有效期", "selfMadeOutsourced": "自制/外包", "onlySupportsUploadingNoMoreThanSupplierData": "仅支持上传不超过100条供应商数据", "mainContactNameAndContactDivisionAreRequired": "主要联系人姓名和联系人分工必填", "noPermissionToViewTheSupplierOrTheSupplierDoesNotExist": "无权限查看该供应商或该供应商不存在", "bankInformationWithOfficialSeal": "加盖公章的银行信息", "importMainDeviceDataCannotBeEmpty": "导入主要设备数据不能为空", "fileExistsDeletionNotAllowed": "存在文件，不允许删除", "shareholderInformation": "股东信息", "productionCycle": "生产周期", "pleaseEnterTheBrandName": "请输入品牌名称", "brandNameCannotBeEmpty": "品牌名称不能为空", "invalidEmailVerificationCode": "邮箱验证码错误或者已经失效", "supplierLevel": "供应商级别", "completed": "已完成", "customerName": "客户名", "importAndExportQualification": "进出口资质", "editControlledInformation": "编辑受控信息", "productionArea": "生产区域面积", "enterSupplierName": "输入供应商名称", "turnoverinRecentYears": "营业额（近3年）", "supplierExportFileXls": "供应商导出文件.xlsx", "statusCode": "状态编码", "pleaseSelectDictionaryLabel": "请选择字典标签", "invoiceVerificationBasedOnReceipt": "基于收货的发票校验", "supplierResourceLibrary": "供应商资源库", "purchaseDate": "购买日期", "newLine": "新增行", "pleaseFillInTheUserName": "请填写用户名称", "industry": "行业", "supplierCode": "供应商编码", "monthlyProduction": "月产量", "supplierInformationDoesNotExist": "供应商信息不存在", "areaCovered": "占地面积", "theCompanyCodeCannotBeEmptyOrDuplicate": "公司编码不能为空且不能重复", "legalRepresentative": "法人代表", "shareLinksToSuppliers": "分享链接给供应商", "registeredCapital": "注册资本", "companyInformation": "公司信息", "factorystreet": "街道(工厂)", "email": "电子邮箱", "squareMeter": "平方米", "templateDownload": "模板下载", "theSupplierDocumentLibraryDoesNotExist": "供应商文档库不存在", "sendingInvitationModificationLinkFailed": "邀请修改链接发送失败", "mainCustomers": "主要客户", "dictionaryLabel": "字典标签", "documentName": "名称", "factoryAddress": "工厂地址", "thePurchasingOrganizationDoesNotExist": "采购组织不存在", "configureSupplierAndPersonnelRelationship": "配置供应商与人员关系", "qualificationAndCertification": "资质与认证", "theCurrentDataDoesNotExist": "当前数据不存在", "pleaseEnterEmailAddress": "请输入邮件地址", "ytdNewlyIntroduced": "YTD新引入", "pleaseEnterStatusCode": "请输入状态编码", "mainCategory": "主类目", "validUntil": "有效期至", "supplyProductCategorycategoryMultilayerFormatAbc": "供应产品类别(品类多层格式:A/B/C)", "branchBranchName": "支行/分行名称", "duplicateSupplierBrandName": "供应商品牌名称重复", "companyWebsite": "公司网站", "mainContactPosition": "主要联系人职位", "dictionaryNotes": "字典备注", "contacts": "联系人", "staffInfo": "员工信息", "country": "国家", "unifiedSocialCreditCode": "统一社会信用代码", "deviceInformationxls": "设备信息.xlsx", "changeSupplierLevel": "变更状态", "dictionaryKeyValue": "字典键值", "erpLoginInformationPurchaseOrganizationView": "ERP登录信息-采购组织视图", "invoiceAddress": "发票地址", "relationshipWithTheMainCompany": "与主公司关系", "shareProportion": "股份比例(%)", "reconciliationAccount": "统驭科目", "emailAddress": "邮箱地址", "unsupportedFileFormat": "不支持的文件格式", "pleaseEnterTheCorrectEmailAddress": "请输入正确的邮箱地址", "buyerConfiguration": "执行采购配置", "invoiceType": "发票类型", "equipmentName": "设备名称", "supplierUniquenessVerificationFailedDuplicateUnifiedCreditCodeAndCompanyName": "供应商唯一性校验失败，统一信用代码+公司名称重复", "ownershipOfIntellectualProperty": "知识产权归属", "registrationApplicationLibrary": "登记申请库", "bankAccount": "银行账号", "productsAndProcesses": "产品和制程", "primaryContactPhoneNumber": "主要联系人电话", "enterNumber": "输入数字", "pleaseEnterTheSupplierNameSearch": "请输入供应商名、供应商代码、简称、供应产品名、电子料品牌", "essentialInformation": "基本信息", "establishedTime": "成立时间", "editErpLoginInformationPurchaseOrganizationView": "编辑ERP登录信息-采购组织视图", "telephone": "电话", "cleanRoomGrade": "洁净室等级", "statusCannotBeEmpty": "状态不能为空", "listOfMainEquipmenttopTen": "主要设备清单（前十）", "youWillShareTheLinkToFillSupplierInf": "您将要分享链接给其他人，使得对方来填写供应商信息。", "verificationOfSupplierUniquenessFailedTheUnifiedCreditDuplicateCodeIsDuplicate": "供应商唯一性校验失败，统一信用代码重复", "clickToExportAllTheDataDisplayedInTheList": "点击可导出列表中已展示的所有数据", "paymentMethod": "付款方式", "invitationModificationLinkSentSuccessfully": "邀请修改链接发送成功", "folderName": "文件夹名称", "batchUpload": "批量上传", "levelStatus": "供应商状态", "nameOfSuppliedProduct": "供应产品名称", "saveAndSendNotificationEmail": "保存并发送通知邮件", "cleanRoom": "洁净室", "pleaseEnterTheSupplierName": "请输入供应商名称", "lastOperator": "最后操作人", "theDocumentLibraryFolderIdCorrespondingToTheUploadFileEntryTypeDoesNotExist": "上传文件入口类型对应的文档库文件夹id不存在", "authorizationRole": "授权角色", "displayOrder": "显示顺序", "FactoryAddressCountryRegionAndStreetProvinceCityRequired": "工厂地址国家地区和街道,省/市必填", "theBindingRelationshipBetweenTheDocumentLibraryAndTheUploadEntryDoesNotExist": "文档库和上传入口的绑定关系不存在", "editAccount": "编辑账户", "pleaseCompleteTheInformation": "请填写完整信息", "noPermissionToOperateTheSuppliersDocumentLibraryFolder": "无权限操作该供应商文档库文件夹", "street": "街道", "areYouSureToDeleteTheSupplierGradeTheStatusNumberIs": "是否确认删除供应商等级状态编号为", "statusCodeCannotBeEmpty": "状态编码不能为空", "mainContactInformation": "主要联系方式", "shortNameOfSupplier": "供应商简称", "purchasingOrganizationCannotBeEmptyAndCannotBeDuplicate": "采购组织不能为空且不能重复", "openToSupplierrequired": "开放给供应商必填", "modifySupplierDocumentLibrary": "修改供应商文档库", "serviceChargePayer": "手续费承担方", "addVendorDocumentLibrary": "添加新文件夹", "procurementConfiguration": "寻源采购配置", "statusNormalDisabled": "状态（0正常 1停用）", "companyType": "公司类别", "bankBranch": "支行名称", "custom": "自定义1", "fullName": "姓名", "editorialQualificationAndCertification": "编辑资质与认证", "addedCategory": "已加品类", "managementSystem": "管理系统", "supplierPersonnelRelationshipxls": "供应商与人员关系.xlsx", "pleaseSelectStatus": "请选择状态", "totalNumberOfSuppliers": "供应商总数", "statusNameCannotBeEmpty": "状态名称不能为空", "money": "金额", "areYouSureToExportAllSupplierUserRel": "是否确认导出所有供应商与人员关系数据项?", "areYouSureToDeleteTheSupplierBrandName": "是否确认删除供应商品牌名称为", "simulatedAccount": "模拟账户", "failedToUploadTheFilePleaseCheckTheFileFormat": "上传文件失败，请检查文件格式", "emailLinkSendingFailed": "邮箱链接发送失败", "ditto": "同上", "facgtoryprovincesAndCities": "省市(工厂)", "countryOfOrigin": "原产地", "demandFulfillmentPurchase": "寻源采购", "pleaseEnterEmail": "请输入邮箱", "modifySupplierBrand": "修改供应商品牌", "mainSuppliersAndMaterials": "主要供应商及材料", "productInformation": "产品信息", "brandNameInitials": "品牌名称首字母", "supplierHasItemsInResourceInventory": "供应商在资源库存在{0}条", "nameDepartment": "名字/部门", "tips": "提示", "HaveSuccessfullySubmitted": "您已成功提交，此页面已失效。您可返回系统其它页面继续操作，或关闭页面离开。", "addSupplierBrand": "添加供应商品牌", "fileComments": "文件备注", "verificationConditions": "校验条件", "supplier": "供货商", "areYouSureToExportTheSupplierDataItem": "是否确认导出供应商数据项?", "model": "型号", "erpLoginInformationCompanyView": "ERP登录信息-公司视图", "sourcingProcurementJobNumber": "寻源采购工号", "proportionOfSales": "销售额占比", "documentLibraryFolderParentIdCannotBeEmpty": "文档库文件夹父级id不能为空", "generalFax": "总机传真", "switchboard": "总机电话", "countryregion": "国家地区", "emailInformationCannotBeEmpty": "邮箱信息不能为空", "natureOfTheCompany": "公司性质", "supplierEchelonLevel": "供应商梯队层级", "pleaseSelectAnAccount": "请选择账户", "numberOfEmployees": "员工人数", "mailbox": "邮箱", "propertyRightOfPlant": "厂房产权", "uploadSupplierWarning": "注意，此处仅支持上传不超过100条供应商数据", "brandId": "品牌id", "pleaseEnterADocumentLibraryComment": "请输入文档库备注", "importSelectedSupplier": "是否引入选择的供应商", "registeredAddressOfTheCompany": "公司注册地址", "multipleOptionsAvailable": "可多选", "pleaseSelectAParentFolder": "请选择父级文件夹", "other": "其他", "totalNumberOfSupplierResources": "供应商资源总数", "proportion": "比例", "salesArea": "销售区域", "purchasingOrganization": "采购组织", "youAreAboutToShareTitle": "您将要分享链接给其他人，邀请对方来填写供应商信息。 受邀人在收到邮件后，可通过邮件中的链接进行详细的信息登记。", "pleaseEnterTheSupplierNameAbbreviationCode": "请输入供供应商名称/简称/编码", "thereMustBeAtLeastOnePrimaryContactTheDivisionOfLaborIsThePrimaryContact": "主要联系人必须存在至少一个联系人分工是主要联系人", "addSupplierLevelStatus": "添加供应商状态", "sendInvitationEmail": "发送邀请邮件", "theFirstLetterCannotBeEmpty": "品牌名称首字母，用于排序.英文使用首字母，中文目前默认号不能为空", "parentFolder": "父级文件夹", "rDPersonnelQualifications": "研发人员资历", "statusName": "状态名称", "endCustomer": "终端客户", "provinceCity": "省/直辖市", "processCapability": "制程能力", "editPurchaseOrganizationView": "编辑采购组织视图", "factorycountry": "国家/地区(工厂)", "permissionToIntroduce": "准许引入", "introductionTime": "引入时间", "updateTime": "更新时间", "openToSuppliers": "是否开放给供应商", "autoSelectedMainContact": "自动勾选上主要联系", "emailLinkSentSuccessfully": "邮箱链接发送成功", "personnelInformation": "人员信息", "permanentlyValid": "是否永久有效", "businessInformation": "商业信息", "submittedSuccessfully": "提交成功", "pleaseEnterTheStatusName": "请输入状态名称", "theAccountHasBeenCreatedDoYouWantToSendAnEmailToTheSupplierAdministrator": "账户已创建，是否发送邮箱通知供应商管理员", "Prompt": "提示：如果您是供应商，此邮箱仅用于当前账户密码找回。业务邮件接收邮箱请进入“供应商信息登记”进行维护！", "pleaseSelectAnAuthorizationRole": "请选择授权角色", "primaryContactName": "主要联系人姓名", "zipCode": "邮编", "pleaseEnterSupplierNameSupplierCodeLoginNameAndUserName": "请输入供应商名称、供应商代码、登录名称、用户名称", "invitationRegistry": "邀请登记库", "selectedBrands": "已选择的品牌", "successfullyImported": "引入成功", "documentLibrary": "文档库", "erpLoginInformation": "ERP登录信息", "provincesAndCities": "省市", "relatedCompanyInformation": "关联公司信息", "brandOfRawMaterials": "原材料品牌", "informationCompletionRate": "信息完成率", "enterpriseManagementSystem": "企业管理系统", "contactDivision": "联系人分工", "accountingPeriod": "账期", "termOfPayment": "付款条件", "qualifiedSupplier": "合格供应商", "certificationCompletionTime": "认证完成时间", "laboratoryEquipment": "实验室设备", "brandName": "品牌名称", "level": "状态", "creditInformation": "征信信息", "proportionInRawMaterialProcurement": "占原材料采购比例", "theSupplierHasAlreadySubmittedPleaseDoNotRepeatTheOperation": "该供应商已提交，请勿重复操作", "supplierStatusBeforeChangeNotSupported": "不支持变更之前的供应商状态", "sendNotificationEmail": "发送通知邮件", "modifyTheBindingRelationshipBetweenTheDocumentLibraryAndTheUploadPortal": "修改文档库和上传入口的绑定关系", "taxRate": "税率", "thePrimaryContactInformationCannotBeEmpty": "主要联系人信息不能为空", "supplierBrandDoesNotExist": "供应商品牌不存在", "pleaseEnterFullSupplierName": "请填写公司全称", "enableDisable": "启用/禁用", "areYouSureToDeleteTheSelectedSupplier": "是否确认删除选择的供应商", "incorrectMailboxFormat": "邮箱格式不正确", "supplierBrandxls": "供应商品牌.xlsx", "editErpLoginInformationCompanyView": "编辑ERP登录信息-公司视图", "batchImport": "批量导入", "position": "职位", "supplierDocumentLibrary": "供应商文档库", "pleaseSelectDemandFulfillmentPurchase": "请选择寻源采购", "establishedAccount": "已建账户", "inviteNewSuppliers": "邀请新供应商", "searchCriteria": "搜索条件", "invitationModification": "邀请修改", "accountName": "开户名称", "modificationRecord": "修改记录", "pleaseSelectACountry": "请选择国家", "modifySupplierLevelStatus": "修改供应商状态", "bankOfDeposit": "开户行", "deleteSelected": "删除选中", "changingTheStatusOfThisSupplierIsNotSupported": "不支持变更此供应商状态", "purchasePriceOfEquipment": "设备采买价", "areYouSureToExportAllSupplierBrandDataItems": "是否确认导出所有供应商品牌数据项?", "editCompanyView": "编辑公司视图", "enterEdit": "进入编辑", "productAndProcessProcessCapability": "产品和制程-制程能力", "contactNumber": "联系电话", "bankNo": "银行号", "doNotAllowSelectingOneselfAsAParentFolder": "不允许选择自己为父级文件夹", "thereAreSubfoldersAndDeletionIsNotAllowed": "存在子文件夹，不允许删除", "areYouSureToExportAllSupplierLevelStatusDataItems": "是否确认导出所有供应商等级状态数据项?", "paymentInformation": "付款信息", "primaryContactEmail": "主要联系人电子邮箱", "supplierName": "供应商名称", "deliveryTerms": "交货条件", "quantity": "数量", "whenSelectingAForeignCurrencySwifCodeIsARequiredField": "选择外币时，SWIFT Code为必填项", "supplyProductCategory": "供应产品类别", "introductionFailedSupplierDataHasNotBeenSubmittedYet": "引入失败，供应商数据还未提交", "supplierLevelStatusxls": "供应商等级状态.xlsx", "pleaseEnterAContact": "请输入联系人", "ytdNewAccess": "YTD新准入", "registeredCapitalCurrency": "注册资本币种", "materialsAndSpecifications": "材料与规格", "documentLibraryNotes": "文档库备注", "mailingAddress": "邮件地址", "pleaseSelectData": "请选择数据", "primaryContact": "主要联系人", "pleaseEnterQuantityAndUnit": "请填写数量+单位", "rawMaterialSupplier": "原材料供应商", "salesInformation": "销售信息", "qualificationCertificateOfSpecialTypeOfWork": "特种工种资格证", "invitationEmailAddress": "邀请邮件地址", "importResults": "导入结果", "user": "用户", "independentRegistry": "自主登记库"}, "sl": {"continuousInThePast": "过去连续", "gradedApplicationFormsThatHaveNotBeenSubmittedCanOnlyBeInvalidated": "未提交状态得分级申请单才能作废", "levelConditions": "等级条件", "pleaseSelectARecommendationLevel": "请选择推荐级别", "ratingApplicationNumber": "评级申请单号", "areYouSureToExportAllHierarchicalApplicationFormMasterDataItems": "是否确认导出所有分级申请单主数据项?", "frequencySettingErrorPleaseLimitToTwoDigits": "频率设置错误，请限制在两位数以内！", "ratingApplicationFormNameIsRequired": "评级申请单名称必填", "inconsistentWithRecommendationLevel": "与推荐级别不一致", "levelAdjustmentList": "级别调整清单", "supplierNameSupplierCodeSupplierAbbreviation": "供应商名称、供应商编码、供应商简称", "pleaseEnterTheNameOfTheRatingApplicationForm": "请输入评级申请单名称", "nameAndNumberOfRatingApplicationForm": "评级申请单名称、评级申请单号", "levelChangeConditionConfigurationModificationRecord": "级别变更条件配置修改记录", "pleaseSelectAPerformanceLevel": "请选择绩效级别", "approvalPassedSuccessfully": "审批通过成功！", "referenceInformation": "参考信息", "theGradingApplicationFormDoesNotExist": "分级申请单不存在", "pleaseSelectAtLeastOneDataItem": "请选择至少一项数据", "each": "每", "createANewRatingApplicationForm": "新建评级申请单", "reasonForAdjustment": "调整原因", "performanceLevelOccurrenceFrequencyInputErrorPleaseBeLessThanTheNumberOfConsecutiveMonthsInThePast": "绩效等级出现次数输入错误，请小于过去连续月数！", "pleaseSelectALevel": "请选择级别", "supplierRatingDoesNotExist": "供应商分级不存在", "currentSupplierPerformanceLevel": "当前供应商绩效等级:", "monthsPerformanceLevelAppears": "个月，绩效等级出现", "configureAutomaticRecommendationLevel": "配置自动推荐级别", "todoPerson": "待办人", "ratingApplicationFormName": "评级申请单名称", "explanationWhenSelectingMultiplePerformanceLevelsFollowTheOrLogicToExecuteAllOtherSituationsOnlyOneMustBeChecked": "说明：绩效等级多选时按或逻辑执行  其他所有情况必须且仅能勾选一个", "pleaseSelectWhetherThereHaveBeenTransactionsInThePastYear": "请选择最近一年是否存在交易", "oneYearTransaction": "一年交易", "thereIsDataInTheLevelAdjustmentListThatHasBeenAppliedForByAnotherApplicationForm": "级别调整清单中存在被别的申请单申请中的数据!", "supplierLevelDescription": "供应商级别说明", "currentLevelEffectiveTime": "当前级别生效时间", "pleaseCheckAtLeastOneItemForAllOtherSituations": "请至少勾选一项为其他所有情况！", "conditionCategoryPositioning": "条件：品类定位", "supplierGradingXlsx": "供应商分级.xlsx", "automaticallyRecommendTo": "自动推荐至", "performanceLevelAppears": "绩效等级出现", "pleaseSelectWhetherThereHaveBeenTransactionsInThePastSixMonths": "请选择最近半年是否存在交易", "addLevelAdjustmentList": "添加级别调整清单", "adjustedLevel": "调整后级别", "theSupplierAdjustmentListForTheHierarchicalApplicationFormDoesNotExist": "分级申请单的供应商调整清单不存在", "temporaryprobationaryValidityPeriod": "临时/试用有效期", "levelName": "级别名称", "pleaseCheckTheInputFormat": "请检查输入格式", "theConditionConfigurationForRecommendationLevelDoesNotExist": "推荐级别的条件配置不存在", "applicant": "申请人", "thereIsCompanyDataInTheLevelAdjustmentListThatIsInconsistentWithTheApplicationForm": "级别调整清单中的存在与申请单上不一致的公司数据!", "ratingApplicationFormHeader": "评级申请单抬头", "successfullyClosed": "关闭成功！", "onlyBatchCreationOfRatingApplicationFormsForOneCompanyIsAllowed": "只允许批量创建一个公司的评级申请单", "monthlyPerformanceLevel": "个月绩效等级中", "supplierNameSupplierAbbreviationSupplierCode": "供应商名称、供应商简称、供应商编码", "supplierGradingReferenceInformationDoesNotExist": "供应商分级参考信息不存在", "currentLevelFromDataDictionarySlSupplierLevel": "当前级别;来自数据字典sl_supplier_level", "beforeChange": "变更前", "supplierClassificationTableId": "供应商分级表id", "second": "次", "categoryPositioning": "品类定位", "conditionConfigurationForRecommendationLevel": "推荐级别的条件配置", "theAttachmentRelationshipOfTheGradingApplicationFormDoesNotExist": "分级申请单附件关系不存在", "gradedApplicationFormMasterXlsx": "分级申请单主.xlsx", "adjustmentTypeFromDataDictionarySlAdjustmentType": "调整类型;来自数据字典sl_adjustment_type", "repetitiveOperationFrequency": "重复运行频率", "supplierGradingAdjustmentRecordDoesNotExist": "供应商分级调整记录不存在", "updateRecommendationLevel": "更新推荐级别", "conditionPerformanceLevel": "条件：绩效级别", "pleaseSelectTheCurrentLevel": "请选择当前级别", "afterTheChange": "变更后", "pleaseSelectATodoPerson": "请选择待办人", "performanceLevelConfiguration": "绩效级别配置", "pleaseSelectCategoryPositioning": "请选择品类定位", "theRecommendedRunningRecordOfTheSystemDoesNotExist": "系统推荐运行记录不存在", "approvalLevelApplicationForm": "审批分级申请单", "successfullyReturned": "退回成功！", "pleaseSelectTheCurrentRatingStatus": "请选择当期评级状态", "pleaseSelectTheCompanyForTheApplicationFormFirst": "请先选择申请单的公司", "levelAdjustmentRecord": "级别调整记录", "successfullyExecutedLevelRecommendation": "级别推荐执行成功", "performanceLevelConfigurationDoesNotExist": "绩效级别配置不存在!", "allOtherSituationsincludingNotParticipatingInPerformanceEvaluation": "其他所有情况（包括未参与绩效考核）", "levelCode": "级别代码", "performanceLevelOccurrenceFrequencyInputErrorPleaseEnterAFrequencyGreaterThanZeroAndLessThanTheNumberOfConsecutiveMonthsInThePast": "绩效等级出现次数输入错误，请输入大于零且小于过去连续月数的次数！", "times": "次，", "failedToUpdateRecommendationLevel": "更新推荐级别失败", "cannotapprove": "海选，潜在，试用，临时供应商，不能发起评级申请单", "previously": "过去", "oneYearTransactionarrival": "一年交易(到货)", "pleaseEnterTheRatingApplicationNumber": "请输入评级申请单号", "successfullyUpdatedRecommendationLevel": "更新推荐级别成功", "performanceLevel": "绩效级别", "initialRunningTime": "初次运行时间", "allocation": "配置", "missingCategoryPositioningConfigurationForCorrespondingCompanycategory": "缺少对应公司/品类的品类定位配置!", "recommendationLevel": "推荐级别", "approval": "审批", "recommendationLevelServiceIsRunning": "推荐级别服务正在运行中!", "pleaseSelectDataFirst": "请先选择数据", "currentRatingStatus": "当期评级状态", "applicationFormId": "申请单id", "pleaseSelectTheApplicant": "请选择申请人", "performanceLevelDescription": "绩效级别说明", "currentLevel": "当前级别", "monthlyRecommendation": "月推荐一次", "halfYearTransactions": "半年交易", "halfYearTransactionarrival": "半年交易(到货)", "systemRecommendedConfiguration": "系统推荐配置", "immediateRunlevelRecommendation": "立即运行级别推荐", "levelRecommendationExecutionFailed": "级别推荐执行失败", "recommendedTime": "推荐时间", "areYouSureToExportAllSupplierRatingDataItems": "是否确认导出所有供应商分级数据项?", "initiateRatingApplication": "发起评级申请", "effectiveTimeOfLevel": "级别生效时间"}, "avpl": {"materialSupplierLadderRelationshipId": "物料供应商阶梯关系ID", "deliveryMethod": "交货方式", "minimumOrderQuantity": "最小起订量", "minimumPackagingQuantity": "最小包装量", "priceValidityPeriodEnds": "价格有效期截止", "materialMasterDataId": "物料主数据ID", "source": "来源", "changeTrend": "变更趋势", "procurementGrouptemporaryStorageSubsequentAssociation": "采购组(临时存储，后续做关联)", "approvalFormNo": "审批单编号", "priceValidUntil": "价格有效期至", "manufacturerQuoted": "报价的制造商", "demandTime": "Demand Time", "pleaseEnterTheSupplier": "请输入供应商", "rfqProjectId": "RFQ项目ID", "materialnewlyAddedCurrentlyNotAvailableInRfq": "材质(新增，目前RFQ那边没有)", "approvalId": "审批ID", "drawingVersionnewlyAddedCurrentlyNotAvailableInRfq": "图纸版本（新增，目前RFQ那边没有）", "originalCurrency": "原始币种", "priceValidityPeriodStartTime": "价格有效期开始时间", "avpl": "物料价格库", "materialRfq": "物料询价", "pleaseSelectAvplMaterial": "请选择需要询价的物料", "unitPrice": "单价", "purchaseUnitQuantity": "采购单位数量", "priceValidityPeriodStarts": "价格有效期开始", "unitPriceBeforeTax": "未税单价", "minimumConsumption": "最低消费", "quotedManufacturersPartNumber": "报价的制造商料号", "materialId": "物料ID", "quantityInBuom": "基本单位数量", "unitPriceIncludingTax": "含税单价", "saveAsPicture": "保存为图片", "materialSupplierRelationshipId": "物料供应商关系ID", "minimumPackageQuantity": "最小包装数量", "pricingUnit": "计价单位", "buomunitOfMeasurementOfRfqMaterial": "基本单位=rfq物料的计量单位", "clickSave": "点击保存", "priceEffectiveEndDate": "价格有效结束日期"}, "rfq": {"historicalPrice": "历史价格", "batchAddMaterials": "批量新增物料", "theProjectMaterialHandlerDoesNotExist": "项目物料办理人不存在", "productModel": "产品型号", "theMaterialSupplierRelationshipDoesNotExist": "物料供应商关系不存在", "noPriceTheSystemWillJudgeNoQuotationAfterSubmissionPleaseConfirmWhetherToSubmit": "无价格，提交后系统将判定为无报价，请确认是否提交", "discardThePossiblyGeneratedApprovalReportAndExit": "放弃可能已生成的审批报告并退出", "rfqList": "询价单列表", "comparisonOfSupplierQuantity": "比价供应商数量", "thereAreDataInNonStatusMaterialStatustoBeRecommendedAndRfqStatusquotedSubmissionFailed": "存在非状态物料状态【待推荐】，询价状态【已报价】的数据，提交失败！", "supplierContactPersonOrEmailDoesNotExist": "供应商联系人或邮件不存在", "cannotDeleteAllQuotations": "不能删除全部报价", "path": "路径", "enclosure": "附件", "stepStatusDoesNotExist": "步骤状态不存在", "batchDownloadDrawings": "批量下载图纸", "approvalFormCreationDate": "审批单创建日期", "inquiryChartDataStatisticsDoNotExist": "询价图表数据统计不存在", "recommendedRemarks": "推荐备注", "batchUploadQuotations": "批量上传报价单", "preprocessing": "预处理", "returnToSimulationConfiguration": "返回模拟配置", "supplierRecommendationPrinciple": "供应商推荐原则", "priceComparisonCalculator": "比价计算器", "plannedMinimumOrderQuantity": "计划最小订货量", "eitherTheQuotationIncludesTaxOrTheQuotationDoesNotIncludeTaxMustBeFilledIn": "报价含税和报价未税必须填写其中一个", "quotationRemarks": "报价备注", "theProjectQuotationDisplayFieldConfigurationDoesNotExist": "项目报价单显示字段配置不存在", "theQuotationInThisStateCannotBeRevoked": "该状态下的报价无法被撤销", "allowSuppliersToModifyPriceLadders": "允许供应商修改价格阶梯", "requestedOrderQuantityFrom": "要求订货量从", "atLeastRetainOneSupplierToParticipateInSimulationDecisionmaking": "至少保留一个供应商参与模拟决策", "expiryDateOfPriceValidity": "价格有效期截止日", "thereIsNoDataToUpdateOnTheCurrentPriceAnalysisPage": "当前价格分析页面没有可以更新的数据", "includeItemsThatDoNotNeedToBeSubmittedForSourcing": "包含无需提交寻源的物料", "lineNumber": "行号", "detectedThatTheMaterialQuotationLadderDoesNotMeetTheTargetOrderQuantityClickCancelToReturnToModificationClickConfirmToSkipPricelessMaterialsAndContinueExecution": "检测到物料报价阶梯不符合目标订单量，点击取消返回修改，点击确定跳过无价格物料继续执行", "previewReport": "预览报告", "generateQuotation": "生成报价单", "theInquiryMaterialsAndSuppliersInThisStateCannotBeDeleted": "该状态下的询价物料及供应商无法被删除", "templateCode": "模板代码", "drawingList": "图纸列表", "plannedLeadTime": "计划交货周期（天）", "backToOverview": "返回总览", "allowSuppliersToModifyPurchaseUnits": "允许供应商修改采购单位", "templateAlreadyExistsForCategory": "品类已存在模板", "targetCurrencyMustBeSelected": "目标币种必须选择", "approvalId": "审批Id", "thereIsADataErrorInThisQuotation": "该报价单发生数据错误！", "approvalFormDoesNotExist": "审批单不存在", "fundOccupationtargetCurrency": "资金占用（目标币种）", "enterRejectionComments": "输入拒绝意见", "orderQuantityArrived": "订货量到", "temporaryMaterialsselfBuiltMaterialsThatCannotBeFoundInTheMainMaterialWarehouse": "临时物料（物料主库查询的不到的情况下，自建的物料)", "thereAreMaterialsThatHaveNotBeenAssignedToSuppliersPleaseSelectASupplierFirstBeforeCreatingAnInquiryForm": "有物料未分配供应商。请先选择供应商，再创建询价单！", "approver": "审批人", "enterExchangeRate": "输入汇率", "surfaceTreatment": "表面处理", "noOperationalData": "无操作数据！", "reportAndApproval": "报告及推荐", "quotationSuccessRate": "报价成功率", "pleaseSelectTheMaterialToBeRecommended": "请选择要推荐物料", "pleaseSelectTheRfqItemToCancel": "请选择要撤销的询价项目", "taxRateNotFilledIn": "税率没有填写", "materialMainWarehouseId": "物料主库ID", "projectCancellation": "项目撤销", "pleaseSaveTheProjectFirst": "请先保存项目", "paymentMethodRequired": "账期", "importSucceeded": "导入成功", "expiryDateOfRecommendedPrice": "推荐价格有效期截止日", "inquiryNumber": "询价编号", "categoryNotFilledOrFilledIncorrectly": "品类没有填写或填写错误", "empty": "清空", "batchOperation": "批量操作", "viewPurchaserAttachments": "查看采购方附件", "exchangeRate": "汇率", "noDataFoundToCreateInquiryForms": "未发现可以创建询价单的数据", "pleaseFillInTheTargetOrderQuantity": "请填写目标订单量", "abandonSubmissionAndClosePopupWindow": "放弃提交并关闭弹窗", "afterYouClickOkTheApprovalFormWillBeReturnedAutomatically": "您点击确定后，系统将自动退回审批单", "inquiryModeDoesNotExist": "询价模式不存在", "thereIsNoDataThatCanBeReturned": "没有可以退回的数据", "selectedSupplier": "已选供应商", "thereAreMaterialsThatCannotBeAssignedToSuppliers": "存在无法分配供应商的物料！", "projectNoteDoesNotExist": "项目备注不存在", "fieldsDisplayed": "显示的字段", "thereIsNoRecommendedSupplierForMaterialCode": "物料编码【{0}】没有推荐供应商！", "columnDisplayCommonScheme": "列显示常用方案", "copiedInquiryHyperlink": "已复制询价单超链接", "uploadQuotationAttachment": "上传报价单附件", "interfaceRecord": "接口记录", "schemeNameCannotBeEmpty": "方案名称不能为空", "areYouSureToExportAllMaterialData": "是否确认导出所有物料数据?", "projectId": "项目ID", "viewBasicProjectInformation": "查看项目基本信息", "pleaseSelectAPrinciple": "请选择原则", "copyAddress": "复制地址", "onlyLookAtThePriceReducedMaterials": "只看降价物料", "quoteStatus": "报价状态", "onlyRecommendedAndApprovedReturnedMaterialsAreAllowedToBeTemporarilyUnapproved": "只允许对待推荐、审批退回的物料执行暂不审批操作！", "targetPrice": "目标价格", "noSimulationScheme": "无模拟方案", "quotationResponseDate": "报价回复日期", "deadlineForQuotation": "报价截至时间", "materialSupplierStatisticsDoNotExist": "物料供应商统计不存在", "purchasersReturnComments": "采购方退回意见", "urgentApproval": "审批加急", "explicitImplicitColumn": "显隐列", "myToDo": "我的待办", "areYouSureToExportAllApprovedBillsOfMaterials": "是否确认导出所有审批物料清单?", "onlyMaterialSuppliersInTheNewlyCreatedAndPendingSourcingStatusCanBeDeleted": "只能删除新建和待寻源状态下的物料供应商", "materialDrawingszip": "物料图纸.zip", "pleaseSelectTheApprovalFormToBeExpedited": "请选择要加急的审批单", "materialLevel": "物料阶层", "approvalForm": "审批单", "material": "物料", "theMaterialHasCreatedAQuotationAndCannotBeShelved": "物料已创建询价单，不可搁置！", "minimumUnitPriceCurrency": "最低单价币种", "updateQuotationNotesSucceeded": "更新报价备注成功", "withdrawalOfReturnedComments": "撤销退回意见", "supplierWithLowestUnitPrice": "最低单价供应商", "rfqSource": "询价单来源", "pleaseSaveTheProjectInformationFirst": "请先保存项目信息", "comparisonAnalysisImportTemplatexls": "比较分析导入模板.xlsx", "notApprovedTemporarily": "暂不审批", "theRelationshipBetweenTheQuotationAndTheMaterialSupplierDoesNotExist": "报价单与物料供应商关系不存在", "alreadyQuotationCannotUndo": "系统将尝试撤销此询价单的报价，如果采购已经处理报价则无法撤销。", "materialCodeAndMaterialDescription": "物料编码、物料描述", "itemTermination": "物料终止", "projectMaterial": "项目物料", "theOrderQuantityIsRequiredToReach": "要求订货量到", "selectAtLeastOneSupplierForASingleSupplierSolution": "单一供应商方案至少选择一个供应商", "theQuotationMaterialSupplierQuantityLadderDoesNotExist": "报价单物料供应商数量阶梯不存在", "recommendedSuppliers": "推荐供应商", "vsHistoricalPriceChangeRate": "vs历史变价率", "followAndUrge": "跟催", "rfqItemNameRfqItemNumberQuotationNumberMaterialCodePurchaserSupplierAbbreviation": "询价项目名称、项目编号、报价单号、物料编码、采购员、供应商简称", "numberOfSteps": "阶梯数量", "priceComparisonxls": "物料比价.xlsx", "theMaterialIsInAPendingApprovedAndCompletedStateAndCannotBeDeleted": "物料处于待审批，已审批，已完成的状态，无法操作", "onlyRecommendedApprovedAndReturnedAndCompletedMaterialsAreAllowedToPerformOperations": "只允许对待推荐、审批退回、已完成的物料执行操作！", "rfqNo": "询价单号", "clickToGeneratePreviewEffectAccordingToApprovalReportConfiguration": "点击可按审批报告配置生成预览效果", "inquirySheet": "询价单", "loadingDestination": "装运目的地", "priceCalculation": "价格计算", "recommendedPriceExpirationDate": "推荐价格有效期截止", "simulatedDecisionmaking": "模拟决策", "pleaseEnterTheReturnComments": "请输入退回意见", "batchImportOfMaterialPrices": "批量导入物料价格", "returnedBy": "退回人", "approvalFormList": "审批单清单", "pleaseEnterComments": "请输入意见", "projectMaterialId": "项目物料ID", "recommendedSupplierName": "推荐供应商名称", "applicationPlan": "应用方案", "approveMaterialQuantity": "审批物料数量", "quotationOriginalCurrency": "报价原币种", "theInquiryProjectMaterialDoesNotExist": "询价项目物料不存在", "materialsThatHaveNotStartedOrCompletedTheInquiryCannotBeTerminated": "不可终止未开始询价或已完成询价的物料！", "purchasersRequirements": "采购方要求", "purchaseUploadQuotationAttachment": "采购上传报价附件", "onlyRecommendedAndApprovedReturnedMaterialsAreAllowedToPerformOperations": "只允许对待推荐、审批退回的物料执行操作！", "deliveryCycledays": "交货周期（天）", "supplierQuotationRemarks": "供应商报价备注", "historicalExchangeRate": "历史汇率", "thereIsNoCategoryInformationUnderMaterialCodePleaseSelectTheCorrespondingCategory": "物料编码{0}下不存在品类信息，请选择对应品类", "withdrawalOfReturnedCommentsSucceeded": "撤销退回意见成功", "sendRecommend": "推送选点评估", "urgent": "加急", "pleaseEnterASchemeName": "请输入方案名称", "theFieldsYouCheckedWillBeDisplayedInTheBasicItemInformationColumnOfTheRoomPriceList": "被勾选的基本信息将展示给供应商", "numberOfApprovals": "审批次数", "advancedRecommendedConfiguration": "高级推荐配置", "rfqMaterialxls": "询价物料.xlsx", "theMaterialCodeComparisonDoesNotMatchTheMaterialMasterData": "物料编码对照与物料主数据不匹配！", "successfullySubmittedReturnComments": "提交退回意见成功", "theRequiredOrderQuantityHasNeverBeenFilledIn": "要求订货量从没有填写", "plannedDeliveryCycle": "计划交货周期", "followUpSuccess": "跟催成功", "hide": "隐藏", "youAreTerminatingTheMaterial": "您正在终止物料！", "return": "退回", "approvalNo": "审批单号", "materialDescriptionNotFilledIn": "物料描述没有填写", "reasonForReturn": "退回原因", "sign": "标记", "businessId": "业务ID", "createRfq": "创建询价单", "categoryegElectronicMechanicalComponentspowerCords": "品类(例：电子机械件/电源线)", "arrangement": "层次", "download": "下载", "quotationSubmit": "系统将提交所有物料，若存在未输入报价的物料，将作为无报价提交。", "pleaseSelectTheTargetCurrency": "请选择目标币种", "eventName": "项目名称", "synchronizeSheetLibrary": "同步图纸库", "approvalAndSupplierAttachmentRelationshipDoNotExist": "审批与供应商附件关系不存在", "unitPriceIncludingTaxpriceUnit": "单价含税/价格单位", "billOfMaterials": "物料清单", "billOfMaterialsHandler": "物料清单办理人", "pleaseEnterTheReturnCommentsOfTheRfqSheet": "请输入退回意见", "rfqMaterialAndSupplierRelationshipId": "询价物料与供应商关系ID", "priceComparisonReport": "系统比价报告", "rfqItemNo": "项目编号", "approvalFormId": "审批单Id", "materialCodeNotFilledIn": "物料编码没有填写", "supplierQuotationFeedback": "供应商报价反馈", "uploadQuotationAttachments": "上传报价附件", "thePurchaserIsNotAllowedToModifyThePurchasingUnit": "采购方不允许修改采购单位！", "quotationUnitPriceIncludingTax": "报价单价含税", "onlyLookAtReturnedMaterials": "只看退回物料", "suppliersThatHaveCreatedRfqSheetsWillNotBeDeleted": "已创建询价单的供应商不会删除", "notYetQuotedCannotBeRevoked": "尚未报价，不允许撤销！", "pleaseFillInTheCompleteExchangeRate": "请填写完整汇率", "categorymfgmpn": "品类/制造商", "batchCreateMaterialDetailsTemplatexls": "批量创建物料明细模板.xlsx", "followUpOfRfq": "询价单跟催", "simulationResults": "模拟结果", "delivery_create_time": "创建日期", "noQuotationInformation": "无报价信息", "approvalDate": "审批日期", "youAreCancelingTheInquiryItem": "您正在撤销询价项目！", "deliveryPerioddays": "交货周期(天)", "priceRecommendationAttachment": "采购推荐附件", "theQuotationDisplayFieldConfigurationDoesNotExist": "报价单显示字段配置不存在", "materialCodeCrossReference": "物料编码对照", "approveBom": "审批物料清单", "drawingType": "图纸类型", "submitWithoutApproval": "提交不审批", "secondPriorityPrinciple": "第二优先原则", "approvalNumber": "审批编号", "reportTemplate": "报告模板", "documentSource": "单据来源", "theProjectCreatorIsNotTheCurrentUserAndCannotBeRevoked": "项目创建人非当前用户，不可撤销！", "approvalFormDetails": "审批单详情", "application": "应用", "pleaseAddCategoryFirst": "请先添加品类", "historicalSuppliers": "历史供应商", "pleaseSelectTheMaterialToBeReturned": "请选择要退回的物料", "noOffer": "检测到未输入报价的物料！点击确定后将作为无报价提交。如您希望仅提交已报价物料，请点击“部分提交”。", "theLadderQuantityOfProjectMaterialsDoesNotExist": "项目物料的阶梯数量不存在", "purposeOfInquiry": "询价目的", "pleaseSelectMaterialsFirst": "请先选择物料", "clickToView": "点击查看", "fileName": "文件名", "youAreAboutToSendAFollowupEmailToTheSupplierPleaseEnterTheDescription": "您将要发送跟催邮件给供应商，请输入说明文字", "submitForApproval": "提交审批", "copySuccessfullyPrompted": "成功提示的文案", "theRequiredOrderQuantityIsFromeg": "要求订货量从(例：1/501)", "simulationScheme": "模拟方案", "orderQuantityFrom": "订货量从", "recommendedConfiguration": "推荐配置", "requestedResponseDate": "要求回复日期 ", "approvalResults": "审批结果", "priceComparisonAndRecommendation": "比价推荐", "pleaseEnterTheApprover": "请输入审批人", "existingPurchasePrice": "现有采购价格", "lowestUnitPriceWithoutTax": "最低单价未税", "projectDeadline": "项目截止日期", "assignVendor": "选择供应商", "noRevocableData": "无可撤销数据!", "pleaseEnterTheReasonForRevocation": "请输入撤销原因", "followUpFailed": "跟催失败", "purchasersQuotationRequirements": "采购方报价要求", "inquiryDetails": "询价单详情", "unitPriceExcludingTaxpriceUnit": "单价未税/价格单位", "configureRfq": "配置询价单", "recommendedUnitPriceIncludingTax": "推荐单价含税", "mixtureRatio": "配比", "previewReportRecordDoesNotExist": "预览报告记录不存在", "projectRemarks": "项目备注", "materialCodeSupplier": "物料编码、供应商", "numberOfAccessories": "附件数量", "recommend": "推荐", "supplierCode": "供应商代码", "priceApprovalAndMaterialSupplierRelationshipDoNotExist": "价格审批与物料供应商关系不存在", "terminateItem": "终止物料", "pleaseImportTheMaterialsFirst": "请先导入物料", "attachmentDoesNotExist": "附件不存在", "currency": "币别", "newMaterial": "新增物料", "quotationDeadline": "报价截至日期", "drawingName": "图纸名称", "uploadReportAttachments": "上传报告附件", "rfqAlreadyExistsPleaseDoNotSubmitAgain": "已存在询价单,请勿重复提交！", "showhide": "显示/隐藏", "materialCode": "物料编码：", "theUploadedDataForPriceAnalysisIsEmpty": "价格分析上传数据为空", "onlyDocumentsThatRequireUrgentApprovalAreAllowed": "只允许加急待审批的单据！", "theQuotationDoesNotExist": "报价单不存在", "schemeNameCannotExceedCharacters": "方案名称不能超过10个字符", "theMaterialHasCreatedAQuotationAndCannotBeDeleted": "物料已创建询价单，不可删除！", "deleteItem": "删除物料", "pleaseEnterMaterialCodeSupplierAbbreviationMpnmfg": "请输入物料编码、供应商简称、MPN/MFG", "theQuotationTemplateAndCategoryRelationshipDoNotExist": "报价模板与品类关系不存在", "deliveryCycle": "交货周期", "recommendedConclusion": "推荐结论", "youAreEnteringReturnCommentsForTheFollowingMaterials": "您正在对以下物料输入退回意见", "areYouSureYouWantToDeleteTheSelectedMaterial": "确定要删除选择的物料么?", "quotationBom": "报价物料清单", "pleaseEnterTheContent": "请输入内容", "selfMadeOrPurchased": "自制或者采购", "principle": "原则", "manufacturerRepliedByTheSupplier": "供应商回复的制造商", "project": "项目", "pleaseSelectAReportTemplate": "请选择报告模板", "templateId": "模板ID", "projectDoesNotExist": "项目不存在", "productLine": "产品线", "importDataCannotBeEmpty": "导入数据不能为空", "returnAll": "全部退回", "generateSimulationResults": "生成模拟结果", "projectItemStatus": "项目物料状态", "notParticipatingInPriceRecommendation": "不参与价格推荐", "deadlinerequiredResponseDate": "截止日期(要求回复日期)", "totalAttachmentsShouldNotExceedmb": "总附件不得超过100MB", "noDataToDelete": "无可删数据!", "materialQuantity": "物料数量", "targetOrderQuantity": "目标订单量", "termination": "终止", "quotationId": "报价单ID", "submitSourcing": "提交寻源", "recommendedUnitPriceExcludingTax": "推荐单价未税", "templateConfiguration": "模板配置", "thePurchaserIsNotAllowedToModifyTheQuantityLadder": "采购方不允许修改数量阶梯！", "viewQuotationAttachments": "查看报价附件", "selectAll": "全选所有", "manufacturersPartNumberRepliedByTheSupplier": "供应商回复的制造商料号", "youAreTerminatingRfqMaterialsPleaseEnterTerminationComments": "您正在终止询价单物料，请输入终止意见", "theMaterialDoesNotExistInThisQuotation": "物料在此报价单中不存在", "drawingLibraryDoesNotExist": "图纸库不存在", "theMaterialStatusIsNotRecommendedAndCannotBeEdited": "物料状态不是待推荐，不允许编辑", "exchangeRateRequired": "汇率必填", "submit": "提 交", "afterYouClickOkYouCanStillModifyTheReturnedCommentsOrWithdrawComments": "您点击确定后，仍可重新修改退回意见或撤销意见", "thePurchaseHasBeenProcessedAndCannotBeRevoked": "采购已处理，无法撤销！", "theQuotationInThisStateCannotBeTerminated": "该状态下的报价无法被终止", "supplierUploadAttachmentId": "供应商上传附件ID", "revoke": "撤销", "erpSynchronizationMessage": "ERP同步消息", "returnComments": "退回意见", "pleaseEnterText": "请输入项目备注", "simulateTheTotalQuantityOfMaterials": "模拟物料总数量", "submitComments": "提交项目备注", "priceCategoryNotFilledOrFilledIncorrectly": "价格类别没有填写或填写错误", "quotationDetails": "报价明细", "timeForEvaluationAndQuotation": "平均报价用时", "urgentSuccess": "加急成功", "areYouSureToRevokeThisApprovalForm": "确定撤销此审批单吗？", "recommendedVsHistoricalPriceChangeRate": "推荐vs历史变价率", "approvalInitiationDate": "审批发起日期", "approvalStatus": "审批状态", "totalCostThisTimetargetCurrency": "本次总成本（目标币种）", "viewDetailsOfRejectionComments": "查看拒绝意见明细", "starMarker": "星标", "youAreReturningAllMaterialsInTheApprovalForm": "您正在退回审批单内全部物料", "deleteSupplier": "删除供应商", "consumption": "用量", "approvalTime": "审批时间", "pleaseSelectTheRfqToRecommend": "请选择要推荐的询价单", "includeSuppliersForWhichRfqHasBeenCreated": "包含已创建询价单的供应商", "successfullySubmittedForApproval": "提交审批成功", "targetPriceStatus": "目标价状态", "createdBy": "创建人", "batchQuoteTemplatexls": "批量报价模板.xlsx", "MinimumOrderQuantity": "最小订单数量", "eventNameInquiryItemNumber": "项目名称、项目编号", "dataUniquenessEncoding": "数据唯一性编码", "basicInformationOfTheProject": "项目基本信息", "youAreTerminatingTheRfqPleaseEnterTerminationComments": "您正在终止询价单，请输入终止意见", "pleaseEnterTheReason": "请输入原因", "transferToSubmit": "系统将提交已输入价格的物料，并将无价格的物料转移到新询价单以供后续提交。", "approvalFormName": "审批单名称", "rfqStatus": "询价单状态", "fileNotFound": "文件未找到！", "materialCodeAndFileName": "物料编码、文件名", "productName": "产品名称", "manufacturermanufacturerItemNo": "制造商/制造商料号", "notParticipatingInTheSimulationPlan": "不参与模拟方案", "price": "价格", "doYouWantToContinue": "是否继续?", "approvalDocStatus": "审批单状态", "pleaseEnterCategoryCodename": "请输入品类编码/名称", "priceCategory": "价格类别", "rateOfChange": "变价率", "afterYouClickOkTheSystemWillAutomaticallyReturnTheQuotation": "您点击确定后，系统将创建询价单给供应商重新报价", "supplierConfirmationOfPrice": "发供应商确认价格", "reportAndAttachments": "报告及附件", "quotationsInThisStateCannotBeReturned": "该状态下的报价无法被退回", "theResultIsInvalidPleaseRecalculateAndApply": "该结果已失效，请重新计算并应用", "approvalDocCreator": "审批单创建人", "newMaterialsAndSuppliers": "新增物料及供应商", "pleaseSelectMaterial": "请选择物料", "batchQuotation": "批量报价", "longestLt": "最长LT", "numberOfQuotations": "报价次数", "uploadAttachmentsForProcurement": "采购上传附件", "saveScheme": "保存方案", "inquiryId": "询价ID", "onlyMaterialsToBeApprovedAreAllowedToPerformOperations": "只允许对待审批的物料执行操作！", "pleaseEnterTheReasonForTermination": "请输入终止原因", "approvalProcessConfiguration": "审批流配置", "handledBy": "办理人", "onlyDocumentsWithQuotationRemindersAreAllowed": "只允许跟催待报价的单据！", "detectedAnInformalSupplierPleaseCheck": "检测到非正式供应商，请检查!", "display": "显示", "approvalFormHasBeenCompletedOperationNotAllowed": "审批单{0}已完成，不允许操作", "pleaseEnterTheRequiredReplyDate": "请输入要求回复日期", "applicationConfiguration": "应用配置", "opinion": "退回意见", "pleaseEnterTheMaterialCode": "请输入物料编码", "viewReturnedItems": "查看退回物料", "historicalCurrency": "历史币种", "projectCreationDate": "项目创建日期", "selectSupplier": "选择供应商", "terminatedSuccessfully": "终止成功", "thisFieldIsNotMaintainedPleaseCheckTheBasicInformationOfTheProject": "此字段未维护，请检查项目基本信息", "noPriceEntryTemplateConfiguredOrNotFound": "没有配置价格录入模版或者价格录入模板未找到", "addTo": "添加", "returnRemarks": "退回备注", "onlyLookAtThePriceRisingMaterials": "只看涨价物料", "suffixType": "后缀类型", "theSupplierRecommendationPrincipleMustBeSelected": "供应商推荐原则必须选择", "translate": "翻译", "pleaseEnterTheApprovalName": "请输入审批名称", "detectedInformalMaterialPleaseCheckMaterialCodeMfgMpn": "检测到非正式物料，请检查物料编码，MFG，MPN!", "theProjectHasCreatedAQuotationAndCannotBeRevoked": "项目已创建询价单，不可撤销！", "cancellationSucceeded": "撤销成功", "quoteTemplate": "报价模板", "requestAnOrderQuantityOfeg": "要求订货量到(例：500/1500)", "approveMaterialxls": "审批物料.xlsx", "technologicalRequirements": "工艺要求", "numberOfInquiries": "询价次数", "approvalMaterialRelationshipDoesNotExist": "审批物料关系不存在", "associatedRfqNo": "关联询价单号", "quotationBomPartialCommit": "系统将提交已输入的物料，并将无价格的物料转移到新询价单以供后需提交。是否继续？", "currentApprover": "当前审批人", "returnCommentsEnteredSuccessfully": "输入退回意见成功", "clauseDetails": "条款明细", "quotedUnitPriceExcludingTax": "报价单价未税", "returnTime": "退回时间", "areYouSureYouWantToDeleteTheSelectedSupplier": "确定要删除选择的供应商?", "serialNo": "序号", "estimatedAnnualConsumption": "预计年用量", "schemeNameAlreadyExists": "方案名称已存在", "theDataHasChangedPleaseRefreshTheList": "数据已变更，请刷新列表！", "viewTemporaryMaterials": "查看临时物料", "pleaseEnterCategoryNameMaterialCodeSupplierAbbreviationMpnmfg": "请输入品类名称、物料编码、供应商简称、MPN/MFG", "theCategoryOfProjectMaterialsDoesNotHaveAConfiguredInquiryTemplate": "项目物料的品类没有配置询价模板", "onlyRecommendedRecoveryOperationsAreAllowedForCompletedMaterials": "只允许对已完成的物料执行恢复推荐操作！", "shelvedMaterial": "搁置物料", "sponsor": "发起人", "areYouSureYouWantToEnterThePriceRecommendationAnalysisForDocumentsThatHaveNotBeenQuoted": "有未报价的单据，是否确认进入价格推荐分析？", "noQuotationAllowedForTheEntireOrder": "不允许整单无报价！", "terminalCustomerModel": "终端客户型号", "eventNameSupplierAbbreviationSupplierCode": "项目名称、供应商简称、供应商编码", "downloadQuotationAttachment": "报价附件下载", "uploadDrawings": "上传图纸", "requestedDeliveryMethod": "交货条件", "pleaseMaintainTheCurrentMaterialLineInformationCompletely": "请把当前物料行信息维护完整", "businessModel": "业务模式", "recovery": "恢复", "specialPurchaseType": "特殊采购类型", "enterReturnComments": "输入退回意见", "customSettings": "自定义设置", "TemporaryMaterialsCanNotToBeRecommended": "临时物料，不允许提交推荐审批！", "approvalApplicant": "审批申请人", "whenYouSubmitTheApprovalFormTheSystemWillAutomaticallyReturnTheMaterialsWithComments": "您提交审批单时，系统将自动退回带有意见的物料", "noteThisOperationCannotBeReplied": "注意：此操作无法恢复！", "uploadProjectAttachments": "上传项目附件", "systemRecommendedSupplier": "系统推荐供应商", "uploadPriceRecommendationAttachment": "上传采购推荐附件", "detailedInformation": "详细信息", "modifyProjectNotes": "修改项目备注", "plannedMinimumPackagingQuantity": "计划最小包装量", "supplierQuotationAttachment": "供应商报价附件", "priceComparisonAnalysis": "比价分析", "projectStatus": "项目状态", "noDataToRecommend": "没有数据可以推荐", "currencyAllocation": "货币配置", "drawingRemarks": "图纸备注", "templateName": "模板名称", "partialSubmission": "部分提交", "thereIsASupplierInTheInquiryStatusDeletingTheSupplierFailed": "存在询价状态中的供应商，删除供应商失败！", "rfqMaterial": "询价物料", "pleaseSelectARow": "请选择行", "orderQuantityMustBeInPositiveOrder": "要求订货量必须为正序", "categoryId": "品类ID"}, "sp": {"totalNumber": "总数", "paymentCycleCode": "付款周期代码", "year": "年", "paymentCycleImportTemplateXlsx": "付款周期导入模板.xlsx", "performanceAllocationFoundationDoesNotExist": "绩效配置基础不存在", "feedbackFrequencyType": "反馈频率类型", "settingOfDeductionStandardsForCountingPoints": "计次扣分标准设定", "score": "得分", "performanceScoringStandardSetting": "绩效评分标准设定", "startingMonth": "开始月", "supplierReportTemplateNotConfiguredForPerformancePleaseContactAdministrator": "绩效未配置供应商报告模板，请联系管理员", "setUp": "设置", "useVetoItems": "使用否决项", "theMaximumDeductionForThisItemIs": "分，最多扣至本项为", "saveAndEnterStandardSettings": "保存并进入标准设定", "thePerformanceOperationFoundationOfCompanyDoesNotExist": "公司{0}绩效运行基础不存在", "august": "8月", "monthlyYtmSummaryResultsDoNotExist": "按月汇总YTM结果不存在", "currentIndicators": "当期指标", "july": "7月", "pleaseFillInAllRequiredFieldsSupplier": "请将必填项填写完毕再进行供应商选择", "reminderTheAboveEmailAddressTitle": "提示：上述邮件地址还可能在系统运行后，接收到新增品类、新增供应商的提示，绩效运算报错的提示等。请注意查收！", "PleaseUploadManuallyRunMonthRelatedData": "1. 请上传手动运行的月份相关数据，超出设定月份的数据即便上传仍会被视为无效。", "ytmTotalScorePoints": "YTM总分（/100分）:", "setNewAnnualIndicators": "设置新的一年指标", "lastRunTime": "最近一次运行时间", "dateRangeIsMandatory": "日期范围必填", "downloadReport": "下载报表", "basicConfiguration": "基础配置", "downloadData": "下载数据", "pleaseFillInRuleWhichCannotBeInterrupted": "请填写规则三，规则三不可以间断", "sendPerformanceReport": "发送绩效报告", "importantReminder": "重要提示", "supplierAnnualPerformanceReportxlsx": "供应商年度绩效报告.xlsx", "originalFileUploadRecord": "原始文件上传记录", "tipWhenPerformingMonthlyPerformanceCalculations": "提示：执行当月绩效计算时，跨月数据上传无效。", "theSystemWillRunAutomaticallyOnTheDay": "系统将于自动运行日", "currentFrequency": "当前频率", "theStartDateMustBeEarlierThanTheEndDate": "开始日期必须小于结束日期", "pleaseSelectTheYear": "请选择年份", "addIndicators": "增加指标", "a": "a", "b": "b", "manualOperation": "手动运行", "c": "c", "d": "d", "aFewDaysAgoAnEmailWasSentTo": "天前，发送邮件至", "qualityImprovementOrderNumber": "品质改善单号", "performanceDetailsSupplierInformationXlsx": "绩效明细供应商信息.xlsx", "suppliersParticipatingInPerformanceXlsx": "绩效参与的供应商.xlsx", "timeFrame": "时间范围", "march": "3月", "notEvaluated": "未评估", "june": "6月", "accordingToTheCurrentIndicatorSetting": "根据当前指标设定，对以下范围的绩效进行手动运行更新", "automaticallyRunPerformanceCalculationsOnADailyBasis": "日，自动运行绩效运算。", "PleasePayAttentionToSelectingOneOrMoreCorrespondingCompaniesBeforeUploading": "2. 上传前请注意选择对应的一个或多个公司；", "theIncrementalTimeForObtainingOrderReceiptDataDoesNotExist": "获取订单收货数据的增量时间不存在", "november": "11月", "deductionPointsForUsageCount": "使用计次扣分", "scorepercentageSystem": "得分（百分制）", "theOperatingFrequencyIsNotAsExpected": "运行频率未在预期", "basicConfigurationAlreadyExists": "基础配置已存在", "basicDataDate": "基础数据日期(202302)", "createPerformanceSourceDataDownloadFile": "创建绩效源数据下载文件", "performanceOperationFoundationDoesNotExist": "绩效运行基础不存在", "previousYearsPerformanceLevel": "上一年绩效等级", "historicalUploadRecords": "历史上传记录", "savingDataCannotBeEmpty": "保存数据不能为空", "theDeductionConfigurationForCountingPointsDoesNotExist": "计次扣分配置不存在", "participatingSuppliersAndTheirWeights": "参与的供应商及权重", "tipPleaseUploadManuallyRunMonth": "提示：请上传手动运行的月份相关数据，超出设定月份的数据即便上传仍会被视为无效。", "supplementaryRules": "补充规则", "settingStandards": "设定标准", "categoryHasNoTransactionDataInYearAndCannotParticipateInPerformance": "品类【{0}】在{1}年中没有交易数据，无法参与绩效", "theFieldsRequiredForPerformanceOperationDoNotExist": "绩效运行的需要用到的字段不存在", "selectTheEndExecutionTime": "选择结束执行时间", "directlyDeductToPoints": "直接扣至0分", "ruleThree": "规则三", "performanceIndicatorContentConfigurationDoesNotExist": "绩效指标内容配置不存在", "materialCategory": "物料品类", "exportingParticipatingCategoriesAndWeightsXlsx": "参与的品类及权重导出.xlsx", "theHighestScoringStandard": "评分标准最高", "Appears": "，出现", "month": "月", "theFileIsBeingCreatedListLater": "文件创建中，请稍后于下载记录列表下载对应文件", "nextStep": "下一步", "companyRequired": "公司必填", "exportPaymentCycleXlsx": "付款周期导出.xlsx", "performanceCalculationBasedOn": "绩效运算按", "definitionOfAnnualIndicatorContent": "年指标内容定义", "currentOccurrence": "当期出现", "theResultOfThisIndicatorIs": "该指标结果为", "pleaseSelectTheYearFirst": "请先选择年份", "performanceBasedConfiguration": "绩效基础配置", "annualCumulativeAverage": "年累计平均", "rule": "规则二", "increaseLevel": "增加等级", "pleaseSelectATimeRange": "请选择时间范围", "executeImmediately": "立即执行", "displayCurrentScore": "显示当期得分", "downloadSupplierAnnualPerformance": "下载供应商年度绩效", "deleteData": "删除数据", "annualSupplierPerformanceOverview": "年度 供应商绩效表现 总览", "performanceIndicatorsDoNotExist": "绩效指标不存在", "performanceLevelStandardSetting": "绩效等级标准设定", "releaseStatus": "发布状态", "performanceLevel": "绩效等级", "performanceResultsOfEachIndicator": "各指标绩效结果", "displayResults": "显示结果", "participateInPerformanceEvaluationForTheYear": "参与该年绩效评估", "specialIndicatorUploadedDocumentDataDoesNotExist": "特殊指标上传文档数据不存在", "thereIsNoTransactionDataForAnyCategoryInThisYearSoItIsNotPossibleToAllocateCategoriesThatParticipateInPerformance": "本次{0}年中没有品类存在交易数据，无法配置参与绩效的品类", "supplierImportTemplateForPerformanceParticipationXlsx": "绩效参与的供应商导入模板.xlsx", "target": "目标", "pleaseSelectTaskStatus": "请选择任务状态", "executionTime": "执行时间", "numberOfSuccessfulUpdates": "更新成功数量：", "scoreForNoEventsOccurred": "未发生事件得分", "indicatorConfigurationDoesNotExist": "指标配置不存在", "pleaseSelectTheCorrectTimeRange": "请选择正确的时间范围", "trend": "趋势", "performanceLevelStandardConfigurationDoesNotExist": "绩效等级标准配置不存在", "previousStep": "上一步", "attentionTheSourceDataForSpecialIndicators": "注意：特殊指标的源数据为人工上传，请进入运行配置中搜索相关文件数据。", "april": "4月", "reminderPerformanceCalculation": "提醒绩效运算。", "indicatorId": "指标ID", "supplier": "供货商", "targetIndicators": "目标指标", "fieldId": "字段ID", "currentReport": "当期报告", "enableFilteringOnSelectedColumns": "对所选的列启用筛选", "downloadRecord": "下载记录", "fourSeasonsq": "四季度(Q4)", "theFunctionHasNotBeenDevelopedYet": "功能还未开发", "summarizeAndCalculateCategories": "品类进行汇总计算", "index": "指标", "scoreweight": "得分/权重", "accountingPeriodDataDoesNotExist": "账期数据不存在", "minimumBonusPoints": "最低加分", "selectMonth": "选择月", "previousYearPerformanceScore": "上一年绩效得分", "setIndicatorContent": "设置指标内容", "paymentCycleImport": "付款周期导入", "annualCumulativePerformanceScoreytm": "年累计绩效得分(YTM)", "categoryAndWeightOfParticipation": "参与的品类及权重", "performanceRunReminderEmailDoesNotExist": "绩效运行提醒邮箱不存在", "thereIsNoConfiguredCompanyPerformanceInformationForYear": "年份{0}不存在配置的公司绩效信息", "theLevelOfCategoryDoesNotMatchTheSummaryLevelOfThePageConfiguration": "品类【{0}】的等级与页面的配置的汇总等级不一致", "callExecutionSuccessfulServiceExecutionInProgress": "调用执行成功，服务执行中", "indicatorName": "指标名", "usingIndicatorValues": "使用指标值", "millisecond": " 毫秒", "scoringCriteriaNo": "评分标准第", "theFilesUnderTheCurrentCreationConditions": "当前创建条件下的文件正在生成中，请耐心等待文件生成", "displayCurrentYearsScore": "显示当年得分", "skip": "跳过", "notSupportingCrossYearSelection": "不支持跨年选择", "fieldValue": "字段值", "addUsageIndicators": "新增使用指标", "file": "文件", "performanceFeedbackListXlsx": "绩效反馈列表.xlsx", "uploader": "上传人", "december": "12月", "manuallyPerformingPerformanceCalculationsWillNotOnlyAffect": "手动执行绩效运算除了将影响设定的月份绩效结果外，截止到当前月的所有相关扣分、否决项、每个月的YTM的绩效结果和得分也将因此被更新。请留意核查", "nextTimeTheMaximumScoreIs": "次时，满分为", "setNewYearIndicators": "设置新一年指标", "specialIndicatorUploadFileRecordDoesNotExist": "特殊指标上传文件记录不存在", "may": "5月", "displayScore": "显示得分", "dateSelectionCannotCrossYears": "日期选择不能跨年", "january": "1月", "requestParameterError": "请求参数错误", "annualAccumulationytm": "年累计（YTM）", "indicatorPerformanceXlsx": "指标绩效.xlsx", "specialIndicatorConfigurationDoesNotExist": "特殊指标配置不存在", "thereIsNoTransactionDataForTheSuppliedGoodsCategoryInThisYearAndParticipatingSuppliersCannotBeConfigured": "本次{0}年中没有供应商品类存在交易数据，无法配置参与供应商", "ruleOne": "规则一", "pleaseKeepTheTimeRangeWithinTheSameYear": "请将时间范围保持在同一年内", "maximumBonusPoints": "最高加分", "selectIndicators": "选择指标", "section": "第", "performanceIndicatorRunningConfigurationDoesNotExist": "绩效指标运行配置不存在", "performanceVetoItemConfigurationDoesNotExist": "绩效否决项配置不存在", "batchSettingIndicatorWeights": "批量设置指标权重", "pleaseEnterPaymentCycleCodeAndPaymentCycleName": "请输入付款周期代码、付款周期名称", "thePerformanceScoringStandardConfigurationDoesNotExist": "绩效评分标准配置不存在", "operationRecords": "运行记录", "displayCurrentResults": "显示当期结果", "downloadHistoricalReports": "下载历史报表", "indicatorRequired": "指标必填", "areYouSureYouWantToPublishNotificationsInBulk": "你确认要批量发布通知？", "pleaseSelectACompanyFirst": "请先选择公司", "thePerformanceManualExecutionParameterSettingDoesNotExist": "绩效手动执行参数设置不存在", "indicatorType": "指标类型", "indicatorContent": "指标内容", "annualTrendOfPerformanceIndicators": "绩效指标年度趋势", "currentTotalScore": " 当期总分（/100）：", "toSetIndicatorsForTheNewYearPleaseSelectTheYear": "为新一年设置指标，请选择年份", "from": "从", "idSelfIncreasingPrimaryKey": "ID;自增主键", "theStartMonthCannotBeGreaterThanTheEndMonth": "开始月不能大于结束月", "selected": "已选择", "theSystemWillStartEvery": "系统将于每", "createImprovementOrder": "创建改善单", "participateIn": "参与", "level": "级", "executionStatus": "执行情况", "batchPublishingNotifications": "批量发布通知", "performanceLevelytm": "绩效等级（YTM）", "reminderPleaseUploadSpecialIndicatorData": "提示：请在执行前，上传相应时间段的各个采购组织的特殊指标数据。", "theNumberOfPerformanceLevelsHasExceeded": "绩效等级数量已经大于等于数据字典的绩效等级配置数量，不可添加", "paymentCycleScore": "账期得分", "companyId": "公司ID", "unit": "单位", "paymentCycleScoreSetting": "付款周期得分设定", "categoryAndWeightImport": "品类及权重导入", "october": "10月", "endMonth": "结束月", "send": "发送", "supplierImportsInvolved": "参与的供应商导入", "paymentCycleName": "付款周期名称", "specialIndicatorResults": "特殊指标结果", "releaseTime": "发布时间", "supplierId": "供应商ID", "line": "行", "dataDeletionSuccessful": "数据删除成功", "adjustAccordingToCustomerConfiguration": "", "closeEditing": "关闭编辑", "theQuarterlyPerspectiveOnTheProportionOfProcurementAmountDoesNotExist": "采购额占比季度视角不存在", "theIndicatorRunningContextDoesNotExist": "指标运行上下文不存在", "september": "9月", "performanceRatingytm": "绩效等级(YTM)", "documentStatus": "单据状态", "basicConfigurationDoesNotExistOrCompanysupplierprocurementHasNotYetBeenConfigured": "基础配置不存在或公司/供应商/采购尚未配置", "pleaseSelectTheIndicatorFirst": "请先选择指标", "pleaseEnterTheSupplierNameAndCode": "请输入供应商名称、编码", "indexCalculationProcessControlDoesNotExist": "指标计算过程控制不存在", "discoveringTheSamePerformanceLevelSavingFailed": "发现相同的绩效等级,保存不通过", "theSupplierAndCategoryFromTheOrderDoNotExist": "来源于订单的供应商和品类不存在", "performanceIndicators": "绩效指标", "reportDownload": "报表下载", "abbreviations": "缩写名", "q1": "一季度(Q1)", "q2": "二季度(Q2)", "q3": "三季度(Q3)", "participateInTheEvaluationOfStatisticalReports": "参与评估 统计报表", "secondaryDeduction": "次，扣", "settingOfVetoCriteria": "否决项标准设定", "participateInEvaluation": "参与评估", "supplierDoesNotExist": "供应商不存在", "pleaseUploadTheFileFirst": "请先上传文件", "february": "2月", "theTotalWeightOfTheIndicatorsInvolvedInCategoryDoesNotEqual": "品类【{0}】参与的指标的权重加总不等于100", "uploadTime": "上传时间", "ytmLevel": "YTM等级：", "automaticOperationPrompt": "自动运行提示", "minute": "分。", "currentLevel": "当期等级：", "lineMarkers": "行标记", "spreportdownload": "sp.reportDownload", "pleaseFillInTheSupplementaryRules": "请填写补充规则", "automaticOperationSettings": "自动运行设定", "notParticipating": "不参与", "theIndicatorFormulaConfigurationDoesNotExist": "指标公式配置不存在", "selectStartExecutionTime": "选择开始执行时间", "basicDataDoesNotExist": "基础数据不存在"}, "order": {"deliveryNoteDoesNotExist": "送货单不存在", "theDeliveryDateMustBeGreaterThanTodayPleaseFillInAgain": "要求到货日期必须大于今天，请重新填写", "importOrder": "导入订单", "theOrderLineStatusDoesNotSupportTheOperationPleaseRefreshThePageAndTryAgain": "订单行状态不支持操作，请刷新页面后重试", "youAreAboutToRejectTheSelectedOrderDataPleaseConfirm": "您将要拒绝勾选的订单数据，请确认。", "theImportedOrderFactoryIsDifferentFromTheOriginalOrderFactory": "此次导入的订单工厂与原订单工厂不相同", "deliveryNoteStatusDoesNotSupportOperation": "送货单状态不支持操作", "batchAdjustmentOfDeliveryTime": "批量调整交期", "localCurrency": "本币", "newAnswer": "新建答交", "batchSubmissionLeadTime": "批量提交交期", "printDeliveryNote": "打印送货单", "orderQuantity": "订单数量", "refusalOfDeliveryDate": "拒绝交期", "advance": "提前", "pleaseEnterTheBankAccount": "请输入银行账号", "purchasingPartyEmail": "采购方邮箱", "forwardingCompany": "货代公司", "doYouWantToEnableEmailRemindersForShipping": "是否开启邮件提醒发货", "overdueCalculationRules": "逾期计算规则", "deliveryNoteNo": "送货单号", "deliveryDetails": "送货明细", "operationSucceeded": "操作成功", "drawing": "图纸", "committedDeliveryDate": "承诺交货日期", "quantityReceived": "收货数量", "unitPriceWithoutTaxincludingPriceUnit": "单价未税（含价格单位）", "orderNoAndMaterialCode": "订单号、物料编码", "theCurrencyExchangeRateConfigurationForOrderNumberOrderLineDoesNotExist": "订单号{0}订单行{1}的币种汇率配置不存在", "planUpdateDate": "计划更新日期", "answerBankOfCommunicationsDoesNotExist": "答交行不存在", "reconfirm": "重新答交", "emailReminderForShipment": "邮件提醒发货", "signBackFile": "回签文件", "customerName": "客户名称", "accept": "接受", "buyer": "执行采购", "supplierContactPhone": "供应商联系人电话", "theQuantityOfTheCommunicationBankCannotExceedTheQuantityOfTheOrderLine": "答交行的数量不允许超出订单行的数量", "whenTheDeliveryDateIsConfirmedWhetherTheDeliveryQuantityExceedsThePlanIsJudgedAsNormal": "交期确认时，交付数量超出计划是否判断为正常", "submitAndAnswer": "提交答交", "failedToObtainTheCorrespondingCustomerPleaseContactTheAdministrator": "获取对应的客户失败，请联系管理员", "delay": "延后", "deliveryNoteDoesNotSupportMultipleReceipts": "送货单不支持多次收货", "uploadDate": "上传日期", "receivingDetails": "收货明细表", "insufficientDeliveryLimit": "交付不足限度", "orderFileDoesNotExist": "订单文件不存在", "onlyUpToAttachmentsAreSupported": "只支持最多5个附件", "newDeliveryOrder": "新建送货单", "amountIncludingTax": "含税金额", "dateSelection": "日期选择", "requiredArrivalQuantity": "要求到货数量", "deliveryNoteRemarks": "送货单备注", "orderLineComments": "订单行备注", "modifyTheRelationshipBetweenCurrencyAndOrderReport": "修改币种与订单报表关系", "purchaseAcceptanceArrivalDate": "采购接受到货日期", "modifyOrderfactoryAndAddressRelationshipConfiguration": "修改订单/工厂与地址关系配置", "orderHeaderDoesNotExist": "订单抬头不存在", "pleaseEnterTheSuppliersShortNameOrCode": "请输入供应商简称或编码", "theRemainingDeliverableQuantityIsLessThanAndOperationIsNotSupported": "剩余可交数量小于0，不支持操作", "deliveryDetailLine": "送货明细行", "onlyOneConfigurationRuleOfAllOtherTypesIsSupported": "仅支持存在一个其余全部类型的配置规则", "pleaseContactTheAdministratorToSetThePdfTemplateConfigurationOfTheDeliveryOrder": "请联系管理员设置送货单的pdf模板配置", "pleaseEnterRequiredItemsInTheForm": "请输入表格必填项", "youAreAboutToTemporarilyAcceptTheCheckedOrderDataPleaseConfirm": "您将要临时接受勾选的订单数据，请确认。", "refuse": "拒绝", "overdue": "逾期", "expectedArrivalDate": "预计到货日期", "allOtherRuleOptionsCanOnlyHaveOneOption": "其余全部规则选项只能有一条", "receiptVoucherNo": "收货凭证号", "orderReviewNo": "订单评审号", "close": "关闭", "doesNotAffectVersionUpgrade": "不影响版本升级", "orderLineStatus": "订单行状态", "numberOfSuccessfulImports": "导入成功数量", "receiptRelCertificate": "关联收货凭证", "productionLicenseNo": "生产许可证号", "caseStickerStatus": "箱贴状态", "orderxls": "订单.xlsx", "toUpdate": "更新", "companyLogo": "公司logo", "rejectOrder": "拒绝订单", "uploadUsers": "上传用户", "deleteRule": "删除规则", "automaticallySendEmailRemindersToSuppliersForShipment": "自动发送邮件提醒供应商发货", "deliveryOrderStatus": "送货单状态", "planLineDoesNotExist": "计划行不存在", "provisionalAcceptance": "临时接受", "selectAtLeastOneData": "至少选择一条数据", "receiptLn": "收货项次", "failedToObtainTheCorrespondingExecutionPurchasePleaseContactTheAdministrator": "获取对应的执行采购失败，请联系管理员", "returnWareHouseQuantity": "退仓数量", "areYouSureToDeleteTheOrderfactoryAndAddressRelationshipConfigurationNumberIs": "是否确认删除订单/工厂与地址关系配置编号为", "confirmDelivery": "确认送货", "to": "至", "remainingDeliverableQuantityOfTheOrder": "订单剩余可交数量", "describe": "描述", "supplierContact": "供应商联系人", "open": "开启", "pleaseSelectPieceOfData": "请选择1条数据", "orderNumber": "订单号", "unitPriceBeforeTaxincludingPriceUnit": "未税单价（含价格单位）", "orderReceipt": "订单签收", "theOrderIsNotInAPendingReceiptStatusAndCurrentOperationIsProhibited": "订单未处于待签收状态禁止进行当前操作", "youAreAboutToReconfirmTheSelectedOrderDataPleaseConfirm": "您将要重新确认勾选的订单数据，请确认。", "orderLineQuantity": "订单行数量", "factoryRequired": "工厂必填", "freeService": "免费项目", "temporaryAcceptanceOfDeliveryDate": "临时接受交期", "externalInspectionMaterials": "外检物料", "theNumberOfCommunicationBanksCannotBeOrTheDateMustBeGreaterThanTodayPleaseFillInAgain": "答交行数量不能为0或者日期必须大于今天，请重新填写", "cssClass": "CSS Class", "dictionaryName": "字典名称", "answeringBankId": "答交行id", "addCurrencyAndOrderReportRelationship": "添加币种与订单报表关系", "toVoid": "作废", "supplierRemarks": "供应商备注", "dateOfVoucher": "凭证日期", "whetherTheDeliveryIsJudgedAsNormalWhenTheDeliveryDateIsConfirmed": "交期确认时，提交交付是否判断为正常", "noCacheInformationFoundForTheSpecifiedRmbCurrency": "指定的人民币币种未找到缓存信息", "affectVersionUpgrade": "影响版本升级", "returnedItems": "退回项目", "theConfirmationStatusOfTheDeliveryDateInTheOrderLineIsNotPendingDelivery": "订单行中交期确认状态不在待答交", "answerStatus": "答交状态", "batchNo": "批次号", "deliveryQuantityToleranceSetting": "交付数量容差设置", "deliveryNote": "送货单", "tianhou": "天后", "youAreAboutToPublishTheCheckedOrderDataPleaseConfirm": "您将要发布勾选的订单数据，请确认。", "numberOfBoxes": "箱数", "saveVersionUpgradeFields": "保存版本升级字段", "deliveryLn": "送货项次", "delivery_confirmation_status": "交期确认状态", "suppliersCanOnlyAssociateOneSpecifiedConfigurationRule": "供应商只能关联一个指定的配置规则", "thereIsNoCorrespondingOrderLineDataForThePlanLine": "计划行没有对应的订单行数据", "unitPriceIncludingTax": "单价含税", "allOrders": "全部订单", "planLineNo": "计划行号", "failedToObtainLockPleaseTryAgain": "获取锁失败，请重试", "batchAnswer": "批量答交", "emailAndReminderSubmission": "邮件跟催答交", "doYouWantToInitiateEmailFollowupAndSubmission": "是否开启邮件跟催答交", "deliveryDriver": "送货司机", "currentlyOnlyNumbersGreaterThanAreSupported": "当前仅支持输入大于0的数字", "theOrderIsNotInTheSignedForStateAndCannotBeRejectedForSigning": "订单未处于已签收状态禁止拒接签收", "automaticallySendEmailsToUrgeSuppliersToRespond": "自动发送邮件跟催供应商答交", "updateConfiguration": "更新配置", "orderDate": "订单日期", "areYouSureYouWantToExportAllDataItemsOfTheAnsweringBank": "是否确认导出所有答交行数据项?", "acceptDeliveryTime": "接受交期", "preview": "预览", "emailReminder": "邮件提醒", "promiseDate": "承诺到货日期", "importPlan": "导入计划", "deliveryOntimeCalculationRules": "交付准时计算规则", "orderLine": "订单行", "receiptQuantity": "入库数量", "edition": "版本", "deliveryQuantityNotBiggerThanZero": "送货数量不允许小于0", "theOrderfactoryAndAddressRelationshipConfigurationDoesNotExist": "订单/工厂与地址关系配置不存在{0}", "whetherToTriggerVersionUpgrading": "是否触发升版", "updateFailed": "更新失败", "logInformation": "日志信息", "designatedSupplier": "指定供应商", "PleaseSureToCompleteDeliveryQuantityAndDeliveryDate": "请确保填写完整的答交数量和答交日期", "deliveryQuantity": "送货数量", "contractName": "合同名称", "company": "公司", "theExecutionPurchaseAndDeliveryNoteAssociatedWithTheImportedOrderDoNotMatch": "导入的订单关联的执行采购和送货单不匹配", "importFailed": "导入失败", "deliveredOnTimeAsRequired": "按要求交付准时", "areYouSureToExportAllOrderDataItems": "是否确认导出所有订单数据项?", "onlyOnePieceOfDataCanBeCopied": "只能复制一条数据", "addressOfThePurchaser": "采购方地址", "batchCreateDeliveryDetails": "批量创建送货明细", "pleaseCreateDeliveryNoteDetailsFirst": "请先创建送货单明细", "reEdit": "重新编辑", "remainingOrdersCanBeDelivered": "订单剩余可交", "pendingSubmissionStatus": "待答交状态 ", "orderHeader": "订单抬头", "shippingOrderHeader": "送货单抬头", "deleteAnswer": "删除答交", "theFactoryAndDeliveryNoteAssociatedWithTheImportedOrderDoNotMatch": "导入的订单关联的工厂和送货单不匹配", "theDateOfIssuance": "发货日期", "pdf": "PDF", "supplierInformationCannotBeEmpty": "供应商信息不能为空", "onlySupported": "只支持", "dateOfReceipt": "收货日期", "totalAmountNotTaxed": "未税总金额", "MinimumOrderQuantity": "最小订单数量", "orderNoMaterialCodeSupplierName": "订单号、物料编码、供应商名称", "reportForm": "报表", "youAreGoingToUrgeTheSupplierToSubmitTheDeliveryPlanPleaseConfirm": "您将要跟催供应商提交答交计划，请确认。", "orderType": "订单类型", "onlyAnsweringBanksWithPendingOrReturningStatusCanBeDeletedPleaseConfirmAgain": "仅能删除待答交、答交退回状态的答交行，请重新确认", "returnQuantity": "退货数量", "orderId": "订单id", "dataMark": "数据标记", "whole": "全部", "conclusionOfExternalInspection": "外检结论", "orderNoMaterialCodeMaterialDescription": "订单号、物料编码、物料描述", "theReportCannotBeEmpty": "报表不能为空", "latestRequiredArrivalDate": "最新要求到货日期", "number": "编号", "answerCompletionStatus": "答交完成状态", "allOthers": "其余全部", "supplierNamecode": "供应商名称/编码", "deliveryAddress": "送货地址", "requiredArrivalDate": "要求到货日期", "pleaseFillIn": "请填写", "supplier": "供货方", "beforeConfirmingTheDeliveryDate": "确认交货日期前", "newSchedule": "新建计划", "orderAddition": "订单新增", "theOrderWasNotSuccessfully": "该订单未成功生成pdf，请联系管理员", "orderLineDoesNotExist": "订单行不存在", "theRequiredArrivalQuantityOfThePlanLineUnderTheOrderLineIsNotEqualToTheQuantityOfTheOrderLine": "订单行下计划行的要求到货数量与订单行数量不等", "totalAmountIncludingTax": "含税总金额", "planLineNotAllowedToBeDeleted": "计划行不允许删除", "theDateValueMustBeGreaterThanTheCurrentDay": "日期值必须大于当天", "printDate": "打印日期", "buyerCompany": "采购方公司", "totalAccessoriesShallNotExceedmbUpToFiles": "总附件不得超过20MB；文件最多5个", "pleaseSelectAShippingAddress": "请选择收货地址", "estimatedDeliveryDate": "确认交货日期", "orderImport": "订单导入", "answerBankOfCommunicationsDoesNotAllowDeletion": "答交行不允许删除", "answeringxls": "答交行.xlsx", "unitPriceWithoutTax": "单价未税", "deliveryAddressIsRequired": "送货地址必填", "overdueDeliveryAsRequired": "按要求交付逾期", "allOrderLines": "全部订单行", "theCurrentUserDoesNotHavePermissionToModifyTheSubmissionConfiguration": "当前用户无权限修改答交配置", "theNumberOfUploadsMustBeLessThan": "上传数量需小于0条", "atLeastOnePlanLineShallBeReservedForTheOrderLine": "订单行最少保留一条计划行", "answerDifference": "答交差异", "pleaseEnterTheDeliveryAddress": "请输入送货地址", "unitPriceExcludingTaxincludingPriceUnit": "单价未税（含价格单位)", "postingQuantity": "过账数量", "theCurrentDeliveryNoteStatusCannotBeDeleted": "当前送货单状不允许删除", "deleteTags": "删除标记", "deliveryDriversTelephone": "送货司机电话", "youAreAboutToRejectTitle": "您将要拒绝供应商回复的到货日期，并要求供应商重新答复，请确认。 ", "orderAnswerStatusError": "答交状态不支持操作", "addOrderfactoryAndAddressRelationshipConfiguration": "添加订单/工厂与地址关系配置", "purchaseRemarks": "采购备注", "areYouSureToDeleteCompanyTableInformationNo": "是否确认删除公司表信息编号为", "downloadList": "下载列表", "orderNumberDoesNotExist": "订单号不存在", "planLineId": "计划行id", "estimatedDeliveryQuantity": "确认交货数量", "pleaseEnterTheDeliveryQuantity": "请输入送货数量", "pleaseSelectVersion": "请选择版本", "receivingOrganization": "收货组织", "youAreAboutToAcceptTheCheckedOrderDataPleaseConfirm": "您将要接受勾选的订单数据，请确认。", "copy": "复制", "downloadStampedPdf": "下载盖章PDF", "remainingDeliverableQuantityOfOrder": "订单剩余可交送货数量", "uploadSignatureFile": "上传回签文件", "pleaseSelectACurrency": "请选择币种", "orderLineNo": "订单行号", "deliveryNoteNoAndSupplierAbbreviation": "送货单号、供应商简称", "confirmReceipt": "确认收货", "releaseLeadTime": "发布交期", "unitPriceIncludingTaxincludingPriceUnit": "单价含税（含价格单位)", "deleteSchedule": "删除计划", "contactNumberOfThePurchaser": "采购方联系电话", "theRowDataStatusMsg": "行数据状态不是待答交，系统跳过执行", "moveType": "移动类型", "storageConditions": "储存条件", "orderSigningStatus": "订单签收状态", "theDeliveryNoteQuantityIsGreaterThanTheRemainingDeliverableQuantityOfTheOrder": "送货单数量大于订单剩余可交", "receiptImport": "收货导入", "confirmationOfPlannedDeliveryTime": "计划行交期确认", "addRules": "新增规则", "shippingAddress": "发货地址", "numberOfOrderLines": "订单行数", "addCompanyTableInformation": "添加公司表信息", "shipmentStatus": "发货状态", "theOrderIsInARefusalToSignForAndCannotBeUploaded": "订单处于拒绝签收或已回签状态禁止上传", "procurementStandardTermsAndConditions": "采购标准条款", "publishPlan": "发布计划", "theOrderLineIsCurrentlyInUsePleaseTryAgainLater": "该订单行正在使用，请稍后尝试！", "excelFileNotAllowEmpty": "excel不允许为空", "dataStatusErrorOperationNotAllowed": "数据状态有误，不允许操作", "timeType": "时间类型", "pleaseSelectTheOrderLineToBeShipped": "请选择要发货的订单行", "selectDate": "选择日期", "timeRangeOfAutomaticAcceptanceOfDeliveryDate": "当前用户自动接受交期的时间范围", "historicalVersion": "历史版本", "supportSearchingSupplierName": "支持搜索供应商名称", "download": "下载", "pleaseSelectAReport": "请选择报表", "purchasingContactPerson": "采购方联系人", "batchCreateDeliveryDetailsImportTemplatexls": "批量创建送货明细导入模板.xlsx", "quantityInDelivery": "送货中数量", "batchSplitPlan": "批量拆计划", "timeFrameForOntime": "订单行，计划行，答交行准时交付的时间范围", "theSelectedDataStateDoesNotSupportOperationOnlySupportPendingStatusDataOperations": "无法接受交期，交期确认状态不是【待接受】！", "pleaseEnterTheDepositBank": "请输入开户银行", "invoiceNo": "发票号", "suppliersApplyingThisRule": "应用此规则的供应商", "whetherToEnableAutomaticAcceptanceOfDeliveryDate": "当前用户是否开启自动接受交期", "day": "天", "batchAnswerOnlySupportWaitingAnswer": "批量答交仅支持待答交状态的答交行", "theCurrentDeliveryNoteCannotBeVoided": "当前送货单不允许作废", "updatedBy": "更新人", "pleaseFillInTheEstimatedDeliveryDate": "请填写预计交货日期", "format": "格式", "companySeal": "公司印章", "orderVersion": "订单版本", "arrivalDate": "到货日期", "deliveryQuantityCannotBeEmptyOrMustBeGreaterThan": "送货数量不能为空或者必须大于0", "receivingStatus": "收货状态", "youAreAboutToDeleteTheCurrentOrderDataPleaseConfirm": "您将要删除当前订单数据，请确认。", "financialStatus": "对账状态", "pleaseSaveTheDeliveryNoteFirst": "请先保存送货单", "frameworkContractNumber": "框架合同号", "orderLineId": "订单行id", "currencyCannotBeBlank": "币种不能为空", "theSupplierAssociatedWithTheImportedOrderDoesNotMatchTheDeliveryNote": "导入的订单关联的供应商和送货单不匹配", "areYouSureToDownloadTheOrderPdf": "是否确认下载订单pdf?", "pleaseFillInTheRequiredInformation": "请填写必填信息", "addByOrder": "按订单添加", "dateOfManufacture": "生产日期", "pleaseSelectAPieceOfData": "请选择一条数据", "onlyOrderDataInPendingStatusIsSupported": "仅支持跟催待答交和答交退回状态的订单数据", "deliveryMethod": "送货方式", "overdueDeliveryAsPromised": "按承诺交付逾期", "orderStatus": "订单状态", "pleaseEnterTheInvoiceAddress": "请输入发票地址", "areYouSureYouWantToVoidThisDeliveryNoteThisOperationCannotBeRecovered": "确定要作废此送货单吗？此操作不可恢复！", "theOrderLineShallReserveAtLeastOneReplyBank": "订单行最少保留一条答交行", "answeringBankNo": "答交行号", "bankOfDeposit": "开户银行", "supplierContactNumber": "供应商联系电话", "onlyDataInPendingAndReturnedStatusCanBeOperated": "仅待答交和答交退回状态的数据才能操作", "deliveryNoteDetailsDoNotExist": "送货单明细不存在", "deliveryTimeCannotBeTheSame": "交期不能相同", "arrivalQuantity": "收货数量", "theCurrentStatusDoesNotSupportEditing": "当前状态不支持编辑", "planLineImport": "计划行导入", "orderRemarks": "订单备注", "modifyCompanyTableInformation": "修改公司表信息", "determine": "确定", "totalDetected": "检测到总计", "theImportedOrderSupplierIsDifferentFromTheOriginalOrderSupplier": "此次导入的订单供应商与原订单供应商不相同", "concentration": "浓度", "pleaseAddDirectlyOnTheLatestBlankLine": "请直接在最新的空行上新增", "timeFrameForOntimeDelivery": "交付准时的时间范围", "batchReleaseLeadTime": "批量发布交期", "registrationCertificateNo": "注册证号", "amountNotTaxed": "未税金额", "areYouSureToDeleteTheRelationshipBetweenCurrencyAndOrderReportNo": "是否确认删除币种与订单报表关系编号为", "answerAndUpdateDate": "答交更新日期", "invoiceAddressIsRequired": "发票地址必填", "thePlannedQuantityAndDateUnderTheSameOrderLineCannotBeEmptyPleaseConfirmAgain": "同一订单行下的计划行数量、日期不能为空，请重新确认", "deliveryCompleted": "交货已完成", "deliverOnTimeAsPromised": "按承诺交付准时", "theDeliveryDateCannotBeLessThanTheCurrentTime": "交期不能小于当前时间", "theTotalQuantityOfPlanLinesAndOrderQuantityAreNotEqualOperationNotAllowed": "计划行总数量和订单数量不相等，不允许操作", "waybillNo": "运单号"}}, "msg": ""}