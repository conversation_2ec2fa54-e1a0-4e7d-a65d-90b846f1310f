/**
* I think element-ui's default theme color is too light for long-term use.
* So I modified the default color and you can modify it to your liking.
**/

/* theme color */
$--color-primary: #4996b8;
$--color-success: #13ce66;
$--color-warning: #ffba00;
$--color-danger: #ff4949;
// $--color-info: #1E1E1E;

$--button-font-weight: 400;

// $--color-text-regular: #1f2d3d;
$--font-size-small: 14px !default;
$--button-small-font-size: 14px !default;
//$--input-mini-font-size: 14px !default;
$--button-mini-font-size: 14px !default;
$--button-small-font-size: 14px !default;
$--border-color-light: #dfe4ed;
$--border-color-lighter: #e6ebf5;

$--table-border: 1px solid #dfe6ec;

/* icon font path, required */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

// 修改样式变量
$vxe-font-size: 14px;

$vxe-font-color: #666;
$vxe-primary-color: #4996b8;
$vxe-table-font-color: $vxe-font-color;
$vxe-table-border-color: #e8eaec;
$vxe-table-border-radius: 4px;
$vxe-disabled-color: #606266;
$vxe-input-disabled-background-color: #F5F7FA !default;

// ...

@import 'vxe-table/styles/index.scss';

@import "~element-ui/packages/theme-chalk/src/index";

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  theme: $--color-primary;
}
