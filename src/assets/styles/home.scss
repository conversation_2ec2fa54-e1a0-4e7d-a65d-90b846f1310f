::v-deep .el-card__body{
  padding:10px;
}
.fastLogin{
  width: 50px;
  height: 50px;
  padding: 10px;
  border-radius: 50%;
  position: relative;
  box-shadow: 7px 14px 21px -8px #888888
}
.fastLogin::before{
  content: "";
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  border-radius: 50%;
  box-shadow: inset 4px 6px 9px -6px #cccccc
}
.statisticsItem{
  width: 40px;
  height: 40px;
}
.fastLoginItem{
  width: 32px;
  height: 32px;

}
.home{
  padding:15px 25px;
}
.statisticsNum{
  flex: 1 0 16.6%;
  height: 96px;
  color: #ffffff;
  display: flex;
  align-items: center
}
.todoItem{
  cursor: pointer;
  border: 3px solid #f1f1f1;
  border-left:3px solid red;
  height: 88px;
  flex: 0 1 25%;
  display: flex;
  justify-content: center;
  padding: 15px;
  flex-direction: column;
}
.noticeTable{
  ::v-deep thead{
    display: none;
  }

}
.market{
  ::v-deep .el-table::before {
    height: 0;
  }

  ::v-deep .el-table__header th {
    background: #EBEEF5;
    padding: 3px 0;
    color: #606266;
  }

  ::v-deep .el-table__row td {
    border: none;
  }
}
.gray-bar{
  border-radius:0;
  background: #e1f8e9
}
.systemTitle{
  font-size: 16px;
  color: #4996b8;
  margin-left: 20px;
  font-weight: bold;
}
::v-deep .el-card__body{
  padding:10px;
}
.fastLogin{
  width: 50px;
  height: 50px;
  padding: 10px;
  border-radius: 50%;
  position: relative;
  box-shadow: 7px 14px 21px -8px #888888
}
.fastLogin::before{
  content: "";
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  border-radius: 50%;
  box-shadow: inset 4px 6px 9px -6px #cccccc
}
.statisticsItem{
  width: 40px;
  height: 40px;
}
.fastLoginItem{
  width: 32px;
  height: 32px;
}
.home{
  padding:15px 25px;
}
.statisticsNum{
  flex: 1 0 16.6%;
  height: 96px;
  color: #ffffff;
  display: flex;
  align-items: center
}
.todoItem{
  cursor: pointer;
  border: 3px solid #f1f1f1;
  border-left:3px solid red;
  height: 88px;
  flex: 0 1 25%;
  display: flex;
  justify-content: center;
  padding: 15px;
  flex-direction: column;
}
.noticeTable{
  ::v-deep thead{
    display: none;
  }

}
.market{
  ::v-deep .el-table::before {
    height: 0;
  }

  ::v-deep .el-table__header th {
    background: #EBEEF5;
    padding: 3px 0;
    color: #606266;
  }

  ::v-deep .el-table__row td {
    border: none;
  }
}
.systemTitle{
  font-size: 16px;
  color: #4996b8;
  margin-left: 20px;
  font-weight: bold;
}
::v-deep .el-table::before{
  height: 0;
}
