/*
  调用：
    this.$exportExcel.exportByGirdOptionColumns(this.girdOption.columns,this.list,'下载','页面1')
  全局挂载：
    import { exportByGirdOptionColumns,exportByElTableRef } from '@/config/excelExport';
    Vue.prototype.$exportExcel = {exportByGirdOptionColumns,exportByElTableRef}
 */

/**
 * vxe-grid的对应定制化导出
 * 暂不支持slot
 * @param columns vxe-grid 的 girdOption 中 columns 配置
 * @param dataList 导出的数据列表
 * @param fileName 文件名 默认会以 下载 作为文件名使用 文件名可以直接传名字，无需带 .xlsx
 * @param sheetName 页签名 默认 Sheet1
 */
export function exportByGirdOptionColumns(
  columns,
  dataList
) {
  // 用于导出的组装list
  let dataExportList = []
  // 用于设置列宽的相关数据
  // 开始组装数据
  if (dataList) {
    for (let dataListElement of dataList) {
      let dataExportRow = {}
      for (let column of columns) {
        // 跳过选择框，空白标题列，操作列，隐藏列
        if (!column.visible || column.title === '' || column.type === 'radio' || column.field === 'operate') {
          continue
        }
        // 转换导出字典
        if (column.excelFormatter) {
          dataExportRow[column.title] = column.excelFormatter({
            value: dataListElement[column.field],
            cellValue: dataListElement[column.field]
          })||''
          // 对于list数据做逗号拼接
          if (isArray(dataExportRow[column.title])) {
            dataExportRow[column.title] = dataExportRow[column.title].join(',')||''
          }
        } else if (column.formatter) {
          dataExportRow[column.title] = column.formatter({
            value: dataListElement[column.field],
            cellValue: dataListElement[column.field]
          })||''
          // 对于list数据做逗号拼接
          if (isArray(dataExportRow[column.title])) {
            dataExportRow[column.title] = dataExportRow[column.title].join(',')||''
          }
        } else {
          dataExportRow[column.title] = dataListElement[column.field]||''
        }
      }
      dataExportList.push(dataExportRow)
    }
  } else {
    let dataExportRow = {}
    for (let column of columns) {
      // 跳过选择框，空白标题列，操作列，隐藏列
      if (!column.visible || column.title === '' || column.type === 'radio' || column.field === 'operate') {
        continue
      }
      dataExportRow[column.title] = ''
    }
    dataExportList.push(dataExportRow)
  }
  return dataExportList
}

/**
 * el-table 导出
 * 暂不支持插槽形式
 * @param elTableRef el-table的ref引用
 * @param dataList 导出的数据列表
 * @param fileName 文件名 默认会以 下载 作为文件名使用 文件名可以直接传名字，无需带 .xlsx
 * @param sheetName 页签名 默认 Sheet1
 */
export function exportByElTableRef(
  elTableRef,
  dataList
) {
  // 用于导出的组装list
  let dataExportList = []
  // 用于设置列宽的相关数据
  // 开始组装数据
  if (dataList) {
    for (let dataListElement of dataList) {
      let dataExportRow = {}
      const tableColumns = elTableRef.columns
      for (let column of tableColumns) {
        if (!column.label || column.label === elTableRef.$t('common.operate')) continue
        dataExportRow[column.label] = dataListElement[column.property]||''
      }
      dataExportList.push(dataExportRow)
    }
  } else {
    let dataExportRow = {}
    const tableColumns = elTableRef.columns
    for (let column of tableColumns) {
      if (column.label === elTableRef.$t('common.operate')) continue
      // 设置列宽
      dataExportRow[column.label] = ''
    }
    dataExportList.push(dataExportRow)
  }
  return dataExportList
}

function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}

