export default {
  route: {
    dashboard: 'Dashboard',
    documentation: 'Documentation',
    guide: 'Guide',
    permission: 'Permission',
    pagePermission: 'Page Permission',
    rolePermission: 'Role Permission',
    directivePermission: 'Directive Permission',
    icons: 'Icons',
    components: 'Components',
    tinymce: 'Tinymce',
    markdown: 'Markdown',
    jsonEditor: 'JSON Editor',
    dndList: 'Dnd List',
    splitPane: 'SplitPane',
    avatarUpload: 'Avatar Upload',
    dropzone: 'Dropzone',
    sticky: 'Sticky',
    countTo: 'Count To',
    componentMixin: 'Mixin',
    backToTop: 'Back To Top',
    dragDialog: 'Drag Dialog',
    dragSelect: 'Drag Select',
    dragKanban: 'Drag Kanban',
    charts: 'Charts',
    keyboardChart: 'Keyboard Chart',
    lineChart: 'Line Chart',
    mixChart: 'Mix Chart',
    example: 'Example',
    nested: 'Nested Routes',
    menu1: 'Menu 1',
    'menu1-1': 'Menu 1-1',
    'menu1-2': 'Menu 1-2',
    'menu1-2-1': 'Menu 1-2-1',
    'menu1-2-2': 'Menu 1-2-2',
    'menu1-3': 'Menu 1-3',
    menu2: 'Menu 2',
    Table: 'Table',
    dynamicTable: 'Dynamic Table',
    dragTable: 'Drag Table',
    inlineEditTable: 'Inline Edit',
    complexTable: 'Complex Table',
    tab: 'Tab',
    form: 'Form',
    createArticle: 'Create Article',
    editArticle: 'Edit Article',
    articleList: 'Article List',
    errorPages: 'Error Pages',
    page401: '401',
    page404: '404',
    errorLog: 'Error Log',
    excel: 'Excel',
    exportExcel: 'Export Excel',
    selectExcel: 'Export Selected',
    mergeHeader: 'Merge Header',
    uploadExcel: 'Upload Excel',
    zip: 'Zip',
    pdf: 'PDF',
    exportZip: 'Export Zip',
    theme: 'Theme',
    clipboardDemo: 'Clipboard',
    i18n: 'I18n',
    externalLink: 'External Link',
    profile: 'Profile'
  },
  navbar: {
    dashboard: 'Dashboard',
    github: 'Github',
    logOut: 'Log Out',
    profile: 'Profile',
    theme: 'Theme',
    size: 'Global Size'
  },
  login: {
    title: 'Please Sign in',
    logIn: 'Login',
    username: 'Username',
    password: 'Password',
    checkpass: ' Remember password',
    forgetpass: 'Forget password?',
    register: 'Register',
    loginMessage: 'Login successfully',
    verifysuccessfully: 'Verify successfully',
    verifyerror: 'Verify error',
    any: 'any',
    verify: 'Swipe right to complete validation',
    thirdparty: 'Or connect with',
    thirdpartyTips: 'Can not be simulated on local, so please combine you own business simulation! ! !',
    msg: 'Loosen the validation',
    msgSuccess: 'verify successfully',
    category: 'Categories',
    supplier: 'Strictly selected suppliers',
    purchase: 'Procurement',
    message1: 'Purchasing Life Made Easy',
    message2: ''
  },
  user: {
    chooseCompany: 'Please select company',
    enterName: 'Please enter your name',
    enterlastName: 'Please enter your lastName',
    enterfirstName: 'Please enter your firstName',
    enterstatus: 'Please select user status',
    enterPhone: 'Please enter your mobile phone number',
    enterMail: 'Please enter email',
    MailRule: 'Please enter the correct email address',
    add: 'add',
    search: 'search',
    batchOperation: 'batch  Operation',
    batchRemove: 'batch Remove',
    batchDisabled: 'batch disabled',
    familyName: 'lastName',
    firstName: 'firstName',
    Phone: 'Phone',
    email: 'email',
    status: 'status',
    disable: 'disable',
    use: 'use',
    createDate: 'createDate',
    lastLoginTime: 'lastLoginTime',
    operator: 'operator',
    edit: 'edit',
    delete: 'delete',
    resetPass: 'Reset Password',
    desiecharacters: ' Designing characters',
    addUser: ' Add User',
    editUser: 'Edit User',
    confirm: 'Confirm',
    cancel: 'Cancel',
    name: 'Name',
    filter: ' Filter',
    filterCondition: 'Please enter a filter condition',
    gobalBuyer: 'gobal buyer',
    roleOperation: 'Role of operation',
    roleAdd: 'Add Roles',
    roleName: 'Role name',
    message: 'operate successfully!',
    editmessage: 'Edit successfully!',
    deletemessage: 'Delete successfully!',
    addmessage: 'Add successfully!',
    selectUser: 'Please select user!',
    deleteUsermessage: 'This action will permanently delete the user. Do you want to continue?',
    hint: 'hint',
    cancelDetele: 'Undeleted',
    desableUsermessage: 'This action will disable/use the user. Do you want to continue?',
    cancelDisable: 'Undisabled!',
    repassMessage: 'This operation resets the user password, do you want to continue?',
    group: 'group',
    enterGroup: 'Please select a user group'

  },
  documentation: {
    documentation: 'Documentation',
    github: 'Github Repository'
  },
  permission: {
    addRole: 'New Role',
    editPermission: 'Edit',
    roles: 'Your roles',
    switchRoles: 'Switch roles',
    tips: 'In some cases, using v-permission will have no effect. For example: Element-UI  el-tab or el-table-column and other scenes that dynamically render dom. You can only do this with v-if.',
    delete: 'Delete',
    confirm: 'Confirm',
    cancel: 'Cancel'
  },
  guide: {
    description: 'The guide page is useful for some people who entered the project for the first time. You can briefly introduce the features of the project. Demo is based on ',
    button: 'Show Guide'
  },
  components: {
    documentation: 'Documentation',
    tinymceTips: 'Rich text is a core feature of the management backend, but at the same time it is a place with lots of pits. In the process of selecting rich texts, I also took a lot of detours. The common rich texts on the market have been basically used, and I finally chose Tinymce. See the more detailed rich text comparison and introduction.',
    dropzoneTips: 'Because my business has special needs, and has to upload images to qiniu, so instead of a third party, I chose encapsulate it by myself. It is very simple, you can see the detail code in @/components/Dropzone.',
    stickyTips: 'when the page is scrolled to the preset position will be sticky on the top.',
    backToTopTips1: 'When the page is scrolled to the specified position, the Back to Top button appears in the lower right corner',
    backToTopTips2: 'You can customize the style of the button, show / hide, height of appearance, height of the return. If you need a text prompt, you can use element-ui el-tooltip elements externally',
    imageUploadTips: 'Since I was using only the vue@1 version, and it is not compatible with mockjs at the moment, I modified it myself, and if you are going to use it, it is better to use official version.'
  },
  table: {
    dynamicTips1: 'Fixed header, sorted by header order',
    dynamicTips2: 'Not fixed header, sorted by click order',
    dragTips1: 'The default order',
    dragTips2: 'The after dragging order',
    title: 'Title',
    importance: 'Imp',
    type: 'Type',
    remark: 'Remark',
    search: 'Search',
    add: 'Add',
    export: 'Export',
    reviewer: 'reviewer',
    id: 'ID',
    date: 'Date',
    author: 'Author',
    readings: 'Readings',
    status: 'Status',
    actions: 'Actions',
    edit: 'Edit',
    publish: 'Publish',
    draft: 'Draft',
    delete: 'Delete',
    cancel: 'Cancel',
    confirm: 'Confirm'
  },
  example: {
    warning: 'Creating and editing pages cannot be cached by keep-alive because keep-alive include does not currently support caching based on routes, so it is currently cached based on component name. If you want to achieve a similar caching effect, you can use a browser caching scheme such as localStorage. Or do not use keep-alive include to cache all pages directly. See details'
  },
  errorLog: {
    tips: 'Please click the bug icon in the upper right corner',
    description: 'Now the management system are basically the form of the spa, it enhances the user experience, but it also increases the possibility of page problems, a small negligence may lead to the entire page deadlock. Fortunately Vue provides a way to catch handling exceptions, where you can handle errors or report exceptions.',
    documentation: 'Document introduction'
  },
  excel: {
    export: 'Export',
    selectedExport: 'Export Selected Items',
    placeholder: 'Please enter the file name (default excel-list)'
  },
  zip: {
    export: 'Export',
    placeholder: 'Please enter the file name (default file)'
  },
  pdf: {
    tips: 'Here we use window.print() to implement the feature of downloading PDF.'
  },
  theme: {
    change: 'Change Theme',
    documentation: 'Theme documentation',
    tips: 'Tips: It is different from the theme-pick on the navbar is two different skinning methods, each with different application scenarios. Refer to the documentation for details.'
  },
  tagsView: {
    refresh: 'Refresh',
    close: 'Close',
    closeOthers: 'Close Others',
    closeAll: 'Close All'
  },
  settings: {
    title: 'Page style setting',
    theme: 'Theme Color',
    tagsView: 'Open Tags-View',
    fixedHeader: 'Fixed Header',
    sidebarLogo: 'Sidebar Logo'
  },
  supplierManage: {
    basicInfo: 'Basic',
    businessInfo: 'Business Info',
    productAndTemp: 'Product/Made',
    managementSystem: 'ManageSys',
    businessQualification: 'Qualification',
    companyInfo: 'Company Info',
    affiliates: 'Affiliates',
    staff: 'Staff',
    businessInformation: 'Business Information',
    sales: 'Sales',
    majorClient: 'Major Client',
    productsAndCategories: 'Products and Categories',
    qualitySystem: 'Quality System',
    managementSoftware: 'Management Software',
    leanProduction: 'Lean Production',
    environmentalQualification: 'Environmental Qualification',
    selectedCategories: 'Selected Categories'
  },
  register: {
    title: 'Personal information(Register as an administrator)',
    firstName: 'FirstName',
    lastName: 'LastName',
    password: 'Password',
    confirmPassword: 'Re-enter password',
    email: 'Email',
    sendCaptcha: 'Send verification code',
    emailCode: 'EmailCode',
    culture: 'Language',
    mobile: 'Mobile',
    mobileCode: 'MobileCode',
    companyTitle: 'CompanyInfo',
    companyName: 'CompanyName',
    companyType: 'CompanyType',
    supplier: 'Supplier',
    client: 'Client',
    region: 'Region',
    registerBtn: 'Sign up',
    hasRegistered: 'Has registered? Login in',
    inputRule: 'Please input',
    passwordRule: 'The password must include at least 1 uppercase letter, 1 lowercase letter, 1 number, and 1 special character',
    passwordText: 'Please input password again',
    rePasswordText: 'The two passwords do not match'
  },
  roleManage: {
    selectCompany: 'select company',
    rolePlace: 'input role',
    perPlace: 'input permission',
    search: 'search',
    add: 'add',
    batchOperate: 'batchOperate',
    batchDeletion: 'batchDeletion',
    roleName: 'roleName',
    sort: 'sort',
    subscribers: 'subscribers',
    operate: 'operate',
    edit: 'edit',
    delete: 'delete',
    addRole: 'addRole',
    cancel: 'cancel',
    submit: 'submit',
    userInfo: 'userInfo',
    emailFilter: 'emailFilter',
    userName: 'userName',
    email: 'email',
    mobile: 'mobile',
    num: 'num',
    filter: 'filter',
    key: 'input key',
    addPer: 'add permission',
    deletePer: 'delete permission',
    deleteMessage: 'This operation will delete the role, do you want to continue?',
    prompt: 'prompt',
    confirm: 'yes',
    cancelBtn: 'no',
    success: 'successfully operate',
    deleteSuccess: 'successfully deleted',
    roleNameRule: 'please input role name',
    sortRule: 'please input sort',
    sortRule1: 'sort must be a numeric value'
  },
  fm: {
    components: {
      fields: {
        input: 'Input',
        textarea: 'Textarea',
        number: 'Number',
        radio: 'Radio',
        checkbox: 'Checkbox',
        time: 'Time',
        date: 'Date',
        rate: 'Rate',
        color: 'Color',
        select: 'Select',
        switch: 'Switch',
        slider: 'Slider',
        text: 'Text',
        blank: 'Custom',
        fileupload: 'File',
        imgupload: 'Image',
        editor: 'Editor',
        cascader: 'Cascader',
        table: 'Sub-table',
        grid: 'Grid',
        tabs: 'Tabs',
        divider: 'Divider'
      },
      basic: {
        title: 'Basic Component'
      },
      advance: {
        title: 'Advance Component'
      },
      layout: {
        title: 'Layout'
      }
    },
    description: {
      containerEmpty: 'You can drag and drop the item from the left to add components',
      configEmpty: 'Please add a component',
      tableEmpty: 'You can drag and drop the item from the left to add components',
      uploadJsonInfo: 'There is the format of JSON below，you can overwrite it with you own JSON code'
    },
    message: {
      copySuccess: 'Copy Successed',
      validError: 'Form data validation failed'
    },
    actions: {
      import: 'Import JSON',
      clear: 'Clear',
      preview: 'Preview',
      json: 'Generate JSON',
      code: 'Generate Code',
      getData: 'Get Data',
      reset: 'Reset',
      copyData: 'Copy Data',
      cancel: 'Cancel',
      confirm: 'Confirm',
      addOption: 'Add Option',
      addColumn: 'Add Column',
      addTab: 'Add Tab',
      upload: 'Upload',
      add: 'Add'
    },
    config: {
      form: {
        title: 'Form Attribute',
        labelPosition: {
          title: 'Label Position',
          left: 'Left',
          right: 'Right',
          top: 'Top'
        },
        labelWidth: 'Label Width',
        size: 'Size',
        customClass: 'Custom Class'
      },
      widget: {
        title: 'Component Attribute',
        model: 'ID',
        name: 'Name',
        width: 'Width',
        height: 'Height',
        size: 'Size',
        labelWidth: 'Label Width',
        custom: 'Custom',
        placeholder: 'Placeholder',
        layout: 'Layout',
        block: 'Block',
        inline: 'Inline',
        contentPosition: 'Content Position',
        left: 'Left',
        right: 'Right',
        center: 'Center',
        showInput: 'Display Input Box',
        min: 'Minimum',
        max: 'Maximum',
        step: 'Step',
        multiple: 'Multiple',
        filterable: 'Searchable',
        allowHalf: 'Allow Half',
        showAlpha: 'Support transparency options',
        showLabel: 'Show lable',
        option: 'Option',
        staticData: 'Static Data',
        remoteData: 'Remote Date',
        remoteFunc: 'Remote Function',
        value: 'Value',
        label: 'Label',
        childrenOption: 'Sub-Option',
        defaultValue: 'Default Value',
        showType: 'Display type',
        isRange: 'Range Time',
        isTimestamp: 'Get time stamp',
        startPlaceholder: 'Placeholder of start time',
        endPlaceholder: 'Placeholder of end time',
        format: 'Format',
        limit: 'Maximum Upload Count',
        isQiniu: 'Upload with Qiniu Cloud',
        tokenFunc: 'A funchtin to get Qiniu Uptoken',
        imageAction: 'Picture upload address',
        tip: 'Text Prompt',
        action: 'Upload Address',
        defaultType: 'Data Type',
        string: 'String',
        object: 'Object',
        array: 'Array',
        number: 'Number',
        boolean: 'Boolean',
        integer: 'Integer',
        float: 'Float',
        url: 'URL',
        email: 'E-mail',
        hex: 'Hexadecimal',
        gutter: 'Grid Spacing',
        columnOption: 'Column Configuration',
        span: 'Grid spans',
        justify: 'Horizontal Arrangement',
        justifyStart: 'Start',
        justifyEnd: 'End',
        justifyCenter: 'Center',
        justifySpaceAround: 'Space Around',
        justifySpaceBetween: 'Space Between',
        align: 'Vertical Arrangement',
        alignTop: 'Top',
        alignMiddle: 'Middle',
        alignBottom: 'Bottom',
        type: 'Type',
        default: 'Default',
        card: 'Tabs',
        borderCard: 'Border-Card',
        tabPosition: 'Tab Position',
        top: 'Top',
        bottom: 'Bottom',
        tabOption: 'Label Configuration',
        tabName: 'Tab Name',
        customClass: 'Custom Class',
        attribute: 'Attribute Action',
        dataBind: 'Data Binding',
        hidden: 'Hidden',
        readonly: 'Read Only',
        disabled: 'Disabled',
        editable: 'Text box is editable',
        clearable: 'Display Clear Button',
        arrowControl: 'Use the arrow for time selection',
        isDelete: 'Deletable',
        isEdit: 'Editable',
        showPassword: 'Display Password',
        validate: 'Validation',
        required: 'Required',
        patternPlaceholder: 'Fill in the regular expressions',
        newOption: 'New Option',
        tab: 'Tab',
        validatorRequired: 'Required',
        validatorType: 'Invaild format',
        validatorPattern: 'Unmatched pattern'
      }
    },
    upload: {
      preview: 'preview',
      edit: 'replace',
      delete: 'delete'
    }
  },
  dictionary: {
    add: 'add dictionary',
    more: 'More menu',
    edit: 'edit dictionary',
    del: 'delete dictionary',
    ref: 'refresh dictionary',
    selected: 'The currently selected',
    deselect: 'deselect',
    keyword: 'Please enter query keywords',
    dataName: 'Please enter a data name',
    status: 'Please select the data status',
    addData: 'add Data',
    name: 'name',
    values: 'values',
    remark: 'remark',
    state: 'state',
    disabled: 'disabled',
    use: 'use',
    time: 'createTime',
    dictionaryName: 'Name',
    enterDictionaryName: 'Please enter the  name',
    language: 'Language',
    selectLanguage: 'Please select language',
    parentNode: 'ParentNode',
    selectParentNode: 'Please select the parent node',
    type: 'Type',
    enterType: 'Please enter the dictionary type',
    code: 'Code',
    enterCode: 'Please enter the dictionary code',
    enterRemark: 'Please enter dictionary remarks',
    sort: 'Sort',
    date: 'Name',
    enterValues: 'Please enter data values',
    dataType: 'dataType',
    enterDataType: 'Please enter the data type',
    enterDataRemark: 'Please enter data remarks',
    isUse: 'isUse',
    sameData: 'The data name is the same, please re-enter',
    sameLanguage: 'Language selection is the same, please re - language',
    sameDictionaryName: 'The dictionary name is the same, please re-enter',
    editData: 'Edit the data',
    delDictionaryData: 'This operation permanently deletes dictionary data. Do you want to continue?',
    selectData: 'Please select data',
    selectNode: 'Please select the node',
    delDictionary: 'This operation deletes the dictionary permanently. Do you want to continue?',
    deleteSuccess: 'Delete the success',
    NotDeleteAll: 'Cannot delete all data names',
    NotDeleteAllDictionary: 'You cannot delete all dictionary names',
    selectLevel: 'Dictionaries can only add 2 levels at most'

  },
  thank: {}

}
