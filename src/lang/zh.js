export default {
  route: {
    dashboard: '首页',
    documentation: '文档',
    guide: '引导页',
    permission: '权限测试页',
    rolePermission: '角色权限',
    pagePermission: '页面权限',
    directivePermission: '指令权限',
    icons: '图标',
    components: '组件',
    tinymce: '富文本编辑器',
    markdown: 'Markdown',
    jsonEditor: 'JSON 编辑器',
    dndList: '列表拖拽',
    splitPane: 'Splitpane',
    avatarUpload: '头像上传',
    dropzone: 'Dropzone',
    sticky: 'Sticky',
    countTo: 'Count To',
    componentMixin: '小组件',
    backToTop: '返回顶部',
    dragDialog: '拖拽 Dialog',
    dragSelect: '拖拽 Select',
    dragKanban: '可拖拽看板',
    charts: '图表',
    keyboardChart: '键盘图表',
    lineChart: '折线图',
    mixChart: '混合图表',
    example: '综合实例',
    nested: '路由嵌套',
    menu1: '菜单1',
    'menu1-1': '菜单 1-1',
    'menu1-2': '菜单 1-2',
    'menu1-2-1': '菜单 1-2-1',
    'menu1-2-2': '菜单 1-2-2',
    'menu1-3': '菜单 1-3',
    menu2: '菜单 2',
    Table: 'Table',
    dynamicTable: '动态 Table',
    dragTable: '拖拽 Table',
    inlineEditTable: 'Table 内编辑',
    complexTable: '综合 Table',
    tab: 'Tab',
    form: '表单',
    createArticle: '创建文章',
    editArticle: '编辑文章',
    articleList: '文章列表',
    errorPages: '错误页面',
    page401: '401',
    page404: '404',
    errorLog: '错误日志',
    excel: 'Excel',
    exportExcel: '导出 Excel',
    selectExcel: '导出 已选择项',
    mergeHeader: '导出 多级表头',
    uploadExcel: '上传 Excel',
    zip: 'Zip',
    pdf: 'PDF',
    exportZip: 'Export Zip',
    theme: '换肤',
    clipboardDemo: 'Clipboard',
    i18n: '国际化',
    externalLink: '外链',
    profile: '个人中心',
    systemSet: '系统设置',
    userManage: '用户管理',
    roleManage: '角色管理'
  },
  navbar: {
    dashboard: '首页',
    github: '项目地址',
    logOut: '退出登录',
    profile: '个人中心',
    theme: '换肤',
    size: '布局大小'
  },
  login: {
    title: '请登录',
    logIn: '登录',
    username: '邮箱或手机号',
    password: '密码',
    checkpass: '记住密码',
    forgetpass: '忘记密码？',
    register: '注册',
    loginMessage: '登陆成功',
    verifysuccessfully: '验证成功',
    verifyerror: '验证失败',
    verify: '向右滑动解锁',
    any: '随便填',
    thirdparty: '第三方登录',
    thirdpartyTips: '本地不能模拟，请结合自己业务进行模拟！！！',
    msg: '松开验证',
    msgSuccess: '验证成功',
    category: '品类',
    supplier: '严选供应商',
    purchase: '采购',
    message2: 'Purchasing Life Made Easy',
    message1: '丰致易采 采购人生也从容'
  },
  supplierManage: {
    basicInfo: '基本信息',
    businessInfo: '商业信息',
    productAndTemp: '产品/制成',
    managementSystem: '管理系统',
    businessQualification: '经营资质',
    companyInfo: '公司信息',
    affiliates: '关联公司',
    staff: '人员',
    businessInformation: '商业信息',
    sales: '销售额',
    majorClient: '主要客户',
    productsAndCategories: '产品和制成',
    qualitySystem: '质量体系',
    managementSoftware: '管理系统',
    leanProduction: '精益生产',
    environmentalQualification: '环境资质',
    selectedCategories: '参加严选的品类'
  },
  user: {
    chooseCompany: '请选择公司',
    enterName: '请输入姓名',
    enterlastName: '请输入用户姓氏',
    enterfirstName: '请输入用户名',
    enterstatus: '请选择用户状态',
    enterPhone: '请输入手机号码',
    enterMail: '请输入电子邮件',
    MailRule: '请输入正确的邮箱地址',
    add: '添加',
    search: '搜索',
    batchOperation: '批量操作',
    batchRemove: '批量删除',
    batchDisabled: '批量禁用',
    familyName: '用户姓氏',
    firstName: '用户名字',
    Phone: '手机号码',
    email: '电子邮箱',
    status: '状态',
    disable: '禁用',
    use: '使用',
    createDate: '创建时间',
    lastLoginTime: '上次登录时间',
    operator: '操作',
    edit: '编辑',
    delete: '删除',
    resetPass: '重置密码',
    desiecharacters: '角色设定',
    addUser: '添加用户',
    editUser: '编辑用户',
    cancel: '取 消',
    confirm: '确 定',
    name: '用户姓名',
    filter: '过滤',
    filterCondition: '请输入过滤条件',
    gobalBuyer: '查看权限',
    roleOperation: '角色操作',
    roleAdd: '添加角色',
    roleRemove: '删除角色',
    roleName: '角色名称',
    message: '操作成功!',
    editmessage: '编辑成功！',
    addmessage: '新增成功!',
    deletemessage: '删除成功!',
    selectUser: '请选择用户!',
    deleteUsermessage: '此操作将永久删除该用户, 是否继续?',
    hint: '提示',
    cancelDetele: '已取消删除!',
    desableUsermessage: '此操作将禁用/使用 该用户, 是否继续?',
    cancelDisable: '已取消禁用!',
    repassMessage: '此操作重置该用户密码，, 是否继续?',
    group: '用户组',
    enterGroup: '请选择用户组'
  },
  documentation: {
    documentation: '文档',
    github: 'Github 地址'
  },
  permission: {
    addRole: '新增角色',
    editPermission: '编辑权限',
    roles: '你的权限',
    switchRoles: '切换权限',
    tips: '在某些情况下，不适合使用 v-permission。例如：Element-UI 的 el-tab 或 el-table-column 以及其它动态渲染 dom 的场景。你只能通过手动设置 v-if 来实现。',
    delete: '删除',
    confirm: '确定',
    cancel: '取消'
  },
  guide: {
    description: '引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。本 Demo 是基于',
    button: '打开引导'
  },
  components: {
    documentation: '文档',
    tinymceTips: '富文本是管理后台一个核心的功能，但同时又是一个有很多坑的地方。在选择富文本的过程中我也走了不少的弯路，市面上常见的富文本都基本用过了，最终权衡了一下选择了Tinymce。更详细的富文本比较和介绍见',
    dropzoneTips: '由于我司业务有特殊需求，而且要传七牛 所以没用第三方，选择了自己封装。代码非常的简单，具体代码你可以在这里看到 @/components/Dropzone',
    stickyTips: '当页面滚动到预设的位置会吸附在顶部',
    backToTopTips1: '页面滚动到指定位置会在右下角出现返回顶部按钮',
    backToTopTips2: '可自定义按钮的样式、show/hide、出现的高度、返回的位置 如需文字提示，可在外部使用Element的el-tooltip元素',
    imageUploadTips: '由于我在使用时它只有vue@1版本，而且和mockjs不兼容，所以自己改造了一下，如果大家要使用的话，优先还是使用官方版本。'
  },
  table: {
    dynamicTips1: '固定表头, 按照表头顺序排序',
    dynamicTips2: '不固定表头, 按照点击顺序排序',
    dragTips1: '默认顺序',
    dragTips2: '拖拽后顺序',
    title: '标题',
    importance: '重要性',
    type: '类型',
    remark: '点评',
    search: '搜索',
    add: '添加',
    export: '导出',
    reviewer: '审核人',
    id: '序号',
    date: '时间',
    author: '作者',
    readings: '阅读数',
    status: '状态',
    actions: '操作',
    edit: '编辑',
    publish: '发布',
    draft: '草稿',
    delete: '删除',
    cancel: '取 消',
    confirm: '确 定'
  },
  example: {
    warning: '创建和编辑页面是不能被 keep-alive 缓存的，因为keep-alive 的 include 目前不支持根据路由来缓存，所以目前都是基于 component name 来进行缓存的。如果你想类似的实现缓存效果，可以使用 localStorage 等浏览器缓存方案。或者不要使用 keep-alive 的 include，直接缓存所有页面。详情见'
  },
  errorLog: {
    tips: '请点击右上角bug小图标',
    description: '现在的管理后台基本都是spa的形式了，它增强了用户体验，但同时也会增加页面出问题的可能性，可能一个小小的疏忽就导致整个页面的死锁。好在 Vue 官网提供了一个方法来捕获处理异常，你可以在其中进行错误处理或者异常上报。',
    documentation: '文档介绍'
  },
  excel: {
    export: '导出',
    selectedExport: '导出已选择项',
    placeholder: '请输入文件名(默认excel-list)'
  },
  zip: {
    export: '导出',
    placeholder: '请输入文件名(默认file)'
  },
  pdf: {
    tips: '这里使用   window.print() 来实现下载pdf的功能'
  },
  theme: {
    change: '换肤',
    documentation: '换肤文档',
    tips: 'Tips: 它区别于 navbar 上的 theme-pick, 是两种不同的换肤方法，各自有不同的应用场景，具体请参考文档。'
  },
  tagsView: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有'
  },
  settings: {
    title: '系统布局配置',
    theme: '主题色',
    tagsView: '开启 Tags-View',
    fixedHeader: '固定 Header',
    sidebarLogo: '侧边栏 Logo'
  },
  register: {
    title: '个人信息（成为管理员）',
    firstName: '名',
    lastName: '姓',
    password: '密码',
    confirmPassword: '确认密码',
    email: '邮箱',
    sendCaptcha: '发送验证码',
    emailCode: '邮箱验证码',
    culture: '语言',
    mobile: '手机号',
    mobileCode: '手机验证码',
    companyTitle: '公司信息',
    companyName: '公司名称',
    companyType: '公司类型',
    supplier: '供应商',
    client: '客户',
    region: '地区',
    registerBtn: '注 册',
    hasRegistered: '我已注册，点击登录',
    inputRule: '请输入',
    passwordRule: '密码最少8位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊字符',
    passwordText: '请再次输入密码',
    rePasswordText: '两次输入密码不一致'
  },
  roleManage: {
    selectCompany: '请选择公司',
    rolePlace: '输入角色',
    perPlace: '输入权限',
    search: '搜索',
    add: '添加',
    batchOperate: '批量操作',
    batchDeletion: '批量删除',
    roleName: '角色名称',
    sort: '排序号',
    subscribers: '用户量',
    operate: '操作',
    edit: '编辑',
    delete: '删除',
    addRole: '添加角色',
    cancel: '取 消',
    submit: '确 定',
    userInfo: '用户信息',
    emailFilter: '电子邮件过滤',
    userName: '登录账户',
    email: '电子邮件',
    mobile: '手机号',
    num: '数量',
    filter: '过滤',
    key: '输入关键词',
    addPer: '添加权限',
    deletePer: '删除权限',
    deleteMessage: '此操作将删除该角色, 是否继续?',
    prompt: '提示',
    confirm: '确定',
    cancelBtn: '取消',
    success: '操作成功',
    deleteSuccess: '删除成功',
    roleNameRule: '请输角色名称',
    sortRule: '请输入排序号',
    sortRule1: '排序号必须为数字值'
  },
  fm: {
    components: {
      fields: {
        input: '单行文本',
        textarea: '多行文本',
        number: '计数器',
        radio: '单选框组',
        checkbox: '多选框组',
        time: '时间选择器',
        date: '日期选择器',
        rate: '评分',
        color: '颜色选择器',
        select: '下拉选择框',
        switch: '开关',
        slider: '滑块',
        text: '文字',
        blank: '自定义区域',
        fileupload: '文件',
        imgupload: '图片',
        editor: '编辑器',
        cascader: '级联选择器',
        table: '子表单',
        grid: '栅格布局',
        tabs: '标签页',
        divider: '分割线'
      },
      basic: {
        title: '基础字段'
      },
      advance: {
        title: '高级字段'
      },
      layout: {
        title: '布局字段'
      }
    },
    description: {
      containerEmpty: '从左侧拖拽来添加字段',
      configEmpty: '请添加字段',
      tableEmpty: '从左侧拖拽来添加字段',
      uploadJsonInfo: 'JSON格式如下，直接复制生成的json覆盖此处代码点击确定即可'
    },
    message: {
      copySuccess: '复制成功',
      validError: '表单数据校验失败'
    },
    actions: {
      import: '导入JSON',
      clear: '清空',
      preview: '预览',
      json: '生成JSON',
      code: '生成代码',
      getData: '获取数据',
      reset: '重置',
      copyData: '复制数据',
      cancel: '取 消',
      confirm: '确 定',
      addOption: '添加选项',
      addColumn: '添加列',
      addTab: '添加标签',
      upload: '点击上传',
      add: '添加'
    },
    config: {
      form: {
        title: '表单属性',
        labelPosition: {
          title: '标签对齐方式',
          left: '左对齐',
          right: '右对齐',
          top: '顶部对齐'
        },
        labelWidth: '表单标签宽度',
        size: '组件尺寸',
        customClass: '自定义Class'
      },
      widget: {
        title: '字段属性',
        model: '字段标识',
        name: '标题',
        width: '宽度',
        height: '高度',
        size: '大小',
        labelWidth: '标签宽度',
        custom: '自定义',
        placeholder: '占位内容',
        layout: '布局方式',
        block: '块级',
        inline: '行内',
        contentPosition: '文案位置',
        left: '左侧',
        right: '右侧',
        center: '居中',
        showInput: '显示输入框',
        min: '最小值',
        max: '最大值',
        step: '步长',
        multiple: '是否多选',
        filterable: '是否可搜索',
        allowHalf: '允许半选',
        showAlpha: '支持透明度选择',
        showLabel: '是否显示标签',
        option: '选项',
        staticData: '静态数据',
        remoteData: '远端数据',
        remoteFunc: '远端方法',
        value: '值',
        label: '标签',
        childrenOption: '子选项',
        defaultValue: '默认值',
        showType: '显示类型',
        isRange: '是否为范围选择',
        isTimestamp: '是否获取时间戳',
        startPlaceholder: '开始时间占位内容',
        endPlaceholder: '结束时间占位内容',
        format: '格式',
        limit: '最大上传数',
        isQiniu: '使用七牛上传',
        tokenFunc: '获取七牛Token方法',
        imageAction: '图片上传地址',
        tip: '提示说明文字',
        action: '上传地址',
        defaultType: '绑定数据类型',
        string: '字符串',
        object: '对象',
        array: '数组',
        number: '数字',
        boolean: '布尔值',
        integer: '整数',
        float: '浮点数',
        url: 'URL地址',
        email: '邮箱地址',
        hex: '十六进制',
        gutter: '栅格间隔',
        columnOption: '列配置项',
        span: '栅格值',
        justify: '水平排列方式',
        justifyStart: '左对齐',
        justifyEnd: '右对齐',
        justifyCenter: '居中',
        justifySpaceAround: '两侧间隔相等',
        justifySpaceBetween: '两端对齐',
        align: '垂直排列方式',
        alignTop: '顶部对齐',
        alignMiddle: '居中',
        alignBottom: '底部对齐',
        type: '风格类型',
        default: '默认',
        card: '选项卡',
        borderCard: '卡片化',
        tabPosition: '选项卡位置',
        top: '顶部',
        bottom: '底部',
        tabOption: '标签配置项',
        tabName: '标签名称',
        customClass: '自定义Class',
        attribute: '操作属性',
        dataBind: '数据绑定',
        hidden: '隐藏',
        readonly: '完全只读',
        disabled: '禁用',
        editable: '文本框可输入',
        clearable: '显示清除按钮',
        arrowControl: '使用箭头进行时间选择',
        isDelete: '删除',
        isEdit: '编辑',
        showPassword: '显示密码',
        validate: '校验',
        required: '必填',
        patternPlaceholder: '填写正则表达式',
        newOption: '新选项',
        tab: '标签页',
        validatorRequired: '必须填写',
        validatorType: '格式不正确',
        validatorPattern: '格式不匹配'
      }
    },
    upload: {
      preview: '预览',
      edit: '替换',
      delete: '删除'
    }
  },
  tenantManage: {
    name: '租户名',
    regionCode: '地区',
    operate: '操作',
    edit: '编辑',
    delete: '删除',
    companyNameRule: '请填写公司名称',
    comTypeRule: '请填写公司类型',
    regionRule: '请填写地区',
    deleteMessage: '此操作将删除该租户, 是否继续?',
    prompt: '提示',
    confirm: '确定',
    cancelBtn: '取消',
    success: '操作成功'
  },
  dictionary: {
    add: '新增字典',
    more: '更多菜单',
    edit: '编辑字典',
    del: '删除字典',
    ref: '刷新字典',
    selected: '当前选择',
    deselect: '取消选择',
    keyword: '请输入查询关键词',
    dataName: '请输入数据名称',
    status: '请选择数据状态',
    addData: '新增数据',
    name: '名称',
    values: '数据值',
    remark: '备注',
    state: '状态',
    disabled: '禁用',
    use: '使用',
    time: '创建时间',
    dictionaryName: '字典名称',
    enterDictionaryName: '请输入字典名称',
    language: '语言',
    selectLanguage: '请选择语言',
    parentNode: '父节点',
    selectParentNode: '请选择父节点',
    type: '字典类型',
    enterType: '请输入字典类型',
    code: '字典code',
    enterCode: '请输入字典code',
    enterRemark: '请输入字典备注',
    sort: '排序',
    date: '数据名称',
    enterValues: '请输入数据值',
    dataType: '数据类型',
    enterDataType: '请输入数据类型',
    enterDataRemark: '请输入数据备注',
    isUse: '是否使用',
    sameData: '数据名称有相同，请重新输入',
    sameLanguage: '语言选择有相同，请重新语言',
    sameDictionaryName: '字典名称有相同，请重新输入',
    editData: '编辑数据',
    delDictionaryData: '此操作将永久删除字典数据, 是否继续?',
    selectData: '请选择数据',
    selectNode: '请选择节点',
    delDictionary: '此操作将永久删除字典, 是否继续?',
    deleteSuccess: '删除成功',
    NotDeleteAll: '不能全部删除数据名称',
    NotDeleteAllDictionary: '不能全部删除字典名称',
    selectLevel: '字典最多只能添加2级'

  },
  taskManage: {
    query: '请输入查询条件',
    selectType: '请选择任务类型名称',
    search: '搜索',
    add: '新增',
    delete: '删除',
    title: '任务标题',
    content: '任务内容',
    status: '任务状态',
    type: '任务类型',
    createTime: '创建时间',
    operate: '操作',
    show: '查看',
    handle: '办理',
    turnTodo: '转办',
    edit: '编辑',
    done: '完成',
    stop: '终止',
    pause: '暂停',
    pending: '恢复自动',
    addTask: '新增任务',
    cancel: '取消',
    confirm: '确定',
    transferTask: '转办任务',
    selectMan: '请选择代理人',
    selectUser: '请选择指定人',
    remark: '备注',
    remarkPlaceholder: '请输入备注',
    recoveryPrompt: '此操作将恢复暂停任务, 是否继续?',
    operateSuccess: '操作成功',
    operateCancel: '取消操作',
    terminateTask: '此操作将暂停任务, 是否继续?',
    stopTask: '此操作将终止任务, 是否继续?',
    turnSuccess: '转办成功',
    all: '全部',
    doneTask: '此操作将完成任务, 是否继续?',
    delTask: '此操作将永久删除任务, 是否继续?',
    delPending: '任务在进程中，不能删除',
    delSuccess: '删除成功',
    selectTask: '请选择任务'
  }

}
