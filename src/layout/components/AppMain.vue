<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view ref="routerView" :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  },
  mounted() {
    this.$nextTick(() => {
      // console.log(this.$refs.routerView.$vnode.parent.componentInstance)

      this.emitter.on('removeCachePage', () => {
        const { cache, keys } = this.$refs.routerView.$vnode.parent.componentInstance

        if (keys.length) {
          // 先获取缓存的路由key前缀
          const prefix = keys.at(0).slice(0, keys.at(0).indexOf('/'))
          const realKey = `${prefix}${this.key}`
          Object.prototype.hasOwnProperty.call(cache, realKey) &&
          (() => {
            // 点击tab关闭页面，移除对应页面缓冲
            delete cache[realKey]
            keys.splice(keys.indexOf(realKey), 1)
          })()
        }
      })
    })
  },
  beforeDestroy() {
    this.emitter.off('removeCachePage')
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  //overflow: hidden;
}

.fixed-header+.app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header+.app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
