<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb v-if="!topNav" id="breadcrumb-container" class="breadcrumb-container" />
    <top-nav v-if="topNav" id="topmenu-container" class="topmenu-container" />

    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <search id="header-search" class="right-menu-item" />
        <!-- 站内信 -->
        <notify-message class="right-menu-item hover-effect" />
        <el-tooltip :content="$t('order.edition')" effect="dark" placement="bottom">
          <div class="right-menu-item hover-effect" @click="openSystemVersion">
            <svg-icon icon-class="question" />
          </div>
        </el-tooltip>
        <screenfull id="screenfull" class="right-menu-item hover-effect" />
        <lang-select class="right-menu-item hover-effect" />
        <!--        <el-tooltip content="布局大小" effect="dark" placement="bottom">-->
        <!--          <size-select id="size-select" class="right-menu-item hover-effect" />-->
        <!--        </el-tooltip>-->

      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="$router.push('/user/profile')">{{ $t('common.personalCenter') }}</el-dropdown-item>
          <el-dropdown-item @click.native="setting = true">
            <span>{{ $t('common.layoutSettings') }}</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>{{ $t('common.logOutAndLogIn') }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-dialog
      :title="$t('order.edition')"
      :visible.sync="dialogVisible"
      append-to-body
      width="40%"
    >
      <hr>
      <p>版本：{{ systemVersion }}</p>
      <p>丰致网站：
        <el-link @click="openEsic">www.esicint.com</el-link>
      <!--        <a @click="openEsic">www.esic.com</a></p>-->
      </p><p>本软件是基于J2EE的各种技术，B/S模式的三层结构设计完成的，
        由丰致科技(深圳)有限公司独立开发本软件的版权属于丰致科技(深圳)有限公司，
        未经丰致公司的授权许可不得擅自发布或使用该软件weaver e-cology、丰致司标均是丰致科技(深圳)有限公司商标，
        Windows、NT、Java等均是各相关公司的商标或注册商标</p>
      <p>警告:本计算机程序受著作权法和国际公约的保护，未经授权擅自复制或散布本程序的部分或全部，
        将承受严厉的民事和刑事处罚，对已知的违反者将给予法律范围内的全面制裁。</p>
      <div style="color: #a6a6a6">Copyright © 2006-2022 esicint.com 版权所有苏ICP备16049996号-1
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('order.determine') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'
import NotifyMessage from '@/layout/components/Message'
import LangSelect from '@/components/LangSelect'
import { getPath } from '@/utils/ruoyi'
import { getConfigKey } from '@/api/infra/config'

export default {
  components: {
    LangSelect,
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc,
    NotifyMessage
  },
  data() {
    return {
      dialogVisible: false,
      systemVersion: null
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },

  watch: {
    'sidebar.opened': {
      // 监听侧边栏展开和收起，调整底部悬浮按钮位置
      handler(val) {
        if (val) {
          document.documentElement.style.setProperty('--fixedButtonLeft', ` calc(50% + 103px)`)
        } else {
          document.documentElement.style.setProperty('--fixedButtonLeft', ` calc(50% + 28px)`)
        }
      },
      immediate: true
    }
  },
  methods: {
    openSystemVersion() {
      this.getSystemVersion()
      this.dialogVisible = true
    },
    getSystemVersion() {
      getConfigKey('system.version').then(response => {
        this.systemVersion = response.data
      })
    },
    openEsic() {
      window.open('http://www.esicint.com/')
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      this.$modal.confirm(this.$t('common.areYouSureToLogOutAndExitTheSystem'), this.$t('supplier.tips')).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = getPath('/index')
        })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #F2F4F9;
  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #606266;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
