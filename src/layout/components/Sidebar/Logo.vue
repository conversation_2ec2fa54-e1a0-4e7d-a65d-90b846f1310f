<template>
  <div :class="{'collapse':collapse}" class="sidebar-logo-container">
    <transition name="sidebarLogoFade">
      <div v-if="collapse">
        <router-link key="collapse" class="sidebar-logo-link" to="/">
          <img
            v-if="logo"
            src="../../../assets/images/logo.png"
            style="position:relative;top: 18px;height: 25px;width: 25px"
          >
          <!--        <img v-if="logo" :src="logo" class="sidebar-logo">-->
          <h1 v-else class="sidebar-title">{{ title }} </h1>
        </router-link>
      </div>
      <div v-else>
        <router-link key="expand" class="sidebar-logo-link" to="/">
          <el-image v-if="loginLogoImage!=undefined&&loginLogoImage!=''" :src="loginLogoImage" style="height: 30px;width: 184px;margin-top: 15px" />
          <svg-icon v-else-if="logo" icon-class="logo3" style="height: 66px;width: 184px" />
        </router-link>

      </div>

    </transition>
  </div>
</template>

<script>
import logoImg from '@/assets/logo/logo.png'
import variables from '@/assets/styles/variables.module.scss'
import { getConfigKey } from '@/api/infra/config'

export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      title: 'SRM 2.0',
      logo: logoImg,
      loginLogoImage: undefined
    }
  },
  computed: {
    variables() {
      return variables
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme
    }
  },
  mounted() {
    this.getLoginImage()
  },
  methods: {
    getLoginImage() {
      getConfigKey('login.logo.image').then(response => {
        this.loginLogoImage = response.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 90px;
  line-height: 50px;
  background: #2e6f88;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
