<template>
  <div v-if="!item.hidden">
    <template
      v-if="hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow"
    >
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-tooltip effect="dark" :content="generateTitle(onlyOneChild.meta.title)" placement="right">
          <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{'submenu-title-noDropdown':!isNest}">
            <item
              :icon="onlyOneChild.meta.icon||(item.meta&&item.meta.icon)"
              :title="generateTitle(onlyOneChild.meta.title)"
            />
          </el-menu-item>
        </el-tooltip>

      </app-link>
    </template>

    <el-submenu v-else ref="subMenu" :index="resolvePath(item.path)" :popper-class="$store.getters.sidebar.opened?'myPopperClass myPopperClassOpen':'myPopperClass myPopperClassClose'">
      <template slot="title">
        <item v-if="item.meta" :icon="item.meta && item.meta.icon" :title="generateTitle(item.meta.title)" />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'
import { generateTitle } from '@/utils/i18n'
import { debounce, throttle } from 'throttle-debounce'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    this.onlyOneChild = null
    return {}
  },
  mounted() {

  },
  methods: {
    generateTitle,
    hasOneShowingChild(children = [], parent) {
      if (!children) {
        children = []
      }
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    }
  }
}
</script>
<style lang="scss">
.el-submenu.is-opened > .el-submenu__title .el-submenu__icon-arrow{
  -webkit-transform: rotateZ(90deg);
  transform: rotateZ(90deg);
}
.el-submenu> .el-submenu__title .el-submenu__icon-arrow{
  -webkit-transform: rotateZ(270deg);
  transform: rotateZ(270deg);
}

.myPopperClass {
  width: 200px;
  max-height: 60vh;
  overflow-y: auto;
  margin-top: -50px;
  .el-submenu.is-opened > .el-submenu__title .el-submenu__icon-arrow{
    -webkit-transform: rotateZ(180deg);
    transform: rotateZ(180deg);
    color: #fff;

  }
  .el-submenu> .el-submenu__title .el-submenu__icon-arrow{
    -webkit-transform: rotateZ(0deg);
    transform: rotateZ(0deg);
    color: #fff;
  }
  .el-submenu__title{
    height: 46px!important;
    line-height: 46px!important;
  }
  ul.el-menu.el-menu--popup{
    position: relative;
    top: -50px;
    li{
      height: 46px;
      line-height: 46px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      span{
        white-space: nowrap; /* 不换行 */
        text-overflow: ellipsis; /* 使用省略号 */
      }
    }
  }
  ul {
    .nest-menu:first-of-type {
      margin-top:46px
    }
  }
  .myPopperClass {
    ul.el-menu.el-menu--popup{
      position: unset;
      top: unset;
    }
    ul {
      .nest-menu:first-of-type {
        margin-top:0
      }
    }
  }
}
.myPopperClass::-webkit-scrollbar{
  width: 0;
}

.myPopperClassOpen {
  left: 205px !important;
  .myPopperClassOpen {
    left: 405px !important;
  }
}
.myPopperClassClose {
  left: 60px !important;
  .myPopperClassClose {
    left: 260px !important;
  }
}
</style>
