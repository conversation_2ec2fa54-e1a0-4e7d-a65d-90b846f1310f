<template>
  <div :class="{'has-logo':showLogo}" :style="{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground"
        :text-color="settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
        :unique-opened="true"
        :active-text-color="variables.logoTitleColor"
        :collapse-transition="false"
        mode="horizontal"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/assets/styles/variables.module.scss'
import { debounce } from 'throttle-debounce'

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapState(['settings']),
    ...mapGetters(['sidebarRouters', 'sidebar']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  mounted() {
    this.$nextTick(() => {
      const elements = document.querySelectorAll('li.el-submenu')
      elements.forEach((element) => {
        element.addEventListener('mouseenter',
          debounce(300, false, () => {
            this.$nextTick(() => {
              const querySelectorAll = document.querySelectorAll('.myPopperClass')
              querySelectorAll.forEach(v => {
                const replace = v.style.top.replace('px', '')
                if (replace && replace <= 20) {
                  v.style.marginTop = '30px'
                } else if (replace && replace <= 50) {
                  v.style.marginTop = '10px'
                } else {
                  v.style.marginTop = '-50px'
                }
              })
            })
          })
        )
      })
    })
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-submenu__icon-arrow {
  color: white;
}
</style>
