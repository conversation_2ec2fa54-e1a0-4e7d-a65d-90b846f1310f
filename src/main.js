import Vue from 'vue'

import Cookies from 'js-cookie'

// import 'vxe-table/lib/style.css'
// 默认点击背景不关闭弹窗
import Element from 'element-ui'
import ElementUI from 'element-ui'
import './assets/styles/element-variables.scss'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import './assets/icons' // icon
import './permission' // permission control
// import './tongji' // 百度统计
import { getDicts } from '@/api/system/dict/data'
import { getConfigKey } from '@/api/infra/config'
import {
  addBeginAndEndTime,
  addDateRange,
  handleTree,
  parseTime,
  resetForm,
  formatNumNoZero,
  downloadFile
} from '@/utils/ruoyi'
import Pagination from '@/components/Pagination'
import StatisticsCard from '@/components/StatisticsCard'
// 自定义表格工具扩展
import RightToolbar from '@/components/RightToolbar'
import CommonSearch from '@/components/CommonSearch'
// 代码高亮插件
// import hljs from 'highlight.js'
// import 'highlight.js/styles/github-gist.css'
import { DICT_TYPE, getDictDataLabel, getDictDatas, getDictDatas2 } from '@/utils/dict'
// 字典标签组件
import DictTag from '@/components/DictTag'
import CascadingCategory from '@/components/CascadingCategory'
import CommonCard from '@/components/CommonCard'
import DocAlert from '@/components/DocAlert'
import NumberFormat from '@/components/NumberFormat'
// 头部标签插件
import VueMeta from 'vue-meta'
// bpmnProcessDesigner 需要引入
import MyPD from '@/components/bpmnProcessDesigner/package/index.js'
import '@/components/bpmnProcessDesigner/package/theme/index.scss'
import 'bpmn-js/dist/assets/diagram-js.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css'

// Form Generator 组件需要使用到 tinymce
import Tinymce from '@/components/tinymce/index.vue'
import '@/icons'
import request from '@/utils/request' // 实现 form generator 使用自己定义的 axios request 对象
import '@/styles/index.scss'
import i18n from './lang' // internationalization
import 'xe-utils'
import VXETable from 'vxe-table'
import mitt from 'mitt'
import VueEllipsis from 'vue-ellipsis-component'

/* import the fontawesome core */
import { library } from '@fortawesome/fontawesome-svg-core'

/* import font awesome icon component */
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

/* import specific icons */
import { faUserSecret } from '@fortawesome/free-solid-svg-icons'
import 'viewerjs/dist/viewer.css'
import VueViewer from 'v-viewer'

/* add icons to the library */
library.add(faUserSecret)

/* add font awesome icon component */
Vue.component('FontAwesomeIcon', FontAwesomeIcon)
Vue.use(VueEllipsis)

import { installElement } from '@/config/element'

installElement(Vue, Element)

import DownloadExcel from '@/components/DownloadExcel'

Vue.component('DownloadExcel', DownloadExcel)

// Websocket注册
import { createSocket, sendWSPush, sendPing, exit, SET_SOCKET_URL } from '@/utils/websocket'
import CopyButton from '@/components/CopyButton/index.vue'
import Tui from '@wocwin/t-ui'
Vue.use(Tui)
ElementUI.Dialog.props.closeOnClickModal.default = false
ElementUI.Dialog.props.lockScroll.default = false
ElementUI.Upload.props.onError.default = function(err, file) {
  const isLt2M = file.size / 1024 / 1024 < process.env.VUE_APP_FILESIZE
  if (!isLt2M) {
    this.$message.error(i18n.t('common.uploadFileSizeCannotExceedm', { fileSize: process.env.VUE_APP_FILESIZE }))
  }
  return isLt2M
}
ElementUI.Descriptions.props.colon.default = false
VXETable.setup({
  i18n: (key, args) => i18n.t(key, args),

  table: {
    showHeaderOverflow: true,
    showOverflow: true,
    scrollY: {
      enabled: false
    }
  }
})
export const emitter = mitt()

// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.emitter = emitter
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.addBeginAndEndTime = addBeginAndEndTime
Vue.prototype.getDictDatas = getDictDatas
Vue.prototype.getDictDatas2 = getDictDatas2
Vue.prototype.getDictDataLabel = getDictDataLabel
Vue.prototype.DICT_TYPE = DICT_TYPE
Vue.prototype.handleTree = handleTree
Vue.prototype.formatNumNoZero = formatNumNoZero
Vue.prototype.downloadFile = downloadFile

// 全局组件挂载
Vue.component('CopyButton', CopyButton)
Vue.component('CommonCard', CommonCard)
Vue.component('CascadingCategory', CascadingCategory)
Vue.component('DictTag', DictTag)
Vue.component('NumberFormat', NumberFormat)
Vue.component('DocAlert', DocAlert)
Vue.component('Pagination', Pagination)
Vue.component('StatisticsCard', StatisticsCard)
Vue.component('RightToolbar', RightToolbar)
Vue.component('CommonSearch', CommonSearch)

Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
// Vue.use(hljs.vuePlugin);
Vue.use(MyPD)
Vue.use(VXETable)
Vue.use(VueViewer)


Vue.component('Tinymce', Tinymce)
Vue.prototype.$axios = request
Vue.prototype.createSocket = createSocket
Vue.prototype.sendWSPush = sendWSPush
Vue.prototype.sendPing = sendPing
Vue.prototype.exit = exit
Vue.prototype.SET_SOCKET_URL = SET_SOCKET_URL

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  i18n: (key, value) => i18n.t(key, value),
  size: Cookies.get('size') || 'small' // set element-ui default size
})

Vue.config.productionTip = false

// 定义一个全局计时器的启动方法
Vue.prototype.$startTimer = function() {
  if (!this.$_globalTimer) { // 避免重复创建计时器
    this.$store.dispatch('NotifyPasswordExpiredTime')
    this.$_globalTimer = setInterval(() => {
      this.$store.dispatch('NotifyPasswordExpiredTime')
      // 这里可以执行你想要周期性执行的代码
    }, 1000 * 60) // 设定计时器的周期，例如1000毫秒
  }
}

// 定义一个全局计时器的停止方法
Vue.prototype.$stopTimer = function() {
  if (this.$_globalTimer) {
    clearInterval(this.$_globalTimer)
    this.$_globalTimer = null // 清除计时器引用
  }
}
new Vue({
  el: '#app',
  router,
  store,
  i18n,
  created() {
    this.$startTimer() // 在Vue实例创建时启动计时器
  },
  beforeDestroy() {
    this.$stopTimer() // 在Vue实例销毁前停止计时器
  },
  render: h => h(App)
})
import waterMark from './utils/waterMark.js'
// 获取环境变量参数
const WATER_MARK = process.env.VUE_APP_WATER_MARK
router.afterEach((item) => {
  if (WATER_MARK && !item.name.includes('/login')) {
    waterMark.set(WATER_MARK)
  }
})
