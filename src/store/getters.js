const getters = {
  sidebar: state => state.app.sidebar,
  language: state => state.app.language,
  size: state => state.app.size,
  device: state => state.app.device,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  userId: state => state.user.id,
  supplierId: state => state.user.supplierId,
  supplierName: state => state.user.supplierName,
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  sealPath: state => state.user.sealPath,
  name: state => state.user.name,
  isExternal: state => state.user.isExternal,
  incapCustomers: state => state.user.incapCustomers,
  nickname: state => state.user.nickname,
  introduction: state => state.user.introduction,
  passwordExpiredPeriod: state => state.user.passwordExpiredPeriod,
  passwordExpiredTime: state => state.user.passwordExpiredTime,
  roles: state => state.user.roles,
  permissions: state => state.user.permissions,
  permission_routes: state => state.permission.routes,
  // 工具栏
  topbarRouters: state => state.permission.topbarRouters,
  defaultRoutes: state => state.permission.defaultRoutes,
  sidebarRouters: state => state.permission.sidebarRouters,
  // 数据字典
  dict_datas: state => state.dict.dictDatas,
  companyId: state => state.user.companyId,
  companyIdList: state => state.user.companyIdList,
  decimalPlace: state => state.dict.decimalPlace,
  fixedHeader: state => state.settings.fixedHeader,
  eventCode: state => state.params.eventCode,
  // 询报价的小数位，临时写死，后续可以扩展从配置获取
  rfqDecimalPlace: state => 8,
  creditModule: state => state.dict.creditModule

}
export default getters
