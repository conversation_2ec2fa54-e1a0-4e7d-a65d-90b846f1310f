import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import user from './modules/user'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import settings from './modules/settings'
import dict from './modules/dict'
import params from './modules/params'
import getters from './getters'
import createPersistedState from 'vuex-persistedstate'
import advanceSearch from "@/store/modules/advanceSearch";

const dataState = createPersistedState({
  paths: ['user.companyId']
})

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    user,
    tagsView,
    permission,
    settings,
    dict,
    params,
    advanceSearch
  },
  plugins: [dataState],
  getters
})

export default store
