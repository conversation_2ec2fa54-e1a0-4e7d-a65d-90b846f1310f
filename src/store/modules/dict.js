import { listSimpleDictDatas } from '@/api/system/dict/data'
import { listCurrencyFromCache } from '@/api/system/currency'
import { getCategoriesListCache } from '@/api/system/categories'
import { listCountryRegionAll } from '@/api/system/countryRegion'
import { getPurchaseOrgCache } from '@/api/system/purchaseOrg'
import { getDeptCode, listSimpleDepts } from '@/api/system/dept'
import { listFactoryFromCache, listPurchaseOrgFactoryFromCache } from '@/api/system/factory'
import { getUsersCache, getUserOrgFromCache } from '@/api/system/user'
import { DICT_TYPE } from '@/utils/dict'
import { getConfigKey } from '@/api/infra/config'
import {getInquiryTypes} from "@/api/rfq/home";
import { getDefectTypeListCache } from '@/api/scar/defectType'

const state = {
  /**
   * 数据字典 MAP
   * key：数据字典大类枚举值 dictType
   * dictValue：数据字典小类数值 {dictValue: '', dictLabel: ''} 的数组
   */
  dictDatas: {},
  decimalPlace: 2,
  creditModule: false
}

const mutations = {
  SET_DICT_DATAS: (state, dictDatas) => {
    state.dictDatas = dictDatas
  },
  SET_DECIMAL_PLACE: (state, value) => {
    state.decimalPlace = value
  },
  SET_CREDIT_MODULE: (state, value) => {
    state.creditModule = value
  }
}

const actions = {
  loadDictDatas({ commit }) {
    return new Promise((resolve,reject) => {
      Promise.all([
        listSimpleDictDatas(),
        listCurrencyFromCache(),
        getCategoriesListCache(''),
        listCountryRegionAll(),
        getPurchaseOrgCache(),
        getDeptCode(),
        listFactoryFromCache(),
        getUsersCache({
          status: 0,
          isExternal: 1
        }),
        getInquiryTypes(),
        getDefectTypeListCache(''),
        getConfigKey('common.decimalplace'),
        listSimpleDepts(),
        getUserOrgFromCache(),
        getConfigKey('supplier.enable'),
        getUsersCache(),
      ]).then(res => {
        // 设置数据
        const dictDataMap = {}

        // 获取币种 品类数据
        dictDataMap[DICT_TYPE.COMMON_CURRENCY] = res[1].data
        dictDataMap[DICT_TYPE.COMMON_CATEGORY] = res[2].data?.map(item => {
          return {
            ...item,
            code: ''
          }
        })
        dictDataMap[DICT_TYPE.COMMON_COUNTRY] = res[3].data?.map(item => {
          return {
            ...item,
            keyPath: null
          }
        })
        dictDataMap[DICT_TYPE.COMMON_PURCHASEORG] = res[4].data
        dictDataMap[DICT_TYPE.COMMON_COMPANY] = res[5].data
        dictDataMap[DICT_TYPE.COMMON_FACTORY] = res[6].data
        dictDataMap[DICT_TYPE.COMMON_USERS] = res[7].data?.map(item => {
          return {
            ...item,
            name: `${item.nickname}`
          }
        })
        dictDataMap[DICT_TYPE.COMMON_USERS_INCLUDES_ALL] = res[14].data?.map(item => {
          return {
            ...item,
            name: `${item.nickname}`
          }
        })
        dictDataMap[DICT_TYPE.RFQ_BUSINESS_TYPE] = res[8].data
        dictDataMap[DICT_TYPE.SCAR_DEFECT_TYPE] = res[9].data
        dictDataMap[DICT_TYPE.COMMON_DEPT] = res[11].data

        // 带当前用户权限的基础数据赋值
        // orgIds=1,2,3 可以赋值后端的GET请求的集合参数
        if (res[12].data) {
          var orgIds = res[12].data?.map((obj)=>{return obj.id})
          listPurchaseOrgFactoryFromCache({
            orgIds: orgIds.join(','),
            status: 0
          }).then(res => {
            dictDataMap[DICT_TYPE.COMMON_FACTORY_WITH_AUTH] = res.data || []
          })
        }

        if (res[0].data) {
          res[0].data.forEach(dictData => {
            // 获得 dictType 层级
            const enumValueObj = dictDataMap[dictData.dictType]
            if (!enumValueObj) {
              dictDataMap[dictData.dictType] = []
            }
            // 处理 dictValue 层级
            dictDataMap[dictData.dictType].push({
              value: dictData.value,
              label: dictData.label,
              colorType: dictData.colorType,
              cssClass: dictData.cssClass,
              status: dictData.status,
              extendedValue: dictData.extendedValue
            })
          })
        }
        // 小数位获取后端配置
        commit('SET_DECIMAL_PLACE', Number(res[10].data))
        commit('SET_CREDIT_MODULE', res[13].data === 'true')
        // 存储到 Store 中
        commit('SET_DICT_DATAS', dictDataMap)
        resolve()
      }).catch(e=>{
        reject(e)
      })
    })

  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
