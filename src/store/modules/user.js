import { getInfo, login, logout, smsLogin, socialBindLogin, socialQuickLogin } from '@/api/login'
import { removeToken, setToken, setPasswordExpiredTime, setCustomList } from '@/utils/auth'
import avatar from '@/assets/images/profile.jpg'
import { Notification } from 'element-ui'
import i18n from '../../lang'
import { getFrontTableFieldsUserRel } from '@/api/system/customField' // internationalization

const user = {
  state: {
    id: 0, // 用户编号
    isExternal: 0, // 1- 对内；2- 对外。
    supplierId: '', // 供应商账户进入系统时，此字段不为空
    supplierName: '',
    name: '',
    avatar: '',
    sealPath: '',
    roles: [],
    permissions: [],
    companyId: '',
    companyIdList: [],
    incapCustomers: [],
    nickname: '',
    passwordExpiredPeriod: 0,
    passwordExpiredTime: '',
    callFlag5: true,
    callFlag10: true,
    callFlag20: true,
    callFlag30: true
  },

  mutations: {
    SET_ID: (state, id) => {
      state.id = id
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_SEALPATH: (state, sealPath) => {
      state.sealPath = sealPath
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_COMPANYID: (state, companyId) => {
      state.companyId = companyId
    },
    SET_COMPANYIDLIST: (state, companyIdList) => {
      state.companyIdList = companyIdList
      if (companyIdList) {
        state.companyId = companyIdList[0]
      }
    },
    SET_EXTERNAL: (state, isExternal) => {
      state.isExternal = isExternal
    },
    SET_NICKNAME: (state, nickname) => {
      state.nickname = nickname
    },
    SET_INCAP_CUSTOMER: (state, incapCustomers) => {
      state.incapCustomers = incapCustomers ? incapCustomers : []
    },
    SET_SUPPLIERID: (state, supplierId) => {
      state.supplierId = supplierId
    },
    SET_SUPPLIERNAME: (state, supplierName) => {
      state.supplierName = supplierName
    },
    SET_PASSWORD_EXPIRED_PERIOD: (state, passwordExpiredPeriod) => {
      state.passwordExpiredPeriod = passwordExpiredPeriod
    },
    SET_PASSWORD_EXPIRED_TIME: (state, passwordExpiredTime) => {
      state.passwordExpiredTime = passwordExpiredTime
    }
  },

  actions: {
    NotifyPasswordExpiredTime({ commit, state }) {
      console.log('NotifyPasswordExpiredTime')
      const extracted = (res) => {
        if (res > 0) {
          Notification({
            title: i18n.t('提示'),
            message: i18n.t('当前密码有效期不足') + Math.ceil(res / 60000) + i18n.t('分钟，请注意保存工作，尽快修改密码'),
            type: 'warning',
            duration: 0
          })
        }
      }
      const item = localStorage.getItem('PASSWORD_EXPIRED_TIME')
      if (item) {
        const res = Number(item) - new Date().getTime()
        if (res < 1000 * 60 * 5) {
          if (state.callFlag5) {
            state.callFlag5 = false
            extracted(res)
          }
        } else if (res < 1000 * 60 * 10) {
          if (state.callFlag10) {
            state.callFlag10 = false
            extracted(res)
          }
        } else if (res < 1000 * 60 * 20) {
          if (state.callFlag20) {
            state.callFlag20 = false
            extracted(res)
          }
        } else if (res < 1000 * 60 * 30) {
          if (state.callFlag30) {
            state.allFlag30 = false
            extracted(res)
          }
        }
      }
    },
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid).then(res => {
          res = res.data
          // 设置 token
          setToken(res)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 社交登录
    SocialLogin({ commit }, userInfo) {
      const code = userInfo.code
      const state = userInfo.state
      const type = userInfo.type
      return new Promise((resolve, reject) => {
        socialQuickLogin(type, code, state).then(res => {
          res = res.data
          // 设置 token
          setToken(res)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 社交登录
    SocialLogin2({ commit }, userInfo) {
      const code = userInfo.code
      const state = userInfo.state
      const type = userInfo.type
      const username = userInfo.username.trim()
      const password = userInfo.password
      return new Promise((resolve, reject) => {
        socialBindLogin(type, code, state, username, password).then(res => {
          res = res.data
          // 设置 token
          setToken(res)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 登录
    SmsLogin({ commit }, userInfo) {
      const mobile = userInfo.mobile.trim()
      const mobileCode = userInfo.mobileCode
      return new Promise((resolve, reject) => {
        smsLogin(mobile, mobileCode).then(res => {
          res = res.data
          // 设置 token
          setToken(res)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          // 没有 data 数据，赋予个默认值
          if (!res) {
            res = {
              data: {
                roles: [],
                user: {
                  id: '',
                  avatar: '',
                  sealPath: '',
                  userName: ''
                },
                incapCustomers: []
              }
            }
          }
          res = res.data // 读取 data 数据
          const user = res.user
          const avatar = require('@/assets/images/profile.jpg')
          const sealPath = require('@/assets/images/profile.jpg')
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_ID', user.id)
          commit('SET_NAME', user.userName)
          commit('SET_AVATAR', user.avatar || avatar)
          commit('SET_SEALPATH', user.sealPath || sealPath)
          commit('SET_EXTERNAL', user.isExternal)
          commit('SET_NICKNAME', user.nickname)
          commit('SET_INCAP_CUSTOMER', res.incapCustomers)
          commit('SET_PASSWORD_EXPIRED_TIME', user.passwordExpiredTime)
          commit('SET_PASSWORD_EXPIRED_PERIOD', user.passwordExpiredPeriod)
          setPasswordExpiredTime(user.passwordExpiredTime)
          getFrontTableFieldsUserRel(user.id).then(res => {
            setCustomList(JSON.stringify(
              res.data.reduce((acc, a) => {
                acc[a.tableId] = JSON.parse(a.fieldsJson)
                return acc
              }
              , {})))
          })
          if (user.supplierId) {
            commit('SET_SUPPLIERID', user.supplierId)
            commit('SET_SUPPLIERNAME', user.supplierName)
          }
          if (user.companyIdList) {
            commit('SET_COMPANYIDLIST', user.companyIdList)
          }
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 匿名退出系统，只是前端清理缓存，后端token不清理
    LogAnonymousOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        removeToken()
        commit('SET_ROLES', [])
        commit('SET_PERMISSIONS', [])
        resolve()
      })
    },
    // 清空roles，用于切换router后（router.beforeEach）刷新当前用户信息
    ClearRoles({ commit, state }) {
      return new Promise((resolve, reject) => {
        commit('SET_ROLES', [])
        resolve()
      })
    }
  }
}

export default user
