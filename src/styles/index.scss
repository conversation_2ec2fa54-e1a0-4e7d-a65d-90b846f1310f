$editorTabsborderColor: #121315;
:root {
  --fixedButtonLeft:  calc(50% + 103px); /* 默认值，或者可以省略 */
}

body, html{
  margin: 0;
  padding: 0;
  background: #fff;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;
}

input, textarea{
  font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;
}

.editor-tabs{
  background: $editorTabsborderColor;
  .el-tabs__header{
    margin: 0;
    border-bottom-color: $editorTabsborderColor;
    .el-tabs__nav{
      border-color: $editorTabsborderColor;
    }
  }
  .el-tabs__item{
    height: 32px;
    line-height: 32px;
    color: #888a8e;
    border-left: 1px solid $editorTabsborderColor!important;
    background: #363636;
    margin-right: 5px;
    user-select: none;
  }
  .el-tabs__item.is-active{
    background: #1e1e1e;
    border-bottom-color: #1e1e1e!important;
    color: #fff;
  }
  .el-icon-edit{
    color: #f1fa8c;
  }
  .el-icon-document{
    color: #a95812;
  }
  :focus.is-active.is-focus:not(:active) {
    box-shadow: none;
    border-radius: 0;
  }
}

// home
.right-scrollbar {
  .el-scrollbar__view {
    padding: 12px 18px 15px 15px;
  }
}
.el-scrollbar__wrap {
  box-sizing: border-box;
  overflow-x: hidden !important;
  margin-bottom: 0 !important;
}
.center-tabs{
  .el-tabs__header{
    margin-bottom: 0!important;
  }
  .el-tabs__item{
    width: 50%;
    text-align: center;
  }
  .el-tabs__nav{
    width: 100%;
  }
}
.reg-item{
  padding: 12px 6px;
  background: #f8f8f8;
  position: relative;
  border-radius: 4px;
  .close-btn{
    position: absolute;
    right: -6px;
    top: -6px;
    display: block;
    width: 16px;
    height: 16px;
    line-height: 16px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    color: #fff;
    text-align: center;
    z-index: 1;
    cursor: pointer;
    font-size: 12px;
    &:hover{
      background: rgba(210, 23, 23, 0.5)
    }
  }
  & + .reg-item{
    margin-top: 18px;
  }
}
.action-bar{
  & .el-button+.el-button {
    margin-left: 15px;
  }
  & i {
    font-size: 20px;
    vertical-align: middle;
    position: relative;
    top: -1px;
  }
}

.custom-tree-node{
  width: 100%;
  font-size: 14px;
  .node-operation{
    float: right;
  }
  i[class*="el-icon"] + i[class*="el-icon"]{
    margin-left: 6px;
  }
  .el-icon-plus{
    color: #409EFF;
  }
  .el-icon-delete{
    color: #157a0c;
  }
}

.el-scrollbar__view{
  overflow-x: hidden;
}

.el-rate{
  display: inline-block;
  vertical-align: text-top;
}
.el-upload__tip{
  line-height: 1.2;
}
.el-button--text{
  font-size: 14px;
  padding-top: 0;
  padding-bottom: 0;
}

.el-table__row{
  td{
    font-size: 14px;
  }
}
.el-table{
  .el-table__header-wrapper   {
    tr{
      height: 25px!important;
      background: #e9edf0;
      font-size: 12px;
      .el-checkbox__input::before{
        content: '';
        position: absolute;
        top: -9px;
        right: -6px;
        bottom: -6px;
        left: -7px;
      }
    }
    th{
      color: #606266;
      font-weight: 400;
      background: #e9edf0;
      height: 25px!important;
      padding: 2px;
      font-size: 12px!important;
    }
  }
  .el-table__fixed-header-wrapper   {
    tr{
      height: 25px!important;
      background: #e9edf0;
      font-size: 12px;
      .el-checkbox__input::before{
        content: '';
        position: absolute;
        top: -9px;
        right: -6px;
        bottom: -6px;
        left: -7px;
      }
    }
    th{
      color: #606266;
      font-weight: 400;
      background: #e9edf0;
      height: 25px!important;
      padding: 2px;
      font-size: 12px!important;
    }
  }
  .el-table__body-wrapper{
    tr{
      height: 34px!important;
      font-size: 14px;
      .el-checkbox__input::before{
        content: '';
        position: absolute;
        top: -9px;
        right: -6px;
        bottom: -6px;
        left: -7px;
      }
    }
    th{
      color:#606266;
      height: 34px!important;
      padding: 2px;
      font-size: 14px!important;
    }
  }
  .el-table__fixed-body-wrapper{
    tr{
      height: 34px!important;
      font-size: 14px;
      .el-checkbox__input::before{
        content: '';
        position: absolute;
        top: -9px;
        right: -6px;
        bottom: -6px;
        left: -7px;
      }
    }
    th{
      color:#606266;
      height: 34px!important;
      padding: 2px;
      font-size: 14px!important;
    }
  }
}
.el-table--border .el-table__cell:first-child .cell{
  padding-left: 7px;
  padding-right: 0;
}
.el-table-column--selection .cell{
  padding: 0 0 0 7px!important;
}
.el-table--small .el-table__cell {
  padding: 0;
}
.el-checkbox__inner{
  width: 16px;
  height: 16px;
  &::after{
    height: 8px;
    left: 6px;
  }
}

.vxe-cell--checkbox::before{
  content: '';
  position: absolute;
  top: 0; right: 1px;
  bottom: 1px; left: -2px;
}
.vxe-cell--checkbox{
  .vxe-checkbox--icon{
    font-size: 16px!important;
  }
}


.vxe-header--column{
  height: 25px!important;
  color: #606266;
  font-weight: 400;
  background: #e9edf0;
  font-size: 12px;
}
.vxe-body--column{
  font-size: 14px;
  color: #606266;
  height: 34px!important;

}
.vxe-footer--column{
  font-size: 14px;
  color: #606266;
  height: 34px!important;
  padding: 0!important;
}
.vxe-table--render-default .vxe-cell{
  padding: 0 2px;
}
.vxe-table--render-default .vxe-body--column:not(.col--ellipsis){
  padding: 0;
}

.el-dialog__header{
  padding: 9px 20px;
  border-bottom: 1px solid #dcdfe6;
  .el-dialog__title{
    font-size: 16px;
  }
  .el-dialog__headerbtn{
    top: 13px;
  }
  .el-dialog__headerbtn::before{
    content: '';
    position: absolute;
    top: -5px; right: -5px;
    bottom: -5px; left: -5px;
  }
}
//查看附件
.upload-show{
  .el-upload--text{
    display: none;
  }
}
.el-dialog__body{
  padding:5px 20px
}

.uploadBtn{
  padding: 5px;
}
.upload{
  font-size: 12px;
  p{
    margin-block-start:5px;
    margin-block-end:5px;

  }
}
// 底部按钮
.fixedBottom {
  position: fixed;
  left:  var(--fixedButtonLeft);
  transform: translateX(-50%);
  bottom: 20px;
  display: flex;
  justify-content: center;
  z-index: 1099;
  transition: left 0.3s ease-in-out;
}


// 选中树节点颜色
.el-tree-node:focus > .el-tree-node__content{
background: #e6faff !important;
}

.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content{
  background: #e6faff !important;
}
