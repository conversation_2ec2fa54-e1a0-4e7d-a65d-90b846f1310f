import { decrypt, encrypt } from '@/utils/jsencrypt'

const AccessTokenKey = 'ACCESS_TOKEN'
const RefreshTokenKey = 'REFRESH_TOKEN'
// 被模拟账户的用户token
const MockUserTokenKey = 'MOCK_USER_TOKEN'
const PASSWORD_EXPIRED_TIME = 'PASSWORD_EXPIRED_TIME'
const CUSTOMLIST = 'customList'

// ========== Token 相关 ==========

export function getAccessToken() {
  return localStorage.getItem(AccessTokenKey)
}

export function getRefreshToken() {
  return localStorage.getItem(RefreshTokenKey)
}

export function getMockUserToken() {
  return localStorage.getItem(MockUserTokenKey)
}

export function setToken(token) {
  localStorage.setItem(AccessTokenKey, token.accessToken)
  localStorage.setItem(RefreshTokenKey, token.refreshToken)
}

export function setPasswordExpiredTime(passwordExpiredTime) {
  localStorage.setItem(PASSWORD_EXPIRED_TIME, passwordExpiredTime)
}

// 由系统用户-管理员-模拟账户时切换账户调用
export function setMockUserToken(token) {
  localStorage.setItem(MockUserTokenKey, token.accessToken)
  localStorage.setItem(AccessTokenKey, token.accessToken)
  localStorage.setItem(RefreshTokenKey, token.refreshToken)
}

export function removeToken() {
  localStorage.removeItem(AccessTokenKey)
  localStorage.removeItem(PASSWORD_EXPIRED_TIME)
  localStorage.removeItem(RefreshTokenKey)
  localStorage.removeItem('COMPANY_ID')
  if (getMockUserToken()) {
    localStorage.removeItem(MockUserTokenKey)
  }
}

export function setCustomList(customList) {
  localStorage.setItem(CUSTOMLIST, customList)
}

// ========== 账号相关 ==========

const UsernameKey = 'USERNAME'
const PasswordKey = 'PASSWORD'
const RememberMeKey = 'REMEMBER_ME'

export function getUsername() {
  return localStorage.getItem(UsernameKey)
}

export function setUsername(username) {
  localStorage.setItem(UsernameKey, username)
}

export function removeUsername() {
  localStorage.removeItem(UsernameKey)
}

export function getPassword() {
  const password = localStorage.getItem(PasswordKey)
  return password ? decrypt(password) : undefined
}

export function setPassword(password) {
  localStorage.setItem(PasswordKey, encrypt(password))
}

export function removePassword() {
  localStorage.removeItem(PasswordKey)
}

export function getRememberMe() {
  return localStorage.getItem(RememberMeKey) === 'true'
}

export function setRememberMe(rememberMe) {
  localStorage.setItem(RememberMeKey, rememberMe)
}

export function removeRememberMe() {
  localStorage.removeItem(RememberMeKey)
}

// ========== 租户相关 ==========

const TenantIdKey = 'TENANT_ID'
const TenantNameKey = 'TENANT_NAME'

export function getTenantName() {
  return localStorage.getItem(TenantNameKey)
}

export function setTenantName(username) {
  localStorage.setItem(TenantNameKey, username)
}

export function removeTenantName() {
  localStorage.removeItem(TenantNameKey)
}

export function getTenantId() {
  return localStorage.getItem(TenantIdKey)
}

export function setTenantId(username) {
  localStorage.setItem(TenantIdKey, username)
}

export function getCompanyId() {
  return localStorage.getItem('COMPANY_ID')
}

export function setCompanyId(ID) {
  localStorage.setItem('COMPANY_ID', ID)
}

export function removeTenantId() {
  localStorage.removeItem(TenantIdKey)
}
