/**
 * Created by 芋道源码
 *
 * 数据字典工具类
 */
import store from '@/store'

export const DICT_TYPE = {
  USER_TYPE: 'user_type',
  COMMON_STATUS: 'common_status',
  COMMON_CATEGORY: 'category',
  COMMON_CURRENCY: 'currency',
  COMMON_COUNTRY: 'region',
  // 采购组织
  COMMON_PURCHASEORG: 'purchaseOrg',
  COMMON_COMPANY: 'company',
  COMMON_USERS: 'userList',
  COMMON_USERS_INCLUDES_ALL: 'userList_include_all', // 全部用户，包含禁用的信息
  COMMON_FACTORY: 'factory',
  COMMON_FACTORY_WITH_AUTH: 'factory_with_auth', // 带当前用户权限的工厂数据（目前工厂没有直接到人的关系，需要走采购组中转下。 人到采购组织；采购组织到人）
  COMMON_DEPT: 'dept',
  SRM_OPEN_HANDLE_STATUS: 'srm_open_handle_status',
  // ========== SYSTEM 模块 ==========
  SYSTEM_USER_SEX: 'system_user_sex',
  SYSTEM_MENU_TYPE: 'system_menu_type',
  SYSTEM_ROLE_TYPE: 'system_role_type',
  // 月份数据
  SYSTEM_MONTH: 'system_month',
  // 国家地区类型
  SYSTEM_COUNTRY_REGION_TYPE: 'system_country_region_type',
  SYSTEM_ROLE_IS_EXTERNAL: 'system_role_is_external',
  SYSTEM_DATA_SCOPE: 'system_data_scope',
  SYSTEM_NOTICE_TYPE: 'system_notice_type',
  SYSTEM_OPERATE_TYPE: 'system_operate_type',
  SYSTEM_LOGIN_TYPE: 'system_login_type',
  SYSTEM_LOGIN_RESULT: 'system_login_result',
  SYSTEM_SMS_CHANNEL_CODE: 'system_sms_channel_code',
  SYSTEM_SMS_TEMPLATE_TYPE: 'system_sms_template_type',
  SYSTEM_SMS_SEND_STATUS: 'system_sms_send_status',
  SYSTEM_SMS_RECEIVE_STATUS: 'system_sms_receive_status',
  SYSTEM_MSG_TEMPLATE_TYPE: 'system_msg_template_type',
  SYSTEM_MSG_LOG_SEND_STATUS: 'system_msg_log_send_status',
  SYSTEM_ERROR_CODE_TYPE: 'system_error_code_type',
  SYSTEM_OAUTH2_GRANT_TYPE: 'system_oauth2_grant_type',
  SYSTEM_CUSTOM_FORM_COLUMN_TYPE: 'system_custom_form_column_type',
  SYSTEM_DEPT_TYPE: 'system_dept_type',
  // 站内信模版的类型
  SYSTEM_NOTIFY_TEMPLATE_TYPE: 'system_notify_template_type',
  // 邮件发送状态
  SYSTEM_MAIL_SEND_STATUS: 'system_mail_send_status',
  SYSTEM_FILE_BUSINESS_TYPE: 'system_file_business_type',
  SUPPLIER_STATUS: 'supplier_status',
  COMMON_Y_N: 'common_y_n',
  COMMON_DATA_SOURCES: 'common_data_sources',
  SYSTEM_MAIL_REMIND_NAME: 'system_mail_remind_name',
  SYSTEM_MAIL_REMIND_FREQUENCY_TYPE: 'system_mail_remind_frequency_type',
  SYSTEM_MAIL_REMIND_TYPE: 'system_mail_remind_type',
  SYSTEM_MODULE_REPORT_REL_CONFIG: 'system_module_report_rel_config', // 模块之间关联报表配置项
  // 采购组
  SYSTEM_PURCHASE_GROUP: 'system_purchase_group',
  /**
   * 1号到31号下拉
   */
  SYSTEM_DATE: 'system_date',
  /**
   * 星期一到星期日的下拉
   */
  SYSTEM_WEEK: 'system_week',
  SYSTEM_USER_HANDOFFS_OPERATION_TYPE: 'system_user_handoffs_operation_type',
  SYSTEM_USER_DOCUMENT_HANDOFFS: 'system_user_document_handoffs',
  SYSTEM_USER_PENDING_HANDOFFS: 'system_user_pending_handoffs',
  SYSTEM_USER_CONFIGURE_HANDOFFS: 'system_user_configure_handoffs',
  SYSTEM_COUNTRY_REGION: 'system_country_region',
  SYSTEM_CATEGORY_TYPE:'system_category_type',

  // ========== INFRA 模块 ==========
  INFRA_BOOLEAN_STRING: 'infra_boolean_string',
  INFRA_REDIS_TIMEOUT_TYPE: 'infra_redis_timeout_type',
  INFRA_JOB_STATUS: 'infra_job_status',
  INFRA_JOB_LOG_STATUS: 'infra_job_log_status',
  INFRA_API_ERROR_LOG_PROCESS_STATUS: 'infra_api_error_log_process_status',
  INFRA_CONFIG_TYPE: 'infra_config_type',
  INFRA_CODEGEN_TEMPLATE_TYPE: 'infra_codegen_template_type',
  INFRA_CODEGEN_SCENE: 'infra_codegen_scene',
  INFRA_FILE_STORAGE: 'infra_file_storage',

  // ========== BPM 模块 ==========
  BPM_MODEL_CATEGORY: 'bpm_model_category',
  BPM_MODEL_FORM_TYPE: 'bpm_model_form_type',
  BPM_TASK_ASSIGN_RULE_TYPE: 'bpm_task_assign_rule_type',
  BPM_PROCESS_INSTANCE_STATUS: 'bpm_process_instance_status',
  BPM_PROCESS_INSTANCE_RESULT: 'bpm_process_instance_result',
  BPM_TASK_ASSIGN_SCRIPT: 'bpm_task_assign_script',
  BPM_OA_LEAVE_TYPE: 'bpm_oa_leave_type',

  // ========== PAY 模块 ==========
  PAY_CHANNEL_WECHAT_VERSION: 'pay_channel_wechat_version', // 微信渠道版本
  PAY_CHANNEL_ALIPAY_SIGN_TYPE: 'pay_channel_alipay_sign_type', // 支付渠道支付宝算法类型
  PAY_CHANNEL_ALIPAY_MODE: 'pay_channel_alipay_mode', // 支付宝公钥类型
  PAY_CHANNEL_ALIPAY_SERVER_TYPE: 'pay_channel_alipay_server_type', // 支付宝网关地址
  PAY_CHANNEL_CODE_TYPE: 'pay_channel_code_type', // 支付渠道编码类型
  PAY_ORDER_NOTIFY_STATUS: 'pay_order_notify_status', // 商户支付订单回调状态
  PAY_ORDER_STATUS: 'pay_order_status', // 商户支付订单状态
  PAY_ORDER_REFUND_STATUS: 'pay_order_refund_status', // 商户支付订单退款状态
  PAY_REFUND_ORDER_STATUS: 'pay_refund_order_status', // 退款订单状态
  PAY_REFUND_ORDER_TYPE: 'pay_refund_order_type', // 退款订单类别

  // ========== MATERIAL 模块 ==========
  MATERIAL_MAKE_OR_BUY: 'material_make_or_buy',
  MATERIAL_UOM: 'material_uom',
  MATERIAL_UOM_LENGTH: 'material_uom_length',
  MATERIAL_UOM_VOLUME: 'material_uom_volume',
  MATERIAL_PURCHASE_TYPE: 'material_purchase_type',
  MATERIAL_TYPE: 'material_type',
  MATERIAL_COUNTRY_ORIGIN: 'material_country_origin',
  MATERIAL_TIME_TYPE: 'material_time_type',
  MATERIAL_TRANSACTION_MODE: 'material_transaction_mode',
  MATERIAL_ABC_LOGO: 'material_abc_logo',
  MATERIAL_KEY_MATERIAL: 'material_key_material',
  MATERIAL_LIFE_CYCLE: 'material_life_cycle',
  MATERIAL_BUSINESS_UNIT_PROPERTIES: 'material_business_unit_properties',
  MATERIAL_ROHS_MATERIAL_PROPERTIES: 'material_rohs_material_properties',
  MATERIAL_GENERALITY: 'material_generality',

  // ========== SUPPLIER 模块 ==========
  SUPPLIER_TYPE: 'supplier_type',
  SUPPLIER_WITH_SUPPLIER_REL: 'supplier_with_supplier_rel',
  SUPPLIER_CONTACT_DIVISION: 'supplier_contact_division',
  SUPPLIER_RATE: 'supplier_rate',
  SUPPLIER_PAYMENT_DAYS: 'supplier_payment_days',
  SUPPLIER_DELIVERY_CONDITION: 'supplier_delivery_condition',
  SUPPLIER_TERMS_OF_PAYMENT: 'supplier_terms_of_payment',
  SUPPLIER_INVOICE_TYPE: 'supplier_Invoice_type',
  SUPPLIER_FEE_BEARER: 'supplier_fee_bearer',
  SUPPLIER_BUILDING_PROPERTY_RIGHT: 'supplier_building_property_right',
  SUPPLIER_COMPANY_NATURE: 'supplier_company_nature',
  SUPPLIER_CURRENCY_UNIT: 'supplier_currency_unit',
  SUPPLIER_CLEAN_ROOM_CLASS: 'supplier_clean_room_class',
  SUPPLIER_PRODUCT_TYPE: 'supplier_product_type',
  SUPPLIER_STAFF: 'supplier_staff',
  SUPPLIER_SPECIAL_WORK_QUALIFICATION_CERTIFICATE: 'supplier_special_work_qualification_certificate',
  SUPPLIER_QUALIFICATIONS_OF_DEVELOPER: 'supplier_qualifications_of_developer',
  SUPPLIER_SALE_AREA: 'supplier_sale_area',
  SUPPLIER_HOMEMADE_OUTSOURCED: 'supplier_homemade_outsourced',
  SUPPLIER_OWNERSHIP_OF_INTELLECTUAL_PROPERTY: 'supplier_ownership_of_intellectual_property',
  QUALIFICATION_AND_CERTIFICATION: 'qualification_and_certification',
  ENTERPRISE_MANAGEMENT_SYSTEM: 'enterprise_management_system',
  QUALIFICATION_AND_CERTIFICATION_RESULT: 'qualification_and_certification_result',
  SUPPLIER_PAYMENT_TERM: 'supplier_payment_term',
  SUPPLIER_CONTROLLER_ACCOUNT: 'supplier_controller_account',
  SUPPLIER_TIER_LEVEL: 'supplier_tier_level',
  SUPPLIER_UNIQUE_VALIDATION: 'supplier_unique_validation',
  SUPPLIER_UPLOAD_TYPE: 'supplier_upload_type',
  SUPPLIER_LEVEL_STATUS: 'supplier_level_status',
  SUPPLIER_CHANGE_RECORD_STATUS: 'supplier_change_record_status',
  SYSTEM_COUNTRY_TYPE: 'system_country_type',
  // 供应商发起信息变更申请得类型
  SUPPLIER_CHANGE_TYPE: 'supplier_change_type',
  // ========== ORDER 模块 ==========
  ORDER_TYPE: 'order_type',
  ORDER_STATUS: 'order_status',
  ORDER_DETAIL_STATUS: 'order_detail_status',
  ORDER_DETAIL_DELIVERY_STATUS: 'order_detail_delivery_status',
  ORDER_DETAIL_RECEIPT_STATUS: 'order_detail_receipt_status',
  ORDER_RECEIPT_TYPE: 'order_receipt_type',
  ORDER_DATE_TYPE_IN_ORDER: 'order_date_type_in_order',
  ORDER_DATE_TYPE_IN_ORDER_DELIVERY: 'order_date_type_in_order_delivery',
  ORDER_DATE_TYPE_IN_ANSWER: 'order_date_type_in_answer',
  ORDER_PROCESS_MARK: 'order_process_mark',
  ORDER_ANSWER_STATUS: 'order_answer_status',
  Order_ANSWER_DIFFERENCE: 'order_answer_difference',
  ORDER_OVERDUE_TYPE: 'order_overdue_type',
  ORDER_LINE_TYPE: 'order_line_type',
  ORDER_DELIVERY_STATUS: 'order_delivery_status',
  ORDER_SHIPPING_METHOD: 'order_shipping_method',
  ORDER_LINE_ANSWER_FINISH_STATUS: 'order_line_answer_finish_status', // 订单行答交完成状态
  ORDER_GOOD_RECEIVING_STATUS: 'order_good_receiving_status', // 订单收退货数据状态(对账状态）
  ORDER_SIGN_STATUS: 'order_sign_status', // 订单确认回签功能#订单签收状态
  ORDER_DETAIL_CONFIRM_STATUS: 'order_detail_confirm_status', // 订单行交期确认状态 (订单确认回签功能#订单行交期确认状态)
  ORDER_CONFIRM_FILE_SOURCE: 'order_confirm_file_source', // 订单行交期确认状态 (订单确认回签功能#订单行交期确认状态)
  ORDER_OVERDUE_AND_ONTIME_CONFIG_TYPE: 'order_overdue_and_ontime_config_type', // 订单行交期确认状态 (订单确认回签功能#订单行交期确认状态)

  // ========== AVPL 模块 ==========
  AVPL_STATUS: 'avpl_status',
  AVPL_DATE_TYPE: 'avpl_date_type',
  AVPL_SOURCE: 'avpl_source',
  // ========== BOM 模块 ==========
  BOM_PROJECT_STATUS: 'bom_project_status', // project status
  BOM_PROJECT_MATERIAL_STATUS: 'bom_material_status', // project material status
  BOM_MATERIAL_LEVEL: 'bom_material_level', // project material level
  BOM_STEP_TYPE: 'bom_step_type', // bom step
  // ========== RFQ 模块 ==========
  RFQ_BUSINESS_TYPE: 'rfq_business_type',
  RFQ_DATE_TYPE: 'rfq_date_type',
  RFQ_MATERIAL_DATE_TYPE: 'rfq_material_date_type',
  RFQ_STEP_STATUS: 'rfq_step_status',
  RFQ_STEP_TYPE: 'rfq_step_type',
  RFQ_ACTION: 'rfq_action',
  RFQ_RECOMMEND_PRINCIPLE: 'rfq_recommend_principle',
  RFQ_MATERIAL_STATUS: 'rfq_material_status',
  RFQ_MATERIAL_STATUS_SEARCH: 'rfq_material_status_search',
  RFQ_INQUIRY_PURPOSE: 'rfq_inquiry_purpose',
  RFQ_APPROVAL_STATUS: 'rfq_approval_status',
  RFQ_APPROVAL_MATERIAL_STATUS: 'rfq_approval_material_status',
  RFQ_QUOTATION_STATUS: 'rfq_quotation_status',
  RFQ_TARGET_PRICE_TAG: 'rfq_target_price_tag',
  RFQ_QUOTATION_MATERIAL_STATUS: 'rfq_quotation_material_status',
  RFQ_SOURCE_OF_INQUIRY: 'rfq_source_of_inquiry',
  RFQ_TIME_TYPE: 'rfq_time_type',
  RFQ_PROJECT_STATUS: 'rfq_project_status',
  RFQ_QUOTATION_PERSPECTIVE_DATE_TYPE: 'rfq_quotation_perspective_date_type',
  RFQ_APPROVAL_DATE_TYPE: 'rfq_approval_date_type',
  RFQ_APPROVAL_FLAG: 'rfq_approval_flag',
  RFQ_SOURCING_SOURCES: 'rfq_sourcing_sources',
  RFQ_PRICE_CATEGORY: 'rfq_price_category',
  RFQ_PROCESSING_TECHNOLOGY: 'rfq_processing_technology',
  RFQ_SURFACE_TREATMENT_PROCESS_METAL: 'rfq_surface_treatment_process_metal',
  RFQ_HEAT_TREATMENT_PROCESS: 'rfq_heat_treatment_process',
  RFQ_SURFACE_TREATMENT_PROCESS_PLASTIC_PARTS: 'rfq_surface_treatment_process_plastic_parts',
  RFQ_PIT_PAPER_GRAY_BOARD: 'rfq_pit_paper_gray_board',
  RFQ_SURFACE_TREATMENT_PROCESS_PCB: 'rfq_surface_treatment_process_pcb',
  RFQ_SIMULATION_PROGRAM: 'rfq_simulation_program',
  RFQ_OA_PRICE_APPROVAL_NO: 'rfq_oa_price_approval_no',
  RFQ_PRICE_TYPE: 'rfq_price_type',
  RFQ_BUSINESS_MODEL: 'rfq_business_model',
  RFQ_ORDER_FREQUENCY:'rfq_order_frequency',
  // ========== AUTH 模块 ==========
  AUTH_SPOT_CHECK_COMMON_TYPE: 'auth_spot_check_common_type', // 认证模块-现场审核评分大类
  AUTH_SUPPLIER_CHANGE_TYPE: 'auth_supplier_change_type', // 认证变更#变更方式
  AUTH_SUPPLIER_DOC_REPOSITORY_ENTRY_TYPE: 'auth_supplier_docRepository_entry_type', // 认证模块#文档库文件夹指定入口关系字典
  AUTH_SUPPLIER_STATUS: 'auth_supplier_status',
  AUTH_SUPPLIER_UPLOAD_BUSINESS_TYPE: 'auth_supplier_upload_business_type',
  AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY: 'auth_supplier_set_up_standard_category',
  AUTH_SUPPLIER_SET_UP_STANDARD_FOLDER_TYPE_REL: 'auth_supplier_set_up_standard_folder_type_rel',
  AUTH_SUPPLIER_SET_UP_STANDARD_SYSTEM_RECOMMEND_PROCESS: 'auth_supplier_set_up_standard_system_recommend_process',
  AUTH_SUPPLIER_SET_UP_STANDARD_SYSTEM_USE_PROCESS: 'auth_supplier_set_up_standard_system_use_process',
  AUTH_SUPPLIER_CHECK_RESULT_TYPE: 'auth_supplier_check_result_type',
  AUTH_SUPPLIER_REVIEWER_STATUS: 'auth_supplier_reviewer_status',
  AUTH_SUPPLIER_SPOT_CHECK_SCORE_ITEM: 'auth_supplier_spot_check_score_item', // 现场审核得分项配置
  AUTH_SUPPLIER_SPOT_CHECK_SCORE_ITEM_V2: 'auth_supplier_spot_check_score_item_v2', // 现场审核得分项配置
  AUTH_SUPPLIER_SPOT_CHECK_NOT_MATCH_ITEM_QUANTITY: 'auth_supplier_spot_check_not_match_item_quantity',
  AUTH_SUPPLIER_DATE_TYPE: 'auth_supplier_date_type',
  AUTH_SUPPLIER_MANUFACTURER_NATURE: 'auth_supplier_manufacturerNature', // 厂商性质
  AUTH_SUPPLIER_SUPPLIER_LEVEL: 'auth_supplier_supplier_level', // 认证模块#供应商认证变更#供应商级别
  SUPPLIER_ACCOUNT_GROUP: 'supplier_account_group', // 供应商账户组
  AUTH_SUPPLIER_SUPPLIER_GRADE: 'auth_supplier_supplier_grade', // 供应商等级（属于公司对应关联的字段信息）
  AUTH_SUPPLIER_TYPE: 'auth_supplier_type', // 认证单据类型（新认证单据、变更单据）
  AUTH_SUPPLIER_TEMP_SUPPLIER_LOGIN_TYPE: 'auth_supplier_temp_supplier_login_type', // 认证单据类型（新认证单据、变更单据）
  AUTH_SUPPLIER_OPERATE_TYPE: 'auth_supplier_operate_type', // 认证模块操作类型
  BASIC_CLASSIFICATION: 'basic_classification', // 认证模块准入申请供应商基本类别
  REGIONAL_CLASSIFICATION: 'regional_classification', // 认证模块准入申请地区类别
  AUTH_SUPPLIER_CREDIT_STATUS: 'auth_supplier_credit_status',
  AUTH_SUPPLIER_SCORE_CHECK_STATUS: 'auth_supplier_score_check_status',
  AUTH_SPOT_CHECK_SCORE_QUESTION_OPTION: 'auth_spot_check_score_question_option',
  AUTH_SPOT_CHECK_SCORE_QUESTION_INCONSISTENT_OPTION: 'auth_spot_check_score_question_inconsistent_option',
  AUTH_SPOT_CHECK_QUESTION_COMPLEX_OPTION: 'auth_spot_check_question_complex_option',
  AUTH_SPOT_CHECK_QUESTION_SIMPLE_OPTION: 'auth_spot_check_question_simple_option',
  SUPPLIER_MAN_MAIN_RESPONSIBILITY: 'supplier_man_main_responsibility',
  SUPPLIER_QUALITY_SYSTEM: 'supplier_quality_system',
  SUPPLIER_INDUSTRY: 'supplier_industry',
  SUPPLIER_LEAN_PROCESS: 'supplier_lean_process',
  SUPPLIER_SEF_DESC_IMPRESSION: 'supplier_sef_desc_impression',

  // ========== SCAR 模块 ==========
  SCAR_SEVERITY_DEGREE: 'scar_severity_degree',
  SCAR_SEVERITY_LEVEL: 'scar_severity_level', // SCAR-SA-严重等级
  SCAR_STATUS: 'scar_status', // SCAR单据状态
  SCAR_BUSINESS_TYPE: 'scar_business_type', // SCAR单据业务类型
  SCAR_RECORD_TYPE: 'scar_record_type', // SCAR单据类型
  SCAR_DEFECT_TYPE: 'scar_defect_type', // SCAR不良类型（类型配置）
  SCAR_DEFECT_SOURCE: 'scar_defect_source', // SCAR问题来源（字典）
  SCAR_LOCATION: 'scar_location', // SCAR物料所在位置
  SCAR_INSPECTION_METHOD: 'scar_inspection_method', // SCAR-IQC-检验方式
  SCAR_QUANTITY_COMPLAIN_TYPE: 'scar_quantity_complain_type', // SCAR-质量投诉类型
  SCAR_IQC_SC_PUBLISH_TYPE: 'scar_iqc_sc_publish_type', // SCAR-IQC-SC-发布的单据类型
  SCAR_OPERATE_STEP: 'scar_operate_step', // 用于质量单据的操作记录#操作节点
  SCAR_OPERATE_TYPE: 'scar_operate_type', // 用于质量单据的操作记录#操作类型
  SCAR_TIME_TYPE: 'scar_time_type', // SCAR-总览列表搜索时间类型
  SCAR_ON_TIME: 'scar_on_time', // SCAR-总览列表-及时/延迟
  SCAR_SEE_RECORD_TYPE: 'scar_see_record_type', // SCAR-总览列表-查看单据类型

  // ========== 绩效 模块 ==========
  SP_RUN_FREQUENCY: 'sp_run_frequency', // 运行频率月/季
  SP_CATEGORY_LEVEL: 'sp_category_level', // 绩效品类汇总等级
  SP_INVOLVED: 'sp_involved', //  绩效参与
  SP_PI_CATEGORY: 'sp_pi_category', // 指标类别
  SP_UPLOAD_FILE_SOURCE: 'sp_upload_file_source', // 绩效原始文件上传来源
  SP_UPLOAD_PROCESS_STATUS: 'sp_upload_process_status', // 绩效原始文件处理状态
  SP_INFLUENCE_SPHERE: 'sp_influence_sphere', // 影响范围
  SP_SERVE: 'sp_serve', // 服务
  SP_FLEXIBILITY: 'sp_flexibility', // 灵活性
  SP_PRODUCT_INNOVATION: 'sp_product_innovation', // 产品创新
  SP_RESPONDING_SPEED: 'sp_responding_speed', // 响应速度
  SP_QUALITY: 'sp_quality', // 绩效质量
  SP_COST: 'sp_cost', // 绩效成本
  SP_PERFORMANCE_DIRECTION: 'sp_performance_direction', // 绩效方向
  SP_OPERATOR_SYMBOL: 'sp_operator_symbol', // 操作符
  SCORING_STANDARD_TYPE: 'scoring_standard_type', // 评分标准类型
  SP_RUN_TYPE: 'sp_run_type',
  SP_RUN_STATUS: 'sp_run_status',
  SP_FIELD_DATA_TYPE: 'sp_field_data_type',
  SP_COMPONENTS_TYPE: 'sp_components_type',
  SP_GRADE: 'sp_grade',
  SP_DATA_EXPORT_STATUS: 'sp_data_export_status', // 绩效源数据文件生成状态
  SP_NO_EVENT_SCORE: 'sp_no_event_score', // 未发生事件得分
  // ========== 分级 模块 ==========
  /**
   * 分级的绩效级别
   */
  SL_PERFORMANCE_LEVEL: 'sl_performance_level',
  /**
   * 分级的级别
   */
  SL_SUPPLIER_LEVEL: 'sl_supplier_level',
  SL_ADJUSTMENT_TYPE: 'sl_adjustment_type',
  /**
   * 当期评级状态
   */
  SL_CURRENT_RATING_STATUS: 'sl_current_rating_status',
  /**
   * 分级审批单状态
   */
  SL_SUPPLIER_APPLICATION_STATUS: 'sl_supplier_application_status',

  // ========== 招标模块 ==========
  TENDER_ANSWER_TYPE: 'tender_answer_type', // 自定义标书回答属性
  TENDER_PROJECT_CLASS: 'tender_project_class', // 招投标项目类别
  TENDER_PROJECT_STATUS: 'tender_project_status', // 招标项目单据状态
  TENDER_TIME_TYPE: 'tender_time_type', // 招投标时间类型
  TENDER_DOCUMENT_TYPE: 'tender_document_type',
  TENDER_BID_OPENING_TYPE: 'tender_bid_opening_type',
  TENDER_BID_STATUS: 'tender_bid_status',
  TENDER_BID_NOTICE: 'tender_bid_notice',
  // ========== 首页 模块 ==========
  OV_MODULE_SUPPLIER_TYPE: 'ov_module_supplier_type',

  // ========== 财务协同 模块 ==========
  /**
   * 基准日类型
   */
  FINANCIAL_DATUM_DATE_TYPE: 'financial_datum_date_type',
  /**
   * 付款日类型
   */
  FINANCIAL_PAYMENT_DATE_TYPE: 'financial_payment_date_type',
  ADDITION_AND_SUBTRACTION_ITEM: 'addition_and_subtraction_item',
  FINANCIAL_BUSINESS_TYPE: 'financial_business_type',
  PAYMENT_APPLICATION_TYPE: 'payment_application_type',

  // =======竞标============
  BIDDING_DATE_TYPE: 'bidding_date_type',
  BIDDING_PRINCIPLE: 'bidding_principle',
  BIDDING_STATUS: 'bidding_status',
  BIDDING_SUPPLIER_RESPONSE_STATUS: 'bidding_supplier_response_status',
  BIDDING_SEND_TYPE: 'bidding_send_type',
  // =======验货台账============
  LEDGER_VEHICLE_CONDITION: 'ledger_vehicle_condition', // 验货台账模块-车辆状况
  LEDGER_NON_CONFORMITY_HANDLING: 'ledger_non_conformity_handling', // 验货台账模块-不合格处理
  LEDGER_ACCEPTANCE_RESULT: 'ledger_acceptance_result', // 验货台账模块-判定结果
  LEDGER_REPORT_DETAIL_TYPE: 'ledger_report_detail_type', // 验货台账模块-报告明细类型
  LEDGER_APPEARANCE: 'ledger_appearance', // 验货台账模块-符合与不符合(选项含--)
  LEDGER_STATUS: 'ledger_status', // 验货台账模块-状态
  /**
   * 报告模板
   */
  BIDDING_REPORT_TEMPLATE: 'bidding_report_template',
  /**
   * 选配范围
   */
  BIDDING_OPTIONAL_RANGE: 'bidding_optional_range',
  BULK_SUPPLIER_CALIBRATION: 'bulk_supplier_calibration',

  BIDDING_ACCEPTANCE_METHOD: 'bidding_acceptance_method',

  BIDDING_OA_APPROVE_STATUS: 'bidding_oa_approve_status',
  // =======TQCDM============
  TQCDM_TQCDM_EVALUATE_ITEM_STATUS: 'tqcdm_tqcdm_evaluate_item_status', // tqcdm评估类型状态
  TQCDM_TQCDM_EVALUATE_PROJECT_STATUS: 'tqcdm_tqcdm_evaluate_project_status', // tqcdm评估项目状态
  TQCDM_TQCDM_EVALUATE_SEARCH_DATE_TYPE: 'tqcdm_tqcdm_evaluate_search_date_type', // tqcdm评估项目列表-时间类型
  TQCDM_TQCDM_TYPE: 'tqcdm_type', // tqcdm评估类型
  TQCDM_TQCDM_EVALUATE_RESULT: 'tqcdm_tqcdm_evaluate_result', // tqcdm评估类型

  /**
   * BOM物料状态
   */
  BOM_MATERIAL_STATUS: 'bom_material_status',
  BOM_PRIORITY_RECOMMEND_PRICE: 'bom_priority_recommend_price',
  BOM_SCHEME_COST: 'bom_scheme_cost',
  BOM_PRICE_SOURCE: 'bom_price_source',
  BOM_SCHEME_APPROVAL_STATUS: 'bom_scheme_approval_status',
  BOM_SCHEME_APPROVAL_CONCLUSION: 'bom_scheme_approval_conclusion',
  BOM_SCHEME_LATEST: 'bom_scheme_latest',
  // =======PPV============

  // =======INCAP order tracker============
  INCAP_CUSTOMER: 'om_incap_customer',


  /**
   * PPV的包装方式
   */
  PPV_PACKAGE: 'ppv_package',
  /**
   * PPV的来源
   */
  PPV_SOURCE: 'ppv_source',
  /**
   * PPV的项目状态
   */
  PPV_PROJECT_STATUS: 'ppv_project_status',
  /**
   * PPV的物料状态
   */
  PPV_MATERIAL_SUPPLIER_STATUS: 'ppv_material_supplier_status',
  /**
   * PPV的反馈结果
   */
  PPV_ACTIONS: 'ppv_actions',
  /**
   * 时间类型
   */
  PPV_TIME_TYPE: 'ppv_time_type',
  // =======PPV============
  /**
   * 销售订单的状态
   */
  OM_SO_STATUS: 'om_so_status',
  /**
   * OM的搜索条件Paid%
   */
  OM_SEARCH_PAID: 'om_search_paid',
  /**
   * OM的搜索条件时间类型
   */
  OM_SO_MASTER_STATUS: 'om_so_master_status',
  OM_SO_DETAIL_STATUS: 'om_so_detail_status',
  OM_CHECK_AVPL_RESULT: 'om_check_avpl_result',
  OM_SHIP_TO: 'om_ship_to',
  OM_SEARCH_TIME_TYPE: 'om_search_time_type',
  /**
   * OM的AVPL状态
   */
  OM_AVPL_STATUS:'om_avpl_status',
  OM_INCAP_CUSTOMER:'om_incap_customer',
  /**
   * Buyer
   */
  OM_BUYER: 'om_buyer',
  /**
   * OM装箱单的状态
   */
  OM_PL_STATUS:"om_pl_status",

  /**
   * represent the parts of the sales order that whether overdue
   */
  OM_PASS_DUE: 'om_pass_due',
  /**
   * delivery type
   */
  OM_DELIVERY_TYPE:'om_delivery_type',
  /**
   * express type
   */
  OM_EXPRESS_TYPE:"om_express_type",
  /**
   * ship type
   */
  OM_SHIP_TYPE:'om_ship_type',
  /**
   * bonded
   */
  OM_BONDED:'om_bonded',
  /**
   * parts is in use
   */
  OM_IN_USE:"om_in_use",
  /**
   * delivery plan status
   */
  OM_DP_STATUS:"om_dp_status",

  /**
   * 缺料计划状态
   */
  OM_SHORT_STATUS:'om_short_status',
  /**
   * 缺料物料状态
   */
  OM_SHORT_PART_STATUS:'om_short_part_status',
  /**
   * 缺料月份
   */
  OM_MONTH:'om_month',
  /**
   * 差额搜索下拉框
   */
  OM_SHORT_GAP:'om_short_gap',
  /**
   * 物料类型：样品物料和正式物料
   */
  OM_MATERIAL_TYPE:"om_material_type",
  TRANSACTION_TYPE:'transaction_type',
  FUNDS_USAGE:'funds_usage',
  OM_AP_STATUS:'om_ap_status',


}

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @param status 全部数据 false  其他值只获取启用数据
 * @returns {*|Array} 数据字典数组
 */
export function getDictDatas(dictType, status) {
  // 当传入false的时候，获取全部数据，其他清空获取启用数据
  if (status === false) {
    return store.getters.dict_datas[dictType] || []
  }
  // 只返回启用状态的数据，如果要获取所有数据，使用getAllDictData方法
  return store.getters.dict_datas[dictType]?.filter(item => item?.status === undefined || item?.status === 0) || []
}
/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @param values 数组、单个元素
 * @returns {*|Array} 数据字典数组
 */
export function getDictDatas2(dictType, values) {
  if (values === undefined) {
    return []
  }
  // 如果是单个元素，则转换成数组
  if (!Array.isArray(values)) {
    values = [this.value]
  }
  // 获得字典数据
  const results = []
  for (const value of values) {
    const dict = getDictData(dictType, value)
    if (dict) {
      results.push(dict)
    }
  }
  return results
}

export function getDictData(dictType, value) {
  // 获取 dictType 对应的数据字典数组
  const dictDatas = getDictDatas(dictType, false)

  if (!dictDatas || dictDatas.length === 0) {
    return ''
  }
  // 获取 value 对应的展示名
  value = value + '' // 强制转换成字符串，因为 DictData 小类数值，是字符串
  for (const dictData of dictDatas) {
    // 获取 dictType 对应的数据字典数组
    if (dictData.value === value || dictData.id + '' === value || dictData.code === value) {
      return dictData
    }
  }
  return undefined
}

export function getDictDataLabel(dictType, value) {
  if (Array.isArray(value)) {
    return value.map(a => {
      const dict = getDictData(dictType, a)
      return dict ? dict.label || dict.name : ''
    })
  } else {
    const dict = getDictData(dictType, value)
    return dict ? dict.label || dict.name : ''
  }
}

export class getDictDataL {
}
