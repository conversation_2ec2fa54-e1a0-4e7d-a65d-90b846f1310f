// translate router.meta.title, be used in breadcrumb sidebar tagsview
import Vue from 'vue'

// 此处动态调整展示的tab页的详情展示
// TODO optimize：【UFFF-1628】SCAR单据类型tab的标题优化
export function generateTitle(title, tag) {
  if (['Iqc/:code', 'Sa/:code', 'Sc/:code', 'Sp/:code'].includes(tag?.name)) {
    return tag.params.code === '0' ? tag.name.slice(0, tag.name.search('/'))?.toUpperCase() : `${tag.params.code}`
  }
  if (['Iqc/see/:code', 'Sa/see/:code', 'Sc/see/:code', 'Sp/see/:code'].includes(tag?.name)) {
    return `查看${tag.params.code === '0' ? (tag.name.slice(0, tag.name.search('/'))?.toUpperCase() ? '' : tag.query.no) : `${tag.params.code}`}`
  }
  if (['Materialview/:id'].includes(tag?.name)) {
    return `${this.$t('material.materialView')} ${tag.params.id}`
  }
  if (['Materialedit/:id'].includes(tag?.name)) {
    return `${this.$t('common.materialEditing')} ${tag.params.id}`
  }

  if (tag?.name === 'Biddingnotice/:projectno') {
    return tag.params.projectNo !== '0' ? tag.params.projectNo : this.$t('common.notificationOfAward')
  }
  if (tag?.name === 'Biddingdetail/:projectno') {
    return tag.params.projectNo !== '0' ? tag.params.projectNo : this.$t('common.createBiddingProject')
  }
  if (tag?.name === 'Biddingdetail/:projectno') {
    return tag.params.projectNo !== '0' ? tag.params.projectNo : this.$t('common.createBiddingProject')
  }
  if (tag?.name === 'Biddingshow/:projectno') {
    return tag.params.projectNo !== '0' ? `${tag.params.projectNo}${this.$t('auth.see')}` : this.$t('common.createBiddingProject')
  }
  if (tag?.name === 'Tendershow/:projectno') {
    return `查看${tag.params.projectNo !== '0' ? tag.params.projectNo : this.$t('common.createBiddingProjects')}`
  }
  if (tag?.name === 'Tenderprocess/:projectno') {
    return tag.params.projectNo !== '0' ? tag.params.projectNo : this.$t('common.createBiddingProjects')
  }
  if (tag?.name === 'Supplierinfo/:id') {
    return tag.query.supplierName ? tag.query.supplierName : this.$t('common.supplierRegistrationPage')
  }
  // 简版供应商详情tab
  if (tag?.name === 'Simpleinfo/:id') {
    return tag.query.supplierName ? tag.query.supplierName : tag.params.id ? this.$t('简版供应商新增') : this.$t('简版供应商编辑')
  }
  if (tag?.name === 'Supplierquote/:quotationsno') {
    return tag.params.quotationsNo ? tag.params.quotationsNo : this.$t('common.supplierQuotation')
  }
  if (tag?.name === 'Orderdetail/:orderid') {
    // 如果是订单详情页面，则显示订单编号
    return `${this.$t('common.orderForm')}${tag.query.id}`
  }
  if (tag?.name === 'Authdetail/:authid') {
    // 如果是认证详情页面，则显示认证编号
    return `认证${tag.query.no ? (tag.query.no === '0' ? '' : tag.query.no) : ''}`
  }
  if (tag?.name === 'Authdetail/see/:authid') {
    // 如果是认证详情页面，则显示认证编号
    return `查看${tag.query.no ? (tag.query.no === '0' ? '' : tag.query.no) : ''}`
  }
  if (tag?.name === 'Answer/detail/:id') {
    // 如果是订单详情页面，则显示订单编号
    return `${this.$t('common.orderForm')}${tag.query.id}`
  }
  if (tag?.name === 'Orderdeliverydetail/:deliveryid') {
    // 如果是订单详情页面，则显示订单编号
    return tag.query.deliveryNoteNo ? `${this.$t('order.deliveryNote')}${tag.query.deliveryNoteNo}` : this.$t('order.newDeliveryOrder')
  }
  if (tag?.name === 'Create/:applicationid') {
    // 分级申请单
    return tag.query.no ? `${tag.query.no}` : title
  }
  if (tag?.name === 'Detail/:applicationid') {
    // 分级申请单
    return `查看${tag.query.no ? `${tag.query.no}` : title}`
  }
  if (tag?.name === 'Processhome/:projectid') {
    return tag.params.projectId !== '0' ? tag.params.projectId : this.$t('common.newInquiry')
  }
  if (tag?.name === 'Basehome/:projectid') {
    console.log('tag.params.projectId', tag.params.projectId)
    return tag.params.projectId !== '0' ? tag.params.projectId : this.$t('新建BOM项目')
  }
  if (tag?.name === 'Ledgerinfo/:batchnumber') {
    return tag.params.batchNumber !== 'null' ? tag.params.batchNumber : this.$t('common.inspectionLedgerDetails')
  }
  // avpl
  if (tag?.name === 'Trending/:materialcode') {
    return tag.params.materialCode
  }
  if (tag?.name === 'Financedetail/:id') {
    return tag.params.id !== '0' ? tag.params.id : this.$t('common.createAStatementOfAccount')
  }
  if (tag?.name === 'Financedetail/see/:id') {
    return `${this.$t('查看')} ${tag.params.id}`
  }

  if (tag?.name === 'Supplierdetail/:id') {
    return tag.params.id !== '0' ? tag.params.id : ' '
  }
  if (tag?.name === 'Bank/:id' || tag?.name === 'Supplier/bank/:id') {
    return tag.params.id !== '0' ? tag.query.recordNo : '修改银行信息申请单'
  }
  if (tag?.name === 'Erp/:id' || tag?.name === 'Supplier/erp/:id') {
    return tag.params.id !== '0' ? tag.query.recordNo : '修改ERP视图'
  }
  if (tag?.name === 'Baseinfo/:id' || tag?.name === 'Supplier/baseinfo/:id') {
    return tag.params.id !== '0' ? tag.query.recordNo : '修改基础信息申请单'
  }
  if (tag?.name === 'Qualandcertinfo/:id' || tag?.name === 'Supplier/qualandcertinfo/:id') {
    return tag.params.id !== '0' ? tag.query.recordNo : '修改系统和资质申请单'
  }
  if (tag?.name === 'Avpldetail/:code') {
    return tag.params.code
  }
  // 路由地址vue会自动修改成 首字母大写，后续字母小写，所以此处的 tag.name 需要符合这个规则，包括组件名称也要符合这个规则
  if (tag?.name === 'Qa/:id') {
    return tag.params.id !== '0' ? `现场审核${tag.params.id}` : ''
  }
  if (tag?.name === 'Auditreport/:id') {
    return tag.params.id !== '0' ? `现场审核报告${tag.params.id}` : ''
  }
  if (tag?.name === 'Ppvdetail/:no') {
    return tag.params.no !== '0' ? tag.params.no : ' '
  }
  if (tag?.name === 'Supplierdetail/:no') {
    return tag.params.no !== '0' ? tag.params.no : ' '
  }
  // TQCDM相关路由
  if (tag?.name === 'Tqcdmcreate/:evaluateid') {
    // 如果是认证详情页面，则显示认证编号
    return tag.params.evaluateid !== '0' ? `TQCDM任务-${tag.query.no}` : 'TQCDM任务创建'
  }
  if (tag?.name === 'Detail/:evaluateid') {
    // 如果是认证详情页面，则显示认证编号
    return `TQCDM任务详情-${tag.query.no}`
  }
  if (tag?.name === 'Soinfo/:id') {
    return tag.query.no !== '0' ? tag.query.no : ' '
  }
  if (tag?.name === 'Poinfo/:id') {
    return tag.query.no !== '0' ? tag.query.no : ' '
  }
  if (tag?.name === 'Avplinfo/:id') {
    return tag.params.id !== '0' ? tag.query.no : '新建avpl'
  }
  if (tag?.name === 'Shortinfo/:id') {
    return tag.params.id !== '0' ? tag.query.no : ''
  }
  if (tag?.name === 'Plinfo/:id') {
    return tag.params.id !== '0' ? tag.query.no : ''
  }
  if (tag?.name === 'Invoiceinfo/:id') {
    return tag.params.id !== '0' ? tag.query.no : ''
  }
  if (tag?.name === 'Deliveryinfo/:id') {
    return tag.params.id !== '0' ? tag.query.no : ''
  }
  if (tag?.name === 'Paydetail/:id') {
    return tag.params.id !== '0' ? tag.query.no : ''
  }
  if (tag?.name === 'Cashdetail/:id') {
    return tag.params.id !== '0' ? tag.query.no : ''
  }

  // 路由地址vue会自动修改成 首字母大写，后续字母小写，所以此处的 tag.name 需要符合这个规则，包括组件名称也要符合这个规则

  const hasKey = this.$te('common.' + title)

  if (hasKey) {
    // $t :this method from vue-i18n, inject in @/lang/index.js
    const translatedTitle = this.$t('common.' + title)

    return translatedTitle
  }
  return title
}

// init object translation label
export function addTranslation(obj) {
  if (obj instanceof Object) {
    Vue.set(obj, 'translations', [{
      translation: '',
      locale: 'zh'
    }, {
      translation: '',
      locale: 'en'
    }
    ])
  }
}
