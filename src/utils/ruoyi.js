// 日期格式化
import request from '@/utils/request'
import { debug } from 'script-ext-html-webpack-plugin/lib/common'

export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '')
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields()
  }
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
  const search = params
  search.params = {}
  if (dateRange != null && dateRange !== '') {
    if (typeof (propName) === 'undefined') {
      search['beginTime'] = dateRange[0]
      search['endTime'] = dateRange[1]
    } else {
      search['begin' + propName] = dateRange[0]
      search['end' + propName] = dateRange[1]
    }
  }
  return search
}

/**
 * 添加开始和结束时间到 params 参数中
 *
 * 注意：第三个参数不为空时，会默认在参数值前面拼接【begin、end】字符串。
 *
 * @param params 参数
 * @param dateRange 时间范围。
 *                大小为 2 的数组，每个时间为 yyyy-MM-dd 格式
 * @param propName 加入的参数名，可以为空
 * @param isDate
 */
export function addBeginAndEndTime(params, dateRange, propName, isDate) {
  // 必须传入参数
  if (!dateRange) {
    return params
  }
  // 如果未传递 propName 属性，默认为 time
  if (!propName) {
    propName = 'Time'
  } else {
    propName = propName.charAt(0).toUpperCase() + propName.slice(1)
  }
  // 设置参数
  if (dateRange[0]) {
    if (isDate) {
      params['begin' + propName] = dateRange[0]
    } else {
      params['begin' + propName] = dateRange[0] + ' 00:00:00'
    }
  }
  if (dateRange[1]) {
    if (isDate) {
      params['end' + propName] = dateRange[1]
    } else {
      params['end' + propName] = dateRange[1] + ' 23:59:59'
    }
  }
  return params
}

// 字符串格式化(%s )
export function sprintf(str) {
  var args = arguments
  var flag = true
  var i = 1
  str = str.replace(/%s/g, function() {
    var arg = args[i++]
    if (typeof arg === 'undefined') {
      flag = false
      return ''
    }
    return arg
  })
  return flag ? str : ''
}

// 转换字符串，undefined,null等转化为""
export function praseStrEmpty(str) {
  if (!str || str === 'undefined' || str === 'null') {
    return ''
  }
  return str
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 * @param {*} rootId 根Id 默认 0
 */
export function handleTree(data, id, parentId, children, rootId) {
  if (data.length === 1) return data
  id = id || 'id'
  parentId = parentId || 'parentId'
  children = children || 'children'
  rootId = rootId || Math.min.apply(Math, data.map(item => {
    return item[parentId]
  })) || 0
  // 对源数据深度克隆
  const cloneData = JSON.parse(JSON.stringify(data))
  // 循环所有项
  const treeData = cloneData.filter(father => {
    const branchArr = cloneData.filter(child => {
      // 返回每一项的子级数组
      return father[id] === child[parentId]
    })
    branchArr.length > 0 ? father.children = branchArr : ''
    // 返回第一层
    return father[parentId] === rootId
  })
  return treeData !== '' ? treeData : data
}

/**
 * 获取当前时间
 * @param timeStr 时分秒 字符串 格式为 xx:xx:xx
 */
export function getNowDateTime(timeStr) {
  const now = new Date()
  const year = now.getFullYear() // 得到年份
  const month = (now.getMonth() + 1).toString().padStart(2, '0') // 得到月份
  const day = now.getDate().toString().padStart(2, '0') // 得到日期

  if (timeStr != null) {
    return `${year}-${month}-${day} ${timeStr}`
  }
  const hours = now.getHours().toString().padStart(2, '0') // 得到小时;
  const minutes = now.getMinutes().toString().padStart(2, '0') // 得到分钟;
  const seconds = now.getSeconds().toString().padStart(2, '0') // 得到秒;
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 获得租户功能是否开启
 */
export function getTenantEnable() {
  // console.log("enable: " + process.env.VUE_APP_TENANT_ENABLE)
  if (process.env.VUE_APP_TENANT_ENABLE === 'true') {
    return true
  }
  if (process.env.VUE_APP_TENANT_ENABLE === 'false') {
    return false
  }
  return process.env.VUE_APP_TENANT_ENABLE || true
}

/**
 * 获得文档是否开启
 */
export function getDocEnable() {
  if (process.env.VUE_APP_DOC_ENABLE === 'true') {
    return true
  }
  if (process.env.VUE_APP_DOC_ENABLE === 'false') {
    return false
  }
  return process.env.VUE_APP_DOC_ENABLE || false
}

/**
 * 获得 Vue 应用的基础路径
 */
export function getBasePath() {
  return process.env.VUE_APP_APP_NAME || '/'
}

/**
 * 获得 Vue 应用的访问路径
 *
 * @param path 路径
 */
export function getPath(path) {
  // 基础路径，必须以 / 结尾
  const basePath = getBasePath()
  if (!basePath.endsWith('/')) {
    return basePath + '/'
  }
  // 访问路径，必须不能以 / 开头
  if (path.startsWith('/')) {
    path = path.substring(1)
  }
  return basePath + path
}

/**
 * 将以base64的图片url数据转换为Blob
 * 用url方式表示的base64图片数据
 */
export function b64toBlob(b64Data) {
  const base64 = {
    dataURL: b64Data, // 用url方式表示的base64图片数据
    type: 'image/png'// 文件类型
  }
  const urlData = base64.dataURL
  const type = base64.type
  let bytes = null
  if (urlData.split(',').length > 1) { // 是否带前缀
    bytes = window.atob(urlData.split(',')[1]) // 去掉url的头，并转换为byte
  } else {
    bytes = window.atob(urlData)
  }
  // 处理异常,将ascii码小于0的转换为大于0
  const ab = new ArrayBuffer(bytes.length)
  const ia = new Uint8Array(ab)
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i)
  }
  return new Blob([ab], { type: type })
}

/**
 * 生成uuid
 * @returns {string}
 */
export function generateUUID() {
  let d = new Date().getTime()
  if (typeof performance !== 'undefined' && typeof performance.now === 'function') {
    d += performance.now() // use high-precision timer if available
  }
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = (d + Math.random() * 16) % 16 | 0
    d = Math.floor(d / 16)
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16)
  })
}
export function formatNumNoZero(value, decimalPlace = 2) {
  if (Number(value)) {
    let returnvalue = (Math.round((Number(value) + Number.EPSILON) * Math.pow(10, decimalPlace)) /
      (Math.pow(10, decimalPlace))).toString()
    if (returnvalue.indexOf('.') > 0) {
      returnvalue = returnvalue.replaceAll('0+?$', '')// 去掉多余的0
    }
    return returnvalue
  } else if (value === 0) {
    return 0
  } else if (value === '***') {
    return value
  } else {
    return ''
  }
}
export async function downloadFile(file) {
  console.log('downloadFile', file)
  let url
  let fileName
  if (file?.response?.data?.url) {
    url = file.response.data.url
    fileName = file.response.data.name
  }
  if (file?.url) {
    url = file.url
    fileName = file.name
  }
  const res = await request({
    url,
    method: 'get',
    responseType: 'blob'
  })
  // 创建 blob
  const blob = new Blob([res], {})
  // 创建 href 超链接，点击进行下载
  window.URL = window.URL || window.webkitURL
  const href = URL.createObjectURL(blob)
  const downA = document.createElement('a')
  downA.href = href
  downA.download = fileName || url.slice(url.lastIndexOf('/'))
  downA.click()
  // 销毁超连接
  window.URL.revokeObjectURL(href)
}
