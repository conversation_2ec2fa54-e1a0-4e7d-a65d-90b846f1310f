
<template>
  <div :class="className" :style="{height:height,width:width}" />

</template>
<script>
import * as echarts from 'echarts'
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Object
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.initChart(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.setOption( {
        title: {
          text: 'PRODUCT GROUP Top10',
          left: 'left'
        },
        tooltip: {},
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: ['IFE', 'BBL Plus', 'BBL Auun', 'HFA GEN3', 'DCU', 'POT GEN2', 'DTA', 'HFA GEN2', 'Sensoris', 'Cable Module 2.5', 'Others']
        },
        series: [{
          type: 'bar',
          data: [30, 25, 17, 17, 15, 9, 6, 5, 3, 2, 10],
          itemStyle: {
            color: '#6495ED'
          },
          label: {
            show: true,
            position: 'right'
          }
        }]
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
