
<template>
  <div :class="className" :style="{height:height,width:width}" />

</template>
<script>
import * as echarts from 'echarts'
import resize from '@/views/dashboard/mixins/resize'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Array
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.initChart(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      const total = this.chartData.reduce((acc, cur) => acc + cur.countNum, 0)
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return `${params.seriesName}<br/>${params.name.split(' ').at(0)} : ${params.value} (${params.percent}%)`;
          }
        },
        legend: {
          bottom: '5%',
          left: 'center',
          orient: 'vertical',
          formatter: function(name) {
            return `{style1|${name.split(' ').at(0)}} {style2|${name.split(' ').at(1)} Projects}`
          }
        },
        color: ['#9DB1CF', '#88C8E3', '#D2D3D4'],
        textStyle: {
          rich: {
            style1: {
              fontWeight: ''
            },
            style2: {
              color: '#929292'
            }
          }
        },
        series: [
          {
            name: 'BU Overview',
            type: 'pie',
            radius: ['35%', '50%'],
            center: ['50%', '30%'],

            avoidLabelOverlap: false,
            top: '3%',
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              normal: {
                show: true,
                position: 'center',
                formatter: `Projects\n \n ${total}`,
                textStyle: {
                  fontSize: '18',
                  fontWeight: 'bold'
                }
              }
            },

            labelLine: {
              show: false
            },
            data: this.chartData.map(a => {
              return {
                value: a.countNum,
                name: getDictDataLabel(DICT_TYPE.COMMON_PURCHASEORG, a.orgId) + ' ' + a.countNum
              }
            })
          }
        ]
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
