
<template>
  <div :class="className" :style="{height:height,width:width}" />

</template>
<script>
import * as echarts from 'echarts'
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Object
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.initChart(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.setOption({
        tooltip: {
          trigger: 'item'
        },
        legend: {
          right: '25%',
          top: 'center',
          orient: 'vertical',
          formatter: function(name) {
            // 这里可以根据图例名称返回不同的富文本样式
            return `{style1|${name}}    {style2|136 Projects}`
          }
        },
        color: ['#0D1B48', '#162859', '#243A6E', '#243A6E', '#3B5488', '#607AA8', '#7991B8', '#9DB1CF', '#A3AAAD', '#EDEEEF'],
        textStyle: {
          rich: {
            style1: {
              fontWeight: ''
            },
            style2: {
              color: '#929292'
            }
          }
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['35%', '50%'],
            center: ['25%', '50%'],

            avoidLabelOverlap: false,
            top: '3%',
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              normal: {
                show: true,
                position: 'center',
                formatter: 'Projects\n \n 245',
                textStyle: {
                  fontSize: '18',
                  fontWeight: 'bold'
                }
              }
            },

            labelLine: {
              show: false
            },
            data: [
              { value: 1048, name: 'GT' },
              { value: 735, name: 'GA' },
              { value: 455, name: 'GS' },
              { value: 33, name: 'GT' },
              { value: 23, name: 'SDS' },
              { value: 580, name: 'DS' },
              { value: 23, name: 'CS' },
              { value: 32, name: 'CV' },
              { value: 580, name: 'SW' },
              { value: 455, name: 'GS' },
              { value: 22, name: 'GS' },
            ]
          }
        ]
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
