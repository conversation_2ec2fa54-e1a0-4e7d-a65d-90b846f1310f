<template>
  <div class="dashboard">
    <div style="flex-basis: 30%; border-right: 0.5px solid #EAEAEA;">

      <div class="main-number">{{ mainNumber }}</div>
      <div class="sub-numbers">
        <div class="sub-number" style=" border-right: 0.5px solid #EAEAEA;">
          <div class="number">{{ subNumbers.addInY24 }}</div>
          <div class="label">Add in Y24</div>
        </div>
        <div class="sub-number">
          <div class="number">{{ subNumbers.ECU }}</div>
          <div class="label">ECU</div>
        </div>
      </div>
    </div>

    <div class="best-prices">
      <div class="title">Best Prices</div>
      <div class="bars">

        <div v-for="(value, key) in priceDistribution" :key="key"  :style="{ width: value.percentage + '%',  }">
          <span style="font-size: 14px"> {{key}}</span>
          <div :style="{backgroundColor: value.color}" class="bar">
            {{ value.percentage }}%

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      mainNumber: 3298,
      subNumbers: {
        addInY24: 230,
        ECU: 182
      },
      priceDistribution: {
        'Brose DC': { percentage: 40, color: '#FF0000' },
        'IMI CN': { percentage: 30, color: '#808080' },
        'KECN': { percentage: 30, color: '#9CA3AF' }
      }
    }
  }
}
</script>

<style>
.dashboard {
  padding: 0 20px;
  height: 300px;
  display: flex;
  align-items: center;
  font-family: Arial, sans-serif;
}

.main-number {
  font-size: 48px;
  color: #333;
  text-align: center;
}

.sub-numbers {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}
.sub-number{
  text-align: center;
  flex: 0 0 50%
}
.sub-number .number {
  font-size: 24px;
  color: #333;
}

.sub-number .label {
  color: #929292;
  font-size: 14px;
}
.best-prices{
  flex-basis: 70%;
  padding: 0 20px;
}
.best-prices .title {
  font-size: 18px;
  margin-bottom: 8px;
}

.best-prices .bars {
  display: flex;
  width: 100%;
}

.bar {
  display: flex;
  align-items: center;
  color: white;
  padding: 4px 8px;
  font-size: 14px;
  height: 30px;
}
</style>
