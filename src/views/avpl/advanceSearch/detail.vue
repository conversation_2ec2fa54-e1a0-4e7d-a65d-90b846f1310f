
<template>
  <div class="detail">
    <common-card title="通用信息">
      <div class="detail-card">
        <el-descriptions
          :column="2"
          :label-style="{
            width: '80px',
            'justify-content': 'right'
          }"
        >

          <el-descriptions-item label="物料编码">{{ detailInfo.materialCode }}</el-descriptions-item>
          <el-descriptions-item label="物料描述">{{ detailInfo.materialDescription }}</el-descriptions-item>
          <el-descriptions-item label="品类">
            <dict-tag :value="detailInfo.categoryId" :type="DICT_TYPE.COMMON_CATEGORY" />
          </el-descriptions-item>
          <el-descriptions-item label="基本单位">
            <dict-tag :value="detailInfo.basicUnit" :type="DICT_TYPE.MATERIAL_UOM" />
          </el-descriptions-item>
          <el-descriptions-item label="采购组织">
            <dict-tag :value="detailInfo.orgId" :type="DICT_TYPE.COMMON_PURCHASEORG" />
          </el-descriptions-item>
          <el-descriptions-item label="材质">
            {{ detailInfo.material }}</el-descriptions-item>
          <el-descriptions-item label="制造商">
            {{ detailInfo.mfg }}</el-descriptions-item>
          <el-descriptions-item label="制造商料号"> {{ detailInfo.mpn }}</el-descriptions-item>

        </el-descriptions>
      </div>
    </common-card>
    <common-card title="图纸与技术规范">
      <div class="detail-card">

        <el-descriptions
          :label-style="{
            width: '80px',
            'justify-content': 'right'
          }"
          :column="2"
        >
          <el-descriptions-item label="规格型号">{{ detailInfo.specifications }}</el-descriptions-item>
          <el-descriptions-item label="版本">{{ detailInfo.version }}</el-descriptions-item>
          <el-descriptions-item label="图纸版本">{{ detailInfo.drawingVersion }}</el-descriptions-item>

        </el-descriptions>
      </div>
    </common-card>
    <common-card title="价格信息">
      <div>

        <vxe-grid
          v-if="activeName === 'first'"
          ref="avplIndex"
          :data="list"
          :loading="loading"
          :span-method="mergeRowMethod"
          v-bind="girdOption"
          @sort-change="sortMethod"
          @filter-change="filterMethod"
          @checkbox-change="checkBoxChange"
          @checkbox-all="checkBoxAllChange"
        >
          <template #materialCode="{row}">
            <copy-button
              type="text"
              @click="$router.push(`/material/materialView/${row.materialCode}?id=${row.materialId}&viewOnly=1`)"
            >
              {{ row.materialCode }}
            </copy-button>
          </template>
          <template #orgId="{row}">
            <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="row.orgId" />
          </template>
          <template #sourcing="{row}">
            <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcing" />
            <dict-tag :type="DICT_TYPE.RFQ_SOURCING_SOURCES" :value="row.sourcing" />
          </template>
          <template #source="{row}">
            <dict-tag :type="DICT_TYPE.AVPL_SOURCE" :value="row.source" />
          </template>
          <template #status="{row}">
            <dict-tag :type="DICT_TYPE.AVPL_STATUS" :value="row.status" />
          </template>
          <template #pgId="{row}">
            <dict-tag :type="DICT_TYPE.SYSTEM_PURCHASE_GROUP" :value="row.pgId" />
          </template>
          <template #purchaseType="{row}">
            <dict-tag :type="DICT_TYPE.MATERIAL_PURCHASE_TYPE" :value="row.purchaseType" />
          </template>
          <template #categoryId="{row}">
            <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
          </template>
          <template #basicUnit="{row}">
            <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.basicUnit" />
          </template>
          <template #orderUnit="{row}">
            <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.orderUnit" />
          </template>
          <template #taxRate="{row}">
            <dict-tag :type="DICT_TYPE.SUPPLIER_RATE" :value="row.taxRate" />
          </template>
          <template #priceCategory="{row}">
            <dict-tag :type="DICT_TYPE.RFQ_PRICE_CATEGORY" :value="row.priceCategory" />
          </template>
          <template #originalCurrency="{row}">
            <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.originalCurrency" />
          </template>
          <template #paymentMethod="{row}">
            <dict-tag :type="DICT_TYPE.SUPPLIER_PAYMENT_DAYS" :value="row.paymentMethod" />
          </template>
          <template #quotationQuantityFrom="{row}">
            <number-format :value="row.quotationQuantityFrom" />
          </template>
          <template #quotationQuantityTo="{row}">
            <number-format :value="row.quotationQuantityTo" />
          </template>
          <template #originalUnitPriceWithoutTax="{row}">
            <number-format :value="row.originalUnitPriceWithoutTax" />
          </template>
          <template #originalUnitPriceTax="{row}">
            <number-format :value="row.originalUnitPriceTax" />
          </template>
          <template #unitPriceWithoutTaxPriceUnit="{row}">
            <number-format :value="row.unitPriceWithoutTaxPriceUnit" />
          </template>
          <template #unitPriceIncludesTaxPriceUnit="{row}">
            <number-format :value="row.unitPriceIncludesTaxPriceUnit" />
          </template>

        </vxe-grid>

        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNo"
          :total="total"
          @pagination="handleSearch"
        />
      </div>

    </common-card>

  </div>
</template>
<script>
import {
  advancedSearchDetailPage,
  createMaterialsForRFQ, exportHeaderExcel,
  getAVPLMaterialAllIds,
  refreshHistoryHash
} from '@/api/avpl/avplCoordination'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getMaterialInfo } from '@/api/material/main'

export default {
  name: 'Avpldetail/:code',
  data() {
    return {
      queryParams: {
        search: '',
        materialCodeList: this.$route.query.materialCodeList ? this.$route.query.materialCodeList.split(',') : [],
        supplierName: '',
        purchaseType: '',
        materialDescription: '',
        categories: [],
        categoryId: '',
        sourcings: [],
        source: '',
        purchaseOrgs: [],
        mfg: '',
        mpn: '',
        pgId: '',
        status: 'price_valid',
        dateType: '',
        beginDate: '',
        endDate: '',
        sortBy: '',
        sortField: '',
        pageNo: 1,
        pageSize: 10
      },
      list: [],
      loading: false,
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'AVPLDelivery',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          keyField: 'materialSupplierRelId',
          isHover: true
        },
        columns: [
          { title: this.$t('common.creationDate'), sortable: true, field: 'createTime', visible: true, width: 100 },
          {
            title: this.$t('common.sourcing'),
            field: 'sourcing',
            visible: true,
            width: 100
          },
          {
            title: this.$t('avpl.source'), field: 'source',
            slots: {
              default: 'source'
            },
            visible: true, width: 100
          },
          // {
          //   title: this.$t('material.materialCode'),
          //   slots: {
          //     default: 'materialCode'
          //   }, field: 'materialCode', visible: true, width: 100
          // },
          // { title: this.$t('material.specificationAndModel'), field: 'specifications', visible: true, width: 100 },
          // { title: this.$t('material.materialDescription'), field: 'materialDescription', visible: true, width: 100 },
          // {
          //   title: this.$t('material.basicUnit'),
          //   field: 'basicUnit',
          //   slots: { default: 'basicUnit' },
          //   visible: true,
          //   width: 100
          // },
          { title: this.$t('material.textureOfMaterial'), field: 'material', visible: true, width: 100 },
          // { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          // { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          // { title: this.$t('order.edition'), field: 'version', visible: true, width: 100 },
          // { title: this.$t('material.drawingVersion'), field: 'drawingVersion', visible: true, width: 100 },
          // {
          //   title: this.$t('material.category'),
          //   field: 'categoryId',
          //   slots: { default: 'categoryId' },
          //   visible: true,
          //   width: 100
          // },
          {
            title: this.$t('material.purchaseType'), field: 'purchaseType',
            slots: { default: 'purchaseType' }, visible: true, width: 100
          },
          {
            title: this.$t('rfq.priceCategory'),
            field: 'priceCategory',
            slots: { default: 'priceCategory' },
            visible: true,
            width: 100
          },
          // {
          //   title: this.$t('supplier.purchasingOrganization'),
          //   slots: {
          //     default: 'orgId'
          //   }, field: 'orgId', visible: true, width: 100
          // },
          { title: this.$t('order.customerName'), field: 'customName', visible: true, width: 100 },
          { title: this.$t('avpl.approvalFormNo'), field: 'approvalNo', visible: true, width: 100 },
          // {
          //   title: this.$t('material.procurementGroup'),
          //   field: 'pgId',
          //   slots: { default: 'pgId' },
          //   visible: true,
          //   width: 100
          // },
          { title: this.$t('common.status'), field: 'status', slots: { default: 'status' }, visible: true, width: 100 },
          { title: this.$t('rfq.eventName'), field: 'eventName', visible: true, width: 100 },
          { title: this.$t('rfq.rfqItemNo'), field: 'projectNo', visible: true, width: 100 },

          { title: this.$t('supplier.supplierCode'), field: 'supplierCode', visible: true, width: 100 },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
          { title: this.$t('avpl.manufacturerQuoted'), field: 'quotationMfg', visible: true, width: 100 },
          { title: this.$t('avpl.quotedManufacturersPartNumber'), field: 'quotationMpn', visible: true, width: 100 },
          {
            title: this.$t('supplier.taxRate'),
            field: 'taxRate',
            slots: { default: 'taxRate' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('avpl.originalCurrency'),
            field: 'originalCurrency',
            slots: { default: 'originalCurrency' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('material.purchasingUnit'),
            field: 'orderUnit',
            slots: { default: 'orderUnit' },
            visible: true,
            width: 100
          },
          { title: this.$t('material.priceUnit'), field: 'priceUnit', visible: true, width: 100 },
          { title: this.$t('avpl.purchaseUnitQuantity'), field: 'orderUnitQuantity', visible: true, width: 100 },
          { title: this.$t('avpl.quantityInBuom'), field: 'measureUnitsQuantity', visible: true, width: 100 },
          { title: this.$t('avpl.pricingUnit'), field: 'salesUnit', visible: true, width: 100 },
          {
            title: this.$t('rfq.unitPriceExcludingTaxpriceUnit'),
            field: 'unitPriceWithoutTaxPriceUnit',
            slots: { default: 'unitPriceWithoutTaxPriceUnit' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.unitPriceIncludingTaxpriceUnit'),
            field: 'unitPriceIncludesTaxPriceUnit',
            slots: { default: 'unitPriceIncludesTaxPriceUnit' },
            visible: true,
            width: 100
          },
          { title: this.$t('avpl.minimumPackageQuantity'), field: 'mpq', visible: true, width: 100 },
          { title: this.$t('avpl.minimumOrderQuantity'), field: 'moq', visible: true, width: 100 },
          { title: this.$t('avpl.priceValidityPeriodStartTime'), field: 'recommendedPriceStart', visible: true, width: 100 },
          { title: this.$t('avpl.priceValidUntil'), field: 'recommendedPriceExpires', visible: true, width: 100 },
          { title: this.$t('rfq.deliveryPerioddays'), field: 'deliveryPeriod', visible: true, width: 100 },
          { title: this.$t('avpl.deliveryMethod'), field: 'deliveryMethod', visible: true, width: 100 },
          {
            title: this.$t('supplier.paymentMethod'), field: 'paymentMethod',
            slots: {
              default: 'paymentMethod'
            },
            visible: true, width: 100
          },
          { title: this.$t('avpl.minimumConsumption'), field: 'minimumConsumption', visible: true, width: 100 },
          { title: this.$t('order.supplierRemarks'), field: 'remark', visible: true, width: 100 },

          {
            title: this.$t('rfq.orderQuantityFrom'),
            field: 'quotationQuantityFrom',
            slots: { default: 'quotationQuantityFrom' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.orderQuantityArrived'),
            field: 'quotationQuantityTo',
            slots: { default: 'quotationQuantityTo' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.quotedUnitPriceExcludingTax'),
            field: 'originalUnitPriceWithoutTax',
            slots: { default: 'originalUnitPriceWithoutTax' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.quotationUnitPriceIncludingTax'),
            field: 'originalUnitPriceTax',
            slots: { default: 'originalUnitPriceTax' },
            visible: true,
            width: 100
          }
          // {
          //   title: this.$t('common.operate'),
          //   showOverflow: false,
          //   field: 'operation',
          //   slots: {
          //     default: 'operation'
          //   },
          //   visible: true,
          //   width: 100,
          //   fixed: 'right'
          // }
        ],

        sortConfig: {
          remote: true
        }
        // toolbarConfig: {
        //   slots: {
        //     // 自定义工具栏模板
        //     buttons: 'toolbar_buttons'
        //   }
        // }
      },
      total: 0,
      activeName: 'first',
      selectedRecord: [],
      mergeRowFields: {
        // 根据物料合并的字段
        MaterialFields: [
          'createTime',
          'sourcing',
          'source',
          'materialCode',
          'specifications',
          'materialDescription',
          'basicUnit',
          'material',
          'mfg',
          'mpn',
          'version',
          'drawingVersion',
          'categoryId',
          'purchaseType',
          'priceCategory',
          'orgId',
          'customName',
          'approvalNo',
          'pgId',
          'eventName',
          'projectNo'
        ],
        // 根据物料+供应商合并的字段
        MaterialSupplierFields: [
          'supplierCode',
          'supplierName',
          'quotationMfg',
          'quotationMpn',
          'taxRate',
          'originalCurrency',
          'orderUnit',
          'priceUnit',
          'orderUnitQuantity',
          'measureUnitsQuantity',
          'salesUnit',
          'mpq',
          'moq',
          'recommendedPriceStart',
          'recommendedPriceExpires',
          'deliveryPeriod',
          'deliveryMethod',
          'paymentMethod',
          'minimumConsumption',
          'remark',
          'status'
        ],
        // 操作列
        operationFields: [
          'operation'
        ]
      },
      sourcingSources: [],
      categoryList: [],
      detailInfo: {

      }
    }
  },
  mounted() {
    this.getList()
    this.getCategories()
    this.getSourcingSourcess()
  },
  methods: {
    getList() {
      this.loading = true
      this.queryParams.categoryId = this.queryParams.categories.at(-1)
      if (this.queryParams.time) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      advancedSearchDetailPage({
        ...this.queryParams,
        ...this.$route.query,
        purchaseOrgs: [this.$route.query.orgId]
      }).then(res => {
        this.loading = false
        this.detailInfo = res.data.list.at(0)
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    // 物料列表的跨页全选框
    checkBoxAllChange({ checked }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.avplIndex
      if (checked) {
        const avplMaterialIds = Array.from(new Set([...this.selectedRecord]))
        if (avplMaterialIds?.length + this.total > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.clearCheckboxRow()
          // 当前选中的数据key数组
          const selectIds = grid.getCheckboxRecords().map(item => item.materialSupplierRelId)
          // 取交集
          const arr = avplMaterialIds.filter(x => selectIds.includes(x))
          if (arr.length > 0) {
            arr.forEach((id) => {
              grid.setCheckboxRow(grid.getRowById(id), true)
            })
          }
        } else {
          this.checkAllData(checked)
        }
      } else {
        this.checkAllData(checked)
      }
    },
    querySearchAsync(query) {
      if (query !== '') {
        this.selectLoading = true
        getMaterialInfo({
          materialCode: query
        }).then(res => {
          this.selectLoading = false
          this.materialList = res.data.map(item => {
            return {
              value: item.materialCode,
              label: item.materialCode
            }
          })
        })
      } else {
        this.materialList = []
      }
    },
    checkAllData(checked) {
      getAVPLMaterialAllIds(this.queryParams).then(res => {
        if (checked) {
          this.selectedRecord = Array.from(new Set([...this.selectedRecord, ...res.data]))
        } else {
          const avplMaterialIds = Array.from(new Set([...res.data]))
          this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => !avplMaterialIds.includes(x))))
        }
      })
    },
    createRfq() {
      const selected = this.selectedRecord
      if (selected.length === 0) {
        this.$message.warning(this.$t('avpl.pleaseSelectAvplMaterial'))
        return
      }
      createMaterialsForRFQ(
        selected
      ).then(res => {
        this.$router.push(`/rfq/processHome/${res.data.projectNo}?id=${res.data.id}&projectNo=${res.data.projectNo}&code=${'whole_enquiry'}`)
      })
    },
    /**
     * 更新历史价格的hash
     */
    refreshHistoryHash() {
      const selected = this.selectedRecord
      if (selected.length === 0) {
        this.$message.warning(this.$t('avpl.pleaseSelectAvplMaterial'))
        return
      }
      refreshHistoryHash(
        selected
      ).then(res => {
        this.$message({
          message: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const cellValue = row[column.property]
      if ((cellValue !== undefined && this.mergeRowFields.MaterialFields.includes(column.property))) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue && row.avplMaterialId === prevRow.avplMaterialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.avplMaterialId === nextRow.avplMaterialId && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      } else if ((['checkbox'].includes(column.type) || this.mergeRowFields.operationFields.includes(column.property)) && row) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && row.avplMaterialId === prevRow.avplMaterialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.avplMaterialId === nextRow.avplMaterialId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      } else if (this.mergeRowFields.MaterialSupplierFields.includes(column.property) && row) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && row.materialSupplierRelId === prevRow.materialSupplierRelId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.materialSupplierRelId === nextRow.materialSupplierRelId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    // 项目寻源采购的数据源组装
    getSourcingSourcess() {
      this.sourcingSources.push(...getDictDatas(DICT_TYPE.COMMON_USERS, 0))
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    handleQuery() {
    },
    resetQuery() {
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.handleSearch()
    },
    filterMethod() {
    },
    handleClick() {
      this.queryParams = {
        search: '',
        materialCode: '',
        supplierName: '',
        purchaseType: '',
        materialDescription: '',
        categories: [],
        categoryId: '',
        sourcings: [],
        purchaseOrgs: [],
        mfg: '',
        mpn: '',
        pgId: '',
        status: '',
        dateType: '',
        beginDate: '',
        endDate: '',
        sortBy: '',
        sortField: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getList()
    },
    downLoadExcel() {
      switch (this.activeName) {
        case 'first':
          exportHeaderExcel(this.queryParams).then(res => {
            this.$download.excel(res, 'AVPL数据.xlsx')
          })
          break
      }
    },
    handleSearch() {
      switch (this.activeName) {
        case 'first':
          this.getList()
          break
      }
    },
    restFields() {
    },
    // 物料列表的单个选择框
    checkBoxChange({ checked, row }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.avplIndex
      if (checked) {
        const avplMaterialIds = this.selectedRecord
        if (avplMaterialIds?.length + 1 > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.setCheckboxRow(grid.getRowById(row.avplMaterialId), false)
        } else {
          this.selectedRecord.push(row.avplMaterialId)
        }
      } else {
        this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => row.avplMaterialId !== x)))
      }
    },
    // 跨页全选的清空
    clearSelected() {
      const grid = this.$refs.avplIndex
      this.selectedRecord = []
      grid.clearCheckboxRow()
    }
  }
}
</script>

<style scoped lang="scss">
.detail{
  padding: 15px 25px;
  &-card{
    padding: 0 50px
  }
}
</style>
