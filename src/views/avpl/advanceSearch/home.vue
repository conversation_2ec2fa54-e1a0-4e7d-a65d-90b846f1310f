
<template>
  <div style="padding: 25px 170px">
    <div style="display: flex;justify-content: center">
      <div class="searchText" style="width: 60%;display: inline-flex;margin: 15px 0">
        <el-select
          ref="searchText"
          v-model="search"
          multiple
          allow-create
          default-first-option
          filterable
          style="width: 80%"
          clearable
          @keyup.enter.native="$refs.searchText.blur()"
        >
          <el-option
            v-for="item in options"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
        <!--            <el-input v-model="queryParams.search" @keyup.enter.native="init" />-->
        <el-button
          style="border-radius: 0px 25px 25px 0px;padding: 0 26px"
          icon="el-icon-search"
          type="primary"
          round
          @click="$router.push('/avpl/search?search=' + search.join(','))"
        />
      </div>
    </div>
    <div class="chartTitle">总览</div>
    <div style="font-size: 20px">
      <el-row :gutter="15">
        <el-col :span="6">
          <el-card>
            BU 总览
            <buchart :chart-data="buOverview" />
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            OEM
            <oemchart />

          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card>
            EMS
            <buchart />

          </el-card>
        </el-col>
      </el-row>

      <transition name="fade">
        <div v-if="isOpen">
          <el-row :gutter="15">
            <el-col :span="12">
              <el-card>
                <span class="barTitle">PRODUCT GR <div> Top10</div></span>
                <bar-chart />
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card>
                <span class="barTitle">Part Type <div> Top10</div></span>

                <bar-chart />
              </el-card>
            </el-col>

          </el-row>
          <el-row :gutter="15">
            <el-col :span="12">
              <el-card>
                <span class="barTitle">MFG <div> Top10</div></span>

                <bar-chart />
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card>
                <span class="barTitle">Brose PNs <div> (Exclude ECU)</div></span>
                <pnscard />
              </el-card>
            </el-col>

          </el-row>
        </div>
      </transition>
      <el-button style="width: 100%" @click="isOpen = !isOpen">
        {{ isOpen ? '收起' : '展开' }}
        <i :class=" isOpen? 'el-icon-arrow-up':'el-icon-arrow-down'" />
      </el-button>

    </div>

    <div class="chartTitle">搜索</div>
    <div style="margin-top: 35px;">
      <div style="display: flex;">
        <search-item
          v-for="item in searchList"
          style="flex: 0 1 25%"
          :card-data="item"
        />
      </div>
    </div>
  </div></template>
<script>
import Buchart from '@/views/avpl/advanceSearch/component/buchart.vue'
import SearchItem from '@/views/avpl/advanceSearch/component/searchItem.vue'
import Oemchart from '@/views/avpl/advanceSearch/component/oemchart.vue'
import BarChart from '@/views/dashboard/BarChart.vue'
import pnscard from '@/views/avpl/advanceSearch/component/pnscard.vue'
import mixin from './mixin'

export default {
  name: 'Home',
  components: { BarChart, Oemchart, SearchItem, Buchart, pnscard },
  mixins: [mixin],

  data() {
    return {
      search: [],
      options: []
    }
  },
  async mounted() {
    if (this.categoryData.length === 0 && this.buOverview.length === 0 && this.mfgData.length === 0) {
      await this.getData()
    }
  }
}
</script>

<style scoped lang="scss">
.barTitle{
  font-size: 20px;
  div{
    display: inline;
    font-size: 14px;
    margin-left: 5px;
    vertical-align: top;

  }
}
.el-row{
  margin-bottom: 15px;
}
.chartTitle{
  font-size: 24px;
  margin-bottom: 5px;
  color: #383838
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}
.searchText{
  ::v-deep .el-input__inner{
    border-radius: 25px 0 0 25px
  }
}

</style>
