import { DICT_TYPE } from '@/utils/dict'
import { getBuOverview, getCategoryOverview, getMfgOverview } from '@/api/avpl/avplCoordination'

export default {
  data() {
    return {
      isOpen: false,
      buOverview: [],
      categoryData: [],
      mfgData: []
    }
  },
  computed: {
    searchList() {
      const temp = [
        {
          name: DICT_TYPE.COMMON_PURCHASEORG,
          btList: this.buOverview.map(a => {
            return {
              name: a.orgId,
              count: a.countNum
            }
          })
        }, {
          name: 'MFG',
          btList: this.mfgData.map(a => {
            return {
              name: a.mfg,
              count: a.countNum
            }
          })
        }, {
          name: DICT_TYPE.COMMON_CATEGORY,
          btList: this.categoryData.map(a => {
            return {
              name: a.categoryId,
              count: a.countNum
            }
          })
        }
      ]
      return temp
    }
  },
  methods: {
    async getData() {
      await Promise.all([this.getBu(),
        this.getMfg(),
        this.getCategory()])
    },
    async getBu() {
      const data = await getBuOverview()
      this.buOverview = data.data
    },
    async getMfg() {
      const data = await getMfgOverview()
      this.mfgData = data.data
    },
    async getCategory() {
      const data = await getCategoryOverview()
      this.categoryData = data.data
    }
  }
}
