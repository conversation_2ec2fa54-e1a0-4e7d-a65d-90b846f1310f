
<template>
  <div class="search">
    <el-card v-if="queryParams.searchName&& btList.length" style="padding: 10px 5px">
      <div style="font-size: 20px;font-weight: bold;margin-bottom: 15px">{{ queryParams.searchName }}</div>
      <expandable :max-height="42">
        <el-button
          v-for="item in btList"
          :key="item.name"
          style="min-width: 110px;margin-top: 5px"
          round
          :type="item.selected ? 'primary':''"
          @click="item.selected = !item.selected;btList = [...btList];init()"
        >
          {{ getDictDataLabel(queryParams.searchType,item.name) || item.name }}

        </el-button>
      </expandable>

    </el-card>
    <el-card v-if="!queryParams.searchName">
      <div class="searchText" style="width: 60%;display: inline-flex;margin: 15px 0">
        <el-select
          ref="searchText"
          v-model="queryParams.search"
          multiple
          allow-create
          default-first-option
          filterable
          style="width: 80%"
          clearable
          @keyup.enter.native="init();$refs.searchText.blur()"
        >
          <el-option
            v-for="item in options"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
        <!--            <el-input v-model="queryParams.search" @keyup.enter.native="init" />-->
        <el-button
          style="border-radius: 0px 25px 25px 0px;padding: 0 26px"
          icon="el-icon-search"
          type="primary"
          round
          @click="init"
        />
      </div>

    </el-card>
    <div style="display: flex">
      <div style="width: 338px;flex:none;padding: 10px 5px 10px 0">
        <el-card>
          <template #header>
            <div style="display: flex;justify-content: space-between">
              <span style="font-size: 16px">
                <svg-icon icon-class="filter" />
                筛选
              </span>
              <el-button type="text" @click="clearFilter">清空</el-button>
            </div>

          </template>
          <div v-for="item in filterSearchQuery" class="filter">
            <div style="margin: 10px 0;font-size: 14px; color: #929292">{{ item.name }}</div>

            <el-scrollbar class="filterItem">
              <el-input v-model="item.search" size="mini" style="margin: 8px 0" />
              <el-checkbox-group

                v-model="queryParams[item.searchValue]"
                clearable
                filterable
                @change="init"
              >
                <el-checkbox
                  v-for="re in avplFilterList(item)"
                  :key="re.name+re.count"
                  style="display: block;margin-bottom: 5px"
                  :label="re.name"
                  :value="re.name"
                >
                  {{ getDictDataLabel(item.searchType, re.name) || re.name }}
                </el-checkbox>
              </el-checkbox-group>
            </el-scrollbar>
          </div>

        </el-card>
      </div>

      <div style="flex: auto;padding: 10px 5px">
        <el-card style="padding: 10px">
          <div v-if="queryParams.searchName" class="searchText" style="width: 60%;display: inline-flex;margin: 15px 0">
            <el-select
              ref="searchText"
              v-model="queryParams.search"
              multiple
              allow-create
              default-first-option
              filterable
              style="width: 80%"
              clearable
              @keyup.enter.native="init();$refs.searchText.blur()"
            >
              <el-option
                v-for="item in options"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
            <!--            <el-input v-model="queryParams.search" @keyup.enter.native="init" />-->
            <el-button
              style="border-radius: 0px 25px 25px 0px;padding: 0 26px"
              icon="el-icon-search"
              type="primary"
              round
              @click="init"
            />
          </div>

          <div
            v-loading="loading"
          >

            <el-descriptions
              v-for="item in tableData"
              class="description"
              :label-style="{ color: '#929292'}"
              :content-style="{
                'vertical-align': 'top'
              }"
              :column="8"
              direction="vertical"
            >
              <template #title>
                <div style="display: flex;justify-content: space-between;width: 100%">
                  <div style="font-size: 18px;font-weight: bold" v-html="item.filtermaterialCode" />
                  <el-button
                    plain
                    type="primary"
                    round
                    @click="$router.push(`/avpl/Avpldetail/${item.materialCode}?materialCode=${item.materialCode}&mfg=${item.mfg}&mpn=${item.mpn}&orgId=${item.orgId}`)"
                  >详情</el-button>
                </div>
              </template>
              <!--              <el-descriptions-item label="物料编码">-->
              <!--                {{ item.materialCode }}-->
              <!--              </el-descriptions-item>-->
              <el-descriptions-item label="物料描述">
                <span v-html="item.filtermaterialDescription" />
              </el-descriptions-item>
              <el-descriptions-item label="MFG/MPN">
                {{ item.mfg }}
                <span v-if="item.mfg&&item.mpn">/</span>
                {{ item.mpn }}

              </el-descriptions-item>
              <el-descriptions-item label="品类">
                <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="item.categoryId" />
              </el-descriptions-item>
              <el-descriptions-item :span="2">

                <template slot="label">
                  <div style="display: flex">
                    <div style="flex: 0 0 50%"> 项目名称</div>
                    <div style="flex: 0 0 50%"> 供应商/ECU</div>
                    <!--                    todo-->

                  </div>

                </template>
                <el-scrollbar>
                  <div style="display: flex;">
                    <div
                      style=" flex: 1 0 50%"
                    >
                      <div
                        v-for="a in item.children"
                        :style="{
                          height:` ${21*a.children.length}px`

                        }"
                        v-html="a.filterProjectNo || a.projectNo"
                      />
                    </div>
                    <div style="flex: 1 0 50%">
                      <div v-for="a in item.children">
                        <div v-for=" b in a.children">
                          {{ b.supplierName }}
                        </div>
                      </div>
                    </div>

                  </div>
                </el-scrollbar>

              </el-descriptions-item>
              <el-descriptions-item label="采购组织">
                <dict-tag :value="item.orgId" :type="DICT_TYPE.COMMON_PURCHASEORG" />
              </el-descriptions-item>
              <el-descriptions-item label="物料基准价">
                {{ item.materialBasePrice }}
              </el-descriptions-item>
              <el-descriptions-item label="物料最低价">
                {{ item.materialLowestPrice }}
              </el-descriptions-item>

            </el-descriptions>
            <el-empty v-if="tableData.length === 0" :image-size="200" />
            <pagination
              v-show="total > 0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNo"
              :total="total"
              @pagination="init"
            />
          </div>

        </el-card>
      </div>
    </div>
  </div>
</template>
<script>
import { DICT_TYPE, getDictDataLabel, getDictDatas } from '@/utils/dict'
import dayjs from 'dayjs'
import { mapState } from 'vuex'
import Expandable from '@/components/Expand/index.vue'
import { getAdvancedSearchPage } from '@/api/avpl/avplCoordination'
import mixin from './mixin'

export default {
  name: 'Avplsearch',
  components: { Expandable },
  mixins: [mixin],
  data() {
    return {
      tableData: [
      ],
      countryRegion: [],
      searchQuery: [
        {
          name: 'BU',
          searchType: DICT_TYPE.COMMON_PURCHASEORG,
          searchValue: 'orgIds',
          search: ''
        },
        {
          name: '品类',
          searchType: DICT_TYPE.COMMON_CATEGORY,
          searchValue: 'categoryIds',
          search: ''
        },
        {
          name: 'MFG',
          searchType: 'MFG',
          searchValue: 'mfgs',
          search: ''

        }

      ],

      total: 0,
      options: [],
      queryParams: {
        searchType: '',
        searchName: '',
        search: [
        ],
        orgIds: [
        ],
        categoryIds: [
        ],
        mfgs: [

        ],
        pageNo: 1,
        pageSize: 10

      },
      loading: false,
      btList: []
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    },
    filterSearchQuery() {
      return this.searchQuery.filter(a => a.searchType !== this.queryParams.searchType)
    }
    // filterSearch: this.searchTypeList.filter(a=>a.searchType === )
  },
  async mounted() {
    this.queryParams.searchType = this.$route.query.searchType
    this.queryParams.searchName = this.$route.query.searchName
    if (this.$route.query.search) {
      this.queryParams.search = this.$route.query.search.split(',')
    }
    if (this.categoryData.length === 0 && this.buOverview.length === 0 && this.mfgData.length === 0) {
      await this.getData()
    }
    if (this.queryParams.searchType) {
      const temp = this.searchList.filter(a => a.name === this.$route.query.searchType)[0].btList
      temp.forEach(a => {
        a.selected = String(a.name) === this.$route.query.selected
      })
      this.btList = temp
    }
    this.init()
  },
  methods: {
    getDictDataLabel,
    dayjs, getDictDatas,
    init() {
      this.loading = true
      getAdvancedSearchPage({ ...this.queryParams,
        [this.searchQuery.find(a => a.searchType === this.queryParams.searchType)?.searchValue]:
          this.btList.filter(a => a.selected).map(a => a.name)
      }).then(res => {
        res.data.list.forEach(a => {
          a.filtermaterialCode = a.materialCode
          a.filtermaterialDescription = a.materialDescription
          if (this.queryParams.search.length) {
            const searchItem = [...this.queryParams.search].sort((a, b) => b.length - a.length)
            const filteredArray = searchItem.reduce((acc, current) => {
              if (!acc.some(item => item.includes(current))) {
                acc.push(current)
              }
              return acc
            }, [])
            filteredArray.forEach(b => {
              const temp = `<span style="color: #4d93b9;font-weight: bold">${b}</span>`
              if (a.filtermaterialCode.includes(b)) {
                a.filtermaterialCode = a.filtermaterialCode.replace(b, temp)
              }
              if (a.filtermaterialDescription.includes(b)) {
                a.filtermaterialDescription = a.filtermaterialDescription.replace(b, temp)
              }
              a.children.forEach(c => {
                c.filterProjectNo = c.projectNo
                if (c.filterProjectNo.includes(b)) {
                  c.filterProjectNo = c.filterProjectNo.replace(b, temp)
                }
              })
            })
          }
        })
        this.loading = false
        this.tableData = res.data.list
        this.total = res.data.total
      })
    },
    clearFilter() {
      this.queryParams = {
        search: [
        ],
        orgIds: [
        ],
        categoryIds: [
        ],
        mfgs: [

        ],
        pageNo: 1,
        pageSize: 10,
        searchType: this.$route.query.searchType,
        searchName: this.$route.query.searchName

      }
      this.searchQuery.forEach(a => {
        a.search = ''
      })
      // this.btList.forEach(a => {
      //   a.selected = false
      // })
      this.init()
    },
    avplFilterList(item) {
      if (item.search) {
        return this.searchList.find(a => a.name === item.searchType)?.btList.filter(a =>
          String(a.name).indexOf(item.search) > -1 ||
          getDictDataLabel(item.searchType, a.name).indexOf(item.search) > -1)
      } else {
        return this.searchList.find(a => a.name === item.searchType)?.btList
      }
    }
  }
}
</script>

<style scoped lang="scss">
.filter{
  padding-bottom: 10px;
  border-bottom: 1px solid #eaeaea;
  .filterItem {
    ::v-deep .el-scrollbar__wrap {
      max-height: 200px;
      overflow-x: hidden;
    }
  }
}

.search{
  padding: 15px 25px;
}
.searchText{
  ::v-deep .el-input__inner{
    border-radius: 25px 0 0 25px
  }
}

::v-deep .el-descriptions__header{
  margin-bottom: 0;
}

::v-deep .el-descriptions__title{
  width: 100%;
}
.description:not(:last-child){
  border-bottom: 1px solid #eaeaea;
  margin-bottom: 10px;
}
.menu {
  list-style: none;
  margin: 10px 0;
  font-size: 14px;
  li {
    width: 100%;
    margin: 5px 0;
    // display: flex;
    div {
      // width: 85%;
      margin-left: 5px;
    }
    .select-label {
      width: 100%;
      margin-top: 10px;
      font-weight: bold;
      font-size: 14px;
      // position:absolute;
      // top:5px
    }
  }
}
.selectedMenu {
  background: #4d93b9;
  color: white;
  padding: 2px 15px;
  margin-right: 3%;
  border-radius: 2px;
  display: inline-block;
  min-width: 70px;
}
.unselectedMenu {
  color: #295b93;
  padding: 2px 15px;
  margin-right: 3%;
  display: inline-block;
  min-width: 70px;
}
::v-deep .el-scrollbar {
  .el-scrollbar__wrap {
    max-height: 90px; // 最大高度
    overflow-x: hidden; // 隐藏横向滚动栏
  }
}
</style>
