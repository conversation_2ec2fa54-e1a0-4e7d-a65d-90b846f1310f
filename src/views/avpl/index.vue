<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane :label="$t('avpl.avpl')" name="first" />
    </el-tabs>
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.search	"
        :placeholder="$t('material.pleaseEnterTheMaterialCodeMaterialDescriptionSpecificationMfgMpn')"
        clearable
        style="flex: 0 1 40%"
        @keyup.enter.native="getList"
      />
      <el-button type="primary" plain @click="queryParams.pageNo = 1;getList();">{{ $t('common.search') }}</el-button>
      <el-button plain style="margin-left: 0" @click="handleClick">{{ $t('common.reset') }}</el-button>
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button type="text">
          {{ $t('common.advancedSearch') }}

        </el-button>
        <i
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>

    </div>
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="189px" size="small">
      <el-form-item :label="$t('material.materialCode')" class="searchItem" prop="materialCode">
        <el-select
          v-model="queryParams.materialCodeList"
          multiple
          filterable
          class="searchValue"
          remote
          reserve-keyword
          :placeholder="$t('auth.pleaseEnterKeywords')"
          :remote-method="querySearchAsync"
          :loading="selectLoading"
        >
          <el-option
            v-for="item in materialList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <!--        <el-input-->
        <!--          v-model="queryParams.materialCode"-->
        <!--          :placeholder="$t('rfq.pleaseEnterTheMaterialCode')"-->
        <!--          class="searchValue"-->
        <!--          clearable-->
        <!--        />-->
      </el-form-item>
      <el-form-item :label="$t('supplier.supplier')" class="searchItem" prop="supplierName">
        <el-input
          v-model="queryParams.supplierName"
          :placeholder="$t('order.pleaseEnterTheSuppliersShortNameOrCode')"
          class="searchValue"
          clearable
        />
      </el-form-item>
      <el-form-item :label="$t('material.purchaseType')" class="searchItem" prop="purchaseType">
        <el-select v-model="queryParams.purchaseType" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.MATERIAL_PURCHASE_TYPE)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('material.materialDescription')" class="searchItem" prop="materialDescription">
        <el-input
          v-model="queryParams.materialDescription"
          :placeholder="$t('material.pleaseEnterTheMaterialDescription')"
          class="searchValue"
          clearable
        />
      </el-form-item>
      <el-form-item :label="$t('material.category')" class="searchItem" prop="categories">
        <el-cascader
          v-model="queryParams.categories	"
          :options="categoryList"
          :placeholder="$t('material.pleaseSelectCategory')"
          :props="{ value: 'id',label:'name'}"
          class="searchValue"
          clearable
          filterable
        />
      </el-form-item>
      <el-form-item :label="$t('common.sourcing')" class="searchItem" prop="sourcings">
        <el-select v-model="queryParams.sourcings	" class="searchValue" clearable filterable multiple>
          <el-option
            v-for="dict in sourcingSources"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('supplier.purchasingOrganization')" class="searchItem" prop="factoryIds">
        <el-select v-model="queryParams.purchaseOrgs" class="searchValue" clearable multiple>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG,0)"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('material.manufacturer')" class="searchItem" prop="mfg">
        <el-input
          v-model="queryParams.mfg"
          :placeholder="$t('material.pleaseEnterTheManufacturer')"
          class="searchValue"
          clearable
        />
      </el-form-item>
      <el-form-item :label="$t('material.manufacturersPartNumber')" class="searchItem" prop="mpn">
        <el-input
          v-model="queryParams.mpn"
          :placeholder="$t('material.pleaseEnterTheManufacturersPartNumber')"
          class="searchValue"
          clearable
        />
      </el-form-item>
      <el-form-item :label="$t('material.procurementGroup')" class="searchItem" prop="pgId">
        <el-select v-model="queryParams.pgId" class="searchValue" clearable>
          <el-option :label="$t('order.whole')" value="" />
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.SYSTEM_PURCHASE_GROUP)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('order.timeType')" class="searchItem" prop="dateType">

        <el-select v-model="queryParams.dateType" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.AVPL_DATE_TYPE)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" label=" ">
        <el-date-picker
          v-model="queryParams.time"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          class="searchValue"
          type="daterange"
          value-format="yyyy-MM-dd"
        />

      </el-form-item>
      <el-form-item :label="$t('common.status')" class="searchItem" prop="status">
        <el-select v-model="queryParams.status" class="searchValue" clearable>
          <el-option :label="$t('order.whole')" value="" />
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.AVPL_STATUS)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('avpl.source')" class="searchItem" prop="source">
        <el-select v-model="queryParams.source" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.AVPL_SOURCE)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <vxe-grid
      v-if="activeName === 'first'"
      ref="avplIndex"
      :data="list"
      :loading="loading"
      :span-method="mergeRowMethod"
      v-bind="girdOption"
      @sort-change="sortMethod"
      @filter-change="filterMethod"
      @checkbox-change="checkBoxChange"
      @checkbox-all="checkBoxAllChange"
    >
      <template #materialCode="{row}">
        <copy-button
          type="text"
          @click="$router.push(`/material/materialView/${row.materialCode}?id=${row.materialId}&viewOnly=1`)"
        >
          {{ row.materialCode }}
        </copy-button>
      </template>
      <template #operation="{row}">
        <el-button
          v-has-permi="['avpl:materials:query']"
          type="text"
          @click="$router.push(`/avpl/trending/${row.materialCode}`)"
        >
          {{ $t('avpl.changeTrend') }}
        </el-button>
      </template>
      <template #orgId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="row.orgId" />
      </template>
      <template #sourcing="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcing" />
        <dict-tag :type="DICT_TYPE.RFQ_SOURCING_SOURCES" :value="row.sourcing" />
      </template>
      <template #source="{row}">
        <dict-tag :type="DICT_TYPE.AVPL_SOURCE" :value="row.source" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.AVPL_STATUS" :value="row.status" />
      </template>
      <template #pgId="{row}">
        <dict-tag :type="DICT_TYPE.SYSTEM_PURCHASE_GROUP" :value="row.pgId" />
      </template>
      <template #purchaseType="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_PURCHASE_TYPE" :value="row.purchaseType" />
      </template>
      <template #categoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
      </template>
      <template #basicUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.basicUnit" />
      </template>
      <template #orderUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.orderUnit" />
      </template>
      <template #taxRate="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_RATE" :value="row.taxRate" />
      </template>
      <template #priceCategory="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_PRICE_CATEGORY" :value="row.priceCategory" />
      </template>
      <template #originalCurrency="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.originalCurrency" />
      </template>
      <template #paymentMethod="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_PAYMENT_DAYS" :value="row.paymentMethod" />
      </template>
      <template #quotationQuantityFrom="{row}">
        <number-format :value="row.quotationQuantityFrom" />
      </template>
      <template #quotationQuantityTo="{row}">
        <number-format :value="row.quotationQuantityTo" />
      </template>
      <template #originalUnitPriceWithoutTax="{row}">
        <number-format :value="row.originalUnitPriceWithoutTax" />
      </template>
      <template #originalUnitPriceTax="{row}">
        <number-format :value="row.originalUnitPriceTax" />
      </template>
      <template #unitPriceWithoutTaxPriceUnit="{row}">
        <number-format :value="row.unitPriceWithoutTaxPriceUnit" />
      </template>
      <template #unitPriceIncludesTaxPriceUnit="{row}">
        <number-format :value="row.unitPriceIncludesTaxPriceUnit" />
      </template>
      <template #priceType="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_PRICE_TYPE" :value="row.priceType" />
      </template>

      <template #toolbar_buttons>
        <el-row :gutter="10" class="mb8" style="width: 100%">
          <el-col :span="10" style="display: flex">
            <el-button
              size="mini"
              type="primary"
              plain
              @click="downLoadExcel"
            >
              {{ $t('order.download') }}
            </el-button>
            <el-button v-has-permi="['avpl:materials:create-rfq']" plain size="mini" type="primary" @click="createRfq">
              {{ $t('avpl.materialRfq') }}
            </el-button>
            <el-button v-has-permi="['avpl:materials:refresh-hash']" plain size="mini" type="primary" @click="refreshHistoryHash">
              {{ $t('auth.updateTheHistoricalPriceHashOfAvpl') }}
            </el-button>
          </el-col>
          <el-col :span="14">
            <right-toolbar
              :custom-columns.sync="girdOption.columns"
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              @queryTable="getList"
            />
          </el-col>
        </el-row>
        <el-alert
          v-if="selectedRecord.length"
          show-icon
          type="success"
        >
          <template slot="title">{{ $t('common.selectedTotal', {selectedTotal: selectedRecord.length}) }}
            <el-button style="padding: 0;margin-left: 10px" type="text" @click="clearSelected">{{
                $t('rfq.empty')
              }}
            </el-button>
          </template>
        </el-alert>
      </template>

    </vxe-grid>

    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="handleSearch"
    />
  </div>
</template>
<script>
import {
  createMaterialsForRFQ,
  exportHeaderExcel,
  getAVPLDataList,
  getAVPLMaterialAllIds, refreshHistoryHash
} from '@/api/avpl/avplCoordination'
import { DICT_TYPE, getDictDataLabel, getDictDatas } from '@/utils/dict'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import store from '@/store'
import { getMaterialInfo } from '@/api/material/main'
import event from '@/views/dashboard/mixins/event'

export default {
  name: 'Avplindex',
  components: { ShowOrEdit },
  mixins: [event],
  data() {
    return {
      selectLoading: false,
      materialList: [],
      showSearch: false,
      categoryList: [],
      sourcingSources: [],
      queryParams: {
        search: '',
        materialCodeList: this.$route.query.materialCodeList ? this.$route.query.materialCodeList.split(',') : [],
        supplierName: '',
        purchaseType: '',
        materialDescription: '',
        categories: [],
        categoryId: '',
        sourcings: [],
        source: '',
        purchaseOrgs: [],
        mfg: '',
        mpn: '',
        pgId: '',
        status: 'price_valid',
        dateType: '',
        beginDate: '',
        endDate: '',
        sortBy: '',
        sortField: '',
        pageNo: 1,
        pageSize: 10
      },
      list: [],
      loading: false,
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'AVPLDelivery',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          keyField: 'materialSupplierRelId',
          isHover: true
        },
        columns: [
          { title: '', type: 'checkbox', visible: true, width: 30, fixed: 'left' },
          { title: this.$t('common.creationDate'), sortable: true, field: 'createTime', visible: true, width: 100 },
          {
            title: this.$t('common.sourcing'),
            field: 'sourcing',
            visible: true,
            width: 100
          },
          {
            title: this.$t('avpl.source'), field: 'source',
            slots: {
              default: 'source'
            },
            visible: true, width: 100
          },
          {
            title: this.$t('material.materialCode'),
            slots: {
              default: 'materialCode'
            }, field: 'materialCode', visible: true, width: 100
          },
          { title: this.$t('material.specificationAndModel'), field: 'specifications', visible: true, width: 100 },
          { title: this.$t('material.materialDescription'), field: 'materialDescription', visible: true, width: 100 },
          {
            title: this.$t('material.basicUnit'),
            field: 'basicUnit',
            slots: { default: 'basicUnit' },
            visible: true,
            width: 100
          },
          { title: this.$t('material.textureOfMaterial'), field: 'material', visible: true, width: 100 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          { title: this.$t('order.edition'), field: 'version', visible: true, width: 100 },
          { title: this.$t('material.drawingVersion'), field: 'drawingVersion', visible: true, width: 100 },
          {
            title: this.$t('material.category'),
            field: 'categoryId',
            slots: { default: 'categoryId' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('material.purchaseType'), field: 'purchaseType',
            slots: { default: 'purchaseType' }, visible: true, width: 100
          },
          {
            title: this.$t('rfq.priceCategory'),
            field: 'priceCategory',
            slots: { default: 'priceCategory' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('supplier.purchasingOrganization'),
            slots: {
              default: 'orgId'
            }, field: 'orgId', visible: true, width: 100
          },
          { title: this.$t('order.customerName'), field: 'customName', visible: true, width: 100 },
          { title: this.$t('avpl.approvalFormNo'), field: 'approvalNo', visible: true, width: 100 },
          {
            title: this.$t('material.procurementGroup'),
            field: 'pgId',
            slots: { default: 'pgId' },
            visible: true,
            width: 100
          },
          { title: this.$t('common.status'), field: 'status', slots: { default: 'status' }, visible: true, width: 100 },
          { title: this.$t('rfq.eventName'), field: 'eventName', visible: true, width: 100 },
          { title: this.$t('rfq.rfqItemNo'), field: 'projectNo', visible: true, width: 100 },

          { title: this.$t('supplier.supplierCode'), field: 'supplierCode', visible: true, width: 100 },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
          { title: this.$t('avpl.manufacturerQuoted'), field: 'quotationMfg', visible: true, width: 100 },
          { title: this.$t('avpl.quotedManufacturersPartNumber'), field: 'quotationMpn', visible: true, width: 100 },
          {
            title: this.$t('supplier.taxRate'),
            field: 'taxRate',
            slots: { default: 'taxRate' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('avpl.originalCurrency'),
            field: 'originalCurrency',
            slots: { default: 'originalCurrency' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('material.purchasingUnit'),
            field: 'orderUnit',
            slots: { default: 'orderUnit' },
            visible: true,
            width: 100
          },
          { title: this.$t('material.priceUnit'), field: 'priceUnit', visible: true, width: 100 },
          { title: this.$t('avpl.purchaseUnitQuantity'), field: 'orderUnitQuantity', visible: true, width: 100 },
          { title: this.$t('avpl.quantityInBuom'), field: 'measureUnitsQuantity', visible: true, width: 100 },
          { title: this.$t('avpl.pricingUnit'), field: 'salesUnit', visible: true, width: 100 },
          {
            title: this.$t('rfq.unitPriceExcludingTaxpriceUnit'),
            field: 'unitPriceWithoutTaxPriceUnit',
            slots: { default: 'unitPriceWithoutTaxPriceUnit' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.unitPriceIncludingTaxpriceUnit'),
            field: 'unitPriceIncludesTaxPriceUnit',
            slots: { default: 'unitPriceIncludesTaxPriceUnit' },
            visible: true,
            width: 100
          },
          { title: this.$t('avpl.minimumPackageQuantity'), field: 'mpq', visible: true, width: 100 },
          { title: this.$t('avpl.minimumOrderQuantity'), field: 'moq', visible: true, width: 100 },
          { title: this.$t('avpl.priceValidityPeriodStartTime'), field: 'recommendedPriceStart', visible: true, width: 100 },
          { title: this.$t('avpl.priceValidUntil'), field: 'recommendedPriceExpires', visible: true, width: 100 },
          { title: this.$t('rfq.deliveryPerioddays'), field: 'deliveryPeriod', visible: true, width: 100 },
          { title: this.$t('avpl.deliveryMethod'), field: 'deliveryMethod', visible: true, width: 100 },
          {
            title: this.$t('supplier.paymentMethod'), field: 'paymentMethod',
            slots: {
              default: 'paymentMethod'
            },
            visible: true, width: 100
          },
          { title: this.$t('avpl.minimumConsumption'), field: 'minimumConsumption', visible: true, width: 100 },
          { title: this.$t('order.supplierRemarks'), field: 'remark', visible: true, width: 100 },

          {
            title: this.$t('rfq.orderQuantityFrom'),
            field: 'quotationQuantityFrom',
            slots: { default: 'quotationQuantityFrom' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.orderQuantityArrived'),
            field: 'quotationQuantityTo',
            slots: { default: 'quotationQuantityTo' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.quotedUnitPriceExcludingTax'),
            field: 'originalUnitPriceWithoutTax',
            slots: { default: 'originalUnitPriceWithoutTax' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.quotationUnitPriceIncludingTax'),
            field: 'originalUnitPriceTax',
            slots: { default: 'originalUnitPriceTax' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('auth.ladderQuotationJudgmentValue'),
            field: 'price_type',
            slots: { default: 'priceType' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('common.operate'),
            showOverflow: false,
            field: 'operation',
            slots: {
              default: 'operation'
            },
            visible: true,
            width: 100,
            fixed: 'right'
          }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      total: 0,
      activeName: 'first',
      selectedRecord: [],
      mergeRowFields: {
        // 根据物料合并的字段
        MaterialFields: [
          'createTime',
          'sourcing',
          'source',
          'materialCode',
          'specifications',
          'materialDescription',
          'basicUnit',
          'material',
          'mfg',
          'mpn',
          'version',
          'drawingVersion',
          'categoryId',
          'purchaseType',
          'priceCategory',
          'orgId',
          'customName',
          'approvalNo',
          'pgId',
          'eventName',
          'projectNo'
        ],
        // 根据物料+供应商合并的字段
        MaterialSupplierFields: [
          'supplierCode',
          'supplierName',
          'quotationMfg',
          'quotationMpn',
          'taxRate',
          'originalCurrency',
          'orderUnit',
          'priceUnit',
          'orderUnitQuantity',
          'measureUnitsQuantity',
          'salesUnit',
          'mpq',
          'moq',
          'recommendedPriceStart',
          'recommendedPriceExpires',
          'deliveryPeriod',
          'deliveryMethod',
          'paymentMethod',
          'minimumConsumption',
          'remark',
          'status'
        ],
        // 操作列
        operationFields: [
          'operation'
        ]
      }
    }
  },
  computed: {
    store() {
      return store
    }
  },
  activated() {
    this.getList()
    this.getCategories()
    this.getSourcingSourcess()
  },
  methods: {
    getList() {
      this.loading = true
      this.queryParams.categoryId = this.queryParams.categories.at(-1)
      if (this.queryParams.time) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      getAVPLDataList(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    // 物料列表的跨页全选框
    checkBoxAllChange({ checked }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.avplIndex
      if (checked) {
        const avplMaterialIds = Array.from(new Set([...this.selectedRecord]))
        if (avplMaterialIds?.length + this.total > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.clearCheckboxRow()
          // 当前选中的数据key数组
          const selectIds = grid.getCheckboxRecords().map(item => item.materialSupplierRelId)
          // 取交集
          const arr = avplMaterialIds.filter(x => selectIds.includes(x))
          if (arr.length > 0) {
            arr.forEach((id) => {
              grid.setCheckboxRow(grid.getRowById(id), true)
            })
          }
        } else {
          this.checkAllData(checked)
        }
      } else {
        this.checkAllData(checked)
      }
    },
    querySearchAsync(query) {
      if (query !== '') {
        this.selectLoading = true
        getMaterialInfo({
          materialCode: query
        }).then(res => {
          this.selectLoading = false
          this.materialList = res.data.map(item => {
            return {
              value: item.materialCode,
              label: item.materialCode
            }
          })
        })
      } else {
        this.materialList = []
      }
    },
    checkAllData(checked) {
      getAVPLMaterialAllIds(this.queryParams).then(res => {
        if (checked) {
          this.selectedRecord = Array.from(new Set([...this.selectedRecord, ...res.data]))
        } else {
          const avplMaterialIds = Array.from(new Set([...res.data]))
          this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => !avplMaterialIds.includes(x))))
        }
      })
    },
    createRfq() {
      const selected = this.selectedRecord
      if (selected.length === 0) {
        this.$message.warning(this.$t('avpl.pleaseSelectAvplMaterial'))
        return
      }
      createMaterialsForRFQ(
        selected
      ).then(res => {
        this.$router.push(`/rfq/processHome/${res.data.projectNo}?id=${res.data.id}&projectNo=${res.data.projectNo}&code=${'whole_enquiry'}`)
      })
    },
    /**
     * 更新历史价格的hash
     */
    refreshHistoryHash() {
      const selected = this.selectedRecord
      if (selected.length === 0) {
        this.$message.warning(this.$t('avpl.pleaseSelectAvplMaterial'))
        return
      }
      refreshHistoryHash(
        selected
      ).then(res => {
        this.$message({
          message: this.$t('order.operationSucceeded'),
          type: 'success'
        })
      })
    },
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const cellValue = row[column.property]
      if ((cellValue !== undefined && this.mergeRowFields.MaterialFields.includes(column.property))) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue && row.avplMaterialId === prevRow.avplMaterialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.avplMaterialId === nextRow.avplMaterialId && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      } else if ((['checkbox'].includes(column.type) || this.mergeRowFields.operationFields.includes(column.property)) && row) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && row.avplMaterialId === prevRow.avplMaterialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.avplMaterialId === nextRow.avplMaterialId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      } else if (this.mergeRowFields.MaterialSupplierFields.includes(column.property) && row) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && row.materialSupplierRelId === prevRow.materialSupplierRelId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.materialSupplierRelId === nextRow.materialSupplierRelId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    // 项目寻源采购的数据源组装
    getSourcingSourcess() {
      this.sourcingSources.push(...getDictDatas(DICT_TYPE.COMMON_USERS, 0))
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    handleQuery() {
    },
    resetQuery() {
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.handleSearch()
    },
    filterMethod() {
    },
    handleClick() {
      this.queryParams = {
        search: '',
        materialCode: '',
        supplierName: '',
        purchaseType: '',
        materialDescription: '',
        categories: [],
        categoryId: '',
        sourcings: [],
        purchaseOrgs: [],
        mfg: '',
        mpn: '',
        pgId: '',
        status: '',
        dateType: '',
        beginDate: '',
        endDate: '',
        sortBy: '',
        sortField: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getList()
    },
    downLoadExcel() {
      switch (this.activeName) {
        case 'first':
          exportHeaderExcel(this.queryParams).then(res => {
            this.$download.excel(res, this.$t('auth.avplDataXlsx'))
          })
          break
      }
    },
    handleSearch() {
      switch (this.activeName) {
        case 'first':
          this.getList()
          break
      }
    },
    restFields() {
    },
    // 物料列表的单个选择框
    checkBoxChange({ checked, row }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.avplIndex
      if (checked) {
        const avplMaterialIds = this.selectedRecord
        if (avplMaterialIds?.length + 1 > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.setCheckboxRow(grid.getRowById(row.avplMaterialId), false)
        } else {
          this.selectedRecord.push(row.avplMaterialId)
        }
      } else {
        this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => row.avplMaterialId !== x)))
      }
    },
    // 跨页全选的清空
    clearSelected() {
      const grid = this.$refs.avplIndex
      this.selectedRecord = []
      grid.clearCheckboxRow()
    }
  }
}

</script>

<style lang="scss" scoped>
.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 189px);
  }
}

.searchTimeItem {
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 82px);
  }
}

.searchValue {
  width: 95%;
}
</style>
