<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'

import resize from '@/views/dashboard/mixins/resize'
export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '250px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ xaxisData, priceSeriesData } = {}) {
      this.chart.setOption({
        toolbox: {
          show: true,
          feature: {
            mark: { show: true },
            // restore: { show: true },
            saveAsImage: {
              show: true,
              pixelRatio: 1,
              title: this.$t('avpl.saveAsPicture'),
              type: 'png',
              lang: [this.$t('avpl.clickSave')]
            }
          }
        },
        xAxis: {
          data: xaxisData,
          boundaryGap: false,
          axisLabel: {
            color: '#999',
            formatter: function(value) {
              return value.split(' ')[0]
            }
          },
          axisLine: {
            color: '#999'
          },
          axisTick: {
            show: false
          }

        },
        grid: {
          x: 60,
          y: 60,
          x2: 60,
          y2: 60,

          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          axisLabel: {
            color: '#999'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [{
          // name: 'abc',
          smooth: true,
          type: 'line',
          data: priceSeriesData,
          animationDuration: 2800,
          animationEasing: 'cubicInOut'
        }
        ]
      })
    }
  }
}
</script>
