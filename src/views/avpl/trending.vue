<template>
  <div class="app-container">
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.search	"
        :placeholder="$t('avpl.pleaseEnterTheSupplier')"
        clearable
        style="flex: 0 1 40%"
        @keyup.enter.native="handleQuery"
      />
      <el-button plain type="primary" @click="queryParams.pageNo = 1;getList();">{{ $t('common.search') }}</el-button>
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button type="text">
          {{ $t('common.advancedSearch') }}

        </el-button>
        <i
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>

    </div>
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="120px" size="small">
      <el-row style="display: flex">
        <el-col :span="18">
          <el-row>
            <el-form-item :label="$t('supplier.supplier')" class="searchItem" prop="supplierName">
              <el-input
                v-model="queryParams.supplierName"
                :placeholder="$t('order.pleaseEnterTheSuppliersShortNameOrCode')"
                class="searchValue"
                clearable
              />
            </el-form-item>
            <el-form-item :label="$t('supplier.purchasingOrganization')" class="searchItem" prop="materialDescription">
              <el-select v-model="queryParams.purchaseOrgs" class="searchValue" clearable multiple>
                <el-option
                  v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG,0)"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('order.timeType')" class="searchItem" prop="dateType">
              <el-select v-model="queryParams.dateType" class="searchValue" clearable>
                <el-option
                  v-for="item in getDictDatas(DICT_TYPE.AVPL_DATE_TYPE)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-date-picker
                v-model="queryParams.time"
                :end-placeholder="$t('common.endDate')"
                :range-separator="$t('order.to')"
                :start-placeholder="$t('common.startDate')"
                class="searchValue"
                style="margin-top: 10px;"
                type="daterange"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-row>
          <el-row />
        </el-col>
        <el-col :span="6" style="margin:  0 auto">
          <div style="text-align: center">
            <el-button icon="el-icon-refresh" size="mini" @click="handleClick">{{ $t('common.reset') }}</el-button>
            <el-button plain icon="el-icon-search" size="mini" type="primary" @click="queryParams.pageNo = 1;getList();">{{
              $t('common.search')
            }}
            </el-button>

          </div>

        </el-col>
      </el-row>

    </el-form>
    <line-chart :chart-data="chartData" />

    <vxe-grid
      :data="list"
      :loading="loading"
      v-bind="girdOption"
    >
      <template #materialCode="{row}">
        <el-button
          type="text"
          @click="$router.push(`/material/materialView/${row.materialCode}?id=${row.materialId}&viewOnly=1`)"
        >
          {{ row.materialCode }}
        </el-button>
      </template>
      <template #operation="{row}">
        <el-button
          type="text"
          @click="$router.push(`/material/materialView/${row.materialCode}?id=${row.materialId}&viewOnly=1`)"
        >
          {{ $t('avpl.changeTrend') }}
        </el-button>
      </template>
      <template #orgId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="row.orgId" />
      </template>
      <template #pgId="{row}">
        <dict-tag :type="DICT_TYPE.SYSTEM_PURCHASE_GROUP" :value="row.pgId" />
      </template>
      <template #sourcing="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcing" />
        <dict-tag :type="DICT_TYPE.RFQ_SOURCING_SOURCES" :value="row.sourcing" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.AVPL_STATUS" :value="row.status" />
      </template>
      <template #purchaseType="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_PURCHASE_TYPE" :value="row.purchaseType" />
      </template>
      <template #categoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
      </template>
      <template #basicUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.basicUnit" />
      </template>
      <template #orderUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.orderUnit" />
      </template>
      <template #taxRate="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_RATE" :value="row.taxRate" />
      </template>
      <template #priceCategory="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_PRICE_CATEGORY" :value="row.priceCategory" />
      </template>
      <template #originalCurrency="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.originalCurrency" />
      </template>
      <template #toolbar_buttons>
        <el-row :gutter="10" class="mb8" style="width: 100%">
          <el-col :span="10" style="display: flex">
            <br>
          </el-col>
          <el-col :span="14">
            <right-toolbar
              :custom-columns.sync="girdOption.columns"
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              @queryTable="init"
            />
          </el-col>
        </el-row>
      </template>

    </vxe-grid>
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { getAVPLLineGraph, getAVPLMaterialPage } from '@/api/avpl/avplCoordination'

export default {
  name: 'Trending',
  components: {
    lineChart: () => import('@/views/avpl/lineChart')

  },

  data() {
    return {
      showSearch: false,
      total: 0,
      list: [],
      loading: false,
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'AVPLTrending',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: false
        },
        columns: [
          { title: this.$t('material.materialCode'), field: 'materialCode', visible: true, width: 100 },
          {
            title: this.$t('material.materialDescription'),
            field: 'materialDescription',
            visible: true,
            width: 100
          },
          {
            title: this.$t('supplier.purchasingOrganization'), field: 'orgId',
            slots: { default: 'orgId' },

            visible: true, width: 100
          },
          { title: this.$t('material.specificationAndModel'), field: 'specifications', visible: true, width: 100 },
          {
            title: this.$t('material.category'), field: 'categoryId',
            slots: { default: 'categoryId' },
            visible: true, width: 100
          },
          { title: this.$t('material.drawingVersion'), field: 'drawingVersion', visible: true, width: 100 },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          { title: this.$t('rfq.orderQuantityFrom'), field: 'quotationQuantityFrom', visible: true, width: 100 },
          { title: this.$t('rfq.orderQuantityArrived'), field: 'quotationQuantityTo', visible: true, width: 100 },
          {
            title: this.$t('avpl.unitPriceIncludingTax'),
            field: 'originalUnitPriceTax',
            visible: true,
            width: 100
          },
          {
            title: this.$t('avpl.unitPriceBeforeTax'),
            field: 'originalUnitPriceWithoutTax',
            visible: true,
            width: 100
          },
          {
            title: this.$t('system.currency'), field: 'originalCurrency',
            slots: { default: 'originalCurrency' },
            visible: true, width: 100
          },
          { title: this.$t('avpl.minimumPackagingQuantity'), field: 'moq', visible: true, width: 100 },
          { title: this.$t('avpl.minimumOrderQuantity'), field: 'mpq', visible: true, width: 100 },
          {
            title: this.$t('avpl.priceEffectiveEndDate'),
            field: 'recommendedPriceExpires',
            visible: true,
            width: 100
          },
          {
            title: this.$t('material.purchaseType'), field: 'purchaseType',
            slots: { default: 'purchaseType' },

            visible: true, width: 100
          },
          {
            title: this.$t('material.basicUnit'), field: 'basicUnit',
            slots: { default: 'basicUnit' },

            visible: true, width: 100
          },
          {
            title: this.$t('material.purchasingUnit'), field: 'orderUnit',
            slots: { default: 'orderUnit' },

            visible: true, width: 100
          },
          { title: this.$t('material.procurementGroup'), field: 'pgId', slots: { default: 'pgId' }, visible: true, width: 100 },
          { title: this.$t('material.materialStatus'), field: 'status', slots: { default: 'status' }, visible: true, width: 100 }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },

      queryParams: {
        search: '',
        materialCode: this.$route.params.materialCode,
        supplierName: '',
        purchaseType: '',
        materialDescription: '',
        categories: [],
        categoryId: '',
        sourcings: [],
        purchaseOrgs: [],
        mfg: '',
        mpn: '',
        pgId: '',
        status: '',
        dateType: '',
        beginDate: '',
        endDate: '',
        sortBy: '',
        sortField: '',
        pageNo: 1,
        pageSize: 10
      },
      chartData: {
        priceSeriesData: [],
        xaxisData: []
      }
    }
  },
  mounted() {
    this.init()
    this.getList()
  },
  methods: {
    init() {
      this.queryParams.materialCode = this.$route.params.materialCode
      if (this.queryParams.time) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + ' 00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + ' 23:59:59' : undefined
      }
      getAVPLLineGraph(this.queryParams).then(res => {
        this.chartData = res.data
      })
    },
    getList() {
      this.queryParams.materialCode = this.$route.params.materialCode
      if (this.queryParams.time) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + ' 00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + ' 23:59:59' : undefined
      }
      getAVPLMaterialPage(this.queryParams).then(res => {
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    handleClick() {
      this.queryParams = {
        search: '',
        materialCode: this.$route.params.materialCode,
        supplierName: '',
        purchaseType: '',
        materialDescription: '',
        categories: [],
        categoryId: '',
        sourcings: [],
        purchaseOrgs: [],
        mfg: '',
        mpn: '',
        pgId: '',
        status: '',
        dateType: '',
        beginDate: '',
        endDate: '',
        sortBy: '',
        sortField: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getList()
    },

    handleQuery() {
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>

.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.searchTimeItem {
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(60% - 82px);
  }
}

.searchValue {
  width: 95%;
}
</style>
