
<template>
  <div class="home">
    <Superset
      :dashboard-key="dashboardKey"
      dashboard-type="dashboard"
      :dashboard-style="{width:'100%',height:'800px',border:'0'}"
    />
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import Superset from '@/components/superset'

export default defineComponent({
  name: 'Index',
  components: { Superset },
  computed: {
    dashboardKey() {
      return this.$i18n.locale === 'zh'
        ? process.env.VUE_APP_SOME_DASHBOARD
        : process.env.VUE_APP_SOME_DASHBOARD_EN
    }
  }
})
</script>

<style scoped lang="scss">
</style>
