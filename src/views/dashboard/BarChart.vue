<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
import resize from './mixins/resize'

const animationDuration = 6000

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption({

        tooltip: {},
        xAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'category',
          data: ['IFE', 'BBL Plus', 'BBL Auun', 'HFA GEN3', 'DCU', 'POT GEN2', 'DTA', 'HFA GEN2', 'Sensoris', 'Cable Module 2.5', 'Others'],
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [{
          type: 'bar',
          data: [30, 25, 17, 17, 15, 9, 6, 5, 3, 2, 10],
          itemStyle: {
            color: '#9DB1CF'
          },
          label: {
            show: true,
            position: 'right'
          }
        }]
      })
    }
  }
}
</script>
