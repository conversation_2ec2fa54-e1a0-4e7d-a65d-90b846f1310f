<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
import resize from './mixins/resize'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Object
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.initChart(val)
      }
    }
  },
  // computed: {
  //   supplierTotal() {
  //     return this.chartData.reduce((acc, cur) => acc + cur.result, 0)
  //   }
  // },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption({
        color: ['#5C7BD9', '#9FE080', '#ffff00', '#FFDC60'],
        title: {
          text: `{a|${this.$t('supplier.totalNumberOfSuppliers')}} {b|${this.chartData?.supplierCount}}`,
          left: '38%',
          textStyle: {
            rich: {
              a: {
                fontWeight: 400,
                fontSize: 16
              },
              b: {
                fontWeight: 600,
                fontSize: 16
              }
            }
          }

          // subtext: 'Funnel',
          // textAlign: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: `{b} : {c}`
        },
        // legend: {
        //   data: ['Show', 'Click', 'Visit', 'Inquiry', 'Order']
        // },
        series: [
          {
            type: 'funnel',
            left: '8%',
            top: 40,
            bottom: 60,
            width: '80%',
            height: '80%',
            min: 0,
            max: 100,
            minSize: '0%',
            maxSize: '100%',
            sort: 'none',
            gap: 2,
            label: {
              show: true,
              color: '#000000',
              textBorderColor: 'transparent',
              fontSize: 15,
              position: 'inside',
              formatter: function(param) {
                return param.data.name + '  ' + param.data.value
              }
            },
            labelLine: {
              length: 10,
              lineStyle: {
                width: 1,
                type: 'solid'
              }
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            },
            emphasis: {
              label: {
                fontSize: 20
              }
            },
            data: this.chartData?.supplierAndRfqVOList?.map(a => {
              return {
                value: a.result,
                name: getDictDataLabel(DICT_TYPE.OV_MODULE_SUPPLIER_TYPE, a.name)
              }
            })
          }
        ]
      })
    }
  }
}
</script>
