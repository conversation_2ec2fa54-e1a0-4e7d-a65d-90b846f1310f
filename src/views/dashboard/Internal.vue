<template>
  <div class="home">
    <div style="font-size: 16px">
      {{ $t('common.welcome') }}
      <span style="font-size: 22px"> {{ $store.getters.nickname }}</span>
      ， {{ $t('common.todayIs') }} {{ dayjs().format('YYYY年MM月DD日') }}
    </div>
    <div style="margin-top:18px;font-size: 27px;text-align: center;font-family: 'SimHei','STHeiti'">
      {{ $t(welcome) }}

    </div>
    <div>
      <div style="color: #4996b8;margin: 10px 0;font-weight: bold ">
        {{ $t('common.accumulatedSystemLaunch') }}
      </div>
      <div style="display: flex">
        <div
          v-for="item in moduleData"
          :style="{
            'flex': `0 0 12.5%`,
            'background': item.color
          }"
          class="statisticsNum"
        >
          <div style="margin: 0 10px">
            <svg-icon :icon-class="item.icon" class-name="statisticsItem" />
          </div>
          <div
            :style="{
              'color': compareColor(item.color)
            }"
          >
            <div style="font-size: 15px">{{ item.name }}</div>
            <div style="font-size: 24px">{{ item.result }}</div>
          </div>

        </div>
      </div>

      <div style="display: flex;margin-top: 18px;min-width: 1269px">
        <div style="flex: 0 1  60%;padding-right:8px">
          <el-card>
            <div slot="header" style="display: flex;justify-content: space-between">
              <span>{{ $t('common.toDoItems') }}</span>
              <user-config
                :config-list="userTodoData"
                :config-type="'todo_list'"
                @freshList="getUserTodo"
              />
            </div>
            <div style="display: flex;padding:5px">
              <div
                v-for="item in userTodoDataEnable"
                :style="{ 'border-left':`3px solid ${item.color}`}"
                class="todoItem"
                @click="pushRoute(item)"
              >
                <span style="font-size: 13px;">
                  {{ item.name }}
                </span>
                <div style="font-size: 25px;font-weight: bold">
                  {{ item.pendingItems == null ? 0 : item.pendingItems }}
                </div>
              </div>
            </div>

          </el-card>
        </div>
        <div style="flex: 0 1  40%;padding-left: 8px">
          <el-card>
            <div slot="header" style="display: flex;justify-content: space-between">
              <span>{{ $t('common.myQuickAccess') }}</span>
              <user-config
                :config-list="userShortcut"
                :config-type="'shortcut'"
                @freshList="getUserShortcut"
              />
            </div>
            <div style="height: 98px;display: flex">
              <div
                v-for="a in userShortcutEnable"
                style="flex: 0 1 25%;
                display: flex;flex-direction:column;justify-content: center;align-items: center;cursor:pointer;"
                @click="pushRoute(a)"
              >
                <div class="fastLogin">
                  <svg-icon :icon-class="a.icon" class-name="fastLoginItem" />

                </div>
                <div style="margin-top: 12px;font-size: 13px">{{ a.name }}</div>
              </div>
            </div>
          </el-card>
        </div>

      </div>

      <div style="display: flex;margin: 18px 0">
        <div style="flex: 0 1 70%;padding-right:8px;min-width: 880px">
          <el-card>
            <span slot="header">
              <div style="justify-content: space-between;display: flex">
                <span>
                  {{ $t('common.systemDataaccumulatedForTheCurrentYear') }}
                </span>

              </div>
            </span>
            <div style="color: #000000;text-align: right;margin-bottom: 18px;">
              {{ $t('common.dataUpdateTime') }}：{{ dayjs(systemData.lastRunTime).format('YYYY-MM-DD') }}
            </div>
            <div style="display: flex">

              <div style="flex: 0 1 55%;">
                <span class="systemTitle">{{ $t('supplier.supplier') }}</span>
                <funnel-chart :chart-data="systemData.supplier" style="margin-top: 18px;" />
              </div>
              <div style="flex: 0 1 45%;">
                <span class="systemTitle">{{ $t('rfq.rfqMaterial') }}</span>
                <div
                  style="display: flex;
                align-items: center;
                height: 300px;
                flex-direction: column;justify-content: space-around;padding: 0 15px"
                >
                  <div style="font-size: 16px;">{{ $t('common.accumulatedGrantPrice') }}<span style="font-weight: bold">{{ systemData.rfq?.cumulativeGrantPrice }}</span></div>
                  <div style="width: 90%">
                    <div
                      :style="systemData.rfq?.activelyGrantRequest === 0 && systemData.rfq?.passiveGrantRequest === 0? {
                        'height': '36px',
                        'border-radius': 0,
                        'background-color': 'gray'
                      }:{
                        'height': '36px',
                        'border-radius': 0,
                        'background-color': '#9FE080'
                      }"
                      class="el-progress-bar__outer"
                    >
                      <div
                        :style="{
                          'width': `${systemData.rfq?.passiveGrantRequest/(systemData.rfq?.passiveGrantRequest+systemData.rfq?.activelyGrantRequest)*100}%`
                        }"
                        class="el-progress-bar__inner gray-bar"
                      />
                    </div>
                    <div style="display: flex;justify-content: space-between;font-size: 15px;margin-top: 15px">
                      <span>
                        {{ $t('common.passiveGrant') }}  {{ systemData.rfq?.passiveGrantRequest }}

                      </span>
                      <span>
                        {{ $t('common.activelyGrant') }} {{ systemData.rfq?.activelyGrantRequest }}

                      </span>
                    </div>
                  </div>
                  <div style="width: 90%">
                    <div
                      :style="systemData.rfq?.singleRoundPrice === 0 && systemData.rfq?.multipleRoundsPrice === 0? {
                        'height': '36px',
                        'border-radius': 0,
                        'background-color': 'gray'
                      }:{
                        'height': '36px',
                        'border-radius': 0,
                        'background-color': '#9FE080'
                      }"
                      class="el-progress-bar__outer"
                    >
                      <div
                        :style="{
                          'width': `${systemData.rfq?.singleRoundPrice/(systemData.rfq?.multipleRoundsPrice+systemData.rfq?.singleRoundPrice)*100}%`
                        }"
                        class="el-progress-bar__inner gray-bar"
                      />
                    </div>
                    <div style="display: flex;justify-content: space-between;font-size: 15px;margin-top: 15px">
                      <span>
                        {{ $t('common.singleRoundBargaining') }}  {{ systemData.rfq?.singleRoundPrice }}
                      </span>
                      <span>
                        {{ $t('common.multipleRoundsOfBargaining') }}   {{ systemData.rfq?.multipleRoundsPrice }}

                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <span class="systemTitle">{{ $t('common.procurementAmount') }}</span>

              <purchase-chart :chart-data="systemData.order" style="margin-top: 18px;" />
            </div>
            <div style="margin: 18px 0; ">
              <span class="systemTitle">{{ $t('common.qualityImprovementForm') }}</span>

              <scar-chart :chart-data="systemData.scar" />
            </div>
          </el-card>
        </div>
        <div style="flex: 0 1 30%;padding-left:8px;min-width: 381px">
          <el-card>
            <div slot="header" style="justify-content: space-between;display: flex">
              <span>
                {{ $t('common.notificationAnnouncement') }}
              </span>
              <span>
                <el-button type="text" @click="$router.push('/system/notice')">{{ $t('system.more') }}</el-button>
              </span>
            </div>

            <el-table :data="noticeList" :height="520" class="noticeTable" show-overflow-tooltip>
              <el-table-column label="" width="10">
                <template>
                  <span style="color: #4996b8">
                    ●
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="" min-width="120" prop="title">
                <template #default="scope">
                  <el-button
                    style="text-decoration: underline"
                    type="text"
                    @click="showDetail(scope.row)"
                  >{{ scope.row.title }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column label="" prop="createTime" width="130">
                <template #default="scope">
                  {{ scope.row.createTime ? dayjs(scope.row.createTime).format('YYYY-MM-DD HH:mm') : '' }}

                </template>

              </el-table-column>
            </el-table>
          </el-card>
          <el-card style="margin-top: 18px">
            <div slot="header" style="justify-content: space-between;display: flex">
              <span>
                {{ $t('common.helpCenter') }}
              </span>
              <span>
                <el-button type="text" @click="$router.push('/system/help-center')">{{ $t('system.more') }}</el-button>
              </span>
            </div>
            <el-table :data="helpCenter" :height="450" class="noticeTable" show-overflow-tooltip>
              <el-table-column label="" width="10">
                <template>
                  <span style="color: #4996b8">
                    ●
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="" min-width="120" prop="title">
                <template #default="scope">
                  <el-button
                    style="text-decoration: underline"
                    type="text"
                    @click="showDetail(scope.row)"
                  >{{ scope.row.title }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column label="" prop="createTime" width="130">
                <template #default="scope">
                  {{ scope.row.createTime ? dayjs(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}

                </template>

              </el-table-column>
            </el-table>
          </el-card>

        </div>
      </div>
    </div>
    <el-dialog
      v-if="detailVisible"
      :title="messageForm.title"
      :visible.sync="detailVisible"
      width="900px"
    >
      <div class="ql-editor" v-html="messageForm.content" />
      <el-descriptions
        v-if="messageForm.fileList.length"
        :colon="false"
        :column="1"
        label-class-name="labelTitle"
        style="padding-left: 12px"
      >
        <el-descriptions-item
          :content-style="{'display':'flex','flex-wrap':'wrap','align-items':'flex-start'}"
          :label="$t('rfq.enclosure')"
          :label-style="{'font-weight':'bold'}"
        >
          <div v-for="item in messageForm.fileList" style="width: 100%;align-self: flex-start;">
            <el-button style="text-decoration: underline" type="text" @click="openFile(item.fileUrl)">{{
              item.fileName
            }}
            </el-button>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import FunnelChart from '@/views/dashboard/FunnelChart.vue'
import PurchaseChart from '@/views/dashboard/PurchaseChart.vue'
import ScarChart from '@/views/dashboard/ScarChart.vue'
import dayjs from 'dayjs'
import {
  getCumulativeDataResult,
  getShortcutList,
  getSystemDataResult,
  getSystemHelpCenter,
  getSystemNoticePage,
  getUserShortcutList,
  getUserTodoList,
  getUserTodoShortcut
} from '@/api/system/home'
import UserConfig from '@/views/dashboard/userConfig.vue'
import { purchaseWords } from '@/views/dashboard/welcomeWords'

export default defineComponent({
  name: 'Index',
  components: { UserConfig, ScarChart, PurchaseChart, FunnelChart },
  data() {
    return {
      welcome: purchaseWords[Math.floor(Math.random() * purchaseWords.length)],
      dayjs,
      moduleData: [],
      todoData: [],
      shortcut: [],
      userTodoData: [{
        'action': 'approvedAndReturnedMaterials',
        'code': '',
        'color': 'red',
        'icon': 'order',
        'id': 0,
        'module': '',
        'name': '订单',
        'pendingItems': 321321,
        'sort': 0,
        'type': '',
        'url': '/rfq/rfqindex',
        'enable': false
      }],
      userTodoDataEnable: [],
      userShortcut: [],
      userShortcutEnable: [],
      systemData: {},
      noticeList: [],
      helpCenter: [],
      detailVisible: false,
      messageForm: {
        title: '',
        type: null,
        content: ''
      }
    }
  },
  mounted() {
    this.getSystemData()
    this.getCumulativeData()
    this.getNoticeList()
    this.getHelpCenter()
    this.getUserTodo()
    this.getUserShortcut()
  },
  methods: {
    rfqBarStyle() {
      return
    },
    openFile(url) {
      window.open(url)
    },
    async getSystemData() {
      const data = await getSystemDataResult()
      this.systemData = data.data
    },
    async getCumulativeData() {
      const data = await getCumulativeDataResult()
      this.moduleData = data.data
    },
    async getTodoData() {
      const data = await getUserTodoShortcut()
      this.todoData = data.data
    },
    async getShortcut() {
      const data = await getShortcutList()
      this.shortcut = data.data
    },
    async getUserTodo() {
      const data = await getUserTodoList()
      this.userTodoData = data.data
      this.userTodoDataEnable = [...this.userTodoData.filter(j => j.enable)]
    },
    async getUserShortcut() {
      const data = await getUserShortcutList()
      this.userShortcut = data.data
      this.userShortcutEnable = [...this.userShortcut.filter(j => j.enable)]
    },
    async getNoticeList() {
      const data = await getSystemNoticePage({
        pageNo: 1,
        pageSize: 10,
        status: 0
      })
      this.noticeList = data.data.list
    },
    async getHelpCenter() {
      const data = await getSystemHelpCenter({
        pageNo: 1,
        pageSize: 10,
        status: 0
      })
      this.helpCenter = data.data.list
    },
    pushRoute(item) {
      this.$store.commit('setParameters', item.action)
      this.$router.push(item.url)
    },
    showDetail(row) {
      this.detailVisible = true
      this.messageForm = row
    },
    compareColor(color) {
      const hexToRgb = (hex) => {
        const [r, g, b] = hex.match(/\w\w/g).map((color) => parseInt(color, 16))
        return [r, g, b]
      }
      const getBrightness = ([r, g, b]) => (r * 299 + g * 587 + b * 114) / 1000
      return getBrightness(hexToRgb('#baccdf')) < getBrightness(hexToRgb(color)) ? '#4996b8' : '#ffffff'
    }
  }
})
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";
@import "~@/assets/styles/home.scss";

</style>
