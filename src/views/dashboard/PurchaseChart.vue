<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts';
import resize from './mixins/resize'



const animationDuration = 6000

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Object
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '330px'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.initChart(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      const title = `{a|${this.$t('common.ov_purchasechart_cumulativeamount')}} {b|${this.chartData?.cumulativeAmount?.toFixed(0)} }{a|${this.$t('common.tenThousandYuan')}}`
      const containerWidth=this.$el.clientWidth
      const titleLength=title.length
      const titleWidth=titleLength * 14
      const leftSpace = (containerWidth - titleWidth) / 2;

      this.chart = echarts.init(this.$el, 'macarons')
      let xAxisData = []
      const series = []
      this.chartData?.monthResult.forEach(a => {
        series.push({
          width: '80%',
          height: '80%',
          name: a.name,
          type: 'bar',
          stack: 'one',
          emphasis: emphasisStyle,
          data: a.result
        })
        xAxisData = a.result.map((a, index) => `${index + 1}月`)
      })
      var emphasisStyle = {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0,0,0,0.3)'
        }
      }
      this.chart.setOption({
        color: ['#5C7BD9','#9FE080'],
        aria:{
          enabled:true,
          pattern: 'diagonal',
          decal: {
            show: true
          }
        },
        tooltip: {
          formatter: function (obj) {
            return obj.name +":"+ obj.seriesName+obj.value?.toFixed(2)+"万元"
          }
        },
        title: {
          left: leftSpace+'px',
          textAlign: 'left',
          text: title,

          textStyle: {
            rich: {
              a: {
                fontWeight: 400,
                fontSize: 16
              },
              b: {
                fontWeight: 600,
                fontSize: 16
              }
            }
          }
        },
        xAxis: {
          data: xAxisData,
          axisLine: { onZero: true },
          splitLine: { show: false },
          splitArea: { show: false }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          }
        },
        grid: {
          height: '80%',
          width: '82%',
          top: 42
        },
        series
      })
    }


  }
}
</script>
