<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
import resize from './mixins/resize'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'



export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Object
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.initChart(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      const title = `{a|累计质量改善单} {b|${this.chartData?.cumulativeAmount || 0}}`
      const containerWidth=this.$el.clientWidth
      const titleLength=title.length
      const titleWidth=titleLength * 14
      const leftSpace = (containerWidth - titleWidth) / 2;

      this.chart = echarts.init(this.$el, 'macarons')

      let xAxisData = []
      const series = []
      this.chartData?.monthResult?.forEach(a => {
        series.push({
          name: getDictDataLabel(DICT_TYPE.SCAR_BUSINESS_TYPE, a.name),
          type: 'line',
          stack: 'Total',
          data: a.result
        })
        xAxisData = a.result.map((a, index) => `${index + 1}月`)
      })
      this.chart.setOption({
        aria: {
          enabled: true
        },
        title: {
          left: leftSpace+'px',
          textAlign: 'left',
          text: title,
          textStyle: {
            rich: {
              a: {
                fontWeight: 400,
                fontSize: 16
              },
              b: {
                fontWeight: 600,
                fontSize: 16
              }
            }
          }
        },
        tooltip: {
          trigger: 'axis'
        },

        grid: {
          height: '80%',
          width: '82%',
          top: 42
        },

        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          }
        },
        series
      })
    }
  }
}
</script>
