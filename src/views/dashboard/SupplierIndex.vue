<template>
  <div class="home">
    <div style="font-size: 16px">
      {{ $t('common.welcome') }}
      <span style="font-size: 22px"> {{ $store.getters.nickname }}</span>
      ， {{ $t('common.todayIs') }} {{ dayjs().format('YYYY年MM月DD日') }}
    </div>
    <div style="margin-top:18px;font-size: 27px;text-align: center;font-family: 'SimHei','STHeiti'">
      {{ $t(welcome) }}
    </div>
    <div>
      <div style="color: #4996b8;margin: 10px 0;font-weight: bold ">
        {{ $t('common.accumulatedSystemLaunch') }}
      </div>
      <div style="display: flex">
        <div
          v-for="item in moduleData"
          class="statisticsNum"
          :style="{
            'flex': `0 0 12.5%`,
            'background': item.color
          }"
        >
          <div style="margin: 0 10px">
            <svg-icon class-name="statisticsItem" :icon-class="item.icon" />
          </div>
          <div
            :style="{
              'color': compareColor(item.color)
            }"
          >
            <div style="font-size: 18px">{{ item.name }}</div>
            <div style="font-size: 24px">{{ item.result }}</div>
          </div>

        </div>
      </div>

      <div style="display: flex;margin-top: 18px;min-width: 1269px">
        <div style="flex: 0 1  60%;padding-right:8px">
          <el-card>
            <div slot="header" style="display: flex;justify-content: space-between">
              <span>{{ $t('common.toDoItems') }}</span>
              <user-config
                :config-type="'todo_list'"
                :config-list="userTodoData"
                @freshList="getUserTodo"
              />
            </div>
            <div style="display: flex;padding:5px">
              <div
                v-for="item in userTodoDataEnable"
                :style="{ 'border-left':`3px solid ${item.color}`}"
                class="todoItem"
                @click="pushRoute(item)"
              >
                <span style="font-size: 13px;">
                  {{ item.name }}
                </span>
                <div style="font-size: 24px">
                  {{ item.pendingItems }}
                </div>
              </div>
            </div>

          </el-card>
        </div>
        <div style="flex: 0 1  40%;padding-left: 8px">
          <el-card>
            <div slot="header" style="display: flex;justify-content: space-between">
              <span>{{ $t('common.myQuickAccess') }}</span>
              <user-config
                :config-type="'shortcut'"
                :config-list="userShortcut"
                @freshList="getUserShortcut"
              />
            </div>
            <div style="height: 98px;display: flex">
              <div
                v-for="a in userShortcutEnable"
                style="flex: 0 1 25%;
                display: flex;flex-direction:column;justify-content: center;align-items: center;cursor:pointer;"
                @click="pushRoute(a)"
              >
                <div class="fastLogin">
                  <svg-icon class-name="fastLoginItem" :icon-class="a.icon" />

                </div>
                <div style="margin-top: 12px;font-size: 13px">{{ a.name }}</div>
              </div>
            </div>
          </el-card>
        </div>

      </div>

      <div style="display: flex;margin: 18px 0">
        <div style="flex: 0 1 50%;padding-right:8px;min-width: 630px">
          <el-card>
            <div slot="header" style="justify-content: space-between;display: flex">
              <span>
                {{ $t('common.notificationAnnouncement') }}
              </span>
              <span>
                <el-button type="text" @click="$router.push('/system/notice')">{{ $t('system.more') }}</el-button>
              </span>
            </div>

            <el-table
              :data="noticeList"
              show-overflow-tooltip
              class="noticeTable"
              :height="306"
              @row-click="showNoticeDetail"
            >
              <el-table-column width="10" label="">
                <template>
                  <span style="color: #4996b8">
                    ●
                  </span>
                </template>
              </el-table-column>
              <el-table-column min-width="120" label="" prop="title">
                <template #default="scope">
                  <el-button
                    style="text-decoration: underline"
                    type="text"
                    @click="showDetail(scope.row)"
                  >{{ scope.row.title }}</el-button>
                </template>
              </el-table-column>
              <el-table-column width="130" label="" prop="createTime">
                <template #default="scope">
                  {{ scope.row.createTime?dayjs(scope.row.createTime).format('YYYY-MM-DD HH:mm'):'' }}

                </template>

              </el-table-column>
            </el-table>
          </el-card>
        </div>
        <div style="flex: 0 1 50%;padding-left:8px;min-width: 630px">
          <el-card>
            <div slot="header" style="justify-content: space-between;display: flex">
              <span>
                {{ $t('common.helpCenter') }}
              </span>
              <span>
                <el-button type="text" @click="$router.push('/system/help-center')">{{ $t('system.more') }}</el-button>
              </span>
            </div>
            <el-table show-overflow-tooltip class="noticeTable" :height="306" :data="helpCenter">
              <el-table-column width="10" label="">
                <template>
                  <span style="color: #4996b8">
                    ●
                  </span>
                </template>
              </el-table-column>
              <el-table-column min-width="120" label="" prop="title">
                <template #default="scope">
                  <el-button
                    style="text-decoration: underline"
                    type="text"
                    @click="showDetail(scope.row)"
                  >{{ scope.row.title }}</el-button>
                </template>
              </el-table-column>
              <el-table-column width="130" label="" prop="createTime">
                <template #default="scope">
                  {{ scope.row.createTime?dayjs(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss'):'' }}

                </template>

              </el-table-column>            </el-table>
          </el-card>
        </div>
      </div>
    </div>
    <el-dialog
      v-if="detailVisible"
      :title="messageForm.title"
      width="900px"
      :visible.sync="detailVisible"
    >
      <div v-html="messageForm.content" />
      <el-descriptions v-if="messageForm.fileList.length" style="padding-left: 12px" :column="1" :colon="false" label-class-name="labelTitle">
        <el-descriptions-item
          :label="$t('rfq.enclosure')"
          :label-style="{'font-weight':'bold'}"
          :content-style="{'display':'flex','flex-wrap':'wrap','align-items':'flex-start'}"
        >
          <div v-for="item in messageForm.fileList" style="width: 100%;align-self: flex-start;">
            <el-button style="text-decoration: underline" type="text" @click="openFile(item.fileUrl)">{{
              item.fileName
            }}
            </el-button>
          </div>
        </el-descriptions-item>
      </el-descriptions>

    </el-dialog>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import dayjs from 'dayjs'
import {
  getCumulativeDataResult,
  getSystemDataResult, getSystemHelpCenter, getSystemNoticePage, getUserShortcutList,
  getUserTodoList
} from '@/api/system/home'
import UserConfig from '@/views/dashboard/userConfig.vue'
import { supplierWords } from '@/views/dashboard/welcomeWords'

export default defineComponent({
  name: 'SupplierIndex',
  components: {
    UserConfig
  },
  data() {
    return {
      welcome: supplierWords[Math.floor(Math.random() * supplierWords.length)],

      noticeId: 0,
      dayjs,
      moduleData: [],
      todoData: [],
      shortcut: [],
      userTodoData: [],
      userTodoDataEnable: [],
      userShortcut: [],
      userShortcutEnable: [],
      systemData: {},
      noticeList: [],
      helpCenter: [],
      detailVisible: false,
      messageForm: {
        title: '',
        type: null,
        content: ''
      }
    }
  },
  mounted() {
    this.getSystemData()
    this.getCumulativeData()
    this.getNoticeList()
    this.getHelpCenter()
    this.getUserTodo()
    this.getUserShortcut()
  },
  methods: {
    async getSystemData() {
      const data = await getSystemDataResult()
      this.systemData = data.data
    },
    async getCumulativeData() {
      const data = await getCumulativeDataResult()
      this.moduleData = data.data
    },

    async getUserTodo() {
      const data = await getUserTodoList()
      this.userTodoData = data.data
      this.userTodoDataEnable = [...this.userTodoData.filter(j => j.enable)]
    },
    async getUserShortcut() {
      const data = await getUserShortcutList()
      this.userShortcut = data.data
      this.userShortcutEnable = [...this.userShortcut.filter(j => j.enable)]
    },
    async getNoticeList() {
      const data = await getSystemNoticePage({
        pageNo: 1,
        pageSize: 10,
        status: 0
      })
      this.noticeList = data.data.list
    },
    async getHelpCenter() {
      const data = await getSystemHelpCenter({
        pageNo: 1,
        pageSize: 10,
        status: 0
      })
      this.helpCenter = data.data.list
    },
    openFile(url) {
      window.open(url)
    },
    pushRoute(item) {
      this.$store.commit('setParameters', item.externalAction)
      this.$router.push(item.externalUrl)
    },
    showNoticeDetail(row) {
      this.noticeVisible = true
      this.noticeId = row.id
    },
    showDetail(row) {
      this.detailVisible = true
      this.messageForm = row
    },
    compareColor(color) {
      const hexToRgb = (hex) => {
        const [r, g, b] = hex.match(/\w\w/g).map((color) => parseInt(color, 16))
        return [r, g, b]
      }
      const getBrightness = ([r, g, b]) => (r * 299 + g * 587 + b * 114) / 1000
      return getBrightness(hexToRgb('#baccdf')) < getBrightness(hexToRgb(color)) ? '#4996b8' : '#ffffff'
    }
  }
})
</script>

<style scoped lang="scss">
@import "~@/assets/styles/card.scss";
@import "~@/assets/styles/home.scss";

</style>
