import dayjs from 'dayjs'

export default {
  created() {
    this.assignParams()
  },
  activated() {
    this.assignParams()
  },
  methods: {
    assignParams() {
      const code = this.$store.getters.eventCode
      if (!code) {
        return
      }
      const actionUser = () => {
        if (this.$store.getters.supplierName) {
          this.queryParams.supplier = this.$store.getters.supplierName
        } else {
          this.queryParams.sourcingIds = [this.$store.getters.userId]
        }
      }
      switch (code) {
        case 'todoAuth':
          this.queryParams.processor = this.$store.getters.userId
          this.queryParams.authStatus.length = 0
          this.queryParams.authStatus.push('Information to be verified', 'set up standard', 'written review check', 'on-the-spot check', 'allow apply', 'Level 1 audit', 'Level 1 approve', 'Level 2 approve', 'Level 3 approve', 'archive check', 'information change')
          break
        case 'pendingApproval':
          if (this.$options.name === 'Rfqindex') {
            this.activeName = 'fourth'
          } else {
            this.queryParams.approvers = [this.$store.getters.userId]
            this.queryParams.status = 'pending_approval'
          }
          break
        case 'approvedAndReturnedMaterials':
          if (this.$options.name === 'Rfqindex') {
            this.activeName = 'third'
          } else {
            this.queryParams.status = 'approve_return'
            this.queryParams.sourcing = [this.$store.getters.userId]
          }
          break
        case 'recommendedMaterial':
          if (this.$options.name === 'Rfqindex') {
            this.activeName = 'third'
          } else {
            this.queryParams.status = 'to_be_recommended'
            this.queryParams.sourcing = [this.$store.getters.userId]
          }
          break
        case 'inquirySheetToBeQuoted':
          if (this.$options.name === 'Rfqindex') {
            this.activeName = 'second'
          } else {
            this.queryParams.quotationsStatus = 'to_quote'
            this.queryParams.sourcing = [this.$store.getters.userId]
          }
          break
        case 'bankOfCommunications':
          this.queryParams.answerStatus = ['waiting_answer', 'reject_answer']
          actionUser()
          break
        case 'bankOfCommunicationsToBeAccepted':
          this.queryParams.answerStatus = ['waiting_accept']
          actionUser()

          break
        case 'overdueDeliver':
          this.activeName = 'second'
          // 订单管理（全部订单行）+条件：是否逾期=逾期
          this.queryParams.overdue = ['already_overdue']
          actionUser()

          break
        case 'overdue':
          this.queryParams.required = ['already_overdue']
          actionUser()

          break
        case 'overduePayment':
          this.queryParams.promised = ['already_overdue']
          actionUser()
          break
        case 'documentsToBeIssued':
          this.queryParams.scarStatus = ['to_be_released']
          this.queryParams.processorIds = [this.$store.getters.userId]
          break
        case 'documentsToBeReviewed':
          this.queryParams.scarStatus = ['sqe_review']
          this.queryParams.processorIds = [this.$store.getters.userId]

          break
        case 'documentToBeApproved':
          this.queryParams.scarStatus = ['countersign']
          this.queryParams.processorIds = [this.$store.getters.userId]

          break
        case 'documentsToBeReplied':
          this.queryParams.scarStatus = ['supplier_reply']
          if (this.$store.getters.supplierName) {
            this.queryParams.supplierText = this.$store.getters.supplierName
          } else {
            this.queryParams.processorIds = [this.$store.getters.userId]
          }
          break
        case 'projectToBeOpened':
          this.queryParams.projectStatus = 'openingEvaluation'
          this.queryParams.nodeStatus = 'BidOpening'
          break
        case 'projectToBeEvaluated':
          this.queryParams.projectStatus = 'openingEvaluation'
          this.queryParams.nodeStatus = 'bidEvaluation'
          break
        case 'pendingBidItems':
          this.queryParams.projectStatus = 'toDetermined'
          this.queryParams.nodeStatus = 'bidDetermined'
          break
        case 'newSupplier':
          this.visible = true
          break
        case 'replyToImprovementOrder':
          this.queryParams.scarStatus = ['supplier_reply']
          if (this.$store.getters.supplierName) {
            // 供应商账户进入则默认填充【供应商】的搜索条件 & SCAR状态默认为 供应商回复
            this.queryParams.supplierText = this.$store.getters.supplierName
          } else {
            this.queryParams.processorIds = [this.$store.getters.userId]
          }

          break
        case 'inquirySheetToBeQuotedForSupplier':
          this.queryParams.quotationStatus = 'to_quote'
          break
        case 'ytdOrder':
          // 订单交付 ytd 完成订单跳转
          this.queryParams.shipStatus = ['Delivered']
          this.queryParams.supplier = this.$route.query.supplier
          break
        case 'ytdOrderTodo':
          // 订单交付 待完成
          this.queryParams.shipStatus = ['To be Confirmed','To be Delivered','Pending']
          this.queryParams.supplier = this.$route.query.supplier
          break
        case 'ytdScar':
          // 订单交付 待完成
          this.queryParams.scarStatus = ['completed']
          break
        case 'ytdScarTodo':
          // 订单交付 待完成
          this.queryParams.scarStatus = ['new', 'to_be_released', 'to_be_released', 'to_be_released', 'to_be_released']
          break
        case 'ytdSignificantQualityEvent':
          // 订单交付 重大质量事件
          this.queryParams.severityLevelList = ['1']
          break
        case 'avplSupplierCount':
          // 物料价格库
          // 创建时间 当前年1.1号-当前事件
          // 物料编码筛选
          this.queryParams.dateType = 'createTime'
          this.queryParams.time = [dayjs().year() + '-01-01', dayjs().format(`YYYY-MM-DD`)]
          break

        case 'competencyAssessed':
          // 能力矩阵待评估
          this.queryParams.currentProcessor = [this.$store.getters.userId]
          this.queryParams.status = ['waiting_evaluated']
          break
        case 'competencyConfirm':
          // 能力矩阵待确认
          this.queryParams.currentProcessor = [this.$store.getters.userId]
          this.queryParams.status = ['waiting_evaluated']
          break
      }
      this.$nextTick(() => {
        this.$store.commit('clearParameters')
      })
    }
  }
}
