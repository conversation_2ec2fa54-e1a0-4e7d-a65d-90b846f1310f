
<template>
  <div>
    <el-button
      size="mini"
      circle
      icon="el-icon-menu"
      @click="showConfig"
    />
    <el-dialog
      v-if="settingVisible"
      title="配置"
      :visible.sync="settingVisible"
      width="300px"
    >
      <draggable
        :scroll-sensitivity="100"
        :force-fallback="true"
        :fallback-tolerance="1"
        :set-data="setData"
        :list="customColumns"
        group="article"
        class="setting"
      >
        <div
          v-for="element in customColumns"
          :key="element.id"
          class="setting-item"
        >
          <div class="list-complete-item-handle">
            <el-checkbox
              v-model="element.enable"
              :disabled="!element.enable &&checkLimit"
            >
              {{ element.name }}
            </el-checkbox>
          </div>
        </div>
      </draggable>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import draggable from 'vuedraggable'
import { getShortcutList, getUserTodoShortcut, updateUserTodoShortcutConfig } from '@/api/system/home'

export default defineComponent({
  name: 'UserConfig',
  components: { draggable },
  props: ['configType', 'configList'],
  data() {
    return {
      customColumns: [],
      settingVisible: false,
      shortcut: [],
      todoData: []
    }
  },
  computed: {
    checkLimit() {
      if (this.configType === 'todo_list') {
        return this.customColumns.filter(a => a.enable).length > 4
      } else {
        return this.customColumns.filter(a => a.enable).length > 3
      }
    }
  },
  mounted() {
    if (this.configType === 'todo_list') {
      this.getTodoData()
    } else {
      this.getShortcut()
    }
  },
  methods: {
    async getTodoData() {
      const data = await getUserTodoShortcut()
      this.customColumns = data.data
    },
    async getShortcut() {
      const data = await getShortcutList()
      this.customColumns = data.data
    },
    setData(dataTransfer) {
      dataTransfer.setData('Text', '')
    },
    confirm() {
      updateUserTodoShortcutConfig(
        this.customColumns.map((b, index) => {
          return {
            sort: index,
            type: this.configType,
            userId: this.$store.getters.userId,
            userTodoShortcutId: b.id,
            enable: b.enable
          }
        })).then(res => {
        this.$message.success('保存成功')
        this.settingVisible = false
        this.$emit('freshList')
      })
    },
    showConfig() {
      this.settingVisible = true
      if (this.configList.length > 0) {
        this.customColumns = [...this.configList]
      }
    }
  }
})
</script>
<style scoped lang="scss">
.setting {
  margin-left: 30px;

  &-item {
    margin: 10px 0;
  }
}
</style>
