
<template>
  <div>
    <VueOfficePdf
      v-if="fileType.includes('pdf')"
      :src="fileUrl"
      style="height: 100vh;"
      @rendered="rendered"
      @error="errorHandler"
    />
    <viewer
      v-else-if="fileType.includes('img')"
      :images="images"
    >
      <img v-for="src in images" :key="src" :src="src">
    </viewer>
    <el-result v-else icon="error" title="不支持的文件格式" sub-title="点击返回首页">
      <template slot="extra">
        <el-button
          type="primary"
          size="medium"
          @click="$router.replace('/')"
        >返回</el-button>
      </template>
    </el-result>
  </div>
</template>
<script>
import VueOfficePdf from '@vue-office/pdf'

export default {

  name: 'FilePreview',
  components: {
    VueOfficePdf
  },
  data() {
    return {
      fileType: '',
      options: { 'inline': true, 'button': true, 'navbar': false, 'title': false, 'toolbar': false, 'tooltip': true, 'movable': true, 'zoomable': true, 'rotatable': true, 'scalable': true, 'transition': true, 'fullscreen': true, 'keyboard': false, 'url': 'data-source' },
      fileUrl: '',
      images: [

      ]
    }
  },
  mounted() {
    this.fileUrl = this.$route.query.fileUrl
    this.fileType = this.checkFileType(this.fileUrl)
  },
  methods: {
    inited(viewer) {

    },
    rendered() {

    },
    errorHandler() {
      this.fileType = ''
    },
    checkFileType(url) {
      const imagePattern = /\.(png|jpg|jpeg)$/i
      const pdfPattern = /\.pdf$/i

      if (imagePattern.test(url)) {
        this.images.push(url)
        return 'img'
      } else if (pdfPattern.test(url)) {
        return 'pdf'
      } else {
        return ''
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
