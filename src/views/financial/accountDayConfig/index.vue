<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item :label="$t('supplier.accountingPeriod')" prop="accountDayLabel">
        <el-input v-model="queryParams.accountDayLabel" :placeholder="$t('financial.pleaseEnterThePaymentPeriod')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['financial:account-day-config:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
        >{{ $t('common.export') }}</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('supplier.dictionaryKeyValue')" align="center" prop="accountDayValue" />
      <el-table-column :label="$t('supplier.accountingPeriod')" align="center" prop="accountDayLabel" />
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('financial.advance')" align="center" prop="prepayPercent" />
      <el-table-column :label="$t('financial.baseDateType')" align="center" prop="datumDateType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.FINANCIAL_DATUM_DATE_TYPE" :value="scope.row.datumDateType" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('financial.days')" align="center" prop="days" />
      <el-table-column :label="$t('financial.paymentDateType')" align="center" prop="paymentDayType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.FINANCIAL_PAYMENT_DATE_TYPE" :value="scope.row.paymentDayType" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('financial.paymentDate')" align="center" prop="prepayDate">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_WEEK" :value="scope.row.prepayDate" />
          <dict-tag :type="DICT_TYPE.SYSTEM_DATE" :value="scope.row.prepayDate" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('rfq.createdBy')" align="center" prop="prepayDate">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="scope.row.creator" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['financial:account-day-config:save']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.edit') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item :label="$t('supplier.accountingPeriod')" prop="accountDayLabel">
          {{ form.accountDayLabel }}
        </el-form-item>
        <el-form-item :label="$t('financial.baseDateType')" prop="datumDateType">
          <el-select v-model="form.datumDateType" :placeholder="$t('financial.pleaseSelectTheBaseDateType')">
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.FINANCIAL_DATUM_DATE_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('financial.days')" prop="days">
          <vxe-input v-model="form.days" type="integer" :placeholder="$t('financial.pleaseEnterTheNumberOfDays')" min="0" style="width: 54%" />
        </el-form-item>
        <el-form-item :label="$t('financial.paymentDateType')" prop="paymentDayType">
          <el-select v-model="form.paymentDayType" :placeholder="$t('financial.pleaseSelectPaymentDateType')" @change="(val)=>{changePaymentDayType(val,'')}">
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.FINANCIAL_PAYMENT_DATE_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('financial.paymentDate')" prop="prepayDate">
          <el-select v-model="form.prepayDate" :placeholder="$t('financial.pleaseSelectPaymentDateType')">
            <el-option
              v-for="dict in prepayDateData"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('financial.advance')" prop="prepayPercent">
          <vxe-input v-model="form.prepayPercent" type="integer" :placeholder="$t('financial.pleaseEnterPrepayment')" min="0" max="100" style="width: 54%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { saveAccountDayConfig, getAccountDayConfigPage, exportAccountDayConfigExcel } from '@/api/financial/accountDayConfig'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'

export default {
  name: 'Accountdayconfig',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 财务协同-账期信息配置列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        accountDayLabel: null,
        accountDayValue: null
      },
      // 付款日期下拉框数据源
      prepayDateData: null,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        datumDateType: [{ required: true, message: this.$t('financial.baseDateTypeCannotBeEmpty'), trigger: 'blur' }],
        days: [{ required: true, message: this.$t('financial.theNumberOfDaysCannotBeEmpty'), trigger: 'blur' }],
        paymentDayType: [{ required: true, message: this.$t('financial.paymentDateTypeCannotBeEmpty'), trigger: 'blur' }],
        prepayDate: [{ required: true, message: this.$t('financial.paymentDateCannotBeEmpty'), trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    changePaymentDayType(val, prepayDate) {
      if (val === 'monthly') {
        this.prepayDateData = getDictDatas(DICT_TYPE.SYSTEM_DATE)
      } else {
        this.prepayDateData = getDictDatas(DICT_TYPE.SYSTEM_WEEK)
      }
      this.form.prepayDate = prepayDate
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      // 执行查询
      getAccountDayConfigPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        prepayPercent: undefined,
        datumDateType: undefined,
        days: undefined,
        paymentDayType: undefined,
        prepayDate: undefined,
        accountDayLabel: undefined,
        accountDayValue: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.form = row
      this.open = true
      this.changePaymentDayType(row.paymentDayType, row.prepayDate)
      this.title = this.$t('financial.modifyAccountingPeriodInformation')
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        saveAccountDayConfig(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      // 执行导出
      this.$modal.confirm(this.$t('financial.areYouSureToExportAllAccountingPeriodInformationConfigurationDataItems')).then(() => {
        this.exportLoading = true
        return exportAccountDayConfigExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('financial.accountPeriodInformationConfigurationXlsx'))
        this.exportLoading = false
      }).catch(() => {})
    }
  }
}
</script>
