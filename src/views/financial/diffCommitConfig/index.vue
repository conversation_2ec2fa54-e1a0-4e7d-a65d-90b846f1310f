<template>
  <div class="app-container">

    <el-form ref="form" :model="form" :rules="rules" label-width="180px">
      <el-form-item :label="$t('financial.differenceAmountrmb')" prop="diffMoney">
        <vxe-input v-model="form.diffMoney" min="0" type="number" :placeholder="$t('financial.pleaseEnterTheDifferenceAmount')" />
        <span class="additional-text">{{ $t('financial.whatIsTheRangeWithinTitle') }}</span>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button v-has-permi="['financial:diff-commit-config:save']" type="primary" @click="submitForm">{{ $t('common.save') }}</el-button>
    </div>
  </div>
</template>

<script>
import { saveDiffCommitConfig, getDiffCommitConfig } from '@/api/financial/diffCommitConfig'

export default {
  name: 'Diffcommitconfig',
  components: {
  },
  data() {
    return {
      dateRangeCreateTime: [],
      // 表单参数
      form: {
        diffMoney: null
      },
      // 表单校验
      rules: {
      }
    }
  },
  created() {
    this.getData()
  },
  methods: {
    /** 查询列表 */
    getData() {
      getDiffCommitConfig().then(response => {
        if (response.data) {
          this.form = response.data
        }
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        console.log(this.form)
        saveDiffCommitConfig(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.savedSuccessfully'))
        })
      })
    }
  }
}
</script>
<style>
.additional-text {
  font-size: 12px;
  color: #999;
  margin-left: 5px;
}
</style>
