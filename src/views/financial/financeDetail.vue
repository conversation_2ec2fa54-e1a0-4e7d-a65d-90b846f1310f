<template>
  <div class="finance">
    <common-card
      :title="$t('financial.accountStatementHeader')"
    >
      <el-form
        ref="stateHead"
        :rules="stateHeadRule"
        :model="stateHead"
        size="small"
        :inline="true"
        label-width="120px"
      >
        <el-form-item class="searchItem" :label="$t('financial.reconciliationNumber')">
          {{ stateHead.accountNo }}
        </el-form-item>
        <div>
          <el-form-item class="searchItem" :label="$t('supplier.supplier')" prop="supplierId">
            <span v-if="supplierId || stateHead.supplierName">
              {{ displaySupplierName }}
            </span>
            <show-or-edit
              v-else
              :value="stateHead.supplierId"
              :custom-list="supplierList"
              :disabled="disabled || !application.workflowPermissions.includes('headEdit')"
            >
              <el-select
                v-model="stateHead.supplierId"
                class="content"
                :placeholder="$t('common.pleaseSelect')"
                clearable
                filterable
                style="width: 80%;"
                remote
                :remote-method="getSupplierDetail"
              >
                <el-option
                  v-for="dict in supplierList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>
            </show-or-edit>

          </el-form-item>
          <el-form-item :label="$t('order.company')" class="searchItem" prop="companyId">
            <show-or-edit
              :value="stateHead.companyId"
              :dict="DICT_TYPE.COMMON_COMPANY"
              :disabled="disabled || !application.workflowPermissions.includes('headEdit')"
            >
              <el-select v-model="stateHead.companyId" class="searchValue" clearable>
                <el-option
                  v-for="item in getDictDatas(DICT_TYPE.COMMON_COMPANY,0)"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
        </div>
        <el-form-item class="searchItem" :label="$t('system.currency')" prop="currency">
          <show-or-edit
            :value="stateHead.currency"
            :dict="DICT_TYPE.COMMON_CURRENCY"
            :disabled="disabled || !application.workflowPermissions.includes('headEdit')"
          >
            <el-select v-model="stateHead.currency" class="searchValue" clearable filterable>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item :label="$t('supplier.taxRate')" class="searchItem" prop="taxRate">
          <show-or-edit
            :value="stateHead.taxRate"
            :dict="DICT_TYPE.SUPPLIER_RATE"
            :disabled="disabled || !application.workflowPermissions.includes('headEdit')"
          >
            <el-select v-model="stateHead.taxRate" class="searchValue" clearable>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_RATE,0)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item label=" " class="searchItem">
          <el-button
            v-if="stateHead.id&&!disabled && application.workflowPermissions.includes('headEdit')"
            type="primary"
            plain
            size="mini"
            icon="el-icon-edit"
            @click="saveHead"
          >{{ $t('common.modify') }}</el-button>
          <el-button
            v-if="!disabled && application.workflowPermissions.includes('headEdit') && !stateHead.id"
            type="primary"
            plain
            size="mini"
            @click="saveHead"
          >{{ $t('order.determine') }}</el-button>
        </el-form-item>
      </el-form>
    </common-card>
    <common-card v-if="stateHead.id">
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input v-model="queryParams.searchText	" style="flex: 0 1 40%" :placeholder="$t('financial.orderNumberReceiptVoucherNumberMaterialCode')" clearable @keyup.enter.native="queryParams.pageNo = 1;getFinancialDetailPage();" />
        <el-button type="primary" plain @click="queryParams.pageNo = 1;getFinancialDetailPage();">{{ $t('common.search') }}</el-button>
      </div>
      <vxe-grid
        ref="financeDetail"
        :data="list"
        :loading="loading"
        v-bind="girdOption"
      >
        <template #toolbar_buttons>
          <el-row :gutter="10" style="width: 100%" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-download"
                size="mini"
                @click="downloadExcel"
              >  {{ $t('order.download') }}</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                v-if="application.workflowPermissions.includes('headEdit') && !disabled"
                type="primary"
                size="mini"
                icon="el-icon-plus"
                @click="showAdd"
              >  {{ $t('rfq.addTo') }}</el-button>
            </el-col>

            <el-col :span="1.5">
              <el-button
                v-if="application.workflowPermissions.includes('headEdit') && !disabled"
                type="danger"
                plain
                size="mini"
                icon="el-icon-delete"
                @click="delState"
              >  {{ $t('common.del') }}</el-button>
            </el-col>

            <right-toolbar
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              :custom-columns.sync="girdOption.columns"
              @queryTable="getAddInfo"
            />
          </el-row>
        </template>
        <template #inventoryQuantity="{row}">
          <span
            :class="row.inventoryQuantity<0?'negative':''"
          >
            <number-format :value="row.inventoryQuantity" />
          </span>
        </template>
        <template #unitPriceBeforeTax="{row}">

          <span
            :class="row.unitPriceBeforeTax<0?'negative':''"
          >
            <number-format :value="row.unitPriceBeforeTax" />

          </span>
        </template>
        <template #unitPriceAfterTax="{row}">
          <span
            :class="row.unitPriceAfterTax<0?'negative':''"
          >
            <number-format :value="row.unitPriceAfterTax" />

          </span>
        </template>
        <template #beforeTax="{row}">
          <span
            :class="row.beforeTax<0?'negative':''"
          >
            <number-format :decimal-place="2" :value="row.beforeTax" />
          </span>
        </template>
        <template #afterTax="{row}">
          <span
            :class="row.afterTax<0?'negative':''"
          >
            <number-format :decimal-place="2" :value="row.afterTax" />

          </span>
        </template>
        <template #taxAmount="{row}">
          <span
            :class="row.taxAmount<0?'negative':''"
          >
            <number-format :decimal-place="2" :value="row.taxAmount" />
          </span>
        </template>
      </vxe-grid>
      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="getFinancialDetailPage"
      />
    </common-card>
    <common-card
      v-if="stateHead.id"
      :title="$t('financial.otherAdditionAndSubtractionItems')"
    >
      <el-button
        v-if="application.workflowPermissions.includes('headEdit') && !disabled"
        type="primary"
        size="mini"
        icon="el-icon-plus"
        @click="addOthers"
      >{{ $t('rfq.addTo') }}</el-button>
      <el-table
        show-summary
        :data="otherTable"
        :summary-method="getOtherItemSummaries"
      >
        <el-table-column :label="$t('rfq.serialNo')" type="index" />
        <el-table-column :label="$t('financial.additionAndSubtractionTerms')">
          <template #header>
            <span class="required">{{ $t('financial.additionAndSubtractionTerms') }}</span>
          </template>
          <template #default="scope">
            <show-or-edit
              :value="scope.row.otherItem"
              :disabled="disabled || !application.workflowPermissions.includes('headEdit')"
              :dict="DICT_TYPE.ADDITION_AND_SUBTRACTION_ITEM"
            >
              <el-select v-model="scope.row.otherItem" class="searchValue" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.ADDITION_AND_SUBTRACTION_ITEM,0)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>

          </template>
        </el-table-column>
        <el-table-column :label="$t('order.amountNotTaxed')" prop="priceBeforeTax">
          <template #header>
            <span class="required">{{ $t('order.amountNotTaxed') }}</span>
          </template>
          <template #default="scope">
            <show-or-edit
              type="Number"
              :class="scope.row.priceBeforeTax<0?'negative':''"
              :value="scope.row.priceBeforeTax"
              :disabled="disabled || !application.workflowPermissions.includes('headEdit')"
            >
              <vxe-input v-model.number="scope.row.priceBeforeTax" type="float" @blur="validatePrice(scope.$index)" />

            </show-or-edit>
          </template>
        </el-table-column>
        <el-table-column :label="$t('order.amountIncludingTax')" prop="priceAfterTax">
          <template #header>
            <span class="required">{{ $t('order.amountIncludingTax') }}</span>
          </template>
          <template #default="scope">
            <show-or-edit
              type="Number"
              :class="scope.row.priceBeforeTax<0?'negative':''"
              :value="scope.row.priceAfterTax"
              :disabled="disabled || !application.workflowPermissions.includes('headEdit')"
            >
              <vxe-input v-model.number="scope.row.priceAfterTax" type="float" @blur="validatePrice(scope.$index)" />

            </show-or-edit>
          </template>
        </el-table-column>
        <el-table-column :label="$t('financial.relatedDocuments')">
          <template #default="scope">
            <show-or-edit
              :value="scope.row.relRecord"
              :disabled="disabled || !application.workflowPermissions.includes('headEdit')"
            >
              <vxe-input v-model="scope.row.relRecord" />

            </show-or-edit>
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.remarks')">
          <template #default="scope">
            <show-or-edit
              :value="scope.row.remark"
              :disabled="disabled || !application.workflowPermissions.includes('headEdit')"
            >
              <vxe-input v-model="scope.row.remark" />
            </show-or-edit>
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.enclosure')">
          <template #default="scope">
            <el-button v-if="!disabled && application.workflowPermissions.includes('headEdit')" type="text" @click="showUpload(scope.row)">{{ $t('common.uploadFile') }}
              <span v-show="scope.row.fileRelVOS.length">({{ scope.row.fileRelVOS.length }})</span>
            </el-button>
            <el-button v-else type="text" @click="showUpload(scope.row)">{{ $t('financial.scarviewattachments') }}
              <span>({{ scope.row.fileRelVOS.length }})</span>
            </el-button>
            <el-dialog
              v-if="scope.row.visible"
              width="500px"
              :title="disabled? $t('financial.scarSeefile'): $t('common.uploadFile')"
              :visible.sync="scope.row.visible"
            >
              <el-upload
                ref="upload"
                :action="url"
                :disabled="disabled || !application.workflowPermissions.includes('headEdit')"
                :drag="!disabled && application.workflowPermissions.includes('headEdit')"
                :file-list="scope.row.fileRelVOS"
                :headers="headers"
                :on-preview="onPreview"
                :on-remove="( file, fileList)=>handleRemove( file, fileList,scope.row,scope.$index)"
                :on-success="(response, file, fileList)=>handleFileSuccess(response, file, fileList,scope.row)"
                class="upload-container"
                multiple
              >
                <i v-if="!disabled && application.workflowPermissions.includes('headEdit')" class="el-icon-upload" />
                <div v-if="!disabled && application.workflowPermissions.includes('headEdit')" class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
              </el-upload>
              <div slot="footer" class="dialog-footer">
                <el-button
                  v-if="!disabled && application.workflowPermissions.includes('headEdit')"
                  type="primary"
                  @click="scope.row.visible = false"
                > {{ $t('common.confirm') }}
                </el-button>
              </div>
            </el-dialog>
          </template>
        </el-table-column>
        <el-table-column
          v-if="!disabled && application.workflowPermissions.includes('headEdit')"
          :label="$t('common.operate')"
        >
          <template #default="scope">
            <el-button
              type="text"
              style="text-decoration: underline"
              @click="delOther(scope.$index)"
            >{{ $t('common.del') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

    </common-card>
    <common-card
      v-if="stateHead.id"
      title=""
    >
      <el-form label-width="180px" style="margin-top: 15px;">
        <el-form-item :label="$t('financial.totalAmountOfOriginalStatement')">
          <number-format :decimal-place="2" :value="stateHead.amountPrevAccountMoney" />
        </el-form-item>
        <el-form-item :label="$t('financial.totalAmountOfStatement')">
          <number-format :decimal-place="2" :value="stateHead.amountAccountMoney" />
          <div v-if="application.workflowPermissions.includes('invoiceInfo')" style="margin-left: 125px; ">
            <el-button type="primary" plain @click="downloadRecord">{{ $t('financial.downloadStatement') }}</el-button>
            <financialFile
              class="required"
              style="display: inline;margin-left: 15px"
              business-value="already_sign_account"
              :disabled="disabled || !application.workflowPermissions.includes('invoiceInfoEdit')"
            >
              {{ disabled || !application.workflowPermissions.includes('invoiceInfoEdit') ? $t('financial.viewStampedStatementOfAccount'):$t('financial.uploadStampedStatementOfAccount') }}

            </financialFile>
          </div>
        </el-form-item>
      </el-form>
    </common-card>
    <common-card
      v-if="application.workflowPermissions.includes('invoiceInfo')"
      :title="$t('financial.invoicingInformation')"
    >
      <financialFile
        class="required"
        :disabled="disabled || !application.workflowPermissions.includes('invoiceInfoEdit')"
        business-value="invoice"
      >
        {{ disabled || !application.workflowPermissions.includes('invoiceInfoEdit') ? $t('financial.viewInvoice'):$t('financial.uploadInvoice') }}

      </financialFile>

      <div style="margin-top: 15px">
        <el-button v-if="!disabled && application.workflowPermissions.includes('invoiceInfoEdit')" type="primary" icon="el-icon-plus" plain @click="upload.open=true">{{ $t('financial.batchAdd') }}</el-button>
        <el-button v-if="!disabled && application.workflowPermissions.includes('invoiceInfoEdit')" type="text" icon="el-icon-plus" plain @click="addInvoice">{{ $t('rfq.addTo') }}</el-button>
      </div>

      <el-table
        show-summary
        :summary-method="getSummaries"
        max-height="590"
        :data="invoiceInfo"
      >
        <el-table-column :label="$t('order.number')" type="index" />
        <el-table-column :label="$t('financial.invoiceDate')">
          <template #header>
            <span class="required">{{ $t('financial.invoiceDate') }}</span>
          </template>
          <template #default="scope">
            <show-or-edit
              :value="scope.row.invoiceDate"
              type="Date"
              :disabled="disabled || !application.workflowPermissions.includes('invoiceInfoEdit')"
            >
              <el-date-picker
                v-model="scope.row.invoiceDate"
                clearable
                style="width: 170px"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </show-or-edit>

          </template>
        </el-table-column>
        <el-table-column :label="$t('financial.invoiceNumber')">
          <template #header>
            <span class="required">{{ $t('financial.invoiceNumber') }}</span>
          </template>
          <template #default="scope">
            <show-or-edit
              :value="scope.row.invoiceNo"
              :disabled="disabled || !application.workflowPermissions.includes('invoiceInfoEdit')"
            >
              <vxe-input v-model="scope.row.invoiceNo" />

            </show-or-edit>
          </template>
        </el-table-column>
        <el-table-column :label="$t('financial.preTaxAmountInOriginalCurrency')" prop="originCurrencyBeforeTax">
          <template #default="scope">
            <show-or-edit
              :value="scope.row.originCurrencyBeforeTax"
              :disabled="disabled || !application.workflowPermissions.includes('invoiceInfoEdit')"
            >
              <vxe-input v-model.number="scope.row.originCurrencyBeforeTax" type="float" />

            </show-or-edit>
          </template>
        </el-table-column>
        <el-table-column :label="$t('financial.taxAmountInOriginalCurrency')" prop="originCurrencyTax">
          <template #default="scope">
            <show-or-edit
              :value="scope.row.originCurrencyTax"
              :disabled="disabled || !application.workflowPermissions.includes('invoiceInfoEdit')"
            >
              <vxe-input v-model.number="scope.row.originCurrencyTax" type="float" />

            </show-or-edit>
          </template>
        </el-table-column>
        <el-table-column :label="$t('financial.totalPriceAndTaxInOriginalCurrency')" prop="amountOriginCurrencyTax">
          <template #header>
            <span class="required">{{ $t('financial.totalPriceAndTaxInOriginalCurrency') }}</span>
          </template>
          <template #default="scope">
            <show-or-edit
              :value="scope.row.amountOriginCurrencyTax"
              :disabled="disabled || !application.workflowPermissions.includes('invoiceInfoEdit')"
            >
              <vxe-input v-model="scope.row.amountOriginCurrencyTax" type="float" />

            </show-or-edit>
          </template>
        </el-table-column>
        <el-table-column :label="$t('financial.voucherItemNumber')">
          <template #header>
            <span class="required">{{ $t('financial.voucherItemNumber') }}</span>
          </template>
          <template #default="scope">
            <show-or-edit
              :value="scope.row.orderNo"
              :disabled="disabled || !application.workflowPermissions.includes('invoiceInfoEdit')"
            >
              <vxe-input v-model="scope.row.orderNo" />

            </show-or-edit>
          </template>
        </el-table-column>
        <el-table-column v-if="!disabled && application.workflowPermissions.includes('invoiceInfoEdit')" :label="$t('common.operate')">
          <template #default="scope">
            <el-button
              type="text"
              style="text-decoration: underline"
              @click="delInvoice(scope.$index,scope.row.id)"
            >{{ $t('common.del') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </common-card>
    <bpm-process-instance
      v-if="application.processInstanceId"
      :process-instance-id="application.processInstanceId"
      :view-only="disabled"
      :close-attribute="{visible:false}"
      @saveAuditEvent="saveAudit"
      @submitAuditEvent="submitAudit"
      @approvalAuditEvent="submitAudit"
      @rejectAuditEvent="rejectAudit"
      @closeAuditEvent="closeAuditEvent"
    />
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          class="small-padding"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileImportSuccess"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="addVisible"
      :title="$t('financial.addInboundData')"
      width="1200px"
      :visible.sync="addVisible"
      @close="closeDialog"
    >
      <el-form inline label-width="105px">
        <el-form-item class="addItem" :label="$t('financial.warehousingDate')">
          <el-date-picker
            v-model="addQueryParams.time"
            clearable
            style="width: 220px"
            type="daterange"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item class="addItem" :label="$t('material.materialDescription')">
          <el-input v-model="addQueryParams.materialDescription" />
        </el-form-item>
        <el-form-item class="addItem" :label="$t('order.orderNumber')">
          <el-input v-model="addQueryParams.orderNo" />
        </el-form-item>
        <el-form-item class="addItem" :label="$t('financial.receiptVoucherNumber')">
          <el-input v-model="addQueryParams.receiptVoucherNum" />
        </el-form-item>
      </el-form>
      <vxe-grid
        ref="addList"
        :data="addList"
        :loading="addLoading"
        v-bind="addGirdOption"
      >
        <template #toolbar_buttons="{}">
          <el-row :gutter="10" style="width: 100%" type="flex" justify="space-between" class="mb8">
            <el-col :span="1.5">
              <el-button
                :loading="btnLoading"
                size="mini"
                type="primary"
                @click="saveAdd"
              >  {{ $t('common.save') }}</el-button>
              <el-button
                size="mini"
                @click="closeDialog"
              >  {{ $t('order.close') }}</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="primary" @click="addQueryParams.pageNo=1;getAddInfo();">{{ $t('common.search') }}</el-button>
              <el-button @click="resetAddQuery">{{ $t('common.reset') }}</el-button>
            </el-col>
          </el-row>
        </template>
        <template #projectNo="{row}">
          <el-button
            style="text-decoration: underline"
            type="text"
            @click="$router.push(`/tender/tenderShow/${row.projectNo}?id=${row.id}`)"
          >{{ row.projectNo }}</el-button>
        </template>

      </vxe-grid>
      <!-- 分页组件 -->
      <pagination
        v-show="addTotal > 0"
        :total="addTotal"
        :page.sync="addQueryParams.pageNo"
        :limit.sync="addQueryParams.pageSize"
        @pagination="getAddInfo"
      />
    </el-dialog>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import BpmProcessInstance from '@/components/BpmProcessInstance/index.vue'
import {
  cancelFinancialRecord,
  deleteFinancialDetail, delInvoiceInfo, downloadRecordRecord, downloadTemplate, exportExcelDetailRecord,
  getFinancialDetailPage,
  getFinancialDetailRecord,
  getListInventory, rejectFinancialRecord,
  saveDiffHeadRecord,
  saveFinancialAdd, saveFinancialRecord, submitFinancialRecord
} from '@/api/financial'
import { mapGetters } from 'vuex'
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE, getDictDataLabel, getDictDatas } from '@/utils/dict'
import { getAccessToken } from '@/utils/auth'
import { getBaseHeader } from '@/utils/request'
import dayjs from 'dayjs'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
export default defineComponent({
  name: 'Financedetail/:id',
  components: {
    ShowOrEdit,
    BpmProcessInstance,
    financialFile: () => import('@/views/financial/upload.vue')
  },
  provide() {
    return {
      financialFile: () => this.application
    }
  },
  data() {
    return {
      btnLoading: false,
      /**
       * 抬头和对账单明细、其他加减项、金额统计是所有节点可见的。
       *
       * 【主线版本/雪榕版本】对账单抬头可编辑-headEdit(抬头和对账单明细、其他加减项的编辑是同时生效的。)
       * 【雪榕版本】对账单明细-detail
       * 【雪榕版本】对账单明细可编辑-detailEdit
       * 【雪榕版本】其他加减项目-otherItem
       * 【雪榕版本】其他加减项目可编辑-otherItemEdit
       * 【主线版本/雪榕版本】金额统计-priceStatistic
       * 【主线版本/雪榕版本】开票资料-invoiceInfo
       * 【雪榕版本】开票资料可编辑-invoiceInfoEdit
       *
       * 主线默认展示抬头、对账单明细、其他加减项、金额统计
       * 创建对账单：headEdit
       * 确认对账单：[]
       * 审批对账单：[]
       * 提交开票资料：invoiceInfo
       *
       * 默认为head，允许抬头的可编辑状态
       */
      application: {
        processInstanceId: '',
        fileRelVOS: [],
        workflowPermissions: ['headEdit']
      },
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: this.$t('financial.batchAdd'),
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/financial/invoice-info/import'
      },
      total: 0,
      list: [],
      title: '',
      open: false,
      loading: false,
      showSearch: false,
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'financeAdd',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: { isCurrent: true,
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns:
            [
              { type: 'checkbox', width: 30, visible: true, fixed: 'left' },

              {
                title: this.$t('financial.warehousingDate'), field: 'inventoryDate',
                visible: true, width: 100,
                formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
              },
              {
                title: this.$t('order.receiptQuantity'),
                field: 'inventoryQuantity',
                slots: { default: 'inventoryQuantity' },

                visible: true,
                width: 100
              },
              {
                title: this.$t('financial.receiptVoucherNumber'),
                field: 'receiptVoucherNum',
                visible: true,
                width: 100
              },
              {
                title: this.$t('financial.voucherItemNumber'),
                field: 'voucherProjectNum',
                visible: true,
                width: 100
              },
              { title: this.$t('order.moveType'),
                formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.ORDER_RECEIPT_TYPE, cellValue) : '',
                field: 'receiptType', visible: true, width: 100 },
              {
                title: this.$t('order.receiptRelCertificate'),
                field: 'receiptRelCertificate',
                visible: false,
                width: 100
              },
              { title: this.$t('material.purchasingUnit'),
                formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.MATERIAL_UOM, cellValue) : '',
                field: 'purchasingUnit', visible: true, width: 100 },
              {
                title: this.$t('order.unitPriceWithoutTax'),
                field: 'unitPriceBeforeTax',
                slots: { default: 'unitPriceBeforeTax' },
                visible: true,
                width: 100
              },
              {
                title: this.$t('order.unitPriceIncludingTax'),
                field: 'unitPriceAfterTax',
                slots: { default: 'unitPriceAfterTax' },
                visible: true,
                width: 100
              },
              { title: this.$t('order.amountNotTaxed'),
                slots: { default: 'beforeTax' },
                field: 'beforeTax', visible: true, width: 100, formatter: ({ cellValue }) => cellValue?.toFixed(2) },
              { title: this.$t('order.amountIncludingTax'),
                slots: { default: 'afterTax' },
                field: 'afterTax', visible: true, width: 100 },
              /*  { title: this.$t('financial.taxAmount'),
                slots: { default: 'taxAmount' },
                field: 'taxAmount', visible: true, width: 100, formatter: ({ cellValue }) => cellValue?.toFixed(2) },*/
              {
                title: this.$t('financial.paidAmountreference'),
                field: 'alreadyPaymentTax',
                visible: true,
                width: 100
              },
              { title: this.$t('order.orderNumber'), field: 'orderNo', visible: true, width: 100 },
              { title: this.$t('order.orderLineNo'), field: 'orderLineNo', visible: true, width: 100 },
              { title: this.$t('material.materialCode'), field: 'materialCode', visible: true, width: 100 },
              {
                title: this.$t('order.describe'),
                field: 'materialDescription',
                visible: true,
                width: 100
              },
              { title: this.$t('material.specificationAndModel'), field: 'specification', visible: true, width: 100 },
              { title: this.$t('material.revision'), field: 'materialVersion', visible: true, width: 100 },
              { title: this.$t('material.manufacturer'), field: 'mfg', visible: false, width: 100 },
              { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: false, width: 100 }
            ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      addGirdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'financeAddIn',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: { isCurrent: true,
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns:
            [
              { type: 'checkbox', width: 30, visible: true, fixed: 'left' },

              {
                title: this.$t('financial.warehousingDate'), field: 'inventoryDate',
                visible: true, width: 100,
                formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
              },
              {
                title: this.$t('order.receiptQuantity'),
                field: 'inventoryQuantity',
                visible: true,
                width: 100
              },
              {
                title: this.$t('financial.receiptVoucherNumber'),
                field: 'receiptVoucherNum',
                visible: true,
                width: 100
              },
              {
                title: this.$t('financial.voucherItemNumber'),
                field: 'voucherProjectNum',
                visible: true,
                width: 100
              },
              {
                title: this.$t('material.materialDescription'),
                field: 'materialDescription',
                visible: true,
                width: 100
              },
              { title: this.$t('order.moveType'),
                formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.ORDER_RECEIPT_TYPE, cellValue) : '',
                field: 'receiptType', visible: true, width: 100 },
              {
                title: this.$t('order.receiptRelCertificate'),
                field: 'receiptRelCertificate',
                visible: false,
                width: 100
              },
              { title: this.$t('material.purchasingUnit'),
                formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.MATERIAL_UOM, cellValue) : '',
                field: 'purchasingUnit', visible: true, width: 100 },
              {
                title: this.$t('order.unitPriceWithoutTax'),
                field: 'unitPriceBeforeTax',
                visible: true,
                width: 100,
                formatter: ({ cellValue }) => cellValue?.toFixed(2)
              },
              {
                title: this.$t('order.unitPriceIncludingTax'),
                field: 'unitPriceAfterTax',
                visible: true,
                width: 100,
                formatter: ({ cellValue }) => cellValue?.toFixed(2)
              },
              { title: this.$t('order.amountNotTaxed'), field: 'beforeTax', visible: true, width: 100, formatter: ({ cellValue }) => cellValue?.toFixed(2) },
              { title: this.$t('order.amountIncludingTax'), field: 'afterTax', visible: true, width: 100, formatter: ({ cellValue }) => cellValue?.toFixed(2) },
              { title: this.$t('financial.taxAmount'), field: 'taxAmount', visible: true, width: 100, formatter: ({ cellValue }) => cellValue?.toFixed(2) },
              {
                title: this.$t('financial.paidAmountreference'),
                field: 'alreadyPaymentTax',
                visible: true,
                width: 100, formatter: ({ cellValue }) => cellValue?.toFixed(2)
              },
              { title: this.$t('order.orderNumber'), field: 'orderNo', visible: true, width: 100 },
              { title: this.$t('order.orderLineNo'), field: 'orderLineNo', visible: true, width: 100 },
              { title: this.$t('material.materialCode'), field: 'materialCode', visible: true, width: 100 },
              // {
              //   title: this.$t('order.describe'),
              //   field: 'materialDescription',
              //   visible: true,
              //   width: 100
              // },
              { title: this.$t('material.specificationAndModel'), field: 'specification', visible: true, width: 100 },
              { title: this.$t('material.revision'), field: 'materialVersion', visible: true, width: 100 },
              { title: this.$t('material.manufacturer'), field: 'mfg', visible: false, width: 100 },
              { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: false, width: 100 }
            ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      addQueryParams: {
        financialId: Number(this.$route.query.id),
        beginInventoryDate: '',
        time: [],
        endInventoryDate: '',
        materialCode: '',
        materialDescription: '',
        orderNo: '',
        receiptVoucherNum: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      },
      queryParams: {
        financialId: Number(this.$route.query.id),
        searchText: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      },
      addVisible: false,
      addList: [],
      addLoading: false,
      addTotal: 0,
      stateHead: {
        supplierId: this.supplierId,
        companyId: '',
        currency: '',
        taxRate: '',
        id: null
      },
      otherTable: [
        {
          fileRelVOS: [],
          itemNo: 1,
          otherItem: '',
          priceBeforeTax: null,
          priceAfterTax: null,
          relRecord: '',
          remark: '',
          visible: false
        }
      ],
      supplierList: [],
      uploadVisible: false,
      isUploading: false,
      url: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      data: {},
      fileList: [],
      disabled: false,
      invoiceInfo: [],
      stateHeadRule: {
        supplierId: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        companyId: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        currency: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }]
      },
      displaySupplierName: this.supplierName,
      invTotalAmount: null,
      afterTaxTotalAmount: null
    }
  },
  computed: {
    ...mapGetters([
      'supplierId',
      'supplierName'
    ]),
    'addQueryParams.financialId': () => {
      return this.stateHead.id
    },
    'queryParams.financialId': () => {
      return this.stateHead.id
    }
  },
  mounted() {
    this.init()
    if (this.$route.query.viewOnly) {
      this.disabled = true
    }
    // 供应商账户进入，初始化供应商信息
    if (this.supplierId) {
      this.stateHead.supplierId = this.supplierId
    }
  },
  methods: {
    async getInfo(id) {
      const res = await getFinancialDetailRecord({
        id
      })
      return res.data.headVO
    },
    /**
     * 优化：验证未税金额是否大于含税金额（13%税）
     * 正数时：未税金额（100） / 含税金额（113）
     * 负数时：未税金额（-100） / 含税金额（-113）
     * 因此比较是需要将负数取反进行比较，兼容负数
     *
     * @param index
     */
    validatePrice(index) {
      var item = this.otherTable[index]
      if (item.priceBeforeTax && item.priceAfterTax) {
        // 是否为负数，则转成正数
        if (item.priceBeforeTax < 0) {
          item.priceBeforeTax = item.priceBeforeTax.negate()
        }
        if (item.priceAfterTax < 0) {
          item.priceAfterTax = item.priceAfterTax.negate()
        }
        if (item.priceBeforeTax > item.priceAfterTax) {
          this.otherTable[index].priceBeforeTax = 0
          this.$message.error(this.$t('financial.theAmountCannotBeLessThanTheAmountBeforeTax'))
        }
      }
    },
    init(fid) {
      const id = this.$route.query.id || fid
      if (id) {
        getFinancialDetailRecord({
          id
        }).then(async res => {
          res.data.otherItemList.forEach(a => {
            a.visible = false
            a.fileRelList?.forEach(b => {
              b.name = b.fileName
            })
            a.fileRelVOS = a.fileRelList || []
          })
          res.data.fileRelVOS = res.data.fileList || []
          this.otherTable = res.data.otherItemList
          this.application = res.data
          this.invoiceInfo = res.data.invoiceInfoList
          if (res.data.headVO.supplierId) {
            await this.getSupplierDetail(res.data.headVO.supplierName)
          }
          this.stateHead = res.data.headVO
          this.displaySupplierName = this.stateHead.supplierName
          this.stateHead.id = Number(id)
          this.getFinancialDetailPage()
        })
      }
    },
    delState() {
      const data = this.$refs.financeDetail.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      deleteFinancialDetail({ ids: data.map(a => a.id) }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.getFinancialDetailPage()
      })
    },
    getAddInfo() {
      getListInventory({
        ...this.addQueryParams,
        beginInventoryDate: this.addQueryParams.time.at(0),
        endInventoryDate: this.addQueryParams.time.at(1),
        financialId: this.stateHead.id
      }).then(res => {
        this.addLoading = false
        this.addList = res.data.list
        this.addTotal = res.data.total
      })
    },
    getFinancialDetailPage() {
      getFinancialDetailPage({
        ...this.queryParams,
        financialId: this.stateHead.id
      }).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    showAdd() {
      this.addVisible = true
      this.getAddInfo()
    },
    closeDialog() {
      this.addVisible = false
      this.resetAddQuery()
    },
    saveAdd() {
      const data = this.$refs.addList.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      data.forEach(a => {
        a.accountId = this.stateHead.id
      })
      this.btnLoading = true
      saveFinancialAdd(data).then(res => {
        this.getFinancialDetailPage()
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.addVisible = false
        this.resetAddQuery()
        this.btnLoading = false
      }).catch(err => {
        this.btnLoading = false
      })
    },
    async saveHead() {
      this.$refs.stateHead.validate(async valid => {
        if (!valid) {
          return
        }
        var error = false
        // extra: 币种为人民币时，其税率必填。
        await this.getConfigKey('system.currency.rmb').then(res => {
          // CNY
          if (res.data) {
            const CNYCurrency = getDictDatas(DICT_TYPE.COMMON_CURRENCY).filter(currencyObj => currencyObj.code === res.data)
            if (CNYCurrency && CNYCurrency[0].id === this.stateHead.currency && (!this.stateHead.taxRate && this.stateHead.taxRate !== 0)) {
              error = true
              this.$message.error(this.$t('financial.taxRateRequiredForRmb'))
            }
          }
        })
        if (!error) {
          saveDiffHeadRecord(this.stateHead).then(async res => {
            this.stateHead.id = res.data
            this.$message.success(this.$t('common.savedSuccessfully'))
            const data = await this.getInfo(this.stateHead.id)
            await this.$tab.closeOpenPage(
              `/financial/financedetail/${data.accountNo}?id=${this.stateHead.id}`
            )
          })
        }
      })
    },
    resetAddQuery() {
      this.addQueryParams = {
        financialId: Number(this.$route.query.id),
        beginInventoryDate: '',
        endInventoryDate: '',
        materialCode: '',
        materialDescription: '',
        orderNo: '',
        time: [],
        receiptVoucherNum: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      }
      this.getAddInfo()
    },
    downloadExcel() {
      exportExcelDetailRecord(this.queryParams).then(res => {
        this.$download.excel(res, this.$t('financial.statementDetailsXlsx'))
      })
    },
    delOther(index) {
      this.otherTable.splice(index, 1)
    },
    addOthers() {
      if (this.otherTable.length > 9) {
        this.$message.error(this.$t('financial.addUpToPiecesOfData'))
        return
      }
      this.otherTable.push({
        fileRelVOS: [],
        itemNo: 1,
        otherItem: '',
        priceBeforeTax: null,
        priceAfterTax: null,
        relRecord: '',
        remark: '',
        visible: false
      })
    },
    async getSupplierDetail(query) {
      if (query) {
        await getSupplierDetail({
          fuzzySupplierName: query
        }).then(res => {
          this.supplierList = res.data
        })
      } else {
        this.supplierList = []
      }
    },
    handleFileSuccess(response, file, fileList, row) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
        this.$refs.upload.clearFiles()
        return
      }
      row.fileRelVOS.push({
        name: file.name,
        fileName: file.name,
        fileId: response.data.id,
        filePath: response.data.url
      })
    },
    handleRemove(file, fileList, row) {
      const index = row.fileRelVOS.findIndex(a => a.fileId === file.fileId)
      if (index > -1) {
        row.fileRelVOS.splice(index, 1)
      }
    },
    showUpload(row) {
      row.visible = true
    },
    onPreview(file) {
      window.open(file.filePath)
    },
    showFile() {
      this.visible = true
      const a = this.app.scarInfo.fileRelList.filter(a => a.businessValue === this.businessValue)
      a.forEach(b => {
        b.name = b?.fileName
      })
      this.fileList = a
    },
    saveAudit() {
      this.invoiceInfo.forEach((a, index) => { a.itemNo = index + 1 })
      saveFinancialRecord({
        id: this.stateHead.id,
        otherItemReqVOS: this.otherTable,
        fileRelVOS: this.application.fileRelVOS,
        invoiceInfoReqVOS: this.invoiceInfo,
        processInstanceId: this.application.processInstanceId
      }).then(res => {
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.init(this.stateHead.id)
      })
    },
    submitAudit(taskId, processInstanceId, reason) {
      if (!this.otherTable.every(a => a.priceBeforeTax && a.priceAfterTax && a.otherItem)) {
        this.$message.error(this.$t('supplier.pleaseCompleteTheInformation'))
        return
      }
      if (this.application.workflowPermissions.includes('invoiceInfo')) {
        if (!this.invoiceInfo) {
          this.$message.error(this.$t('financial.pleaseAddAtLeastOneInvoicingInformation'))
          return
        }
        if (!this.invoiceInfo.every(a => a.amountOriginCurrencyTax)) {
          this.$message.error(this.$t('financial.pleaseFillInTheTotalAmountOfOriginalCurrencyIncludingTax'))
          return
        }
        if (!this.application.fileRelVOS.some(a => a.businessValue === 'invoice')) {
          this.$message.error(this.$t('financial.pleaseUploadTheInvoice'))
          return
        }
        if (!this.application.fileRelVOS.some(a => a.businessValue === 'already_sign_account')) {
          this.$message.error(this.$t('financial.pleaseUploadAStampedStatementOfAccount'))
          return
        }
      }
      this.invoiceInfo.forEach((a, index) => { a.itemNo = index + 1 })
      submitFinancialRecord({
        id: this.stateHead.id,
        workflowPermissions: this.application.workflowPermissions,
        otherItemReqVOS: this.otherTable,
        taskId,
        invoiceInfoReqVOS: this.invoiceInfo,
        fileRelVOS: this.application.fileRelVOS,
        processInstanceId: this.application.processInstanceId,
        processInstanceReason: reason
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.$tab.closeOpenPage('/financial/finacneIndex')
      })
    },
    rejectAudit(taskId, processInstanceId, processInstanceReason) {
      rejectFinancialRecord({
        id: this.stateHead.id,
        processInstanceId: this.application.processInstanceId,
        taskId,
        processInstanceReason
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.$tab.closeOpenPage('/financial/finacneIndex')
      })
    },
    closeAuditEvent() {
      cancelFinancialRecord({ ids: [this.stateHead.id] }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.$tab.closeOpenPage('/financial/finacneIndex')
      })
    },
    addInvoice() {
      this.invoiceInfo.push({
        amountOriginCurrencyTax: null,
        invoiceDate: '',
        invoiceNo: '',
        itemNo: null,
        orderNo: '',
        originCurrencyBeforeTax: null,
        originCurrencyTax: null
      })
    },
    delInvoice(index, id) {
      this.invoiceInfo.splice(index, 1)
      if (id) {
        delInvoiceInfo({ id }).then(res => {
          this.$message.success(this.$t('common.delSuccess'))
        })
      }
    },
    getOtherItemSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = this.$t('financial.otherAdditionAndSubtractionAmounts')
          return
        }
        if (index !== 2 && index !== 3) {
          sums[index] = ''
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.$t('financial.total')
          return
        }
        if (index === 1 || index === 2 || index === 6 || index === 7) {
          sums[index] = ''
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          sums[index] = ''
        }
      })
      // 获取原币价税合计
      this.invTotalAmount = sums[5]
      sums[5] = this.formatNumNoZero(sums[5])// 保留的小数位数
      return sums
    },
    downloadRecord() {
      downloadRecordRecord({ id: this.stateHead.id }).then(res => {
        if (res.data) {
          window.open(res.data)
        }
      })
    },
    submitFileForm() {
      this.$refs.upload.submit()
    },
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileImportSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      response.data.forEach(a => { a.invoiceDate = a.invoiceDate ? dayjs(a.invoiceDate).format('YYYY-MM-DD') : '' })
      this.invoiceInfo.push(...response.data)
      this.$message.success(this.$t('rfq.importSucceeded'))

      // this.getList()
    },
    importTemplate() {
      downloadTemplate().then(response => {
        this.$download.excel(response, this.$t('financial.invoiceInformationImportTemplatexlsx'))
      })
    }
  }
})
</script>

<style scoped lang="scss">
::v-deep .el-table__footer-wrapper{
  font-weight: bold;
  font-size: 14px;
}

.finance{
  padding: 20px;
}
.searchItem{
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 120px);
  }
}

.addItem{
  width: 25%;
  margin-right: 0;
  ::v-deep .el-form-item__content{
    width: calc(100% - 105px);
  }
}
.searchValue{
  width: 95%;
}
.negative{
  color: red;
}
.required::before{
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}
</style>
