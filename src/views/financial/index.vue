<template>
  <div>
    <div class="app-container">
      <StatisticsCard :item-arr="financialStatic" />
      <!-- 搜索工作栏 -->
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.supplierText	"
          style="flex: 0 1 40%"
          :placeholder="$t('financial.supplierCodeSupplierNameAbbreviation')"
          clearable
          @keyup.enter.native="doSearch"
        />
        <el-button type="primary" plain @click="queryParams.pageNo = 1;doSearch();">{{ $t('common.search') }}</el-button>
        <el-button plain style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            class="el-icon-arrow-up"
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>

      </div>
      <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="175px">
        <el-form-item class="searchItem" :label="$t('order.company')">
          <el-select v-model="queryParams.companyIds" class="searchValue" clearable filterable multiple>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.COMMON_COMPANY,0)"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('supplier.supplier')">
          <el-input
            v-model="queryParams.supplierText"
            class="searchValue"
            :placeholder="$t('common.pleaseEnter')"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('common.buyer')" class="searchItem">
          <el-select v-model="queryParams.buyerIds" class="searchValue" clearable filterable multiple>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item class="searchItem" :label="$t('financial.statementStatus')">
          <el-input
            v-model="queryParams.accountStatus"
            class="searchValue"
            :placeholder="$t('common.pleaseEnter')"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('financial.reconciliationNumber')" class="searchItem">
          <el-input
            v-model="queryParams.accountNo"
            class="searchValue"
            :placeholder="$t('common.pleaseEnter')"
            clearable
          />

        </el-form-item>
        <el-form-item :label="$t('order.orderNumber')" class="searchItem">
          <el-input
            v-model="queryParams.orderNo"
            class="searchValue"
            :placeholder="$t('common.pleaseEnter')"
            clearable
          />

        </el-form-item>

        <el-form-item :label="$t('financial.invoiceVoucherNumber')" class="searchItem">
          <el-input
            v-model="queryParams.invoiceVoucherNo"
            class="searchValue"
            :placeholder="$t('common.pleaseEnter')"
            clearable
          />

        </el-form-item>
        <el-form-item class="searchItem" :label="$t('financial.receiptVoucherNumber')" prop="dateType">
          <el-input
            v-model="queryParams.receiptVoucherNum"
            class="searchValue"
            :placeholder="$t('common.pleaseEnter')"
            clearable
          />

        </el-form-item>
        <el-form-item class="searchItem" :label="$t('financial.paymentDueDate')">
          <el-date-picker
            v-model="queryParams.time"
            class="searchValue"
            type="daterange"
            :start-placeholder="$t('common.startDate')"
            :end-placeholder="$t('common.endDate')"
            :range-separator="$t('order.to')"
          />
        </el-form-item>

      </el-form>
      <!--      <div v-show="showSearch" style="text-align: center">-->
      <!--        <el-button type="primary" size="mini" icon="el-icon-search" @click="doSearch">{{ $t('common.search') }}</el-button>-->
      <!--        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('common.reset') }}</el-button>-->

      <!--      </div>-->
      <!-- 列表 -->
      <vxe-grid
        ref="financeTable"
        :data="list"
        :loading="loading"
        v-bind="girdOption"
        @sort-change="sortMethod"
        @filter-change="filterMethod"
      >

        <template #toolbar_buttons>
          <el-row :gutter="10" style="width: 100%" class="mb8">

            <el-col :span="1.5">
              <el-button
                v-has-permi="['financial:record:create']"
                type="primary"
                size="mini"
                icon="el-icon-plus"
                @click="$router.push('/financial/financedetail/0')"
              > {{ $t('common.createAStatementOfAccount') }}
              </el-button>
            </el-col>

            <el-col :span="1.5">
              <el-button
                v-has-permi="['financial:detail:query']"
                type="primary"
                plain
                icon="el-icon-download"
                size="mini"
                @click="downloadExcel"
              > {{ $t('order.download') }}
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                v-has-permi="['financial:record:cancel']"
                type="danger"
                plain
                size="mini"
                icon="el-icon-close"
                @click="revokeState"
              > {{ $t('rfq.revoke') }}
              </el-button>
            </el-col>
            <right-toolbar
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              :custom-columns.sync="girdOption.columns"
              @queryTable="doSearch"
            />
          </el-row>
        </template>
        <template #accountNo="{row}">
          <copy-button
            type="text"
            @click="$router.push(`/financial/financedetail/see/${row.accountNo}?id=${row.id}&viewOnly=true`)"
          >{{ row.accountNo }}
          </copy-button>
        </template>
        <template #accountStatus="{row}">
          <el-tag
            :key="row.accountStatus"
            :type="row.statusCssClass"
            :disable-transitions="true"
          >
            {{ row.accountStatus }}
          </el-tag>
        </template>
        <template #operate="{row}">
          <OperateDropDown
            :menu-item="[
              { name: $t('financial.handle'),
                show:row.assignUserIds?.includes($store.getters.userId) && row.accountStatus !== '已完成',
                action: (row)=>$router.push(`/financial/financedetail/${row.accountNo}?id=${row.id}`),
                para: row
              }
            ]"
          />
        </template>

      </vxe-grid>
      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="doSearch"
      />
    </div>

  </div>
</template>
<script>
import { defineComponent } from 'vue'
import {
  cancelFinancialRecord,
  exportExcelRecord,
  getFinancialRecord, getStatic
} from '@/api/financial'
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE, getDictDataLabel, getDictDatas } from '@/utils/dict'
import OperateDropDown from '@/components/OperateDropDown/index.vue'

export default defineComponent({
  name: 'Finacneindex',
  components: { OperateDropDown },
  data() {
    return {
      total: 0,
      list: [],
      title: '',
      open: false,
      loading: false,
      showSearch: false,

      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'financeIndex',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: { isCurrent: true,
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns:
          [
            { title: '', type: 'checkbox', visible: true, width: 30, fixed: 'left' },
            { title: this.$t('common.operate'), field: 'operate', slots: { default: 'operate' }, fixed: 'left', showOverflow: false, visible: true, width: 35 },
            { title: this.$t('financial.reconciliationNumber'), field: 'accountNo', slots: { default: 'accountNo' }, fixed: 'left', visible: true, width: 150 },
            {
              title: this.$t('common.creationDate'),
              sortable: true,
              field: 'createTime',
              visible: true,
              width: 100,
              formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
            },
            {
              title: this.$t('order.company'),
              field: 'companyId',
              visible: true,
              width: 100,
              formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.COMMON_COMPANY, cellValue) : ''
            },
            { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
            { title: this.$t('supplier.supplierCode'), field: 'supplierCode', visible: true, width: 100 },
            { title: this.$t('financial.statementStatus'), field: 'accountStatus', visible: true, width: 100, slots: { default: 'accountStatus' }},
            {
              title: this.$t('financial.totalAmountOfStatementincludingTax'),
              field: 'amountAccountMoney',
              visible: true,
              width: 100,
              formatter: ({ cellValue }) => cellValue?.toFixed(2)
            },
            {
              title: this.$t('system.currency'),
              filterMultiple: false,
              filters: getDictDatas(DICT_TYPE.COMMON_CURRENCY, 0).map(a => {
                return { label: a.name, value: a.id }
              }),
              field: 'currency',
              visible: true,
              width: 100,
              formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.COMMON_CURRENCY, cellValue) : ''
            },
            {
              title: this.$t('supplier.taxRate'),
              filterMultiple: false,
              filters: getDictDatas(DICT_TYPE.SUPPLIER_RATE, 0),
              field: 'taxRate',
              visible: true,
              width: 100,
              formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.SUPPLIER_RATE, cellValue) : ''
            },
            { title: this.$t('financial.invoiceDate'), sortable: true, field: 'invoiceDate', visible: true, width: 100 },
            {
              title: this.$t('supplier.accountingPeriod'),
              filterMultiple: false,
              filters: getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS, 0),
              field: 'accountDay',
              visible: true,
              width: 100,
              formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.SUPPLIER_PAYMENT_DAYS, cellValue) : ''
            },
            { title: this.$t('financial.paymentDueDate'), field: 'paymentDueDate', visible: true, width: 100 },
            {
              title: this.$t('common.buyer'),
              field: 'buyerIds',
              visible: true,
              width: 100,
              formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.COMMON_USERS, cellValue) : ''
            },
            { title: this.$t('financial.paymentMark'), field: 'paymentMark', visible: true, width: 100 },
            { title: this.$t('financial.relatedPaymentApplication'), field: 'paymentApplyRel', visible: true, width: 100 },
            { title: this.$t('financial.invoiceVoucherNumber'), field: 'invoiceVoucherNo', visible: true, width: 100 }

          ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        },
        sortConfig: {
          remote: true
        }
      },

      queryParams: {
        accountDay: '',
        accountNo: '',
        accountStatus: '',
        beginPaymentDueDate: null,
        buyerIds: [],
        companyIds: [],
        currency: null,
        endPaymentDueDate: null,
        invoiceVoucherNo: '',
        orderNo: '',
        pageNo: 1,
        pageSize: 10,
        paymentApplyRel: '',
        paymentMark: '',
        receiptVoucherNum: '',
        sortBy: 'ASC',
        sortField: '',
        supplierText: '',
        taxRate: '',
        time: []
      },
      financialStatic: [],
      isInitial: false
    }
  },
  mounted() {
    this.getStatic()
    if (!this.isInitial) {
      this.doSearch()
    }
  },
  activated() {
    this.getStatic()
    if (this.isInitial) {
      this.doSearch()
    }
  },
  methods: {
    getStatic() {
      getStatic().then(res => {
        this.financialStatic = [
          {
            label: this.$t('financial.ytdHasBeenCreated'),
            value: res.data.ytdCreatedTotal
          },
          {
            label: this.$t('financial.ytdCompleted'),
            value: res.data.ytdCompleteTotal
          },
          {
            label: this.$t('financial.inTheYtdProcess'),
            value: res.data.ytdPendingTotal
          }
        ]
      })
    },
    doSearch() {
      this.loading = true
      this.queryParams.beginPaymentDueDate = this.queryParams.time?.at(0) || null
      this.queryParams.endPaymentDueDate = this.queryParams.time?.at(1) || null

      getFinancialRecord(this.queryParams).then(res => {
        this.isInitial = true
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },

    resetQuery() {
      this.queryParams = {
        accountDay: '',
        accountNo: '',
        accountStatus: '',
        beginPaymentDueDate: null,
        buyerIds: [],
        companyIds: [],
        currency: null,
        endPaymentDueDate: null,
        invoiceVoucherNo: '',
        orderNo: '',
        pageNo: 1,
        pageSize: 10,
        paymentApplyRel: '',
        paymentMark: '',
        receiptVoucherNum: '',
        sortBy: 'ASC',
        sortField: '',
        supplierText: '',
        taxRate: '',
        time: []
      }
      this.doSearch()
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.doSearch()
    },
    filterMethod({ column, values, property }) {
      this.queryParams[column.field] = values.at(0)
      this.doSearch()
    },
    revokeState() {
      const data = this.$refs.financeTable.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.$confirm(this.$t('financial.areYouSureYouWantToCancelTheDocument'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        cancelFinancialRecord({ ids: data.map(a => a.id) }).then(res => {
          this.$message.success(this.$t('rfq.cancellationSucceeded'))
          this.doSearch()
        })
      })
    },
    downloadExcel() {
      exportExcelRecord(this.queryParams).then(res => {
        this.$download.excel(res, this.$t('financial.statementXlsx'))
      })
    }
  }
})
</script>

<style scoped lang="scss">
.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 175px);
  }
}

.searchValue {
  width: 95%;
}
</style>
