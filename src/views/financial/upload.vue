<template>
  <div>
    <el-button v-if="!disabled" type="primary" @click="showFile">
      <slot />
      <span v-show="fileLength">({{ fileLength }})</span>

    </el-button>
    <el-button v-else type="primary" @click="showFile">
      <slot />
      <span>({{ fileLength }})</span>
    </el-button>
    <el-dialog
      v-if="visible"
      :title="disabled? $t('scar.seeFile'):$t('common.uploadFile')"
      :visible.sync="visible"
      width="500px"
      append-to-body
      @close="submitFileForm"
    >
      <el-upload
        ref="upload"
        :headers="headers"
        class="upload-container"
        :action="url"
        :disabled="disabled"
        :on-success="handleFileSuccess"
        :file-list="fileList"
        :on-remove="handleRemove"
        :on-preview="onPreview"
        :drag="!disabled"
        multiple
      >
        <i v-if="!disabled" class="el-icon-upload" />
        <div v-if="!disabled" class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="!disabled"
          type="primary"
          @click="submitFileForm"
        >  {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getAccessToken } from '@/utils/auth'

export default {
  name: 'Financials',
  inject: ['financialFile'],
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    businessValue: {
      type: String

    }
  },

  data() {
    return {
      visible: null,
      isUploading: false,
      url: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      data: {},
      fileList: [],
      // 一次批量上传的临时文件集合
      tempList: [],
      delIds: []
    }
  },
  computed: {
    fileInfo() {
      return this.financialFile()
    },
    fileLength() {
      return this.fileInfo.fileRelVOS?.filter(a => a.businessValue === this.businessValue).length
    }
  },
  methods: {
    handleFileSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
        this.$refs.upload.clearFiles()
        return
      }
      this.tempList.push({
        name: file.name,
        fileName: file.name,
        fileId: response.data.id,
        businessValue: this.businessValue,
        filePath: response.data.url
      })
    },
    submitFileForm() {
      this.fileList = this.fileList.filter(a => !this.delIds.includes(a.fileId))
      this.fileInfo.fileRelVOS = [...this.fileInfo.fileRelVOS.filter(a => a.businessValue !== this.businessValue), ...this.fileList, ...this.tempList]
      // 一次批量上传的临时文件集合需要在此处置空
      this.tempList = []
      this.visible = false
    },
    handleRemove(file, fileList) {
      if (file.fileId) {
        this.delIds.push(file.fileId)
      } else {
        const index = this.fileList.findIndex(a => a.fileId === file.response.data.id)
        this.tempList.splice(index, 1)
      }
    },
    showUpload() {
      this.showFile()
    },
    onPreview(file) {
      if (file.filePath) {
        window.open(file.filePath)
      }
    },
    showFile() {
      this.visible = true
      const a = this.fileInfo.fileRelVOS.filter(a => a.businessValue === this.businessValue)
      a.forEach(b => {
        b.name = b?.fileName
      })
      this.fileList = a
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body{
  padding-top: 15px;
}
</style>
