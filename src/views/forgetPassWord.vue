<template>
  <div style="display: flex;align-items: center;min-height: 100%">
    <div
      :style="{'backgroundImage':(loginBgImage!=undefined&&loginBgImage!='')?'url('+loginBgImage+')':'../../assets/images/bg.jpg'}"
      class="back-container"
    >
      <div class="back-background">
        <div style="position: fixed;top: 15px;display: flex;align-items: center;color: #ffffff;font-size: 16px">
          <el-image v-if="loginLogoImage!=undefined&&loginLogoImage!=''" :src="loginLogoImage"
                    style="margin-left: 15px"
          />
          <svg-icon v-else icon-class="logo3" style="height: 68px;width: 198px"/>
        </div>
        <div style="position: fixed;bottom: 15px;right:15px;align-items: center;color: #ffffff;font-size: 16px">
          <div style="color: black;font-size:16px ">Powered by
            <el-button type="text" @click="window.open('http://www.esicint.com')"><span
              style="text-decoration: underline;font-size:16px"
            >ESIC</span></el-button>
          </div>
          <svg-icon v-if="language==='zh'" icon-class="logo3" style="height: 68px;width: 198px"/>
          <svg-icon v-else icon-class="logo1" style="margin-left:47px;height: 92px;width: 125px"/>
        </div>
      </div>

      <div class="forgetPassWord">
        <el-form
          ref="forgetPassWordForm"
          :inline="true"
          :model="forgetPassWordForm"
          class="forgetPassWord-form"
          label-position="left"
          label-width="120px"
        >
          <el-steps :space="200" :active="stepActive" align-center>
            <el-step title="确认账户"></el-step>
            <el-step title="发送验证码"></el-step>
            <el-step title="完成"></el-step>
          </el-steps>
          <div style="margin-top: 30px">
            <div v-if="stepActive===1">
              <el-form-item :label="$t('供应商账号')"  prop="username"
                            :rules="[{ required: true, trigger: ['blur', 'change'], message: '请填写供应商账号' }]"
              >
                <el-input v-model="forgetPassWordForm.username" class="forgetPassWord-common"/>
              </el-form-item>
              <el-form-item :label="$t('supplier.mailbox')"  prop="email"
                            :rules="[{ required: true, trigger: ['blur', 'change'], message: '请填写邮箱' },
                            {pattern:this.mailPattern, message: this.$t('邮箱格式不正确'),trigger: 'blur'}]"
              >
                <el-input v-model="forgetPassWordForm.email" class="forgetPassWord-common"/>
              </el-form-item>
            </div>
            <div v-if="stepActive===2">
              <el-form-item :label="$t('common.emailVerificationCode')"  prop="code"
                            :rules="{ required: true, trigger: ['blur', 'change'], message: '请填写验证码' }">
                <el-input v-model="forgetPassWordForm.code" class="forgetPassWord-common">
                  <el-button slot="append" :disabled="emailDisable" style="padding: 0 5px" type="text"
                             @click="sendCaptcha"
                  >
                    {{
                      emailDisable ? $t('common.reacquireAfterSeconds', { countDown: countDown }) : $t('common.sendVerificationCode')
                    }}
                  </el-button>
                </el-input>
              </el-form-item>
              <el-form-item :label="$t('新密码')"  prop="password"
                            :rules="[{ required: true, trigger: ['blur', 'change'], message: '请填写新密码' },
                            {pattern: /^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/,
                            message: this.$t('system.thePasswordShouldHaveSymbol')}]">
                <el-input v-model="forgetPassWordForm.password" show-password class="forgetPassWord-common"/>
              </el-form-item>
            </div>
            <div v-if="stepActive===3">
              <el-result icon="success" title="成功" subTitle="请重新登录"/>
            </div>
          </div>
          <div style="margin-top: 25px">
            <el-button v-if="stepActive===2" style="width: 100%" type="primary" plain @click="stepActive--">
              {{ $t('上一步') }}
            </el-button>
            <el-button style="width: 100%;margin-left: 0;margin-top: 10px;" type="primary" @click="submitNext">{{
                stepActive >= 3 ? $t('返回登录') : $t('下一步')
              }}
            </el-button>

          </div>
        </el-form>
      </div>
    </div>
  </div>

</template>
<script>
import { checkEmail, forgetPassWord, sendEmail } from '@/api/system/user'
import { getConfigKey } from '@/api/infra/config'

export default {
  name: 'ForgetPassWord',
  data() {
    return {
      loginBgImage: undefined,
      loginLogoImage: undefined,
      stepActive: 1,
      countDown: 60,
      emailDisable: true,
      mailPattern: /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      forgetPassWordForm: {
        username: '',
        email: '',
        code: '',
        password: ''
      }
    }
  },
  computed: {
    language() {
      return this.$store.getters.language
    }
  },
  created() {
    this.getConfigKey('system.company.name').then(response => {
      this.sysCompanyName = response.data
    })
  },
  mounted() {
    this.getLoginImage()
  },
  methods: {
    async submitNext() {
      this.$refs.forgetPassWordForm.validate(
        async(valid) => {
          if (valid) {
            if (this.stepActive === 3) {
              this.$router.push('/login')
            } else {
              if (this.stepActive === 1) {
                const { data } = await checkEmail({
                  email: this.forgetPassWordForm.email,
                  username: this.forgetPassWordForm.username
                })
                if (!data) return
                this.sendCaptcha()
              }
              if (this.stepActive === 2) {
                await forgetPassWord({
                  email: this.forgetPassWordForm.email,
                  username: this.forgetPassWordForm.username,
                  code: this.forgetPassWordForm.code,
                  password: this.forgetPassWordForm.password,
                })
              }
              this.stepActive++
            }
          }
        }
      )
    },
    getLoginImage() {
      getConfigKey('login.bg.image').then(response => {
        this.loginBgImage = response.data
      })
      getConfigKey('login.logo.image').then(response => {
        this.loginLogoImage = response.data
      })
    },
    getCountDown() {
      const count = this.countDown
      const interval = window.setInterval(() => {
        if (--this.countDown <= 0) {
          this.countDown = count
          window.clearInterval(interval)
        }
      }, 1000)
    },
    sendCaptcha() {
      sendEmail({
        email: this.forgetPassWordForm.email,
        username: this.forgetPassWordForm.username
      }).then(res => {
        this.emailDisable = true
        setTimeout(() => {
          this.emailDisable = false
        }, 60000)
        this.getCountDown()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.back-container {
  height: 100vh;
  width: 100%;
  // background-color: $bg;
  //background: linear-gradient(45deg, #0073b1, #0c8996);
  background-image: url("../assets/images/bg.jpg");
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-family: "Helvetica Bold", sans-serif;
      font-size: 30px;
      // color: $light_gray;
      margin: 0 auto 32px auto;
      text-align: left;
      color: #2E6F88;
      font-weight: bold;
    }

    .set-language {
      color: #fff;
      position: absolute;
      top: 8px;
      font-size: 20px;
      right: 0px;
      cursor: pointer;

      ::v-deep .el-dropdown-selfdefine {
        color: #2E6F88;
      }
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }

  .verify {
    ::v-deep .verify-bar-area {
      border: none;
      color: #909399;
    }

    ::v-deep .verify-left-bar {
      border: none;
    }

    ::v-deep .verify-move-block {
      border-radius: 5px;
      border: none;
      box-shadow: 1px 1px 5px 0px rgba(41, 119, 150, 0.54);

      .verify-icon:before {
        width: 20px;
        height: 20px;
        background-image: url(data:image/svg+xml;base64,PHN2ZyBpZD0i5Zu+5bGCXzEiIGRhdGEtbmFtZT0i5Zu+5bGCIDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDIwMCAyMDAiPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDojODBBQ0MzO308L3N0eWxlPjwvZGVmcz48dGl0bGU+5Zu+5qCHPC90aXRsZT48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0xMDMuMywxMDkuMjFhMTAuNDgsMTAuNDgsMCwwLDAsMC0xNC44MmwtNjAtNjAtMTAsMTAsNTcuNCw1Ny40Mi01Ny40LDU3LjMxLDEwLDEwLjA2Wm02Mi44MywwYTEwLjYyLDEwLjYyLDAsMCwwLC4wNS0xNUwxMDcuMTEsMzQuNDNsLTEwLjkxLDEwLDU3LjQsNTcuNDItNTcuNCw1Ny4zMSwxMCwxMC4wNloiLz48L3N2Zz4K)

      }

    }

    ::v-deep .verify-move-block:hover {
      background: #ffffff !important;
    }

    ::v-deep .verify-msg {
      font-size: 14px;
    }
  }

}

.forgetPassWord {
  border-radius: 10px;
  width: 650px;
  background: #F5F7FA;
  margin: 10px auto;
  padding: 60px 70px;

  .forgetPassWord-head {
    display: flex;
    justify-content: center;
    align-items: center;

    .head-png {
      margin: 0 50px;
      width: 100px;
    }
  }

  .agree {
    text-align: center;
    margin: 10px 0
  }

  .forgetPassWord-title {
    font-weight: bold;
    color: #4996B8;
    font-size: 20px;
    text-align: center;
    margin-bottom: 40px;
  }

  .forgetPassWord-form {
    .forgetPassWord-name {
      width: 211px;
    }

    .forgetPassWord-common {
      width: 380px
    }

    .forgetPassWord-checkbox {
      text-align: left;
      width: 560px;
    }

    ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #4996B8;
    }

    ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #4996B8;
      border: 1px solid #4996B8 !important
    }

    ::v-deep .el-checkbox__inner {
      border: 1px solid #4996B8 !important
    }

    ::v-deep .el-checkbox__label {
      padding-left: 40px;
    }

    ::v-deep .el-input-group__append {
      background: #ffffff;
      color: #4996B8;
    }

    .link {
      color: #3b91b6;
    }

    #forgetPassWord-btn {
      float: right;
      width: 55%;
      height: 39px;
      background: #4996B8;
      //box-shadow: 0px 13px 36px 0px rgba(130,184,211,0.74);
      border-radius: 6px;
      font-size: 18px
    }
  }
}

.toSupplierBtn {
  width: 80px;
  border-radius: 3px;
}

</style>
