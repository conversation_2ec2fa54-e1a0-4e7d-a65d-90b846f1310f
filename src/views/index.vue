
<template>
  <div>
    <Internal v-if="$store.getters.isExternal===1" />
    <SupplierIndex v-else />
  </div>
</template>
<script>
import { defineComponent } from 'vue'

import dayjs from 'dayjs'
import Internal from '@/views/dashboard/Internal.vue'
import SupplierIndex from '@/views/dashboard/SupplierIndex.vue'

export default defineComponent({
  name: 'Index',
  components: {
    Internal,
    SupplierIndex
  },
  data() {
    return {
      dayjs

    }
  },
  mounted() {

  },
  methods: {

  }
})
</script>

<style scoped lang="scss">

</style>
