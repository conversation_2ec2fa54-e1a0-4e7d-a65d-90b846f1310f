<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item class="searchItem" label="用户编号" prop="userId">
        <el-select v-model="queryParams.userId" :placeholder="$t('system.pleaseEnterTheOperator')" class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户类型" prop="userType">
        <el-select v-model="queryParams.userType" clearable placeholder="请选择用户类型">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.USER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="应用名" prop="applicationName">
        <el-input
          v-model="queryParams.applicationName"
          clearable
          placeholder="请输入应用名"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="请求地址" prop="requestUrl">
        <el-input v-model="queryParams.requestUrl" clearable placeholder="请输入请求地址" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="请求时间">
        <el-date-picker
          v-model="dateRangeBeginTime"
          end-placeholder="结束日期"
          range-separator="-"
          start-placeholder="开始日期"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="执行时长" prop="duration">
        <el-input v-model="queryParams.duration" placeholder="请输入执行时长" type="number" min="0" oninput="value=value.replace(/[^\d]/g,'')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="结果码" prop="resultCode">
        <el-select v-model="queryParams.resultCode" placeholder="请选择结果编码" clearable style="width: 240px">
          <el-option :key="0" :label="$t('system.success')" :value="0" />
          <el-option :key="-1" :label="$t('system.fail')" :value="-1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['infra:api-access-log:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="日志编号" align="center" prop="id" />
      <el-table-column label="用户编号" align="center" prop="userId">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="scope.row.userId" />
        </template>
      </el-table-column>
      <el-table-column label="用户类型" align="center" prop="userType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.USER_TYPE" :value="scope.row.userType" />
        </template>
      </el-table-column>>
      <el-table-column label="应用名" align="center" prop="applicationName" />
      <el-table-column label="请求方法名" align="center" prop="requestMethod" />
      <el-table-column label="请求地址" align="center" prop="requestUrl" width="250" />
      <el-table-column label="请求时间" align="center" prop="beginTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.beginTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="执行时长" prop="startTime">
        <template slot-scope="scope">
          <span>{{ scope.row.duration }}  ms</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作结果" prop="status">
        <template slot-scope="scope">
          <span>{{ scope.row.resultCode === 0 ? '成功' : ''+ scope.row.resultCode +' (' + scope.row.resultMsg + ')' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['infra:api-access-log:query']"
            icon="el-icon-view"
            size="mini"
            type="text"
            @click="handleView(scope.row,scope.index)"
          >详细
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 查看明细 -->
    <el-dialog :visible.sync="open" append-to-body title="API 访问日志详细" width="700px">
      <el-form ref="form" :model="form" label-width="100px" size="mini">
        <el-row>
          <el-col :span="24">
            <el-form-item label="日志主键">{{ form.id }}</el-form-item>
            <el-form-item label="链路追踪">{{ form.traceId }}</el-form-item>
            <el-form-item label="应用名">{{ form.applicationName }}</el-form-item>
            <el-form-item label="用户信息">
              {{ form.userId }}
              <dict-tag :type="DICT_TYPE.USER_TYPE" :value="form.userType" />
              | {{ form.userIp }} | {{ form.userAgent }}
            </el-form-item>
            <el-form-item label="请求信息">{{ form.requestMethod }} | {{ form.requestUrl }}</el-form-item>
            <el-form-item label="请求参数">{{ form.requestParams }}</el-form-item>
            <el-form-item label="开始时间">
              {{ parseTime(form.beginTime) }} ~ {{ parseTime(form.endTime) }} | {{ form.duration }} ms
            </el-form-item>
            <el-form-item label="操作结果">
              <div v-if="form.resultCode === 0">正常</div>
              <div v-else-if="form.resultCode > 0">失败 | {{ form.resultCode }} || {{ form.resultMsg }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { exportApiAccessLogExcel, getApiAccessLogPage } from '@/api/infra/apiAccessLog'

export default {
  name: 'Apiaccesslog',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // API 访问日志列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeBeginTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        userId: null,
        userType: null,
        applicationName: null,
        requestUrl: null,
        duration: null,
        resultCode: null
      },
      // 表单参数
      form: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeBeginTime, 'beginTime')
      // 执行查询
      getApiAccessLogPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {}
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeBeginTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true
      this.form = row
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeBeginTime, 'beginTime')
      // 执行导出
      this.$modal.confirm('是否确认导出所有API 访问日志数据项?').then(() => {
        this.exportLoading = true
        return exportApiAccessLogExcel(params)
      }).then(response => {
        this.$download.excel(response, 'API 访问日志.xlsx')
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>
