
<template>
  <div style="padding: 15px 25px;overflow: hidden">
    <div style="text-align: right;height: 34px"   v-if="originContent">
      <span style="color: red">请仔细检查替换中文是否正确，检查无误后点击下一步，若生成存在错误请直接复制正确的文本到需要替换的内容</span>
      <el-button style="margin-left: 15px" type="primary" @click="copyReplace">下一步</el-button>
      <el-button
        key="originContent"
        v-clipboard:copy="originContent"
        v-clipboard:success="()=>{$modal.msgSuccess('复制成功')}"
      >
        复制
      </el-button>
    </div>
    <!--    <div style="color: red">仅限开发本地模式使用！！！仅限开发本地模式使用！！！仅限开发本地模式使用！！！</div>-->
    <div style="display: flex">
      <div class="content">
        <el-input
          ref="code"
          v-model="content"
          placeholder="输入你的代码"
          type="textarea"
          style="height: 100%"
        />
      </div>
      <div class="content" style="padding: 5px 15px">
        <pre ref="replacer">
          <code v-html="replacedContent" />
        </pre>
      </div>
    </div>
  </div>
</template>
<script>
import hljs from 'highlight.js/lib/highlight'
import 'highlight.js/styles/github-gist.css'
hljs.registerLanguage('javascript', require('highlight.js/lib/languages/javascript'))

export default {
  name: 'Index',
  data() {
    return {
      key: '',
      content: '',
      scrolling: false,
      originContent: ''

    }
  },
  computed: {
    replacedContent() {
      return this.init()
    }
  },
  mounted() {

  },
  methods: {
    init() {
      const searchIndex = this.content.search('<script>')
      // 截取script标签前的字段为template的内容
      const templateString = this.content.slice(0, searchIndex)
      // 之后的为script
      const scriptString = this.content.slice(searchIndex)

      const replacedTemplate = templateString
        .replaceAll(/[ ](\w*?)="(.*?[\u4e00-\u9fa5]+?.*?)"/g, ' :$1="\$t(\'$2\')"')
        .replaceAll(/>(.*?[\u4e00-\u9fa5]+?.*?)</g, '>{{ \$t(\'$1\') }}<')

      const replacedScript = scriptString.replaceAll(/(?<!t\()('.*?[\u4e00-\u9fa5]+?.*?')/g, 'this.\$t($1)')
      this.originContent = replacedTemplate + replacedScript
      const result = hljs.highlight('javascript', replacedTemplate + replacedScript || '', true)
      return result.value || '&nbsp;'
    },
    copyReplace() {
      this.$emit('copyReplace', this.originContent)
    }
  }
}
</script>

<style scoped lang="scss">
.content{
  flex: 0 0 50%;
  height: 300px;
}
::v-deep .el-textarea__inner{
  height: 100%;
}
pre{
  max-height: 300px;
}
</style>
