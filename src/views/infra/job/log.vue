<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="120px" size="small">
      <el-form-item label="处理器的名字" prop="handlerName">
        <el-input
          v-model="queryParams.handlerName"
          clearable
          placeholder="请输入处理器的名字"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开始执行时间" prop="beginTime">
        <el-date-picker
          v-model="queryParams.beginTime"
          clearable
          placeholder="选择开始执行时间"
          type="date"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="结束执行时间" prop="endTime">
        <el-date-picker
          v-model="queryParams.endTime"
          clearable
          placeholder="选择结束执行时间"
          type="date"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select v-model="queryParams.status" clearable placeholder="请选择任务状态">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.INFRA_JOB_LOG_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['infra:job:export']"
          icon="el-icon-download"
          size="mini"
          type="warning"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" label="日志编号" prop="id" />
      <el-table-column align="center" label="任务编号" prop="jobId" />
      <el-table-column align="center" label="处理器的名字" prop="handlerName" />
      <el-table-column align="center" label="处理器的参数" prop="handlerParam" />
      <el-table-column align="center" label="第几次执行" prop="executeIndex" />
      <el-table-column align="center" label="执行时间" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.beginTime) + ' ~ ' + parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="执行时长" prop="duration">
        <template slot-scope="scope">
          <span>{{ scope.row.duration + ' 毫秒' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="任务状态" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.INFRA_JOB_LOG_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['infra:job:query']"
            :loading="exportLoading"
            icon="el-icon-view"
            size="mini"
            type="text"
            @click="handleView(scope.row)"
          >详细
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 调度日志详细 -->
    <el-dialog :visible.sync="open" append-to-body title="调度日志详细" width="1280px">
      <el-form ref="form" :model="form" label-width="100px" size="mini">
        <el-form-item label="日志编号">{{ form.id }}</el-form-item>
        <el-form-item label="任务编号">{{ form.jobId }}</el-form-item>
        <el-form-item label="处理器的名字">{{ form.handlerName }}</el-form-item>
        <el-form-item label="处理器的参数">{{ form.handlerParam }}</el-form-item>
        <el-form-item label="第几次执行">{{ form.executeIndex }}</el-form-item>
        <el-form-item label="执行时间">{{ parseTime(form.beginTime) + ' ~ ' + parseTime(form.endTime) }}</el-form-item>
        <el-form-item label="执行时长">{{ parseTime(form.duration) + ' 毫秒' }}</el-form-item>
        <el-form-item label="任务状态">
          <dict-tag :type="DICT_TYPE.INFRA_JOB_LOG_STATUS" :value="form.status" />
        </el-form-item>
        <el-form-item label="执行结果">
          <el-input v-model="form.result" :autosize="{ maxRows: 20}" :readonly="true" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { exportJobLogExcel, getJobLogPage } from '@/api/infra/jobLog'

export default {
  name: 'Joblog',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 调度日志表格数据
      list: [],
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        handlerName: null,
        beginTime: null,
        endTime: null,
        status: null
      }
    }
  },
  created() {
    this.queryParams.jobId = this.$route.query && this.$route.query.jobId
    this.getList()
  },
  methods: {
    /** 查询调度日志列表 */
    getList() {
      this.loading = true
      getJobLogPage({
        ...this.queryParams,
        beginTime: this.queryParams.beginTime ? this.queryParams.beginTime + ' 00:00:00' : undefined,
        endTime: this.queryParams.endTime ? this.queryParams.endTime + ' 23:59:59' : undefined
      }).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      }
      )
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true
      this.form = row
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = {
        ...this.queryParams,
        beginTime: this.queryParams.beginTime ? this.queryParams.beginTime + ' 00:00:00' : undefined,
        endTime: this.queryParams.endTime ? this.queryParams.endTime + ' 23:59:59' : undefined
      }
      params.pageNo = undefined
      params.pageSize = undefined
      // 执行导出
      this.$modal.confirm('是否确认导出所有定时任务日志数据项?').then(() => {
        this.exportLoading = true
        return exportJobLogExcel(params)
      }).then(response => {
        this.$download.excel(response, '定时任务日志.xlsx')
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>
