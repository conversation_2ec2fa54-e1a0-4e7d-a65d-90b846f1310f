<template>
  <div v-loading="load" class="app-container">
    <doc-alert title="$t('替换文本中的中文为系统翻译中的代码')" url="http://localhost:81/system/tag" />

    <!-- 条件 -->
    <el-form ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="auto" :rules="rules">
      <el-row>
        <el-col :span="6">
          <el-form-item label="模块" prop="module">
            <el-select v-model="queryParams.module" placeholder="请选择模块" clearable>
              <el-option
                v-for="module in moduleList"
                :key="parseInt(module.id)"
                :label="module.name"
                :value="module.id+'_'+module.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="匹配正则" prop="regId">
            <el-select v-model="queryParams.regId" placeholder="请输入正则匹配中文">
              <el-option
                v-for="item in regRules"
                :key="item.id"
                :label="item.desc"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label=" " prop="">
            <el-button type="warning" icon="el-icon-refresh-right" @click="syncSystemTranslation">自动同步system_translation翻译</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <!--    替换前端i18n标签-->
      <replacer v-if="queryParams.regId === 2" @copyReplace="cpReplace" />
      <el-row>
        <el-col :span="12">
          <el-form-item label="需要替换的文本" label-width="auto">
            <el-input
              v-model="queryParams.sourceData"
              type="textarea"
              :rows="15"
              style="width:500px"
              placeholder="请输入内容"
              resize="none"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-button type="primary" icon="el-icon-search" @click="submitForm">替换</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label-width="auto" label="替换后的文本">
            <el-input v-model="processedData" type="textarea" :rows="15" style="width:500px" readonly resize="none" />
            <el-button
              key="processedData"
              v-clipboard:copy="processedData"
              v-clipboard:success="()=>{$modal.msgSuccess('复制成功')}"
              style="margin-left: 10px"
            >
              复制
            </el-button>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label-width="auto" label="参考SQL">
            <el-input v-model="sql" type="textarea" :rows="15" style="width:500px" readonly resize="none" />
            <el-button
              key="sql"
              v-clipboard:copy="sql"
              v-clipboard:success="()=>{$modal.msgSuccess('复制成功')}"
              style="margin-left: 10px"
            >
              复制
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
  </div>
</template>

<script>
import { getAllModule } from '@/api/system/module'
import { replaceI18n, syncSystemTranslation } from '@/api/infra/translationer'

export default {
  components: {
    replacer: () => import('../i18nReplacer/index.vue')
  },
  data() {
    return {
      queryParams: {
        module: null,
        regId: '',
        childReg: ''
      },
      regRules: [
        {
          id: 1,
          desc: '后端java文件导出、入类匹配规则',
          reg: '\\{(?!common\\.|supplier\\.|material\\.|avpl\\.|auth\\.|order\\.|system\\.|rfq\\.).*?\\}',
          childReg: '([\\{].*[\\}])',
          formatter: '{%s}'
        },
        {
          id: 2,
          desc: '前端vue文件匹配规则',
          reg: `\\$t\\(['|\\"](?!common\\.|supplier\\.|material\\.|avpl\\.|auth\\.|order\\.|system\\.|rfq\\.).*?['|\\"]\\)`,
          childReg: '\'.*\'|\".*\"',
          formatter: '\\$t(\'%s\')'
        },
        // reg: 仅匹配 "{ 文本内容带参数：{}，多个参数:{}}“ 的字符串。
        // formatter: "{%s}" 的双引号的含义是，reg通过双引号去匹配  ”{xxx{}}“ 文本格式。进提出和双引号组合的花括号，因此最后的生成需要把”“加上
        {
          id: 3,
          desc: '后端ErrorCode匹配规则',
          reg: '"\\{(?!common\\.|supplier\\.|material\\.|avpl\\.|auth\\.|order\\.|system\\.|rfq\\.).*?\\}"',
          childReg: '"([\\\\{].*[\\\\}])',
          formatter: '"{%s}"'
        }
      ],
      moduleList: [],
      sourceData: '',
      processedData: '',
      load: false,
      sql: '',
      rules: {
        module: [{ required: true, message: '请选择模块', trigger: 'change' }],
        regId: [{ required: true, message: '请输入正则表达式', trigger: 'change' }]
      }
    }
  },
  mounted() {
    getAllModule().then(res => {
      this.moduleList = res.data
      this.regRules[1].reg = `\\$t\\(['|\\"](?!${res.data.map(a => a.code).join('\\.|')}\\.).*?['|\\"]\\)`
    })
  },
  methods: {
    syncSystemTranslation() {
      this.$confirm('是否同步system_translation翻译', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        syncSystemTranslation().then(res => {
          this.$message({
            type: 'success',
            message: '同步成功'
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    submitForm() {
      this.$refs.queryForm.validate(valid => {
        if (!valid) {
          return
        }
        const params = { ...this.queryParams }
        if (params.regId) {
          params.childReg = this.regRules.filter(reg => reg.id === this.queryParams.regId)[0].childReg
          params.formatter = this.regRules.filter(reg => reg.id === this.queryParams.regId)[0].formatter
          params.matchRegular = this.regRules.filter(reg => reg.id === this.queryParams.regId)[0].reg
        }
        this.load = true
        replaceI18n(params).then(res => {
          this.processedData = res.data.processedData
          this.sql = res.data.sql
          this.load = false
        }).finally(() => {
          this.load = false
        })
      })
    },
    cpReplace(val) {
      this.queryParams.sourceData = val
      this.submitForm()
    }
  }
}
</script>
