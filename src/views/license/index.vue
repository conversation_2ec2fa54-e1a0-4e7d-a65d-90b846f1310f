<template>
  <div class="license">
    <common-card
      title="生成证书"
    >
      <div class="common-body">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="失效日期" prop="expiryTime">
            <el-date-picker
              v-model="form.expiryTime"
              style="width: 240px"
              type="date"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="私钥密码" prop="keyPass">
            <el-input v-model="form.keyPass" placeholder="请填写私钥密码" auto-complete="new-password" show-password style="width: 240px" />
          </el-form-item>
          <el-form-item label="私钥" prop="key">
            <el-button v-if="!isKeyExist" @click="handleAdd(true)">上传私钥</el-button>
            <el-button v-if="isKeyExist" @click="deleteLicenseFile">删除私钥</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="generateLicense">生成证书</el-button>
            <el-button @click="downloadLicenseFile">下载证书</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleAdd(false)">上传证书</el-button>
            <el-button @click="installLicenseFile">安装证书</el-button>
          </el-form-item>
          <el-form-item>
            当前证书有效期：{{ validityDate }}
          </el-form-item>
        </el-form>
      </div>
    </common-card>

    <!-- 上传文件 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".lic, .keystore"
        :auto-upload="false"
        drag
        :headers="upload.headers"
        :action="upload.url"
        :data="upload.data"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">
          将文件拖到此处，或 <em>点击上传</em>
        </div>
        <div slot="tip" class="el-upload__tip" style="color:red">提示：仅允许导入 lic、keystore 格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">取消</el-button>
        <el-button type="primary" @click="submitFileForm">确定</el-button>

      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  deleteLicenseFile,
  generateLicense,
  getLicensFile,
  getLicenseFileItem,
  installLicense, getLicensFileValidityDate
} from '@/api/license/license'
import { getAccessToken } from '@/utils/auth'

export default {
  name: 'Index',
  data() {
    return {
      form: {
        expiryTime: '',
        keyPass: ''
      },
      rules: {
        expiryTime: [{ required: true, message: '失效日期不能为空', trigger: 'blur' }],
        keyPass: [{ required: true, message: '私钥密码不能为空', trigger: 'blur' }],
        key: [{ required: true, message: '私钥密码不能为空', trigger: 'blur' }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        },
        shortcuts: [{
          text: '一年后',
          onClick(picker) {
            const date = new Date()
            date.setFullYear(date.getFullYear() + 1)
            picker.$emit('pick', date)
          }
        }, {
          text: '两年后',
          onClick(picker) {
            const date = new Date()
            date.setFullYear(date.getFullYear() + 2)
            picker.$emit('pick', date)
          }
        }, {
          text: '三年后',
          onClick(picker) {
            const date = new Date()
            date.setFullYear(date.getFullYear() + 3)
            picker.$emit('pick', date)
          }
        }, {
          text: '五年后',
          onClick(picker) {
            const date = new Date()
            date.setFullYear(date.getFullYear() + 5)
            picker.$emit('pick', date)
          }
        }, {
          text: '永久有效',
          onClick(picker) {
            const date = new Date()
            date.setFullYear(date.getFullYear() + 1000)
            picker.$emit('pick', date)
          }
        }]
      },
      upload: {
        open: false, // 是否显示弹出层
        title: '', // 弹出层标题
        isUploading: false, // 是否禁用上传
        url: process.env.VUE_APP_BASE_API + '/admin-api/license/upload-license-file', // 请求地址
        headers: { Authorization: 'Bearer ' + getAccessToken() } // 设置上传的请求头部
      },
      isKey: false,
      isKeyExist: false,
      validityDate: ''
    }
  },
  mounted() {
    this.getLicenseFileItem()
    this.getValidityDate()
  },
  methods: {
    /** 生成证书 */
    generateLicense() {
      generateLicense(this.form).then(response => {
        if (response.data) {
          this.$message.success(this.$t('生成成功'))
          this.$modal.confirm('生成成功，是否立即下载证书？').then(() => {
            this.downloadLicenseFile()
          })
        } else {
          this.$message.error(this.$t('生成失败'))
        }
      })
    },
    /** 下载证书 */
    downloadLicenseFile() {
      getLicensFile().then(resp => {
        // 将Base64字符串转换为字节数组
        const byteCharacters = atob(resp.data)
        const byteArrays = new Uint8Array(byteCharacters.length)
        for (let i = 0; i < byteCharacters.length; i++) {
          byteArrays[i] = byteCharacters.charCodeAt(i)
        }

        // 创建Blob对象和文件对象
        const blob = new Blob([byteArrays], { type: 'lic' })

        window.URL = window.URL || window.webkitURL
        const href = URL.createObjectURL(blob)
        const downA = document.createElement('a')
        downA.href = href
        downA.download = 'license.lic'
        downA.click()
        // 销毁超连接
        window.URL.revokeObjectURL(href)
      })
    },
    /** 安装证书 */
    installLicenseFile() {
      installLicense().then(resp => {
        if (resp.data.result) {
          this.$message.success(resp.data.message)
        } else {
          this.$message.error(this.$t('证书安装失败'))
        }
        this.getLicenseFileItem()
      })
    },
    /** 上传文件 */
    handleAdd(isKey) {
      this.isKey = isKey
      this.upload.open = true
      this.upload.title = '上传文件'
    },
    /** 处理文件上传中 */
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true // 禁止修改
    },
    /** 文件上传成功处理 */
    handleFileSuccess(response, file, fileList) {
      // 清理
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }

      this.getLicenseFileItem()

      if (!this.isKey) {
        // 如果时上传的证书，则上传成功后询问是否执行安装
        this.$modal.confirm('上传成功，是否立即安装证书？').then(() => {
          this.installLicenseFile()
        })
      } else {
        this.$message.success(this.$t('common.uploadSucceeded'))
      }
    },
    /** 发起文件上传 */
    submitFileForm() {
      this.$refs.upload.submit()
    },
    /** 获取上传文件 */
    getLicenseFileItem(fileName) {
      getLicenseFileItem(fileName).then(resp => {
        this.isKeyExist = resp.data
      })
    },
    /** 删除文件 */
    deleteLicenseFile() {
      deleteLicenseFile({ fileName: 'privateKeys.keystore' }).then(response => {
        if (response.data) {
          this.$message.success(this.$t('删除成功'))
          this.getLicenseFileItem()
        } else {
          this.$message.error(this.$t('删除失败'))
        }
      })
    },
    getValidityDate() {
      getLicensFileValidityDate().then(resp => {
        this.validityDate = resp.data.validityDate
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.license {
  padding: 15px 20px;
}
.common-body {
  padding: 0 100px;
}
</style>
