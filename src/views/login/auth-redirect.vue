<!--
 * @Author: your name
 * @Date: 2020-09-01 17:19:53
 * @LastEditTime: 2020-09-17 14:28:27
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ESIC_web\src\views\login\auth-redirect.vue
-->

<script>
export default {
  name: 'Authredirect',
  created() {
    const hash = window.location.search.slice(1)
    if (window.localStorage) {
      window.localStorage.setItem('x-admin-oauth-code', hash)
      window.close()
    }
  },
  render: function(h) {
    return h() // avoid warning message
  }
}
</script>
