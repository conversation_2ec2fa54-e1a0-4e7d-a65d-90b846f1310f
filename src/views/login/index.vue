<template>
  <div class="login" style="height:100%">

    <div class="login-container" :style="{'backgroundImage':(loginBgImage!=undefined&&loginBgImage!='')?'url('+loginBgImage+')':'../../assets/images/bg.jpg'}">

      <div class="login-background">
        <div style="position: fixed;top: 15px;display: flex;align-items: center;color: #ffffff;font-size: 16px">
          <el-image v-if="loginLogoImage!=undefined&&loginLogoImage!=''" :src="loginLogoImage" style="margin-left: 15px" />
          <svg-icon v-else icon-class="logo3" style="height: 68px;width: 198px" />
          <span style="padding: 0 21px;border-left: 1px solid #ffffff;color: black">
            {{ $t("common.loginMessage1") }}
          </span>
        </div>
        <div style="position: fixed;bottom: 15px;right:15px;align-items: center;color: #ffffff;font-size: 16px">
          <div style="color: black;font-size:16px ">Powered by <el-button type="text" @click="openCompanyIndex"> <span style="text-decoration: underline;font-size:16px">ESIC</span></el-button></div>
          <svg-icon v-if="language==='zh'" icon-class="logo3" style="height: 68px;width: 198px" />
          <svg-icon v-else icon-class="logo1" style="margin-left:47px;height: 92px;width: 125px" />
        </div>
      </div>
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        autocomplete="on"
        class="login-form"
        label-position="right"
        @submit.native.prevent
      >
        <div class="title-container">
          <h3 class="title">
            {{ $t("common.pleaseSignIn") }}
          </h3>
          <lang-select class="set-language" />
        </div>
        <el-form-item prop="username" style="margin-bottom: 12px">
          <span class="svg-container">
            <svg-icon icon-class="mail" style="font-size: 20px" />
          </span>
          <el-input
            ref="username"
            v-model="loginForm.username"
            :placeholder="$t('common.userAccount')"
            autocomplete="on"
            name="username"
            tabindex="1"
            type="text"
          />
        </el-form-item>

        <el-tooltip v-model="capsTooltip" content="Caps lock is On" manual placement="right">
          <el-form-item prop="password" style="margin-bottom: 12px">
            <span class="svg-container">
              <svg-icon icon-class="lockLogin" style="font-size: 20px" />
            </span>
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              :placeholder="$t('common.password')"
              :type="passwordType"
              autocomplete="on"
              name="password"
              tabindex="2"
              @blur="capsTooltip = false"
              @keyup.native="checkCapslock"
            />
            <span class="show-pwd" @click="showPwd">
              <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
            </span>
          </el-form-item>
        </el-tooltip>
        <div>
          <verify
            :key="$t('common.swipeRightToUnlock')"
            :bar-size="{ width:' 100%',height:'46px'}"
            :explain="$t('common.swipeRightToUnlock')"
            :msg="$t('common.looseValidation')"
            :msg-success="$t('common.verificationSucceeded')"
            :show-button="false"
            class="verify"
            type="3"
            @error="onVerifyError"
            @success="onVerifySuccess"
          />
        </div>

        <el-button
          v-if="esicFlag"
          :disabled="disabledFlag"
          :loading="loading"
          size="small"
          style="background:#4996B8;color:#ffffff;width: 100%;font-size:14px; height:41px;
                 box-shadow: 0px 12px 34px 0px rgba(130,184,211,0.74);
                  margin: 34px 0"
          type="primary"
          @click.native.prevent="handleLogin"
        >
          {{ $t("common.logIn") }}
        </el-button>
        <div>
          <!--  社交登录 -->
          <el-row style="width:100%;">
            <div class="oauth-login" style="display: flex;justify-content: space-between;">
              <div v-for="item in SysUserSocialTypeEnum" v-if="socialSwitch" :key="item.type" class="oauth-login-item" @click="toSocialLogin(item)">
                <img
                  :src="item.img"
                  height="20px"
                  width="20px"
                  :alt="$t('common.logIn')"
                >
                <span style="margin-left: 5px">{{ item.title }}</span>
              </div>
              <div class="oauth-login-item">
                <el-button style="color:#2E6F88;font-size:14px" type="text" @click="$router.push('/register')">
                  {{ $t("common.register") }}
                </el-button>
                <el-button style="color:#2E6F88;font-size:14px" type="text" @click="$router.push('/updatePassword')">
                  {{ $t('common.changePassword') }}
                </el-button>
              </div>
              <div class="oauth-login-item">
                <el-button style="color:#2E6F88;font-size:14px" type="text" @click="$router.push('/forgetPassWord')">
                  {{ $t("common.forgetPassword") }}
                </el-button>
              </div>
            </div>
          </el-row>
          <el-row>
            <!--
              <el-col :span="8" style="line-height: 35px;height:35px">
                <el-checkbox v-model="checkPass"> {{ $t('common.logincheckpass') }}</el-checkbox>
              </el-col>
              -->
            <!--            <el-col :span="10" style="text-align: right;padding-right: 43px;">-->
            <!--              <el-button style="color:#2E6F88;font-size:14px" type="text" @click="$router.push('/register')">-->
            <!--                {{ $t("common.register") }}-->
            <!--              </el-button>-->
            <!--            </el-col>-->
            <!--            <el-col :span="14" style="text-align: left;padding-left: 53px;border-left: 1px solid #DCDFE6">-->
            <!--              <el-button style="color:#2E6F88;font-size:14px" type="text" @click="$router.push('/forgetPassWord')">-->
            <!--                {{ $t("common.forgetPassword") }}?-->
            <!--              </el-button>-->
            <!--            </el-col>-->

          </el-row>
        </div>

        <!--
      <div style="position:relative">
        <div class="tips">
          <span>{{ $t('common.loginusername') }} : admin</span>
          <span>{{ $t('common.loginpassword') }} : {{ $t('common.loginany') }}</span>
        </div>
        <div class="tips">
          <span style="margin-right:18px;">
            {{ $t('common.loginusername') }} : editor
          </span>
          <span>{{ $t('common.loginpassword') }} : {{ $t('common.loginany') }}</span>
        </div>

        <el-button class="thirdparty-button" type="primary" @click="showDialog=true">
          {{ $t('common.loginthirdparty') }}
        </el-button>
      </div>
      -->

      </el-form>

    </div>
    <el-dialog
      v-if="dialogVisible"
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="dialogVisible"
      class="forgertP"
      :title="$t('common.forgetPassword')"
      width="520px"
      @close="callback()"
    >
      <!--    <forget-pass @callback='callback'></forget-pass>-->
    </el-dialog>
    <el-dialog
      v-if="codeVisible"
      :title="$t('common.scanCodeToLogIn')"
      width="470px"
      :visible.sync="codeVisible"
      :before-close="closeCode"
    >
      <div style="justify-content: center;display: flex">
        <div style="width: 250px;height: 250px;position:relative;">
          <img v-if="codeUrl" alt="" :src="codeUrl" style="width: 250px;height: 250px">
          <el-skeleton v-else>
            <template slot="template">
              <div style="display: flex;justify-content: center;">
                <el-skeleton-item variant="image" style="width: 240px; height: 240px;" />
              </div>
            </template>
          </el-skeleton>
          <div v-show="!codeExpire" class="invalid-code">
            <span>{{ $t('common.theQrCodeHasExpired') }}</span>
            <el-button type="text" @click="toSocialLogin">{{ $t('common.reload') }}</el-button>
          </div>
        </div>

      </div>
      <div style="text-align: center">
        <el-button style="margin-top: 15px" type="primary" @click="closeCode">{{ $t('order.close') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { validUsername } from '@/utils/validate'
import Verify from 'vue2-verify'

import LangSelect from '@/components/LangSelect'
import Cookies from 'js-cookie'
import {
  getPassword,
  getRememberMe,
  getTenantName,
  getUsername,
  removePassword,
  removeRememberMe,
  removeTenantName,
  removeUsername,
  setPassword,
  setRememberMe,
  setTenantName, setToken,
  setUsername
} from '@/utils/auth'
import { getConfigKey } from '@/api/infra/config'
import { SystemUserSocialTypeEnum } from '@/utils/constants'
import { checkWxBind, checkWxLogin, getWxMpBindCode, getWxMpCode, socialAuthRedirect } from '@/api/login'
import { generateUUID } from '@/utils/ruoyi'

export default {
  name: 'Login',
  components: {
    LangSelect,
    Verify
    // forgetPass
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error('Please enter the correct user name'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('The password can not be less than 6 digits'))
      } else {
        callback()
      }
    }
    return {
      msgSuccess: '',
      msg: '',
      esicFlag: true,
      loginForm: {
        loginType: 'uname',
        username: '',
        password: '',
        mobile: '',
        mobileCode: '',
        rememberMe: false,
        code: '',
        uuid: ''
      },
      loginRules: {
        userName: [{
          required: true,
          trigger: 'blur',
          validator: validateUsername
        }],
        passWord: [{
          required: true,
          trigger: 'blur',
          validator: validatePassword
        }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
      checkPass: false,
      disabledFlag: true,
      verfily: {},
      dialogVisible: false,
      loginBgImage: undefined,
      loginLogoImage: undefined,
      SysUserSocialTypeEnum: SystemUserSocialTypeEnum,

      codeVisible: false,
      codeUrl: null,
      timer: null,
      socialSwitch: process.env.VUE_APP_SOCIAL_LOGIN === 'true',
      codeExpire: true,
      timeOut: null,
      loginUUid: ''
    }
  },
  computed: {
    language() {
      return this.$store.getters.language
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {
    Cookies.set('size', 'small')
    // window.addEventListener('storage', this.afterQRScan)
  },
  mounted() {
    this.getCookie()
    this.getLoginImage()
    if (this.loginForm.userName === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.Password === '') {
      this.$refs.password.focus()
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  beforeDestroy() {
    clearInterval(this.timer)
    clearTimeout(this.timeOut)
  },
  methods: {
    openCompanyIndex() {
      window.open('http://www.esicint.com')
    },
    getLoginImage() {
      getConfigKey('login.bg.image').then(response => {
        this.loginBgImage = response.data
      })
      getConfigKey('login.logo.image').then(response => {
        this.loginLogoImage = response.data
      })
    },
    getCookie() {
      const username = getUsername()
      const password = getPassword()
      const rememberMe = getRememberMe()
      const tenantName = getTenantName()
      this.loginForm = {
        ...this.loginForm,
        username: username || this.loginForm.username,
        password: password || this.loginForm.password,
        rememberMe: rememberMe ? getRememberMe() : false,
        tenantName: tenantName || this.loginForm.tenantName
      }
    },
    callback() {
      this.dialogVisible = false
    },
    openForget() {
      this.dialogVisible = true
    },
    onVerifySuccess(obj) {
      // console.log(data)
      console.log('verify success', obj)
      this.$message({
        message: this.$t('common.verificationSucceeded'),
        type: 'success'
      })
      this.disabledFlag = false
      this.verfily = obj
    },
    onVerifyError(obj) {
      console.log('verify error', obj)
      // 错误刷新验证码
      this.$message({
        message: this.$t('common.verificationFailed'),
        type: 'error'
      })
      this.disabledFlag = true
      obj.refresh()
      this.verfily = obj
    },
    checkCapslock(e) {
      const {
        key
      } = e
      this.capsTooltip = key && key.length === 1 && key >= 'A' && key <= 'Z'
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          // 设置 Cookie
          if (this.loginForm.rememberMe) {
            setUsername(this.loginForm.username)
            setPassword(this.loginForm.password)
            setRememberMe(this.loginForm.rememberMe)
            setTenantName(this.loginForm.tenantName)
          } else {
            removeUsername()
            removePassword()
            removeRememberMe()
            removeTenantName()
          }
          // 发起登陆
          // console.log("发起登录", this.loginForm);
          this.$store.dispatch(this.loginForm.loginType === 'sms' ? 'SmsLogin' : 'Login', this.loginForm).then(() => {
            this.$notify.closeAll()
            this.$router.push({ path: this.redirect || '/', query: this.otherQuery }).catch(() => {
            })
          }).catch(() => {
            this.loading = false
            // this.getCode();
          })
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
    doSocialLogin(socialTypeEnum) {
      // 设置登录中
      this.loading = true
      // 计算 redirectUri
      const redirectUri = location.origin + '/social-login?type=' + socialTypeEnum.type + '&redirect=' + (this.redirect || '/') // 重定向不能丢
      // const redirectUri = 'http://127.0.0.1:48080/api/gitee/callback';
      // const redirectUri = 'http://127.0.0.1:48080/api/dingtalk/callback';
      // 进行跳转
      socialAuthRedirect(socialTypeEnum.type, encodeURIComponent(redirectUri)).then((res) => {
        // socialAuthRedirect(socialTypeEnum.type, encodeURIComponent('http://route.zyf.cool:1234')).then((res) => {
        // console.log(res.url);
        window.location.href = res.data
      })
    },
    toSocialLogin(item) {
      this.codeVisible = true
      this.loginUUid = generateUUID()

      getWxMpCode(this.loginUUid).then(res => {
        this.codeUrl = res.data
        this.codeExpire = true
        this.setCodeExpire()

        this.timer = setInterval(() => {
          this.checkLogin()
        }, 1000)
      })
    },
    checkLogin() {
      checkWxLogin(this.loginUUid).then(res => {
        if (res.data?.userId) {
          this.closeCode()
          setToken(res.data)
          this.$router.push('/')
        } else if (res.data) {
          this.$message.error(res.data)
          this.closeCode()
        }
      })
    },
    closeCode() {
      clearInterval(this.timer)
      clearTimeout(this.timeOut)
      this.codeVisible = false
    },
    setCodeExpire() {
      this.timeOut = setTimeout(() => {
        this.codeExpire = false
        clearInterval(this.timer)
        clearTimeout(this.timeOut)
      }, 60000)
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #283443;
$light_gray: #fff;
$cursor: #4d4747;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {

    color: $cursor

  }

}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 46px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 0;
      margin-left: 15px;
      color: black !important;
      height: 46px;
      caret-color: $cursor;
      font-size: 12px;

      &:-webkit-autofill {
        box-shadow: 0 0 0 100px #ffffff inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid #DCDFE6;
    //background: rgba(0, 0, 0, 0.1);
    background: #FFFFFF;
    border-radius: 5px;
    color: #454545;
  }

}
</style>
<style lang="scss" scoped>

$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;
.invalid-code{
  top: 0;
  text-align: center;
  position: absolute;
  width: 250px;
  height: 250px;
  background: rgba(255,255,255,0.95);
  line-height: 250px;
}

.oauth-login {
  display: flex;
  align-items: center;
  cursor:pointer;
}
.oauth-login-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  color: #2e6f88;
}
.oauth-login-item img {
  height: 23px;
  width: 23px;
}
.oauth-login-item span:hover {
  text-decoration: underline;
}
.register-btn {
  float: right;
  padding: 10px;
}

::v-deep .icon-right:before {
  z-index: 2
}

::v-deep .icon-check:before {
  z-index: 2
}

.login-background {
  text-align: center;
  padding: 15px 0;
  width: calc(87.3% - 549px);
  min-height: 100%;
}

.login-container {
  min-height: 100%;
  width: 100%;
  // background-color: $bg;
  //background: linear-gradient(45deg, #0073b1, #0c8996);
  background-image: url("../../assets/images/bg.jpg");
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .login-form {
    position: relative;
    width: 400px;
    max-width: 100%;
    padding: 37px 46px 42px 46px;
    margin-right: 12.7%;
    overflow: hidden;
    background: #F5F7FA;
    border-radius: 11px;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-family: "Helvetica Bold", sans-serif;
      font-size: 30px;
      // color: $light_gray;
      margin: 0 auto 32px auto;
      text-align: left;
      color: #2E6F88;
      font-weight: bold;
    }

    .set-language {
      color: #fff;
      position: absolute;
      top: 8px;
      font-size: 20px;
      right: 0px;
      cursor: pointer;

      ::v-deep .el-dropdown-selfdefine {
        color: #2E6F88;
      }
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }

  .verify {
    ::v-deep .verify-bar-area {
      border: none;
      color: #909399;
    }

    ::v-deep .verify-left-bar {
      border: none;
    }

    ::v-deep .verify-move-block {
      border-radius: 5px;
      border: none;
      box-shadow: 1px 1px 5px 0px rgba(41, 119, 150, 0.54);

      .verify-icon:before {
        width: 20px;
        height: 20px;
        background-image: url(data:image/svg+xml;base64,PHN2ZyBpZD0i5Zu+5bGCXzEiIGRhdGEtbmFtZT0i5Zu+5bGCIDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDIwMCAyMDAiPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDojODBBQ0MzO308L3N0eWxlPjwvZGVmcz48dGl0bGU+5Zu+5qCHPC90aXRsZT48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0xMDMuMywxMDkuMjFhMTAuNDgsMTAuNDgsMCwwLDAsMC0xNC44MmwtNjAtNjAtMTAsMTAsNTcuNCw1Ny40Mi01Ny40LDU3LjMxLDEwLDEwLjA2Wm02Mi44MywwYTEwLjYyLDEwLjYyLDAsMCwwLC4wNS0xNUwxMDcuMTEsMzQuNDNsLTEwLjkxLDEwLDU3LjQsNTcuNDItNTcuNCw1Ny4zMSwxMCwxMC4wNloiLz48L3N2Zz4K)

      }

    }

    ::v-deep .verify-move-block:hover {
      background: #ffffff !important;
    }

    ::v-deep .verify-msg {
      font-size: 14px;
    }
  }

}
</style>

<style>

.forgertP .el-dialog__header {
  padding: 10px;
  text-align: center;
  background: #F1F1F1
}

.forgertP .el-dialog__body {
  background: #F1F1F1;
  padding: 0px 20px 30px
}

</style>
