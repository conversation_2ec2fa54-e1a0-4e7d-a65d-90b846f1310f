<template>
  <div class="login" style="height:100%">

    <div
      class="login-container"
      :style="{'backgroundImage':(loginBgImage!=undefined&&loginBgImage!='')?'url('+loginBgImage+')':'../../assets/images/bg.jpg'}"
    >

      <div class="login-background">
        <div style="position: fixed;top: 15px;display: flex;align-items: center;color: #ffffff;font-size: 16px">
          <el-image
            v-if="loginLogoImage!=undefined&&loginLogoImage!=''"
            :src="loginLogoImage"
            style="margin-left: 15px"
          />
          <svg-icon v-else icon-class="logo3" style="height: 68px;width: 198px" />
          <span style="padding: 0 21px;border-left: 1px solid #ffffff;color: black">
            {{ $t('common.loginMessage1') }}
          </span>
        </div>
        <div style="position: fixed;bottom: 15px;right:15px;align-items: center;color: #ffffff;font-size: 16px">
          <div style="color: black;font-size:16px ">Powered by
            <el-button type="text" @click="window.open('http://www.esicint.com')"><span
              style="text-decoration: underline;font-size:16px"
            >ESIC</span></el-button>
          </div>
          <svg-icon v-if="language==='zh'" icon-class="logo3" style="height: 68px;width: 198px" />
          <svg-icon v-else icon-class="logo1" style="margin-left:47px;height: 92px;width: 125px" />
        </div>
      </div>
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        label-position="right"
        @submit.native.prevent
      >
        <div class="title-container">
          <h3 class="title">
            {{ $t('common.changePassword') }}
          </h3>
          <lang-select class="set-language" />
        </div>
        <el-form-item
          prop="username"
          style="margin-bottom: 20px"
          :label="$t('common.userAccount')"
        >
          <el-input
            ref="username"
            v-model="loginForm.username"
            :placeholder="$t('common.pleaseEnter')"
            name="username"
            tabindex="1"
            type="text"
          />
        </el-form-item>
        <el-form-item
          prop="password"
          style="margin-bottom: 20px"
          :label="$t('common.originalPassword')"
        >
          <el-input
            :key="passwordType[0]"
            ref="password0"
            v-model="loginForm.password"
            :placeholder="$t('common.pleaseEnter')"
            :type="passwordType[0]"
            tabindex="2"
            @blur="capsTooltip = false"
          />
          <span class="show-pwd" @click="showPwd(0)">
            <svg-icon :icon-class="passwordType[0] === 'password' ? 'eye' : 'eye-open'" />
          </span>
        </el-form-item>

        <el-tooltip v-model="capsTooltip" content="Caps lock is On" manual placement="right">
          <el-form-item
            prop="passwordNew"
            style="margin-bottom: 20px"
            :label="$t('system.newPassword')"
          >
            <el-input
              :key="passwordType[1]"
              ref="password1"
              v-model="loginForm.passwordNew"
              :placeholder="$t('common.pleaseEnter')"
              :type="passwordType[1]"
              tabindex="3"
              @blur="capsTooltip = false"
              @keyup.native="checkCapslock"
            />
            <span class="show-pwd" @click="showPwd(1)">
              <svg-icon :icon-class="passwordType[1] === 'password' ? 'eye' : 'eye-open'" />
            </span>
          </el-form-item>
        </el-tooltip>
        <el-form-item
          prop="passwordNewAgain"
          style="margin-bottom: 20px"
          :label="$t('common.confirmPassword')"
        >
          <el-input
            :key="passwordType[2]"
            ref="password2"
            v-model="loginForm.passwordNewAgain"
            :placeholder="$t('common.pleaseEnter')"
            :type="passwordType[2]"
            tabindex="4"
            @blur="capsTooltip = false"
          />
          <span class="show-pwd" @click="showPwd(2)">
            <svg-icon :icon-class="passwordType[2] === 'password' ? 'eye' : 'eye-open'" />
          </span>
        </el-form-item>
        <div>
          <verify
            :key="$t('common.swipeRightToUnlock')"
            :bar-size="{ width:' 100%',height:'46px'}"
            :explain="$t('common.swipeRightToUnlock')"
            :msg="$t('common.looseValidation')"
            :msg-success="$t('common.verificationSucceeded')"
            :show-button="false"
            class="verify"
            type="3"
            @error="onVerifyError"
            @success="onVerifySuccess"
          />
        </div>

        <el-button
          :disabled="disabledFlag"
          :loading="loading"
          size="small"
          style="background:#4996B8;color:#ffffff;width: 100%;font-size:14px; height:41px;
                 box-shadow: 0px 12px 34px 0px rgba(130,184,211,0.74);
                  margin: 34px 0"
          type="primary"
          @click.native.prevent="handleLogin"
        >
          {{ $t('common.submit') }}
        </el-button>
      </el-form>

    </div>
  </div>
</template>

<script>
import { validUsername } from '@/utils/validate'
import Verify from 'vue2-verify'

import LangSelect from '@/components/LangSelect'
import { getConfigKey } from '@/api/infra/config'
import { updatePasswordWithExpired } from '@/api/login'

export default {
  name: 'UpdatePassword',
  components: {
    LangSelect,
    Verify
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error('Please enter the correct user name'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 4 || value.length > 16) {
        callback(new Error(this.$t('common.passwordLengthIsDigits')))
      } else {
        callback()
      }
    }
    const validatePasswordNewAgain = (rule, value, callback) => {
      if (value.length < 4 || value.length > 16) {
        callback(new Error(this.$t('common.passwordLengthIsDigits')))
      } else {
        if (this.loginForm.passwordNew && this.loginForm.passwordNewAgain && this.loginForm.passwordNew !== this.loginForm.passwordNewAgain) {
          callback(new Error(this.$t('common.thePasswordsEnteredTwiceAreInconsistent')))
        } else {
          callback()
        }
      }
    }
    return {
      msgSuccess: '',
      msg: '',
      loginForm: {
        loginType: 'uname',
        username: '',
        password: '',
        passwordNew: '',
        passwordNewAgain: ''
      },
      loginRules: {
        userName: [{
          required: true,
          trigger: 'blur',
          validator: validateUsername
        }],
        password: [{
          required: true,
          trigger: 'blur',
          validator: validatePassword
        }],
        passwordNew: [{
          required: true,
          trigger: 'blur',
          validator: validatePasswordNewAgain
        }],
        passwordNewAgain: [{
          required: true,
          trigger: 'blur',
          validator: validatePasswordNewAgain
        }]
      },
      passwordType: ['password', 'password', 'password'],
      capsTooltip: false,
      loading: false,
      disabledFlag: true,
      verfily: {},
      loginBgImage: undefined,
      loginLogoImage: undefined
    }
  },
  computed: {
    language() {
      return this.$store.getters.language
    }
  },
  mounted() {
    this.getLoginImage()
  },
  methods: {
    getLoginImage() {
      getConfigKey('login.bg.image').then(response => {
        this.loginBgImage = response.data
      })
      getConfigKey('login.logo.image').then(response => {
        this.loginLogoImage = response.data
      })
    },
    onVerifySuccess(obj) {
      this.$message({
        message: this.$t('common.verificationSucceeded'),
        type: 'success'
      })
      this.disabledFlag = false
      this.verfily = obj
    },
    onVerifyError(obj) {
      this.$message({
        message: this.$t('common.verificationFailed'),
        type: 'error'
      })
      this.disabledFlag = true
      obj.refresh()
      this.verfily = obj
    },
    checkCapslock({ key }) {
      this.capsTooltip = key && key.length === 1 && key >= 'A' && key <= 'Z'
    },
    showPwd(index) {
      if (this.passwordType[index] === 'password') {
        this.passwordType[index] = ''
      } else {
        this.passwordType[index] = 'password'
      }
      this.passwordType = [...this.passwordType]
      this.$nextTick(() => {
        this.$refs['password' + index].focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          updatePasswordWithExpired(this.loginForm).then(res => {
            if (res.data) {
              this.$message({
                message: this.$t('common.updateSuccessful'),
                type: 'success'
              })
              setTimeout(() => {
                this.$router.push({ path: '/login' })
              }, 1000)
            }
          }).catch(error => {
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #283443;
$light_gray: #fff;
$cursor: #4d4747;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {

    color: $cursor

  }

}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 46px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 0;
      margin-left: 15px;
      color: black !important;
      height: 46px;
      caret-color: $cursor;
      font-size: 12px;

      &:-webkit-autofill {
        box-shadow: 0 0 0 100px #ffffff inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid #DCDFE6;
    //background: rgba(0, 0, 0, 0.1);
    background: #FFFFFF;
    border-radius: 5px;
    color: #454545;
  }

}
</style>
<style lang="scss" scoped>

$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;
.invalid-code {
  top: 0;
  text-align: center;
  position: absolute;
  width: 250px;
  height: 250px;
  background: rgba(255, 255, 255, 0.95);
  line-height: 250px;
}

.oauth-login {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.oauth-login-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  color: #2e6f88;
}

.oauth-login-item img {
  height: 23px;
  width: 23px;
}

.oauth-login-item span:hover {
  text-decoration: underline;
}

.register-btn {
  float: right;
  padding: 10px;
}

::v-deep .icon-right:before {
  z-index: 2
}

::v-deep .icon-check:before {
  z-index: 2
}

.login-background {
  text-align: center;
  padding: 15px 0;
  width: calc(87.3% - 549px);
  min-height: 100%;
}

.login-container {
  min-height: 100%;
  width: 100%;
  // background-color: $bg;
  //background: linear-gradient(45deg, #0073b1, #0c8996);
  background-image: url("../../assets/images/bg.jpg");
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .login-form {
    position: relative;
    width: 400px;
    max-width: 100%;
    padding: 37px 46px 42px 46px;
    margin-right: 12.7%;
    overflow: hidden;
    background: #F5F7FA;
    border-radius: 11px;

    ::v-deep .el-form-item__label {
      margin-left: 13px;
    }
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-family: "Helvetica Bold", sans-serif;
      font-size: 30px;
      // color: $light_gray;
      margin: 0 auto 32px auto;
      text-align: left;
      color: #2E6F88;
      font-weight: bold;
    }

    .set-language {
      color: #fff;
      position: absolute;
      top: 8px;
      font-size: 20px;
      right: 0px;
      cursor: pointer;

      ::v-deep .el-dropdown-selfdefine {
        color: #2E6F88;
      }
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 40px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }

  .verify {
    ::v-deep .verify-bar-area {
      border: none;
      color: #909399;
    }

    ::v-deep .verify-left-bar {
      border: none;
    }

    ::v-deep .verify-move-block {
      border-radius: 5px;
      border: none;
      box-shadow: 1px 1px 5px 0px rgba(41, 119, 150, 0.54);

      .verify-icon:before {
        width: 20px;
        height: 20px;
        background-image: url(data:image/svg+xml;base64,PHN2ZyBpZD0i5Zu+5bGCXzEiIGRhdGEtbmFtZT0i5Zu+5bGCIDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDIwMCAyMDAiPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDojODBBQ0MzO308L3N0eWxlPjwvZGVmcz48dGl0bGU+5Zu+5qCHPC90aXRsZT48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0xMDMuMywxMDkuMjFhMTAuNDgsMTAuNDgsMCwwLDAsMC0xNC44MmwtNjAtNjAtMTAsMTAsNTcuNCw1Ny40Mi01Ny40LDU3LjMxLDEwLDEwLjA2Wm02Mi44MywwYTEwLjYyLDEwLjYyLDAsMCwwLC4wNS0xNUwxMDcuMTEsMzQuNDNsLTEwLjkxLDEwLDU3LjQsNTcuNDItNTcuNCw1Ny4zMSwxMCwxMC4wNloiLz48L3N2Zz4K)

      }

    }

    ::v-deep .verify-move-block:hover {
      background: #ffffff !important;
    }

    ::v-deep .verify-msg {
      font-size: 14px;
    }
  }

}
</style>

<style>

.forgertP .el-dialog__header {
  padding: 10px;
  text-align: center;
  background: #F1F1F1
}

.forgertP .el-dialog__body {
  background: #F1F1F1;
  padding: 0px 20px 30px
}

</style>
