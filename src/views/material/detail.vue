<template>
  <div v-disable-all="viewOnly" style="padding: 25px 15px">
    <common-card
      v-show="createFlag"
      :title="$t('material.generalInformation')"
      class="materialCommon"
    >
      <!--      <div slot="header" class="mainTab">-->
      <!--        {{ $t('material.generalInformation') }}-->
      <!--      </div>-->
      <div style="display: flex;justify-content: center;margin-bottom: 30px">

        <el-form ref="commonInfoConcise" style="width: 50%" label-width="203px" :model="commonInfo" size="mini">
          <el-form-item
            :rules=" { required: true, message: $t('material.pleaseSelectAFactory'), trigger: 'change' }"
            prop="factory"

            :label="$t('material.factory')"
          >
            <el-select v-model="commonInfo.factory" :disabled="viewOnly" style="width: 100%;" multiple clearable>
              <el-option v-for="item in factoryList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>

          <el-form-item
            :rules=" { required: true, message: $t('material.pleaseEnterTheMaterialDescription'), trigger: 'blur' }"

            prop="materialDescription"
            :label="$t('material.materialDescription')"
          >
            <el-input v-model="commonInfo.materialDescription" :placeholder="$t('common.pleaseEnter')" />

          </el-form-item>
          <el-form-item :label="$t('material.specificationAndModel')">
            <el-input v-model="commonInfo.specifications" :placeholder="$t('common.pleaseEnter')" />

          </el-form-item>
          <el-form-item :label="$t('material.materialCode')">
            <el-input v-model="commonInfo.materialCode" :placeholder="$t('common.pleaseEnter')" />
          </el-form-item>
          <el-form-item :label="$t('material.category')">
            <el-cascader
              v-model="commonInfo.category"
              filterable
              clearable
              style="width: 100%"
              :props="{ value: 'id',label:'name'}"
              :options="categoryList"
              :disabled="viewOnly"
              :placeholder="$t('material.pleaseSelectCategory')"
            />

          </el-form-item>

          <el-form-item :label="$t('material.selfMadeOrPurchased')">
            <el-select v-model="commonInfo.materialType" :disabled="viewOnly" :placeholder="$t('material.selfMadePurchased')" style="width: 100%">
              <el-option
                v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_MAKE_OR_BUY)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('material.basicUnit')">
            <el-select v-model="commonInfo.basicUnit" :disabled="viewOnly" filterable style="width: 100%">
              <el-option
                v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <!--          <el-form-item :label="$t('material.purchasingUnit')">-->
          <!--            <el-select v-model="commonInfo.purchasingUnit" :disabled="viewOnly" filterable style="width: 100%">-->
          <!--              <el-option-->
          <!--                v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"-->
          <!--                :key="dict.value"-->
          <!--                :label="dict.label"-->
          <!--                :value="dict.value"-->
          <!--              />-->
          <!--            </el-select>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item :label="$t('material.revision')">-->
          <!--            <el-input v-model="commonInfo.version" :placeholder="$t('material.pleaseEnterTheVersion')" />-->

          <!--          </el-form-item>-->

          <!--          <el-form-item :label="$t('material.drawingVersion')">-->
          <!--            <el-input v-model="commonInfo.drawingVersion" :placeholder="$t('material.pleaseEnterTheDrawingVersion')" />-->

          <!--          </el-form-item>-->

          <!--          <el-form-item :label="$t('material.procurementGroup')">-->
          <!--            <el-select v-model="commonInfo.pgId" :disabled="viewOnly" filterable style="width: 100%">-->
          <!--              <el-option-->
          <!--                v-for="dict in getMaterialDict(DICT_TYPE.SYSTEM_PURCHASE_GROUP)"-->
          <!--                :key="dict.value"-->
          <!--                :label="dict.label"-->
          <!--                :value="dict.value"-->
          <!--              />-->
          <!--            </el-select>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item :label="$t('material.lengthXWidthXHeightmm')">-->
          <!--            <div style="display: flex">-->
          <!--              <el-input v-model.number="commonInfo.length" type="number" :placeholder="$t('material.pleaseEnterTheLength')" />-->
          <!--              <el-input v-model.number="commonInfo.width" type="number" :placeholder="$t('material.pleaseEnterTheWidth')" />-->
          <!--              <el-input v-model.number="commonInfo.height" type="number" :placeholder="$t('material.pleaseEnterTheHeight')" />-->
          <!--            </div>-->

          <!--          </el-form-item>-->

          <!--          <el-form-item :label="$t('material.customerCode')">-->
          <!--            <el-input v-model="commonInfo.customerCode" :placeholder="$t('common.pleaseEnter')" />-->

          <!--          </el-form-item>-->
          <!--          <el-form-item :label="$t('material.purchaseType')">-->
          <!--            <el-select v-model="commonInfo.purchaseType" :disabled="viewOnly" filterable style="width: 100%">-->
          <!--              <el-option-->
          <!--                v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_PURCHASE_TYPE)"-->
          <!--                :key="dict.value"-->
          <!--                :label="dict.label"-->
          <!--                :value="dict.value"-->
          <!--              />-->
          <!--            </el-select>-->

          <!--          </el-form-item>-->
          <!--          <el-form-item :label="$t('material.standardCost')">-->
          <!--            <div style="display: flex">-->
          <!--              <el-input v-model.number="commonInfo.standardCost" type="number" style="flex: 1 0 75%" />-->
          <!--              <el-select v-model="commonInfo.standardCostCurrency" :disabled="viewOnly" filterable style="width: 100%">-->
          <!--                <el-option-->
          <!--                  v-for="dict in getMaterialDict('currency')"-->
          <!--                  :key="dict.id"-->
          <!--                  :label="dict.name"-->
          <!--                  :value="dict.id"-->
          <!--                />-->
          <!--              </el-select>-->
          <!--            </div>-->

          <!--          </el-form-item>-->

          <!--          <el-form-item :label="$t('material.materialRemarks')">-->
          <!--            <el-input v-model="commonInfo.remarks" :placeholder="$t('common.pleaseEnter')" />-->
          <!--          </el-form-item>-->
          <!--          <el-form-item :label="$t('material.extendedField1')">-->
          <!--            <el-input v-model="commonInfo.extendField1" :placeholder="$t('common.pleaseEnter')" />-->

          <!--          </el-form-item>-->
          <!--          <el-form-item :label="$t('material.extendedField2')">-->
          <!--            <el-input v-model="commonInfo.extendField2" :placeholder="$t('common.pleaseEnter')" />-->

          <!--          </el-form-item>-->
          <!--          <el-form-item :label="$t('material.extendedField3')">-->
          <!--            <el-input v-model="commonInfo.extendField3" :placeholder="$t('common.pleaseEnter')" />-->

          <!--          </el-form-item>-->
          <!--          <el-form-item :label="$t('material.extendedField4')">-->
          <!--            <el-input v-model="commonInfo.extendField4" :placeholder="$t('common.pleaseEnter')" />-->

          <!--          </el-form-item>-->
          <!--          <el-form-item label="MFG/MPN">-->
          <!--            <div v-for="(item,index) in commonInfo.mfgRelCreateReqVO" :key="index" style="margin: 0;display: flex;align-items: center">-->
          <!--              <el-input v-model="item.mfg" style="width: 30%" :placeholder="$t('material.pleaseEnterTheManufacturer')" />-->
          <!--              <el-input v-model="item.mpn" style="width: 30%" :placeholder="$t('material.pleaseEnterTheManufacturersPartNumber')" />-->
          <!--              <el-input v-model="item.remarks" style="width: 30%" :placeholder="$t('material.pleaseEnterTheManufacturersRemarks')" />-->
          <!--              <i-->
          <!--                v-if="!viewOnly"-->
          <!--                class="el-icon-circle-plus"-->
          <!--                style="margin-left:10px;font-size: 18px;cursor: pointer"-->
          <!--                @click="commonInfo.mfgRelCreateReqVO.push({-->
          <!--                  mfg: '',-->
          <!--                  mpn: '',-->
          <!--                  remarks: ''-->
          <!--                })"-->
          <!--              />-->
          <!--              <i-->
          <!--                v-if="index !==0&&!viewOnly"-->
          <!--                class="el-icon-remove"-->
          <!--                style="margin-left:10px;font-size: 18px;cursor: pointer"-->
          <!--                @click="commonInfo.mfgRelCreateReqVO.splice(index,1)"-->
          <!--              />-->
          <!--            </div>-->

          <!--          </el-form-item>-->
        </el-form>

      </div>
      <div class="fixedBottom">
        <el-button @click="cancelMaterial">{{ $t('common.cancel') }}</el-button>
        <el-button @click="saveCommonInfo(true)">{{ $t('material.detailedEditing') }}</el-button>
        <el-button type="primary" @click="saveCommonInfo()">{{ $t('common.save') }}</el-button>

      </div>
    </common-card>
    <div v-show="!createFlag">
      <common-card
        :title="$t('material.generalInformation')"
      >

        <el-form ref="commonInfo" :model="commonInfo" inline label-width="203px" size="mini">

          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item
                :rules=" { required: true, message: $t('material.pleaseSelectAFactory'), trigger: 'change' }"
                prop="factory"
                class="materialForm"
                :label="$t('material.factory')"
              >
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.factory"
                  :dict="DICT_TYPE.COMMON_FACTORY"
                >
                  <el-select v-model="commonInfo.factory" :disabled="viewOnly" style="width: 100%;" multiple clearable>
                    <el-option v-for="item in factoryList" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.materialCode')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.materialCode"
                >
                  <el-input v-model="commonInfo.materialCode" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :rules=" { required: true, message: $t('material.pleaseEnterTheMaterialDescription'), trigger: 'blur' }"
                class="materialForm"
                prop="materialDescription"
                :label="$t('material.materialDescription')"
              >
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.materialDescription"
                >
                  <el-input v-model="commonInfo.materialDescription" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.specificationAndModel')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.specifications"
                >
                  <el-input v-model="commonInfo.specifications" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>

            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.revision')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.version"
                >
                  <el-input v-model="commonInfo.version" :placeholder="$t('material.pleaseEnterTheVersion')" />
                </show-or-edit>
              </el-form-item>

            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.drawingVersion')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.drawingVersion"
                >
                  <el-input v-model="commonInfo.drawingVersion" :placeholder="$t('material.pleaseEnterTheDrawingVersion')" />
                </show-or-edit>
              </el-form-item>

            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.category')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.category.at(-1)"
                  :dict="DICT_TYPE.COMMON_CATEGORY"
                >
                  <el-cascader
                    v-model="commonInfo.category"
                    filterable
                    clearable
                    style="width: 100%"
                    :props="{ value: 'id',label:'name'}"
                    :options="categoryList"
                    :disabled="viewOnly"
                    :placeholder="$t('material.pleaseSelectCategory')"
                  />
                </show-or-edit>
              </el-form-item>

            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.selfMadeOrPurchased')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.materialType"
                  :dict="DICT_TYPE.MATERIAL_MAKE_OR_BUY"
                >
                  <el-select v-model="commonInfo.materialType" :disabled="viewOnly" :placeholder="$t('material.selfMadePurchased')" style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_MAKE_OR_BUY)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>

            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.basicUnit')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.basicUnit"
                  :dict="DICT_TYPE.MATERIAL_UOM"
                >
                  <el-select v-model="commonInfo.basicUnit" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.purchasingUnit')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.purchasingUnit"
                  :dict="DICT_TYPE.MATERIAL_UOM"
                >
                  <el-select v-model="commonInfo.purchasingUnit" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.procurementGroup')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.pgId"
                  :dict="DICT_TYPE.SYSTEM_PURCHASE_GROUP"
                >
                  <el-select v-model="commonInfo.pgId" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.SYSTEM_PURCHASE_GROUP)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.lengthXWidthXHeightmm')">
                <div>
                  <show-or-edit
                    style="display: flex"
                    :disabled="viewOnly"
                    type="Custom"
                  >
                    <div slot="content">
                      {{ commonInfo.length }} * {{ commonInfo.width }} *  {{ commonInfo.height }}
                    </div>
                    <el-input v-model.number="commonInfo.length" type="number" :placeholder="$t('material.pleaseEnterTheLength')" />

                    <el-input v-model.number="commonInfo.width" type="number" :placeholder="$t('material.pleaseEnterTheWidth')" />
                    <el-input v-model.number="commonInfo.height" type="number" :placeholder="$t('material.pleaseEnterTheHeight')" />
                  </show-or-edit>
                </div>

              </el-form-item>
            </el-col>

          </el-row>
          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.customerCode')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.customerCode"
                >
                  <el-input v-model="commonInfo.customerCode" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.purchaseType')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.purchaseType"
                  :dict="DICT_TYPE.MATERIAL_PURCHASE_TYPE"
                >
                  <el-select v-model="commonInfo.purchaseType" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_PURCHASE_TYPE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.standardCost')">
                <div style="display: flex">
                  <show-or-edit
                    :disabled="viewOnly"
                    :value="commonInfo.standardCost"
                    style="flex: 1 0 75%"
                    type="Custom"
                  >
                    <div slot="content">
                      {{ commonInfo.standardCost }}
                      <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="commonInfo.standardCostCurrency" />
                    </div>
                    <el-input v-model.number="commonInfo.standardCost" style="width: 70%" type="number" />
                    <el-select v-model="commonInfo.standardCostCurrency" style="width: 30%" :disabled="viewOnly" filterable>
                      <el-option
                        v-for="dict in getMaterialDict(DICT_TYPE.COMMON_CURRENCY)"
                        :key="dict.id"
                        :label="dict.name"
                        :value="dict.id"
                      />
                    </el-select>
                  </show-or-edit>
                </div>

              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.materialRemarks')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.remarks"
                >
                  <el-input v-model="commonInfo.remarks" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.model')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.model"
                >
                  <el-input v-model="noGeneralInfo.model" :placeholder="$t('common.pleaseEnter')" />

                </show-or-edit></el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.isItAServiceType')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.extendField2"
                >
                  <el-input v-model="commonInfo.extendField2" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.extendedField3')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.extendField3"
                >
                  <el-input v-model="commonInfo.extendField3" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.extendedField4')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="commonInfo.extendField4"
                >
                  <el-input v-model="commonInfo.extendField4" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col
              v-for="(item,index) in commonInfo.mfgRelCreateReqVO"
              :span="12"
            >
              <el-form-item
                class="materialForm"
                label="MFG/MPN"
              >
                <show-or-edit
                  :key="index"
                  style="display: flex;align-items: center"
                  :disabled="viewOnly"
                  type="Custom"
                >
                  <div slot="content">
                    {{ (item.mfg!=null && item.mfg!='' && item.mpn!='' && item.mpn!=null) ? item.mfg+"/"+ item.mpn:item.mfg+item.mpn }}
                    {{ item.remarks }}
                  </div>
                  <div style="width: calc(100% - 56px)">

                    <el-input v-model="item.mfg" style="width: 33.3%" :placeholder="$t('material.pleaseEnterTheManufacturer')" />
                    <el-input v-model="item.mpn" style="width: 33.3%" :placeholder="$t('material.pleaseEnterTheManufacturersPartNumber')" />
                    <el-input v-model="item.remarks" style="width: 33.3%" :placeholder="$t('material.pleaseEnterTheManufacturersRemarks')" />
                  </div>
                  <div>

                    <i
                      v-if="!viewOnly"
                      class="el-icon-circle-plus"
                      style="margin-left:10px;font-size: 18px;cursor: pointer"
                      @click="commonInfo.mfgRelCreateReqVO.push({
                        mfg: '',
                        mpn: '',
                        remarks: ''
                      })"
                    />
                    <i
                      v-if="commonInfo.mfgRelCreateReqVO.length>1&&!viewOnly"
                      class="el-icon-remove"
                      style="margin-left:10px;font-size: 18px;cursor: pointer"
                      @click="commonInfo.mfgRelCreateReqVO.splice(index,1)"
                    />
                  </div>
                </show-or-edit>
              </el-form-item>

            </el-col>

          </el-row>
        </el-form>

      </common-card>
      <!--      <el-card class="materialCard">-->
      <!--        <div slot="header" class="mainTab">-->
      <!--          {{ $t('material.generalInformation') }}-->
      <!--          <i-->
      <!--            class="el-icon-arrow-up"-->
      <!--            :style="showCommon? '':{transform: 'rotate(180deg)'}"-->
      <!--            style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;-->
      <!--          font-weight: bold;"-->
      <!--            @click="showCommon= !showCommon"-->
      <!--          />-->
      <!--        </div>-->
      <!--      </el-card>-->
      <common-card
        :title="$t('material.managementInformation')"
      >
        <el-form inline label-width="203px" size="mini">
          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.supplementaryDescriptionOfMaterials')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.additionalNotes"
                >
                  <el-input v-model="noGeneralInfo.additionalNotes" :placeholder="$t('material.pleaseEnterTheSupplementaryDescriptionOfTheMaterial')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.oldMaterialNo')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.oldMaterialCode"
                >
                  <el-input v-model="noGeneralInfo.oldMaterialCode" :placeholder="$t('material.pleaseEnterTheOldMaterialNumber')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.isItConfidential')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.isConfidential"
                  :dict="DICT_TYPE.COMMON_Y_N"
                >
                  <el-select v-model="noGeneralInfo.isConfidential" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.COMMON_Y_N)"
                      :key="parseInt(dict.value)"
                      :label="dict.label"
                      :value="parseInt(dict.value)"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.tradingMode')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.transactionMode"
                  :dict="DICT_TYPE.MATERIAL_TRANSACTION_MODE"
                >
                  <el-select v-model="noGeneralInfo.transactionMode" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_TRANSACTION_MODE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.keyMaterials')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.keyMaterial"
                  :dict="DICT_TYPE.MATERIAL_KEY_MATERIAL"
                >
                  <el-select v-model="noGeneralInfo.keyMaterial" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_KEY_MATERIAL)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>

            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.qualityStandard')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.qualityStandard"
                >
                  <el-input v-model="noGeneralInfo.qualityStandard" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.priceUnit')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.priceUnit"
                >
                  <el-input v-model.number="noGeneralInfo.priceUnit" type="number" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.lifeCycle')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.lifeCycle"
                  :dict="DICT_TYPE.MATERIAL_LIFE_CYCLE"
                >
                  <el-select v-model="noGeneralInfo.lifeCycle" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_LIFE_CYCLE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>

            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.divisionAttributes')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.businessUnitProperties"
                  :dict="DICT_TYPE.MATERIAL_BUSINESS_UNIT_PROPERTIES"
                >
                  <el-select v-model="noGeneralInfo.businessUnitProperties" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_BUSINESS_UNIT_PROPERTIES)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.generality')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.generality"
                  :dict="DICT_TYPE.MATERIAL_GENERALITY"
                >
                  <el-select v-model="noGeneralInfo.generality" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_GENERALITY)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>

      </common-card>
      <!--      <el-card class="materialCard">-->
      <!--        <div slot="header" class="mainTab">-->
      <!--          {{ $t('material.managementInformation') }}-->
      <!--          <i-->
      <!--            class="el-icon-arrow-up"-->
      <!--            :style="showInfo? '':{transform: 'rotate(180deg)'}"-->
      <!--            style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;-->
      <!--          font-weight: bold;"-->
      <!--            @click="showInfo= !showInfo"-->
      <!--          />-->
      <!--        </div>-->
      <!--      </el-card>-->
      <common-card
        :title="$t('material.orderPriceTerms')"
      >

        <el-form inline label-width="167px" size="mini">
          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item class="orderPriceForm" :label="$t('material.plannedMinimumLotSizemoq')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.moq"
                >
                  <el-input v-model.number="noGeneralInfo.moq" type="number" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="orderPriceForm" :label="$t('material.plannedMinimumPackagingQuantitympq')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.mpq"
                >
                  <el-input v-model.number="noGeneralInfo.mpq" type="number" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="orderPriceForm" :label="$t('material.plannedDeliveryTime')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.deliveryTime"
                >
                  <el-input v-model="noGeneralInfo.deliveryTime" type="number" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>

            </el-col>
          </el-row>
        </el-form>

      </common-card>
      <!--      <el-card class="materialCard">-->
      <!--        <div slot="header" class="mainTab">-->
      <!--          {{ $t('material.orderPriceTerms') }}-->
      <!--          <i-->
      <!--            class="el-icon-arrow-up"-->
      <!--            :style="showInfo1? '':{transform: 'rotate(180deg)'}"-->
      <!--            style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;-->
      <!--          font-weight: bold;"-->
      <!--            @click="showInfo1= !showInfo1"-->
      <!--          />-->
      <!--        </div>-->
      <!--      </el-card>-->
      <common-card
        :title="$t('material.customsDeclarationInformation')"
      >
        <el-form inline label-width="228px" size="mini">

          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item class="customsForm" :label="$t('material.productionLicenseNoProductionFilingCertificateNo')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.prCertificateNo"
                >
                  <el-input v-model="noGeneralInfo.prCertificateNo" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="customsForm" :label="$t('material.dateOfIssuanceOfProductionLicenseProductionRecordCertificate')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.prCertificateIssueDate"
                  type="Date"
                >
                  <el-date-picker
                    v-model="noGeneralInfo.prCertificateIssueDate"
                    style="width: 100%"
                    :placeholder="$t('common.pleaseSelectADate')"
                    type="date"
                    placement="bottom-start"
                    value-format="yyyy-MM-dd"
                  />
                </show-or-edit>
              </el-form-item>

            </el-col>
            <el-col :span="12">

              <el-form-item class="customsForm" :label="$t('material.validityPeriodOfProductionLicenseProductionFilingCertificate')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.prCertificateValidityPeriod"
                  type="Date"
                >
                  <el-date-picker
                    v-model="noGeneralInfo.prCertificateValidityPeriod"
                    style="width: 100%"
                    :placeholder="$t('common.pleaseSelectADate')"
                    type="date"
                    placement="bottom-start"
                    value-format="yyyy-MM-dd"
                  />
                </show-or-edit>
              </el-form-item>

            </el-col>

            <el-col :span="12">
              <el-form-item class="customsForm" :label="$t('material.approvalNumber')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.approvalNo"
                >
                  <el-input v-model="noGeneralInfo.approvalNo" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="customsForm" :label="$t('material.classificationCodeOfMedicalDevices')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.medicalDeviceClassificationCode"
                >
                  <el-input v-model="noGeneralInfo.medicalDeviceClassificationCode" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="customsForm" :label="$t('material.uidProductCode')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.uidProductCode"
                >
                  <el-input v-model="noGeneralInfo.uidProductCode" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>

            </el-col>

            <el-col :span="12">
              <el-form-item class="customsForm" :label="$t('material.countryOfOriginCodeAndCountryOfOrigin')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.countryOrigin"
                  :dict="DICT_TYPE.MATERIAL_COUNTRY_ORIGIN"
                >
                  <el-select v-model="noGeneralInfo.countryOrigin" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_COUNTRY_ORIGIN)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="customsForm" label="HS Code">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.hsCode"
                >
                  <el-input v-model="noGeneralInfo.hsCode" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="customsForm" :label="$t('material.commodityName')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.productName"
                >
                  <el-input v-model="noGeneralInfo.productName" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="customsForm" :label="$t('material.taxClassificationCode')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.taxClassificationCode"
                >
                  <el-input v-model="noGeneralInfo.taxClassificationCode" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="customsForm" :label="$t('material.packageSpecification')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.packagingSpecifications"
                >
                  <el-input v-model="noGeneralInfo.packagingSpecifications" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>

            </el-col>
            <el-col :span="12">
              <el-form-item class="customsForm" :label="$t('material.packagingCategory')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.packagingCategory"
                >
                  <el-input v-model="noGeneralInfo.packagingCategory" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="customsForm" :label="$t('material.chemicalCsaNumber')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.chemicalCsaNo"
                >
                  <el-input v-model="noGeneralInfo.chemicalCsaNo" :placeholder="$t('common.pleaseEnter')" />

                </show-or-edit></el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="customsForm" :label="$t('material.explosiveNameCode')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.explosiveNameCode"
                >
                  <el-input v-model="noGeneralInfo.explosiveNameCode" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit></el-form-item>
            </el-col>

          </el-row>
        </el-form>

      </common-card>
      <!--      <el-card class="materialCard">-->
      <!--        <div slot="header" class="mainTab">-->
      <!--          {{ $t('material.customsDeclarationInformation') }}-->
      <!--          <i-->
      <!--            class="el-icon-arrow-up"-->
      <!--            :style="showInfo2? '':{transform: 'rotate(180deg)'}"-->
      <!--            style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;-->
      <!--          font-weight: bold;"-->
      <!--            @click="showInfo2= !showInfo2"-->
      <!--          />-->
      <!--        </div>-->
      <!--      </el-card>-->
      <common-card
        :title="$t('material.storingInformation')"
      >
        <el-form inline label-width="203px" size="mini">
          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.shelfLife')">
                <show-or-edit
                  style="display: flex"
                  :disabled="viewOnly"
                  type="Custom"
                >
                  <div slot="content">
                    {{ noGeneralInfo.shelfLife }}
                    <dict-tag :type="DICT_TYPE.MATERIAL_TIME_TYPE" :value="noGeneralInfo.shelfLifeUnit" />
                  </div>
                  <el-input v-model.number="noGeneralInfo.shelfLife" type="number" style="flex: 1 0 75%" />
                  <el-select v-model="noGeneralInfo.shelfLifeUnit" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_TIME_TYPE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>

              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.abcIdentification')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.abcLogo"
                  :dict="DICT_TYPE.MATERIAL_ABC_LOGO"
                >
                  <el-select v-model="noGeneralInfo.abcLogo" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_ABC_LOGO)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.degreeOfProtection')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.protectionClass"
                >
                  <el-input v-model="noGeneralInfo.protectionClass" :placeholder="$t('common.pleaseEnter')" />

                </show-or-edit></el-form-item>

            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.storageConditions')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.storageConditionTemperature"
                  type="Custom"
                >
                  <div slot="content">
                    {{ noGeneralInfo.storageConditionTemperature+'℃' }}
                    <span style="margin-right: 10px" />
                    {{ noGeneralInfo.storageConditionsHumidity+'RH%' }}
                  </div>
                  <div style="width: 50%;display: inline-block">
                    <el-input v-model.number="noGeneralInfo.storageConditionTemperature" style="width: 70%" type="number" />
                    <span style="margin-left: 5px">℃</span>
                  </div>
                  <div style="width: 50%;display: inline-block">
                    <el-input v-model.number="noGeneralInfo.storageConditionsHumidity" style="width: 70%" type="number" />
                    <span style="margin-left: 5px">RH%</span>
                  </div>
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.position')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.position"
                >
                  <el-input v-model="noGeneralInfo.position" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit></el-form-item>
            </el-col>

          </el-row>
        </el-form>

      </common-card>
      <!--      <el-card class="materialCard">-->
      <!--        <div slot="header" class="mainTab">-->
      <!--          {{ $t('material.storingInformation') }}-->
      <!--          <i-->
      <!--            class="el-icon-arrow-up"-->
      <!--            :style="showInfo3? '':{transform: 'rotate(180deg)'}"-->
      <!--            style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;-->
      <!--          font-weight: bold;"-->
      <!--            @click="showInfo3= !showInfo3"-->
      <!--          />-->
      <!--        </div>-->
      <!--      </el-card>-->

      <common-card
        :title="$t('material.manufacturingInformation')"
      >
        <el-form inline label-width="203px" size="mini">
          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.brand')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.brand"
                >
                  <el-input v-model="noGeneralInfo.brand" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit></el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.materialGrade')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.materialGrade"
                >
                  <el-input v-model="noGeneralInfo.materialGrade" :placeholder="$t('common.pleaseEnter')" />
                </show-or-edit></el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.textureOfMaterial')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.material"
                >
                  <el-input v-model="noGeneralInfo.material" :placeholder="$t('common.pleaseEnter')" />

                </show-or-edit></el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.colour')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.color"
                >
                  <el-input v-model="noGeneralInfo.color" :placeholder="$t('material.pleaseEnterAColor')" />

                </show-or-edit></el-form-item>

            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.materialShop')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.materialShop"
                >
                  <el-input v-model="noGeneralInfo.materialShop" :placeholder="$t('common.pleaseEnter')" />

                </show-or-edit></el-form-item>

            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.component')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.element"
                >
                  <el-input v-model="noGeneralInfo.element" :placeholder="$t('common.pleaseEnter')" />

                </show-or-edit></el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.purpose')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.purposes"
                >
                  <el-input v-model="noGeneralInfo.purposes" :placeholder="$t('common.pleaseEnter')" />

                </show-or-edit></el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.dangerousGoodsOrNot')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.isDangerous"
                  :dict="DICT_TYPE.COMMON_Y_N"
                >
                  <el-select v-model="noGeneralInfo.isDangerous" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.COMMON_Y_N)"
                      :key="parseInt(dict.value)"
                      :label="dict.label"
                      :value="parseInt(dict.value)"
                    />
                  </el-select>
                </show-or-edit></el-form-item>

            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.rohsMaterialProperties')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.rohsMaterialProperties"
                  :dict="DICT_TYPE.MATERIAL_ROHS_MATERIAL_PROPERTIES"
                >
                  <el-select v-model="noGeneralInfo.rohsMaterialProperties" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_ROHS_MATERIAL_PROPERTIES)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>

                </show-or-edit></el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.grossWeight')">
                <show-or-edit
                  style="display: flex"
                  :disabled="viewOnly"
                  type="Custom"
                >
                  <div slot="content">
                    {{ noGeneralInfo.grossWeight }}
                    <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="noGeneralInfo.grossWeightUnit" />
                  </div>
                  <el-input v-model.number="noGeneralInfo.grossWeight" type="number" style="flex: 1 0 75%" />
                  <el-select v-model="noGeneralInfo.grossWeightUnit" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />

                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="30">

            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.netWeight')">
                <show-or-edit
                  style="display: flex"
                  :disabled="viewOnly"
                  type="Custom"
                >
                  <div slot="content">
                    {{ noGeneralInfo.netWeight }}
                    <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="noGeneralInfo.netWeightUnit" />
                  </div>
                  <el-input v-model.number="noGeneralInfo.netWeight" type="number" style="flex: 1 0 75%" />
                  <el-select v-model="noGeneralInfo.netWeightUnit" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.processCode')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.processCode"
                >
                  <el-input v-model="noGeneralInfo.processCode" :placeholder="$t('common.pleaseEnter')" />

                </show-or-edit></el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.processingTechnology')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.processingTechnology"
                >
                  <el-input v-model="noGeneralInfo.processingTechnology" :placeholder="$t('common.pleaseEnter')" />

                </show-or-edit></el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.volume')">
                <show-or-edit
                  style="display: flex"
                  :disabled="viewOnly"
                  type="Custom"
                >
                  <div slot="content">
                    {{ noGeneralInfo.volume }}
                    <dict-tag :type="DICT_TYPE.MATERIAL_UOM_VOLUME" :value="noGeneralInfo.volumeUnit" />
                  </div>
                  <el-input v-model.number="noGeneralInfo.volume" type="number" style="flex: 1 0 75%" />
                  <el-select v-model="noGeneralInfo.volumeUnit" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM_VOLUME)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

      </common-card>
      <common-card
        :title="$t('supplier.essentialInformation')"
      >
        <el-form v-show="showInfo5" inline label-width="203px" size="mini">
          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.materialType')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.materialType"
                  :dict="DICT_TYPE.MATERIAL_TYPE"
                >
                  <el-select v-model="noGeneralInfo.materialType" :disabled="viewOnly" filterable style="width: 100%">
                    <el-option
                      v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_TYPE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>

                </show-or-edit></el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.productPurity')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.productPurity"
                >
                  <el-input v-model="noGeneralInfo.productPurity" :placeholder="$t('common.pleaseEnter')" />

                </show-or-edit></el-form-item>
            </el-col>
          </el-row>

        </el-form>

      </common-card>
      <common-card
        :title="$t('supplier.salesInformation')"
      >

        <el-form inline label-width="203px" size="mini">
          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.productCategory')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.productCategory"
                >
                  <el-input v-model="noGeneralInfo.productCategory" :placeholder="$t('common.pleaseEnter')" />

                </show-or-edit></el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="materialForm" :label="$t('material.endCustomerItemNo')">
                <show-or-edit
                  :disabled="viewOnly"
                  :value="noGeneralInfo.endCustomerPartNumber"
                >
                  <el-input v-model="noGeneralInfo.endCustomerPartNumber" :placeholder="$t('common.pleaseEnter')" />

                </show-or-edit></el-form-item>

            </el-col>
          </el-row>
        </el-form>

      </common-card>
      <!--      <el-card class="materialCard">-->
      <!--        <div slot="header" class="mainTab">-->
      <!--          {{ $t('material.manufacturingInformation') }}-->
      <!--          <i-->
      <!--            class="el-icon-arrow-up"-->
      <!--            :style="showInfo4? '':{transform: 'rotate(180deg)'}"-->
      <!--            style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;-->
      <!--          font-weight: bold;"-->
      <!--            @click="showInfo4= !showInfo4"-->
      <!--          />-->
      <!--        </div>-->
      <!--      </el-card>-->
      <!--      <el-card class="materialCard">-->
      <!--        <div slot="header" class="mainTab">-->
      <!--          {{ $t('supplier.essentialInformation') }}-->
      <!--          <i-->
      <!--            class="el-icon-arrow-up"-->
      <!--            :style="showInfo5? '':{transform: 'rotate(180deg)'}"-->
      <!--            style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;-->
      <!--          font-weight: bold;"-->
      <!--            @click="showInfo5= !showInfo5"-->
      <!--          />-->
      <!--        </div>-->
      <!--      </el-card>-->
      <!--      <el-card class="materialCard">-->
      <!--        <div slot="header" class="mainTab">-->
      <!--          {{ $t('supplier.salesInformation') }}-->
      <!--          <i-->
      <!--            class="el-icon-arrow-up"-->
      <!--            :style="showInfo6? '':{transform: 'rotate(180deg)'}"-->
      <!--            style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;-->
      <!--          font-weight: bold;"-->
      <!--            @click="showInfo6= !showInfo6"-->
      <!--          />-->
      <!--        </div>-->
      <!--      </el-card>-->
      <custom-form ref="custom" style="margin-bottom: 86px" :view-only="viewOnly" :code="'material'" />
      <div v-if="!viewOnly" class="fixedBottom">
        <el-button plain type="primary" @click="cancelMaterial">{{ $t('common.cancel') }}</el-button>
        <el-button style="margin-left: 20px" type="primary" @click="saveMaterial">{{ $t('common.save') }}</el-button>

      </div>
    </div>

  </div>
</template>

<script>
import customForm from '@/components/CustomForm'
import { listFactoryFromCache } from '@/api/system/factory'
import { getMaterial, saveMaterial, updateMaterial } from '@/api/material/main'
import { getTreeMap } from '@/utils'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { parseTime } from '@/utils/ruoyi'
import ShowOrEdit from '@/components/ShowOrEdit'
import { getCompanyLists } from '@/api/system/companies'

export default {
  name: 'Materiallist',
  components: {
    customForm,
    ShowOrEdit
  },
  data() {
    return {
      commonInfo: {
        materialCode: '',
        materialDescription: '',
        specifications: '',
        version: '',
        drawingVersion: '',
        category: [],
        categoryId: '',
        materialType: '',
        basicUnit: '',
        purchasingUnit: '',
        pgId: null,
        length: null,
        width: null,
        height: null,
        customerCode: '',
        purchaseType: '',
        standardCost: null,
        standardCostCurrency: null,
        remarks: '',
        // effectiveDate: '',
        // expirationDate: '',
        extendField1: '',
        extendField2: '',
        extendField3: '',
        extendField4: '',
        factory: [],
        factoryRelCreateReqVO: [],
        companyRelCreateReqVO: [],
        mfgRelCreateReqVO: [{
          mfg: '',
          mpn: '',
          remarks: ''
        }]
      },
      noGeneralInfo: {
        materialId: '',
        additionalNotes: '',
        materialType: '',
        oldMaterialCode: '',
        model: '',
        grossWeight: null,
        grossWeightUnit: '',
        netWeight: null,
        netWeightUnit: '',
        volume: null,
        volumeUnit: '',
        color: '',
        approvalNo: '',
        prCertificateNo: '',
        prCertificateIssueDate: '',
        prCertificateValidityPeriod: '',
        medicalDeviceClassificationCode: '',
        countryOrigin: '',
        uidProductCode: '',
        hsCode: '',
        productName: '',
        taxClassificationCode: '',
        packagingSpecifications: '',
        packagingCategory: '',
        materialGrade: '',
        material: '',
        materialShop: '',
        element: '',
        brand: '',
        purposes: '',
        isDangerous: null,
        chemicalCsaNo: '',
        explosiveNameCode: '',
        isConfidential: null,
        processingTechnology: '',
        processCode: '',
        moq: null,
        mpq: null,
        deliveryTime: '',
        shelfLife: null,
        shelfLifeUnit: '',
        transactionMode: '',
        abcLogo: '',
        protectionClass: '',
        storageConditionTemperature: '',
        storageConditionsHumidity: '',
        position: '',
        productCategory: '',
        keyMaterial: '',
        productPurity: '',
        qualityStandard: '',
        priceUnit: null,
        lifeCycle: '',
        businessUnitProperties: '',
        rohsMaterialProperties: '',
        generality: '',
        endCustomerPartNumber: ''
      },
      factoryList: [],
      categoryList: [],
      currencyList: [],
      value: null,
      showCommon: true,
      showNoGeneral: true,
      showInfo: true,
      showInfo1: true,
      showInfo2: true,
      showInfo3: true,
      showInfo4: true,
      showInfo5: true,
      showInfo6: true,
      viewOnly: false,
      createFlag: true
    }
  },
  async mounted() {
    if (this.$route.query.id) {
      this.createFlag = false
    }
    this.viewOnly = this.$route.query.viewOnly === '1'
    await this.getFactory()
    await this.getCompany()
    this.getCategories()
    this.getMaterialDetail()
  },
  methods: {
    /**
     * 根据查看还是编辑，来提供字典数据的处理
     * @param type
     */
    getMaterialDict(type) {
      return getDictDatas(type, this.viewOnly ? false : 0)
    },
    getMaterialDetail() {
      const id = this.$route.query.id
      if (id) {
        getMaterial({
          id
        }).then(res => {
          res.data.materialRespVO.factory = res.data.materialRespVO.factoryRelCreateReqVO.map(item => item.factoryId)
          res.data.materialRespVO.factory = [...new Set(res.data.materialRespVO.factory)]
          res.data.materialRespVO.category = getTreeMap(res.data.materialRespVO.categoryId, this.categoryList)
          this.commonInfo = res.data.materialRespVO
          if (!this.viewOnly && this.commonInfo.mfgRelCreateReqVO.length === 0) {
            this.commonInfo.mfgRelCreateReqVO = [{
              mfg: '',
              mpn: '',
              remarks: ''
            }]
          }
          this.noGeneralInfo = res.data.noGeneralMaterialRespVO
          this.noGeneralInfo.prCertificateIssueDate = parseTime(res.data.noGeneralMaterialRespVO.prCertificateIssueDate)
          this.noGeneralInfo.prCertificateValidityPeriod = parseTime(res.data.noGeneralMaterialRespVO.prCertificateValidityPeriod)
          this.$refs.custom.getCustomFormValue(res.data.materialRespVO.id)
        })
      }
    },
    async getFactory() {
      const res = await listFactoryFromCache({ status: 0 })
      this.factoryList = res.data
    },
    async getCompany() {
      const res = await getCompanyLists({ status: 0 })
      this.companyList = res.data
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(this.getMaterialDict(DICT_TYPE.COMMON_CATEGORY), 'id'))
    },
    cancelMaterial() {
      this.$tab.closeOpenPage('/material/materialindex')
    },
    saveMaterial() {
      const custom = this.$refs.custom.submitCustom()
      this.$refs.commonInfo.validate(async(valid) => {
        if (valid && custom) {
          this.commonInfo.categoryId = this.commonInfo.category?.at(-1)
          this.commonInfo.factoryRelCreateReqVO = this.commonInfo.factory.map(item => {
            return { factoryId: item }
          })
          const postData = {
            materialCreateReqVO: this.commonInfo,
            noGeneralMaterialCreateReqVO: this.noGeneralInfo,
            customFormColumnsDataCreateReqDTO: custom || []
          }
          let data
          if (this.commonInfo.id) {
            data = await updateMaterial(postData)
          } else {
            data = await saveMaterial(postData)
          }
          if (data.data) {
            this.$message.success(this.$t('common.savedSuccessfully'))
            this.emitter.emit('freshMaterial')
            this.$tab.closeOpenPage('/material/materialindex')
          }
        } else {
          this.$message.error(this.$t('common.pleaseFillInTheCompleteData'))
        }
      })
    },
    saveCommonInfo(toDetail) {
      this.$refs.commonInfoConcise.validate(async valid => {
        this.commonInfo.categoryId = this.commonInfo.category?.at(-1)
        this.commonInfo.factoryRelCreateReqVO = this.commonInfo.factory.map(item => {
          return { factoryId: item }
        })
        const postData = {
          materialCreateReqVO: this.commonInfo
          // noGeneralMaterialCreateReqVO: this.noGeneralInfo,
          // customFormColumnsDataCreateReqDTO: custom || []
        }
        if (valid) {
          const data = await saveMaterial(postData)
          if (data.data) {
            this.$message.success(this.$t('common.savedSuccessfully'))
          }
          if (toDetail) {
            this.commonInfo.id = data.data
            this.detailMode()
          } else {
            this.emitter.emit('freshMaterial')
            this.$tab.closeOpenPage('/material/materialindex')
          }
        }
      })
    },
    numInput(event) {
      console.log(event)
    },
    detailMode() {
      this.createFlag = false
      this.$nextTick(() => {
        window.scrollTo(0, 0)
      })
    }
  }

}
</script>

<style lang="scss" scoped>

.materialCommon{
  margin: 10px 0;
}
.materialCard{
  margin: 10px 0;
}
.materialForm {
  width: 95%;
  padding-bottom: 10px;
  ::v-deep .el-form-item__content {
    width: calc(100% - 203px);
  }
}
.customsForm {
  width: 95%;
  padding-bottom: 10px;
  ::v-deep .el-form-item__content {
    width: calc(100% - 228px);
  }
}

.orderPriceForm {
  width: 95%;
  padding-bottom: 10px;
  ::v-deep .el-form-item__content {
    width: calc(100% - 167px);
  }
}
::v-deep .el-form--label-top .el-form-item__label{
  padding-bottom: 0;
}
::v-deep .el-input__inner{
  background: #ffffff!important;
}

.mainTab{
  color: #4996b8;
  font-size: 16px;
  font-weight: 700;
}
</style>
