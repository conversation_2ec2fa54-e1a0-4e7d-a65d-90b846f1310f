
<!--老页面 已废弃-->
<!--老页面 已废弃-->
<!--老页面 已废弃-->

<template>
  <div v-disable-all="viewOnly" style="padding: 25px 15px">
    <el-card class="materialCard">
      <div slot="header" class="mainTab">
        {{ $t('material.generalInformation') }}
        <i
          class="el-icon-arrow-up"
          :style="showCommon? '':{transform: 'rotate(180deg)'}"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          @click="showCommon= !showCommon"
        />
      </div>
      <el-form v-show="showCommon" ref="commonInfo" :model="commonInfo" inline label-position="top" size="mini">
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.materialCode')">
              <el-input v-model="commonInfo.materialCode" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :rules=" { required: true, message: $t('material.pleaseEnterTheMaterialDescription'), trigger: 'blur' }"
              required
              class="materialForm"
              prop="materialDescription"
              :label="$t('material.materialDescription')"
            >
              <el-input v-model="commonInfo.materialDescription" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.specificationAndModel')">
              <el-input v-model="commonInfo.specifications" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item
              :rules=" { required: true, message: $t('material.pleaseSelectAFactory'), trigger: 'change' }"
              required
              prop="factory"
              class="materialForm"
              :label="$t('material.factory')"
            >
              <el-select v-model="commonInfo.factory" :disabled="viewOnly" style="width: 100%;" multiple clearable>
                <el-option v-for="item in factoryList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.revision')">
              <el-input v-model="commonInfo.version" :placeholder="$t('material.pleaseEnterTheVersion')" />

            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.drawingVersion')">
              <el-input v-model="commonInfo.drawingVersion" :placeholder="$t('material.pleaseEnterTheDrawingVersion')" />

            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.category')">
              <el-cascader
                v-model="commonInfo.category"
                filterable
                clearable
                style="width: 100%"
                :props="{ value: 'id',label:'name'}"
                :options="categoryList"
                :disabled="viewOnly"
                :placeholder="$t('material.pleaseSelectCategory')"
              />

            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.selfMadeOrPurchased')">
              <el-select v-model="commonInfo.materialType" :disabled="viewOnly" :placeholder="$t('material.selfMadePurchased')" style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_MAKE_OR_BUY)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>

          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.basicUnit')">
              <el-select v-model="commonInfo.basicUnit" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.purchasingUnit')">
              <el-select v-model="commonInfo.purchasingUnit" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.procurementGroup')">
              <el-input v-model.number="commonInfo.pgId" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.lengthXWidthXHeightmm')">
              <div style="display: flex">
                <el-input v-model.number="commonInfo.length" type="number" :placeholder="$t('material.pleaseEnterTheLength')" />
                <el-input v-model.number="commonInfo.width" type="number" :placeholder="$t('material.pleaseEnterTheWidth')" />
                <el-input v-model.number="commonInfo.height" type="number" :placeholder="$t('material.pleaseEnterTheHeight')" />
              </div>

            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.customerCode')">
              <el-input v-model="commonInfo.customerCode" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.purchaseType')">
              <el-select v-model="commonInfo.purchaseType" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_PURCHASE_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.standardCost')">
              <div style="display: flex">
                <el-input v-model.number="commonInfo.standardCost" type="number" style="flex: 1 0 75%" />
                <el-select v-model="commonInfo.standardCostCurrency" :disabled="viewOnly" filterable style="width: 100%">
                  <el-option
                    v-for="dict in getMaterialDict('currency')"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>
              </div>

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.materialRemarks')">
              <el-input v-model="commonInfo.remarks" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.extendedField1')">
              <el-input v-model="commonInfo.extendField1" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.extendedField2')">
              <el-input v-model="commonInfo.extendField2" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.extendedField3')">
              <el-input v-model="commonInfo.extendField3" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.extendedField4')">
              <el-input v-model="commonInfo.extendField4" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item style="width: 60%" label="MFG/MPN">
          <div v-for="(item,index) in commonInfo.mfgRelCreateReqVO" :key="index" style="margin:6px 0;display: flex;align-items: center">
            <el-input v-model="item.mfg" style="width: 30%" :placeholder="$t('material.pleaseEnterTheManufacturer')" />
            <el-input v-model="item.mpn" style="width: 30%" :placeholder="$t('material.pleaseEnterTheManufacturersPartNumber')" />
            <el-input v-model="item.remarks" style="width: 30%" :placeholder="$t('material.pleaseEnterTheManufacturersRemarks')" />
            <i
              v-if="!viewOnly"
              class="el-icon-circle-plus"
              style="margin-left:10px;font-size: 18px;cursor: pointer"
              @click="commonInfo.mfgRelCreateReqVO.push({
                mfg: '',
                mpn: '',
                remarks: ''
              })"
            />
            <i
              v-if="index !==0&&!viewOnly"
              class="el-icon-remove"
              style="margin-left:10px;font-size: 18px;cursor: pointer"
              @click="commonInfo.mfgRelCreateReqVO.splice(index,1)"
            />
          </div>

        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="materialCard">
      <div slot="header" class="mainTab">
        {{ $t('material.nonGeneralInformation') }}
        <i
          class="el-icon-arrow-up"
          :style="showNoGeneral? '':{transform: 'rotate(180deg)'}"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          @click="showNoGeneral= !showNoGeneral"
        />
      </div>
      <el-form v-show="showNoGeneral" inline label-position="top" size="mini">
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.supplementaryDescriptionOfMaterials')">
              <el-input v-model="noGeneralInfo.additionalNotes" :placeholder="$t('material.pleaseEnterTheSupplementaryDescriptionOfTheMaterial')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.materialType')">
              <el-select v-model="noGeneralInfo.materialType" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.oldMaterialNo')">
              <el-input v-model="noGeneralInfo.oldMaterialCode" :placeholder="$t('material.pleaseEnterTheOldMaterialNumber')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.model')">
              <el-input v-model="noGeneralInfo.model" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>

          </el-col>

        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.grossWeight')">
              <div style="display: flex">
                <el-input v-model.number="noGeneralInfo.grossWeight" type="number" style="flex: 1 0 75%" />
                <el-select v-model="noGeneralInfo.grossWeightUnit" :disabled="viewOnly" filterable style="width: 100%">
                  <el-option
                    v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />

                </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.netWeight')">
              <div style="display: flex">

                <el-input v-model.number="noGeneralInfo.netWeight" type="number" style="flex: 1 0 75%" />
                <el-select v-model="noGeneralInfo.netWeightUnit" :disabled="viewOnly" filterable style="width: 100%">
                  <el-option
                    v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.volume')">
              <div style="display: flex">

                <el-input v-model.number="noGeneralInfo.volume" type="number" style="flex: 1 0 75%" />
                <el-select v-model="noGeneralInfo.volumeUnit" :disabled="viewOnly" filterable style="width: 100%">
                  <el-option
                    v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </div>
            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.colour')">
              <el-input v-model="noGeneralInfo.color" :placeholder="$t('material.pleaseEnterAColor')" />

            </el-form-item>

          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.approvalNumber')">
              <el-input v-model="noGeneralInfo.approvalNo" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.productionLicenseNoProductionFilingCertificateNo')">
              <el-input v-model="noGeneralInfo.prCertificateNo" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.dateOfIssuanceOfProductionLicenseProductionRecordCertificate')">
              <el-date-picker
                v-model="noGeneralInfo.prCertificateIssueDate"
                style="width: 100%"
                :placeholder="$t('common.pleaseSelectADate')"
                type="date"
                placement="bottom-start"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>

          </el-col>
          <el-col :span="6">

            <el-form-item class="materialForm" :label="$t('material.validityPeriodOfProductionLicenseProductionFilingCertificate')">
              <el-date-picker
                v-model="noGeneralInfo.prCertificateValidityPeriod"
                style="width: 100%"
                :placeholder="$t('common.pleaseSelectADate')"
                type="date"
                placement="bottom-start"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>

          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.classificationCodeOfMedicalDevices')">
              <el-input v-model="noGeneralInfo.medicalDeviceClassificationCode" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.countryOfOriginCodeAndCountryOfOrigin')">
              <el-select v-model="noGeneralInfo.countryOrigin" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_COUNTRY_ORIGIN)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" label="UID商品码">
              <el-input v-model="noGeneralInfo.uidProductCode" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" label="HS Code">
              <el-input v-model="noGeneralInfo.hsCode" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.commodityName')">
              <el-input v-model="noGeneralInfo.productName" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.taxClassificationCode')">
              <el-input v-model="noGeneralInfo.taxClassificationCode" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.packageSpecification')">
              <el-input v-model="noGeneralInfo.packagingSpecifications" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.packagingCategory')">
              <el-input v-model="noGeneralInfo.packagingCategory" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.materialGrade')">
              <el-input v-model="noGeneralInfo.materialGrade" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.textureOfMaterial')">
              <el-input v-model="noGeneralInfo.material" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.materialGrade')">
              <el-input v-model="noGeneralInfo.materialShop" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.component')">
              <el-input v-model="noGeneralInfo.element" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.brand')">
              <el-input v-model="noGeneralInfo.brand" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.purpose')">
              <el-input v-model="noGeneralInfo.purposes" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.dangerousGoodsOrNot')">
              <el-select v-model="noGeneralInfo.isDangerous" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.COMMON_Y_N)"
                  :key="parseInt(dict.value)"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.chemicalCsaNumber')">
              <el-input v-model="noGeneralInfo.chemicalCsaNo" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.explosiveNameCode')">
              <el-input v-model="noGeneralInfo.explosiveNameCode" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.isItConfidential')">
              <el-select v-model="noGeneralInfo.isConfidential" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.COMMON_Y_N)"
                  :key="parseInt(dict.value)"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.processingTechnology')">
              <el-input v-model="noGeneralInfo.processingTechnology" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.processCode')">
              <el-input v-model="noGeneralInfo.processCode" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.plannedMinimumLotSizemoq')">
              <el-input v-model.number="noGeneralInfo.moq" type="number" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.plannedMinimumPackagingQuantitympq')">
              <el-input v-model.number="noGeneralInfo.mpq" type="number" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.plannedDeliveryTime')">
              <el-input v-model="noGeneralInfo.deliveryTime" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.shelfLife')">
              <div style="display: flex">
                <el-input v-model.number="noGeneralInfo.shelfLife" type="number" style="flex: 1 0 75%" />
                <el-select v-model="noGeneralInfo.shelfLifeUnit" :disabled="viewOnly" filterable style="width: 100%">
                  <el-option
                    v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_TIME_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </div>

            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.tradingMode')">
              <el-select v-model="noGeneralInfo.transactionMode" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_TRANSACTION_MODE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" label="ABC标识">
              <el-select v-model="noGeneralInfo.abcLogo" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_ABC_LOGO)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.degreeOfProtection')">
              <el-input v-model="noGeneralInfo.protectionClass" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.storageConditions')">
              <div style="display: flex;justify-content: space-between">
                <div>    <el-input v-model.number="noGeneralInfo.storageConditionTemperature" style="width: 70%" type="number" />
                  <span style="margin-left: 5px">℃</span></div>
                <div>
                  <el-input v-model.number="noGeneralInfo.storageConditionsHumidity" style="width: 70%" type="number" />
                  <span style="margin-left: 5px">RH%</span>
                </div>

              </div>

            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.position')">
              <el-input v-model="noGeneralInfo.position" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.productCategory')">
              <el-input v-model="noGeneralInfo.productCategory" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.keyMaterials')">
              <el-select v-model="noGeneralInfo.keyMaterial" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_KEY_MATERIAL)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.productPurity')">
              <el-input v-model="noGeneralInfo.productPurity" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.qualityStandard')">
              <el-input v-model="noGeneralInfo.qualityStandard" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.priceUnit')">
              <el-input v-model.number="noGeneralInfo.priceUnit" type="number" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.lifeCycle')">
              <el-select v-model="noGeneralInfo.lifeCycle" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_LIFE_CYCLE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </el-form-item>

          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.divisionAttributes')">
              <el-select v-model="noGeneralInfo.businessUnitProperties" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_BUSINESS_UNIT_PROPERTIES)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.rohsMaterialProperties')">
              <el-select v-model="noGeneralInfo.rohsMaterialProperties" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_ROHS_MATERIAL_PROPERTIES)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.generality')">
              <el-select v-model="noGeneralInfo.generality" :disabled="viewOnly" filterable style="width: 100%">
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_GENERALITY)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="materialForm" :label="$t('material.endCustomerItemNo')">
              <el-input v-model="noGeneralInfo.endCustomerPartNumber" :placeholder="$t('common.pleaseEnter')" />

            </el-form-item>

          </el-col>
        </el-row>
      </el-form>

    </el-card>
    <custom-form ref="custom" style="margin-bottom: 86px" :view-only="viewOnly" :code="'material'" />
    <el-card v-if="!viewOnly" class="fixedBottom materialCard">
      <div style="text-align: right">
        <el-button @click="cancelMaterial">{{ $t('common.cancel') }}</el-button>
        <el-button style="margin-left: 20px" type="primary" @click="saveMaterial">{{ $t('common.save') }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import customForm from '@/components/CustomForm'
import { listFactoryFromCache } from '@/api/system/factory'
import { getMaterial, saveMaterial, updateMaterial } from '@/api/material/main'
import { getTreeMap } from '@/utils'
import { getDictDatas } from '@/utils/dict'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'Materiallist',
  components: {
    customForm
  },
  data() {
    return {
      commonInfo: {
        materialCode: '',
        materialDescription: '',
        specifications: '',
        version: '',
        drawingVersion: '',
        category: [],
        categoryId: '',
        materialType: '',
        basicUnit: '',
        purchasingUnit: '',
        pgId: null,
        length: null,
        width: null,
        height: null,
        customerCode: '',
        purchaseType: '',
        standardCost: null,
        standardCostCurrency: null,
        remarks: '',
        // effectiveDate: '',
        // expirationDate: '',
        extendField1: '',
        extendField2: '',
        extendField3: '',
        extendField4: '',
        factory: [],
        factoryRelCreateReqVO: [],
        mfgRelCreateReqVO: [{
          mfg: '',
          mpn: '',
          remarks: ''
        }]
      },
      noGeneralInfo: {
        materialId: '',
        additionalNotes: '',
        materialType: '',
        oldMaterialCode: '',
        model: '',
        grossWeight: null,
        grossWeightUnit: '',
        netWeight: null,
        netWeightUnit: '',
        volume: null,
        volumeUnit: '',
        color: '',
        approvalNo: '',
        prCertificateNo: '',
        prCertificateIssueDate: '',
        prCertificateValidityPeriod: '',
        medicalDeviceClassificationCode: '',
        countryOrigin: '',
        uidProductCode: '',
        hsCode: '',
        productName: '',
        taxClassificationCode: '',
        packagingSpecifications: '',
        packagingCategory: '',
        materialGrade: '',
        material: '',
        materialShop: '',
        element: '',
        brand: '',
        purposes: '',
        isDangerous: null,
        chemicalCsaNo: '',
        explosiveNameCode: '',
        isConfidential: null,
        processingTechnology: '',
        processCode: '',
        moq: null,
        mpq: null,
        deliveryTime: '',
        shelfLife: null,
        shelfLifeUnit: '',
        transactionMode: '',
        abcLogo: '',
        protectionClass: '',
        storageConditionTemperature: '',
        storageConditionsHumidity: '',
        position: '',
        productCategory: '',
        keyMaterial: '',
        productPurity: '',
        qualityStandard: '',
        priceUnit: null,
        lifeCycle: '',
        businessUnitProperties: '',
        rohsMaterialProperties: '',
        generality: '',
        endCustomerPartNumber: ''
      },
      factoryList: [],
      categoryList: [],
      currencyList: [],
      value: null,
      showCommon: true,
      showNoGeneral: true,
      viewOnly: false
    }
  },
  async mounted() {
    await this.getFactory()
    this.getCategories()
    this.getMaterialDetail()
  },
  methods: {
    /**
     * 根据查看还是编辑，来提供字典数据的处理
     * @param type
     */
    getMaterialDict(type) {
      return getDictDatas(type, this.viewOnly ? null : 0)
    },
    getMaterialDetail() {
      const id = this.$route.query.id
      this.viewOnly = this.$route.query.viewOnly === '1'
      if (id) {
        getMaterial({
          id
        }).then(res => {
          res.data.materialRespVO.factory = res.data.materialRespVO.factoryRelCreateReqVO.map(item => item.factoryId)
          res.data.materialRespVO.category = getTreeMap(res.data.materialRespVO.categoryId, this.categoryList)
          this.commonInfo = res.data.materialRespVO
          if (!this.viewOnly && this.commonInfo.mfgRelCreateReqVO.length === 0) {
            this.commonInfo.mfgRelCreateReqVO = [{
              mfg: '',
              mpn: '',
              remarks: ''
            }]
          }
          this.noGeneralInfo = res.data.noGeneralMaterialRespVO
          this.noGeneralInfo.prCertificateIssueDate = parseTime(res.data.noGeneralMaterialRespVO.prCertificateIssueDate)
          this.noGeneralInfo.prCertificateValidityPeriod = parseTime(res.data.noGeneralMaterialRespVO.prCertificateValidityPeriod)
          this.$refs.custom.getCustomFormValue(res.data.materialRespVO.id)
        })
      }
    },
    async getFactory() {
      const res = await listFactoryFromCache({ status: 0 })
      this.factoryList = res.data
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(this.getMaterialDict(DICT_TYPE.COMMON_CATEGORY), 'id'))
    },
    cancelMaterial() {
      this.$tab.closeOpenPage('/material/materialindex')
    },
    saveMaterial() {
      const custom = this.$refs.custom.submitCustom()
      this.$refs.commonInfo.validate(async(valid) => {
        if (valid && custom) {
          this.commonInfo.categoryId = this.commonInfo.category?.at(-1)
          this.commonInfo.factoryRelCreateReqVO = this.commonInfo.factory.map(item => {
            return { factoryId: item }
          })
          const postData = {
            materialCreateReqVO: this.commonInfo,
            noGeneralMaterialCreateReqVO: this.noGeneralInfo,
            customFormColumnsDataCreateReqDTO: custom || []
          }
          let data
          if (this.commonInfo.id) {
            data = await updateMaterial(postData)
          } else {
            data = await saveMaterial(postData)
          }
          if (data.data) {
            this.$message.success(this.$t('common.savedSuccessfully'))
            this.$tab.closeOpenPage('/material/materialindex')
          }
        } else {
          this.$message.error(this.$t('common.pleaseFillInTheCompleteData'))
        }
      })
    },

    numInput(event) {
      console.log(event)
    }
  }

}
</script>

<style lang="scss" scoped>

.materialCard{
  margin: 10px 0;
}
.materialForm {
  width: 80%;
  padding-bottom: 5px;
}
::v-deep .el-form--label-top .el-form-item__label{
  padding-bottom: 0;
}
::v-deep .el-input__inner{
  background: #ffffff!important;
}
.mainTab{
  font-size: 16px;
  font-weight: 700;
}
</style>
