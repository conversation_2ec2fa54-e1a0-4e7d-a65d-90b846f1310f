<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.search	"
        :placeholder="$t('material.pleaseEnterTheMaterialCodeMaterialDescriptionSpecificationMfgMpn')"
        clearable
        style="flex: 0 1 40%"
        @keyup.enter.native="handleQuery"
      />
      <el-button type="primary" plain @click="handleQuery">{{ $t('common.search') }}</el-button>
      <el-button style="margin-left: 0" plain @click="resetQuery">{{ $t('common.reset') }}</el-button>

      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button type="text">
          {{ $t('common.advancedSearch') }}

        </el-button>
        <i
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>

    </div>
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="135px" size="small">
      <el-form-item :label="$t('material.materialCode')" class="searchItem" prop="materialCode">
        <el-input
          v-model="queryParams.materialCode"
          :placeholder="$t('common.pleaseEnter')"
          class="searchValue"
          clearable
        />
      </el-form-item>
      <el-form-item :label="$t('material.category')" class="searchItem" prop="category">
        <el-cascader
          v-model="queryParams.category"
          :options="categoryList"
          :placeholder="$t('common.pleaseSelect')"
          :props="{ value: 'id',label:'name',multiple :true}"
          class="searchValue"
          clearable
          filterable
        />
      </el-form-item>
      <el-form-item :label="$t('material.purchaseType')" class="searchItem" prop="purchaseType">
        <el-select v-model="queryParams.purchaseType" class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_PURCHASE_TYPE,0)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('material.factory')" class="searchItem" prop="factoryIds">
        <el-select v-model="queryParams.factoryIds" class="searchValue" clearable multiple>
          <el-option v-for="item in factoryList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('material.procurementGroup')" class="searchItem" prop="pgIds">
        <el-input v-model="queryParams.pgIds" :placeholder="$t('common.pleaseEnter')" class="searchValue" />
      </el-form-item>

    </el-form>
    <!-- 列表 -->
    <vxe-grid
      :data="list"
      :loading="loading"
      v-bind="girdOption"
      @sort-change="sortMethod"
      @filter-change="filterMethod"
    >
      <template #createTime="{row}">
        {{ parseTime(row.createTime, '{y}-{m}-{d}') }}
      </template>
      <template #updateTime="{row}">
        {{ parseTime(row.updateTime, '{y}-{m}-{d}') }}
      </template>
      <template #effectiveDate="{row}">
        {{ parseTime(row.effectiveDate, '{y}-{m}-{d}') }}
      </template>
      <template #expirationDate="{row}">
        {{ parseTime(row.expirationDate, '{y}-{m}-{d}') }}
      </template>
      <template #materialCode="{row}">
        <copyButton
          @click="$router.push(`/material/materialView/${row.materialCode}?id=${row.id}&viewOnly=1`)"
        >
          {{ row.materialCode }}
        </copyButton>
        <!--        <el-button-->
        <!--             ></el-button>-->
      </template>
      <template #materialType="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_MAKE_OR_BUY" :value="row.materialType" />

      </template>
      <template #basicUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.basicUnit" />

      </template>
      <template #purchasingUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.purchasingUnit" />

      </template>
      <template #categoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />

      </template>
      <template #pgId="{row}">
        <dict-tag :type="DICT_TYPE.SYSTEM_PURCHASE_GROUP" :value="row.pgId" />
      </template>
      <template #purchaseType="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_PURCHASE_TYPE" :value="row.purchaseType" />

      </template>
      <template #dataSources="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_DATA_SOURCES" :value="row.dataSources" />

      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="row.status" />

      </template>

      <template #toolbar_buttons>
        <el-row :gutter="10" class="mb8" style="width: 100%">
          <!--          <el-col :span="1.5">-->
          <!--            <el-button-->
          <!--              disabled-->
          <!--              icon="el-icon-upload"-->
          <!--              size="mini"-->
          <!--              type="primary"-->
          <!--            >{{ $t('common.synchronizeErp') }}-->
          <!--            </el-button>-->
          <!--          </el-col>-->
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['material:material:create']"
              icon="el-icon-plus"
              size="mini"
              type="primary"
              @click="$router.push('/material/materiallist')"
            >
              {{ $t('material.createANewItem') }}
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['material:material:import']"
              icon="el-icon-upload2"
              size="mini"
              type="primary"
              :loading="exportLoading"
              @click="handleImport"
            > {{ $t('common.batchCreation') }}
            </el-button>

          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['material:material:export']"
              :loading="exportLoading"
              icon="el-icon-download"
              size="mini"
              type="primary"
              @click="handleExport"
            >
              {{ $t('common.batchExport') }}
            </el-button>
          </el-col>

          <right-toolbar
            :custom-columns.sync="girdOption.columns"
            :list-id="girdOption.id"
            :show-search.sync="showSearch"
            @queryTable="getList"
          />
        </el-row>
      </template>
      <template #operate="{row}">

        <!--        <el-button v-hasPermi="['material:material:update']" type="text" @click="handleUpdate(row)">-->
        <!--          {{ $t('common.edit') }}-->

        <!--        </el-button>-->
        <OperateDropDown
          :menu-item="[
            {
              name: $t('common.edit'),
              show: $store.getters.permissions.includes('material:material:update'),
              action: (row) => handleUpdate(row),
              para: row
            },
            {
              name: $t('common.del'),
              show: $store.getters.permissions.includes('material:material:delete'),
              action: (row) => handleDelete(row),
              para: row
            },
            {
              name: $t('common.disable'),

              show: row.status === 0 && $store.getters.permissions.includes('material:material:update-status'),
              action: (row) => changeStatus(row),
              para: row
            },
            {
              name: $t('common.enable'),

              show: row.status !== 0 && $store.getters.permissions.includes('material:material:update-status'),
              action: (row) => changeStatus(row),
              para: row
            },
            {
              name: $t('common.operationRecord'),

              show:true,
              action: (row) => handleLog(row),
              para: row

            }
          ]"
        />

        <!--        <el-button-->
        <!--          v-if="row.status === 1"-->
        <!--          v-hasPermi="['material:material:delete']"-->
        <!--          type="text"-->
        <!--          @click="handleDelete(row)"-->
        <!--        >-->
        <!--          {{ $t('common.del') }}-->

        <!--        </el-button>-->
        <!--        <el-button-->
        <!--          v-if="row.status === 0"-->
        <!--          v-hasPermi="['material:material:update-status']"-->
        <!--          type="text"-->
        <!--          @click="changeStatus(row)"-->
        <!--        >-->
        <!--          {{ $t('common.disable') }}-->

        <!--        </el-button>-->
        <!--        <el-button v-else v-hasPermi="['material:material:update-status']" type="text" @click="changeStatus(row)">-->
        <!--          {{ $t('common.enable') }}-->

        <!--        </el-button>-->
        <!--        <el-button type="text" @click="handleLog(row)">-->
        <!--          {{ $t('common.operationRecord') }}-->
        <!--        </el-button>-->
      </template>
    </vxe-grid>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 物料导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" append-to-body width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url"
          :auto-upload="false"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" :loading="exportLoading" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

    <!-- 日志 -->
    <el-dialog
      v-if="log.open"
      :close-on-click-modal="false"
      :title="$t('common.operationRecord')"
      :visible.sync="log.open"
      width="1000px"
    >
      <operation-record
        :business-id="log.businessId"
        :columns="log.columns"
        :log-visible.sync="log.open"
      />
    </el-dialog>
  </div>
</template>

<script>
import { delMaterial, exportExcel, getPage, importTemplate, putMaterial } from '@/api/material/main'
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { listFactoryFromCache } from '@/api/system/factory'
import { getBaseHeader } from '@/utils/request'
import operationRecord from '@/components/OperationRecord/operationRecord'
import OperateDropDown from '@/components/OperateDropDown/index.vue'

export default {
  name: 'Materialindex',
  components: {
    OperateDropDown,
    operationRecord
  },
  data() {
    return {
      parseTime,
      // 遮罩层
      loading: false,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 物料基本信息主列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        materialCode: '',
        materialType: [],
        categoryIds: [],
        category: [],
        pgIds: '',
        factoryIds: [],
        purchaseType: '',
        search: '',
        sortBy: '',
        sortField: ''
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'material',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns: [
          { field: 'factoryName', title: this.$t('material.factory'), visible: true, width: 100, fixed: 'left' },
          {
            field: 'materialCode',
            slots: { default: 'materialCode' },
            title: this.$t('material.materialCode'), visible: true, width: 100, fixed: 'left'
          },
          {
            field: 'materialDescription',
            title: this.$t('material.materialDescription'),
            visible: true,
            width: 200
          },
          {
            field: 'createTime',
            title: this.$t('common.creationDate'),
            sortable: true,
            slots: { default: 'createTime' },
            visible: true, width: 100
          },
          {
            field: 'updateTime',
            sortable: true,
            slots: { default: 'updateTime' },
            title: this.$t('common.dateOfChange'), visible: true, width: 100
          },

          { field: 'specifications', title: this.$t('material.specificationAndModel'), visible: true, width: 150 },
          {
            field: 'categoryId',
            slots: { default: 'categoryId' },
            title: this.$t('material.category'), visible: true, width: 100
          },
          {
            field: 'basicUnit',
            slots: { default: 'basicUnit' },
            title: this.$t('material.basicUnit'), visible: true, width: 100
          },
          {
            field: 'dataSources',
            slots: { default: 'dataSources' },
            title: this.$t('material.dataSources'),
            visible: true,
            width: 100
          },
          {
            field: 'status',
            slots: { default: 'status' },
            title: this.$t('material.materialStatus'), visible: true, width: 100
          },
          {
            field: 'purchasingUnit',
            title: this.$t('material.purchasingUnit'),
            slots: { default: 'purchasingUnit' },
            visible: false,
            width: 100
          },
          {
            field: 'pgId',
            title: this.$t('material.procurementGroup'),
            slots: { default: 'pgId' },
            visible: false,
            width: 100
          },
          {
            field: 'effectiveDate',
            sortable: true,
            slots: { default: 'effectiveDate' },
            title: this.$t('material.effectiveDate'), visible: false, width: 100
          },
          {
            field: 'expirationDate',
            sortable: true,
            slots: { default: 'expirationDate' },
            title: this.$t('material.deactivationDate'), visible: false, width: 100
          },
          {
            field: 'materialType',
            slots: { default: 'materialType' },
            filters: getDictDatas(DICT_TYPE.MATERIAL_MAKE_OR_BUY, 0),
            title: this.$t('material.selfMadeOrPurchased'), visible: false, width: 110
          },
          { field: 'version', title: this.$t('material.revision'), visible: false, width: 100 },
          { field: 'drawingVersion', title: this.$t('material.drawingVersion'), visible: false, width: 100 },
          { field: 'length', title: this.$t('material.lengthnmm'), visible: false, width: 100 },
          { field: 'width', title: this.$t('material.widthnmm'), visible: false, width: 100 },
          { field: 'height', title: this.$t('material.heightnmm'), visible: false, width: 100 },
          { field: 'customerCode', title: this.$t('material.customerCode'), visible: false, width: 100 },
          { field: 'mfg', title: this.$t('material.manufacturer'), visible: false, width: 100 },
          { field: 'mpn', title: this.$t('material.manufacturersPartNumber'), visible: false, width: 100 },
          {
            field: 'purchaseType',
            slots: { default: 'purchaseType' },
            title: this.$t('material.purchaseType'), visible: false,
            width: 100
          },
          { field: 'standardCost', title: this.$t('material.standardCost'), visible: false, width: 100 },
          {
            field: 'standardCostCurrency',
            title: this.$t('system.currency'),
            formatter: ({ cellValue }) => cellValue ? getDictDatas('currency').find(a => a.id === cellValue)?.name : '',
            visible: false,
            width: 100
          },
          {
            field: 'operate',
            slots: { default: 'operate' }, fixed: 'right',
            title: this.$t('common.operate'), visible: true, width: 35, showOverflow: false
          }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      // 物料导入参数
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/material/material/import'
      },
      // 日志参数
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null,
        columns: []
      },
      categoryList: [],
      factoryList: []
    }
  },
  async mounted() {
    await this.getFactory()
    this.getCategories()
    this.getList()
    this.emitter.on('freshMaterial', () => {
      this.getList()
    })
  },
  beforeDestroy() {
    this.emitter.off('freshMaterial')
  },
  methods: {
    /** 日志操作 */
    handleLog(row) {
      this.log.columns = []
      const id = row.id
      this.log.open = true
      this.log.businessId = id
      this.log.columns.push({
        tableName: 'material_material',
        keyColumns: ['materialCode', 'materialDescription']
      })
      this.log.columns.push({
        tableName: 'material_factory_rel',
        keyColumns: ['factoryId']
      })
      this.log.columns.push({
        tableName: 'material_mfg_rel',
        keyColumns: ['mfg', 'mpn']
      })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = this.$t('common.batchCreation')
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.$download.excel(response, '物料导入模板.xlsx')
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.exportLoading = true
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.exportLoading = false
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createMaterials) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createMaterials.length
      }
      if (data.failureMaterials) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureMaterials).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.exportLoading = true
      this.$refs.upload.submit()
    },
    async getFactory() {
      const res = await listFactoryFromCache({ status: 0 })
      this.factoryList = res.data
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      this.queryParams.categoryIds = this.queryParams.category.map(item => item?.at(-1))
      const params = { ...this.queryParams }
      // 执行查询
      getPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    changeStatus(row) {
      putMaterial({
        id: row.id,
        status: row.status ? 0 : 1
      }).then(res => {
        this.getList()
        this.$message.success(this.$t('common.updateSuccessful'))
      })
    },
    // /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.search = ''
      this.queryParams.sortField = ''
      this.queryParams.sortBy = ''
      this.handleQuery()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let materialCode = row.materialCode
      if (!row.materialCode) {
        materialCode = row.id
      }
      this.$router.push({
        path: `/material/materialEdit/${materialCode}`,
        query: {
          id: row.id
        }
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.exportLoading = true
      exportExcel(queryParams).then(response => {
        this.$download.excel(response, this.$t('material.materialDataExportFilexls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.getList()
    },
    filterMethod({ column, values }) {
      this.queryParams[column.field] = values
      this.getList()
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const materialCode = row.materialCode
      const id = row.id
      this.$modal.confirm(this.$t('material.areYouSureToDeleteTheItemNo') + materialCode + this.$t('material.dataItemOf')).then(function() {
        return delMaterial({ id })
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    }
  }
}

</script>
<style lang="scss" scoped>
.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 135px);
  }
}

.searchValue {
  width: 95%;
}
</style>

