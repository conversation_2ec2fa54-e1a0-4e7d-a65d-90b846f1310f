<template>
  <div style="padding: 25px 15px">
    <common-card
      :title="$t('AVPL 信息')"
      class="materialCommon"
    >
      <div style="display: flex;justify-content: center;margin-bottom: 30px">
          <el-form
            ref="avplInfo"
            :model="avplInfo"
            :rules="avplInfoRule"
            class="baseInfo"
            inline
            label-width="200px"
          >
            <!-- 客户名称 -->
            <el-form-item ref="customer" :label="$t('Customer')" prop="customer">
              <show-or-edit :value="avplInfo.customer">
                <el-select v-model="avplInfo.customer" clearable class="smallSelect" style="width: 100%;">
                  <el-option v-for="item in getDictDatas(DICT_TYPE.OM_INCAP_CUSTOMER)" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </show-or-edit>
            </el-form-item>
            <!-- 供应商名称 -->
            <el-form-item ref="supplier" :label="$t('Supplier')" prop="supplierId">
              <show-or-edit :value="avplInfo.supplierId" :custom-list="canAuthSuppliers">
                <!--                <el-input v-model="avplInfo.supplierId" :placeholder="$t('common.pleaseSelect')"/>-->
                <el-select
                    style="width: 100%"
                    v-model="avplInfo.supplierId"
                    :placeholder="$t('scar.pleaseEnterKeywordsForFuzzySearch')"
                    class="valueStyle"
                    clearable
                    filterable
                    :remote-method="doListNewAuthSupplier"
                    remote
                    @clear="clearSupplier"
                >
                  <el-option
                      v-for="item in canAuthSuppliers"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                  />
                </el-select>
              </show-or-edit>
            </el-form-item>

            <!-- 项目 -->
            <el-form-item ref="project" :label="$t('Project')">
              <show-or-edit :value="avplInfo.project">
                <el-input v-model="avplInfo.project"  :placeholder="$t('common.pleaseSelect')" />
              </show-or-edit>
            </el-form-item>
            <!-- 供应商编码 -->
            <el-form-item ref="vendorCode" :label="$t('Vendor ID')">
              <show-or-edit :value="avplInfo.vendorCode">
                <el-input v-model="avplInfo.vendorCode" :placeholder="$t('common.pleaseSelect')"/>
              </show-or-edit>
            </el-form-item>


            <!-- 工厂 -->
            <el-form-item ref="factory" :label="$t('Factory')">
              <show-or-edit :value="avplInfo.factory">
                <el-input v-model="avplInfo.factory"  :placeholder="$t('common.pleaseSelect')"/>
              </show-or-edit>
            </el-form-item>
            <!-- 供应商价格 -->
            <el-form-item ref="purPrice" :label="$t('Pur. Price (w/o Tax)')" prop="purPrice">
              <show-or-edit :value="avplInfo.purPrice">
                <vxe-input
                    v-model="avplInfo.purPrice"
                    size="mini"
                    type="number"
                    style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>
            <!-- 物料编码 -->
            <el-form-item ref="partNo" :label="$t('Part No.')">
              <show-or-edit :value="avplInfo.partNo">
                <el-input v-model="avplInfo.partNo"  :placeholder="$t('common.pleaseSelect')"/>
              </show-or-edit>
            </el-form-item>
            <!-- PO币种 -->
            <el-form-item ref="supplierCurrency" :label="$t('Supplier Currency')">
              <show-or-edit :dict="DICT_TYPE.MATERIAL_UOM" :value="avplInfo.supplierCurrency">
                <el-select v-model="avplInfo.supplierCurrency" class="smallSelect" clearable style="width: 100%">
                  <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                      :key="dict.id"
                      :label="dict.name"
                      :value="dict.id"
                  />
                </el-select>
              </show-or-edit>
            </el-form-item>
            <!-- 物料描述 -->
            <el-form-item ref="description" :label="$t('Description')">
              <show-or-edit :value="avplInfo.description">
                <el-input v-model="avplInfo.description"  :placeholder="$t('common.pleaseSelect')"/>
              </show-or-edit>
            </el-form-item>
            <!-- 税率 -->
            <el-form-item ref="tax" :label="$t('Tax%')" prop="tax">
              <show-or-edit :dict="DICT_TYPE.SUPPLIER_RATE" :value="avplInfo.tax">
                <el-select v-model="avplInfo.tax" class="smallSelect" clearable style="width: 100%">
                  <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_RATE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </show-or-edit>
            </el-form-item>
            <!-- 版本 -->
            <el-form-item ref="version" :label="$t('Version')">
              <show-or-edit :value="avplInfo.version">
                <el-input v-model="avplInfo.version" :placeholder="$t('common.pleaseSelect')"/>
              </show-or-edit>
            </el-form-item>

            <!-- 最小订购量 -->
            <el-form-item ref="moq" :label="$t('MOQ')" prop="moq">
              <show-or-edit :value="avplInfo.moq">
                <vxe-input
                    v-model="avplInfo.moq"
                    size="mini"
                    type="number"
                    style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>



            <!-- mfg -->
            <el-form-item ref="mfg" :label="$t('MFG')">
              <show-or-edit :value="avplInfo.mfg">
                <el-input v-model="avplInfo.mfg" :placeholder="$t('common.pleaseSelect')"/>
              </show-or-edit>
            </el-form-item>
            <!-- 最小包装数量 -->
            <el-form-item ref="spq" :label="$t('SPQ')" prop="spq">
              <show-or-edit :value="avplInfo.spq">
                <vxe-input
                    v-model="avplInfo.spq"
                    size="mini"
                    type="number"
                    style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>
            <!-- mpn -->
            <el-form-item ref="mpn" :label="$t('MPN')">
              <show-or-edit :value="avplInfo.mpn">
                <el-input v-model="avplInfo.mpn" :placeholder="$t('common.pleaseSelect')"/>
              </show-or-edit>
            </el-form-item>
            <!-- 付款条款 -->
            <el-form-item ref="paymentTerm" :label="$t('Pay. Term')" prop="paymentTerm">
              <show-or-edit :dict="DICT_TYPE.SUPPLIER_PAYMENT_DAYS" :value="avplInfo.paymentTerm" >
                <el-select v-model="avplInfo.paymentTerm" class="smallSelect" clearable style="width: 100%">
                  <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </show-or-edit>
            </el-form-item>
            <!-- 单位 -->
            <el-form-item ref="uom" :label="$t('UOM')">
              <show-or-edit :dict="DICT_TYPE.MATERIAL_UOM" :value="avplInfo.uom">
                <el-input v-model="avplInfo.uom" />
              </show-or-edit>
            </el-form-item>
            <!-- 交货条款 -->
            <el-form-item ref="deliveryTerm" :label="$t('Del. Term')" prop="deliveryTerm">
              <show-or-edit :value="avplInfo.deliveryTerm">
                <el-input v-model="avplInfo.deliveryTerm" />
              </show-or-edit>
            </el-form-item>
            <!-- SO单价 -->
            <el-form-item ref="soUnitPrice" :label="$t('SO Price')">
              <show-or-edit :value="avplInfo.soUnitPrice">
                <vxe-input
                    v-model="avplInfo.soUnitPrice"
                    size="mini"
                    type="number"
                    style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>
            <!-- 状态 -->
            <el-form-item ref="status" :label="$t('Status')" prop="status">
              <show-or-edit :dict="DICT_TYPE.OM_AVPL_STATUS" :value="avplInfo.status">
                <el-select v-model="avplInfo.status" class="smallSelect" clearable style="width: 100%">
                  <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.OM_AVPL_STATUS)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </show-or-edit>
            </el-form-item>
            <!-- SO币种 -->
            <el-form-item ref="currency" :label="$t('Currency')">
              <show-or-edit :dict="DICT_TYPE.MATERIAL_UOM" :value="avplInfo.currency">
                <el-select v-model="avplInfo.currency" class="smallSelect" clearable style="width: 100%">
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>
              </show-or-edit>
            </el-form-item>
            <!-- 价格阶梯从 -->
            <el-form-item ref="qtyFr" :label="$t('Qty Fr')">
              <show-or-edit :value="avplInfo.qtyFr">
                <vxe-input
                  v-model="avplInfo.qtyFr"
                  size="mini"
                  type="number"
                  style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>
            <!-- 价格阶梯到 -->
            <el-form-item ref="qtyTo" :label="$t('Qty To')" prop="qtyTo">
              <show-or-edit :value="avplInfo.qtyTo">
                <vxe-input
                  v-model="avplInfo.qtyTo"
                  size="mini"
                  type="number"
                  style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>
            <!-- 交货周期 -->
            <el-form-item ref="leadTime" :label="$t('Lead Time (wk)')" prop="leadTime">
              <show-or-edit :value="avplInfo.leadTime">
                <vxe-input
                  v-model="avplInfo.leadTime"
                  size="mini"
                  type="integer"
                  style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>

            <!-- 样品销售订单价格 -->
            <el-form-item ref="sampleSoPrice" :label="$t('Sample SO Price')" prop="sampleSoPrice">
              <show-or-edit :value="avplInfo.sampleSoPrice">
                <el-input v-model="avplInfo.sampleSoPrice" />
              </show-or-edit>
            </el-form-item>

            <!-- 样品采购价格 -->
            <el-form-item ref="samplePurPrice" :label="$t('Sample Pur. Price')" prop="samplePurPrice">
              <show-or-edit :value="avplInfo.samplePurPrice">
                <vxe-input
                  v-model="avplInfo.samplePurPrice"
                  size="mini"
                  type="number"
                  style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>
            <!-- 样品交货周期 -->
            <el-form-item ref="sampleLeadTime" :label="$t('Sample LT (wk)')" prop="sampleLeadTime">
              <show-or-edit :value="avplInfo.sampleLeadTime">
                <vxe-input
                  v-model="avplInfo.sampleLeadTime"
                  size="mini"
                  type="integer"
                  style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>

            <!-- 工装成本 -->
            <el-form-item ref="toolingCost" :label="$t('Tooling Cost')" prop="toolingCost">
              <show-or-edit :value="avplInfo.toolingCost">
                <vxe-input
                  v-model="avplInfo.toolingCost"
                  size="mini"
                  type="number"
                  style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>

            <!-- 工装付款条款 -->
            <el-form-item ref="toolingPaymentTerm" :label="$t('Tooling Pay. Term')" prop="toolingPaymentTerm">
              <show-or-edit :value="avplInfo.toolingPaymentTerm">
                <el-input v-model="avplInfo.toolingPaymentTerm" />
              </show-or-edit>
            </el-form-item>

            <!-- 表面处理 -->
            <el-form-item ref="surfaceTreatment" :label="$t('Surface Treatment')" prop="surfaceTreatment">
              <show-or-edit :value="avplInfo.surfaceTreatment">
                <el-input v-model="avplInfo.surfaceTreatment" />
              </show-or-edit>
            </el-form-item>

            <!-- 有效期起始 -->
            <el-form-item ref="validFr" :label="$t('Valid Fr')" prop="validFr">
              <show-or-edit :value="avplInfo.validFr">
                <el-date-picker
                  style="width: 100%"
                  v-model="avplInfo.validFr"
                  class="commonForm"
                  :placeholder="$t('common.pleaseSelectADate')"
                  type="date"
                  placement="bottom-start"
                  value-format="yyyy-MM-dd"
                />
              </show-or-edit>
            </el-form-item>

            <!-- 有效期结束 -->
            <el-form-item ref="validTo" :label="$t('Valid To')" prop="validTo">
              <show-or-edit :value="avplInfo.validTo">
                <el-date-picker
                  style="width: 100%"
                  v-model="avplInfo.validTo"
                  class="commonForm"
                  :placeholder="$t('common.pleaseSelectADate')"
                  type="date"
                  placement="bottom-start"
                  value-format="yyyy-MM-dd"
                />
              </show-or-edit>
            </el-form-item>

            <!-- 报价日期 -->
            <el-form-item ref="quoteDate" :label="$t('Quote Date')" prop="quoteDate">
              <show-or-edit :value="avplInfo.quoteDate">
                <el-date-picker
                  style="width: 100%"
                  v-model="avplInfo.quoteDate"
                  class="commonForm"
                  :placeholder="$t('common.pleaseSelectADate')"
                  type="date"
                  placement="bottom-start"
                  value-format="yyyy-MM-dd"
                />
              </show-or-edit>
            </el-form-item>
            <!-- 仓库/集散中心 -->
            <el-form-item ref="hub" :label="$t('Hub')" prop="hub">
              <show-or-edit :value="avplInfo.hub">
                <el-input v-model="avplInfo.hub" />
              </show-or-edit>
            </el-form-item>

            <!-- 原产地 -->
            <el-form-item ref="coo" :label="$t('COO')" prop="coo">
              <show-or-edit :value="avplInfo.coo">
                <el-input v-model="avplInfo.coo" />
              </show-or-edit>
            </el-form-item>

            <!-- 是否不可取消、不可退货 -->
            <el-form-item ref="ncnr" :label="$t('NCNR')" prop="ncnr">
              <show-or-edit :value="avplInfo.ncnr">
                <el-input v-model="avplInfo.ncnr" />
              </show-or-edit>
            </el-form-item>

            <!-- 预留窗口期 -->
            <el-form-item ref="resWindow" :label="$t('Res. Window')" prop="resWindow">
              <show-or-edit :value="avplInfo.resWindow">
                <vxe-input
                  v-model="avplInfo.resWindow"
                  size="mini"
                  type="number"
                  style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>

            <!-- 取消窗口期 -->
            <el-form-item ref="canWindow" :label="$t('Can. Window')" prop="canWindow">
              <show-or-edit :value="avplInfo.canWindow">
                <vxe-input
                  v-model="avplInfo.canWindow"
                  size="mini"
                  type="number"
                  style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>

            <!-- 品类 -->
            <el-form-item ref="category" :label="$t('Category')" prop="category">
              <show-or-edit
                :dict="DICT_TYPE.COMMON_CATEGORY"
                :value="avplInfo.category"
              >
                <cascading-category
                  :original-value.sync="avplInfo.category"
                  :multiple="false"
                  style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>

            <!-- 采购经理 -->
            <el-form-item ref="sourcingManager" :label="$t('Sourcing Manager')" prop="sourcingManager">
              <show-or-edit
                :dict="DICT_TYPE.COMMON_USERS"
                :value="avplInfo.sourcingManager"
              >
                <el-select v-model="avplInfo.sourcingManager" class="searchValue" clearable filterable style="width: 100%">
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>
              </show-or-edit>

            </el-form-item>

            <!-- 备注 -->
            <el-form-item ref="remark" :label="$t('Remark')" prop="remark">
              <show-or-edit :value="avplInfo.remark">
                <el-input v-model="avplInfo.remark" />
              </show-or-edit>
            </el-form-item>
            <!-- 开机费用 -->
            <el-form-item ref="startupCost" :label="$t('Startup Cost')">
              <show-or-edit :value="avplInfo.startupCost">
                <vxe-input
                  v-model="avplInfo.startupCost"
                  size="mini"
                  type="number"
                  style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>

            <el-form-item ref="startupQuantity" :label="$t('Startup Qty')">
              <show-or-edit :value="avplInfo.startupQuantity">
                <vxe-input
                  v-model="avplInfo.startupQuantity"
                  size="mini"
                  type="number"
                  style="width: 100%"
                />
              </show-or-edit>
            </el-form-item>
          </el-form>
      </div>
    </common-card>

    <div  class="fixedBottom">
      <el-button plain type="primary" @click="doCancel">{{ $t('common.cancel') }}</el-button>
      <el-button style="margin-left: 20px" type="primary" @click="doSaveSimpleBaseInfo">{{ $t('common.save') }}</el-button>
    </div>
  </div>
</template>


<script>
import Sticky from '@/components/Sticky'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import BpmProcessInstance from '@/components/BpmProcessInstance/index.vue'
import brand from '@/views/supplier/info/brand'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { getSupplierBaseInfo, saveSimpleBaseInfo } from '@/api/supplier/simplesupplier'
import router from "@/router";
import {getAvplInfo, saveAvplInfo} from "@/api/om/avpl";
import { listNewSupplier } from '@/api/om/so'

export default {
  name: 'Avplinfo/:id',
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  components: {
    ShowOrEdit,
    brand,
    Sticky, BpmProcessInstance
  },
  data() {
    return {
      avplInfo: {
        project: '', // 项目
        customer: '', // 客户名称
        factory: '', // 工厂
        partNo: '', // 物料编码
        description: '', // 物料描述
        version: '', // 版本
        mfg: '', // mfg
        mpn: '', // mpn
        uom: '', // 单位
        currency: '', // SO币种
        soUnitPrice: null, // SO单价
        supplierCurrency: '', // PO币种（供应商的币种）
        diff: null, // 差价（(so价格-供应商价格)/so价格）%
        supplierId: '', // 供应商ID
        supplier: '', // 供应商名称
        vendorCode: '', // 供应商编码（vendor_id）
        qtyFr: null, // 价格阶梯从
        qtyTo: null, // 价格阶梯到
        purPrice: null, // 供应商价格
        tax: null, // 税率%
        spq: null, // 标准（最小）包装数量
        moq: null, // 最小订购量
        leadTime: null, // 交货周期 (周)
        sampleSoPrice: null, // 样品销售订单价格
        samplePurPrice: null, // 样品采购价格
        sampleLeadTime: null, // 样品交货周期 (周)
        toolingCost: null, // 工装成本
        toolingPaymentTerm: '', // 工装付款条款
        surfaceTreatment: '', // 表面处理
        validFr: null, // 有效期起始
        validTo: null, // 有效期结束
        quoteDate: null, // 报价日期
        deliveryTerm: '', // 交货条款
        paymentTerm: '', // 付款条款
        hub: '', // 仓库/集散中心
        coo: '', // 原产地
        ncnr: '', // 是否不可取消、不可退货 (NCNR)
        resWindow: null, // 预留窗口期
        canWindow: null, // 取消窗口期
        category: '', // 品类
        sourcingManager: '', // 采购经理
        status: 'Valid', // 状态
        remark: '', // 备注
        startupCost:null,//开机费用
        startupQuantity:null//开机数量
      },
      avplInfoRule: {
        //INCAP-339 AVPL期望如果没有输入物料编码，生产一个临时物料编码
        //partNo: [{ required: true, message: this.$t('Part No. required'), trigger: 'blur' }],
        customer: [{ required: true, message: this.$t('Customer required'), trigger: 'blur' }],
        supplierId: [{ required: true, message: this.$t('Supplier required'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('Status required'), trigger: 'blur' }],
        purPrice: [{ required: true, message: this.$t('Pur Price required'), trigger: 'blur' }]
      },
      canAuthSuppliers: []
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    router() {
      return router
    },
    getDictDatas,
    init() {
      if (this.$route.params.id !== '0') {
        getAvplInfo(this.$route.params.id).then(res => {
          this.avplInfo = res.data
          listNewSupplier(this.avplInfo.supplier).then(res => {
            this.canAuthSuppliers = res.data
          })
        })

      }
    },
    // 新增供应商数据保存
    doSaveSimpleBaseInfo() {
      this.$refs['avplInfo'].validate(valid => {
        this.avplInfo.supplier = this.canAuthSuppliers.find(item => item.id === this.avplInfo.supplierId).name
        if (valid) {

          saveAvplInfo(this.avplInfo).then(res=>{
            this.$modal.msgSuccess(this.$t('common.savedSuccessfully'))
            // 新增对象后刷新页面路由，供应商名称作为tag名称
            // 编辑对象不需要刷新页面路由
            if (this.$route.params.id === '0') {
              this.$tab.closeOpenPage({ path: `/om/avplinfo/${res.data.id}?no=${res.data.partNo}` })
            }
          })
        }
      })
    },
    doListNewAuthSupplier(query) {
      if (query) {
        listNewSupplier(query).then(res => {
          this.canAuthSuppliers = res.data
        })
      }
    },
    clearSupplier() {
      this.canAuthSuppliers = []
      this.avplInfo.supplierId = ''
      this.avplInfo.supplier = ''
    },
    // 取消按钮
    doCancel() {
      this.$tab.closeOpenPage('/om/avpl')
    }
  }
}
</script>

<style lang="scss" scoped>
.baseInfo {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;

    .el-form-item__content {
      width: calc(100% - 200px);
    }
  }
}

.associatedCompany {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}

.shadow {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}

.supplierStaff {
  ::v-deep .el-form-item {
    width: 100%;
    margin-right: 0;
    padding-right: 10px;
  }
}

::v-deep .el-input__inner {
  color: #606266 !important;
  //background: #ffffff!important;
}

.verticalMiddle {
  display: flex;
  align-items: center
}

::v-deep .descriptionLabel {
  width: 145px;
  font-weight: bold;
  display: inline-block;
  text-align: right;
}

.upload p {
  line-height: 12px;
}

.required:before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

.supplier {
  .tableTitle {
    padding-left: 70px;
    font-size: 15px;
    font-weight: bold;
    margin: 20px 0;
  }

  .tablePadding {
    padding: 0 20px
  }

  .shadowPadding {
    padding: 5px 0;
    margin: 15px
  }

  .supplier-progress {
    height: 3px;
    width: 50%;
    background-color: #323232;
  }

  margin: 0 auto;
  width: 1000px;
  background: #c1c1c1;
  padding: 20px;

  .active {
    background-color: #4996b8;
    color: #fff
  }

  &-header {
    font-size: 16px;
    width: 100%;
    padding-bottom: 25px;
    margin-bottom: 20px;

    &-right {
      text-align: right;
      font-size: 12px;
      color: #173b75;
      margin-right: 20px
    }
  ;

    &-btn {
      cursor: pointer;
      padding: 10px 15px;
      display: inline-block;
      background-color: #cecece;
      color: #fff
    }
  ;
  }

  .smallInput {
    width: 188px;
  }

  .smallSelect {
    width: 188px;
  }

  .bigBaseInput {
    width: 533px
  }

  .bigInput {
    width: 591px
  }

  .colCenter {
    text-align: center;
  }

  .form-title {
    border-left: 6px solid #376092;
    margin: 10px 0;
    padding: 10px 30px;
    font-size: 16px;
    font-weight: bold;
    background-color: #f1f1f1;
  }

  // 上传输入框的文件列表样式调整#文字加粗
  ::v-deep .el-upload-list__item-name {
    font-weight: 600;
  }

  .centerRow {
    ::v-deep .el-form-item__content {
      width: calc(100% - 139px);
      text-align: center;
    }
  }

  .ellipsis-line {
    width: 248px;
    overflow: hidden;
    text-overflow: ellipsis; //文本溢出显示省略号
    white-space: nowrap; //文本不会换行
  }

  .shadow {
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }

  .rawMaterialFlex {
    flex: 0 1 15%;
    text-align: center;
  }

  .customItem {
    flex: 0 1 25%;
    text-align: center;
  }

  .form-main {
    .product {
      display: flex;
      min-height: 150px;

      .product-left {
        flex: 0 1 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 5px 10px;

        .product-title {
          flex: none;
          font-weight: bold;
          text-align: center;
          word-break: break-all;
          font-size: 20px;
          margin: 10px 0;
        }

        .product-detail {
          word-break: break-all;
          flex: auto;
          text-align: center;
        }
      }

      .product-right {
        justify-content: center;
        align-items: center;
        flex: 0 1 50%;
        border-left: 1px #dcdcdc solid;
        display: flex;
        flex-wrap: wrap;
      }
    }

  }

  .fixedBottom {
    position: fixed;
    width: calc(100% - 231px);
    bottom: 20px;
    display: flex;
    justify-content: center;
    margin-top: 40px;
    right: 30px;
  }
}
</style>
