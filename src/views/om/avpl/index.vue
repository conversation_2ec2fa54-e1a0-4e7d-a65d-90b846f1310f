<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.search"
        :placeholder="$t('Please enter P/N,MFG,MPN,Description')"
        clearable
        style="flex: 0 1 35%"
        @keyup.enter.native="handleQuery"
      />
      <!-- 搜索 -->
      <el-button plain type="primary" @click="handleQuery">
        {{ $t('common.search') }}
      </el-button>
      <!-- 重置 -->
      <el-button
        style="margin-left: 0"
        size="mini"
        @click="resetQuery"
      >
        {{ $t('common.reset') }}
      </el-button>
      <!-- 高级搜索 -->
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button type="text">
          {{ $t('common.advancedSearch') }}
        </el-button>
        <i
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>
    </div>
    <!-- 高级搜索的form-->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="130px" size="small">
      <!-- P/N-->
      <el-form-item :label="$t('P/N')" class="searchItem">
        <el-input v-model="queryParams.partNo" :placeholder="$t('Please enter P/N')" clearable />
      </el-form-item>
      <!-- MFG-->
      <el-form-item :label="$t('MFG')" class="searchItem">
        <el-input v-model="queryParams.mfg" :placeholder="$t('Please enter MFG')" clearable />
      </el-form-item>
      <!-- MPN-->
      <el-form-item :label="$t('MPN')" class="searchItem">
        <el-input v-model="queryParams.mpn" :placeholder="$t('Please enter MPN')" clearable />
      </el-form-item>
      <!-- Supplier-->
      <el-form-item :label="$t('Supplier')" class="searchItem">
        <el-input v-model="queryParams.supplier" :placeholder="$t('Please enter supplier')" clearable />
      </el-form-item>
      <!-- Category-->
      <el-form-item :label="$t('Category')" class="searchItem">
        <cascading-category
          class="searchValue"
          style="width:100%"
          :original-value.sync="queryParams.categoryIds"
        />
      </el-form-item>
      <!-- Status-->
      <el-form-item :label="$t('Status')" class="searchItem">
        <el-select
          v-model="queryParams.levelStatus"
          class="searchValue"
          clearable
          filterable
          multiple
          style="width:100%"
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.OM_AVPL_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- Payment. Term-->
      <el-form-item class="searchItem" :label="$t('Payment. Term')">
        <el-select v-model="queryParams.paymentTerm" class="searchValue" clearable style="width:100%">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('Customer')" prop="customer" style="width: 66%">
        <el-radio-group v-model="queryParams.customer" @change="getList();">
          <el-radio :label="null">{{ $t('order.whole') }}</el-radio>
          <el-radio v-for="customer in $store.getters.incapCustomers" :key="customer" :label="customer">
            {{ customer }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <!-- 操作工具栏 -->
    <div style="padding-top: 20px">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5" />
        <el-col :span="1.5">
          <!-- 新增avpl-->
          <el-button
            v-hasPermi="['om:avpl-master:create']"
            :loading="exportLoading"
            icon="el-icon-plus"
            size="mini"
            type="primary"
            @click="$router.push(`/om/avplinfo/0`)"
          >{{ $t('新增AVPL') }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <!-- 新增avpl-->
          <el-button
            v-hasPermi="['om:avpl-master:delete']"
            :loading="exportLoading"
            icon="el-icon-delete"
            size="mini"
            type="primary"
            @click="doDelAvpl"
          >{{ $t('删除AVPL') }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['om:avpl-master:import']"
            :loading="exportLoading"
            icon="el-icon-upload2"
            size="mini"
            type="primary"
            plain
            @click="handleImport"
          >{{ $t('common.batchCreation') }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['om:avpl-master:query']"
            plain
            :loading="exportLoading"
            :title="$t('supplier.clickToExportAllTheDataDisplayedInTheList')"
            icon="el-icon-download"
            size="mini"
            type="primary"
            @click="handleExport"
          >{{ $t('common.batchExport') }}
          </el-button>
        </el-col>
        <el-tooltip :content="$t('supplier.clickToExportAllTheDataDisplayedInTheList')" placement="top-start" />
        <right-toolbar
          :custom-columns.sync="girdOption.columns"
          :list-id="girdOption.id"
          :show-search.sync="showSearch"
          :only-custom="false"
          @queryTable="getList"
        />
      </el-row>
    </div>

    <!-- 列表 -->
    <vxe-grid
      ref="avplTable"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
    >
      <template #currency="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.currency" />
      </template>
      <template #supplierCurrency="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.supplierCurrency" />
      </template>
      <template #tax="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_RATE" :value="row.tax" />
      </template>
      <template #sourcingManager="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcingManager" />
      </template>
<!--      <template #deliveryType="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION" :value="row.deliveryTerm" />
      </template>-->
      <template #paymentType="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_PAYMENT_DAYS" :value="row.paymentTerm" />
      </template>
      <template #category="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.category" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.OM_AVPL_STATUS" :value="row.status" />
      </template>
      <template #partNo="{row}">
        <copyButton v-if="$store.getters.permissions.includes('om:avpl-master:create')" type="text" @click="$router.push(`/om/avplinfo/${row.id}?no=${row.partNo}`)">{{ row.partNo }}</copyButton>
        <span v-else>{{ row.partNo }}</span>
      </template>
    </vxe-grid>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 供应商导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url"
          :auto-upload="false"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="doDownloadTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" :loading="upload.isUploading" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getBaseHeader } from '@/utils/request'
import { exportAvplInfoExcel, getAvplPage, getImportAvplTemplate } from '@/api/om/avpl'
import {delAvplInfo} from "../../../api/om/avpl";

export default {
  name: 'Avpl',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 供应商列表
      list: [],
      // 弹出层标题
      title: '',
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        // 顶部模糊搜索条件
        search: '',
        categoryIds: [],
        partNo: '',
        mfg: '',
        mpn: '',
        supplier: '',
        deliveryTerm: '',
        paymentTerm: '',
        customer:'',
        levelStatus: ['Valid']
      },
      // 供应商导入参数
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/om/avpl-master/import-template'
      },
      // 表单参数
      form: {},
      girdOption: {
        id: 'avplTableGirdOption',
        align: 'left',
        minHeight: 30,
        border: true,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isCurrent: true,
          isHover: true
        },
        keepSource: false,
        columns: [
          { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
          { field: 'project', title: this.$t('Project'), visible: true, width: 170 }, // 项目
          { field: 'customer', title: this.$t('Customer'), visible: true, width: 170, fixed: 'left' }, // 客户名称
          { field: 'factory', title: this.$t('Factory'), visible: true, width: 170 }, // 工厂
          { field: 'partNo', title: this.$t('Part Number'), visible: true, width: 170, slots: { default: 'partNo' }, fixed: 'left' }, // 物料编码
          { field: 'description', title: this.$t('Description'), visible: true, width: 200 }, // 物料描述
          { field: 'version', title: this.$t('Version'), visible: true, width: 100 }, // 版本
          { field: 'mfg', title: this.$t('MFG'), visible: true, width: 150 }, // mfg
          { field: 'mpn', title: this.$t('MPN'), visible: true, width: 150 }, // mpn
          { field: 'uom', title: this.$t('UOM'), visible: true, width: 100 }, // 单位
          { field: 'currency', title: this.$t('SO Currency'), visible: true, width: 120, slots: { default: 'currency' }}, // SO币种
          { field: 'soUnitPrice', title: this.$t('SO Price'), visible: true, width: 120 ,align: 'right'}, // SO单价
          { field: 'supplierCurrency', title: this.$t('Supplier Currency'), visible: true, width: 150, slots: { default: 'supplierCurrency' }}, // PO币种
          { field: 'diff', title: this.$t('Diff%'), visible: true, width: 120,align: 'right' }, // 差价
          { field: 'supplier', title: this.$t('Supplier'), visible: true, width: 150, fixed: 'left' }, // 供应商名称
          { field: 'vendorCode', title: this.$t('Vendor Code'), visible: true, width: 150 }, // 供应商编码
          { field: 'qtyFr', title: this.$t('Qty Fr'), visible: true, width: 120 }, // 价格阶梯从
          { field: 'qtyTo', title: this.$t('Qty To'), visible: true, width: 120 }, // 价格阶梯到
          { field: 'purPrice', title: this.$t('Pur. Price (w/o Tax)'), visible: true, width: 150,align: 'right' }, // 供应商价格
          { field: 'tax', title: this.$t('Tax%'), visible: true, width: 120, slots: { default: 'tax' }}, // 税率%
          { field: 'spq', title: this.$t('SPQ'), visible: true, width: 120,align: 'right' }, // 标准包装数量
          { field: 'moq', title: this.$t('MOQ'), visible: true, width: 120,align: 'right' }, // 最小订购量
          { field: 'leadTime', title: this.$t('Lead Time (wk)'), visible: true, width: 150 }, // 交货周期
          { field: 'sampleSoPrice', title: this.$t('Sample SO Price'), visible: true, width: 150 ,align: 'right'}, // 样品销售订单价格
          { field: 'samplePurPrice', title: this.$t('Sample Pur. Price'), visible: true, width: 150 ,align: 'right'}, // 样品采购价格
          { field: 'sampleLeadTime', title: this.$t('Sample LT (wk)'), visible: true, width: 150 }, // 样品交货周期
          { field: 'toolingCost', title: this.$t('Tooling Cost'), visible: true, width: 150 }, // 工装成本
          { field: 'toolingPaymentTerm', title: this.$t('Tooling Pay. Term'), visible: true, width: 170 }, // 工装付款条款
          { field: 'surfaceTreatment', title: this.$t('Surface Treatment'), visible: true, width: 150 }, // 表面处理
          { field: 'validFr', title: this.$t('Valid Fr'), visible: true, width: 150 }, // 有效期起始
          { field: 'validTo', title: this.$t('Valid To'), visible: true, width: 150 }, // 有效期结束
          { field: 'quoteDate', title: this.$t('Quote Date'), visible: true, width: 150 }, // 报价日期
          { field: 'deliveryTerm', title: this.$t('Del. Term'), visible: true, width: 150}, // 交货条款
          { field: 'paymentTerm', title: this.$t('Pay. Term'), visible: true, width: 150, slots: { default: 'paymentType' }}, // 付款条款
          { field: 'hub', title: this.$t('Hub'), visible: true, width: 150 }, // 仓库/集散中心
          { field: 'coo', title: this.$t('COO'), visible: true, width: 150 }, // 原产地
          { field: 'ncnr', title: this.$t('NCNR'), visible: true, width: 100 }, // 是否不可取消、不可退货
          { field: 'resWindow', title: this.$t('Res. Window'), visible: true, width: 150 }, // 预留窗口期
          { field: 'canWindow', title: this.$t('Can. Window'), visible: true, width: 150 }, // 取消窗口期
          { field: 'category', title: this.$t('Category'), visible: true, width: 150, slots: { default: 'category' }}, // 品类
          { field: 'sourcingManager', title: this.$t('Sourcing Manager'), visible: true, width: 150, slots: { default: 'sourcingManager' }}, // 采购经理
          { field: 'status', title: this.$t('Status'), visible: true, width: 120, slots: { default: 'status' }}, // 状态
          { field: 'remark', title: this.$t('Remark'), visible: true, width: 200 },// 备注
          { field: 'startupCost', title: this.$t('Startup Cost'), visible: true, width: 150,align: 'right' },
          { field: 'startupQuantity', title: this.$t('Startup Qty'), visible: true, width: 150,align: 'right' },
        ],
        sortConfig: {
          remote: true
        },
        slots: {
          // 自定义工具栏模板
          buttons: 'toolbar_buttons'
        }
      }
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getDictDatas,
    /**
     * 删除avpl（逻辑删除）支持多选
     */
    doDelAvpl() {
      const selectRows = this.$refs.avplTable.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to del'))
        return false
      }
      var avplIds = selectRows.map(i => i.id)
      this.$modal.confirm('是否确认删除所选的数据项?').then(function() {
        delAvplInfo({ avplIds: avplIds.join(',') })
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getAvplPage({ ...this.queryParams }).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$modal.confirm(this.$t('是否确认导出AVPL数据项?')).then(() => {
        this.exportLoading = true
        return exportAvplInfoExcel(queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('AVPL导出数据.xlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.create) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.create.length
      }
      if (data.failure) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failure).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    /** 批量创建 */
    handleImport() {
      this.upload.title = this.$t('common.batchCreation')
      this.upload.open = true
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        // 顶部模糊搜索条件
        search: '',
        categoryIds: [],
        partNo: '',
        mfg: '',
        mpn: '',
        supplier: '',
        deliveryTerm: '',
        paymentTerm: '',
        customer:'',
        levelStatus: ['Valid']
      }
      this.handleQuery()
    },
    handleUpload() {
      this.uploadVisible = false
      this.showUpload = false
      this.$refs.upload.submit()
    },
    /** 下载模板操作 */
    doDownloadTemplate() {
      getImportAvplTemplate().then(res => {
        this.$download.excel(res, this.$t('AVPL上传模板.xlsx'))
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.realTab {
  width: 220px;
  height: 90px;
  font-size: 16px;
  background: rgb(242, 244, 249);
  color: #000000;
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding: 21px;
}
.realNum{
  color: #327da1;
  font-size: 25px;
}
.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 130px);
  }
}

.searchValue {
  width: 80%;
}
.scale-leave-active {
  -webkit-animation: scale-down-tr 0.4s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  animation: scale-down-tr 0.4s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}

.scale-enter-active {
  -webkit-animation: scale-up-tr 0.4s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
  animation: scale-up-tr 0.4s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
}

@-webkit-keyframes scale-up-tr {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@keyframes scale-up-tr {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@-webkit-keyframes scale-down-tr {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@keyframes scale-down-tr {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

</style>
