
<template>
  <div class="app-container">
    <div class="stats-container">
      <el-card v-for="(stat, index) in stats" :key="index" shadow="hover">
        <div style="font-size: 24px; color: #409EFF; font-weight: bold;">
          $  {{ statList[stat.field] || 0 }}
        </div>
        <div style="color: #909399; margin-top: 8px;">
          {{ stat.label }}
        </div>
      </el-card>

    </div>
    <el-card style="margin-top: 12px">
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.search"
          style="flex: 0 1 35%"
          :placeholder="$t('SO NO#/Customer/Buyer/Part No.')"
          @keyup.enter.native="getListInit"
        />
        <el-button plain type="primary" @click="getListInit">
          {{ $t('common.search') }}
        </el-button>
        <el-button
          style="margin-left: 0"
          @click="handleReset"
        >
          {{ $t('common.reset') }}
        </el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>
      </div>
      <el-form v-show="showSearch" inline label-width="120px">

        <el-form-item class="searchItem" :label="$t('AP NO')" prop="no">
          <el-input v-model="queryParams.apNo" placeholder="请输入" />

        </el-form-item>

        <el-form-item class="searchItem" :label="$t('供应商')" prop="">
          <el-autocomplete
            v-model="queryParams.supplier"
            :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'supplier')})"
            :placeholder="$t('Supplier')"
            :popper-append-to-body="false"
            popper-class="el-autocomplete-suggestion"
            class="overList"
            @select="((item) => {handleSelect(item, 'supplier')})"
            @keyup.enter.native="getListInit"
          />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('资金用途')" prop="">
          <el-select v-model="queryParams.fundsUsage" class="searchValue" clearable filterable>
            <el-option v-for="item in getDictDatas(DICT_TYPE.FUNDS_USAGE)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="入账日期" class="searchItem">
          <el-date-picker
            v-model="queryParams.time"
            class="searchValue"
            value-format="yyyy-MM-dd"
            type="daterange"
            :range-separator="$t('order.to')"
            :start-placeholder="$t('common.startDate')"
            :end-placeholder="$t('common.endDate')"
          />
        </el-form-item>

        <el-form-item class="searchItem" :label="$t('客户名称')" prop="customer" style="width: 66%">
          <el-radio-group v-model="queryParams.customer" @change="getListInit();">
            <el-radio :label="null">{{ $t('order.whole') }}</el-radio>
            <el-radio v-for="customer in $store.getters.incapCustomers" :key="customer" :label="customer">
              {{ customer }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <vxe-grid
        ref="cash"
        :data="list"
        :loading="loading"
        v-bind="soOverviewGrid"
      >

        <template #dict="{column,row}">
          <dict-tag :type="column.params.dict" :value="row[column.field]" />
        </template>
        <template #cashNo="{row}">
          <copy-button @click="$router.push(`/capital/cashDetail/${row.id}?no=${row.cashNo}`)">{{ row.cashNo }}</copy-button>
        </template>
        <template #apNo="{row}">
          <copy-button @click="$router.push(`/capital/payDetail/${row.apId}?no=${row.apNo}`)">{{ row.apNo }}</copy-button>
        </template>
        <template #operate="{row}">
          <OperateDropDown
            :menu-item="[
              {
                name: $t('编辑'),
                show: true,
                action: (row) => handleEdit(row),
                para: row
              },
              {
                name: $t('附件'),
                show: true,
                action: (row) => handleShowFile(row),
                para: row
              },
              {
                name: $t('om.ChangeLog'),
                show: true,
                action: (row) => showLogs(row.id,'om_cash_balance_master',['customer','supplier','transactionType','currency','amount','fundsUsage'],['cashNo']),
                para: row
              },
            ]"
          />
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="24" style="width: 100%" class="mb8">
            <el-col :span="22" style="display: flex">

              <el-button size="mini" plain type="primary" @click="dialogVisible = true;paymentForm.id=null;">{{ $t('登记现金') }}</el-button>
              <el-button size="mini" plain :loading="uploadLoading" @click="exportExcel">{{ $t('导出明细') }}</el-button>
            </el-col>
            <el-col :span="2">
              <right-toolbar
                :list-id="soOverviewGrid.id"
                :show-search.sync="showSearch"
                :custom-columns.sync="soOverviewGrid.columns"
                @queryTable="getListInit"
              />
            </el-col>
          </el-row>
        </template>
      </vxe-grid>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getList()"
      />
    </el-card>

    <el-dialog
      title="登记客户付款"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form ref="paymentForm" :model="paymentForm" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customer">
              <el-select v-model="paymentForm.customer" placeholder="请选择客户" style="width: 100%">
                <el-option
                  v-for="item in $store.getters.incapCustomers"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交易类型" prop="transactionType">
              <el-select v-model="paymentForm.transactionType" placeholder="请选择" style="width: 100%">
                <el-option v-for="item in getDictDatas(DICT_TYPE.TRANSACTION_TYPE)" :key="item.value" :label="item.label" :value="item.value" />

              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="金额" prop="amount">
              <el-input-number
                v-model="paymentForm.amount"
                :precision="2"
                :step="100"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="币种" prop="currency">
              <el-select v-model="paymentForm.currency" placeholder="请选择" style="width: 100%">
                <el-option v-for="item in getDictDatas(DICT_TYPE.COMMON_CURRENCY)" :key="item.id" :label="item.name" :value="item.id" />

              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="入账日期" prop="transactionDate">
              <el-date-picker
                v-model="paymentForm.transactionDate"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="摘要" prop="abstractContent">
              <el-input v-model="paymentForm.abstractContent" placeholder="请输入摘要" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资金用途" prop="fundsUsage">
              <el-select v-model="paymentForm.fundsUsage" placeholder="请选择" style="width: 100%">
                <el-option v-for="item in getDictDatas(DICT_TYPE.FUNDS_USAGE)" :key="item.value" :label="item.label" :value="item.value" />

              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input
            v-model="paymentForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
        <el-form-item label="附件">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="headers"
            :on-preview="onPreview"
            :on-success="(response, file, fileList)=>onSuccess(response, file)"
            :on-remove="(file,fileList)=>onRemove(file,fileList)"
            multiple
            :limit="5"
            :file-list="paymentForm.cashBalanceFileList"
          >
            <el-button
              class="uploadBtn"
              size="small"
              plain
              icon="el-icon-plus"
              type="primary"
            />
          </el-upload>

        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPayment()">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      v-if="showFile"
      :visible.sync="showFile"
      :title="$t('scar.viewAttachments')"
      width="400px"
    >
      <el-upload
        class="upload-show"
        disabled
        :action="uploadUrl"
        :on-preview="onPreview"
        multiple
        :limit="5"
        :file-list="fileList"
      />
      <div slot="footer">
        <el-button type="primary" @click="showFile=false">{{ $t('order.determine') }}</el-button>
      </div>

    </el-dialog>
    <!--操作记录-->
    <el-dialog
      v-if="log.open"
      :close-on-click-modal="false"
      :title="$t('common.operationRecord')"
      :visible.sync="log.open"
      width="1000px"
    >
      <operation-record
        :business-id="log.businessId"
        :columns="log.columns"
        :log-visible.sync="log.open"
      />
    </el-dialog>
  </div>
</template>
<script>
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { DICT_TYPE } from '@/utils/dict'
import { parseTime } from '@/utils/ruoyi'
import {
  exportCashBalanceMasterExcel,
  getCashBalanceMasterPage,
  getCashStatistics,
  saveCashBalanceMaster
} from '@/api/om/capital'
import { getSearchResult } from '@/api/om/orderTracker'
import { getBaseHeader } from '@/utils/request'
import operationRecord from "@/components/OperationRecord/operationRecord.vue";

export default {
  name: 'Cash',
  components: {operationRecord, OperateDropDown },
  data() {
    return {
      showFile: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      headers: getBaseHeader(),
      stats: [
        { label: '已收入', field: 'totalYtdIncome' },
        { label: '已支出', field: 'totalYtdExpenditure' },
        { label: '客户不承担费用总计', field: 'totalYtdCustomerNotResponsible' },
        { label: '总剩余', field: 'totalBalance' }
      ],
      statList: {},
      queryParams: {
        search: '',
        apNo: '',
        supplier: '',
        fundsUsage: '',
        beginDate: '',
        endDate: '',
        customer: null,
        pageNo: 1,
        pageSize: 10,
        time: []
      },
      // 操作日志
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: []
      },
      total: 0,
      list: [],
      showSearch: false,
      uploadLoading: false,
      loading: false,
      soOverviewGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'cash',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
          { title: this.$t('现金流水编号'), field: 'cashNo', slots:{ default: 'cashNo'}, visible: true, width: 150 },
          { title: this.$t('AP NO'), field: 'apNo', slots:{ default: 'apNo'}, visible: true, width: 150 },
          { title: this.$t('客户'), field: 'customer', visible: true, width: 150 },
          { title: this.$t('供应商'), field: 'supplier', visible: true, width: 150 },
          { title: this.$t('交易时间'), field: 'transactionDate',
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true, width: 150 },
          { title: this.$t('交易类型'), field: 'transactionType', slots: { default: 'dict' },
            params: { dict: DICT_TYPE.TRANSACTION_TYPE }, visible: true, width: 150 },
          { title: this.$t('收入'), field: 'totalIncome', visible: true, width: 150 },
          { title: this.$t('支出'), field: 'totalExpenditure', visible: true, width: 150 },
          { title: this.$t('余额'), field: 'balance', visible: true, width: 150 },
          { title: this.$t('摘要'), field: 'abstractContent', visible: true, width: 300 },
          { title: this.$t('资金用途'), field: 'fundsUsage', slots: { default: 'dict' }, params: { dict: DICT_TYPE.FUNDS_USAGE }, visible: true, width: 200 },
          { title: this.$t('备注'), field: 'remarks', visible: true, width: 200 },
          {
            field: 'operate',
            slots: { default: 'operate' }, fixed: 'right',
            title: this.$t('common.operate'), visible: true, width: 35, showOverflow: false
          }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      dialogVisible: false,
      paymentForm: {
        cashNo: '',
        customer: '',
        supplierId: null,
        supplier: '',
        amount:null,
        transactionDate: '',
        transactionType: '',
        abstractContent: '',
        currency: 3,
        fundsUsage: '',
        remarks: '',
        cashBalanceFileList: [
        ]
      },
      rules: {
        customer: [
          { required: true, message: '请选择客户', trigger: 'change' }
        ],
        transactionType: [
          { required: true, message: '请选择交易类型', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入金额', trigger: 'blur' }
        ],
        currency: [
          { required: true, message: '请选择币种', trigger: 'change' }
        ],
        transactionDate: [
          { required: true, message: '请选择入账日期', trigger: 'change' }
        ],
        abstractContent: [
          { required: true, message: '请输入摘要', trigger: 'blur' }
        ],
        fundsUsage: [
          { required: true, message: '请选择资金用途', trigger: 'change' }
        ]
      },
      fileList: []
    }
  },
  mounted() {
    this.getStatistic()
    this.getListInit()
  },
  methods: {
    getStatistic() {
      getCashStatistics().then(res => {
        this.statList = res.data
        for (const key in res.data) {
          if (res.data[key] !== '***' && typeof res.data[key] === 'number') {
            res.data[key] = Math.floor(res.data[key]).toLocaleString()
          }
        }
      })
    },
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      getCashBalanceMasterPage(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.queryParams = {
        search: '',
        apNo: '',
        supplier: '',
        fundsUsage: '',
        beginDate: '',
        endDate: '',
        customer: '',
        pageNo: 1,
        pageSize: 10,
        time: []
      }
      this.getListInit()
    },
    handleSelect(item, type) {
      this.queryParams.supplier = item.value
    },
    querySearchAsync(queryString, cb, type) {
      if (queryString.length < 2) {
        return
      }
      getSearchResult({
        searchValue: queryString,
        type: type
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item
          }
        }))
      })
    },

    exportExcel() {
      this.$modal.confirm(this.$t('Do you confirm the download?')).then(() => {
        this.exportLoading = true
        return exportCashBalanceMasterExcel(this.queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('cash.xlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    submitPayment() {
      this.$refs.paymentForm.validate((valid) => {
        if (valid) {
          saveCashBalanceMaster(this.paymentForm).then(res => {
            this.paymentForm = {
              customerName: '',
              type: '',
              amount: 0,
              currency: 3,
              date: '',
              summary: '',
              purpose: '',
              remarks: ''
            }
            this.dialogVisible = false
            this.$message.success('操作成功')
            this.getListInit()
          })
        }
      })
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.fileUrl) {
        window.open(file.fileUrl)
      }
    },
    onSuccess(response, file) {
      if (response.code === 0) {
        this.paymentForm.cashBalanceFileList.push({ name: response.data.name, url: response.data.url, id: response.data.id })
      }
    },
    onRemove(file, fileList) {
      this.paymentForm.cashBalanceFileList.splice(this.paymentForm.cashBalanceFileList.find(a => a.id === file.id), 1)
    },
    handleShowFile(row) {
      if (row.files?.length) {
        row.files.forEach(a => {
          a.name = a.fileName
          a.url = a.fileUrl
        })
        this.fileList = row.files
        this.showFile = true
      } else {
        this.$message.error('暂无附件')
      }
    },
    handleEdit(row) {
      if (row.files?.length) {
        row.files.forEach(a => {
          a.name = a.fileName
          a.url = a.fileUrl
        })
        row.cashBalanceFileList = row.files
      } else {
        row.cashBalanceFileList = []
      }
      this.paymentForm = row
      this.dialogVisible = true
    },
    // show operate log
    showLogs(businessId, table, column,keyColumns) {
      this.log.open = true
      this.log.businessId = businessId
      this.log.columns = [{
        tableName: table,
        searchColumns: column,
        keyColumns:keyColumns
      }]
    }

  }
}
</script>

<style scoped lang="scss">
.stats-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}
.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}
.searchValue {
  width: 100%;
}

</style>
