
<template>

  <div class="app-container">
    <!-- 基本信息 -->
    <common-card  style="margin-bottom: 20px;">
      <div slot="header">
        <span>基本信息</span>
      </div>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="单据编号">{{baseInfo.cashNo}}</el-descriptions-item>
        <el-descriptions-item label="单据日期">{{baseInfo.transactionDate}}</el-descriptions-item>
        <el-descriptions-item label="交易类型">
          <dict-tag :type="DICT_TYPE.TRANSACTION_TYPE" :value="baseInfo.transactionType" />
        </el-descriptions-item>
        <el-descriptions-item label="交易金额">
          <span style="color: #67C23A">{{baseInfo.amount}}</span>
        </el-descriptions-item>
        <el-descriptions-item label="交易对手方">{{baseInfo.customer}}</el-descriptions-item>
      </el-descriptions>
    </common-card>

    <!-- 订单明细 -->
    <common-card >
      <div slot="header">
        <span>订单明细</span>
      </div>
      <el-table :data="allocationData" border style="width: 100%">
        <el-table-column prop="soNo" label="SO NO"></el-table-column>
        <el-table-column prop="poNo" label="PO NO"></el-table-column>
        <el-table-column prop="partNo" label="Part NO"></el-table-column>
        <el-table-column prop="supplier" label="Supplier"></el-table-column>
        <el-table-column prop="shippingDate" label="shipping date"></el-table-column>
        <el-table-column prop="shippingQuantity" label="Shipping Quantity" align="right"></el-table-column>
        <el-table-column prop="unitPrice" label="U/P" align="right">
          <template slot-scope="scope">
            <span>{{scope.row.unitPrice}} </span>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="Amount" align="right">
          <template slot-scope="scope">
            <span>{{scope.row.amount}}</span>
          </template>
        </el-table-column>
      </el-table>
    </common-card>
  </div>
</template>
<script>
import { getCashBalanceDetailList, getCashBalanceMaster } from '@/api/om/capital'

export default {
  name: 'Cashdetail/:id',
  data() {
    return {
      allocationData: [],
      baseInfo:{
        cashNo: "",
        apId: null,
        apNo: null,
        customer: "",
        supplier: "",
        transactionDate: null,
        transactionType: "",
        totalIncome: null,
        totalExpenditure: null,
        balance: null,
        amount: null,
        abstractContent: "",
        fundsUsage: "",
        remarks: "",
        files: [
        ]
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      getCashBalanceDetailList({balanceId:this.$route.params.id}).then(res=>{
        this.allocationData = res.data
      })
      getCashBalanceMaster({id:this.$route.params.id}).then(res=>{
        this.baseInfo = res.data
      })
    },

  }
}
</script>

<style scoped lang="scss">

</style>
