<template>
  <div class="app-container">
    <common-card title="基本信息">
      <el-descriptions :column="3" border>
        <el-descriptions-item label="AP NO">{{ basicInfo.apNo }}</el-descriptions-item>
        <el-descriptions-item label="总金额">
          {{ basicInfo.totalAmount }} {{ basicInfo.currency }}
        </el-descriptions-item>
        <el-descriptions-item label="供应商数">{{ basicInfo.supplierCount }}</el-descriptions-item>
        <el-descriptions-item label="日期">{{ basicInfo.createTime }}</el-descriptions-item>
        <el-descriptions-item label="付款状态">
          <dict-tag :type="DICT_TYPE.OM_AP_STATUS" :value="basicInfo.status" />
        </el-descriptions-item>
      </el-descriptions>
    </common-card>
    <common-card title="供应商信息">
      <div style="display: flex;justify-content: space-between">
        <div>
          <el-input v-model="searchSupplier" style="width: 400px" clearable />
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </div>
        <el-button style="margin: 5px 0" type="primary" @click="exportSupplier">导出</el-button>

      </div>

      <el-table :data="supplierData" border style="width: 100%" @row-click="rowClick">
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button type="text" v-if="scope.row.status==='no_payment'" @click="disbursements(scope.row)">{{ $t('付款') }}</el-button>
            <el-button type="text" v-if="scope.row.status==='no_payment'" @click="showFile(scope.row)">{{ $t('上传水单') }}</el-button>
            <el-button type="text" @click="showInvoices(scope.row)">{{ $t('查看发票') }}</el-button>
            <el-dialog
              width="400px"
              title="付款水单"
              :visible.sync="scope.row.fileDialogVisible"
              class="upload-demo"
            >
              <div class="upload-container">
                <el-upload
                  :action="uploadFileUrl"
                  :headers="headers"
                  :on-success="(response, file, fileList)=>handleUploadSuccess(response, file,scope.row)"
                  :on-remove="( file, fileList)=>handleRemove( file, fileList,scope.row,scope.$index)"
                  :on-preview="onPreview"
                  :file-list="scope.row.files"
                  multiple
                  drag
                >
                  <i class="el-icon-upload" />
                  <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
                </el-upload>
              </div>
              <div slot="footer">
                <el-button type="primary" @click="scope.row.fileDialogVisible = false">确定</el-button>
              </div>
            </el-dialog>
          </template>
        </el-table-column>
        <el-table-column prop="supplier" label="供应商" />
        <el-table-column prop="paymentTerm" label="付款条款" />
        <el-table-column prop="currency" label="币种">
          <template slot-scope="scope">
            <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="scope.row.currency" />
          </template>
        </el-table-column>
        <el-table-column prop="bankName" label="开户行" />
        <el-table-column prop="swiftCode" label="SWIFT Code" />
        <el-table-column prop="bankCountry" label="银行国家" />
        <el-table-column prop="bankCity" label="银行城市" />
        <el-table-column prop="bankAccount" label="银行账号" />
        <el-table-column prop="accountName" label="开户名称" />
        <el-table-column prop="supplierTotalAmount" label="金额" align="right">
          <template slot-scope="scope">
            <span>{{ scope.row.supplierTotalAmount }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="additionCharge" label="附加费" align="right">
          <template slot-scope="scope">
            <span>{{ scope.row.additionalFee }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="handingCharge" label="ESIC是否支付手续费">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.handingCharge" @change="changeCharge(scope.row)" />
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="supplierTotal > 0"
        :limit.sync="supplierQuery.pageSize"
        :page.sync="supplierQuery.pageNo"
        :total="supplierTotal"
        @pagination="getSupplierData()"
      />
    </common-card>
    <common-card title="物料明细">
      <div style="text-align: right;margin: 5px 0">
        <el-button type="primary" @click="exportMaterial">导出</el-button>

      </div>

      <el-table :data="materialData" border style="width: 100%">
        <el-table-column prop="soNo" label="SO NO" />
        <el-table-column prop="poNo" label="PO NO" />
        <el-table-column prop="partNo" label="Part NO" />
<!--        <el-table-column prop="shippingDate" label="Shipping Date" width="120" />-->
        <el-table-column prop="quantity" label="Shipping Quantity" align="right" />
        <el-table-column prop="unitPrice" label="U/P" align="right">
          <template slot-scope="scope">
            {{ scope.row.unitPrice }}
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" align="right">
          <template slot-scope="scope">
            {{ scope.row.amount }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="materialTotal > 0"
        :limit.sync="materialQuery.pageSize"
        :page.sync="materialQuery.pageNo"
        :total="materialTotal"
        @pagination="getMaterial()"
      />
    </common-card>
    <el-dialog
      title="发票信息"
      :visible.sync="invoiceDialogVisible"
      width="50%"
    >
      <el-table :data="currentSupplierInvoices" border style="width: 100%">
        <el-table-column prop="supplier" label="供应商" />
        <el-table-column prop="invoiceNo" label="发票号" />
        <el-table-column prop="invoiceAmount" label="发票金额" align="right">

        </el-table-column>
      </el-table>
    </el-dialog>

  </div>
</template>
<script>
import {
  bearHandingCharge,
  exportAccountsPayableDetailExcel, exportAccountsPayableSupplierExcel,
  getAccountsPayableDetailPage, getAccountsPayableInvoicePage,
  getAccountsPayableMaster,
  getAccountsPayableSupplierPage,
  downloadSupplierFile,
  uploadSupplierFile, updateAccountsPayableMaster, updateSupplierPaymentStatus
} from '@/api/om/capital'
import { DICT_TYPE } from '@/utils/dict'
import { getBaseHeader } from '@/utils/request'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
export default {
  name: 'Paydetail/:id',
  components: { OperateDropDown },
  data() {
    return {
      basicInfo: {
        apNo: '',
        totalAmount: '',
        currency: '',
        supplierCount: 2,
        createTime: '',
        status: ''
      },
      supplierQuery: {
        id: this.$route.params.id,
        supplier: '',
        pageNo: 1,
        pageSize: 10

      },
      materialQuery: {
        id: this.$route.params.id,
        pageNo: 1,
        pageSize: 10
      },

      searchSupplier: '',
      supplierData: [],
      materialData: [],
      supplierTotal: 0,
      materialTotal: 0,
      invoiceDialogVisible: false,
      currentSupplierInvoices: [],
      fileList: [],
      uploadData: {
        apSupplierId: null
      },
      currentSupplier: null,
      headers: getBaseHeader(),
      uploadFileUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload'
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  mounted() {
    this.getBasic()
    this.getSupplierData()
  },
  methods: {
    getBasic() {
      getAccountsPayableMaster({ id: this.$route.params.id }).then(
        res => {
          this.basicInfo = res.data
        }
      )
    },
    getSupplierData() {
      getAccountsPayableSupplierPage({
        apId: this.$route.params.id,
        ...this.supplierQuery,
        supplier: this.searchSupplier
      }).then(res => {
        res.data.list.forEach(a => {
          a.fileDialogVisible = false
          if (a.files) {
            a.files?.forEach(b => {
              b.name = b.fileName
              b.url = b.fileUrl
              b.id = b.fileId
            })
          } else {
            a.files = []
          }
        })
        this.supplierData = res.data.list
        this.supplierTotal = res.data.total
        this.getMaterial(res.data.list.at(0)?.supplier)
      })
    },
    getMaterial(supplier) {
      getAccountsPayableDetailPage({
        apId: this.$route.params.id,
        ...this.materialQuery,
        supplier: supplier || this.searchSupplier
      }).then(res => {
        this.materialData = res.data.list
        this.materialTotal = res.data.total
      })
    },

    handleSearch() {
      this.supplierQuery.pageNo = 1
      this.materialQuery.pageNo = 1
      this.getSupplierData()
    },
    exportSupplier() {
      exportAccountsPayableSupplierExcel({
        apId: this.$route.params.id,
        ...this.materialQuery,
        supplier: this.searchSupplier
      }).then(res => {
        this.$download.excel(res, '导出供应商信息.xlsx')
      })
    },
    exportMaterial() {
      exportAccountsPayableDetailExcel({
        apId: this.$route.params.id,
        ...this.materialQuery,
        supplier: this.searchSupplier
      }).then(res => {
        this.$download.excel(res, '导出物料明细.xlsx')
      })
    },
    changeCharge(val) {
      bearHandingCharge({
        apSupplierId: val.id,
        bearHandingCharge: val.handingCharge
      }).then(res => {
        this.$message.success('操作成功')
      })
    },
    showInvoices(obj1) {
      this.invoiceDialogVisible = true
      getAccountsPayableInvoicePage({
        apId: this.$route.params.id,
        apSupplierId: obj1.id,
        pageNo: 1,
        pageSize: 200
      }).then(res => {
        this.currentSupplierInvoices = res.data.list
      })
    },
    showFile(row) {
      row.fileDialogVisible = true
    },
    handleUploadSuccess(response, file, row) {
      row.files.push({
        id: response.data.id,
        name: file.name,
        url: response.data.url
      })
      this.saveFile(row)
    },
    handleRemove(file, fileList, row) {
      const index = row.files.findIndex(a => a.fileId === file.fileId)
      if (index > -1) {
        row.files.splice(index, 1)
      }
      this.saveFile(row)
    },
    saveFile(row) {
      uploadSupplierFile({
        apSupplierId: row.id,
        fileIdList: row.files.map(a => a.id || a.fileId)
      })
    },
    downloadFile(file) {
      // 调用下载文件接口
      downloadSupplierFile(file.id).then(res => {
        this.$download.file(res, file.name)
      })
    },
    onPreview(file) {
      if (file.url) {
        window.open(file.url)
      }
    },
    disbursements(obj1) {
      this.$modal.confirm('确定操作给该供应商的付款？').then(function() {
        return  updateSupplierPaymentStatus({
          apSupplierId: obj1.id
        })
      }).then(() => {
        this.$message.success('操作成功')
        this.getSupplierData()
      }).catch(() => {})
    },
    rowClick(row) {
      this.getMaterial(row.supplier)
    }
  }
}
</script>

<style scoped lang="scss">

</style>
