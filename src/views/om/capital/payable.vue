<template>
  <div class="app-container">
    <div class="stats-container">
      <el-card shadow="hover">
        <div style="font-size: 24px; color: #409EFF; font-weight: bold;">
          $ {{ statistics.countPayableAmount }}
        </div>
        <div style="color: #909399; margin-top: 8px;">
          总应付金额
        </div>
      </el-card>
      <el-card shadow="hover">
        <div style="font-size: 24px; color: #67C23A; font-weight: bold;">
          $ {{ statistics.tt0AdvanceAmount }}
        </div>
        <div style="color: #909399; margin-top: 8px;">
          TT0/TTAdvance应付金额
        </div>
      </el-card>
      <el-card shadow="hover">
        <div style="font-size: 24px; color: #F56C6C; font-weight: bold;">
          $ {{ statistics.withinDue30DaysAmount }}
        </div>
        <div style="color: #909399; margin-top: 8px;">
          逾期30日内
        </div>
      </el-card>
      <el-card shadow="hover">
        <div style="font-size: 24px; color: #67C23A; font-weight: bold;">
          $ {{ statistics.within30daysAmount }}
        </div>
        <div style="color: #909399; margin-top: 8px;">
          应付30日内
        </div>
      </el-card>
      <el-card shadow="hover">
        <div style="font-size: 24px; color: #E6A23C; font-weight: bold;">
          $ {{ statistics.within60daysAmount }}
        </div>
        <div style="color: #909399; margin-top: 8px;">
          应付31-60日
        </div>
      </el-card>
      <el-card shadow="hover">
        <div style="font-size: 24px; color: #909399; font-weight: bold;">
          $ {{ statistics.within90daysAmount }}
        </div>
        <div style="color: #909399; margin-top: 8px;">
          应付61-90日
        </div>
      </el-card>
      <el-card shadow="hover">
        <div style="font-size: 24px; color: #909399; font-weight: bold;">
          $ {{ statistics.moreThan90daysAmount }}
        </div>
        <div style="color: #909399; margin-top: 8px;">
          应付90日以上
        </div>
      </el-card>
    </div>
    <el-card>
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.search"
          style="flex: 0 1 35%"
          :placeholder="$t('SO NO#/PO NO#/Part No.')"
          @keyup.enter.native="getListInit"
        />
        <el-button plain type="primary" @click="getListInit">
          {{ $t('common.search') }}
        </el-button>
        <el-button
          style="margin-left: 0"
          @click="handleReset"
        >
          {{ $t('common.reset') }}
        </el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>
      </div>
      <el-form v-show="showSearch" inline label-width="120px">

        <el-form-item class="searchItem" :label="$t('AP NO')" prop="no">
          <el-input v-model="queryParams.apNo" placeholder="请输入" />

        </el-form-item>
        <el-form-item class="searchItem" :label="$t('发票号码')" prop="no">
          <el-input v-model="queryParams.invoiceNo" placeholder="请输入" />

        </el-form-item>

        <el-form-item class="searchItem" :label="$t('供应商')" prop="">
          <el-autocomplete
            v-model="queryParams.supplier"
            :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'supplier')})"
            :placeholder="$t('Supplier')"
            :popper-append-to-body="false"
            popper-class="el-autocomplete-suggestion"
            class="overList"
            @select="((item) => {handleSelect(item, 'supplier')})"
            @keyup.enter.native="getListInit"
          />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('付款状态')" prop="">
          <el-select v-model="queryParams.apStatus" class="searchValue" multiple clearable filterable>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_AP_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <vxe-grid
        ref="cash"
        :data="list"
        :loading="loading"
        v-bind="payGrid"
      >
        <template #apNo="{row}">
          <copy-button @click="$router.push(`/capital/payDetail/${row.id}?no=${row.apNo}`)">{{ row.apNo }}</copy-button>
        </template>

        <template #dict="{column,row}">
          <dict-tag :type="column.params.dict" :value="row[column.field]" />
        </template>
        <template #operate="{row}">
          <OperateDropDown
            :menu-item="[
              {
                name: $t('批量付款'),
                show: true,
                action: (row) => showBulkPayment(row),
                para: row
              },
              {
                name: $t('附件'),
                show: true,
                action: (row) => showFile(row),
                para: row
              },
              {
                name: $t('作废'),
                show: (row.status==='no_payment' || row.status==='overdue'),
                action: (row) => handleCancel(row),
                para: row
              },
            ]"
            ></OperateDropDown>
          <el-dialog
            width="400px"
            title="附件管理"
            :visible.sync="row.fileDialogVisible"
            class="upload-demo"
          >
            <div class="upload-container">
              <el-upload
                :action="uploadFileUrl"
                :headers="headers"
                :on-success="(response, file, fileList)=>handleUploadSuccess(response, file,row)"
                :on-remove="( file, fileList)=>handleRemove( file, fileList,row,)"
                :on-preview="onPreview"
                :file-list="row.files"
                multiple
                drag
              >
                <i class="el-icon-upload" />
                <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
              </el-upload>
            </div>
            <div slot="footer">
              <el-button type="primary" @click="row.fileDialogVisible = false">确定</el-button>
            </div>
          </el-dialog>
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="24" style="width: 100%" class="mb8">
            <el-col :span="22" style="display: flex">
              <BatchUpload
                :btn-name="$t('新建付款单')"
                :template-download="async ()=>{ return exportAccountsPayableTemplate()}"
                template-name="pay template"
                url="om/accounts-payable-master/import-ap-template"
                :after-upload="getListInit"
              />
            </el-col>
            <el-col :span="2">
              <right-toolbar
                :list-id="payGrid.id"
                :show-search.sync="showSearch"
                :custom-columns.sync="payGrid.columns"
                @queryTable="getListInit"
              />
            </el-col>
          </el-row>
        </template>
      </vxe-grid>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getList()"
      />
    </el-card>
    <el-dialog
      width="400px"
      title="批量付款"
      :visible.sync="upload.show"
      class="upload-demo"
    >
      <div class="upload-container">
        <el-upload
          :action="uploadFileUrl"
          :headers="headers"
          :on-success="(response, file, fileList)=>bulkPaymentHandleUploadSuccess(response)"
          :file-list="upload.files"
          drag
          accept=".zip,.ZIP"
          :limit="1"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer">
        <el-button type="primary" :loading="upload.load" @click="bulkPayment">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  accountsPayableStatistics, bulkPayment,
  exportAccountsPayableTemplate,
  getAccountsPayableMasterPage,
  updateAccountsPayableMaster, uploadApFile, uploadSupplierFile
} from '@/api/om/capital'
import { getSearchResult } from '@/api/om/orderTracker'
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE } from '@/utils/dict'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import { getBaseHeader } from '@/utils/request'

export default {
  name: 'Payable',
  components: {BatchUpload, OperateDropDown },
  data() {
    return {
      statistics: {
        countPayableAmount: null,
        tt0AdvanceAmount: null,
        withinDue30DaysAmount: null,
        within30daysAmount: null,
        within60daysAmount: null,
        within90daysAmount: null,
        moreThan90daysAmount: null
      },

      queryParams: {
        apNo: '',
        invoiceNo: '',
        supplier: '',
        apStatus: [],
        pageNo: 1,
        pageSize: 10
      },
      total: 0,
      list: [],
      showSearch: false,
      loading: false,
      payGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'cash',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { title: this.$t('AP NO'), slots: { default: 'apNo' }, field: 'apNo', visible: true },
          { title: this.$t('供应商数'), field: 'supplierCount', visible: true },
          { title: this.$t('货物总金额'), field: 'goodsAmount', visible: true },
          { title: this.$t('附加费总金额'), field: 'additionCharge', visible: true },
          { title: this.$t('总金额'), field: 'totalAmount', visible: true },
          { title: this.$t('日期'), field: 'createTime',
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true },
          { title: this.$t('付款状态'), field: 'status', slots: { default: 'dict' },
            params: { dict: DICT_TYPE.OM_AP_STATUS }, visible: true },
          {
            field: 'operate',
            slots: { default: 'operate' }, fixed: 'right',
            title: this.$t('common.operate'), visible: true, width: 35, showOverflow: false
          }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      fileList: [],
      currentSupplier: null,
      headers: getBaseHeader(),
      uploadFileUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      dialogVisible: false,
      upload:{
        show:false,
        activityApId:null,
        fileId:null,
        load:false,
        files:[]
      }
    }
  },
  mounted() {
    this.getList()
    this.getStatistic()
  },
  methods: {
    exportAccountsPayableTemplate,
    getStatistic() {
      accountsPayableStatistics().then(res => {
        this.statistics = res.data
      })
    },
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      getAccountsPayableMasterPage(this.queryParams).then(res => {
        res.data.list.forEach(a => {
          a.fileDialogVisible = false
          if (a.files) {
            a.files?.forEach(b => {
              b.name = b.fileName
              b.url = b.fileUrl
              b.id = b.fileId
            })
          } else {
            a.files = []
          }
        })
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.queryParams = {
        search: '',
        apNo: '',
        supplier: '',
        fundsUsage: '',
        beginDate: '',
        endDate: '',
        customer: '',
        pageNo: 1,
        pageSize: 10,
        time: []
      }
      this.getListInit()
    },
    handleSelect(item, type) {
      this.queryParams.supplier = item.value
    },
    querySearchAsync(queryString, cb, type) {
      if (queryString.length < 2) {
        return
      }
      getSearchResult({
        searchValue: queryString,
        type: type
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item
          }
        }))
      })
    },

    handleCancel(row) {
      this.$confirm('确认作废吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(() => {
        updateAccountsPayableMaster({
          id: row.id,
          status: 'voided'
        }).then(() => {
          this.$message.success('操作成功')
          this.getList()
        })
      })
    },
    handleUploadSuccess(response, file, row) {
      row.files.push({
        id: response.data.id,
        name: file.name,
        url: response.data.url
      })
      this.saveFile(row)
    },
    handleRemove(file, fileList, row) {
      const index = row.files.findIndex(a => a.fileId === file.fileId)
      if (index > -1) {
        row.files.splice(index, 1)
      }
      this.saveFile(row)
    },
    saveFile(row) {
      uploadApFile({
        apId: row.id,
        fileIdList: row.files.map(a => a.id || a.fileId)
      })
    },

    onPreview(file) {
      if (file.url) {
        window.open(file.url)
      }
    },
    showFile(obj1) {
      obj1.fileDialogVisible = true
    },
    showBulkPayment(row){
      this.upload.show=true
      this.upload.activityApId=row.id
    },
    bulkPaymentHandleUploadSuccess(response){
      this.upload.fileId=response.data.id
    },

    bulkPayment(){
      this.upload.load=true
      bulkPayment({
        fileId:this.upload.fileId,
        apId:this.upload.activityApId
      }).then(res=>{
        this.$message.success('操作成功,本次共处理成功{'+res.data+'}个付款')
        this.upload.show = false
        this.upload.load=false
        // 清空上一次的文件列表
        this.upload.files = []
      }).catch(err=>{
        this.$message.error(err.message)
        this.upload.load=false
        // 清空上一次的文件列表
        this.upload.files = []
      })

    }
  }
}
</script>

<style scoped lang="scss">
.stats-container {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  gap: 20px;
  margin-bottom: 20px;
  width: 100%;
  .el-card {
    flex: 1;
    min-width: 200px;
  }
}
.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}
.searchValue {
  width: 100%;
}
</style>
