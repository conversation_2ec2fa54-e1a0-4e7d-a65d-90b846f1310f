<template>
  <div class="app-container">
    <common-card :title="$t('批量发送邮件')">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item class="searchItem" :label="$t('客户')" prop="customer" style="width: 66%">
          <el-radio-group v-model="form.customer">
            <el-radio v-for="customer in $store.getters.incapCustomers" :key="customer" :label="customer">
              {{ customer }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('system.templateTitle')" prop="title">
          <el-input v-model="form.title" :placeholder="$t('system.pleaseEnterTheTemplateTitle')" />
        </el-form-item>
        <el-form-item :label="$t('system.templateContent')">
          <tinymce v-if="freshTinymce" v-model="form.content" :height="192" />
        </el-form-item>
        <el-form-item
          :label="$t('auth.uploadAttachments')"
          class="commonFormItem"
        >
          <div style="display: flex;justify-content: space-between">
            <el-upload
              class="upload-demo"
              :action="uploadUrl"
              :headers="getBaseHeader()"
              :on-remove="onRemove"
              :on-preview="onPreview"
              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
              multiple
              :limit="5"
              :show-file-list="false"
              :file-list="fileList"
            >
              <el-button
                class="uploadBtn"
                size="small"
                plain
                icon="el-icon-plus"
                type="primary"
              />
            </el-upload>
            <div>
              {{ $t('scar.viewAttachments') }}
              <el-button
                class="uploadBtn"
                size="small"
                style="padding: 5px 9px"
                :disabled="fileList.length===0"
                plain
                :type="fileList.length?'primary':''"
                @click="showFile=true"
              >
                {{ fileList.length }}
              </el-button>

              <el-dialog
                v-if="showFile"
                :visible.sync="showFile"
                :title="$t('scar.viewAttachments')"
                width="400px"
              >
                <el-upload
                  class="upload-demo"
                  :action="uploadUrl"
                  :headers="getBaseHeader()"
                  :on-remove="onRemove"
                  :on-preview="onPreview"
                  :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                  multiple
                  :limit="5"
                  :show-file-list="true"
                  :file-list="fileList"
                />
                <div slot="footer">
                  <el-button type="primary" @click="showFile=false">{{ $t('order.close') }}</el-button>
                </div>
              </el-dialog>
            </div>
          </div>
        </el-form-item>
        <div style="width: 100%;display:flex;justify-content: center">
          <el-button icon="el-icon-edit-outline" type="primary" @click="createMail">{{ $t('确定批量发送') }}</el-button>
        </div>
      </el-form>
    </common-card>
    <common-card
      :title="$t('历史发送列表')"
    >
      <div style="margin-top: 25px;">
        <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
          <el-input
            v-model="queryParams.title"
            style="flex: 0 1 35%"
            :placeholder="$t('邮件标题')"
            @keyup.enter.native="getListInit"
          />
          <el-button plain type="primary" @click="getListInit">
            {{ $t('common.search') }}
          </el-button>
          <el-button
            style="margin-left: 0"
            @click="handleReset"
          >
            {{ $t('common.reset') }}
          </el-button>
          <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
            <el-button type="text">
              {{ $t('common.advancedSearch') }}
            </el-button>
            <i
              :style="showSearch? '':{transform: 'rotate(180deg)'}"
              class="el-icon-arrow-up"
              style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
            />
          </div>
        </div>

        <el-form v-show="showSearch" inline label-width="120px">
          <el-form-item class="searchItem" :label="$t('Customer')" style="width: 66%">
            <el-radio-group v-model="queryParams.customer" @change="getListInit();">
              <el-radio :label="null">{{ $t('order.whole') }}</el-radio>
              <el-radio v-for="customer in $store.getters.incapCustomers" :key="customer" :label="customer">
                {{ customer }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

        </el-form>
        <vxe-grid
          ref="poInfo"
          :data="list"
          :loading="loading"
          v-bind="historyEmailGrid"
        >
        </vxe-grid>
        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNo"
          :total="total"
          @pagination="getList()"
        />
      </div>
    </common-card>
  </div>
</template>

<script>

import {getBaseHeader} from "@/utils/request";
import Editor from "@/components/Editor/index.vue";
import OperateDropDown from "@/components/OperateDropDown/index.vue";
import {creatEmail, getHistoryEmail} from "@/api/om/config/api";
import {deleteFile} from "@/api/infra/file";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "Massemail",
  components: {
    OperateDropDown,
    Editor
  },
  data() {
    return {
      form: {
        title:null,
        content:null,
        customer:'INDIA',
        fileIds:[]
      },
      rules: {
        title: [
          { required: true, message: this.$t('邮件标题必填'), trigger: 'blur' },
        ],
        content: [
          { required: true, message: this.$t('邮件内容必填'), trigger: 'blur' },
        ]
      },
      fileList: [],
      showFile: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      getBaseHeader,
      queryParams:{
        title:null,
        customer: null,
      },
      loading:false,
      list:[],
      total:0,
      historyEmailGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'poInfo',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { title: '客户', field: 'customer', visible: true, },
          { title: '邮件标题', field: 'title', visible: true,},
          { title: '发送时间', field: 'createTime',formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true},
          { title: '发送状态', field: 'status', visible: true},
        ],
        sortConfig: {
          remote: true
        }
      },
      showSearch:false,
      freshTinymce:true
    }
  },
  methods: {
    onRemove(file, fileList) {
      const index = this.fileList.indexOf(file.response?.data?.fileId) || this.fileList.indexOf(file.fileId)
      this.fileList.splice(index, 1)
    },
    onSuccess(response, file, fileList) {
      this.fileList.push({
        name: file.name,
        fileUrl: response.data.url,
        fileId: response.data.id
      })
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.fileUrl) {
        window.open(file.fileUrl)
      }
    },
    handleReset() {
      this.queryParams = {
        title:null,
        customer: null,
      }
      this.getList()
    },
    createMail(){
      this.$modal.confirm('将会向目标客户2年内发生过订单的供应商发送邮件，是否确定？')
        .then(() => {
          if (this.fileList) {
            console.log(this.fileList)
            this.form.fileIds = this.fileList.map(file => file.fileId)
          }
          return creatEmail(this.form)
        })
        .then(() => {
          this.form = {
            title: null,
            content: null,
            customer: 'INDIA',
            fileIds: []
          }
          this.fileList = []
          this.getListInit()
          this.$message.success(this.$t('操作成功'))
        })
        .catch(() => {})
    },
    getList() {
      this.loading = true
      getHistoryEmail(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
  },
  mounted() {
    this.getListInit()
  },
  deactivated() {
    this.freshTinymce = false
  },

  activated() {
    this.freshTinymce = true
  }
}
</script>

<style scoped lang="scss">
.commonFormItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 150px);
  }
}
</style>

