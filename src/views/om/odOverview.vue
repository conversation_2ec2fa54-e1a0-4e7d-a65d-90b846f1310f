

<template>
<div>
  <div style="display: flex;justify-content: space-between">
    <div v-for="item in headData" style="flex: 0 0 24%">
      <el-card>
        <div class="app-head" >
          <div  class="app-head-item">
            <svg-icon :icon-class="item.icon" class-name="headIcon" :style="{color:item.color}" />
            <div style="padding-left: 25px;width: 100%">
              <div style="font-size: 28px;border-bottom: 0.5px solid #C3C3C3;padding-bottom: 8px;font-weight: 500">
                <span v-if="item.unit">   {{ item.unit }}</span>
                {{statistic[item.field]}}
<!--                {{ item.label }}-->
              </div>

            </div>
          </div>
        </div>
        <div style="margin-left: 71px">
          <div style="font-size: 16px;margin-top: -30px">
            <div v-for="stat in statistic.customerVOList.filter(a=>a.type === item.type )">
              <span style="color: #565656;display: inline-block;width: 65px">{{ stat.customer }}</span>
              <span style="font-size:16px;margin: 0 5px">{{ stat.typeValue }}</span>
              <span style="font-size:16px;"  v-if="stat.typeValueSub">({{ stat.typeValueSub }})</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

  </div>
  <div style="display: flex;justify-content: space-between;margin-top: 15px">
    <div  style="flex: 0 1 33%">
      <el-card>
        <div style="display: flex;justify-content: space-between;margin: 10px 0;font-size: 18px;">
          <span>Total Value</span>
          <el-button type="text" @click="staticDownload">Download</el-button>
        </div>

        <el-table :data="odStatic">
          <el-table-column type="index" label="No." prop="" width="50"></el-table-column>
          <el-table-column label="Customer" prop="customer"></el-table-column>
          <el-table-column label="Prev. Years" prop="prevYears"></el-table-column>
          <el-table-column label="Shipped Val. YTD" prop="shippedYtd"></el-table-column>
          <el-table-column label="Open PO Val. YTD " prop="openPoYtd"></el-table-column>
        </el-table>      </el-card>
    </div>
    <div  style="flex: 0 1 33%">
      <el-card>
        <div style="display: flex;justify-content: space-between;margin: 10px 0;font-size: 18px;">
          <span>Top Suppliers Spend YTD</span>
          <el-button type="text" @click="topSupplierDownload">Download</el-button>
        </div>

        <el-table v-loading="topLoading" :data="topSupplier">
          <el-table-column type="index" label="No." prop="" width="50">
            <template #default="scope">
              {{topQuery.pageSize*(topQuery.pageNo-1)+scope.$index +1}}
            </template>
          </el-table-column>
          <el-table-column label="Supplier" prop="supplier"></el-table-column>
          <el-table-column label="Prev. Years" prop="prevYears">
            <template #default="scope">
              <span>
                {{scope.row.prevYears}}
              </span>
              <el-progress v-if="scope.row.prevYearsRate" color="#C8DFEA" :percentage="scope.row.prevYearsRate*100"></el-progress>
            </template>
          </el-table-column>
          <el-table-column label="Shipped Val. YTD" prop="shippedYtd">
            <template #default="scope">

                <span>
                {{scope.row.shippedYtd}}
              </span>
            <el-progress v-if="scope.row.shippedYtdRate" color="#F8BD83" :percentage="scope.row.shippedYtdRate*100"></el-progress>
            </template>
          </el-table-column>
          <el-table-column label="Open PO Val. YTD " prop="openPoYtd">
            <template #default="scope">

                <span>
                {{scope.row.openPoYtd}}
              </span>
            <el-progress v-if="scope.row.openPoYtdRate" color="#EADFFF" :percentage="scope.row.openPoYtdRate*100"></el-progress>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="topTotal > 0"
          layout="prev, pager, next"
          :limit.sync="topQuery.pageSize"
          :page.sync="topQuery.pageNo"
          :total="topTotal"
          @pagination="getTopSupplier()"
        />
      </el-card>
    </div>
    <div  style="flex: 0 1 33%">
      <el-card>
        <div style="display: flex;justify-content: space-between;margin: 10px 0;font-size: 18px;">
          <span>Parts Spend YTD</span>
          <el-button type="text" @click="partsDownload">Download</el-button>
        </div>

        <el-table v-loading="partLoading" :data="partsSpend">
          <el-table-column type="index" label="No." prop="" width="50">
            <template #default="scope">
              {{partQuery.pageSize*(partQuery.pageNo-1)+scope.$index +1}}
            </template>
          </el-table-column>
          <el-table-column label="Part No" prop="partNo"></el-table-column>
          <el-table-column label="Prev. Years" prop="prevYears">
            <template #default="scope">
              <span>
                {{scope.row.prevYears}}
              </span>
              <el-progress v-if="scope.row.prevYearsRate" color="#C8DFEA" :percentage="scope.row.prevYearsRate*100"></el-progress>
            </template>
          </el-table-column>
          <el-table-column label="Shipped Val. YTD" prop="shippedYtd">
            <template #default="scope">

                <span>
                {{scope.row.shippedYtd}}
              </span>
            <el-progress v-if="scope.row.shippedYtdRate"  color="#F8BD83" :percentage="scope.row.shippedYtdRate*100"></el-progress>
            </template>
          </el-table-column>
          <el-table-column label="Open PO Val. YTD " prop="openPoYtd">
            <template #default="scope">

                <span>
                {{scope.row.openPoYtd}}
              </span>
            <el-progress v-if="scope.row.openPoYtdRate" color="#EADFFF" :percentage="scope.row.openPoYtdRate*100"></el-progress>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          layout="prev, pager, next"
          v-show="partTotal > 0"
          :limit.sync="partQuery.pageSize"
          :page.sync="partQuery.pageNo"
          :total="partTotal"
          @pagination="getParts()"
        />
      </el-card>
    </div>
    </div>

</div>
</template>
<script>
import {
  exportPartsSpendExcel,
  exportTopSupplierSpendExcel,
  exportTotalValueExcel,
  getOrderStatisticsTotalValue,
  getPartsSpendPage,
  getTopSupplierSpendPage
} from '@/api/om/orderTracker'

export default {
  name: 'odOverview',
  props:['statistic'],
  data() {
    return {
      headData: [
        {
          icon: 'shopping',
          label: 'SO(Line)',
          color: '#4996B8',
          field: 'soCount',
          field1: 'soLineCount',
          unit: '',
          type:'so_count',
        },
        {
          icon: 'shopping',
          label: 'SO Value',
          color: '#E2C052',
          field: 'soAmount',
          unit: '$',
          type:'so_amount',
        },
        {
          icon: 'ep-list',
          label: 'PO(Line)',
          color: '#4996B8',
          field: 'poCount',
          field1: 'poLineCount',
          unit: '',
          type:'po_count',
        },
        {
          icon: 'ep-list',
          label: 'PO Value',
          color: '#E2C052',
          field: 'poAmount',
          unit: '$',
          type:'po_amount',
        }
      ],
      odStatic: [],
      topSupplier: [],
      partsSpend: [],
      topQuery:{
        "pageNo": 1,
        "pageSize": 15,
      },
      topTotal:0,
      partQuery:{
        "pageNo": 1,
        "pageSize": 15,
      },
      partTotal:0,
      partLoading: false,
      topLoading: false
    }
  },
  mounted() {
    this.init()
    this.getTopSupplier()
    this.getParts()
  },
  methods: {
    init() {
      getOrderStatisticsTotalValue().then(res=>{
        res.data.forEach(a=>{
          a.prevYears =  Math.floor(a.prevYears).toLocaleString()
          a.shippedYtd =  Math.floor(a.shippedYtd).toLocaleString()
          a.openPoYtd =  Math.floor(a.openPoYtd).toLocaleString()
        })
        this.odStatic= res.data
      })
    },
    getTopSupplier(){
      this.topLoading = true
      getTopSupplierSpendPage(this.topQuery).then(res=>{
        res.data.list.forEach(a=>{
          a.prevYears =  Math.floor(a.prevYears).toLocaleString()
          a.shippedYtd =  Math.floor(a.shippedYtd).toLocaleString()
          a.openPoYtd =  Math.floor(a.openPoYtd).toLocaleString()
        })
        this.topSupplier = res.data.list
        this.topTotal = res.data.total
        this.topLoading = false
      })
    },
    getParts(){
      this.partLoading = true
      getPartsSpendPage(this.partQuery).then(res=>{
        res.data.list.forEach(a=>{
          a.prevYears =  Math.floor(a.prevYears).toLocaleString()
          a.shippedYtd =  Math.floor(a.shippedYtd).toLocaleString()
          a.openPoYtd =  Math.floor(a.openPoYtd).toLocaleString()
        })
        this.partsSpend = res.data.list
        this.partTotal = res.data.total
        this.partLoading = false
      })
    },
    staticDownload(){
      exportTotalValueExcel().then(res=>{
        this.$download.excel(res, 'Total Value.xlsx')
      })
    },
    topSupplierDownload(){
      exportTopSupplierSpendExcel().then(res=>{
        this.$download.excel(res, 'Top Suppliers Spend YTD.xlsx')
      })
    },
    partsDownload(){
      exportPartsSpendExcel().then(res=>{
        this.$download.excel(res, 'Parts Spend YTD.xlsx')
      })
    },
  }
}
</script>
<style scoped lang="scss">
.app-head{
  display: flex;
  justify-content: space-between;
  flex: 0 0 100%;
  &-item{
    height: 112px;
    flex: 0 0 100%;
    border-radius: 8px;
    display: flex;
    //padding: 20px 3%;
    align-items: center;
  }
}
.headIcon{
  font-size: 48px;
  color: red;
}
::v-deep .el-progress__text{
  display: none;
}
::v-deep .el-progress-bar{
  padding-right: 0;
}
::v-deep .el-table{
  width: 100%;
  .el-table__header-wrapper table,.el-table__body-wrapper table{
    width: 100% !important;
  }
  .el-table__body, .el-table__footer, .el-table__header{
    table-layout: auto;
  }
}
</style>
