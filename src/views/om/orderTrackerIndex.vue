<template>
  <div>
    <div class="app-container" style="padding: 10px">
      <div class="app-title">
        <div class="app-title-tab">
          <span :class="{'app-title-tab-selected': selectedTab === 'Order'}" @click="selectedTab = 'Order'">Order</span>
          <span :class="{'app-title-tab-selected': selectedTab === 'Overview'}" @click="selectedTab = 'Overview'">Overview</span>
        </div>
        <div style="flex: 0 1 940px">
          <el-descriptions
            :label-style="{
              'font-size': '18px',
              'line-height': '48px',
            }"
          >
            <el-descriptions-item label="Material count">
              <div class="des-content">{{ statistic.materialCount }}({{statistic.materialCountAll}})</div>
            </el-descriptions-item>
            <el-descriptions-item label="Supplier count">
              <div class="des-content"> {{ statistic.supplierCount }}({{statistic.supplierCountAll}})</div>
            </el-descriptions-item>
            <el-descriptions-item label="Commission">
              <div class="des-content-one"> {{ statistic.commission }}({{statistic.commissionAll}})</div>
            </el-descriptions-item>

          </el-descriptions>

        </div>
      </div>
      <div v-if="selectedTab === 'Order'">
        <div
          style="text-align: right;color: #689CB7;font-size: 14px;cursor:pointer;"
          @click="showStatisticians"
        >
          {{ $t('common.statisticians') }}
          <i
            class="el-icon-s-data"
            style="font-size: 20px;"
          />
        </div>
        <Transition name="scale">
          <div class="app-head" v-show="showHead">
            <div v-for="item in headData" class="app-head-item">
              <svg-icon :icon-class="item.icon" class-name="headIcon" :style="{color:item.color}" />
              <div style="padding-left: 25px">
                <div style="font-size: 18px"> {{ item.label }}</div>
                <div style="font-size: 25px;margin-top: 7px">

                  <span v-if="item.unit">   {{ item.unit }}</span>
                  {{ statistic[item.field] }}
                  <span v-if="statistic[item.field1]">({{ statistic[item.field1] }})</span>

                </div>
              </div>
            </div>
          </div>
        </Transition>
        <el-card style="margin-top: 12px;">

          <div style="margin-top: 25px;">
            <el-form  inline label-width="140px">
              <el-form-item class="searchItem" :label="$t('SO/PO NO#')" prop="no">
                <el-autocomplete
                  v-model="queryParams.no"
                  :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'so')})"
                  :placeholder="$t('SO NO#、PO NO#')"
                  :popper-append-to-body="false"
                  popper-class="el-autocomplete-suggestion"
                  class="searchItem"
                  @select="((item) => {handleSelect(item, 'so')})"
                  @keyup.enter.native="getListInit"
                />
              </el-form-item>
              <el-form-item class="searchItem" label=" " prop="no" style="width: 66%">
                <div style="display: flex">
                  <el-input
                    v-model="queryParams.search"
                    style="width: calc(50% - 70px)"
                    :placeholder="$t('SO NO#、PO NO#、Customer、Supplier、Part No.、Description、MFG、MPN、ESIC Inv.、AWB#、Ship Inv.、Vend Inv.')"
                    @keyup.enter.native="getListInit"
                  />
                  <el-button plain type="primary" @click="getListInit">
                    {{ $t('common.search') }}
                  </el-button>
                  <el-button
                    style="margin-left: 0"
                    @click="handleReset"
                  >
                    {{ $t('common.reset') }}
                  </el-button>
                  <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
                    <el-button type="text">
                      {{ $t('common.advancedSearch') }}
                    </el-button>
                    <i
                      :style="showSearch? '':{transform: 'rotate(180deg)'}"
                      class="el-icon-arrow-up"
                      style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
                    />
                  </div>
                </div>

              </el-form-item>
              <div v-show="showSearch">

              <el-form-item class="searchItem" :label="$t('Part No.')" prop="">
                <el-input v-model="queryParams.partNo" :placeholder="$t('Part No.、Description')" @keyup.enter.native="getListInit" />
              </el-form-item>

              <el-form-item class="searchItem" :label="$t('MFG')" prop="">
                <el-autocomplete
                  v-model="queryParams.mfg"
                  :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'mfg')})"
                  :placeholder="$t('MFG')"
                  :popper-append-to-body="false"
                  popper-class="el-autocomplete-suggestion"
                  class="overList"
                  @select="((item) => {handleSelect(item, 'mfg')})"
                  @keyup.enter.native="getListInit"
                />
              </el-form-item>
              <el-form-item class="searchItem" :label="$t('MPN')" prop="">
                <el-input v-model="queryParams.mpn" :placeholder="$t('MPN')" @keyup.enter.native="getListInit" />
              </el-form-item>

              <el-form-item class="searchItem" :label="$t('Supplier')" prop="">
                <el-autocomplete
                  v-model="queryParams.supplier"
                  :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'supplier')})"
                  :placeholder="$t('Supplier')"
                  :popper-append-to-body="false"
                  popper-class="el-autocomplete-suggestion"
                  class="overList"
                  @select="((item) => {handleSelect(item, 'supplier')})"
                  @keyup.enter.native="getListInit"
                />
              </el-form-item>

              <el-form-item class="searchItem" :label="$t('Buyer')" prop="">
                <el-select v-model="queryParams.buyer" class="searchValue" clearable filterable>
                  <el-option v-for="item in buyers" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>

            <el-form-item class="searchItem" :label="$t('Status')" prop="">
              <el-select v-model="queryParams.shipStatus" class="searchValue" clearable filterable multiple>
                <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SO_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item class="searchItem" :label="$t('SO Paid%')" prop="">
              <el-select v-model="queryParams.soPaid" class="searchValue" clearable multiple>
                <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SEARCH_PAID)" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item class="searchItem" :label="$t('SO Paid PO%')" prop="">
              <el-select v-model="queryParams.paid" class="searchValue" clearable multiple>
                <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SEARCH_PAID)" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
                <el-form-item class="searchItem" :label="$t('SO TT Copy')" prop="">
                  <el-input v-model="queryParams.soTTCopy" :placeholder="$t('SO TT Copy')" @keyup.enter.native="getListInit" />
                </el-form-item>
            <!--payment term-->
            <el-form-item class="searchItem" :label="$t('Pay. Term')" prop="">
              <el-select v-model="queryParams.paymentTerm" class="searchValue" clearable multiple>
                <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item class="searchItem" :label="$t('order.timeType')" prop="dateType">
              <el-select v-model="queryParams.dateType" class="searchValue" clearable>
                <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SEARCH_TIME_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label=" " class="searchItem">
              <el-date-picker
                v-model="queryParams.time"
                class="searchValue"
                value-format="yyyy-MM-dd"
                type="daterange"
                :range-separator="$t('order.to')"
                :start-placeholder="$t('common.startDate')"
                :end-placeholder="$t('common.endDate')"
              />
            </el-form-item>

            <el-form-item class="searchItem" :label="$t('Ship to')" prop="">
              <el-select v-model="queryParams.shipTo" class="searchValue" clearable filterable >
                <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SHIP_TO)" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <!-- in shipping tracker-->
            <el-form-item class="searchItem" :label="$t('In Shipping Tracker')" prop="dateType">
              <el-select v-model="queryParams.isInPacking" class="searchValue" clearable>
                <el-option v-for="item in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item class="searchItem" :label="$t('Urgent')" prop="dateType">
              <el-select v-model="queryParams.urgent" class="searchValue" clearable>
                <el-option v-for="item in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item class="searchItem" :label="$t('Customer')" prop="customer" style="width: 66%">
              <el-radio-group v-model="queryParams.customer" @change="getListInit();getStatic();">
                <el-radio :label="null">{{ $t('order.whole') }}</el-radio>
                <el-radio v-for="customer in $store.getters.incapCustomers" :key="customer" :label="customer">
                  {{ customer }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

              </div>
          </el-form>

          </div>
          <div style="display: flex;border-bottom: 3px solid #4996B8;">
            <div class="headCard" >
              <div class="headCard-body" :class="{ 'headCard-selected': queryParams.status === '',}"
                @click="queryParams.status = '';getListInit()"
              >
                <div class="headCard-body-num"> {{ totalCount }}</div>
                <div class="headCard-body-label">ALL</div>
              </div>
            </div>
            <div
              v-for="dict in orderTrackerStatisticsData" class="headCard">
              <div class="headCard-body"
                :class="{'headCard-selected': queryParams.status === dict.shipStatus,}"
                @click="queryParams.status = dict.shipStatus;getListInit()"
              >
                <div class="headCard-body-num"> {{ dict.count }}</div>
                <div class="headCard-body-label">   {{ dict.shipStatus }}</div>
              </div>
            </div>
          </div>
        <div>
          <el-dialog
            width="400px"
            append-to-body
            :visible.sync="filterDialog"
            title="批量筛选"
          >
            <el-select
              v-model="queryParams[filterField]"
              ref="filterField"
              multiple
              filterable
              allow-create
              style="width: 100%"
              clearable
              default-first-option
              @paste.native="handlePaste"

            >
              <el-option
                v-for="item in []"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <div slot="footer">
              <el-button @click="queryParams[filterField] = [];filterDialog = false;getListInit()">清空</el-button>
              <el-button type="primary" @click="filterDialog = false;getListInit()">确定</el-button>
            </div>
          </el-dialog>
            <!--          <vxe-grid
              ref="orderTrackerTable"
              :data="list"
              :loading="loading"
              v-bind="orderTrackerOptionTable"
              :header-cell-style="tableHeaderColor"
              @sort-change="sortMethod"
            >-->
            <vxe-grid
              ref="orderTrackerTable"
              :data="list"
              class="mytable-style"
              :loading="loading"
              v-bind="orderTrackerOptionTable"
              @sort-change="sortMethod"
              @edit-closed="debouncedEditClosed"
              :header-cell-class-name="headerCellClassName"
            >
              <template #batchSearch="{column,row}">
                <div style="display: flex;justify-content: space-between;align-items: center">
                  <span>{{column.title}}</span>
                  <svg-icon
                    @click="filterDialog = true;filterField = column.params.field"
                    icon-class="filter"
                    :class-name="queryParams[column.params.field].length>0?'filterSelect' : ''"
                    style="cursor:pointer;margin-left: 3px"></svg-icon>
                </div>
              </template>
              <template #partNo="{column,row}">
                <copy-button type="text" >{{ row.partNo }}</copy-button>
              </template>
              <template #mpn="{column,row}">
                <copy-button type="text" >{{ row.mpn }}</copy-button>
              </template>
              <template #deliveryType="{column,row}">
                <dict-tag :type="DICT_TYPE.OM_DELIVERY_TYPE" :value="row.deliveryType" />
              </template>
              <template #paid="{row}">
                <number-format :value="row.paid" :decimal-place="0" />
              </template>
              <template #soPaid="{row}">
                <number-format :value="row.soPaid" :decimal-place="0" />
              </template>
              <template #unitPrice="{row}">
              <span v-if="row.unitPrice">
                {{ row.unitPrice?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
              </template>
              <template #orderQuantity="{row}">
              <span v-if="row.orderQuantity">
                {{ row.orderQuantity?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
              </template>
              <template #totalAmount="{row}">
              <span v-if="row.totalAmount">
                {{ row.totalAmount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
              </template>
              <template #poQuantity="{row}">
              <span v-if="row.poQuantity">
                {{ row.poQuantity?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
              </template>
              <template #totalPoAmount="{row}">
              <span v-if="row.totalPoAmount">
                {{ row.totalPoAmount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
              </template>
              <template #supplierUnitPrice="{row}">
              <span v-if="row.supplierUnitPrice">
                {{ row.supplierUnitPrice?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
              </template>
              <template #supplierAmount="{row}">
              <span v-if="row.supplierAmount">
                {{ row.supplierAmount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
              </template>
              <template #shippedSOAmount="{row}">
              <span v-if="row.shippedSOAmount">
                {{ row.shippedSOAmount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
              </template>
              <template #conDelQuantity="{row}">
              <span>
                {{ row.conDelQuantity?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
              </template>
              <template #shipQuantity="{row}">
              <span v-if="row.shipQuantity">
                {{ row.shipQuantity?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
              </template>
              <template #dict="{column,row}">
                <dict-tag :type="column.params.dict" :value="row[column.field]" />
              </template>
              <template #Remark="{row}">
                <show-or-edit
                  :value="row.remark"
                >
                  <vxe-input v-model="row.remark" />
                </show-or-edit>
              </template>
              <template #urgent="{row}">
                <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="row.urgent" v-if="row.status==='Closed' ||row.status==='cancel'" />
                <el-switch
                  v-else
                  v-model="row.urgent"
                  @change="saveUrgent(row)"
                />
              </template>
              <template #poNo="{column,row}">
                <copy-button type="text" @click="$router.push(`/om/poInfo/${row.poId}?no=${row.poNo}&viewOnly=true`)">{{ row.poNo }}</copy-button>
              </template>
              <template #soNo="{column,row}">
                <copy-button type="text" @click="$router.push(`/om/soInfo/${row.soId}?no=${row.soNo}&viewOnly=true`)">
                  {{ row.soNo }}
                </copy-button>
              </template>
              <!--            <template #status="{row}">
                <dict-tag :type="DICT_TYPE.OM_SO_STATUS" :value="row.status" />
              </template>-->
              <template #currency="{row}">
                <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.currency" />
              </template>
              <template #supplierCurrency="{row}">
                <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.supplierCurrency" />
              </template>
              <!--发运日期-->
              <template #shipDate="{row}">
                <div style="display: flex; align-items: center;">
                  <!-- 显示日期 -->
                  <span style="padding-right: 10px">{{ row.shipDate ? parseTime(row.shipDate, '{y}-{m}-{d}') : '' }}</span>
                  <!-- 增加操作按钮，适当调整左边的间距 -->
                  <OperateDropDown
                    v-if="row.shipDate"
                    :menu-item="[
                    {
                      name: $t('common.operationRecord'),
                      show: $store.getters.permissions.includes('om:order-tracker:query'),
                      action: (row) => showLogs(row.shipId, 'om_ship_master', ['shipDate']),
                      para: row
                    },
                  ]"
                  />
                </div>
              </template>
              <!--最新要求交付日期-->
              <template #poReqDelDate="{row}">
                <div style="display: flex; align-items: center;">
                  <!-- 显示日期 -->
                  <span style="padding-right: 10px">{{ row.poReqDelDate ? parseTime(row.poReqDelDate, '{y}-{m}-{d}') : '' }}</span>
                  <!-- 增加操作按钮，适当调整左边的间距 -->
                  <OperateDropDown
                    v-if="row.poReqDelDate&&row.conDelDate"
                    :menu-item="[
                    {
                      name: $t('common.operationRecord'),
                      show: true,
                      action: (row) => showLogs(row.shipDetailId, 'om_ship_detail', ['reqDelDate','conDelDate','conDelQuantity']),
                      para: row
                    },
                  ]"
                  />
                </div>
              </template>
              <template #poConDelDate="{row}">
                <div style="display: flex; align-items: center;">
                  <!-- 显示日期 -->
                  <span style="padding-right: 10px">{{ row.poConDelDate ? parseTime(row.poConDelDate, '{y}-{m}-{d}') : '' }}</span>
                  <!-- 增加操作按钮，适当调整左边的间距 -->
                  <OperateDropDown
                    v-if="row.poReqDelDate&&row.conDelDate"
                    :menu-item="[
                    {
                      name: $t('common.operationRecord'),
                      show: true,
                      action: (row) => showLogs(row.shipDetailId, 'om_ship_detail', ['reqDelDate','conDelDate','conDelQuantity']),
                      para: row
                    },
                  ]"
                  />
                </div>
              </template>
              <!--供应商开票日期-->
              <template #supplierInvoiceData="{row}">
                <div style="display: flex; align-items: center;">
                  <!-- 显示日期 -->
                  <span style="padding-right: 10px">{{ row.supplierInvoiceData ? parseTime(row.supplierInvoiceData, '{y}-{m}-{d}') : '' }}</span>
                  <!-- 增加操作按钮，适当调整左边的间距 -->
                  <OperateDropDown
                    v-if="row.supplierInvoiceData"
                    :menu-item="[
                    {
                      name: $t('common.operationRecord'),
                      show: $store.getters.permissions.includes('om:order-tracker:query'),
                      action: (row) => showLogs(row.shipDetailId, 'om_ship_detail', ['supplierInvoiceData']),
                      para: row
                    },
                  ]"
                  />
                </div>
              </template>
              <!--付款截止日期-->
              <template #paymentDueDate="{row}">
                <div style="display: flex; align-items: center;">
                  <!-- 显示日期 -->
                  <span style="padding-right: 10px">{{ row.paymentDueDate ? parseTime(row.paymentDueDate, '{y}-{m}-{d}') : '' }}</span>
                  <!-- 增加操作按钮，适当调整左边的间距 -->
                  <OperateDropDown
                    v-if="row.paymentDueDate"
                    :menu-item="[
                    {
                      name: $t('common.operationRecord'),
                      show: $store.getters.permissions.includes('om:order-tracker:query'),
                      action: (row) => showLogs(row.shipDetailId, 'om_ship_detail', ['paymentDueDate']),
                      para: row
                    },
                  ]"
                  />
                </div>
              </template>
              <template #operate="{row}">
                <OperateDropDown
                  :menu-item="[
                  {
                    name: $t('om.DeliveryDateLog'),
                    show: $store.getters.permissions.includes('om:order-tracker:query'),
                    action: (row) => showLogs(row.shipDetailId,'om_ship_detail',['reqDelDate','conDelDate','conDelQuantity']),
                    para: row
                  },
                  {
                    name: $t('om.DeleteSO'),
                    show: $store.getters.permissions.includes('om:order-tracker:delete-so'),
                    action: (row) => deleteData(row,'so'),
                    para: row
                  },
                  {
                    name: $t('om.DeletePO'),
                    show: $store.getters.permissions.includes('om:order-tracker:delete-po'),
                    action: (row) => deleteData(row,'po'),
                    para: row
                  },
                  {
                    name: $t('om.DeleteShip'),
                    show: $store.getters.permissions.includes('om:order-tracker:delete-ship'),
                    action: (row) => deleteData(row,'ship'),
                    para: row
                  }
                ]"
                />
              </template>
              <template #toolbar_buttons>
                <el-row :gutter="24" style="width: 100%" class="mb8">
                  <el-col :span="22" style="display: flex">
                    <el-button
                      v-hasPermi="['om:order-tracker:new-order-tracker']"
                      :loading="exportLoading"
                      icon="el-icon-upload2"
                      type="primary"
                      @click="uploadInsert.open = true"
                    >{{ $t('om.BatchAddOrders') }}
                    </el-button>
                    <el-dropdown v-has-permi="['om:order-tracker:update-delivery-date','om:order-tracker:split-ship']" style="padding-left: 10px;padding-right: 10px">
                      <el-button type="primary" size="medium"  icon="el-icon-edit-outline">
                        {{ $t('om.MaintainDeliveryDate') }}<i class="el-icon-arrow-down el-icon--right" />
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <!--确认交期-->
                        <el-dropdown-item v-hasPermi="['om:order-tracker:update-delivery-date']" @click.native="checkSelect('confirmDeliveryDate');">{{ $t('om.ConfirmDeliveryDate') }}</el-dropdown-item>
                        <el-dropdown-item v-hasPermi="['om:order-tracker:update-customer-pay']" @click.native="splitShipQuantity();">{{ $t('om.SplitShipmentQuantity') }}</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                    <el-button v-hasPermi="['om:order-tracker:update-ship']" icon="el-icon-edit-outline" type="primary" @click="checkSelect('maintainShip');">{{ $t('om.MaintainTransportation') }}</el-button>
                    <el-dropdown v-has-permi="['om:order-tracker:update-supplier-pay','om:order-tracker:update-customer-pay']" style="padding-left: 10px;">
                      <el-button type="primary" size="medium"  icon="el-icon-edit-outline">
                        {{ $t('om.MaintainPayment') }}<i class="el-icon-arrow-down el-icon--right" />
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <!--确认交期-->
                        <el-dropdown-item v-hasPermi="['om:order-tracker:update-supplier-pay']" @click.native="checkSelect('maintainPayment');">{{ $t('om.MaintainSupplierPayment') }}</el-dropdown-item>
                        <el-dropdown-item v-hasPermi="['om:order-tracker:update-customer-pay']" @click.native="checkSelect('maintainPaymentByCustomer');">{{ $t('om.MaintainSOPayment') }}</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                    <!--批量维护更新-->
                    <el-dropdown v-has-permi="['om:order-tracker:update-delivery-date-excel','om:order-tracker:update-pay-excel']" style="padding-left: 10px;padding-right: 10px" @command="handleUpdate">
                      <el-button type="primary" size="medium"  icon="el-icon-upload2">
                        {{ $t('om.EXCELBatchChange') }}<i class="el-icon-arrow-down el-icon--right" />
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <!--确认交期-->
                        <el-dropdown-item v-hasPermi="['om:order-tracker:update-delivery-date-excel']" command="maintainDeliveryDate">{{ $t('om.MaintainDeliveryDate') }}</el-dropdown-item>
                        <!--维护运输-->
                        <!--                      <el-dropdown-item command="maintainShip">{{ $t('维护运输') }}</el-dropdown-item>-->
                        <!--维护付款-->
                        <el-dropdown-item v-hasPermi="['om:order-tracker:update-pay-excel']" command="maintainPayment">{{ $t('om.MaintainPayment') }}</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                    <!--资金预测-->
                    <el-button
                      v-hasPermi="['om:order-tracker:financial-forecast']"
                      size="mini"
                      icon="el-icon-download"
                      type="primary"
                      :loading="exportLoading"
                      @click="exportForecastExcel"
                    >{{ $t('om.FundsForecast') }}</el-button>
                    <el-button
                      v-hasPermi="['om:order-tracker:query']"
                      size="mini"
                      plain
                      type="primary"
                      :loading="exportLoading"
                      @click="exportExcel"
                      icon="el-icon-download"
                    >
                      {{ $t('order.download') }}
                    </el-button>
                  </el-col>
                  <el-col :span="2">
                    <right-toolbar :list-id="orderTrackerOptionTable.id" :show-search.sync="showSearch" :custom-columns.sync="orderTrackerOptionTable.columns" @queryTable="getListInit" />
                  </el-col>
                </el-row>
              </template>
            </vxe-grid>
            <pagination
              v-show="total > 0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNo"
              :total="total"
              @pagination="getList()"
            />
          </div>
        </el-card>
      </div>
      <div v-if=" selectedTab === 'Overview'">
        <od-overview :statistic="statistic"></od-overview>
      </div>

    </div>
    <!--导入印度 order tracker-->
    <el-dialog :title="upload.title" :visible.sync="upload.open" append-to-body width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url"
          :auto-upload="false"
          :data="queryParams"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-preview="onPreview"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button plain type="primary" @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <!-- 供应商导入对话框 -->
    <el-dialog :title="uploadInsert.title" :visible.sync="uploadInsert.open" width="430px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="uploadInsert"
          :action="uploadInsert.url"
          :auto-upload="false"
          :disabled="uploadInsert.isUploading"
          :headers="uploadInsert.headers"
          :limit="1"
          :on-progress="handleInsertTemplateFileUploadProgress"
          :on-success="handleInsertTemplateFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadInsert.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="importInsertTemplate(null)"> {{ $t('Download Template') }}</el-button>
        <el-button type="primary" @click="submitInsertTemplateForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <!--确认交期-->
    <el-dialog :title="$t('Confirm Delivery Date')" :visible.sync="confirmDeliveryDateVisible" append-to-body width="460px">
      <div>
        <el-form ref="confirmDeliveryDate" :model="confirmDeliveryDate" label-width="180px">
          <!-- 要求交期 -->
          <el-form-item :label="$t('PO Req. Del.Date')">
            <el-date-picker
              v-model="confirmDeliveryDate.poReqDelDate"
              type="date"
              :placeholder="$t('select PO Req. Del.Date')"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>

          <!-- 确认交期 -->
          <el-form-item :label="$t('Supplier Con. Del.Date')">
            <el-date-picker
              v-model="confirmDeliveryDate.conDelDate"
              type="date"
              :placeholder="$t('select Supplier Con. Del.Date')"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <!--供应商发票号-->
          <el-form-item :label="$t('ESIC Inv.')">
            <el-input v-model="confirmDeliveryDate.invoiceNo" :placeholder="$t('Please enter the ESIC Inv.')" style="width: 220px" />
          </el-form-item>
          <!--供应商发票号-->
          <el-form-item :label="$t('Vend Inv.')">
            <el-input v-model="confirmDeliveryDate.supplierInvoiceNo" :placeholder="$t('Please enter the Vend Inv.')" style="width: 220px" />
          </el-form-item>
          <!-- 供应商开票日 -->
          <el-form-item :label="$t('Vend Inv. Date')">
            <el-date-picker
                v-model="confirmDeliveryDate.supplierInvoiceData"
                type="date"
                :placeholder="$t('select Vend Inv. Date')"
                value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <!--备注-->
          <el-form-item :label="$t('Status')">
            <el-select v-model="confirmDeliveryDate.status" class="searchValue" clearable style="width: 220px">
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SO_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <!-- 提交按钮靠右对齐，并且与表单有一段距离 -->
          <div class="button-container">
            <el-button @click="confirmDeliveryDateVisible = false">{{ $t('common.cancel') }}</el-button>
            <el-button type="primary" @click="updateOrderTracker('confirmDeliveryDate')">{{ $t('Submit') }}</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
    <!--维护运输-->
    <el-dialog :title="$t('Maintain Transportation')" :visible.sync="maintainShipVisible" append-to-body width="460px">
      <div>
        <el-form ref="maintainShip" :model="maintainShip" label-width="180px">

          <!-- 发运日期 -->
          <el-form-item :label="$t('Ship Date')">
            <el-date-picker
              v-model="maintainShip.shipDate"
              type="date"
              :placeholder="$t('select ship date')"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <!-- 确认交期 -->
          <el-form-item :label="$t('Shipped QTY')">
            <el-input v-model="maintainShip.shipQuantity" type="number" max="100" min="0" :placeholder="$t('When the Ship Date is maintained, the Shipped QTY will default to equal the Supplier Confirmed Delivery QTY')" style="width: 220px" />
          </el-form-item>
          <!--运输发票-->
          <el-form-item :label="$t('Ship Inv.')">
            <el-input v-model="maintainShip.shippingInvoiceNo" :placeholder="$t('Please enter the Ship Inv.')" style="width: 220px" />
          </el-form-item>
          <!--跟踪号-->
          <el-form-item :label="$t('AWB#')">
            <el-input v-model="maintainShip.trackingNumber" :placeholder="$t('Please enter the AWB#')" style="width: 220px" />
          </el-form-item>

          <!--货代-->
          <el-form-item :label="$t('FWD')">
            <el-input v-model="maintainShip.forwarder" :placeholder="$t('Please enter the FWD')" style="width: 220px" />
          </el-form-item>

          <!--发运地点-->
          <el-form-item :label="$t('Ship To')">
            <el-select v-model="maintainShip.shipTo" class="searchValue" clearable filterable style="width: 220px">
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SHIP_TO)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <!--货物所在地-->
          <el-form-item :label="$t('Goods On')">
            <el-input v-model="maintainShip.goodsOn" :placeholder="$t('Please enter the Goods On')" style="width: 220px" />
          </el-form-item>
          <!--装箱单号-->
          <el-form-item :label="$t('PL#')">
            <el-input v-model="maintainShip.plNo" :placeholder="$t('Please enter the PL#')" style="width: 220px" />
          </el-form-item>
          <!--装箱单号-->
          <el-form-item :label="$t('AIR/SEA')">
            <el-select v-model="maintainShip.deliveryType" clearable placeholder="Please enter the AIR/SEA" style="width: 220px">
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.OM_DELIVERY_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-if="maintainShip.deliveryType==='express'" :label="$t(' ')">
            <el-radio-group  v-model="maintainShip.deliveryTypeDescription">
              <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.OM_EXPRESS_TYPE)"
                        :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 提交按钮靠右对齐，并且与表单有一段距离 -->
          <div class="button-container">
            <el-button @click="maintainShipVisible = false">{{ $t('common.cancel') }}</el-button>
            <el-button type="primary" @click="updateOrderTracker('maintainShip')">{{ $t('Submit') }}</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
    <!--维护付款-->
    <el-dialog :title="$t('Maintain Payment')" :visible.sync="maintainPaymentVisible" append-to-body width="460px">
      <div>
        <el-form ref="maintainPayment" :model="maintainPayment" label-width="180px">
          <!--供应商发票号-->
          <el-form-item :label="$t('Vend Inv.')">
            <el-input v-model="maintainPayment.supplierInvoiceNo" :placeholder="$t('Please enter the Vend Inv.')" style="width: 220px" />
          </el-form-item>

          <!-- 供应商开票日 -->
          <el-form-item :label="$t('Vend Inv. Date')">
            <el-date-picker
              v-model="maintainPayment.supplierInvoiceData"
              type="date"
              :placeholder="$t('select Vend Inv. Date')"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>

          <!--是否支付-->
          <el-form-item :label="$t('Paid PO%')">
            <el-input v-model="maintainPayment.paid" type="number" max="100" min="0" :placeholder="$t('Please enter the Paid PO%')" style="width: 220px" />
          </el-form-item>

          <!--付款凭证-->
          <el-form-item :label="$t('SO TT Copy')">
            <el-input v-model="maintainPayment.ttCopy" :placeholder="$t('Please enter the SO TT Copy')" style="width: 220px" />
          </el-form-item>

          <!-- 付款到期日 -->
          <el-form-item :label="$t('Pay. Due Date')">
            <el-date-picker
              v-model="maintainPayment.paymentDueDate"
              type="date"
              :placeholder="$t('select Pay. Due Date')"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>

          <!--备注-->
          <el-form-item :label="$t('Remark')">
            <el-input v-model="maintainPayment.remark" :placeholder="$t('Please enter the Remark')" style="width: 220px" />
          </el-form-item>
          <!--备注-->
          <el-form-item :label="$t('Status')">
            <el-select v-model="maintainPayment.soStatus" class="searchValue" clearable style="width: 220px">
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SO_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <!-- 提交按钮靠右对齐，并且与表单有一段距离 -->
          <div class="button-container">
            <el-button @click="maintainPaymentVisible = false">{{ $t('common.cancel') }}</el-button>
            <el-button type="primary" @click="updateOrderTracker('maintainPayment')">{{ $t('Submit') }}</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
    <!--维护付款-->
    <el-dialog :title="$t('Maintain Customer Payment')" :visible.sync="maintainPaymentByCustomerVisible" append-to-body width="460px">
      <div>
        <el-form ref="maintainPaymentByCustomer" :model="maintainPaymentByCustomer" label-width="180px">
          <!--供应商发票号-->
          <el-form-item :label="$t('ESIC Inv.')">
            <el-input v-model="maintainPaymentByCustomer.invoiceNo" :placeholder="$t('Please enter the ESIC Inv.')" style="width: 220px" />
          </el-form-item>
          <!--是否支付-->
          <el-form-item :label="$t('SO Paid%')">
            <el-input v-model="maintainPaymentByCustomer.soPaid" type="number" max="100" min="0" :placeholder="$t('Please enter the SO Paid%')" style="width: 220px" />
          </el-form-item>
          <!-- 付款到期日 -->
          <el-form-item :label="$t('SO Paid Date')">
            <el-date-picker
              v-model="maintainPaymentByCustomer.soPaidDate"
              type="date"
              :placeholder="$t('select SO Paid Date')"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <!-- 提交按钮靠右对齐，并且与表单有一段距离 -->
          <div class="button-container">
            <el-button @click="maintainPaymentByCustomerVisible = false">{{ $t('common.cancel') }}</el-button>
            <el-button type="primary" @click="updateOrderTracker('maintainPaymentByCustomer')">{{ $t('Submit') }}</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
    <!--操作记录-->
    <el-dialog
      v-if="log.open"
      :close-on-click-modal="false"
      :title="$t('common.operationRecord')"
      :visible.sync="log.open"
      width="1000px"
    >
      <operation-record
        :business-id="log.businessId"
        :columns="log.columns"
        :log-visible.sync="log.open"
      />
    </el-dialog>
    <el-dialog
      v-if="splitVisible"
      :visible.sync="splitVisible"
      :title="$t('Split Shipment')"
    >
      <el-descriptions>
        <el-descriptions-item :label="$t('SO NO#')">{{ splitData.soNo }}</el-descriptions-item>
        <el-descriptions-item :label="$t('PO NO#')">{{ splitData.poNo }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Supplier')">{{ splitData.supplier }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Part No.')">{{ splitData.partNo }}</el-descriptions-item>
        <el-descriptions-item :label="$t('MFG')">{{ splitData.mfg }}</el-descriptions-item>
        <el-descriptions-item :label="$t('MPN')">{{ splitData.mpn }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Order Quantity')">{{ splitData.poQuantity }}</el-descriptions-item>
      </el-descriptions>

      <el-table :data="splitDataTable">
        <el-table-column :label="$t('PO Req. Del.Date')" prop="poReqDelDate" align="center">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.reqDelDate"
              type="date"
              :disabled="!['New','To be Confirmed','To be Delivered','Pending'].includes(scope.row.status)"
              placeholder="Select Date"
              value-format="yyyy-MM-dd"
            />
          </template>
        </el-table-column>

        <el-table-column :label="$t('Supplier Con. Del.Date')" prop="supplierConDelDate" align="center">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.conDelDate"
              type="date"
              :disabled="!['New','To be Confirmed','To be Delivered','Pending'].includes(scope.row.status)"
              placeholder="Select Date"
              value-format="yyyy-MM-dd"
            />
          </template>
        </el-table-column>

        <el-table-column :label="$t('Supplier Con.Del.QTY')" prop="supplierConDelQty" align="center">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.conDelQuantity"
              :min="0"
              :disabled="!['New','To be Confirmed','To be Delivered','Pending'].includes(scope.row.status)"
              :controls="false"
            />
          </template>
        </el-table-column>

        <el-table-column :label="$t('Status')" prop="status" align="center">
          <template slot-scope="scope">

            <dict-tag :value="scope.row.status" :type="DICT_TYPE.OM_SO_STATUS" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('Operation')" prop="status" align="center">
          <template slot-scope="scope">
            <i
              class="el-icon-circle-plus"
              style="margin-left:10px;font-size: 18px;cursor: pointer"
              @click="splitDataTable.push({
                conDelQuantity:'',
                conDelDate:'',
                reqDelDate:scope.row.reqDelDate,
                status: scope.row.status==='Pending'?scope.row.status:'To be Confirmed',
                poDetailId:scope.row.poDetailId,
                poId:scope.row.poId
              })"
            />
            <i
              class="el-icon-remove"
              style="margin-left:10px;font-size: 18px;cursor: pointer"
              @click="splitDataTable.length===1?
                splitDataTable = [{

                }]
                :splitDataTable.splice(scope.$index,1)"
            />
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary" @click="confirmSplit">Confirm</el-button>
        <el-button @click="splitVisible =false">Cancel</el-button>

      </div>

    </el-dialog>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import {
  deleteOrderTrackerData, exportMaintainDeliveryDate, exportMaintainPayment, exportNewOrderTracker,
  exportOrderTracker, getConfirmedById, getOrderStatistics,
  exportForecast,
  getOrderTrackerPage, getOrderTrackerStatistics, getSearchResult,
  saveConfirmed,
  updateOrderTracker,saveOrderTrackerUrgent
} from '@/api/om/orderTracker'
import { formatNumNoZero, parseTime } from '@/utils/ruoyi'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getBaseHeader } from '@/utils/request'
import operationRecord from '@/components/OperationRecord/operationRecord.vue'
import dayjs from 'dayjs'
import { debounce } from 'throttle-debounce'
import OdOverview from './odOverview.vue'
import event from '@/views/dashboard/mixins/event'

export default defineComponent({
  name: 'Omordertrackerindex',
  components: { OdOverview, operationRecord, OperateDropDown },
  mixins: [event],
  data() {
    return {
      showHead: false,
      customers: [],
      buyers: [],
      total: 0,
      list: [],
      orderTrackerStatisticsData: [],
      title: '',
      open: false,
      loading: false,
      showSearch: true,
      exportLoading: false,
      queryParams: {
        search: '',
        no: '',
        customer: null,
        isInPacking: null,
        partNo: '',
        mfg: '',
        mpn: '',
        supplier: '',
        buyer: '',
        soStatus: [],
        shipStatus: [],
        paid: [],
        soTTCopy: '',
        dateType: '',
        time: [],
        pageNo: 1,
        pageSize: 10,
        sortBy: 'ASC',
        sortField: '',
        status: '',
        soPaid: [],
        poNos: [],
        soNos: [],
        partNos: [],
        paymentTerm: [],
        urgent:null,
      },
      orderTrackerOptionTable: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'orderTrackerTable',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        editConfig: {
          trigger: 'click',
          mode: 'row',
          beforeEditMethod: this.beforeEditMethod
        },
        columns: [
          { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
          { title: this.$t('Urgent'),field: 'urgent', width: 60, visible: true, fixed: 'left',
            slots: { default: 'urgent' }},
          {
            title: this.$t('SO NO#'),
            field: 'soNo',
            visible: true,
            width: 100,
            fixed: 'left',
            params: { field: 'soNos' },
            slots: { default: 'soNo', header: 'batchSearch' }
          },
          {
            title: this.$t('PO NO#'),
            field: 'poNo',
            visible: true,
            width: 100,
            fixed: 'left',
            params: { field: 'poNos' },
            slots: { default: 'poNo', header: 'batchSearch' }
          },
          {
            title: this.$t('PO Line'),
            field: 'poLine',
            visible: true,
            width: 100,
            fixed: 'left',
          },
          { title: this.$t('Supplier'), field: 'supplier', visible: true, width: 100, fixed: 'left' },
          {
            title: this.$t('Part No.'),
            field: 'partNo',
            visible: true,
            width: 100,
            fixed: 'left',
            params: { field: 'partNos' },
            slots: { default: 'partNo', header: 'batchSearch' }
          },
          { title: this.$t('Wk'), field: 'wk', visible: true, width: 100 },
          {
            title: this.$t('SO Date'), field: 'soDate', visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          { title: this.$t('Customer'), field: 'customer', visible: true, width: 100 },
          { title: this.$t('Status'), field: 'status', visible: true, width: 100 },
          { title: this.$t('Buyer'), field: 'buyer', visible: true, width: 100 },
          { title: this.$t('Currency'), field: 'currency', visible: true, width: 100, slots: { default: 'currency' } },
          {
            title: this.$t('U/P'),
            field: 'unitPrice',
            visible: true,
            width: 100,
            align: 'right',
            slots: { default: 'unitPrice' }
          },
          {
            title: this.$t('SO Quantity'),
            field: 'orderQuantity',
            visible: true,
            width: 100,
            align: 'right',
            slots: { default: 'orderQuantity' }
          },
          {
            title: this.$t('Total Amount'),
            field: 'totalAmount',
            visible: true,
            width: 100,
            align: 'right',
            slots: { default: 'totalAmount' }
          },
          {
            title: this.$t('SO Req. Del.Date'), field: 'reqDelDate', visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          {
            title: this.$t('SO Con. Del.Date'), field: 'conDelDate', visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          {
            title: this.$t('SO Paid%'),
            field: 'soPaid',
            visible: true,
            width: 100,
            slots: { default: 'soPaid' },
            align: 'right'
          },
          {
            title: this.$t('SO Paid Date'), field: 'soPaidDate', visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          {
            title: this.$t('PO Date'), field: 'poDate', visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          { title: this.$t('Description'), field: 'description', visible: true, width: 100 },
          { title: this.$t('Version'), field: 'version', visible: true, width: 100 },
          { title: this.$t('UOM'), field: 'uom', visible: true, width: 100 },
          {
            title: this.$t('Order Quantity'),
            field: 'poQuantity',
            visible: true,
            width: 100,
            align: 'right',
            slots: { default: 'poQuantity' }
          },
          {
            title: this.$t('Pur. Amount'),
            field: 'totalPoAmount',
            visible: true,
            width: 100,
            align: 'right',
            slots: { default: 'totalPoAmount' }
          },
          {
            title: this.$t('Pur. U/P'),
            field: 'supplierUnitPrice',
            visible: true,
            width: 100,
            align: 'right',
            slots: { default: 'supplierUnitPrice' }
          },
          {
            title: this.$t('Supplier Currency'),
            field: 'supplierCurrency',
            visible: true,
            width: 100,
            slots: { default: 'supplierCurrency' }
          },
          {
            title: this.$t('PO Req. Del.Date'),
            field: 'poReqDelDate',
            visible: true,
            width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            slots: { default: 'poReqDelDate' }
          },
          {
            title: this.$t('Supplier Con. Del.Date'),
            field: 'poConDelDate',
            visible: true,
            width: 100,
            sortable: true,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            slots: { default: 'poConDelDate' }
          },
          {
            title: this.$t('Supplier Con.Del.QTY'),
            field: 'conDelQuantity',
            visible: true,
            width: 100,
            slots: { default: 'conDelQuantity' }
          },
          {
            title: this.$t('Ship Date'), field: 'shipDate', visible: true, width: 100,
            slots: { default: 'shipDate' }
          },
          {
            title: this.$t('Shipped QTY'),
            field: 'shipQuantity',
            visible: true,
            width: 100,
            align: 'right',
            slots: { default: 'shipQuantity' }
          },
          {
            title: this.$t('Shipped Amount'),
            field: 'supplierAmount',
            visible: true,
            width: 100,
            align: 'right',
            slots: { default: 'supplierAmount' }
          },
          {
            title: this.$t('Shipped SO Amount'),
            field: 'shippedSOAmount',
            visible: true,
            width: 100,
            align: 'right',
            slots: { default: 'shippedSOAmount' }
          },
          {
            title: this.$t('Ship to'),
            field: 'shipTo',
            visible: true,
            width: 100,
            slots: { default: 'dict' },
            params: { dict: DICT_TYPE.OM_SHIP_TO }
          },
          { title: this.$t('Goods on'), field: 'goodsOn', visible: true, width: 100 },
          { title: this.$t('ESIC Inv.'), field: 'invoiceNo', visible: true, width: 100 },
          { title: this.$t('AWB#'), field: 'trackingNumber', visible: true, width: 100 },
          { title: this.$t('FWD'), field: 'forwarder', visible: true, width: 100 },
          {
            title: this.$t('AIR/SEA'),
            field: 'deliveryType',
            slots: { default: 'deliveryType' },
            visible: true,
            width: 100
          },
          { title: this.$t('Ship Inv.'), field: 'shippingInvoiceNo', visible: true, width: 100 },
          { title: this.$t('Vend Inv.'), field: 'supplierInvoiceNo', visible: true, width: 100 },

          {
            title: this.$t('Vend Inv. Date'),
            field: 'supplierInvoiceData',
            visible: true,
            width: 100,
            slots: { default: 'supplierInvoiceData' }
          },
          { title: this.$t('Pay. Term'), field: 'paymentTerm', visible: true, width: 100 },
          {
            title: this.$t('Pay. Due Date'), field: 'paymentDueDate', visible: true, width: 100,
            slots: { default: 'paymentDueDate' }
          },
          { title: this.$t('Lead Time(day)'), field: 'leadTime', visible: true, width: 100 },

          {
            title: this.$t('Paid PO%'),
            field: 'paid',
            visible: true,
            width: 100,
            slots: { default: 'paid' },
            align: 'right'
          },
          { title: this.$t('Paid Amount'), field: 'paidAmount', visible: true, width: 100},
          { title: this.$t('SO TT Copy'), field: 'ttCopy', visible: true, width: 100 },
          {
            title: this.$t('Remark'),
            field: 'remark',
            visible: true,
            width: 100,
            slots: { edit: 'Remark' },
            editRender: {}
          },
          {
            title: this.$t('In Shipping Tracker'),
            field: 'isInPacking',
            visible: true,
            width: 100,
            slots: { default: 'dict' },
            params: { dict: DICT_TYPE.INFRA_BOOLEAN_STRING }
          },
          { title: this.$t('MFG'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('MPN'), field: 'mpn', slots: { default: 'mpn' }, visible: true, width: 100 },
          { title: this.$t('Category'), field: 'category', visible: true, width: 100, slots: { default: 'dict' }, params: { dict: DICT_TYPE.COMMON_CATEGORY } }
        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        },
        showFooter: true,
        // 此代码不能删除，会导致计算合并的列和滚动条失效
        footerData: []
      },
      debouncedEditClosed: debounce(50, this.editClosed), // 设置防抖延迟时间，单位毫秒
      // 上传相关
      uploadInsert: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: this.$t('Batch Add/Change'),
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/om/order-tracker/import-new-order-tracker',
        type: null
      },
      // 上传相关
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: this.$t('batch import'),
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: '',
        type: null
      },
      // 确认交期
      confirmDeliveryDateVisible: false,
      confirmDeliveryDate: {
        id: null,
        poReqDelDate: null,
        conDelDate: null,
        conDelQuantity: null,
        supplierInvoiceNo: null,
        supplierInvoiceData: null,
        invoiceNo: '',
        status: null
      },
      // 维护运输
      maintainShipVisible: false,
      maintainShip: {
        id: null,
        shipDate: null,
        shippingInvoiceNo: null,
        trackingNumber: null,
        forwarder: null,
        shipTo: null,
        goodsOn: null,
        plNo: null,
        shipQuantity: null,
        deliveryType: null,
        deliveryTypeDescription: null
      },
      // 维护付款
      maintainPaymentVisible: false,
      maintainPayment: {
        id: null,
        supplierInvoiceNo: null,
        supplierInvoiceData: null,
        paid: null,
        ttCopy: null,
        paymentDueDate: null,
        remark: null
      },
      // 维护客户付款
      maintainPaymentByCustomerVisible: false,
      maintainPaymentByCustomer: {
        id: null,
        soPaid: null,
        soPaidDate: null,
        invoiceNo: ''
      },
      // 操作日志
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: [
          {
            tableName: 'order_detail',
            searchColumns: ['status']
          }
        ]
      },
      commonColumn: [],
      columnType: '',
      headData: [
        {
          icon: 'shopping',
          label: 'SO(Line)',
          color: '#4996B8',
          field: 'soCount',
          field1: 'soLineCount',
          unit: ''
        },
        {
          icon: 'shopping',
          label: 'SO Value',
          color: '#E2C052',
          field: 'soAmount',
          unit: '$'
        },
        {
          icon: 'ep-list',
          label: 'PO(Line)',
          color: '#4996B8',
          field: 'poCount',
          field1: 'poLineCount',
          unit: ''
        },
        {
          icon: 'ep-list',
          label: 'PO Value',
          color: '#E2C052',
          field: 'poAmount',
          unit: '$'
        }
      ],
      selectedTab: 'Order',
      statistic: {
        materialCount: 0,
        supplierCount: 0,
        soCount: 0,
        soLineCount: 0,
        soAmount: 0,
        poCount: 0,
        poLineCount: 0,
        poAmount: 0,
        commission: 0,
        materialCountAll:0,
        supplierCountAll:0,
        commissionAll:0,
        customerVOList:[]
      },
      splitVisible: false,
      splitData: {},
      splitDataTable: [],
      totalCount: 0,
      filterDialog: false,
      filterField: ''
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  mounted() {
    this.getListInit()
    this.getStatic()
    let tempShowHead= window.localStorage.getItem('orderTracker_showHead')
    if(tempShowHead!=null){
      this.showHead=JSON.parse(tempShowHead)
    }
  },
  activated() {
  },
  created() {
    this.getOrderTrackerStatistics()
    this.initDDL()

  },
  methods: {
    saveUrgent(row){
      console.log(row)
      saveOrderTrackerUrgent({
        shipDetailId: row.shipDetailId,
        urgent: row.urgent
      })
    },
    // 不同维度的表头颜色区分
    headerCellClassName({ column, columnIndex }) {
      if (column.field === 'poReqDelDate' || column.field === 'poConDelDate' || column.field === 'conDelQuantity') {
        return 'row-green'
      }
      return null
    },
    showStatisticians() {
      this.showHead = !this.showHead;
      window.localStorage.setItem('orderTracker_showHead', this.showHead);
    },
    // 供应商提交交期编辑后触发
    editClosed({row, rowIndex}) {
      const saveEntity = [{
        shipDetailUpdateReqVO: {
          id: row.shipDetailId,
          remark: row.remark,
          shipId: row.shipId
        }
      }]
      updateOrderTracker(saveEntity).then(res => {
        if (res.data) {
          this.$message.success(this.$t('order.operationSucceeded'))
        }
      }).catch(() => {
      })
    },
    beforeEditMethod({row}) {
      return true
    },
    getDictDatas,
    /**
     * 初始化客户和Buyer的下拉框数据
     */
    initDDL() {
      getSearchResult({
        searchValue: null,
        type: 'customer'
      }).then(res => {
        this.customers = res.data
      })
      getSearchResult({
        searchValue: null,
        type: 'buyer'
      }).then(res => {
        this.buyers = res.data
      })
    },
    getStatic() {
      getOrderStatistics({ customer: this.queryParams.customer }).then(res => {
        for (const key in res.data) {
          if (res.data[key] !== '***' && typeof res.data[key] === 'number') {
            res.data[key] = Math.floor(res.data[key]).toLocaleString()
          }
        }
        res.data.customerVOList.forEach(a=>{
          a.typeValue = Math.floor(a.typeValue).toLocaleString()
          if (a.typeValueSub){
            a.typeValueSub = Math.floor(a.typeValueSub).toLocaleString()
          }
        })
        this.statistic = res.data
      })
      this.getOrderTrackerStatistics();
    },
    querySearchAsync(queryString, cb, type) {
      if (queryString.length < 2) {
        return
      }
      getSearchResult({
        searchValue: queryString,
        type: type
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item
          }
        }))
      })
    },
    handleSelect(item, type) {
      switch (type) {
        case 'so':
          this.queryParams.no = item.value
          break
        case 'customer':
          this.queryParams.customer = item.value
          break
        case 'buyer':
          this.queryParams.buyer = item.value
          break
        case 'supplier':
          this.queryParams.supplier = item.value
          break
        case 'mfg':
          this.queryParams.mfg = item.value
          break
        // eslint-disable-next-line no-fallthrough
        default:
          break
      }
    },
    /** 下载模板操作 */
    importInsertTemplate(type) {
      exportNewOrderTracker().then(response => {
        this.$download.excel(response, 'New Order Template.xlsx')
      })
    },
    submitInsertTemplateForm() {
      this.$refs.uploadInsert.submit()
    },
    // 文件上传中处理
    handleInsertTemplateFileUploadProgress(event, file, fileList) {
      this.uploadInsert.isUploading = true
    },
    // 文件上传成功处理
    handleInsertTemplateFileSuccess(response, file, fileList) {
      this.uploadInsert.loading = false
      this.uploadInsert.open = false
      this.uploadInsert.isUploading = false
      this.$refs.uploadInsert.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.create) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.create.length
      }
      if (data.failure) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + Object.keys(data.failure).length
        for (const index in data.failure) {
          text += '<br />&nbsp;&nbsp;&nbsp;&nbsp; ' + this.$t('rfq.lineNumber') + index + '：' + data.failure[index]
        }
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getListInit()
    },
    /** 查询 */
    getOrderTrackerStatistics() {
      this.loading = true
      // 执行查询
      getOrderTrackerStatistics(this.queryParams.customer===null ? '':this.queryParams.customer).then(response => {
        const sortMap = new Map(getDictDatas(DICT_TYPE.OM_SO_STATUS).map((item, index) => [item.value.toLowerCase(), index]))
        if (response.data) {
          response.data.sort((a, b) => (sortMap.get(a.shipStatus?.toLowerCase())) - (sortMap.get(b.shipStatus?.toLowerCase())))
          this.totalCount = response.data.reduce((total, item) => total + item.count, 0)?.toLocaleString()
          response.data.forEach(a => {
            a.count = a.count.toLocaleString()
          })
          this.orderTrackerStatisticsData = response.data
        }
        this.loading = false
      })
    },
    /** 删除功能 */
    deleteData(row, type) {
      this.$modal.confirm(this.$t('Do you confirm the deletion of the data？')).then(function () {
        let id = 0
        if (type === 'so') {
          id = row.soId
        } else if (type === 'po') {
          id = row.poId
        } else {
          id = row.shipId
        }
        return deleteOrderTrackerData({ id: id, type: type })
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    exportForecastExcel() {
      this.$modal.confirm(this.$t('Do you confirm the export of the funds forecast data?')).then(() => {
        this.exportLoading = true
        return exportForecast(this.queryParams)
      }).then(response => {
        const formattedDate = dayjs().format('YYYYMMDD')
        this.$download.excel(response, formattedDate + this.$t('Funds Forecast.xlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    exportExcel() {
      this.$modal.confirm(this.$t('Do you confirm the download?')).then(() => {
        this.exportLoading = true
        //  UFFF-1501-导出调整字段
        return exportOrderTracker(this.queryParams)
      }).then(response => {
        const formattedDate = dayjs().format('YYYYMMDD')
        this.$download.excel(response, 'Order Tracker' + formattedDate + '.xlsx')
        this.exportLoading = false
      }).catch(() => {
      })
    },
    getListInit() {
      // 跨页后使用前面页数据进行搜索，此时未重置pageNo导致搜索结果为空
      this.queryParams.pageNo = 1
      this.getList()
    },
    // 获取列表数据
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      getOrderTrackerPage(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
        this.orderTrackerOptionTable.footerData.length = 0
        this.orderTrackerOptionTable.footerData.push( {
          soNo: 'Total',
          paidAmount: Number(formatNumNoZero(res.data.totalValues.totalPaidAmount)).toLocaleString(),
          poQuantity:Number(formatNumNoZero(res.data.totalValues.totalOrderQuantity)).toLocaleString(),
          totalPoAmount:Number(formatNumNoZero(res.data.totalValues.totalPurAmount)).toLocaleString(),
          orderQuantity:Number(formatNumNoZero(res.data.totalValues.totalSoQty)).toLocaleString(),
          totalAmount:Number(formatNumNoZero(res.data.totalValues.totalSoAmount)).toLocaleString(),
          shipQuantity:Number(formatNumNoZero(res.data.totalValues.totalShipQty)).toLocaleString(),
          supplierAmount:Number(formatNumNoZero(res.data.totalValues.totalShipAmount)).toLocaleString(),
          shippedSOAmount:Number(formatNumNoZero(res.data.totalValues.shippedSOAmount)).toLocaleString(),
          conDelQuantity:Number(formatNumNoZero(res.data.totalValues.conDelQuantityAmount)).toLocaleString()
        })
      })
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.getList()
    },
    handleReset() {
      this.queryParams = {
        search: '',
        no: '',
        customer: '',
        partNo: '',
        mfg: '',
        mpn: '',
        supplier: '',
        buyer: '',
        soStatus: [],
        shipStatus: [],
        paid: [],
        dateType: '',
        time: [],
        pageNo: 1,
        soTTCopy: '',
        pageSize: 10,
        sortBy: 'ASC',
        sortField: '',
        status: '',
        soPaid: [],
        poNos: [],
        soNos: [],
        partNos: []
      }
      this.getListInit()
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.loading = false
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.create) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.create.length
      }
      if (data.failure) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + Object.keys(data.failure).length
        for (const index in data.failure) {
          text += '<br />&nbsp;&nbsp;&nbsp;&nbsp; ' + this.$t('rfq.lineNumber') + index + '：' + data.failure[index]
        }
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getListInit()
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.url) {
        window.open(file.url)
      }
    },
    // upload bom data
    submitFileForm() {
      this.$refs.upload.submit()
    },
    // 获取导入模板
    importTemplate() {
      switch (this.upload.type) {
        case 'maintainDeliveryDate':
          exportMaintainDeliveryDate(this.queryParams).then(response => {
            this.$download.excel(response, 'Maintain Delivery Date.xlsx')
          })
          break
        case 'maintainPayment':
          exportMaintainPayment(this.queryParams).then(response => {
            this.$download.excel(response, 'Maintain Payment.xlsx')
          })
          break
        default:
          return
      }
    },
    // update the order tracker information
    updateOrderTracker(mark) {
      const selectRows = this.$refs.orderTrackerTable.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to modify'))
        return false
      }
      let saveEntity = []
      switch (mark) {
        case 'confirmDeliveryDate':
          saveEntity = selectRows.map(j => {
            return {
              poDetailUpdateReqVO: {
                id: j.shipDetailId,
                shipId: j.shipId,
                reqDelDate: this.confirmDeliveryDate.poReqDelDate,
                conDelDate: this.confirmDeliveryDate.conDelDate,
                supplierInvoiceNo: this.confirmDeliveryDate.supplierInvoiceNo,
                supplierInvoiceData: this.confirmDeliveryDate.supplierInvoiceData,
                invoiceNo: this.confirmDeliveryDate.invoiceNo,
                status: this.confirmDeliveryDate.status
              }
            }
          })
          break
        case 'maintainShip':
          saveEntity = selectRows.map(j => {
            return {
              shipMasterUpdateReqVO: {
                id: j.shipId,
                shipDetailId: j.shipDetailId,
                poDetailId: j.poDetailId,
                shipDate: this.maintainShip.shipDate,
                shippingInvoiceNo: this.maintainShip.shippingInvoiceNo,
                trackingNumber: this.maintainShip.trackingNumber,
                forwarder: this.maintainShip.forwarder,
                shipTo: this.maintainShip.shipTo,
                goodsOn: this.maintainShip.goodsOn,
                plNo: this.maintainShip.plNo,
                deliveryType: this.maintainShip.deliveryType,
                quantity: this.maintainShip.shipQuantity,
                customer:j.customer,
                deliveryTypeDescription:this.maintainShip.deliveryTypeDescription
              }
            }
          })
          console.log(saveEntity)
          break
        case 'maintainPayment':
          saveEntity = selectRows.map(j => {
            return {
              shipDetailUpdateReqVO: {
                id: j.shipDetailId,
                supplierInvoiceNo: this.maintainPayment.supplierInvoiceNo,
                supplierInvoiceData: this.maintainPayment.supplierInvoiceData,
                paid: this.maintainPayment.paid,
                ttCopy: this.maintainPayment.ttCopy,
                paymentDueDate: this.maintainPayment.paymentDueDate,
                remark: this.maintainPayment.remark,
                status: this.maintainPayment.soStatus,
                shipId: j.shipId
              }
            }
          })
          break
        case 'maintainPaymentByCustomer':
          saveEntity = selectRows.map(j => {
            return {
              maintainPaymentByCustomerReqVO: {
                id: j.shipDetailId,
                invoiceNo: this.maintainPaymentByCustomer.invoiceNo,
                soPaid: this.maintainPaymentByCustomer.soPaid,
                soPaidDate: this.maintainPaymentByCustomer.soPaidDate,
                shipId: j.shipId
              }
            }
          })
          break
      }
      updateOrderTracker(saveEntity).then(res => {
        if (res.data) {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.confirmDeliveryDateVisible = false
          this.maintainShipVisible = false
          this.maintainPaymentVisible = false
          this.maintainPaymentByCustomerVisible = false
          this.getList()
        }
      }).catch(() => {
      })
    },
    // check if select record
    checkSelect(mark) {
      const selectRows = this.$refs.orderTrackerTable.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to modify'))
        return false
      } else {
        switch (mark) {
          case 'confirmDeliveryDate':
            this.confirmDeliveryDateVisible = true

            // 如果选中一行，赋值
            if (selectRows.length === 1) {
              const selectedRow = selectRows[0]
              this.confirmDeliveryDate = {...this.confirmDeliveryDate, ...selectedRow}
              this.confirmDeliveryDate.poReqDelDate = this.confirmDeliveryDate.poReqDelDate ? parseTime(this.confirmDeliveryDate.poReqDelDate, '{y}-{m}-{d}') : null;
              this.confirmDeliveryDate.conDelDate = this.confirmDeliveryDate.conDelDate ? parseTime(this.confirmDeliveryDate.conDelDate, '{y}-{m}-{d}') : null;
              this.confirmDeliveryDate.supplierInvoiceData = this.confirmDeliveryDate.supplierInvoiceData ? parseTime(this.confirmDeliveryDate.supplierInvoiceData, '{y}-{m}-{d}') : null;
              // 安全赋值，确保对象属性存在
              /*   this.confirmDeliveryDate = {
                id: selectedRow.shipDetailId || null,
                reqDelDate: selectedRow.reqDelDate ||null,  // 转换日期为 ISO 字符串
                conDelDate: selectedRow.conDelDate||null,  // 转换日期为 ISO 字符串
                conDelQuantity: selectedRow.conDelQuantity || null,
                supplierInvoiceNo: selectedRow.supplierInvoiceNo || null,
                supplierInvoiceData: selectedRow.supplierInvoiceData || null,
                invoiceNo: selectedRow.invoiceNo || null,
                soStatus: selectedRow.soStatus || null
              }*/
            } else {
              // 初始化 confirmDeliveryDate 对象为 null 或其他默认值
              this.confirmDeliveryDate = {
                id: null,
                poReqDelDate: null,
                conDelDate: null,
                conDelQuantity: null,
                supplierInvoiceNo: null,
                supplierInvoiceData: null,
                invoiceNo: null,
                status: null
              }
            }
            break
          case 'maintainShip':
            this.maintainShipVisible = true
            this.maintainShip.id = null
            this.maintainShip.shipDate = null
            this.maintainShip.shipQuantity = null
            this.maintainShip.shippingInvoiceNo = null
            this.maintainShip.trackingNumber = null
            this.maintainShip.forwarder = null
            this.maintainShip.shipTo = null
            this.maintainShip.goodsOn = null
            this.maintainShip.plNo = null
            this.maintainShip.deliveryType = null
            break
          case 'maintainPayment':
            if(selectRows.length === 1){
              const selectedRow = selectRows[0]
              this.maintainPaymentVisible = true
              this.maintainPayment = {...this.maintainPayment, ...selectedRow}
              this.maintainPayment.supplierInvoiceData = this.maintainPayment.supplierInvoiceData ? parseTime(this.maintainPayment.supplierInvoiceData, '{y}-{m}-{d}') : null;
              this.maintainPayment.paymentDueDate = this.maintainPayment.paymentDueDate ? parseTime(this.maintainPayment.paymentDueDate, '{y}-{m}-{d}') : null;
              this.maintainPayment.soStatus = selectedRow.status
            }else {
              this.maintainPaymentVisible = true
              this.maintainPayment.id = null
              this.maintainPayment.supplierInvoiceNo = null
              this.maintainPayment.supplierInvoiceData = null
              this.maintainPayment.paid = null
              this.maintainPayment.ttCopy = null
              this.maintainPayment.paymentDueDate = null
              this.maintainPayment.remark = null
              this.maintainPayment.soStatus = null
            }
            break
          case 'maintainPaymentByCustomer':
            this.maintainPaymentByCustomerVisible = true
            this.maintainPaymentByCustomer.id = null
            this.maintainPaymentByCustomer.invoiceNo = null
            this.maintainPaymentByCustomer.soPaid = null
            this.maintainPaymentByCustomer.soPaidDate = null
            break
        }
      }
    },
    // show operate log
    showLogs(businessId, table, column) {
      this.log.open = true
      this.log.businessId = businessId
      this.log.columns = [{
        tableName: table,
        searchColumns: column
      }]
    },
    // batch maintain
    handleUpdate(command) {
      switch (command) {
        case 'maintainDeliveryDate':
          this.upload.title = this.$t('Maintain Delivery Date')
          this.upload.type = 'maintainDeliveryDate'
          this.upload.url = process.env.VUE_APP_BASE_API + '/admin-api/om/order-tracker/import-delivery-date'
          break
        case 'maintainPayment':
          this.upload.title = this.$t('Maintain PO Payment')
          this.upload.type = 'maintainPayment'
          this.upload.url = process.env.VUE_APP_BASE_API + '/admin-api/om/order-tracker/import-payment'
          break
        default:
          return
      }
      this.upload.open = true
    },
    // split ship confirm quantity
    splitShipQuantity() {
      // 选中行
      const selectRows = this.$refs.orderTrackerTable.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to modify'))
        return false
      } else if (selectRows.length > 1) {
        this.$message.warning(this.$t('You can only select one record to modify'))
        return
      } else {
        this.splitVisible = true
        this.splitData = selectRows[0]
        getConfirmedById({ poDetailId: this.splitData.poDetailId }).then(res => {
          res.data.forEach(a => {
            a.reqDelDate = a.reqDelDate ? dayjs(a.reqDelDate).format('YYYY-MM-DD') : ''
            a.conDelDate = a.conDelDate ? dayjs(a.conDelDate).format('YYYY-MM-DD') : ''
          })
          this.splitDataTable = res.data
        })
      }
    },

    confirmSplit() {
      saveConfirmed(this.splitDataTable).then(res => {
        this.$message.success('Submission Successful')
        this.splitVisible = false
        this.getList()
      })
    },
    handlePaste(event) {
      event.preventDefault();
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedData = clipboardData.getData('text');
      const content = pastedData
        .split('\n') // 按行分割
        .map(row => row.trim()).filter(a=>a)
      this.queryParams[this.filterField] = [...new Set([...this.queryParams[this.filterField], ...content])]
      this.$refs.filterField.blur()

    },
  }
})
</script>

<style scoped lang="scss">
.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 140px);
  }
  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}
.searchValue {
  width: 100%;
}
.headCard{
  padding: 5px;
  padding-bottom: 0;
  //margin-bottom: -1px;
  cursor: pointer;
  flex: 1 1 14.2%;

  &-body {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    height: 112px;
    padding: 15px;
    background: #F9F9F9;
    border-radius: 8px 8px 0 0;
    //border-bottom: 3px solid #4996B8;

    &-num{
      font-size: 28px;
      color: #383838;
      font-weight: 700;
    }
    &-label{
      font-size: 14px;
      color: #565656;
      font-weight: 400;
    }
  }
}
.headCard-selected{
  position: relative;
  bottom: -3px;
  background: rgba(73,150,184,0.10);
  border: 3px solid #4996B8;
  border-bottom: 4px solid #fff !important;
  border-radius: 8px 8px 0 0;
}
.button-container {
  margin-top: 20px; /* 设置按钮与表格的距离 */
  text-align: right; /* 使按钮靠右对齐 */
}
.overList{
  ::v-deep .el-autocomplete-suggestion{
    width: auto !important;
    min-width: 180px;
    max-width: 600px !important;
  }
}
.app-head{
  display: flex;
  justify-content: space-between;
  &-item{
    height: 112px;
    flex: 0 1 24%;
    border: 0.5px solid #C3C3C3;
    border-radius: 8px;
    display: flex;
    padding: 20px 3%;
    align-items: center;
  }
}
.headIcon{
  font-size: 48px;
  color: red;
}
.app-title{
  display: flex;
  margin-bottom: 0px;
  justify-content: space-between;
  &-tab{
    flex: none;
    font-size: 24px;
    color: #383838;
    letter-spacing: 0;
    font-weight: 400;
    cursor: pointer;
    span{
      margin: 0 15px;
    }
    &-selected{
      font-size: 28px;
      color: #383838;
      letter-spacing: 0;
      font-weight: 700;
      position: relative;
    }
    &-selected::after {
      content: "";
      position: absolute;
      border-radius: 2px;
      width: 20px; /* Adjust the width of the line */
      height: 4px; /* Adjust the thickness of the line */
      background-color: black; /* Adjust the line color */
      bottom: -6px; /* Adjust the position of the line relative to the text */
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
.des-content{
  min-width: 175px;
  font-weight: 700;
  font-size: 28px;
  position: relative;
}
.des-content-one{
  min-width: 150px;
  font-weight: 700;
  font-size: 28px;
}
.des-content::after {
  content: "|";
  color: #C3C3C3;
  font-weight: 100;
  position: absolute;
  right: 17%;
}
::v-deep .el-statistic .con .number{
  font-size: 28px;
}
.filterSelect {
  color: #4d93b9;
}
::v-deep .mytable-style .vxe-header--column.row-green {
  background-color: #59b8c0;
  color: #fff;
}
</style>
