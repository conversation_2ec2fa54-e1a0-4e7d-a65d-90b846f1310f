<template>
  <div class="app-container">
<!--    invoice base info-->
    <common-card :title="$t('Invoice Information')">
      <el-form ref="invoiceForm" :model="invoiceInfo" inline label-width="150px">
        <!--        invoice no-->
        <el-form-item
          :label="$t('Invoice NO#')"
          class="commonFormItem"
          prop="invoiceNo"
        >
          <show-or-edit :disabled="!editMode" :value="invoiceInfo.invoiceNo">
            <el-input v-model="invoiceInfo.invoiceNo" :disabled="true" />
          </show-or-edit>
        </el-form-item>
        <!--          del.term-->
        <el-form-item
          :label="$t('Del.Term')"
          class="commonFormItem"
          prop="delTerm"
        >
          <show-or-edit :disabled="!editMode" :value="invoiceInfo.delTerm" :dict="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION">
            <el-select v-model="invoiceInfo.delTerm" class="searchValue" :disabled="!editMode" clearable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <!--            pay.term-->
        <el-form-item
          :label="$t('Pay.Term')"
          class="commonFormItem"
          prop="payTerm"
        >
          <show-or-edit :disabled="!editMode" :value="invoiceInfo.payTerm" :dict="DICT_TYPE.SUPPLIER_PAYMENT_DAYS">
            <el-select v-model="invoiceInfo.payTerm" class="searchValue" :disabled="!editMode" clearable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>

          </show-or-edit>
        </el-form-item>
        <!--        attn-->
        <el-form-item
          :label="$t('Attn')"
          class="commonFormItem"
          prop="attn"
        >
          <show-or-edit :disabled="!editMode" :value="invoiceInfo.attn">
            <el-input v-model="invoiceInfo.attn"  />
          </show-or-edit>
        </el-form-item>
        <!--        email-->
        <el-form-item
          :label="$t('Email')"
          class="commonFormItem"
          prop="email"
        >
          <show-or-edit :disabled="!editMode" :value="invoiceInfo.email">
            <el-input v-model="invoiceInfo.email" />
          </show-or-edit>
        </el-form-item>
        <!--        tel-->
        <el-form-item
          :label="$t('Tel')"
          class="commonFormItem"
          prop="tel"
        >
          <show-or-edit :disabled="!editMode" :value="invoiceInfo.tel">
            <el-input v-model="invoiceInfo.tel" />
          </show-or-edit>
        </el-form-item>
        <!--        country of origin-->
        <el-form-item
          :label="$t('Country Of Origin')"
          class="commonFormItem"
          prop="countryOfOrigin"
        >
          <show-or-edit :disabled="!editMode" :value="invoiceInfo.countryOfOrigin">
            <el-input v-model="invoiceInfo.countryOfOrigin" />
          </show-or-edit>
        </el-form-item>
        <!--        address-->
        <el-form-item
          :label="$t('ADD')"
          class="commonFormItem"
          prop="address"
        >
          <show-or-edit :disabled="!editMode" :value="invoiceInfo.address">
            <el-input v-model="invoiceInfo.address" />
          </show-or-edit>
        </el-form-item>
        <!--        bill to-->
        <el-form-item
          :label="$t('Bill To')"
          class="commonFormItem"
          prop="billTo"
        >
          <show-or-edit :disabled="!editMode" :value="invoiceInfo.billTo">
            <el-input v-model="invoiceInfo.billTo" />
          </show-or-edit>
        </el-form-item>
        <!--        remarks-->
        <el-form-item
          :label="$t('Remarks')"
          class="commonFormItem"
          style="width: 100%"
          prop="remarks"
        >
          <show-or-edit :disabled="!editMode" :value="invoiceInfo.remarks">
            <el-input v-model="invoiceInfo.remarks" type="textarea" :rows="3" autosize :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <!--        attachment-->
        <el-form-item
          :label="$t('auth.uploadAttachments')"
          class="commonFormItem"
        >
          <div style="display: flex;justify-content: space-between">
            <el-upload
              class="upload-demo"
              :disabled="!editMode"
              :action="uploadInfo.uploadUrl"
              :headers="getBaseHeader()"
              :on-remove="onRemove"
              :on-preview="onPreview"
              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
              multiple
              :limit="5"
              :show-file-list="false"
              :file-list="invoiceInfo.files"
            >
              <el-button
                v-if="editMode"
                v-has-Permi="['om:invoice-master:update']"
                :disabled="!editMode"
                class="uploadBtn"
                size="small"
                plain
                icon="el-icon-plus"
                type="primary"
              />
            </el-upload>
            <div>
              {{ $t('scar.viewAttachments') }}
              <el-button
                class="uploadBtn"
                size="small"
                style="padding: 5px 9px"
                :disabled="invoiceInfo.files.length===0"
                plain
                :type="invoiceInfo.files.length?'primary':''"
                @click="uploadInfo.showFile=true"
              >
                {{ invoiceInfo.files.length }}
              </el-button>

              <el-dialog
                v-if="uploadInfo.showFile"
                :visible.sync="uploadInfo.showFile"
                :title="$t('scar.viewAttachments')"
                width="400px"
              >
                <el-upload
                  class="upload-demo"
                  :disabled="!editMode"
                  :action="uploadInfo.uploadUrl"
                  :headers="getBaseHeader()"
                  :on-remove="onRemove"
                  :on-preview="onPreview"
                  :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                  multiple
                  :limit="5"
                  :show-file-list="true"
                  :file-list="invoiceInfo.files"
                />
                <div slot="footer">
                  <el-button type="primary" @click="uploadInfo.showFile=false">{{ $t('order.close') }}</el-button>
                </div>

              </el-dialog>
            </div>
          </div>
        </el-form-item>
        <!--        button:update-->
        <div v-if="editMode" style="width: 100%;display:flex;justify-content: center">
          <el-button v-has-Permi="['om:invoice-master:update']" icon="el-icon-edit-outline" type="primary" @click="submitForm">{{ $t('Update') }}</el-button>
        </div>

      </el-form>
    </common-card>
<!--    parts of invoice-->
    <common-card
      :title="$t('Invoice Item List')"
    >
      <div style="margin-top: 25px;">
<!--        search-->
        <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
          <el-input
            v-model="queryParams.search"
            style="flex: 0 1 35%"
            :placeholder="$t('SO NO#/Part No.')"
            @keyup.enter.native="getListInit"
          />
          <el-button plain type="primary" @click="getListInit">
            {{ $t('common.search') }}
          </el-button>
          <el-button
            style="margin-left: 0"
            @click="handleReset"
          >
            {{ $t('common.reset') }}
          </el-button>
          <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
            <el-button type="text">
              {{ $t('common.advancedSearch') }}
            </el-button>
            <i
              :style="showSearch? '':{transform: 'rotate(180deg)'}"
              class="el-icon-arrow-up"
              style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
            />
          </div>
        </div>
        <el-form v-show="showSearch" inline label-width="120px">
          <!--          so no-->
          <el-form-item class="searchItem" :label="$t('SO NO#')" prop="">
              <el-autocomplete
                v-model="queryParams.soNo"
                :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'so')})"
                :placeholder="$t('SO NO#')"
                :popper-append-to-body="false"
                popper-class="el-autocomplete-suggestion"
                class="searchItem"
                @select="((item) => {handleSelect(item, 'so')})"
                @keyup.enter.native="getListInit"
              />
          </el-form-item>
          <!--          part no-->
          <el-form-item class="searchItem" :label="$t('Part No.')" prop="">
            <el-input v-model="queryParams.partNo" :placeholder="$t('Please enter content')" @keyup.enter.native="getListInit" />
          </el-form-item>
          <!--          version-->
          <el-form-item class="searchItem" :label="$t('Version')" prop="">
            <el-input v-model="queryParams.version" :placeholder="$t('Please enter content')" @keyup.enter.native="getListInit" />
          </el-form-item>
          <!--          description-->
          <el-form-item class="searchItem" :label="$t('Description')" prop="">
            <el-input v-model="queryParams.description" :placeholder="$t('Please enter content')" @keyup.enter.native="getListInit" />
          </el-form-item>
        </el-form>
        <vxe-grid
          ref="invoiceInfo"
          :data="list"
          :loading="loading"
          v-bind="invoiceDetailGrid"
        >

          <template #soNo="{row}">
            <copy-button type="text" @click="$router.push(`/om/soInfo/${row.soId}?no=${row.soNo}&viewOnly=true`)">
              {{ row.soNo }}
            </copy-button>
          </template>
          <template #quantity="{row}">
              <span v-if="row.quantity">
                {{ row.quantity?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #unitPrice="{row}">
              <span v-if="row.unitPrice">
                {{ row.unitPrice?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #amount="{row}">
              <span v-if="row.amount">
                {{ row.amount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>

          <template #operate="{row}">
            <OperateDropDown
              :menu-item="[
                {
                  name: $t('om.ChangeLog'),
                  show: $store.getters.permissions.includes('om:po-master:detail'),
                  action: (row) => showLogs(row.id,'om_po_detail',['partNo','description','mfg','mpn','version','unitPrice','quantity','uom','pricePer','reqDelDate','leadTime','remark'],['partNo']),
                  para: row
                },
              ]"
            />
          </template>
          <template #toolbar_buttons>
            <el-row :gutter="24" style="width: 100%" class="mb8">
              <el-col :span="22" style="display: flex">
                <el-button  :loading="exportLoading" size="mini" plain icon="el-icon-download" type="primary" @click="exportExcel">{{ $t('order.download') }}</el-button>
              </el-col>
              <el-col :span="2">
                <right-toolbar
                  :list-id="invoiceDetailGrid.id"
                  :show-search.sync="showSearch"
                  :custom-columns.sync="invoiceDetailGrid.columns"
                  @queryTable="getListInit"
                />
              </el-col>
            </el-row>
          </template>
        </vxe-grid>
        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNo"
          :total="total"
          @pagination="getList()"
        />
      </div>
    </common-card>

    <!--操作记录-->
    <el-dialog
      v-if="log.open"
      :close-on-click-modal="false"
      :title="$t('common.operationRecord')"
      :visible.sync="log.open"
      width="1000px"
    >
      <operation-record
        :business-id="log.businessId"
        :columns="log.columns"
        :log-visible.sync="log.open"
      />
    </el-dialog>

  </div>
</template>

<script>

import {defineComponent} from "vue";
import PoDetail from "@/views/om/poDetail.vue";
import OperateDropDown from "@/components/OperateDropDown/index.vue";
import ShowOrEdit from "@/components/ShowOrEdit/index.vue";
import operationRecord from "@/components/OperationRecord/operationRecord.vue";
import {DICT_TYPE, getDictDatas} from "@/utils/dict";
import {getBaseHeader} from "@/utils/request";
import {exportInvoiceDetailExcel, getInvoiceDetailPage, getInvoiceMaster, saveInvoiceMaster} from "@/api/om/pl/invoice";
import {exportPoDetailExcel} from "@/api/om/po";
import {getSearchResult} from "@/api/om/orderTracker";

export default defineComponent({
  name:'Invoiceinfo/:id',
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  components: {operationRecord, ShowOrEdit, OperateDropDown, PoDetail},

  data(){
    return {
      uploadInfo:{
        showFile:false,
        uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      },
      invoiceInfo:{
        invoiceId:null,
        invoiceNo: '',
        delTerm:'',
        payTerm:'',
        attn:'',
        email:'',
        tel:'',
        countryOfOrigin:'',
        address:'',
        billTo:'',
        remarks:'',
        status:'',
        files:[]
      },
      queryParams:{
        search:'',
        invoiceId:null,
        soNo:'',
        partNo:'',
        description:'',
        version:''
      },
      loading:false,
      exportLoading:false,
      list:[],
      total: 0,
      invoiceDetailGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'invoiceDetail',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { title: 'SO NO#', field: 'soNo', visible: true,  fixed: 'left',slots:{default: 'soNo'} },
          { title: 'Part No.', field: 'partNo', visible: true,  fixed: 'left' },
          { title: 'Description', field: 'description', visible: true, },
          { title: 'Version', field: 'version', visible: true },
          { title: 'Quantity', field: 'quantity', visible: true, slots: { default: 'quantity'},align: 'right' },
          { title: 'U/P', field: 'unitPrice', visible: true,  align: 'right' },
          { title: 'Amount', field: 'amount', visible: true,align: 'right'}
        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      showSearch: false,
      editMode:true,
      // 操作日志
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: []
      },
    }
  },

  mounted() {
    this.editMode = this.$route.query.viewOnly !== 'true'
    this.init()
    this.getList()
  },
  activated() {
    this.editMode = this.$route.query.viewOnly !== 'true'
    this.init()
    this.getList()
  },
  methods:{
    getBaseHeader,
    getDictDatas,
    init() {
      getInvoiceMaster(this.$route.params.id).then(res => {
        res.data.files?.forEach(a => {
          a.name = a.fileName
        })
        this.queryParams.invoiceId=this.$route.params.id
        this.invoiceInfo = res.data
        //结束的订单不允许编辑
        if(this.invoiceInfo.status==='closed'){
          this.editMode=false
        }
      })
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    getList() {
      this.loading = true
      console.log("Mark")
      console.log(this.queryParams);
      getInvoiceDetailPage({
        ...this.queryParams,
        invoiceId:this.$route.params.id
      }).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    querySearchAsync(queryString, cb, type) {
      if (queryString.length < 2) {
        return
      }
      getSearchResult({
        searchValue: queryString,
        type: type
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item
          }
        }))
      })
    },
    handleSelect(item, type) {
      switch (type) {
        case 'so':
          this.queryParams.soNo = item.value
          break
        case 'customer':
          this.queryParams.customer = item.value
          break
        case 'buyer':
          this.queryParams.buyer = item.value
          break
        case 'supplier':
          this.queryParams.supplier = item.value
          break
        case 'mfg':
          this.queryParams.mfg = item.value
          break
        // eslint-disable-next-line no-fallthrough
        default:
          break
      }
    },
    viewAttachments() {
      // Logic to view attachments
    },
    onRemove(file, fileList) {
      const index = this.invoiceInfo.files.indexOf(file.response?.data?.fileId) || this.invoiceInfo.files.indexOf(file.fileId)
      this.invoiceInfo.files.splice(index, 1)
    },
    onSuccess(response, file, fileList) {
      this.invoiceInfo.files.push({
        name: file.name,
        fileUrl: response.data.url,
        fileId: response.data.id
      })
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.fileUrl) {
        window.open(file.fileUrl)
      }
    },
    submitForm() {
      saveInvoiceMaster({ ...this.invoiceInfo,
        fileIds: this.invoiceInfo.files.map(a => a.fileId)
      }).then(res => {
        if (res.data){
          this.$message.success('Operation successful')
        }
        else{
          this.$message.success('Operation failed')
        }
      })
    },
    exportExcel() {
      this.$modal.confirm(this.$t('Do you confirm the download?')).then(() => {
        this.exportLoading = true
        return exportInvoiceDetailExcel({ ...this.queryParams
        })
      }).then(response => {
        this.$download.excel(response, this.$t('invoice parts.xlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    // show operate log
    showLogs(businessId, table, column,keyColumns) {
      this.log.open = true
      this.log.businessId = businessId
      this.log.columns = [{
        tableName: table,
        searchColumns: column,
        keyColumns:keyColumns
      }]
    },
    handleReset() {
      this.queryParams = {
        search:'',
        invoiceId:this.$route.params.id,
        soNo:'',
        partNo:'',
        description:'',
        version:''
      }
      this.getList()
    },
  }
})



</script>

<style scoped lang="scss">
.commonFormItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 150px);
  }
}
.searchValue{
  width: 100%;
}
.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}
::v-deep .avpl-row{
  background:#81D3F8 !important;
}
::v-deep .avpl-edit{
  background:#FFE668 !important;
}
.button-container {
  margin-top: 20px; /* 设置按钮与表格的距离 */
  text-align: right; /* 使按钮靠右对齐 */
}
</style>
