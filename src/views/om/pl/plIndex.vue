<template>
  <div class="app-container">
    <div style="margin-top: 25px;">
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.search"
          style="flex: 0 1 35%"
          :placeholder="$t('PL#/SO NO#/Ship Inv./Part No./Supplier')"
          @keyup.enter.native="getListInit"
        />
        <el-button plain type="primary" @click="getListInit">
          {{ $t('common.search') }}
        </el-button>
        <el-button
          style="margin-left: 0"
          @click="handleReset"
        >
          {{ $t('common.reset') }}
        </el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>
      </div>

      <el-form v-show="showSearch" inline label-width="120px">

        <el-form-item class="searchItem" :label="$t('PL#')" prop="plNo">
          <el-input v-model="queryParams.plNo" :placeholder="$t('PL#')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Ship Inv.')" prop="invoiceNo">
          <el-input v-model="queryParams.invoiceNo" :placeholder="$t('Ship Inv.')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('SO/PO NO#')" prop="soNo">
          <el-autocomplete
              v-model="queryParams.soNo"
              :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'so')})"
              :popper-append-to-body="false"
              popper-class="el-autocomplete-suggestion"
              class="searchItem"
              @select="((item) => {handleSelect(item, 'so')})"
              @keyup.enter.native="getListInit"
          />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Part No.')" prop="">
          <el-input v-model="queryParams.partNo" :placeholder="$t('Part No.、Description')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Status')" prop="">
          <el-select v-model="queryParams.status" class="searchValue" clearable filterable>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_PL_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Ship Date" class="searchItem">
          <el-date-picker
              v-model="queryParams.time"
              class="searchValue"
              value-format="yyyy-MM-dd"
              type="daterange"
              :range-separator="$t('order.to')"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
          />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Supplier')" prop="">
          <el-autocomplete
              v-model="queryParams.supplier"
              :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'supplier')})"
              :placeholder="$t('Supplier')"
              :popper-append-to-body="false"
              popper-class="el-autocomplete-suggestion"
              class="overList"
              @select="((item) => {handleSelect(item, 'supplier')})"
              @keyup.enter.native="getListInit"
          />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Customer')" prop="customer" style="width: 66%">
          <el-radio-group v-model="queryParams.customer" @change="getListInit();">
            <el-radio :label="null">{{ $t('order.whole') }}</el-radio>
            <el-radio v-for="customer in $store.getters.incapCustomers" :key="customer" :label="customer">
              {{ customer }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

      </el-form>
      <vxe-grid
        ref="plOverview"
        :data="list"
        :loading="loading"
        v-bind="plOverviewGrid"
      >

        <template #dict="{column,row}">
          <dict-tag :type="column.params.dict" :value="row[column.field]" />
        </template>
        <template #plNo="{column,row}">
          <copy-button type="text" @click="$router.push(`/om/plInfo/${row.id}?no=${row.plNo}`)">{{ row.plNo }}</copy-button>
        </template>
        <template #invoiceNo="{column,row}">
          <copy-button type="text" @click="$router.push(`/om/Invoiceinfo/${row.invoiceId}?no=${row.invoiceNo}`)">
            {{ row.invoiceNo }}
          </copy-button>
        </template>
        <template #deliveryType="{column,row}">
          <dict-tag :type="DICT_TYPE.OM_DELIVERY_TYPE" :value="row.deliveryType" />
        </template>
        <template #operate="{row}">
          <OperateDropDown
            :menu-item="[
              {
                name: $t('Edit Packing List'),
                show: $store.getters.permissions.includes('om:pl-master:update'),
                action: (row) => $router.push(`/om/plInfo/${row.id}?no=${row.plNo}`),
                para: row
              },
               {
                name: $t('Edit Invoice'),
                show: $store.getters.permissions.includes('om:invoice-master:update'),
                action: (row) => $router.push(`/om/Invoiceinfo/${row.invoiceId}?no=${row.invoiceNo}`),
                para: row
              },
              {
                name: $t('Maintain Shipping Tracker'),
                show: $store.getters.permissions.includes('om:pl-master:update-shipping-tracker'),
                action: (row) =>showShippingTracker(row),
                para: row
              },
              {
                name: $t('om.ChangeLog'),
                show: $store.getters.permissions.includes('om:pl-master:query'),
                action: (row) => showLogs(row.id,'om_pl_master',['shipTo','deliveryType','delTerm','forwarder','trackingNumber','shipDate','status'],['pl_no']),
                para: row
              },

            ]"
          />
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="24" style="width: 100%" class="mb8">
            <el-col :span="22" style="display: flex">
              <el-button size="mini" plain type="primary" icon="el-icon-edit-outline" :loading="operateLoading" @click="showConfirmForwarder">{{ $t('邮件客户确认货代/运输方式') }}</el-button>
              <el-button size="mini" plain type="primary" icon="el-icon-edit-outline" :loading="operateLoading" @click="requiredAwb">{{ $t('告知供应商（HUB）货代/运输方式') }}</el-button>
              <el-dropdown  style="padding-left: 10px;" v-hasPermi="['om:pl-master:query']">
                <el-button type="primary" size="medium"  icon="el-icon-edit-outline">
                  {{ $t('order.download') }}<i class="el-icon-download" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <!--download shipping tracker-->
                  <el-dropdown-item @click.native="exportExcel">{{ $t('Shipping Tracker') }}</el-dropdown-item>
                  <!--download packing list-->
                  <el-dropdown-item @click.native="exportPacking();">{{ $t('Packing List') }}</el-dropdown-item>
                  <!--download -->
                  <el-dropdown-item @click.native="exportInvoiceExcel();">{{ $t('Invoice') }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-col>
            <el-col :span="2">
            <right-toolbar
              :list-id="plOverviewGrid.id"
              :show-search.sync="showSearch"
              :custom-columns.sync="plOverviewGrid.columns"
              @queryTable="getListInit"
            />
            </el-col>
          </el-row>
        </template>

      </vxe-grid>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getList()"
      />
    </div>
    <!--操作记录-->
    <el-dialog
        v-if="log.open"
        :close-on-click-modal="false"
        :title="$t('common.operationRecord')"
        :visible.sync="log.open"
        width="1000px"
    >
      <operation-record
          :business-id="log.businessId"
          :columns="log.columns"
          :log-visible.sync="log.open"
      />
    </el-dialog>
    <!--维护货代-->
    <el-dialog :title="$t('Maintain Shipping Tracker')" :visible.sync="maintainShippingTrackerVisible" append-to-body width="460px">
      <div>
        <el-form ref="maintainShippingTracker" :model="maintainShippingTracker" label-width="150px">
          <!--FWD-->
          <el-form-item :label="$t('FWD')">
            <el-input v-model="maintainShippingTracker.forwarder" max="200" :placeholder="$t('Please enter the FWD')" style="width: 220px" />
          </el-form-item>
          <!--AWB#-->
          <el-form-item :label="$t('AWB#')">
            <el-input v-model="maintainShippingTracker.trackingNumber" max="200" min="0" :placeholder="$t('Please enter the AWB#')" style="width: 220px" />
          </el-form-item>
<!--          AIR/SEA-->
          <el-form-item :label="$t('AIR/SEA')">
            <el-select v-model="maintainShippingTracker.deliveryType" clearable placeholder="Please enter the AIR/SEA" style="width: 220px">
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.OM_DELIVERY_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <!--          delivery description-->
          <el-form-item v-if="maintainShippingTracker.deliveryType==='express'" :label="$t(' ')">
            <el-radio-group  v-model="maintainShippingTracker.deliveryTypeDescription">
              <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.OM_EXPRESS_TYPE)"
                        :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <!--contact-->
          <el-form-item :label="$t('Contact')">
            <el-input v-model="maintainShippingTracker.contact" max="200" :placeholder="$t('Please enter the Contact')" style="width: 220px" />
          </el-form-item>
          <!-- ship date -->
          <el-form-item :label="$t('Ship Date')">
            <el-date-picker
                v-model="maintainShippingTracker.shipDate"
                type="date"
                :placeholder="$t('select Ship Date')"
                value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <!-- 提交按钮靠右对齐，并且与表单有一段距离 -->
          <div class="button-container">
            <el-button @click="maintainShippingTrackerVisible = false">{{ $t('common.cancel') }}</el-button>
            <el-button plain type="primary" v-has-permi="['om:pl-master:release']" :loading="operateLoading" @click="ReleaseShip">{{ $t('Release Shipping') }}</el-button>
            <el-button type="primary" @click="saveShippingTracker(true)">{{ $t('Submit') }}</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
    <!--select the recipient for email-->
    <el-dialog
      :title="$t('select the recipient for the email')"
      :visible.sync="confirmEmail.show"
      append-to-body width="460px"
    >
      <div>
        <el-form ref="confirmEmail" :model="confirmEmail">
          <el-form-item :label="$t('Recipient')">
            <el-radio-group v-model="confirmEmail.recipient" size="small">
              <el-radio v-for="item in confirmEmail.recipients"
                        :key="item" :label="item">{{item}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <!--show text input when choose customize -->
            <el-input
              v-if="confirmEmail.recipient === 'customize'"
              v-model="confirmEmail.email"
              placeholder="Please input recipient email address"
              size="small"
              style="margin-top: 20px;"
            />
          </el-form-item>
          <!-- 提交按钮靠右对齐，并且与表单有一段距离 -->
          <div class="button-container" style="padding-bottom: 10px">
            <el-button @click="confirmEmail.show=false">{{ $t('common.cancel') }}</el-button>
            <el-button type="primary" :loading="confirmEmail.loading" @click="submitConfirmEmail">{{ $t('Submit') }}</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>

    <!--select the recipient for email-->
    <el-dialog
      :title="$t('Confirm Email')"
      :visible.sync="confirmCustomerEmail.show"
      append-to-body width="460px"
    >
      <div>
        <el-form ref="confirmEmail" :model="confirmCustomerEmail">
          <el-form-item :label="$t('客户邮箱')">
            <!--show text input when choose customize -->
            <el-input
              v-model="confirmCustomerEmail.customer"
              placeholder="Please input recipient email address"
              size="small"
              style="margin-top: 20px;"
            />
          </el-form-item>
          <el-form-item :label="$t('采购邮箱')">
            <!--show text input when choose customize -->
            <el-input
              v-model="confirmCustomerEmail.purchase"
              placeholder="Please input recipient email address"
              size="small"
              style="margin-top: 20px;"
            />
          </el-form-item>
          <!-- 提交按钮靠右对齐，并且与表单有一段距离 -->
          <div class="button-container" style="padding-bottom: 10px">
            <el-button @click="confirmCustomerEmail.show=false">{{ $t('common.cancel') }}</el-button>
            <el-button type="primary" :loading="confirmCustomerEmail.loading" @click="confirmForwarder">{{ $t('Submit') }}</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
  </div>

</template>
<script>
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { getSearchResult, updateOrderTracker } from '@/api/om/orderTracker'
import { parseTime } from '@/utils/ruoyi'
import {DICT_TYPE, getDictDatas} from '@/utils/dict'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import operationRecord from '@/components/OperationRecord/operationRecord.vue'
import {
  confirmEmail,
  confirmForwarder,
  exportShipTracker, getTargetMail, noticeStartShipping, requiredAwb,
  savePLMaster,
  shipTrackerPage,
  startShip
} from '@/api/om/pl/pl'
import {createForm, updateForm} from "@/api/bpm/form";
import {exportDeliveryPlan, exportInvoice, exportPacking} from "@/api/om/pl/shippingPlan";
export default {
  name: 'Plindex',
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  components: { operationRecord,BatchUpload, OperateDropDown },
  data() {
    return {
      queryParams: {
        search: '',
        poNo: '',
        invoiceNo:'',
        supplier: '',
        soNo:'',
        partNo: '',
        status: '',
        shipDateStart: '',
        shipDateEnd: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: '',
        customer:''
      },
      total: 0,
      list: [],
      showSearch: false,
      loading: false,
      operateLoading:false,
      plOverviewGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'plOverview',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
          { title: 'PL#', field: 'plNo', slots: { default: 'plNo' }, visible: true, width: 130, fixed: 'left' },
          { title: 'Ship Inv.', field: 'invoiceNo', slots: { default: 'invoiceNo' }, visible: true, width: 130, fixed: 'left' },
          { title: 'Customer', field: 'customer', visible: true, width: 100},
          { title: this.$t('Ship Date'), field: 'shipDate', visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''},
          { title: '# of SOs', field: 'soCount', visible: true, width: 100,align: 'right'},
          { title: '# of parts', field: 'partCount', visible: true, width: 100,align: 'right' },
          { title: '# of Suppliers', field: 'supplierCount', visible: true, width: 100,align: 'right' },
          { title: 'FWD', field: 'Forwarder', visible: true, width: 100 },
          { title: 'AWB#', field: 'trackingNumber', visible: true, width: 100 },
          { title: 'AIR/SEA', field: 'deliveryType', slots:{default:'deliveryType'}, visible: true, width: 100 },
          { title: 'Status', field: 'status', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_PL_STATUS }, visible: true, width: 100 },
          { title: '# of Cartons', field: 'carton', visible: true, width: 100,align: 'right'},
          { title: 'Net Weight(KG)', field: 'netWeight', visible: true, width: 100,align: 'right'},
          { title: 'Gross Weight(KG)', field: 'grossWeight', visible: true, width: 100,align: 'right'},
          { title: 'Volume(M3)', field: 'volume', visible: true, width: 100,align: 'right'},
          {
            field: 'operate',
            slots: { default: 'operate' }, fixed: 'right',
            title: this.$t('common.operate'), visible: true, width: 35, showOverflow: false
          }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      // 操作日志
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: []
      },
      // 维护货代
      maintainShippingTrackerVisible: false,
      maintainShippingTracker:{
        // 是否显示弹出层
        id: null,
        forwarder:null,
        trackingNumber:null,
        deliveryType:null,
        deliveryTypeDescription:null,
        contact:null,
        shipDate:null
      },
      confirmEmail:{
        show:false,
        recipient:'customize',  //customize or supplier
        email:'',
        recipients:['customize','supplier'],
        rules: {
          email: [{ required: true, message: "email address is required", trigger: "blur" }]
        },
        loading:false
      },
      confirmCustomerEmail:{
        show:false,
        customer:"",
        purchase:"",
        plId:null,
        loading:false
      },
    }
  },
  mounted() {
    this.getList()
  },
  activated() {
    this.getList()
  },
  methods: {
    getDictDatas,
    // show operate log
    showLogs(businessId, table, column,keyColumns) {
      this.log.open = true
      this.log.businessId = businessId
      this.log.columns = [{
        tableName: table,
        searchColumns: column,
        keyColumns:keyColumns
      }]
    },
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.shipDateStart = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.shipDateEnd = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      shipTrackerPage(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },

    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    handleSelect(item, type) {
      switch (type) {
        case 'so':
          this.queryParams.no = item.value
          break
        case 'customer':
          this.queryParams.customer = item.value
          break
        case 'buyer':
          this.queryParams.buyer = item.value
          break
        case 'supplier':
          this.queryParams.supplier = item.value
          break
        case 'mfg':
          this.queryParams.mfg = item.value
          break
        // eslint-disable-next-line no-fallthrough
        default:
          break
      }
    },
    querySearchAsync(queryString, cb, type) {
      if (queryString.length < 2) {
        return
      }
      getSearchResult({
        searchValue: queryString,
        type: type
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item
          }
        }))
      })
    },
    handleReset() {
      this.queryParams = {
        search: '',
        soNo: '',
        payTerm: [],
        soStatus: [],
        supplier: '',
        buyer: '',
        delTerm: [],
        partNo: '',
        dateType: '',
        shipDateStart: '',
        shipDateEnd: '',
        customer: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      }
      this.getList()
    },
    batchReleaseShip() {
      const selectRows = this.$refs.plOverview.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to release!'))
        return false
      } else {
        this.$modal.confirm('Confirm the release?').then(() => {
          this.operateLoading=true
          return startShip(selectRows.map(row => row.id).join(','))
        }).then(res => {
          if (res.data) {
            this.$message.success('Operation Successful')
            this.getList()
          }
        }).finally(() => {
          this.operateLoading = false
        })
      }
    },
    ReleaseShip() {
      this.$modal
        .confirm(this.$t('Do you confirm start the transportation?'))
        .then(async () => {
          this.operateLoading = true;

          try {
            // Ensure saveShippingTracker completes before moving to startShip
            await this.saveShippingTracker(false); // Wait for this to complete
            // Now execute startShip after saveShippingTracker is done
            const res = await startShip(this.maintainShippingTracker.id); // Wait for startShip to complete
            // Handle the response from startShip
            if (res.data) {
              this.$message.success('Operation Successful');
              this.maintainShippingTrackerVisible = false;
              this.getList();
            }
          } catch (error) {
            // Handle any errors
            console.error(error);
            this.$message.error('An error occurred.');
          } finally {
            this.operateLoading = false; // Always turn off the loading state
          }
        });
    },
    exportExcel() {
      this.operateLoading=true
      this.$modal.confirm(this.$t('Do you confirm the export of the Shipping Tracker data?')).then(() => {
        this.operateLoading=true
        return exportShipTracker(this.queryParams)
      }).then(res=>{
        this.$download.excel(res, 'Shipping Tracker.xlsx')
      }).finally(() => {
        this.operateLoading = false
      })
    },

    exportPacking() {
      this.$modal.confirm(this.$t('Do you confirm the export of the Packing List data?')).then(() => {
        this.operateLoading=true
        return exportPacking(this.queryParams)
      }).then(res => {
        this.$download.excel(res, 'Packing List.xlsx')
      }).finally(() => {
        this.operateLoading = false
      })
    },
    exportInvoiceExcel() {
      this.$modal.confirm(this.$t('Do you confirm the export of the Invoice data?')).then(() => {
        this.operateLoading=true
        return exportInvoice(this.queryParams)
      }).then(res => {
        this.$download.excel(res, 'Invoice.xlsx')
      }).finally(() => {
        this.operateLoading = false
      })
    },


    /**
     * 显示更新货代窗口
     * @param row
     */
    showShippingTracker(row){
      this.maintainShippingTracker={...this.maintainShippingTracker,plId: row.id,...row}
      this.maintainShippingTracker.shipDate=this.maintainShippingTracker.shipDate?parseTime(this.maintainShippingTracker.shipDate, '{y}-{m}-{d}'):null;
      this.maintainShippingTrackerVisible=true
    },
    /**
     * 保存货代信息
     */
    saveShippingTracker(showPrompt){
      this.operateLoading=true
      savePLMaster(this.maintainShippingTracker).then(res => {
        if (res.data) {
          if (showPrompt) {
            this.$message.success(this.$t('order.operationSucceeded'))
          }
          this.maintainShippingTrackerVisible = false
          this.getList()
        }
      }).catch(() => {}).finally(() => {
        this.operateLoading = false
      })
    },
    //show confirm email
    showConfirmEmail(){
      this.confirmEmail.email=''
      this.confirmEmail.recipient='customize'
      this.confirmEmail.show=true;
    },
    //resend weekly reminder mail to supplier to confirm delivery date
    submitConfirmEmail(){
      this.$refs["confirmEmail"].validate(valid => {
        if (!valid) {
          return;
        }
        this.confirmEmail.loading=true;
        // send mail
        confirmEmail(this.confirmEmail).then(response => {
          if (response.data){
            this.$modal.msgSuccess("Operation successful");
            this.confirmEmail.loading=false
            this.confirmEmail.show=false
          }else{
            this.$modal.msgSuccess("Operation failed");
            this.confirmEmail.loading=false
          }
        }).catch(err => {
          this.confirmEmail.loading=false
        });
      });
    },
    //send mail to customer:confirm forwarder and delivery type
    showConfirmForwarder(){
      const selectRows = this.$refs.plOverview.getCheckboxRecords()
      if (selectRows.length === 0){
        this.$message.warning(this.$t('Please select the record to operate!'))
        return false
      }
      if (selectRows.length > 1){
        this.$message.warning(this.$t('Please select only one record to operate!'))
        return false
      }
      this.$modal.confirm('确认发送邮件给客户获取货代和运输方式?').then(() => {
        return getTargetMail(selectRows[0].id).then(res=>{
          this.confirmCustomerEmail.customer=res.data.customerMail;
          this.confirmCustomerEmail.purchase=res.data.procurementEmail;
          this.confirmCustomerEmail.show=true;
          this.confirmCustomerEmail.plId=selectRows[0].id
        })
      }).then(res => {
      })
    },

    confirmForwarder(){
      this.confirmCustomerEmail.loading=true
      confirmForwarder({plId: this.confirmCustomerEmail.plId, customerMail: this.confirmCustomerEmail.customer, procurementEmail: this.confirmCustomerEmail.purchase}).then(res=>{
        if (res.data) {
          this.$message.success('Operation Successful')
          this.confirmCustomerEmail.show=false
          this.confirmCustomerEmail.loading=false
          this.getList()
        }
      }).catch(e=>{
        console.log(e)
        this.confirmCustomerEmail.loading=false
      })
    },
    //send mail to supplier:notice delivery type and required the AWB
    requiredAwb(){
      const selectRows = this.$refs.plOverview.getCheckboxRecords()
      if (selectRows.length === 0){
        this.$message.warning(this.$t('Please select the record to operate!'))
        return false
      }
      if (selectRows.length > 1){
        this.$message.warning(this.$t('Please select only one record to operate!'))
        return false
      }
      this.$modal.confirm('确认发送邮件给供应商通知其运货方式?').then(() => {
        return  requiredAwb(selectRows[0].id)
      }).then(res => {
        if (res.data) {
          this.$message.success('Operation Successful')
          this.getList()
        }
      })
    },
    //send mail to customer:notice the packing list start to shipping
    noticeStartShipping(){
      const selectRows = this.$refs.plOverview.getCheckboxRecords()
      if (selectRows.length === 0){
        this.$message.warning(this.$t('Please select the record to operate!'))
        return false
      }
      if (selectRows.length > 1){
        this.$message.warning(this.$t('Please select only one record to operate!'))
        return false
      }
      noticeStartShipping(selectRows[0].id).then(res => {
        if (res.data) {
          this.$message.success('Operation Successful')
          this.getList()
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.button-container {
  margin-top: 20px; /* 设置按钮与表格的距离 */
  text-align: right; /* 使按钮靠右对齐 */
}
.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }

  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}

.searchValue {
  width: 100%;
}

</style>
