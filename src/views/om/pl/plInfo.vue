<template>
  <div class="app-container">
    <common-card :title="$t('Packing List Basic Information')">
      <el-form ref="orderForm" :model="orderData" inline label-width="180px">
        <el-form-item class="commonFormItem" :label="$t('PL#')">
          {{orderData.plNo}}
        </el-form-item>
        <!--Total Amount-->
        <el-form-item class="commonFormItem" :label="$t('TotalAmount')">
          {{orderData.totalAmount}}$
        </el-form-item>
        <!--plan ship date-->
        <el-form-item
          :label="$t('Plan Ship Date')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.planShipDate">
            <el-date-picker
              v-model="orderData.planShipDate"
              class="searchValue"
              :disabled="!editMode"
              :placeholder="$t('order.selectDate')"
              type="date"
              value-format="timestamp"
            />
          </show-or-edit>
        </el-form-item>
<!--        Carton(s)-->
        <el-form-item
          label="Carton(s)"
          class="commonFormItem"
        >
          <span>{{orderData.carton}}</span>
        </el-form-item>
<!--        Net-Weight(KG)-->
        <el-form-item
          label="Net-Weight(KG)"
          class="commonFormItem"
        >
          <span>{{orderData.netWeight}}</span>
        </el-form-item>
<!--        Gross-Weight(KG)-->
        <el-form-item
          label="Gross-Weight(KG)"
          class="commonFormItem"
        >
          <span>{{orderData.grossWeight}}</span>
        </el-form-item>
<!--        Volume(m3)-->
        <el-form-item
          label="Volume(m3)"
          class="commonFormItem"
        >
          <span>{{orderData.volume}}</span>
        </el-form-item>
        <!--ship type-->
        <el-form-item class="commonFormItem" label=" " style="width: 100%">
          <el-radio-group v-model="orderData.shipType">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.OM_SHIP_TYPE)"
                      :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!--bonded-->
        <el-form-item class="commonFormItem" label=" " style="width: 100%">
          <el-radio-group v-model="orderData.bonded" disabled>
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.OM_BONDED)"
                      :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!--delivery type-->
        <el-form-item
          label="AIR/SEA"
          class="commonFormItem"
          prop="deliveryType"
          style="width: 33.3%"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.deliveryType">
            <el-radio-group v-model="orderData.deliveryType">
              <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.OM_DELIVERY_TYPE)"
                        :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
            </el-radio-group>
          </show-or-edit>
        </el-form-item>
        <!--deliveryTypeDescription-->
        <el-form-item
          :label="orderData.deliveryType==='express'? $t('Express') : ''"
          class="commonFormItem"
          prop="deliveryType"
          style="width: 66.6%"
        >
          <show-or-edit :disabled="!editMode"  :value="orderData.deliveryTypeDescription">
            <el-radio-group v-if="orderData.deliveryType==='express'" v-model="orderData.deliveryTypeDescription" style="padding-left: 10px">
              <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.OM_EXPRESS_TYPE)"
                        :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
            </el-radio-group>
          </show-or-edit>
        </el-form-item>
        <!--        customer-->
        <el-form-item class="commonFormItem" :label="$t('Customer')">
          {{orderData.customer}}
        </el-form-item>
        <!--        ship to-->
        <el-form-item class="commonFormItem" :label="$t('Ship To')">
          <dict-tag  :value="orderData.shipTo" :type="DICT_TYPE.OM_SHIP_TO" />
        </el-form-item>
        <!--        fwd-->
        <el-form-item
          :label="$t('FWD')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.forwarder">
            <el-input v-model="orderData.forwarder" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>

        <!--        awb-->
        <el-form-item
          :label="$t('AWB#')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.trackingNumber">
            <el-input v-model="orderData.trackingNumber" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>

        <!--        contact-->
        <el-form-item
          :label="$t('Contact')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.contact">
            <el-input v-model="orderData.contact" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
<!--        ship date-->
        <el-form-item
          :label="$t('Ship Date')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.shipDate" type="Date">
            <el-date-picker
              v-model="orderData.shipDate"
              class="searchValue"
              :disabled="!editMode"
              :placeholder="$t('order.selectDate')"
              type="date"
              value-format="timestamp"
            />
          </show-or-edit>
        </el-form-item>
<!--        del.term-->
        <el-form-item
            :label="$t('Del. Term')"
            class="commonFormItem"
            prop="delTerm"
        >
          <show-or-edit :disabled="!editMode" :dict="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION" :value="orderData.delTerm" :type="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION">
            <el-select v-model="orderData.delTerm" class="searchValue" :disabled="!editMode" clearable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </show-or-edit>
        </el-form-item>
<!--        from-->
        <el-form-item
            :label="$t('From')"
            class="commonFormItem"
            prop="fromCity"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.fromCity">
            <el-input v-model="orderData.fromCity" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
<!--        country of origin-->
        <el-form-item
            :label="$t('Country of origin')"
            class="commonFormItem"
            prop="country"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.country">
            <el-input v-model="orderData.country" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
<!--        upload-->
        <el-form-item
          :label="$t('auth.uploadAttachments')"
          class="commonFormItem"
        >
          <div style="display: flex;justify-content: space-between">

            <el-upload
              class="upload-demo"
              :disabled="!editMode"
              :action="uploadUrl"
              :headers="getBaseHeader()"
              :on-remove="onRemove"
              :on-preview="onPreview"
              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
              multiple
              :limit="5"
              :show-file-list="false"
              :file-list="fileList"
            >
              <el-button
                v-if="editMode"
                v-hasPermi="['om:pl-master:update']"
                :disabled="!editMode"
                class="uploadBtn"
                size="small"
                plain
                icon="el-icon-plus"
                type="primary"
              />
            </el-upload>
            <div>
              {{ $t('scar.viewAttachments') }}
              <el-button
                class="uploadBtn"
                size="small"
                style="padding: 5px 9px"
                :disabled="fileList.length===0"
                plain
                :type="fileList.length?'primary':''"
                @click="showFile=true"
              >
                {{ fileList.length }}
              </el-button>

              <el-dialog
                v-if="showFile"
                :visible.sync="showFile"
                :title="$t('scar.viewAttachments')"
                width="400px"
              >
                <el-upload
                  class="upload-demo"
                  :disabled="!editMode"
                  :action="uploadUrl"
                  :headers="getBaseHeader()"
                  :on-remove="onRemove"
                  :on-preview="onPreview"
                  :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                  multiple
                  :limit="5"
                  :show-file-list="true"
                  :file-list="fileList"
                />
                <div slot="footer">
                  <el-button type="primary" @click="showFile=false">{{ $t('order.close') }}</el-button>
                </div>

              </el-dialog>
            </div>
          </div>

        </el-form-item>
<!--        remarks-->
        <el-form-item
          :label="$t('Remarks')"
          class="commonFormItem"
          style="width: 100%"
          prop="remarks"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.remarks">
            <el-input v-model="orderData.remarks" type="textarea" :rows="3" autosize :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
<!--        status-->
        <el-form-item
          :label="$t('Status')"
          class="commonFormItem"
          style="width: 100%"
          prop="Status"
        >
          <dict-tag :value="orderData.status" :type="DICT_TYPE.OM_PL_STATUS" />
        </el-form-item>
        <div
          v-if="editMode"
          style="width: 100%;display:flex;justify-content: center"
        >
          <el-button v-hasPermi="['om:pl-master:update']" icon="el-icon-edit-outline" type="primary" @click="submitForm(true)">{{ $t('Update') }}</el-button>
        </div>

      </el-form>

    </common-card>
    <common-card
      :title="$t('Packing list')"
    >
      <div style="margin-top: 25px;">
        <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
          <el-input
            v-model="queryParams.search"
            style="flex: 0 1 35%"
            :placeholder="$t('SO NO#/Customer/Buyer/Part No.')"
            @keyup.enter.native="getListInit"
          />
          <el-button plain type="primary" @click="getListInit">
            {{ $t('common.search') }}
          </el-button>
          <el-button
            style="margin-left: 0"
            @click="handleReset"
          >
            {{ $t('common.reset') }}
          </el-button>
          <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
            <el-button type="text">
              {{ $t('common.advancedSearch') }}
            </el-button>
            <i
              :style="showSearch? '':{transform: 'rotate(180deg)'}"
              class="el-icon-arrow-up"
              style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
            />
          </div>
        </div>

        <el-form v-show="showSearch" inline label-width="120px">
          <el-form-item class="searchItem" :label="$t('SO No.')" prop="soNo">
            <el-autocomplete
              v-model="queryParams.soNo"
              :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'so')})"
              :placeholder="$t('SO NO#')"
              :popper-append-to-body="false"
              popper-class="el-autocomplete-suggestion"
              class="searchItem"
              @select="((item) => {handleSelect(item, 'so')})"
              @keyup.enter.native="getListInit"
            />
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('Part No.')" prop="partNo">
            <el-input v-model="queryParams.partNo" :placeholder="$t('Part No.、Description')" @keyup.enter.native="getListInit" />

          </el-form-item>

          <el-form-item class="searchItem" :label="$t('Supplier')" prop="">
            <el-input v-model="queryParams.supplier" :placeholder="$t('Please enter Supplier')" @keyup.enter.native="getListInit" />

          </el-form-item>
          <el-form-item class="searchItem" :label="$t('Description')" prop="">
            <el-input v-model="queryParams.description" :placeholder="$t('Please enter content')" @keyup.enter.native="getListInit" />

          </el-form-item>

        </el-form>
        <vxe-grid
          ref="plInfo"
          :data="list"
          :loading="loading"
          :span-method="mergeRowMethod"
          v-bind="plInfoGrid"
        >

          <template #quantity="{row}">
            <el-input v-model="row.quantity"  />
          </template>
          <template #carton="{row}">
            <vxe-input type="integer" v-model="row.carton" @change="handleCellChange" />
          </template>
          <template #boxSize="{row}">
            <el-input type="textarea" rows="1" v-model="row.boxSize"  />
          </template>
          <template #netWeight="{row}">
            <vxe-input type="number" v-model="row.netWeight" @change="handleCellChange" />
          </template>
          <template #grossWeight="{row}">
            <vxe-input type="number" v-model="row.grossWeight" @change="handleCellChange" />
          </template>
          <template #volume="{row}">
            <vxe-input type="number" v-model="row.volume" @change="handleCellChange" />
          </template>
          <template #invoiceDate="{row}">
            <el-date-picker
              v-model="row.invoiceDate"
              class="searchValue"
              :disabled="!editMode"
              :placeholder="$t('order.selectDate')"
              type="date"

              value-format="yyyy-MM-dd"
            />
          </template>
          <template #invoiceNumber="{row}">
            <el-input v-model="row.invoiceNumber"  />
          </template>
          <template #invoiceAmount="{row}">
            <vxe-input type="number" v-model="row.invoiceAmount"  />
          </template>
          <template #bankCharge="{row}">
            <vxe-input type="number" v-model="row.bankCharge"  />
          </template>
          <template #awb="{row}">
            <el-input v-model="row.awb"  />
          </template>
          <template #operate="{row}">
            <OperateDropDown
              v-if="editMode"
              :menu-item="[
                {
                  name: $t('common.edit'),
                  show: editMode&&$store.getters.permissions.includes('om:so-detail:update'),
                  action: (row) => showSoDetail(row),
                  para: row
                },
                // {
                //   name: $t('删除'),
                //   show: true,
                //   action: (row) => delRow(row),
                //   para: row
                // },
                {
                  name: $t('om.ChangeLog'),
                  show: $store.getters.permissions.includes('om:so-master:detail'),
                  action: (row) => showLogs(row.soDetailId,'om_so_detail',['partNo','description','mfg','mpn','version','orderQuantity','uom','pricePer','reqDelDate','conDelDate','remarks','status','unitPricePer'],['partNo']),
                  para: row
                },
              ]"
            />
          </template>
          <template #toolbar_buttons>
            <el-row :gutter="24" style="width: 100%" class="mb8">
              <el-col :span="22" style="display: flex">
                <el-button type="primary" v-if="editMode"  @click="showMaterial">New Parts</el-button>
                <el-button type="primary" v-if="editMode"  @click="showAttachMaterial">Attach Parts</el-button>
                <el-button type="primary" v-if="editMode" @click="delRow">Delete Parts</el-button>
                <el-button type="primary" @click="exportExcel">Download</el-button>
              </el-col>
              <el-col :span="2">
                <right-toolbar
                    :list-id="plInfoGrid.id"
                    :show-search.sync="showSearch"
                    :custom-columns.sync="plInfoGrid.columns"
                    @queryTable="getListInit"
                />
              </el-col>
            </el-row>
          </template>
        </vxe-grid>
        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNo"
          :total="total"
          @pagination="getList()"
        />
      </div>
      <div class="fixedBottom">
        <el-button type="primary" v-if="orderData.type==='dp' && editMode" @click="seekSupport" v-has-permi="['om:pl-detail:support']">Seek Support</el-button>
        <el-button type="primary" v-if="editMode" @click="savePack">Save</el-button>
      </div>
    </common-card>
    <soDetail
      ref="soDetailDialog"
      :so-detail-visible.sync="soDetailVisible"
      @getTable="getList"
    />
    <!--操作记录-->
    <el-dialog
        v-if="log.open"
        :close-on-click-modal="false"
        :title="$t('common.operationRecord')"
        :visible.sync="log.open"
        width="1000px"
    >
      <operation-record
          :business-id="log.businessId"
          :columns="log.columns"
          :log-visible.sync="log.open"
      />
    </el-dialog>
    <el-dialog
    title="新增物料"
    v-if="newVisible"
    width="1200px"
    :visible.sync="newVisible"
    >
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="materialQuery.search"
          style="flex: 0 1 35%"
          :placeholder="$t('SO NO#/Customer/Buyer/Part No.')"
          @keyup.enter.native="getMaterial"
        />
        <el-button plain type="primary" @click="getMaterial">
          {{ $t('common.search') }}
        </el-button>
        <el-button
          style="margin-left: 0"
          @click="handleMaterialReset"
        >
          {{ $t('common.reset') }}
        </el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearchMater= !showSearchMater">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearchMater? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>
      </div>

      <el-form v-show="showSearchMater" inline label-width="120px">

        <el-form-item class="searchItem" :label="$t('SO NO#')" prop="no">
          <el-autocomplete
            v-model="materialQuery.no"
            :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'so')})"
            :placeholder="$t('SO NO#')"
            :popper-append-to-body="false"
            popper-class="el-autocomplete-suggestion"
            class="searchItem"
            @select="((item) => {handleSelect(item, 'so')})"
            @keyup.enter.native="getListInit"
          />
        </el-form-item>

        <el-form-item class="searchItem" :label="$t('Part No.')" prop="">
          <el-input v-model="materialQuery.partNo" :placeholder="$t('Part No.、Description')" @keyup.enter.native="getListInit" />
        </el-form-item>

        <el-form-item class="searchItem" :label="$t('MFG')" prop="">
          <el-autocomplete
            v-model="materialQuery.mfg"
            :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'mfg')})"
            :placeholder="$t('MFG')"
            :popper-append-to-body="false"
            popper-class="el-autocomplete-suggestion"
            class="overList"
            @select="((item) => {handleSelect(item, 'mfg')})"
            @keyup.enter.native="getListInit"
          />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('MPN')" prop="">
          <el-input v-model="materialQuery.mpn" :placeholder="$t('MPN')" @keyup.enter.native="getListInit" />
        </el-form-item>

        <el-form-item class="searchItem" :label="$t('Supplier')" prop="">
          <el-autocomplete
            v-model="materialQuery.supplier"
            :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'supplier')})"
            :placeholder="$t('Supplier')"
            :popper-append-to-body="false"
            popper-class="el-autocomplete-suggestion"
            class="overList"
            @select="((item) => {handleSelect(item, 'supplier')})"
            @keyup.enter.native="getListInit"
          />
        </el-form-item>

        <el-form-item class="searchItem" :label="$t('Buyer')" prop="">
          <el-select v-model="materialQuery.buyer" class="searchValue" clearable filterable>
            <el-option v-for="item in buyers" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>

           <el-form-item class="searchItem" :label="$t('order.timeType')" prop="dateType">
          <el-select v-model="materialQuery.dateType" class="searchValue" clearable>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SEARCH_TIME_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label=" " class="searchItem">
          <el-date-picker
            v-model="materialQuery.time"
            class="searchValue"
            value-format="yyyy-MM-dd"
            type="daterange"
            :range-separator="$t('order.to')"
            :start-placeholder="$t('common.startDate')"
            :end-placeholder="$t('common.endDate')"
          />
        </el-form-item>
        </el-form>
      <el-table ref="materialTable" :data="materialTable">
        <el-table-column type="selection"></el-table-column>
        <el-table-column
          prop="soNo"
          label="SO NO#"
        />
        <el-table-column
          prop="partNo"
          label="Part No."
        />
        <el-table-column
          prop="description"
          label="Description"
        />
        <el-table-column
          prop="version"
          label="Version"
        />
        <el-table-column
          prop="supplier"
          label="Supplier"
        />
        <el-table-column
          prop="unitPrice"
          label="Unit Price"
        />
        <el-table-column
          prop="amount"
          label="Amount"
        />
        <el-table-column
          prop="quantity"
          label="Quantity"
        />
        <el-table-column
          prop="shipTo"
          label="Ship To"
        >
          <template slot-scope="scope">
            <dict-tag :type="DICT_TYPE.OM_SHIP_TO" :value="scope.row.shipTo" />
        </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="materialTotal > 0"
        :limit.sync="materialQuery.pageSize"
        :page.sync="materialQuery.pageNo"
        :total="materialTotal"
        @pagination="getMaterial"
      />
      <div slot="footer">
        <el-button  @click="newVisible = false">取消</el-button>
        <el-button type="primary" @click="saveMaterial">保存</el-button>

      </div>
    </el-dialog>
<!--    新增附加物料-->
    <el-dialog
      title="新增附加物料"
      v-if="attachMaterialVisible"
      width="400px"
      :visible.sync="attachMaterialVisible"
    >
      <el-form
        :model="attachMaterial"
          :rules="{
          partNo: [{ required: true, message: $t('Part No. required'), trigger: 'blur' }],
          quantity: [{ required: true, message: $t('Quantity required'), trigger: 'blur' }],
          supplier: [{ required: true, message: $t('Supplier required'), trigger: 'blur' }]
        }"
               class="baseInfo"
               inline
               label-width="120px"
      >
        <!-- Part No. -->
        <el-form-item ref="partNo" :label="$t('Part No.')" prop="partNo">
            <el-input v-model="attachMaterial.partNo" :placeholder="$t('common.pleaseEnter')" />
        </el-form-item>

        <!-- Description -->
        <el-form-item ref="description" :label="$t('Description')">
            <el-input v-model="attachMaterial.description" :placeholder="$t('common.pleaseEnter')" />
        </el-form-item>

        <!-- Version -->
        <el-form-item ref="version" :label="$t('Version')">
            <el-input v-model="attachMaterial.version" :placeholder="$t('common.pleaseEnter')" />
        </el-form-item>

        <!-- quantity -->
        <el-form-item ref="quantity" :label="$t('Quantity')" prop="quantity">
          <vxe-input type="number" min="0" v-model="attachMaterial.quantity" :placeholder="$t('common.pleaseEnter')" />
        </el-form-item>

        <!-- supplier -->
        <el-form-item ref="supplierId" :label="$t('Supplier')" prop="supplier">
          <el-autocomplete
            v-model="attachMaterial.supplier"
            :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'supplier')})"
            :placeholder="$t('Supplier')"
            :popper-append-to-body="false"
            popper-class="el-autocomplete-suggestion"
            class="overList"
            @select="((item) => {handleSelect(item, 'supplierSelect')})"
            @keyup.enter.native="getListInit"
          />
        </el-form-item>

      </el-form>
      <div slot="footer">
        <el-button  @click="attachMaterialVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAttachMaterial">保存</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getBaseHeader } from '@/utils/request'
import operationRecord from '@/components/OperationRecord/operationRecord.vue'
import {exportPlDetailExcel, getPlMaster, newAttachParts, savePLMaster, seekSupport} from '@/api/om/pl/pl'
import { pagePlDetail } from '../../../api/om/so'
import { deleteParts, newParts, retrieveOrderTracker, saveParts, sendToHub } from '../../../api/om/pl/pl'
import {exportInvoiceDetailExcel} from "@/api/om/pl/invoice";
import {getSearchResult} from "@/api/om/orderTracker";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: 'Plinfo/:id',
  components: {
    operationRecord, OperateDropDown, BatchUpload, ShowOrEdit,
    soDetail: () => import('@/views/om/soDetail')
  },
  data() {
    return {
      getBaseHeader,
      soDetailVisible: false,
      remark: '',
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      showFile: false,
      fileList: [],
      buyers: [],
      orderData: {
        plNo: '',
        invoiceNo: '',
        totalAmount: null,
        customer: '',
        shipTo: '',
        shipType: '',
        bonded: '',
        deliveryType: '',
        deliveryTypeDescription: '',
        planShipDate: '',
        shipDate: '',
        trackingNumber: '',
        forwarder: '',
        contact: '',
        delTerm: null,
        fromCity: null,
        country: null,
        status: null,
        remarks: ''
      },
      orderRules: {
        // Define your validation rules here
      },
      editMode: true,
      queryParams: {
        search: "",
        soNo: "",
        partNo: "",
        supplier: "",
        description: "",
        pageNo: 1,
        pageSize: 10,
      },
      total: 0,
      list: [],
      showSearch: false,
      showSearchMater: false,
      loading: false,
      plInfoGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'plInfo',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          {type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left'},
          {title: this.$t('SO NO#'), field: 'soNo', visible: true, width: 100},
          {title: this.$t('Part No.'), field: 'partNo', visible: true, width: 100},
          {title: this.$t('Description'), field: 'description', visible: true, width: 100},
          {title: this.$t('Version'), field: 'version', visible: true, width: 100},
          {title: this.$t('Quantity'), field: 'quantity', slots: {default: 'quantity'}, visible: true, width: 100},
          {title: this.$t('Carton'), field: 'carton', slots: {default: 'carton'}, visible: true, width: 100},
          {
            title: this.$t('木箱外径尺寸/单箱'),
            field: 'boxSize',
            slots: {default: 'boxSize'},
            visible: true,
            width: 180
          },
          {
            title: this.$t('NetWeight(kg)'),
            field: 'netWeight',
            slots: {default: 'netWeight'},
            visible: true,
            width: 130
          },
          {
            title: this.$t('GrossWeight(kg)'),
            field: 'grossWeight',
            slots: {default: 'grossWeight'},
            visible: true,
            width: 130
          },
          {title: this.$t('Volume m³'), field: 'volume', slots: {default: 'volume'}, visible: true, width: 100},
          {title: this.$t('Supplier'), field: 'supplier', visible: true, width: 100},
          {title: this.$t('Address'), field: 'address',visible: true,  width: 180},
          {
            title: this.$t('Invoice Date'),
            field: 'invoiceDate',
            visible: true,
            slots: {default: 'invoiceDate'},
            width: 130
          },
          {
            title: this.$t('Invoice #'),
            field: 'invoiceNumber',
            visible: true,
            slots: {default: 'invoiceNumber'},
            width: 100
          },
          {
            title: this.$t('Invoice Amount'),
            field: 'invoiceAmount',
            visible: true,
            slots: {default: 'invoiceAmount'},
            width: 100
          },
          {
            title: this.$t('Bank Charge'),
            field: 'bankCharge',
            visible: true,
            slots: {default: 'bankCharge'},
            width: 100
          },
          {title: this.$t('AWB#'), field: 'awb', visible: true, slots: {default: 'awb'}, width: 100}
        ],


        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      exportLoading: false,
      obligatoryDialog: false,
      confirmLoading: false,
      avplLoading: false,
      // 操作日志
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: []
      },
      newVisible: false,
      materialQuery: {
        time: [],
        search: '',
        no: '',
        partNo: '',
        mfg: '',
        mpn: '',
        supplier: '',
        buyer: '',
        timeType: '',
        startDate: '',
        endDate: '',
        pageNo: 1,
        pageSize: 10,
      },
      materialTotal: 0,
      materialTable: [],
      attachMaterialVisible:false,
      attachMaterial:{
        plId:null,
        partNo:null,
        description:null,
        version:null,
        supplier:null,
        quantity:null,
        type:null,
      }
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  mounted() {
    this.editMode = this.$route.query.viewOnly !== 'true'
    this.init()
    this.getList()
  },
  // INCAP-541 菜单配置了缓存，但是还是页面会刷新
  // activated() {
  //   this.editMode = this.$route.query.viewOnly !== 'true'
  //   this.init()
  //   this.getList()
  // },
  methods: {
    seekSupport() {
      seekSupport({plId:this.$route.params.id}).then(res=>{
        this.$message.success('Operation Successful')
      })
    },
    getDictDatas,
    showSoDetail(row) {
      this.soDetailVisible = true
      this.$refs.soDetailDialog.init(row.soDetailId)
    },
    init() {
      getPlMaster(this.$route.params.id).then(res => {
        res.data.files.forEach(a => {
          a.name = a.fileName
        })
        this.fileList = res.data?.files || []
        this.orderData = res.data
        //结束的订单不允许编辑
        if (this.orderData.status === 'closed' || this.orderData.status === 'cancel') {
          //this.editMode=false
        }
      })
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      pagePlDetail({
        ...this.queryParams,
        plId: this.$route.params.id
      }).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    submitForm(showPrompt) {
      savePLMaster({
        ...this.orderData,
        fileIds: this.fileList.map(a => a.fileId)
      }).then(res => {
        if (showPrompt) {
          this.$message.success('Operation Successful')
        }
      })
    },
    onRemove(file, fileList) {
      const index = this.fileList.indexOf(file.response?.data?.fileId) || this.fileList.indexOf(file.fileId)
      this.fileList.splice(index, 1)
    },
    onSuccess(response, file, fileList) {
      this.fileList.push({
        name: response.data.name,
        fileUrl: response.data.url,
        fileId: response.data.id
      })
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.fileUrl) {
        window.open(file.fileUrl)
      }
    },
    handleReset() {
      this.queryParams = {
        search: "",
        soNo: "",
        partNo: "",
        supplier: "",
        description: "",
        pageNo: 1,
        pageSize: 10,
      }
      this.getList()
    },
    mergeRowMethod({row, _rowIndex, column, visibleData}) {
      const fields = ['carton', 'boxSize', 'netWeight', 'grossWeight', 'volume', 'supplier', 'address', 'invoiceDate', 'invoiceNumber', 'invoiceAmount', 'bankCharge', 'awb']
      const cellValue = row[column.property]
      if ((column.property === 'checkbox' || cellValue !== undefined) && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow.supplierId === row.supplierId) {
          return {rowspan: 0, colspan: 0}
        } else {
          let countRowspan = 1
          while (nextRow && nextRow.supplierId === row.supplierId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return {rowspan: countRowspan, colspan: 1}
          }
        }
      }
    },


    exportExcel() {
      this.$modal.confirm(this.$t('Do you confirm the download?')).then(() => {
        this.exportLoading = true
        return exportPlDetailExcel({...this.queryParams, plId: this.$route.params.id})
      }).then(response => {
        this.$download.excel(response, this.$t('packing list parts.xlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },

    // show operate log
    showLogs(businessId, table, column, keyColumns) {
      this.log.open = true
      this.log.businessId = businessId
      this.log.columns = [{
        tableName: table,
        searchColumns: column,
        keyColumns: keyColumns
      }]
    },
    delRow() {
      const rec = this.$refs.plInfo.getCheckboxRecords()
      if (rec.length === 0) {
        this.$message.warning('Please select the data to be deleted')
        return
      }
      this.$modal.confirm('Are you sure you want to delete the selected data?').then(() => {
        deleteParts({
          plId: this.$route.params.id,
          shipDetailIds: rec.map(a => a.plDetailId),
          plDetailIds: rec.map(a => a.plDetailId)
        }).then(() => {
          this.$message.success('Operation Successful')
          this.getList()
        })
      })

    },
    savePack() {
      this.submitForm(false)
      saveParts(this.list).then(res => {
        this.$message.success('Operation Successful')
        this.getList()
      })
    },
    showMaterial() {
      this.handleMaterialReset()
      this.newVisible = true
    },
    showAttachMaterial(){
      this.handleAttachMaterialReset()
      this.attachMaterialVisible = true
    },
    querySearchAsync(queryString, cb, type) {
      if (!queryString || queryString.length < 2) {
        return
      }
      getSearchResult({
        searchValue: queryString,
        type: type
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item
          }
        }))
      })
    },
    handleSelect(item, type) {
      switch (type) {
        case 'so':
          this.queryParams.soNo = item.value
          break
        case 'customer':
          this.queryParams.customer = item.value
          break
        case 'buyer':
          this.queryParams.buyer = item.value
          break
        case 'supplier':
          this.queryParams.supplier = item.value
          break
        case 'mfg':
          this.queryParams.mfg = item.value
          break
        case 'supplierSelect':
          this.attachMaterial.supplier = item.value
          break
        // eslint-disable-next-line no-fallthrough
        default:
          break
      }
    },
    handleMaterialReset() {
      this.materialQuery = {
        time: [],
        pageNo: 1,
        pageSize: 10,
        search: '',
        no: '',
        partNo: '',
        mfg: '',
        mpn: '',
        supplier: '',
        buyer: '',
        timeType: '',
        startDate: '',
        endDate: '',
      }
      this.getMaterial()
    },
    handleAttachMaterialReset() {
      this.attachMaterial = {
        plId:null,
        partNo:null,
        description:null,
        version:null,
        supplierId:null,
        quantity:null,
        type:null,
      }
    },
    getMaterial() {
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      retrieveOrderTracker({
        customer: this.orderData.customer,
        shipTo: this.orderData.shipTo,
        ...this.materialQuery
      }).then(res => {
        this.materialTable = res.data.list
        this.materialTotal = res.data.total
      })
    },
    saveMaterial() {
      const rec = this.$refs.materialTable.selection
      if (rec.length === 0) {
        this.$message.warning('Please select the data')
        return
      }

      newParts({
        plId: this.$route.params.id,
        shipDetailIds: rec.map(a => a.shipDetailId),
        // plDetailIds: rec.map(a => a.plDetailId)
      }).then(res => {
        this.$message.success('Operation Successful')
        this.newVisible = false
        this.getList()

      })
    },
    saveAttachMaterial() {
      newAttachParts({...this.attachMaterial, plId: this.$route.params.id,}).then(res => {
        this.$message.success('Operation Successful')
        this.attachMaterialVisible = false
        this.getList()

      })
    },
    // process new line
    formatAddress(address) {
      console.log(address);
      if (address) {
        // 将 \r\n 或 \n 替换为 <br> 标签
        let newAddress = address.replace(/(\r\n|\n)/g, '<br>');
        console.log(newAddress)
        return newAddress
      }
      return '';
    },
    handleCellChange({row}) {
      this.calculateTotals(); // Recalculate totals when a cell is changed
    },
    calculateTotals() {
      // 计算每个字段的总和
      this.orderData.carton = this.calculateSum('carton');
      this.orderData.netWeight = this.calculateSum('netWeight');
      this.orderData.grossWeight = this.calculateSum('grossWeight');
      this.orderData.volume = this.calculateSum('volume');
    },

    calculateSum(field) {
      let supplierId = null
      return this.list.reduce((sum, row) => {
        // 如果供应商 ID 变化，更新 supplierId
        if (row.supplierId === supplierId) {
          return sum+0
        }
        supplierId = row.supplierId;
        // 累加当前字段的值
        return sum + (Number(row[field]) || 0);
      }, 0);
    }
  }
}
</script>

<style lang="scss" scoped>
.commonFormItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 180px);
  }
}
.button-container {
  margin-top: 20px; /* 设置按钮与表格的距离 */
  text-align: right; /* 使按钮靠右对齐 */
}
.overList{
  ::v-deep .el-autocomplete-suggestion{
    width: auto !important;
    min-width: 180px;
    max-width: 600px !important;
  }
}
.searchValue{
  width: 100%;
}
.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}
::v-deep .avpl-row{
  background:#81D3F8 !important;
}
::v-deep .avpl-edit{
  background:#FFE668 !important;
}


</style>
