<template>
  <div class="app-container">
    <common-card :title="$t('Shipping Plan General')">
      <el-form ref="orderForm" :model="DeliveryPlanData" inline label-width="180px">
        <!--DP-->
        <el-form-item class="commonFormItem" :label="$t('DP#')">
          {{DeliveryPlanData.plNo}}
        </el-form-item>
        <!--Total Amount-->
        <el-form-item class="commonFormItem" :label="$t('TotalAmount')">
          {{DeliveryPlanData.totalAmount}}$
        </el-form-item>
        <el-form-item class="commonFormItem" label=" ">

        </el-form-item>
        <!--ship type-->
        <el-form-item class="commonFormItem" label=" " style="width: 100%">
          <el-radio-group v-model="DeliveryPlanData.shipType">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.OM_SHIP_TYPE)"
                      :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!--cartons-->
        <el-form-item class="commonFormItem" :label="$t('Cartons')">
          <show-or-edit :disabled="!editMode" :value="DeliveryPlanData.carton">
            <vxe-input type="integer" style="width: 100%" v-model="DeliveryPlanData.carton" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <!--net-weight(kg)-->
        <el-form-item
          :label="$t('Net Weight（KG）')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="DeliveryPlanData.netWeight">
            <vxe-input type="number" style="width: 100%" v-model="DeliveryPlanData.netWeight" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <!--gross-weight(kg)-->
        <el-form-item
          :label="$t('Gross Weight(KG)')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="DeliveryPlanData.grossWeight" type="Date">
            <vxe-input type="number" style="width: 100%" v-model="DeliveryPlanData.grossWeight" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <!--volume(m3)-->
        <el-form-item
          :label="$t('Volume(m3)')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="DeliveryPlanData.volume" type="Date">
            <vxe-input type="number" style="width: 100%" v-model="DeliveryPlanData.volume" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <!--尺寸-->
        <el-form-item
          :label="$t('尺寸')"
          class="commonFormItem"
          style="width: 100%"
        >
          <show-or-edit :disabled="!editMode" :value="DeliveryPlanData.boxSize">
            <el-input type="textarea" :rows="3" v-model="DeliveryPlanData.boxSize" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <!--invoice date-->
        <el-form-item
          :label="$t('Invoice  Date')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="DeliveryPlanData.invoiceDate">
            <el-date-picker
              v-model="DeliveryPlanData.invoiceDate"
              class="searchValue"
              :disabled="!editMode"
              :placeholder="$t('order.selectDate')"
              type="date"
              value-format="timestamp"
            />
          </show-or-edit>
        </el-form-item>
        <!--invoice #-->
        <el-form-item
          :label="$t('Invoice #')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="DeliveryPlanData.invoiceNumber">
            <el-input v-model="DeliveryPlanData.invoiceNumber" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <!--Invoice Amount-->
        <el-form-item
          :label="$t('Invoice Amount')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="DeliveryPlanData.invoiceAmount">
            <vxe-input type="number" style="width: 100%" v-model="DeliveryPlanData.invoiceAmount" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <!--Bank Charge-->
        <el-form-item
          :label="$t('Bank Charge')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="DeliveryPlanData.bankCharge">
            <vxe-input type="number" style="width: 100%" v-model="DeliveryPlanData.bankCharge" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <!--AWB#-->
        <el-form-item
          :label="$t('AWB#')"
          class="commonFormItem"
          style="width: 66.66%"
        >
          <show-or-edit :disabled="!editMode" :value="DeliveryPlanData.awb">
            <el-input v-model="DeliveryPlanData.awb" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <!--upload attach-->
        <el-form-item
          :label="$t('auth.uploadAttachments')"
          class="commonFormItem"

        >
          <div style="display: flex;justify-content: space-between">
            <el-upload
              class="upload-demo"
              :disabled="!editMode"
              :action="uploadUrl"
              :headers="getBaseHeader()"
              :on-remove="onRemove"
              :on-preview="onPreview"
              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
              multiple
              :limit="5"
              :show-file-list="false"
              :file-list="fileList"
            >
              <el-button
                v-if="editMode"
                v-hasPermi="['om:pl-master:update']"
                :disabled="!editMode"
                class="uploadBtn"
                size="small"
                plain
                icon="el-icon-plus"
                type="primary"
              />
            </el-upload>
            <div>
              {{ $t('scar.viewAttachments') }}
              <el-button
                class="uploadBtn"
                size="small"
                style="padding: 5px 9px"
                :disabled="fileList.length===0"
                plain
                :type="fileList.length?'primary':''"
                @click="showFile=true"
              >
                {{ fileList.length }}
              </el-button>

              <el-dialog
                v-if="showFile"
                :visible.sync="showFile"
                :title="$t('scar.viewAttachments')"
                width="400px"
              >
                <el-upload
                  class="upload-demo"
                  :disabled="!editMode"
                  :action="uploadUrl"
                  :headers="getBaseHeader()"
                  :on-remove="onRemove"
                  :on-preview="onPreview"
                  :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                  multiple
                  :limit="5"
                  :show-file-list="true"
                  :file-list="fileList"
                />
                <div slot="footer">
                  <el-button type="primary" @click="showFile=false">{{ $t('order.close') }}</el-button>
                </div>

              </el-dialog>
            </div>
          </div>

        </el-form-item>
        <el-form-item
          :label="$t('Remarks')"
          class="commonFormItem"
          style="width: 100%"
          prop="remarks"
        >
          <show-or-edit :disabled="!editMode" :value="DeliveryPlanData.remarks">
            <el-input v-model="DeliveryPlanData.remarks" type="textarea" :rows="3" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <div
          v-if="editMode"
          style="width: 100%;display:flex;justify-content: center"
        >
          <el-button v-hasPermi="['om:shipping-plan-delivery-plan:update']" icon="el-icon-edit-outline" type="primary" @click="submitForm(true)">{{ $t('Update') }}</el-button>
        </div>
      </el-form>
    </common-card>
    <common-card
      :title="$t('Shipping Plan Parts')"
    >
      <div style="margin-top: 25px;">
        <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
          <!--search-->
          <el-input
            v-model="queryParams.search"
            style="flex: 0 1 35%"
            :placeholder="$t('PO NO#,Part No.')"
            @keyup.enter.native="getListInit"
          />
          <!--search-->
          <el-button plain type="primary" @click="getListInit">
            {{ $t('common.search') }}
          </el-button>
          <!--reset-->
          <el-button
            style="margin-left: 0"
            @click="handleReset"
          >
            {{ $t('common.reset') }}
          </el-button>
          <!--advance search-->
          <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
            <el-button type="text">
              {{ $t('common.advancedSearch') }}
            </el-button>
            <i
              :style="showSearch? '':{transform: 'rotate(180deg)'}"
              class="el-icon-arrow-up"
              style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
            />
          </div>
        </div>
        <el-form v-show="showSearch" inline label-width="120px">
          <!--po no-->
          <el-form-item class="searchItem" :label="$t('PO NO#')">
            <el-input v-model="queryParams.poNo" :placeholder="$t('PO NO#')" @keyup.enter.native="getListInit" />
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('Part No.')">
            <el-input v-model="queryParams.partNo" :placeholder="$t('Part No.、Description')" @keyup.enter.native="getListInit" />
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('Req.Del.Date')" prop="">
            <el-date-picker
              v-model="queryParams.reqDeliveryDates"
              class="searchValue"
              value-format="yyyy-MM-dd"
              type="daterange"
              :range-separator="$t('order.to')"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
            />
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('Con.Del.Date')" prop="">
            <el-date-picker
              v-model="queryParams.conDeliveryDates"
              class="searchValue"
              value-format="yyyy-MM-dd"
              type="daterange"
              :range-separator="$t('order.to')"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
            />
          </el-form-item>
        </el-form>
        <vxe-grid
          ref="deliveryPlanInfo"
          :data="list"
          :loading="loading"
          :span-method="mergeRowMethod"
          v-bind="deliveryPlanInfoGrid"
        >

          <template #quantity="{row}">
            <vxe-input type="number" style="width: 100%" v-model="row.quantity"  />
          </template>
          <template #carton="{row}">
            <el-input v-model="row.carton"  />
          </template>
          <template #dict="{column,row}">
            <dict-tag :type="column.params.dict" :value="row[column.field]" />
          </template>
          <template #decimal="{column,row}">
            {{ row[column.field]?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
          </template>
          <template #operate="{row}">
            <OperateDropDown
              v-if="editMode"
              :menu-item="[
                {
                  name: $t('common.edit'),
                  show: editMode&&$store.getters.permissions.includes('om:so-detail:update'),
                  action: (row) => showSoDetail(row),
                  para: row
                },
                // {
                //   name: $t('删除'),
                //   show: true,
                //   action: (row) => delRow(row),
                //   para: row
                // },
                {
                  name: $t('om.ChangeLog'),
                  show: $store.getters.permissions.includes('om:so-master:detail'),
                  action: (row) => showLogs(row.soDetailId,'om_so_detail',['partNo','description','mfg','mpn','version','orderQuantity','uom','pricePer','reqDelDate','conDelDate','remarks','status','unitPricePer'],['partNo']),
                  para: row
                },
              ]"
            />
          </template>
          <template #toolbar_buttons>
            <el-row :gutter="24" style="width: 100%" class="mb8">
              <el-col :span="22" style="display: flex">
                <el-button type="primary" v-if="editMode" v-has-permi="['om:pl-detail:add']"  @click="showMaterial">New Parts</el-button>
                <el-button type="primary" v-if="editMode" v-has-permi="['om:pl-detail:delete']" @click="delRow">Delete Parts</el-button>
                <el-button type="primary" @click="exportExcel">Download</el-button>
              </el-col>
              <el-col :span="2">
                <right-toolbar
                  :list-id="deliveryPlanInfoGrid.id"
                  :show-search.sync="showSearch"
                  :custom-columns.sync="deliveryPlanInfoGrid.columns"
                  @queryTable="getListInit"
                />
              </el-col>
            </el-row>
          </template>
        </vxe-grid>
        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNo"
          :total="total"
          @pagination="getList()"
        />
      </div>
      <div class="fixedBottom">
        <el-button type="primary" v-if="editMode" @click="seekSupport" v-has-permi="['om:pl-detail:support']">Seek Support</el-button>
        <el-button type="primary" v-if="editMode" v-has-permi="['om:pl-detail:update']"  @click="savePart">Save</el-button>
      </div>
    </common-card>
    <soDetail
      ref="soDetailDialog"
      :so-detail-visible.sync="soDetailVisible"
      @getTable="getList"
    />
    <!--操作记录-->
    <el-dialog
      v-if="log.open"
      :close-on-click-modal="false"
      :title="$t('common.operationRecord')"
      :visible.sync="log.open"
      width="1000px"
    >
      <operation-record
        :business-id="log.businessId"
        :columns="log.columns"
        :log-visible.sync="log.open"
      />
    </el-dialog>
    <!--    new parts-->
    <el-dialog
      title="新增物料"
      v-if="newVisible"
      width="1200px"
      :visible.sync="newVisible"
    >
    <toBeDeliveryList ref="toBeDeliveryTableRef" :documentType="type" />
      <div slot="footer">
        <el-button  @click="newVisible = false">Cancel</el-button>
        <el-button type="primary" @click="saveMaterial">Confirm</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getBaseHeader } from '@/utils/request'
import operationRecord from '@/components/OperationRecord/operationRecord.vue'
import toBeDeliveryList from "@/views/om/pl/shippingPlan/toBeDeliveryList.vue";
import {getSearchResult} from "@/api/om/orderTracker";
import {parseTime} from "@/utils/ruoyi";
import {
  exportPlDeliveryPlanDetailExcel,
  getDeliveryPlanPartsPage,
  getPlDeliveryPlanEntity,
  saveDelivery,
  deleteParts
} from "@/api/om/pl/shippingPlan";

import {newParts, saveParts, seekSupport} from "@/api/om/pl/pl";
export default {
  name: 'Deliveryinfo/:id',
  components: {
    operationRecord, OperateDropDown, BatchUpload, ShowOrEdit,toBeDeliveryList,
    soDetail: () => import('@/views/om/soDetail') },
  data() {
    return {
      getBaseHeader,
      soDetailVisible: false,
      remark: '',
      uploadUrl:  process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      showFile: false,
      fileList: [],
      buyers: [],
      DeliveryPlanData: {
        plId:null,
        plSupplierId:null,
        plNo: '',
        totalAmount: null,
        shipTo: '',
        shipType:'',
        carton: null,
        netWeight: null,
        grossWeight: null,
        volume:null,
        boxSize:'',
        invoiceDate:null,
        invoiceNumber: '',
        invoiceAmount:null,
        bankCharge:null,
        awb:'',
        remarks: ''
      },
      orderRules: {
        // Define your validation rules here
      },
      editMode: true,
      queryParams: {
        search: "",
        poNo: "",
        partNo: "",
        reqDeliveryDates:[],
        startReqDeliveryDate:null,
        endReqDeliveryDate:null,
        conDeliveryDates:[],
        startConDeliveryDate:null,
        endConDeliveryDate:null,
        pageNo: 1,
        pageSize: 10,
      },
      total: 0,
      list: [],
      showSearch: false,
      showSearchMater: false,
      loading: false,
      deliveryPlanInfoGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'deliveryPlan',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
          { title: this.$t('PO NO#'), field: 'poNo', visible: true },
          { title: this.$t('Part No.'), field: 'partNo', visible: true},
          { title: 'Req.Del.Date', field: 'reqDelDate',  visible: true, formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''},
          { title: 'Con.Del.Date', field: 'conDelDate',  visible: true, formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''},
          { title: 'Pass Due', field: 'passDue', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_PASS_DUE }, visible: true},
          { title: 'Pur. Price', field: 'unitPrice',slots:{default:'decimal'}, visible: true, align: 'right' },
          { title: this.$t('Quantity'), field: 'quantity',slots:{default:'quantity'}, visible: true,align:'right' },
          { title: 'Amount', field: 'amount',slots:{default:'decimal'}, visible: true,align:'right' }
        ],


        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      exportLoading: false,
      obligatoryDialog: false,
      confirmLoading: false,
      avplLoading:false,
      // 操作日志
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: []
      },
      newVisible: false,
      materialQuery:{
        time:[],
        search: '',
        no: '',
        partNo: '',
        mfg: '',
        mpn: '',
        supplier: '',
        buyer: '',
        timeType: '',
        startDate: '',
        endDate: '',
        pageNo: 1,
        pageSize: 10,
      },
      materialTotal: 0,
      materialTable: [],
      type:'dp'
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  mounted() {
    this.editMode = this.$route.query.viewOnly !== 'true'
    this.init()
    this.getList()
  },
  activated() {
    this.editMode = this.$route.query.viewOnly !== 'true'
    this.init()
    this.getList()
  },
  methods: {
    seekSupport() {
      seekSupport({plId:this.$route.params.id}).then(res=>{
        this.$message.success('Operation Successful')
      })
    },
    getDictDatas,
    showSoDetail(row) {
      this.soDetailVisible = true
      this.$refs.soDetailDialog.init(row.soDetailId)
    },
    init() {
      getPlDeliveryPlanEntity(this.$route.params.id).then(res => {
        res.data.files.forEach(a => {
          a.name = a.fileName
        })
        this.fileList = res.data?.files || []
        this.DeliveryPlanData = res.data
        //结束的订单不允许编辑
        if(this.DeliveryPlanData.status==='closed'||this.DeliveryPlanData.status==='cancel'){
          this.editMode=false
        }
      })
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    getList() {
      this.loading = true
      if (this.queryParams.reqDeliveryDates?.length) {
        this.queryParams.startReqDeliveryDate = this.queryParams.reqDeliveryDates[0] ? this.queryParams.reqDeliveryDates[0] + 'T00:00:00' : undefined
        this.queryParams.endReqDeliveryDate = this.queryParams.reqDeliveryDates[1] ? this.queryParams.reqDeliveryDates[1] + 'T23:59:59' : undefined
      }
      if (this.queryParams.conDeliveryDates?.length) {
        this.queryParams.startConDeliveryDate = this.queryParams.startConDeliveryDate[0] ? this.queryParams.startConDeliveryDate[0] + 'T00:00:00' : undefined
        this.queryParams.endConDeliveryDate = this.queryParams.endConDeliveryDate[1] ? this.queryParams.endConDeliveryDate[1] + 'T23:59:59' : undefined
      }
      getDeliveryPlanPartsPage({ ...this.queryParams,
        plIds: [this.$route.params.id]
      }).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    submitForm(showPrompt) {
      console.log(this.DeliveryPlanData)
      saveDelivery({ ...this.DeliveryPlanData,
        fileIds: this.fileList.map(a => a.fileId)
      }).then(res => {
        if (showPrompt) {
          this.$message.success('Operation Successful')
        }
      })
    },
    onRemove(file, fileList) {
      const index = this.fileList.indexOf(file.response?.data?.fileId) || this.fileList.indexOf(file.fileId)
      this.fileList.splice(index, 1)
    },
    onSuccess(response, file, fileList) {
      this.fileList.push({
        name: response.data.name,
        fileUrl: response.data.url,
        fileId: response.data.id
      })
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.fileUrl) {
        window.open(file.fileUrl)
      }
    },
    handleReset() {
      this.queryParams = {
        search: "",
        poNO: "",
        partNo: "",
        reqDeliveryDates:[],
        startReqDeliveryDate:null,
        endReqDeliveryDate:null,
        conDeliveryDates:[],
        startConDeliveryDate:null,
        endConDeliveryDate:null,
        pageNo: 1,
        pageSize: 10,
      }
      this.getList()
    },
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const fields = ['carton', 'boxSize', 'netWeight', 'grossWeight', 'volume', 'supplier','address']
      const cellValue = row[column.property]
      if ((column.property==='checkbox' || cellValue !== undefined) && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow.supplierId === row.supplierId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow.supplierId === row.supplierId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    exportExcel() {
      this.$modal.confirm(this.$t('Do you confirm the download?')).then(() => {
        this.exportLoading = true
        return exportPlDeliveryPlanDetailExcel({ ...this.queryParams,plIds: [this.$route.params.id]})
      }).then(response => {
        this.$download.excel(response, this.$t('packing list parts.xlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    // show operate log
    showLogs(businessId, table, column,keyColumns) {
      this.log.open = true
      this.log.businessId = businessId
      this.log.columns = [{
        tableName: table,
        searchColumns: column,
        keyColumns:keyColumns
      }]
    },
    delRow() {
      const rec = this.$refs.deliveryPlanInfo.getCheckboxRecords()
      if (rec.length === 0) {
        this.$message.warning('Please select the data to be deleted')
        return
      }
      this.$modal.confirm('Are you sure you want to delete the selected data?').then(() => {
        deleteParts({
          plDetailIds: rec.map(a => a.plDetailId)
        }).then(() => {
          this.$message.success('Operation Successful')
          this.getList()
        })
      })

    },
    savePart() {
      this.submitForm(false)
      saveParts(this.list).then(res=>{
        this.$message.success('Operation Successful')
        this.getList()
      })
    },
    showMaterial(){
      this.handleMaterialReset()
      this.newVisible = true
    },
    querySearchAsync(queryString, cb, type) {
      if (queryString.length < 2) {
        return
      }
      getSearchResult({
        searchValue: queryString,
        type: type
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item
          }
        }))
      })
    },
    handleSelect(item, type) {
      switch (type) {
        case 'so':
          this.queryParams.soNo = item.value
          break
        case 'customer':
          this.queryParams.customer = item.value
          break
        case 'buyer':
          this.queryParams.buyer = item.value
          break
        case 'supplier':
          this.queryParams.supplier = item.value
          break
        case 'mfg':
          this.queryParams.mfg = item.value
          break
        // eslint-disable-next-line no-fallthrough
        default:
          break
      }
    },
    handleMaterialReset(){
      this.materialQuery = {
        time:[],
        pageNo: 1,
        pageSize: 10,
        search: '',
        no: '',
        partNo: '',
        mfg: '',
        mpn: '',
        supplier: '',
        buyer: '',
        timeType: '',
        startDate: '',
        endDate: '',
      }
    },
    saveMaterial() {
      const rec = this.$refs.toBeDeliveryTableRef.getSelectedRows()
      if (rec.length === 0) {
        this.$message.warning('Please select the data')
        return
      }
      newParts({
        plId: this.$route.params.id,
        shipDetailIds: rec.map(a => a.shipDetailId),
        type:this.type
      }).then(res=> {
        this.$message.success('Operation Successful')
        this.newVisible = false
        this.getList()

      })
    }
  }
}
</script>

<style lang="scss" scoped>
.commonFormItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 180px);
  }
}
.button-container {
  margin-top: 20px; /* 设置按钮与表格的距离 */
  text-align: right; /* 使按钮靠右对齐 */
}
.overList{
  ::v-deep .el-autocomplete-suggestion{
    width: auto !important;
    min-width: 180px;
    max-width: 600px !important;
  }
}
.searchValue{
  width: 100%;
}
.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}
::v-deep .avpl-row{
  background:#81D3F8 !important;
}
::v-deep .avpl-edit{
  background:#FFE668 !important;
}

</style>
