<template>
  <div class="app-container">
    <div style="margin-top: 25px;">
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.search"
          style="flex: 0 1 35%"
          :placeholder="$t('PO NO#/Part NO./Supplier')"
          @keyup.enter.native="getListInit"
        />
        <el-button plain type="primary" @click="getListInit">
          {{ $t('common.search') }}
        </el-button>
        <el-button
          style="margin-left: 0"
          @click="handleReset"
        >
          {{ $t('common.reset') }}
        </el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>
      </div>
      <el-form v-show="showSearch" inline label-width="120px">
        <!--po no-->
        <el-form-item class="searchItem" :label="$t('PO NO#')" prop="poNo">
          <el-input v-model="queryParams.poNo" :placeholder="$t('PO NO#')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <!--invoice no-->
        <el-form-item class="searchItem" :label="$t('Inv NO#')" prop="invoiceNo">
          <el-input v-model="queryParams.invoiceNo" :placeholder="$t('Inv NO#')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <!--part no-->
        <el-form-item class="searchItem" :label="$t('Part NO.')" prop="partNo">
          <el-input v-model="queryParams.partNo" :placeholder="$t('Part NO.')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <!--supplier-->
        <el-form-item class="searchItem" :label="$t('Supplier')" prop="">
          <el-autocomplete
            v-model="queryParams.supplier"
            :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'supplier')})"
            :placeholder="$t('Supplier')"
            :popper-append-to-body="false"
            popper-class="el-autocomplete-suggestion"
            class="overList"
            @select="((item) => {handleSelect(item, 'supplier')})"
            @keyup.enter.native="getListInit"
          />
        </el-form-item>
        <!--ship to-->
        <el-form-item  class="searchItem" :label="$t('Ship To')"  prop="shipTo">
          <el-select v-model="queryParams.shipTo" class="searchValue" clearable filterable >
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SHIP_TO)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- request delivery date -->
        <el-form-item :label="$t('Req.Del. Date')" class="searchItem">
          <el-date-picker
            v-model="queryParams.reqDeliveryDates"
            class="searchValue"
            value-format="yyyy-MM-dd"
            type="daterange"
            :range-separator="$t('order.to')"
            :start-placeholder="$t('common.startDate')"
            :end-placeholder="$t('common.endDate')"
          />
        </el-form-item>
        <!-- confirm delivery date -->
        <el-form-item :label="$t('Con.Del.Date')" class="searchItem">
          <el-date-picker
            v-model="queryParams.conDeliveryDates"
            class="searchValue"
            value-format="yyyy-MM-dd"
            type="daterange"
            :range-separator="$t('order.to')"
            :start-placeholder="$t('common.startDate')"
            :end-placeholder="$t('common.endDate')"
          />
        </el-form-item>
      </el-form>
      <!-- shipping plan -->
      <vxe-grid
        v-if="showPerspective.value===1"
        ref="plShippingPlan"
        :data="packingList"
        :loading="loading"
        v-bind="plShippingPlanGrid"
        key="plShippingPlan"
      >
        <template #dict="{column,row}">
          <dict-tag :type="column.params.dict" :value="row[column.field]" />
        </template>
        <template #unitPrice="{row}">
              <span v-if="row.unitPrice">
                {{ row.unitPrice?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #quantity="{row}">
              <span v-if="row.quantity">
                {{ row.quantity?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #amount="{row}">
              <span v-if="row.amount">
                {{ row.amount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="24" style="width: 100%" class="mb8">
            <el-col :span="18" style="display: flex">
              <!--create shipping order-->
              <el-button size="mini" plain type="primary" v-has-permi="['om:shipping-plan-shipping-order:create']" icon="el-icon-document" :loading="operateLoading" @click="createPackingOrder">{{ $t('Create Shipping Order') }}</el-button>
              <!--create delivery plan-->
              <el-button size="mini" v-if="showPerspective.value===1" v-has-permi="['om:shipping-plan-delivery-plan:create']" icon="el-icon-document" plain type="primary" @click="createDeliveryPlan">{{ $t('Create Delivery Plan') }}</el-button>
              <!--download-->
              <el-button size="mini" icon="el-icon-download" plain type="primary" @click="exportExcel" :loading="operateLoading">{{ $t('order.download') }}</el-button>
            </el-col>
            <el-col :span="4">
                <el-radio-group v-model="showPerspective.value" key="plPackingList" style="display:flex;align-items: center;height: 30px" @change="changePerspective">
                  <el-radio v-for="item in showPerspective.data" :key="item.value" :value="item.value" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
            </el-col>
            <el-col :span="2">
              <right-toolbar
                :list-id="plShippingPlanGrid.id"
                :show-search.sync="showSearch"
                :custom-columns.sync="plShippingPlanGrid.columns"
                @queryTable="getListInit"
              />
            </el-col>
          </el-row>
        </template>
        <template #plNO="{row}">
          <copy-button type="text" @click="$router.push(`/om/${row.type==='dp'?'deliveryInfo':'plInfo'}/${row.plId}?no=${row.plNO}`)">{{ row.plNO }}</copy-button>
        </template>

      </vxe-grid>
      <pagination
        v-if="showPerspective.value===1"
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="changePerspective()"
      />
      <!-- delivery plan-->
      <vxe-grid
        v-if="showPerspective.value===2"
        ref="plDeliveryPlan"
        :data="deliverPlanList"
        :loading="loading"
        v-bind="plDeliveryPlanGrid"
        :cell-class-name="cellClass"
        key="plDeliveryPlan"
      >
        <template #operate="{row}">
          <OperateDropDown
            :menu-item="[
              {
                name: $t('Modify Arrival Information'),
                show: $store.getters.permissions.includes('om:pl-supplier:maintain'),
                action: (row) =>maintainBox(row),
                para: row
              },
            ]"
          />
        </template>
        <template #dict="{column,row}">
          <dict-tag :type="column.params.dict" :value="row[column.field]" />
        </template>
        <template #dpNo="{row}">
          <copy-button type="text" @click="$router.push(`/om/deliveryInfo/${row.dpId}?no=${row.dpNo}`)">{{ row.dpNo }}</copy-button>
        </template>
        <template #invAmount="{row}">
              <span v-if="row.invAmount">
                {{ row.invAmount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="24" style="width: 100%" class="mb8">
            <el-col :span="18" style="display: flex">
                <!--create shipping order-->
              <el-button size="mini" plain type="primary" v-has-permi="['om:shipping-plan-shipping-order:create']" icon="el-icon-document" :loading="operateLoading" @click="createPackingOrder">{{ $t('Create Shipping Order') }}</el-button>
              <!--delete-->
              <el-button size="mini" v-if="showPerspective.value===2" v-has-permi="['om:shipping-plan-delivery-plan:delete']" icon="el-icon-delete" plain type="danger" @click="deletePackingList">{{ $t('Delete') }}</el-button>
<!--              hub confirm-->
              <el-button size="mini" type="primary" plain  v-has-permi="['om:shipping-plan:hub-confirm']" @click="sendEmailToHub">HUB Confirm</el-button>
              <el-button size="mini" type="primary" plain  v-has-permi="['om:shipping-plan:hub-receive']" @click="doHubReceive">HUB Receive</el-button>
              <!--download-->
              <el-button size="mini" icon="el-icon-download" plain type="primary" @click="exportDeliveryPlanExcel" :loading="operateLoading">{{ $t('order.download') }}</el-button>
            </el-col>
            <el-col :span="4">
              <el-radio-group v-model="showPerspective.value" key="deliveryPlanList" style="display:flex;align-items: center;height: 30px" @change="changePerspective">
                <el-radio v-for="item in showPerspective.data" :key="item.value" :value="item.value" :label="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-col>
            <el-col :span="2">
              <right-toolbar
                :list-id="plDeliveryPlanGrid.id"
                :show-search.sync="showSearch"
                :custom-columns.sync="plDeliveryPlanGrid.columns"
                @queryTable="getListInit"
              />
            </el-col>
          </el-row>
        </template>
      </vxe-grid>
      <pagination
        v-if="showPerspective.value===2"
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="changePerspective()"
      />
    </div>
    <updatebox ref="updateBox" :plSupplierId="plSupplierId" @refresh-table="changePerspective" />
  </div>
</template>
<script>
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { getSearchResult } from '@/api/om/orderTracker'
import { parseTime } from '@/utils/ruoyi'
import {DICT_TYPE, getDictDatas} from '@/utils/dict'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import operationRecord from '@/components/OperationRecord/operationRecord.vue'
import {deletePackingList, newPl, newPlFromDp, sendToHub, startShip, hubReceive, notHk} from '@/api/om/pl/pl'
import {
  exportDeliveryPlan,
  exportShipping,
  getDeliveryPlanPage,
  getShippingPlanPage,
  newDp
} from "@/api/om/pl/shippingPlan";
import Updatebox from "@/views/om/pl/shippingPlan/updateBox.vue";
export default {
  name: 'Omshippingplan',
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  components: {Updatebox, operationRecord,BatchUpload, OperateDropDown },
  data() {
    return {
      queryParams: {
        search: '',
        invoiceNo:'',
        supplier: '',
        poNo:'',
        partNo: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: '',
        shipTo:'',
        reqDeliveryDates:[],
        startReqDeliveryDate:null,
        endReqDeliveryDate:null,
        conDeliveryDates:[],
        startConDeliveryDate:null,
        endConDeliveryDate:null
      },
      total: 0,
      packingList:[],
      deliverPlanList:[],
      showSearch: false,
      loading: false,
      operateLoading:false,
      showPerspective:{
        value:1,
        data:[
          {
            label:'Shipping Plan',
            value:1
          },
          {
            label:'Delivery Plan',
            value: 2
          }
        ]
      },
      plShippingPlanGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'plShippingPlan',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
          { title: 'Status', field: 'status',  slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SO_STATUS }, visible: true, width: 130, fixed: 'left' },
          { title: 'Delivery Plan Status',field: 'dpStatus',slots:{default:'dict'},params: { dict: DICT_TYPE.OM_DP_STATUS }, visible: true, width: 100,fixed: "left"},
          { title: 'In Use', field: 'plNO',  slots: { default: 'plNO' },  visible: true, width: 130, fixed: 'left' },
          { title: 'Req.Del.Date', field: 'reqDelDate',  visible: true, width: 130, fixed: 'left',formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''},
          { title: 'Con.Del.Date', field: 'conDelDate',  visible: true, width: 130, fixed: 'left',formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''},
          { title: 'Pass Due', field: 'passDue', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_PASS_DUE }, visible: true, width: 100},
          { title: 'Part No.', field: 'partNo', visible: true, width: 100,align: 'right'},
          { title: 'Pur. Price', field: 'unitPrice',slots:{default:'unitPrice'}, visible: true, width: 100,align: 'right' },
          { title: 'Con.Del.Qty', field: 'quantity',slots:{default:'quantity'}, visible: true, width: 100,align: 'right' },
          { title: 'Amount', field: 'amount',slots:{default:'amount'}, visible: true, width: 100,align: 'right' },
          { title: 'PO NO#', field: 'poNo', visible: true, width: 100 },
          { title: 'Supplier', field: 'supplier', visible: true, width: 100 },
          { title: 'Ship To', field: 'shipTo', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SHIP_TO }, visible: true, width: 100 },
          { title: 'Goods On', field: 'goodsOn',visible: true, width: 100 },
          { title: 'Vend Inv.', field: 'supplierInvoiceNo', visible: true, width: 100 },
          { title: 'Vend Inv.Date', field: 'supplierInvoiceData', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',visible: true, width: 100 },
          { title: 'Pay.Term', field: 'paymentTerm', slots: { default: 'dict' }, params: { dict: DICT_TYPE.SUPPLIER_PAYMENT_DAYS }, visible: true, width: 100 },
          { title: 'Paid PO%', field: 'paid', visible: true, width: 100,align: 'right' },
          { title: '# of Cartons', field: 'carton', visible: true, width: 100,align: 'right'},
          { title: '木箱外径尺寸/单箱', field: 'boxSize', visible: true, width: 100,align: 'right'},
          { title: 'Net Weight(KG)', field: 'netWeight', visible: true, width: 100,align: 'right'},
          { title: 'Gross Weight(KG)', field: 'grossWeight', visible: true, width: 100,align: 'right'},
          { title: 'Volume(M3)', field: 'volume', visible: true, width: 100,align: 'right'}
        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      plDeliveryPlanGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'plDeliveryPlan',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          // keyField:'dpId',
          isHover: true
        },
        columns: [
          { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
          { title: 'Date', field: 'date',formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 120, fixed: 'left' },
          { title: 'DP NO#', field: 'dpNo', fixed: 'left', visible: true, width: 120, slots:{default:'dpNo'}},
          { title: 'Supplier', field: 'supplier',  visible: true, width: 120, fixed: 'left'},
          { title: 'Delivery Plan Status',field: 'status',slots:{default:'dict'},params: { dict: DICT_TYPE.OM_DP_STATUS }, visible: true, width: 100,fixed: "left"},
          { title: 'Ship To', field: 'shipTo', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SHIP_TO }, visible: true, width: 100},
          { title: 'Vend Inv.Date', field: 'invDate',formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100},
          { title: 'Vend Inv.', field: 'invNo', visible: true, width: 100},
          { title: 'Vend inv.Amount', field: 'invAmount',slots:{default:'invAmount'}, visible: true, width: 100,align: 'right'},
          { title: 'Bank Charge', field: 'bankCharge', visible: true, width: 100,align: 'right'},
          { title: '#AWB', field: 'awb', visible: true,  width: 100},
          { title: 'Number of Carton(s)(Supplier)', field: 'carton', params:{field:'cartonHub'} , visible: true,align: 'right' },
          { title: '木箱外径尺寸/单箱(Supplier)', field: 'boxSize', params:{field:'boxSizeHub'}, visible: true},
          { title: 'NetWeight(kg)(Supplier)', field: 'netWeight', params:{field:'netWeightHub'}, visible: true,align: 'right' },
          { title: 'Gross Weight(kg)(Supplier)', field: 'grossWeight', params:{field:'grossWeightHub'}, visible: true,align: 'right'},
          { title: 'Volume(m3)(Supplier)', field: 'volume', params:{field:'volumeHub'}, visible: true,align: 'right' },
          { title: 'Number of Carton(s)(HUB)', field: 'cartonHub', params:{field:'carton'} , visible: true,align: 'right' },
          { title: '木箱外径尺寸/单箱(HUB)', field: 'boxSizeHub', params:{field:'boxSize'}, visible: true},
          { title: 'NetWeight(kg)(HUB)', field: 'netWeightHub', params:{field:'netWeight'}, visible: true,align: 'right' },
          { title: 'Gross Weight(kg)(HUB)', field: 'grossWeightHub', params:{field:'grossWeight'}, visible: true,align: 'right'},
          { title: 'Volume(m3)(HUB)', field: 'volumeHub', params:{field:'volume'}, visible: true,align: 'right' },
          {
            field: 'operate',
            slots: { default: 'operate' }, fixed: 'right',
            title: this.$t('common.operate'), visible: true, width: 35, showOverflow: false
          }
        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      plSupplierId: '',
    }
  },
  mounted() {
    if (this.$route.query.perspective != null){
      this.showPerspective.value=parseInt(this.$route.query.perspective);
    }
    this.changePerspective()
  },
  activated() {
    if (this.$route.query.perspective != null){
      this.showPerspective.value=parseInt(this.$route.query.perspective);
    }
    this.changePerspective()
  },
  watch:{
    'showPerspective.value':function() {
      this.changePerspective()
    }
  },
  methods: {
    getDictDatas,
    // show operate log
    showLogs(businessId, table, column,keyColumns) {
      this.log.open = true
      this.log.businessId = businessId
      this.log.columns = [{
        tableName: table,
        searchColumns: column,
        keyColumns:keyColumns
      }]
    },
    getShippingPlanList() {
      this.loading = true
      if (this.queryParams.reqDeliveryDates?.length) {
        this.queryParams.startReqDeliveryDate = this.queryParams.reqDeliveryDates[0] ? this.queryParams.reqDeliveryDates[0] + 'T00:00:00' : undefined
        this.queryParams.endReqDeliveryDate = this.queryParams.reqDeliveryDates[1] ? this.queryParams.reqDeliveryDates[1] + 'T23:59:59' : undefined
      }
      if (this.queryParams.conDeliveryDates?.length) {
        this.queryParams.startConDeliveryDate = this.queryParams.startConDeliveryDate[0] ? this.queryParams.startConDeliveryDate[0] + 'T00:00:00' : undefined
        this.queryParams.endConDeliveryDate = this.queryParams.endConDeliveryDate[1] ? this.queryParams.endConDeliveryDate[1] + 'T23:59:59' : undefined
      }
      getShippingPlanPage(this.queryParams).then(res => {
        this.loading = false
        this.packingList = res.data.list
        this.total = res.data.total
      })
    },
    getDeliveryPlanList() {
      this.loading = true
      if (this.queryParams.reqDeliveryDates?.length) {
        this.queryParams.startReqDeliveryDate = this.queryParams.reqDeliveryDates[0] ? this.queryParams.reqDeliveryDates[0] + 'T00:00:00' : undefined
        this.queryParams.endReqDeliveryDate = this.queryParams.reqDeliveryDates[1] ? this.queryParams.reqDeliveryDates[1] + 'T23:59:59' : undefined
      }
      if (this.queryParams.conDeliveryDates?.length) {
        this.queryParams.startConDeliveryDate = this.queryParams.startConDeliveryDate[0] ? this.queryParams.startConDeliveryDate[0] + 'T00:00:00' : undefined
        this.queryParams.endConDeliveryDate = this.queryParams.endConDeliveryDate[1] ? this.queryParams.endConDeliveryDate[1] + 'T23:59:59' : undefined
      }
      getDeliveryPlanPage(this.queryParams).then(res => {
        this.loading = false
        this.deliverPlanList = res.data.list
        this.total = res.data.total
      })
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.changePerspective()
    },
    handleSelect(item, type) {
      switch (type) {
        case 'so':
          this.queryParams.no = item.value
          break
        case 'customer':
          this.queryParams.customer = item.value
          break
        case 'buyer':
          this.queryParams.buyer = item.value
          break
        case 'supplier':
          this.queryParams.supplier = item.value
          break
        case 'mfg':
          this.queryParams.mfg = item.value
          break
        // eslint-disable-next-line no-fallthrough
        default:
          break
      }
    },
    querySearchAsync(queryString, cb, type) {
      if (queryString.length < 2) {
        return
      }
      getSearchResult({
        searchValue: queryString,
        type: type
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item
          }
        }))
      })
    },
    handleReset() {
      this.queryParams = {
        search: '',
        invoiceNo:'',
        supplier: '',
        poNo:'',
        partNo: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: '',
        shipTo:'',
        reqDeliveryDates:[],
        startReqDeliveryDate:null,
        endReqDeliveryDate:null,
        conDeliveryDates:[],
        startConDeliveryDate:null,
        endConDeliveryDate:null
      }
      this.changePerspective()
    },
    batchReleaseShip() {
      const selectRows = this.$refs.plShippingPlan.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to release!'))
        return false
      } else {
        this.$modal.confirm('Confirm the release?').then(() => {
          this.operateLoading=true
          return startShip(selectRows.map(row => row.id).join(','))
        }).then(res => {
          if (res.data) {
            this.$message.success('Operation Successful')
            this.changePerspective()
          }
        }).finally(() => {
          this.operateLoading = false
        })
      }
    },
    exportExcel() {
      this.$modal.confirm(this.$t('Do you confirm the export of the Shipping Plan data?')).then(() => {
        this.operateLoading=true
        return exportShipping(this.queryParams)
      }).then(res => {
        this.$download.excel(res, 'Shipping Plan.xlsx')
      }).finally(() => {
        this.operateLoading = false
      })
    },
    exportDeliveryPlanExcel() {
      this.$modal.confirm(this.$t('Do you confirm the export of the Delivery Plan data?')).then(() => {
        this.operateLoading=true
        return exportDeliveryPlan(this.queryParams)
      }).then(res => {
        this.$download.excel(res, 'Delivery Plan.xlsx')
      }).finally(() => {
        this.operateLoading = false
      })
    },
    //switch perspective
    changePerspective(){
      console.log('this.showPerspective.value', this.showPerspective.value)
        switch (this.showPerspective.value){
          case 1:
            this.getShippingPlanList()
            break;
          case 2:
            this.getDeliveryPlanList()
            break;
          default:
            this.getShippingPlanList()
        }
    },
    // delete the selected packing list
    deletePackingList(){
      const selectRows = this.$refs.plDeliveryPlan.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to delete'))
        return false
      } else {
        this.$modal.confirm(this.$t('Do you confirm delete the selected Delivery Plan?')).then(() => {
          this.operateLoading = true
          return deletePackingList({plIds:selectRows.map(row => row.dpId).join(',')})
        }).then(res => {
          if (res.data) {
            this.$message.success('Operation Successful')
            this.changePerspective();
          }
        }).finally(() => {
          this.operateLoading = false
        })
      }
    },
    //create delivery plan
    createDeliveryPlan(){
      const selectRows = this.$refs.plShippingPlan.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to operate'))
        return false
      }
      this.$modal.confirm(this.$t('Do you confirm the creation of a Delivery Plan base on selected records?')).then(() => {
        this.operateLoading = true
        return newDp({shipDetailIds: selectRows.map(row => row.shipDetailId).join()})
      }).then(res=>{
        if (res.data){
          this.$message.success('Operation Successful')
          this.$router.push({path:'/om/deliveryInfo/'+res.data.id+'?no='+res.data.plNo})
        }else{
          this.$message.success('Operation failed')
        }
      }).finally(() => {
        this.operateLoading = false
      })

    },
    maintainBox(row){
        this.plSupplierId=row.plSupplierId
        this.$nextTick(()=>{
          this.$refs.updateBox.showDialog=true
          this.$refs.updateBox.getPlSupplier()
      })
    },
    //create packing order
    createPackingOrder(){
      switch (this.showPerspective.value){
        case 1:
          // 选中行
          const shippingPlanSelectRows = this.$refs.plShippingPlan.getCheckboxRecords()
          if (shippingPlanSelectRows.length === 0) {
            this.$message.warning(this.$t('Please select the record to operate'))
            return false
          } else {
            notHk({shipDetailIds: shippingPlanSelectRows.map(row => row.shipDetailId).join(),showPerspective:this.showPerspective.value}).then(res=>{
              let confirmMsg = '';
              if (res.data){
                confirmMsg = this.$t('您选择了包含多个供应商且Ship To不是Hong Kong的记录，是否继续创建？');
              }else{
                confirmMsg = this.$t('Do you confirm the creation of a Packing List based on selected records?');
              }
              this.$modal.confirm(confirmMsg).then(() => {
                this.operateLoading = true
                return  newPl({shipDetailIds: shippingPlanSelectRows.map(row => row.shipDetailId).join()})
              }).then(res=>{
                if (res.data){
                  this.$message.success('Operation Successful')
                  this.$router.push({path:'/om/plInfo/'+res.data.id+'?no='+res.data.plNo})
                }else{
                  this.$message.success('Operation failed')
                }
              }).finally(() => {
                this.operateLoading = false
              })
            })
          }
          break;
        case 2: const selectRows = this.$refs.plDeliveryPlan.getCheckboxRecords()
          if (selectRows.length === 0) {
            this.$message.warning(this.$t('Please select the record to operate'))
            return false
          } else{
            notHk({shipDetailIds: selectRows.map(row => row.dpId).join(),showPerspective:this.showPerspective.value}).then(res=> {
              let confirmMsg = '';
              if (res.data) {
                confirmMsg = this.$t('您选择了包含多个供应商且Ship To不是Hong Kong的记录，是否继续创建？');
              } else {
                confirmMsg = this.$t('Do you confirm the creation of a Packing List based on selected records?');
              }
              this.$modal.confirm(confirmMsg).then(() => {
                this.operateLoading = true
                return  newPlFromDp({dpIds: selectRows.map(row => row.dpId).join()})
              }).then(res=>{
                if (res.data){
                  this.$message.success('Operation Successful')
                  this.$router.push({path:'/om/plInfo/'+res.data.id+'?no='+res.data.plNo})
                }else{
                  this.$message.success('Operation failed')
                }
              }).finally(() => {
                this.operateLoading = false
              })
            })
          }
          break;
        default:
          break;
      }
    },
    //HUB confirm
    sendEmailToHub(){
      const rec = this.$refs.plDeliveryPlan.getCheckboxRecords()
      if (rec.length === 0) {
        this.$message.warning('Please select the data to be sent')
        return
      }
      if (rec.length > 1){
        this.$message.warning('Please select only one record')
        return
      }
      this.$modal.confirm(this.$t('确认发送给HUB提醒确认供应商的货物信息?')).then(() => {
        sendToHub({dpIds: rec.map(row => row.dpId).join()})
      }).then(() => {
        this.$message.success('Operation Successful')
        this.getList()
      })
    },
    // hub receive
    // copy 5 supplier rel fields val to hub fields（overlay old val if exist）
    // update dp status OK
    doHubReceive() {
      const selectRows = this.$refs.plDeliveryPlan.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning('Please select the data to be operate')
        return
      }
      // om_pl_master#id
      const dpIds=selectRows.map(row => row.dpId).join()
      this.$modal.confirm(this.$t('Confirm that the selected data is received with one click?')).then(() => {
        hubReceive({dpIds: dpIds}).then(res => {
          this.getDeliveryPlanList()
        })
      }).then(() => {
        this.$message.success('Operation Successful')
      })
    },
    // cell style config
    cellClass({ row, column }) {
      if (['cartonHub', 'boxSizeHub', 'netWeightHub', 'grossWeightHub', 'volumeHub'].includes(column.field) && row[column.field] !== row[column.params.field]) {
        return 'avpl-edit'
      }
    },
  }
}
</script>
<style scoped lang="scss">
.button-container {
  margin-top: 20px; /* 设置按钮与表格的距离 */
  text-align: right; /* 使按钮靠右对齐 */
}
.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }

  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}
.searchValue {
  width: 100%;
}
::v-deep .vxe-header--column{
  height: 43px!important;
  line-height: 16px;
}
::v-deep .vxe-cell--title{
  white-space: pre-wrap!important;
}
::v-deep .avpl-edit{
  background:#FFE668 !important;
}
</style>
