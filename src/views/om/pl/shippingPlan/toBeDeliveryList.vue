

<template>
  <div class="app-container">
    <div style="margin-top: 25px;">
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.search"
          style="flex: 0 1 35%"
          :placeholder="$t('PO NO#/Part NO./Inv NO#/Supplier')"
          @keyup.enter.native="getListInit"
        />
        <el-button plain type="primary" @click="getListInit">
          {{ $t('common.search') }}
        </el-button>
        <el-button
          style="margin-left: 0"
          @click="handleReset"
        >
          {{ $t('common.reset') }}
        </el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>
      </div>
      <el-form v-show="showSearch" inline label-width="120px">
        <!--po no-->
        <el-form-item class="searchItem" :label="$t('PO NO#')" prop="poNo">
          <el-input v-model="queryParams.poNo" :placeholder="$t('PO NO#')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <!--invoice no-->
        <el-form-item class="searchItem" :label="$t('Inv NO#')" prop="invoiceNo">
          <el-input v-model="queryParams.invoiceNo" :placeholder="$t('Inv NO#')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <!--part no-->
        <el-form-item class="searchItem" :label="$t('Part NO.')" prop="partNo">
          <el-input v-model="queryParams.partNo" :placeholder="$t('Part NO.')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <!--supplier-->
        <el-form-item class="searchItem" :label="$t('Supplier')" prop="">
          <el-autocomplete
            v-model="queryParams.supplier"
            :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'supplier')})"
            :placeholder="$t('Supplier')"
            :popper-append-to-body="false"
            popper-class="el-autocomplete-suggestion"
            class="overList"
            @select="((item) => {handleSelect(item, 'supplier')})"
            @keyup.enter.native="getListInit"
          />
        </el-form-item>
        <!--ship to-->
        <el-form-item  class="searchItem" :label="$t('Ship To')"  prop="shipTo">
          <el-select v-model="queryParams.shipTo" class="searchValue" clearable filterable >
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SHIP_TO)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- request delivery date -->
        <el-form-item :label="$t('Req.Del. Date')" class="searchItem">
          <el-date-picker
            v-model="queryParams.reqDeliveryDates"
            class="searchValue"
            value-format="yyyy-MM-dd"
            type="daterange"
            :range-separator="'-'"
            :start-placeholder="$t('common.startDate')"
            :end-placeholder="$t('common.endDate')"
          />
        </el-form-item>
        <!-- confirm delivery date -->
        <el-form-item :label="$t('Con.Del.Date')" class="searchItem">
          <el-date-picker
            v-model="queryParams.conDeliveryDates"
            class="searchValue"
            value-format="yyyy-MM-dd"
            type="daterange"
            :range-separator="'-'"
            :start-placeholder="$t('common.startDate')"
            :end-placeholder="$t('common.endDate')"
          />
        </el-form-item>
      </el-form>
      <!-- shipping plan -->
      <vxe-grid
        ref="toBeDeliveryRef"
        :data="toBeDeliveryList"
        :loading="loading"
        v-bind="toBeDeliveryGrid"
        key="toBeDelivery"
      >
        <template #dict="{column,row}">
          <dict-tag :type="column.params.dict" :value="row[column.field]" />
        </template>
        <template #unitPrice="{row}">
              <span v-if="row.unitPrice">
                {{ row.unitPrice?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #quantity="{row}">
              <span v-if="row.quantity">
                {{ row.quantity?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #amount="{row}">
              <span v-if="row.amount">
                {{ row.amount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
      </vxe-grid>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getToBeDeliveryList()"
      />
    </div>
  </div>
</template>

<script>
import {DICT_TYPE} from "@/utils/dict";
import {parseTime} from "@/utils/ruoyi";
import {getShippingPlanPage} from "@/api/om/pl/shippingPlan";

export default{
  name:'Tobedelivery',
  props:{
    documentType:{
      type:String,
      default:'dp'
    }
  },
  data(){
    return {
      queryParams: {
        search: '',
        invoiceNo:'',
        supplier: '',
        poNo:'',
        partNo: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: '',
        shipTo:'',
        reqDeliveryDates:[],
        startReqDeliveryDate:null,
        endReqDeliveryDate:null,
        conDeliveryDates:[],
        startConDeliveryDate:null,
        endConDeliveryDate:null,
        type:null
      },
      toBeDeliveryGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'toBeDelivery',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          keyField:'shipDetailId',
          isHover: true
        },
        columns: [
          { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
          { title: 'Status', field: 'status',  slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SO_STATUS }, visible: true, width: 130, fixed: 'left' },
          { title: 'Req.Del.Date', field: 'reqDelDate',  visible: true, width: 130, fixed: 'left',formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''},
          { title: 'Con.Del.Date', field: 'conDelDate',  visible: true, width: 130, fixed: 'left',formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''},
          { title: 'Pass Due', field: 'passDue', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_PASS_DUE }, visible: true, width: 100},
          { title: 'Part No.', field: 'partNo', visible: true, width: 100,align: 'right'},
          { title: 'Pur. Price', field: 'unitPrice',slots:{default:'unitPrice'}, visible: true, width: 100,align: 'right' },
          { title: 'Con.Del.Qty', field: 'quantity',slots:{default:'quantity'}, visible: true, width: 100,align: 'right' },
          { title: 'Amount', field: 'amount',slots:{default:'amount'}, visible: true, width: 100 },
          { title: 'PO NO#', field: 'poNo', visible: true, width: 100 },
          { title: 'Supplier', field: 'supplier', visible: true, width: 100 },
          { title: 'Ship To', field: 'shipTo', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SHIP_TO }, visible: true, width: 100 },
          { title: 'Goods On', field: 'goodsOn',visible: true, width: 100 },
          { title: 'Vend Inv.', field: 'supplierInvoiceNo', visible: true, width: 100 },
          { title: 'Vend Inv.Date', field: 'supplierInvoiceData', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',visible: true, width: 100 },
          { title: 'Pay.Term', field: 'paymentTerm', slots: { default: 'dict' }, params: { dict: DICT_TYPE.SUPPLIER_PAYMENT_DAYS }, visible: true, width: 100 },
          { title: 'Paid PO%', field: 'paid', visible: true, width: 100 },
          { title: '# of Cartons', field: 'carton', visible: true, width: 100,align: 'right'},
          { title: '木箱外径尺寸/单箱', field: 'boxSize', visible: true, width: 100,align: 'right'},
          { title: 'Net Weight(KG)', field: 'netWeight', visible: true, width: 100,align: 'right'},
          { title: 'Gross Weight(KG)', field: 'grossWeight', visible: true, width: 100,align: 'right'},
          { title: 'Volume(M3)', field: 'volume', visible: true, width: 100,align: 'right'}
        ],
        sortConfig: {
          remote: true
        }
      },
      showSearch:false,
      toBeDeliveryList:[],
      total:0,
      loading:false,

    }
  },
  activated() {
    this.handleReset()
  },
  mounted(){
    this.handleReset()
  },
  methods:{
    getListInit() {
      this.queryParams.pageNo = 1
      this.getToBeDeliveryList()
    },
    getToBeDeliveryList() {
      this.loading = true
      if (this.queryParams.reqDeliveryDates?.length) {
        this.queryParams.startReqDeliveryDate = this.queryParams.reqDeliveryDates[0] ? this.queryParams.reqDeliveryDates[0] + 'T00:00:00' : undefined
        this.queryParams.endReqDeliveryDate = this.queryParams.reqDeliveryDates[1] ? this.queryParams.reqDeliveryDates[1] + 'T23:59:59' : undefined
      }
      if (this.queryParams.conDeliveryDates?.length) {
        this.queryParams.startConDeliveryDate = this.queryParams.startConDeliveryDate[0] ? this.queryParams.startConDeliveryDate[0] + 'T00:00:00' : undefined
        this.queryParams.endConDeliveryDate = this.queryParams.endConDeliveryDate[1] ? this.queryParams.endConDeliveryDate[1] + 'T23:59:59' : undefined
      }
      this.queryParams.type=this.documentType;
      getShippingPlanPage(this.queryParams).then(res => {
        this.loading = false
        this.toBeDeliveryList = res.data.list
        this.total = res.data.total
      })
    },
    handleReset() {
      this.queryParams = {
        search: '',
        invoiceNo:'',
        supplier: '',
        poNo:'',
        partNo: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: '',
        shipTo:'',
        reqDeliveryDates:[],
        startReqDeliveryDate:null,
        endReqDeliveryDate:null,
        conDeliveryDates:[],
        startConDeliveryDate:null,
        endConDeliveryDate:null
      }
      this.getToBeDeliveryList()
    },
    getSelectedRows() {
      return this.$refs.toBeDeliveryRef.getCheckboxRecords();
    },
  }


}
</script>

<style scoped lang="scss">
.button-container {
  margin-top: 20px; /* 设置按钮与表格的距离 */
  text-align: right; /* 使按钮靠右对齐 */
}
.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }

  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}

.searchValue {
  width: 100%;
}

::v-deep .vxe-header--column{
  height: 43px!important;
  line-height: 16px;
}
::v-deep .vxe-cell--title{
  white-space: pre-wrap!important;
}
</style>
