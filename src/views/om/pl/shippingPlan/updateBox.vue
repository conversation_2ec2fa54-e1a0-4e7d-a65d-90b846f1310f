<template>
  <div  class="app-container">
    <el-dialog
      title="Maintain Packing List"
      v-if="showDialog"
      width="1200px"
      :visible.sync="showDialog"
    >
      <div style="display: flex;justify-content: center;margin-bottom: 30px">
        <el-form
          ref="updateBox"
          :model="updateEntity"
          inline
          label-width="180px"
          style="margin-top: 20px"
        >
          <!-- cartons -->
          <el-form-item :label="$t('Carton(s)')" class="commonFormItem">
            <vxe-input type="integer" style="width: 100%" v-model="updateEntity.cartonHub" :placeholder="updateEntity.carton" />
          </el-form-item>
          <!-- net weight(kg) -->
          <el-form-item :label="$t('Net Weight(KG)')" class="commonFormItem">
            <vxe-input type="number" style="width: 100%" v-model="updateEntity.netWeightHub" :placeholder="updateEntity.netWeight" />
          </el-form-item>
          <!-- gross weight(kg) -->
          <el-form-item  :label="$t('Gross Weight(KG)')" class="commonFormItem">
            <vxe-input type="number" style="width: 100%" v-model="updateEntity.grossWeightHub" :placeholder="updateEntity.grossWeight"/>
          </el-form-item>
          <!-- volume -->
          <el-form-item  :label="$t('Volume(m3)')" class="commonFormItem">
            <vxe-input type="number" style="width: 100%" v-model="updateEntity.volumeHub" :placeholder="updateEntity.volume"  />
          </el-form-item>
          <!-- box size -->
          <el-form-item :label="$t('木箱外径尺寸/单箱')" class="commonFormItem" style="width: 66.6%">
            <el-input  style="width: 100%;"  type="textarea" :rows="2" v-model="updateEntity.boxSizeHub" :placeholder="updateEntity.boxSize" />
          </el-form-item>
          <div style="text-align: right">
            <el-button @click="closeDialog">{{ $t('common.cancel') }}</el-button>
            <el-button style="margin-left: 20px" type="primary" @click="updateBoxInfo">{{ $t('Confirm') }}</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {getPlSupplier, updateBox} from "@/api/om/pl/pl";

export default{
  name:"Updatebox",
  props:{
    plSupplierId:{
      type:String
    }
  },
  data(){
    return {
      //update entity
      updateEntity:{
        plSupplierId:null,
        carton:null,
        cartonHub:null,
        boxSize:'',
        boxSizeHub:'',
        netWeight:null,
        netWeightHub:null,
        grossWeight:null,
        grossWeightHub:null,
        volume:null,
        volumeHub:null,
        plId:null
      },
      //dialog show flag
      showDialog:false
    }
  },
  methods:{
    closeDialog(){
      this.showDialog=false
    },
    updateBoxInfo(){
      this.updateEntity.plSupplierId=this.plSupplierId;
      updateBox(this.updateEntity).then(res=>{
        if (res.data){
          this.$message({
            message: this.$t('common.updateSuccess'),
            type: 'success'
          })
          this.closeDialog();
          this.$emit("refresh-table")
        }
      })
    },
    getPlSupplier(){
      getPlSupplier(this.plSupplierId).then(res=>{
        this.updateEntity=res.data
      })
    }
  }
}


</script>

<style scoped lang="scss">
.commonFormItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 180px);
  }
}
</style>
