<template>
  <div style="padding: 25px 15px">
    <div style="display: flex;justify-content: center;margin-bottom: 30px">
      <el-form
        ref="poDetailInfo"
        :model="poDetailInfo"
        class="baseInfo"
        inline
        label-width="150px"
      >
        <!-- Part No. -->
        <el-form-item ref="partNo" :label="$t('Part No.')">
          <show-or-edit :value="poDetailInfo.partNo">
            <el-input v-model="poDetailInfo.partNo" :placeholder="$t('common.pleaseEnter')"   :disabled="true"/>
          </show-or-edit>
        </el-form-item>

        <!-- Description -->
        <el-form-item ref="description" :label="$t('Description')">
          <show-or-edit :value="poDetailInfo.description">
            <el-input v-model="poDetailInfo.description" :placeholder="$t('common.pleaseEnter')" :disabled="true"/>
          </show-or-edit>
        </el-form-item>

        <!-- MFG -->
        <el-form-item ref="mfg" :label="$t('MFG')">
          <show-or-edit :value="poDetailInfo.mfg">
            <el-input v-model="poDetailInfo.mfg" :placeholder="$t('common.pleaseEnter')" :disabled="true"/>
          </show-or-edit>
        </el-form-item>

        <!-- MPN -->
        <el-form-item ref="mpn" :label="$t('MPN')">
          <show-or-edit :value="poDetailInfo.mpn">
            <el-input v-model="poDetailInfo.mpn" :placeholder="$t('common.pleaseEnter')" :disabled="true"/>
          </show-or-edit>
        </el-form-item>

        <!-- 版本 -->
        <el-form-item ref="version" :label="$t('Version')">
          <show-or-edit :value="poDetailInfo.version">
            <el-input v-model="poDetailInfo.version" :placeholder="$t('common.pleaseEnter')" :disabled="true"/>
          </show-or-edit>
        </el-form-item>

        <!-- 单位 -->
        <el-form-item ref="uom" :label="$t('UOM')">
          <show-or-edit :dict="DICT_TYPE.MATERIAL_UOM" :value="poDetailInfo.uom">
            <el-select v-model="poDetailInfo.uom" class="smallSelect" clearable style="width: 100%;" :disabled="true">
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_UOM)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>

        <!-- 价格单位 -->
        <el-form-item ref="pricePer" :label="$t('Price Per')">
          <show-or-edit :value="poDetailInfo.pricePer">
            <vxe-input
              :disabled="true"
              v-model="poDetailInfo.pricePer"
              size="mini"
              type="number"
              style="width: 100%"
            />
          </show-or-edit>
        </el-form-item>

        <!-- Lead Time -->
        <el-form-item ref="leadTime" :label="$t('L/T (day)')">
          <show-or-edit :value="poDetailInfo.leadTime">
            <vxe-input
              v-model="poDetailInfo.leadTime"
              size="mini"
              type="number"
              style="width: 100%"
            />
          </show-or-edit>
        </el-form-item>

        <!-- Pur. U/P -->
        <el-form-item ref="unitPrice" :label="$t('Pur. U/P')">
          <show-or-edit :value="poDetailInfo.unitPrice">
            <vxe-input
              v-model="poDetailInfo.unitPrice"
              size="mini"
              type="number"
              style="width: 100%"
            />
          </show-or-edit>
        </el-form-item>

        <!-- Order Quantity -->
        <el-form-item ref="orderQuantity" :label="$t('Order Quantity')">
          <show-or-edit :value="poDetailInfo.orderQuantity">
            <vxe-input
              v-model="poDetailInfo.orderQuantity"
              size="mini"
              type="number"
              style="width: 100%"
            />
          </show-or-edit>
        </el-form-item>

        <!-- Req.Del.Date -->
        <el-form-item ref="reqDelDate" :label="$t('PO Req. Del.Date')">
          <show-or-edit :value="poDetailInfo.reqDelDate">
            <el-date-picker
              v-model="poDetailInfo.reqDelDate"
              style="width: 100%"
              class="commonForm"
              :placeholder="$t('common.pleaseSelectADate')"
              type="date"
              placement="bottom-start"
              value-format="yyyy-MM-dd"
            />
          </show-or-edit>
        </el-form-item>

        <!-- Req.Del.Date -->
        <el-form-item />

        <!-- Req.Del.Date -->
        <el-form-item ref="remark" :label="$t('Remark')"  style="width:100%">
          <show-or-edit :value="poDetailInfo.remark">
            <el-input v-model="poDetailInfo.remark" :placeholder="$t('common.pleaseEnter')"/>
          </show-or-edit>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import router from '@/router'
import { getPoDetailInfo, savePoDetailInfo } from '@/api/om/po'

export default {
  name: 'poDetail/:id',
  components: {
    ShowOrEdit
  },
  props: {
    poId: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      poDetailInfo: {
        id: null,
        partNo: '', // 物料编码
        description: '', // 物料描述
        version: '', // 版本
        mfg: '', // mfg
        mpn: '', // mpn
        uom: '', // 单位
        pricePer: null, // 价格单位
        unitPrice: null, // 单价
        orderQuantity: null, // 订单数量
        reqDelDate: null, // 要求交付日期
        leadTime: null, // 交期
        remark: '' // 备注
      }
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  mounted() {
    this.init()
  },
  activated() {
    this.init()
  },
  methods: {
    router() {
      return router
    },
    getDictDatas,
    init() {
      if (this.poId !== '0') {
        getPoDetailInfo(this.poId).then(res => {
          this.poDetailInfo = res.data
        })
      }
    },
    // 新增供应商数据保存
    savePoDetailInfo() {
      this.$refs['poDetailInfo'].validate(valid => {
        if (valid) {
          savePoDetailInfo(this.poDetailInfo).then(res => {
            this.$modal.msgSuccess(this.$t('common.savedSuccessfully'))
            this.$emit('freshList')
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.baseInfo {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;

    .el-form-item__content {
      width: calc(100% - 200px);
    }
  }
}

.associatedCompany {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}

.shadow {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}

.supplierStaff {
  ::v-deep .el-form-item {
    width: 100%;
    margin-right: 0;
    padding-right: 10px;
  }
}

::v-deep .el-input__inner {
  color: #606266 !important;
  //background: #ffffff!important;
}

.verticalMiddle {
  display: flex;
  align-items: center
}

::v-deep .descriptionLabel {
  width: 145px;
  font-weight: bold;
  display: inline-block;
  text-align: right;
}

.upload p {
  line-height: 12px;
}

.required:before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

.supplier {
  .tableTitle {
    padding-left: 70px;
    font-size: 15px;
    font-weight: bold;
    margin: 20px 0;
  }

  .tablePadding {
    padding: 0 20px
  }

  .shadowPadding {
    padding: 5px 0;
    margin: 15px
  }

  .supplier-progress {
    height: 3px;
    width: 50%;
    background-color: #323232;
  }

  margin: 0 auto;
  width: 1000px;
  background: #c1c1c1;
  padding: 20px;

  .active {
    background-color: #4996b8;
    color: #fff
  }

  &-header {
    font-size: 16px;
    width: 100%;
    padding-bottom: 25px;
    margin-bottom: 20px;

    &-right {
      text-align: right;
      font-size: 12px;
      color: #173b75;
      margin-right: 20px
    }
  ;

    &-btn {
      cursor: pointer;
      padding: 10px 15px;
      display: inline-block;
      background-color: #cecece;
      color: #fff
    }
  ;
  }

  .smallInput {
    width: 188px;
  }

  .smallSelect {
    width: 188px;
  }

  .bigBaseInput {
    width: 533px
  }

  .bigInput {
    width: 591px
  }

  .colCenter {
    text-align: center;
  }

  .form-title {
    border-left: 6px solid #376092;
    margin: 10px 0;
    padding: 10px 30px;
    font-size: 16px;
    font-weight: bold;
    background-color: #f1f1f1;
  }

  // 上传输入框的文件列表样式调整#文字加粗
  ::v-deep .el-upload-list__item-name {
    font-weight: 600;
  }

  .centerRow {
    ::v-deep .el-form-item__content {
      width: calc(100% - 139px);
      text-align: center;
    }
  }

  .ellipsis-line {
    width: 248px;
    overflow: hidden;
    text-overflow: ellipsis; //文本溢出显示省略号
    white-space: nowrap; //文本不会换行
  }

  .shadow {
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }

  .rawMaterialFlex {
    flex: 0 1 15%;
    text-align: center;
  }

  .customItem {
    flex: 0 1 25%;
    text-align: center;
  }

  .form-main {
    .product {
      display: flex;
      min-height: 150px;

      .product-left {
        flex: 0 1 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 5px 10px;

        .product-title {
          flex: none;
          font-weight: bold;
          text-align: center;
          word-break: break-all;
          font-size: 20px;
          margin: 10px 0;
        }

        .product-detail {
          word-break: break-all;
          flex: auto;
          text-align: center;
        }
      }

      .product-right {
        justify-content: center;
        align-items: center;
        flex: 0 1 50%;
        border-left: 1px #dcdcdc solid;
        display: flex;
        flex-wrap: wrap;
      }
    }

  }

  .fixedBottom {
    position: fixed;
    width: calc(100% - 231px);
    bottom: 20px;
    display: flex;
    justify-content: center;
    margin-top: 40px;
    right: 30px;
  }
}
</style>
