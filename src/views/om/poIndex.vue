<template>
  <div class="app-container">
    <div style="margin-top: 25px;">
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.search"
          style="flex: 0 1 35%"
          :placeholder="$t('PO NO#/Supplier')"
          @keyup.enter.native="getListInit"
        />
        <el-button plain type="primary" @click="getListInit">
          {{ $t('common.search') }}
        </el-button>
        <el-button
          style="margin-left: 0"
          @click="handleReset"
        >
          {{ $t('common.reset') }}
        </el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>
      </div>

      <el-form v-show="showSearch" inline label-width="120px">

        <el-form-item class="searchItem" :label="$t('SO/PO NO#')" prop="no">
          <el-autocomplete
            v-model="queryParams.poNo"
            :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'so')})"
            :popper-append-to-body="false"
            popper-class="el-autocomplete-suggestion"
            class="searchItem"
            @select="((item) => {handleSelect(item, 'so')})"
            @keyup.enter.native="getListInit"
          />
        </el-form-item>

        <el-form-item class="searchItem" :label="$t('Pay. Term')" prop="">
          <el-select v-model="queryParams.payTerm" class="searchValue" clearable filterable multiple>
            <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Del. Term')" prop="">
          <el-select v-model="queryParams.delTerm" class="searchValue" clearable multiple>
            <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Supplier')" prop="">
          <el-autocomplete
            v-model="queryParams.supplier"
            :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'supplier')})"
            :placeholder="$t('Supplier')"
            :popper-append-to-body="false"
            popper-class="el-autocomplete-suggestion"
            class="overList"
            @select="((item) => {handleSelect(item, 'supplier')})"
            @keyup.enter.native="getListInit"
          />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Po Status')" prop="">
          <el-select v-model="queryParams.poStatus" class="searchValue" clearable filterable multiple>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SO_MASTER_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Part No.')" prop="">
          <el-input v-model="queryParams.partNo" :placeholder="$t('Part No.、Description')" @keyup.enter.native="getListInit" />

        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.timeType')" prop="dateType">
          <el-select v-model="queryParams.dateType" class="searchValue" clearable>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SEARCH_TIME_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label=" " class="searchItem">
          <el-date-picker
            v-model="queryParams.time"
            class="searchValue"
            value-format="yyyy-MM-dd"
            type="daterange"
            :range-separator="$t('order.to')"
            :start-placeholder="$t('common.startDate')"
            :end-placeholder="$t('common.endDate')"
          />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Customer')" prop="customer" style="width: 66%">
          <el-radio-group v-model="queryParams.customer" @change="getListInit();">
            <el-radio :label="null">{{ $t('order.whole') }}</el-radio>
            <el-radio v-for="customer in $store.getters.incapCustomers" :key="customer" :label="customer">
              {{ customer }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <vxe-grid
        ref="poOverview"
        :data="list"
        :loading="loading"
        v-bind="poOverviewGrid"
        @sort-change="sortMethod"
      >

        <template #dict="{column,row}">
          <dict-tag :type="column.params.dict" :value="row[column.field]" />
        </template>
        <template #poNo="{column,row}">
          <copy-button type="text" @click="$router.push(`/om/poInfo/${row.id}?no=${row.poNo}&viewOnly=true`)">{{ row.poNo }}</copy-button>
        </template>
        <template #soNo="{column,row}">
          <copy-button type="text" @click="$router.push(`/om/soInfo/${row.soId}?no=${row.soNo}&viewOnly=true`)">
            {{ row.soNo }}
          </copy-button>
        </template>
        <template #numOfParts="{row}">
              <span v-if="row.numOfParts">
                {{ row.numOfParts?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #numOfLines="{row}">
              <span v-if="row.numOfLines">
                {{ row.numOfLines?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #totalPoAmount="{row}">
              <span v-if="row.totalPoAmount">
                {{ row.totalPoAmount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #operate="{row}">
          <OperateDropDown
            :menu-item="[
              {
                name: $t('common.edit'),
                show: (row.poStatus==='ongoing'||row.poStatus==='new')&&$store.getters.permissions.includes('om:po-master:update'),
                action: (row) => $router.push(`/om/poInfo/${row.id}?no=${row.poNo}`),
                para: row
              },
/*               {
                name: $t('Release Order'),
                show: $store.getters.permissions.includes('om:po-master:release-po'),
                action: (row) => releaseOne(row),
                para: row
              }, */
              {
                name: $t('PDF'),
                show: row.poStatus === 'ongoing'&&$store.getters.permissions.includes('om:po-master:query'),
                action: (row) => downloadPdf(row),
                para: row
              },
              {
                name: $t('om.ChangeLog'),
                show: $store.getters.permissions.includes('om:po-master:query'),
                action: (row) => showLogs(row.id,'om_po_master',['poNo','poDate','supplier','paymentTerm','delTerm','shipTo','remark','poStatus'],['poNo']),
                para: row
              },

            ]"
          />
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="24" style="width: 100%" class="mb8">

            <el-col :span="22" style="display: flex">
<!--              <el-button v-hasPermi="['om:po-master:release-po']" size="mini" type="primary" icon="el-icon-position" @click="releaseRow">{{ $t('Release Order') }}</el-button>-->
              <el-button v-hasPermi="['om:po-master:cancel-po']" size="mini" plain type="danger" icon="el-icon-close" @click="batchRevoke">{{ $t('rfq.revoke') }}</el-button>
              <el-button v-hasPermi="['om:po-master:query']" size="mini" icon="el-icon-download" plain type="primary" @click="exportExcel">{{ $t('order.download') }}</el-button>
            </el-col>
            <el-col :span="2">
            <right-toolbar
              :list-id="poOverviewGrid.id"
              :show-search.sync="showSearch"
              :custom-columns.sync="poOverviewGrid.columns"
              @queryTable="getListInit"
            />
            </el-col>
          </el-row>
        </template>

      </vxe-grid>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getList()"
      />
    </div>
    <!--操作记录-->
    <el-dialog
        v-if="log.open"
        :close-on-click-modal="false"
        :title="$t('common.operationRecord')"
        :visible.sync="log.open"
        width="1000px"
    >
      <operation-record
          :business-id="log.businessId"
          :columns="log.columns"
          :log-visible.sync="log.open"
      />
    </el-dialog>
  </div>

</template>
<script>
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { getSearchResult } from '@/api/om/orderTracker'
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE } from '@/utils/dict'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import { zipByIds } from '@/api/infra/file'
import { cancelPo, exportPdf,exportPoMaster, getPoMasterPage, releasePo } from '@/api/om/po'
import operationRecord from '@/components/OperationRecord/operationRecord.vue'
import {Properties as response} from "svg-sprite-loader/examples/custom-runtime-generator/build/main";
export default {
  name: 'Poindex',
  components: { operationRecord,BatchUpload, OperateDropDown },
  data() {
    return {
      queryParams: {
        search: '',
        poNo: '',
        payTerm: [
        ],
        delTerm: [
        ],
        supplier: '',
        poStatus: [
        ],
        partNo: '',
        dateType: '',
        beginDate: '',
        endDate: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: '',
        customer:''
      },
      total: 0,
      list: [],
      showSearch: false,
      loading: false,
      poOverviewGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'poOverview',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
          {
            field: 'operate',
            slots: { default: 'operate' }, fixed: 'left',
            title: this.$t('common.operate'), visible: true, width: 35, showOverflow: false
          },
          { title: 'PO Date', field: 'poDate', visible: true, width: 100, fixed: 'left', sortable: true  },
          { title: 'PO NO#', field: 'poNo', slots: { default: 'poNo' }, visible: true, width: 100, fixed: 'left' },
          { title: 'SO NO#', field: 'soNo', visible: true, width: 100, fixed: 'left' , slots: { default: 'soNo' }},
          { title: 'Supplier', field: 'supplier', visible: true, width: 100 },
          { title: 'PO Status', field: 'poStatus', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SO_MASTER_STATUS }, visible: true, width: 100 },
          { title: '# of parts', field: 'numOfParts', visible: true, width: 100,slots: {default:'numOfParts'},align: 'right' },
          { title: '# of PO lines', field: 'numOfLines', visible: true, width: 100, slots:{default:'numOfLines'},align: 'right' },
          { title: 'Pur. Total Amount', field: 'totalPoAmount', visible: true, width: 100,slots:{default:'totalPoAmount'} ,align: 'right'},
          { title: 'Supplier Currency', field: 'currency', slots: { default: 'dict' }, params: { dict: DICT_TYPE.COMMON_CURRENCY }, visible: true, width: 100 },
          { title: 'PO Req Del Date F', field: 'reqDelDateF', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: 'PO Req Del Date T', field: 'reqDelDateT', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: 'Supplier Con. Del. Date F', field: 'conDelDateF', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: 'Supplier Con. Del. Date T', field: 'conDelDateT', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: 'Ship Date F', field: 'shipDateF', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: 'Ship Date T', field: 'shipDateT', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: 'Pay. Term', field: 'paymentTerm', slots: { default: 'dict' }, params: { dict: DICT_TYPE.SUPPLIER_PAYMENT_DAYS }, visible: true, width: 100 },
          { title: 'Remark', field: 'remark', visible: true, width: 100 }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      // 操作日志
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: []
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // show operate log
    showLogs(businessId, table, column,keyColumns) {
      this.log.open = true
      this.log.businessId = businessId
      this.log.columns = [{
        tableName: table,
        searchColumns: column,
        keyColumns:keyColumns
      }]
    },
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      getPoMasterPage(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.getList()
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    handleSelect(item, type) {
      switch (type) {
        case 'so':
          this.queryParams.no = item.value
          break
        case 'customer':
          this.queryParams.customer = item.value
          break
        case 'buyer':
          this.queryParams.buyer = item.value
          break
        case 'supplier':
          this.queryParams.supplier = item.value
          break
        case 'mfg':
          this.queryParams.mfg = item.value
          break
        // eslint-disable-next-line no-fallthrough
        default:
          break
      }
    },
    querySearchAsync(queryString, cb, type) {
      if (queryString.length < 2) {
        return
      }
      getSearchResult({
        searchValue: queryString,
        type: type
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item
          }
        }))
      })
    },
    handleReset() {
      this.queryParams = {
        search: '',
        soNo: '',
        payTerm: [],
        soStatus: [],
        supplier: '',
        buyer: '',
        delTerm: [],
        partNo: '',
        dateType: '',
        beginDate: '',
        endDate: '',
        customer: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      }
      this.getList()
    },
    editRow(row, str1) {

    },
    batchRevoke() {
      const selectRows = this.$refs.poOverview.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to modify.'))
        return false
      } else {
        this.$modal.confirm('Do you confirm the cancellation of the selected PO orders?').then(() => {
          return cancelPo(selectRows.map(row => row.id).join(','))
        }).then(res => {
          this.$message.success('Operation successful.')
          this.getList()
        })
      }
    },
    downloadRow(row, str1) {
      if (row.fileIds.length) {
        zipByIds({
          files: row.fileIds,
          zipFileName: 'SO file'
        }).then(res => {
          this.$download.zip(res, 'SO file.zip')
        })
      } else {
        this.$message.error('No files available for download')
      }
    },
    exportExcel() {
      exportPoMaster(this.queryParams).then(res => {
        this.$download.excel(res, 'po file.xlsx')
      })
    },
    releaseOne(row) {
      this.$modal.confirm('Do you confirm the release of PO order "' + row.poNo + '"?').then(() => {
        return releasePo({ poIds: [row.id] })
      }).then(res => {
        this.$message.success('Operation successful.')
        this.getList()
      })
    },
    releaseRow() {
      const selectRows = this.$refs.poOverview.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to modify.'))
        return false
      } else {
        this.$modal.confirm('Do you confirm the release of the selected PO orders?').then(() => {
          return releasePo({ poIds: selectRows.map(row => row.id) })
        }).then(res => {
          this.$message.success('Operation successful.')
          this.getList()
        })
      }
    },
    //download pdf
    downloadPdf(row){
      exportPdf(row.id).then(res=>{
        this.$download.pdf(res, row.poNo+".pdf")
      })
    }
  }
}
</script>
<style scoped lang="scss">

.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }

  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}

.searchValue {
  width: 100%;
}

</style>
