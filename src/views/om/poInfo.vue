<template>
  <div class="app-container">
    <common-card :title="$t('PO Basic Information')">

      <el-form ref="orderForm" :model="orderData" inline label-width="150px">
        <el-form-item
          :label="$t('Supplier')"
          class="commonFormItem"
          prop="supplier"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.supplier">
            <el-input v-model="orderData.supplier" :disabled="true" />
          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('Pur. Total Amount')"
          class="commonFormItem"
          prop="totalPoAmount"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.totalPoAmount">
            <el-input v-model="orderData.totalPoAmount" :disabled="true" />
          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('Pay.Term')"
          class="commonFormItem"
          prop="paymentTerm"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.paymentTerm" :dict="DICT_TYPE.SUPPLIER_PAYMENT_DAYS">
            <el-select v-model="orderData.paymentTerm" class="searchValue" :disabled="!editMode" clearable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>

          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('Del.Term')"
          class="commonFormItem"
          prop="delTerm"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.delTerm" :dict="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION">
            <el-select v-model="orderData.delTerm" class="searchValue" :disabled="!editMode" clearable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('Ship To')"
          class="commonFormItem"
          prop="shipTo"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.shipTo" :dict="DICT_TYPE.OM_SHIP_TO">
            <el-select v-model="orderData.shipTo" class="searchValue" clearable filterable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SHIP_TO)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('Supplier Currency')"
          class="commonFormItem"
          prop="shipTo"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.shipTo" :dict="DICT_TYPE.COMMON_CURRENCY">
            <el-select v-model="orderData.currency" class="searchValue" clearable filterable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.COMMON_CURRENCY)" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('PO NO#')"
          class="commonFormItem"
          prop="soNo"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.poNo">
            <el-input v-model="orderData.poNo" :disabled="true" />
          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('PO Date')"
          class="commonFormItem"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.poDate" type="Date">
            <el-date-picker
              v-model="orderData.poDate"
              class="searchValue"
              :disabled="!editMode"
              :placeholder="$t('order.selectDate')"
              type="date"
              value-format="yyyy-MM-dd"
            />
          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('PO Status')"
          class="commonFormItem"
          style="width: 100%"
          prop="Status"
        >
          <dict-tag :value="orderData.poStatus" :type="DICT_TYPE.OM_SO_MASTER_STATUS" />
        </el-form-item>
        <el-form-item
          :label="$t('Remark')"
          class="commonFormItem"
          style="width: 100%"
          prop="remark"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.remark">
            <el-input v-model="orderData.remark" type="textarea" :rows="3" autosize :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('auth.uploadAttachments')"
          class="commonFormItem"
        >
          <div style="display: flex;justify-content: space-between">

            <el-upload
              class="upload-demo"
              :disabled="!editMode"
              :action="uploadUrl"
              :headers="getBaseHeader()"
              :on-remove="onRemove"
              :on-preview="onPreview"
              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
              multiple
              :limit="5"
              :show-file-list="false"
              :file-list="fileList"
            >
              <el-button
                v-if="editMode"
                v-hasPermi="['om:po-master:update-po']"
                :disabled="!editMode"
                class="uploadBtn"
                size="small"
                plain
                icon="el-icon-plus"
                type="primary"
              />
            </el-upload>
            <div>
              {{ $t('scar.viewAttachments') }}
              <el-button
                class="uploadBtn"
                size="small"
                style="padding: 5px 9px"
                :disabled="fileList.length===0"
                plain
                :type="fileList.length?'primary':''"
                @click="showFile=true"
              >
                {{ fileList.length }}
              </el-button>

              <el-dialog
                v-if="showFile"
                :visible.sync="showFile"
                :title="$t('scar.viewAttachments')"
                width="400px"
              >
                <el-upload
                  class="upload-demo"
                  :disabled="!editMode"
                  :action="uploadUrl"
                  :headers="getBaseHeader()"
                  :on-remove="onRemove"
                  :on-preview="onPreview"
                  :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                  multiple
                  :limit="5"
                  :show-file-list="true"
                  :file-list="fileList"
                />
                <div slot="footer">
                  <el-button type="primary" @click="showFile=false">{{ $t('order.close') }}</el-button>
                </div>

              </el-dialog>
            </div>
          </div>

        </el-form-item>
        <div v-if="editMode" style="width: 100%;display:flex;justify-content: center">
          <el-button v-hasPermi="['om:po-master:update-po']" icon="el-icon-edit-outline" type="primary" @click="submitForm">{{ $t('Update') }}</el-button>
        </div>

      </el-form>

    </common-card>
    <common-card
      :title="$t('PO Item List')"
    >
      <div style="margin-top: 25px;">
        <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
          <el-input
            v-model="queryParams.searchKey"
            style="flex: 0 1 35%"
            :placeholder="$t('SO NO#/Part No.')"
            @keyup.enter.native="getListInit"
          />
          <el-button plain type="primary" @click="getListInit">
            {{ $t('common.search') }}
          </el-button>
          <el-button
            style="margin-left: 0"
            @click="handleReset"
          >
            {{ $t('common.reset') }}
          </el-button>
          <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
            <el-button type="text">
              {{ $t('common.advancedSearch') }}
            </el-button>
            <i
              :style="showSearch? '':{transform: 'rotate(180deg)'}"
              class="el-icon-arrow-up"
              style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
            />
          </div>
        </div>

        <el-form v-show="showSearch" inline label-width="120px">
          <el-form-item class="searchItem" :label="$t('Part No.')" prop="">
            <el-input v-model="queryParams.partNo" :placeholder="$t('Part No.、Description')" @keyup.enter.native="getListInit" />

          </el-form-item>

          <el-form-item class="searchItem" :label="$t('MFG')" prop="">
            <el-input v-model="queryParams.mfg" :placeholder="$t('Please enter content')" @keyup.enter.native="getListInit" />

          </el-form-item>
          <el-form-item class="searchItem" :label="$t('MPN')" prop="">
            <el-input v-model="queryParams.mpn" :placeholder="$t('Please enter content')" @keyup.enter.native="getListInit" />

          </el-form-item>
          <el-form-item class="searchItem" :label="$t('Description')" prop="">
            <el-input v-model="queryParams.description" :placeholder="$t('Please enter content')" @keyup.enter.native="getListInit" />

          </el-form-item>

        </el-form>
        <vxe-grid
          ref="poInfo"
          :data="list"
          :loading="loading"
          v-bind="poInfoGrid"
        >
          <template #partNo="{column,row}">
            <copy-button type="text">{{ row.partNo }}</copy-button>
          </template>

          <template #dict="{column,row}">
            <dict-tag :type="column.params.dict" :value="row[column.field]" />
          </template>
          <template #unitPrice="{row}">
              <span v-if="row.unitPrice">
                {{ row.unitPrice?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #orderQuantity="{row}">
              <span v-if="row.orderQuantity">
                {{ row.orderQuantity?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #amount="{row}">
              <span v-if="row.amount">
                {{ row.amount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #operate="{row}">
            <OperateDropDown
              :menu-item="[
                {
                  name: $t('common.edit'),
                  show: editMode&&$store.getters.permissions.includes('om:po-detail:update'),
                  action: (row) => modifyPoDetail(row),
                  para: row
                },
                {
                  name: $t('om.ChangeLog'),
                  show: $store.getters.permissions.includes('om:po-master:detail'),
                  action: (row) => showLogs(row.id,'om_po_detail',['partNo','description','mfg','mpn','version','unitPrice','orderQuantity','uom','pricePer','reqDelDate','leadTime','remark'],['partNo']),
                  para: row
                },
              ]"
            />
          </template>
          <template #toolbar_buttons>
            <el-row :gutter="24" style="width: 100%" class="mb8">
              <el-col :span="22" style="display: flex">
                <el-button v-if="editMode&&orderData.poStatus==='new'" v-hasPermi="['om:po-master:release-single-po']" icon="el-icon-position" size="mini" type="primary" :loading="releaseLoading" @click="getReleasePoMail('ReleaseOrder')">{{ $t('Release Order') }}</el-button>
                <el-button v-if="editMode&&orderData.poStatus==='ongoing'" v-hasPermi="['om:po-master:release-single-po']" icon="el-icon-position" size="mini" type="primary" :loading="releaseLoading" @click="getReleasePoMail('ResendMail')">{{ $t('Resend Mail') }}</el-button>
                <el-button v-hasPermi="['om:po-master:detail']" :loading="exportLoading" size="mini" plain icon="el-icon-download" type="primary" @click="exportExcel">{{ $t('order.download') }}</el-button>
              </el-col>
              <el-col :span="2">
              <right-toolbar
                :list-id="poInfoGrid.id"
                :show-search.sync="showSearch"
                :custom-columns.sync="poInfoGrid.columns"
                @queryTable="getListInit"
              />
              </el-col>
            </el-row>
          </template>
        </vxe-grid>
        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNo"
          :total="total"
          @pagination="getList()"
        />
      </div>
    </common-card>
    <!--修改物料-->
    <el-dialog
      title="Parts Modification"
      width="1000px"
      :visible.sync="poEditShow"
    >
      <PoDetail ref="poDetail" v-if="poEditShow" :po-id="poEdit.poId" @freshList="getList();poEditShow=false;" />
      <div slot="footer">
        <el-button @click="poEditShow =false">Cancel</el-button>
        <el-button type="primary" @click="modifyPoDetailAction">Confirm</el-button>
      </div>
    </el-dialog>
    <!--操作记录-->
    <el-dialog
        v-if="log.open"
        :close-on-click-modal="false"
        :title="$t('common.operationRecord')"
        :visible.sync="log.open"
        width="1000px"
    >
      <operation-record
          :business-id="log.businessId"
          :columns="log.columns"
          :log-visible.sync="log.open"
      />
    </el-dialog>
    <!--释放订单时候的邮箱确认-->
    <el-dialog :title="$t('Release Mail Info')" :visible.sync="releaseMailVisible" append-to-body width="360px">
      <div>
        <el-form ref="releaseMailForm" :model="releaseMailDate" :rules="rules" label-width="60px">
          <!-- 收件人 -->
          <el-form-item :label="$t('Mail')" prop="toMail">
            <el-input v-model="releaseMailDate.toMail" :placeholder="$t('Please enter the mail address')" style="width: 220px" />
          </el-form-item>

          <!-- CC -->
          <el-form-item :label="$t('CC')" prop="ccMail">
            <el-input v-model="releaseMailDate.ccMail" :placeholder="$t('Please enter the cc address')" style="width: 220px" />
          </el-form-item>
          <!-- 提交按钮靠右对齐，并且与表单有一段距离 -->
          <div class="button-container">
            <el-button @click="releaseMailVisible = false">{{ $t('common.cancel') }}</el-button>
            <el-button type="primary" :loading="releaseMailLoad" @click="releaseRow()">{{ $t('Submit') }}</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import PoDetail from '@/views/om/poDetail.vue'
import { parseTime } from '@/utils/ruoyi'
import {
  cancelSoDetail
} from '@/api/om/so.js'
import { getBaseHeader } from '@/utils/request'
import { exportPoDetailExcel, getPoDetailPage, getPoMaster, getReleasePoMail, savePoMaster } from '@/api/om/po.js'
import { releasePo } from '@/api/om/po'
import { getSearchResult } from '@/api/om/orderTracker'
import operationRecord from '@/components/OperationRecord/operationRecord.vue'

export default {
  name: 'Poinfo/:id',
  components: { OperateDropDown, BatchUpload, ShowOrEdit, PoDetail,operationRecord },
  data() {
    return {
      getBaseHeader,
      remark: '',
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      showFile: false,
      fileList: [],
      orderData: {
        id: null,
        fileIds: [
        ],
        poId: '',
        poNo: '',
        poDate: '',
        supplier: '',
        numOfParts: null,
        numOfLines: null,
        totalPoAmount: null,
        currency: null,
        paymentTerm: '',
        delTerm: '',
        shipTo: '',
        remark: '',
        poStatus: ''
      },
      orderRules: {
        // Define your validation rules here
      },
      editMode: true,
      queryParams: {
        searchKey: '',
        partNo: '',
        description: '',
        mfg: '',
        mpn: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: ''
      },
      total: 0,
      list: [],
      showSearch: false,
      loading: false,
      poInfoGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'poInfo',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          {
            field: 'operate',
            slots: { default: 'operate' }, fixed: 'left',
            title: this.$t('common.operate'), visible: true, width: 35, showOverflow: false
          },
          { title: 'Part No.', field: 'partNo',slots:{default:'partNo'},  visible: true, width: 100, fixed: 'left' },
          { title: 'Line No', field: 'poLine', visible: true, width: 100,align: 'right' },
          { title: 'Description', field: 'description', visible: true, width: 150 },
          { title: 'MFG', field: 'mfg', visible: true, width: 100 },
          { title: 'MPN', field: 'mpn', visible: true, width: 100 },
          { title: 'Version', field: 'version', visible: true, width: 100 },
          { title: 'Pur. U/P', field: 'unitPrice', visible: true, width: 100, slots: { default: 'unitPrice'},align: 'right' },
          { title: 'Order Quantity', field: 'orderQuantity', visible: true, width: 100, slots: { default: 'orderQuantity'},align: 'right' },
          { title: 'UOM', field: 'uom', visible: true, width: 100 },
          { title: 'Pur. Amount', field: 'amount', visible: true, width: 100, slots: { default: 'amount'} ,align: 'right'},
          { title: 'PO Req. Del.Date', field: 'reqDelDate', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: 'L/T (day)', field: 'leadTime', visible: true, width: 100 },
          { title: 'Remark', field: 'remark', visible: true, width: 150 }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      exportLoading: false,
      releaseLoading: false,
      obligatoryDialog: false,
      poEdit: {
        poId: '0'
      },
      poEditShow:false,
      // 操作日志
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: []
      },
      releaseMailVisible:false,
      releaseMailLoad:false,
      //Release Mail Info
      releaseMailDate:{
        toMail:'',
        ccMail:'',
        type:''
      },
      // 表单校验
      rules: {
        toMail: [{ required: true, message: 'Mail must required', trigger: 'blur' }],
        ccMail: [{ required: true, message: 'CC must required', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.editMode = this.$store.getters.permissions.includes('om:po-master:update')
    this.init()
    this.getList()
  },
  methods: {
    init() {
      getPoMaster(this.$route.params.id).then(res => {
        res.data.files?.forEach(a => {
          a.name = a.fileName
        })
        this.fileList = res.data?.files || []
        this.orderData = res.data
        //结束的订单不允许编辑
        if(this.orderData.poStatus==='closed'||this.orderData.soStatus==='cancel'){
          this.editMode=false
        }
      })
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      getPoDetailPage({
        ...this.queryParams,
        poId: this.$route.params.id
      }).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    viewAttachments() {
      // Logic to view attachments
    },
    submitForm() {
      savePoMaster({ ...this.orderData,
        fileIds: this.fileList.map(a => a.fileId)
      }).then(res => {
        this.$message.success('Operation successful')
      })
    },
    onRemove(file, fileList) {
      const index = this.fileList.indexOf(file.response?.data?.fileId) || this.fileList.indexOf(file.fileId)
      this.fileList.splice(index, 1)
    },
    onSuccess(response, file, fileList) {
      this.fileList.push({
        name: file.name,
        fileUrl: response.data.url,
        fileId: response.data.id
      })
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.fileUrl) {
        window.open(file.fileUrl)
      }
    },
    handleReset() {
      this.queryParams = {
        searchKey: '',
        partNo: '',
        mfg: '',
        mpn: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      }
      this.getList()
    },
    delRow(row) {
      cancelSoDetail().then(res => {
        this.$message.success('Operation successful')
        this.getList()
      })
    },
    batchRevoke() {
      const selectRows = this.$refs.poInfo.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to modify'))
        return false
      } else {
        cancelSoDetail({
          poId: this.$route.params.id,
          soDetailIds: selectRows.map(row => row.soDetailId)
        }).then(res => {
          this.$message.success('Operation successful')
          this.getList()
        })
      }
    },
    exportExcel() {
      this.$modal.confirm(this.$t('Do you confirm the download?')).then(() => {
        this.exportLoading = true
        return exportPoDetailExcel({ ...this.queryParams,
          poId: this.$route.params.id
        })
      }).then(response => {
        this.$download.excel(response, this.orderData.poNo+ this.$t('_po info material.xlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    getReleasePoMail(type) {
      this.$confirm('Do you confirm the release？', 'Prompt', {
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        this.releaseLoading = true
        getReleasePoMail(this.orderData.id).then(res => {
          this.releaseMailVisible=true
          this.releaseMailDate=res.data
          this.releaseMailDate.type=type
        }).finally(() => {
          this.releaseLoading = false
        })
      }).catch(() => {
      })
    },
    releaseRow() {
      this.$refs['releaseMailForm'].validate(valid => {
        if (valid) {
          this.releaseLoading = true
          this.releaseMailLoad=true
          releasePo({
            poIds: [this.orderData.id],
            toMail: this.releaseMailDate.toMail,
            ccMail: this.releaseMailDate.ccMail,
            type: this.releaseMailDate.type
          }).then(res => {
            this.$message.success('Operation successful')
            this.releaseMailVisible = false
            this.init()
            this.getList()
          }).finally(() => {
            this.releaseLoading = false
            this.releaseMailLoad=false
          })
        }
      })
    },
    modifyPoDetail(row) {
      this.poEditShow = true
      this.poEdit.poId = row.id
    },
    modifyPoDetailAction() {
      //this.poEdit.show = false
      this.$refs.poDetail.savePoDetailInfo()
    },
    // show operate log
    showLogs(businessId, table, column,keyColumns) {
      this.log.open = true
      this.log.businessId = businessId
      this.log.columns = [{
        tableName: table,
        searchColumns: column,
        keyColumns:keyColumns
      }]
    }
  }
}
</script>

<style lang="scss" scoped>
.commonFormItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 150px);
  }
}
.poInfo{

}
.searchValue{
  width: 100%;
}
.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}
::v-deep .avpl-row{
  background:#81D3F8 !important;
}
::v-deep .avpl-edit{
  background:#FFE668 !important;
}
.button-container {
  margin-top: 20px; /* 设置按钮与表格的距离 */
  text-align: right; /* 使按钮靠右对齐 */
}
</style>
