<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item :label="$t('Ship To')" prop="shipTo">
        <el-select v-model="queryParams.shipTo" :placeholder="$t('common.pleaseSelect')" clearable size="small">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.OM_SHIP_TO)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="handleQuery"
        >{{ $t('common.search') }}</el-button>
        <el-button
          icon="el-icon-refresh"
          @click="resetQuery"
        >{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">

      <el-table-column :label="$t('Ship To')" align="center" prop="shipTo" />

      <el-table-column :label="$t('Company')" align="center" prop="company"/>

      <el-table-column :label="$t('Address')" align="center" prop="address"/>

      <el-table-column :label="$t('Email')" align="center" prop="email"/>

      <el-table-column :label="$t('Contact Person')" align="center" prop="contactPerson"/>

      <el-table-column :label="$t('Phone')" align="center" prop="phone"/>

      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item :label="$t('Ship To')" prop="shipTo">
          <el-select v-model="form.shipTo" disabled>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.OM_SHIP_TO)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('Company')" prop="company">
          <el-input v-model="form.company" clearable maxlength="500" />
        </el-form-item>
        <el-form-item :label="$t('Address')" prop="address">
          <el-input v-model="form.address" clearable maxlength="500" />
        </el-form-item>
        <el-form-item :label="$t('Email')" prop="email">
          <el-input v-model="form.email" clearable maxlength="200" />
        </el-form-item>
        <el-form-item :label="$t('Contact Person')" prop="contactPerson">
          <el-input v-model="form.contactPerson" clearable maxlength="200" />
        </el-form-item>
        <el-form-item :label="$t('Phone')" prop="phone">
          <el-input v-model="form.phone" clearable maxlength="500" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="cancel"
        >{{ $t('common.cancel') }}</el-button>
        <el-button
          type="primary"
          @click="submitForm"
        >{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { updateConfig, getConfigPage } from '@/api/om/config'

export default {
  name: 'Shipconfig',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单通用配置列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        shipTo: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        shipTo: [{ required: true, message: this.$t('system.configurationItemCannotBeEmpty'), trigger: 'change' }]
      },
      // 报表列表
      reportList: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      // 执行查询
      getConfigPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        shipTo: undefined,
        company: undefined,
        address: undefined,
        email: undefined,
        contactPerson: undefined,
        phone: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.form.id = row.id
      this.form.shipTo = row.shipTo
      this.form.company = row.company
      this.form.address = row.address
      this.form.email = row.email
      this.form.contactPerson = row.contactPerson
      this.form.phone = row.phone
      this.open = true
      this.title = this.$t('Ship To Address Config')
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        updateConfig(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          this.open = false
          this.getList()
        })
      })
    }
  }
}
</script>
