<template>
  <div class="app-container">
    <div style="margin-top: 25px;">
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.search"
          style="flex: 0 1 35%"
          :placeholder="$t('Shortage NO#、Part No.、Description')"
          @keyup.enter.native="getListInit"
        />
        <el-button plain type="primary" @click="getListInit">
          {{ $t('common.search') }}
        </el-button>
        <el-button
          style="margin-left: 0"
          @click="handleReset"
        >
          {{ $t('common.reset') }}
        </el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>
      </div>

      <el-form v-show="showSearch" inline label-width="120px">
        <el-form-item class="searchItem" :label="$t('Shortage NO#')" prop="">
          <el-input v-model="queryParams.no" :placeholder="$t('Shortage NO#')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Part No.')" prop="">
          <el-input v-model="queryParams.partNo" :placeholder="$t('Part No.、Description')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Shortage Status')" prop="">
          <el-select v-model="queryParams.shortStatus" class="searchValue" clearable filterable multiple>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SHORT_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Customer')" prop="customer" style="width: 66%">
          <el-radio-group v-model="queryParams.customer" @change="getListInit();">
            <el-radio :label="null">{{ $t('order.whole') }}</el-radio>
            <el-radio v-for="customer in $store.getters.incapCustomers" :key="customer" :label="customer">
              {{ customer }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <vxe-grid
        ref="shortageOverview"
        :data="list"
        :loading="loading"
        v-bind="shortageOverviewGrid"
      >
        <template #dict="{column,row}">
          <dict-tag :type="column.params.dict" :value="row[column.field]" />
        </template>
        <template #no="{column,row}">
          <copy-button type="text" @click="$router.push(`/om/shortInfo/${row.id}?no=${row.no}&viewOnly=true`)">{{ row.no }}</copy-button>
        </template>
        <template #numOfParts="{row}">
              <span v-if="row.numOfParts">
                {{ row.numOfParts?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #operate="{row}">
          <OperateDropDown
            :menu-item="[
              {
                name: $t('common.edit'),
                show: (row.status==='ongoing'||row.status==='New')&&$store.getters.permissions.includes('om:short-master:update'),
                action: (row) => $router.push(`/om/shortInfo/${row.id}?no=${row.no}`),
                para: row
              }
            ]"
          />
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="24" style="width: 100%" class="mb8">
            <el-col :span="22" style="display: flex">
              <BatchUpload
                  v-hasPermi="['om:short-master:upload']"
                  :btn-name="$t('Upload Shortage List')"
                  :template-download="async ()=>{ return exportOrderTemplate()}"
                  template-name="so template"
                  url="om/short-master/upload"
                  :after-upload="getList"
              />
            </el-col>
            <el-col :span="2">
            </el-col>
          </el-row>
        </template>
      </vxe-grid>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getList()"
      />
    </div>
  </div>

</template>
<script>
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE } from '@/utils/dict'
import { getShortMasterPage } from '@/api/om/short'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
export default {
  name: 'Shortindex',
  components: { BatchUpload,OperateDropDown },
  data() {
    return {
      queryParams: {
        search: '',
        shortStatus: [],
        no:'',
        partNo: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: '',
        customer:''
      },
      total: 0,
      list: [],
      showSearch: false,
      loading: false,
      shortageOverviewGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'shortageOverview',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { title: 'Shortage NO#', field: 'no', slots: { default: 'no' }, visible: true, width: '15%', fixed: 'left' },
          { title: this.$t('Customer'), field: 'customer', visible: true, width: '15%', fixed: 'left' },
          { title: '# of parts', field: 'numOfParts', visible: true, width: '15%',slots: {default:'numOfParts'},align: 'right' },
          { title: 'Shortage Status', field: 'status', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SHORT_STATUS }, visible: true, width: '15%' },
          { title: 'Remark', field: 'remarks', visible: true, width: '45%' },
          { title: this.$t('Create Date'), field: 'createTime', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: '10%' },
          {
            field: 'operate',
            slots: { default: 'operate' }, fixed: 'right',
            title: this.$t('common.operate'), visible: true, width: '120px', showOverflow: false
          }
        ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }

      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getShortMasterPage(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.queryParams = {
        search: '',
        shortStatus: [],
        no:'',
        partNo: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: '',
        customer:''
      }
      this.getList()
    }
  }
}
</script>
<style scoped lang="scss">

.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }

  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}

.searchValue {
  width: 100%;
}

</style>
