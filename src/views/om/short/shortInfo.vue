<template>
  <div class="app-container">
    <common-card :title="$t('Shortage Basic Information')">

      <el-form ref="orderForm" :model="orderData" inline label-width="150px">
        <el-form-item
          :label="$t('Shortage NO#')"
          class="commonFormItem"
          prop="no"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.no">
            {{orderData.no}}
          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('Create Date')"
          class="commonFormItem"
          prop="soDate"
        >
          {{ orderData.createTim?dayjs(orderData.createTim).format('YYYY-MM-DD'):'' }}
        </el-form-item>
        <el-form-item
          :label="$t('Shortage Status')"
          class="commonFormItem"
          style="width: 100%"
          prop="Status"
        >
          <dict-tag :value="orderData.status" :type="DICT_TYPE.OM_SHORT_STATUS" />
        </el-form-item>
        <el-form-item
          :label="$t('Remark')"
          class="commonFormItem"
          style="width: 100%"
          prop="remark"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.remarks">
            <el-input v-model="orderData.remarks" type="textarea" :rows="3" autosize :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('auth.uploadAttachments')"
          class="commonFormItem"
        >
          <div style="display: flex;justify-content: space-between">

            <el-upload
              class="upload-demo"
              :disabled="!editMode"
              :action="uploadUrl"
              :headers="getBaseHeader()"
              :on-remove="onRemove"
              :on-preview="onPreview"
              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
              multiple
              :limit="5"
              :show-file-list="false"
              :file-list="fileList"
            >
              <el-button
                v-if="editMode"
                v-hasPermi="['om:short-master:update']"
                :disabled="!editMode"
                class="uploadBtn"
                size="small"
                plain
                icon="el-icon-plus"
                type="primary"
              />
            </el-upload>
            <div>
              {{ $t('scar.viewAttachments') }}
              <el-button
                class="uploadBtn"
                size="small"
                style="padding: 5px 9px"
                :disabled="fileList.length===0"
                plain
                :type="fileList.length?'primary':''"
                @click="showFile=true"
              >
                {{ fileList.length }}
              </el-button>

              <el-dialog
                v-if="showFile"
                :visible.sync="showFile"
                :title="$t('scar.viewAttachments')"
                width="400px"
              >
                <el-upload
                  class="upload-demo"
                  :disabled="!editMode"
                  :action="uploadUrl"
                  :headers="getBaseHeader()"
                  :on-remove="onRemove"
                  :on-preview="onPreview"
                  :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                  multiple
                  :limit="5"
                  :show-file-list="true"
                  :file-list="fileList"
                />
                <div slot="footer">
                  <el-button type="primary" @click="showFile=false">{{ $t('order.close') }}</el-button>
                </div>

              </el-dialog>
            </div>
          </div>

        </el-form-item>
        <div v-if="editMode" style="width: 100%;display:flex;justify-content: center">
          <el-button v-hasPermi="['om:short-master:update']" icon="el-icon-edit-outline" type="primary" @click="submitForm">{{ $t('Update') }}</el-button>
        </div>

      </el-form>

    </common-card>
    <common-card
      :title="$t('Shortage List')"
    >
      <div style="margin-top: 25px;">
        <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
          <el-input
            v-model="queryParams.search"
            style="flex: 0 1 35%"
            :placeholder="$t('Part No.、Supplier、PO NO#')"
            @keyup.enter.native="getListInit"
          />
          <el-button plain type="primary" @click="getListInit">
            {{ $t('common.search') }}
          </el-button>
          <el-button
            style="margin-left: 0"
            @click="handleReset"
          >
            {{ $t('common.reset') }}
          </el-button>
          <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
            <el-button type="text">
              {{ $t('common.advancedSearch') }}
            </el-button>
            <i
              :style="showSearch? '':{transform: 'rotate(180deg)'}"
              class="el-icon-arrow-up"
              style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
            />
          </div>
        </div>

        <el-form v-show="showSearch" inline label-width="120px">
          <el-form-item class="searchItem" :label="$t('Part No.')" prop="">
            <el-input v-model="queryParams.partNo" :placeholder="$t('Part No.、Description')" @keyup.enter.native="getListInit" />

          </el-form-item>

          <el-form-item class="searchItem" :label="$t('PO NO#')" prop="">
            <el-input v-model="queryParams.poNo" :placeholder="$t('PO NO#')" @keyup.enter.native="getListInit" />

          </el-form-item>
          <el-form-item class="searchItem" :label="$t('Supplier')" prop="">
            <el-input v-model="queryParams.supplier" :placeholder="$t('Supplier')" @keyup.enter.native="getListInit" />
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('New Status')" prop="">
            <el-select v-model="queryParams.newShipStatus" class="searchValue" clearable filterable multiple>
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SO_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('Status')" prop="">
            <el-select v-model="queryParams.shipStatus" class="searchValue" clearable filterable multiple>
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SO_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('Gap')" prop="">
            <el-select v-model="queryParams.gap" class="searchValue" clearable filterable multiple>
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SHORT_GAP)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('ESIC OPEN PO QTY - Demand')" prop="">
            <el-select v-model="queryParams.esicOpenPoDemandGap" class="searchValue" clearable filterable multiple>
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SHORT_GAP)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('Execution Status')" prop="">
            <el-select v-model="queryParams.status" class="searchValue" clearable filterable multiple>
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SHORT_PART_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
        <vxe-grid
          ref="shortInfo"
          :data="list"
          :loading="loading"
          v-bind="shortInfoGrid"
        >

          <template #dict="{column,row}">
            <dict-tag :type="column.params.dict" :value="row[column.field]" />
          </template>
          <template #qty="{column,row}">
              <span v-if="row[column.field]">
                {{ row[column.field]?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>

          <template #operate="{row}">
            <OperateDropDown
              :menu-item="[
                {
                  name: $t('common.edit'),
                  show: editMode&&$store.getters.permissions.includes('om:po-detail:update'),
                  action: (row) => modifyPoDetail(row),
                  para: row
                },
                {
                  name: $t('om.ChangeLog'),
                  show: $store.getters.permissions.includes('om:po-master:detail'),
                  action: (row) => showLogs(row.id,'om_po_detail',['partNo','description','mfg','mpn','version','unitPrice','orderQuantity','uom','pricePer','reqDelDate','leadTime','remark'],['partNo']),
                  para: row
                },
              ]"
            />
          </template>
          <template #toolbar_buttons>
            <el-row :gutter="24" style="width: 100%" class="mb8">
              <el-col :span="22" style="display: flex">
                <el-button v-if="editMode&&orderData.poStatus==='new'" v-hasPermi="['om:po-master:release-single-po']" icon="el-icon-position" size="mini" type="primary" :loading="releaseLoading" @click="getReleasePoMail('ReleaseOrder')">{{ $t('Release Order') }}</el-button>
                <el-button v-if="editMode&&orderData.poStatus==='ongoing'" v-hasPermi="['om:po-master:release-single-po']" icon="el-icon-position" size="mini" type="primary" :loading="releaseLoading" @click="getReleasePoMail('ResendMail')">{{ $t('Resend Mail') }}</el-button>
                <el-button v-hasPermi="['om:po-master:detail']" :loading="exportLoading" size="mini" plain icon="el-icon-download" type="primary" @click="exportExcel">{{ $t('order.download') }}</el-button>
              </el-col>
              <el-col :span="2">
              <right-toolbar
                :list-id="shortInfoGrid.id"
                :show-search.sync="showSearch"
                :custom-columns.sync="shortInfoGrid.columns"
                @queryTable="getListInit"
              />
              </el-col>
            </el-row>
          </template>
        </vxe-grid>
        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNo"
          :total="total"
          @pagination="getList()"
        />
      </div>
    </common-card>
  </div>
</template>

<script>
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import PoDetail from '@/views/om/poDetail.vue'
import { parseTime } from '@/utils/ruoyi'
import {
  cancelSoDetail
} from '@/api/om/so.js'
import { getBaseHeader } from '@/utils/request'
import { exportPoDetailExcel, getPoDetailPage, getPoMaster, getReleasePoMail, savePoMaster } from '@/api/om/po.js'
import operationRecord from '@/components/OperationRecord/operationRecord.vue'
import { getShortMaster, getShortMaterialDetailPage, saveShortMaster } from '@/api/om/short'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'

export default {
  name: 'Shortinfo/:id',
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  components: { OperateDropDown, BatchUpload, ShowOrEdit, PoDetail,operationRecord },
  data() {
    return {
      getBaseHeader,
      remark: '',
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      showFile: false,
      fileList: [],
      orderData: {
        id: null,
        fileIds: [
        ],
        no: '',
        createTim: null,
        status: '',
        remarks: ''
      },
      editMode: true,
      queryParams: {
        search: '',
        partNo: '',
        poNo: '',
        supplier: '',
        newShipStatus: [],
        shipStatus:[],
        gap:[],
        esicOpenPoDemandGap:[],
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: ''
      },
      total: 0,
      list: [],
      showSearch: false,
      loading: false,
      shortInfoGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'shortInfo',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      mergeRowFields: [
        'checkbox',
        'partNo',
        'description',
        'mfg',
        'mpn',
        'customer',
        'openPo',
        'esicOpenPo',
        'gap',
        'demandTotal',
        'esicOpenPoDemandGap',
        'customerSupplier',
        'customerLeadTime',
        'customerSpq',
        'customerMoq',
        'customerSupplierStockInMrp',
        'status'
      ],
      staticColumn:[
        { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
        { title: 'Part No.', field: 'partNo', visible: true, width: 100, fixed: 'left' },
        { title: 'Description', field: 'description', visible: true, width: 150 },
        { title: 'MFG', field: 'mfg', visible: true, width: 100 },
        { title: 'MPN', field: 'mpn', visible: true, width: 100 },
        { title: 'Customer', field: 'customer', visible: true, width: 100 },
        { title: 'Open PO', field: 'openPo', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        { title: 'ESiC System Open PO\n' +
              ' (To be delivered+To be confirmed + Pending)', field: 'esicOpenPo', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        { title: 'GAP', field: 'gap', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        { title: 'Total', field: 'demandTotal', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        { title: 'ESIC OPEN PO QTY - Demand ', field: 'esicOpenPoDemandGap', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        { title: 'Supplier', field: 'customerSupplier', visible: true, width: 100 },
        { title: 'Leadtime in WK', field: 'customerLeadTime', visible: true, width: 100 },
        { title: 'SPQ', field: 'customerSpq', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        { title: 'MOQ', field: 'customerMoq', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        { title: 'Supplier Stock/Added in MRP?', field: 'customerSupplierStockInMrp', visible: true, width: 100 },
        { title: 'Execution Status', field: 'status', visible: true, width: 100 , slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SHORT_PART_STATUS }},
        { title: 'Supplier', field: 'supplier', visible: true, width: 100 },
        { title: 'SO Date', field: 'soDate', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
        { title: 'PO Date', field: 'poDate', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
        { title: 'PO#', field: 'poNo', visible: true, width: 100 },
        { title: 'Order Quantity', field: 'orderQuantity', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        { title: 'PO Request Del. Date', field: 'reqDelDate', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
        { title: 'New Request Del Date', field: 'newReqDelDate', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
        { title: 'New Request Del Quantity', field: 'newReqDelQuantity', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        { title: 'New Status', field: 'newShipStatus', visible: true, width: 100 , slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SO_STATUS }},
        { title: 'Action', field: 'action', visible: true, width: 100 },
        { title: 'Supplier Con. Del Date', field: 'conDelDate', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
        { title: 'Supplier Con. Del Qty', field: 'conDelQuantity', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        { title: 'Status', field: 'shipStatus', visible: true, width: 100 , slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SO_STATUS }},
        { title: 'Shipped Date', field: 'shipDate', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
        { title: 'Shipped Quantity', field: 'shipQuantity', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        { title: 'LT (weeks)', field: 'leadTime', visible: true, width: 100 },
        { title: 'MOQ', field: 'moq', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        { title: 'MPQ', field: 'spq', visible: true, width: 100, slots: { default: 'qty'},align: 'right' },
        {
          field: 'operate',
          slots: { default: 'operate' }, fixed: 'right',
          title: this.$t('common.operate'), visible: true, width: 35, showOverflow: false
        }
      ],
      exportLoading: false,
      releaseLoading: false,
      obligatoryDialog: false,
      poEdit: {
        poId: '0'
      },
      poEditShow:false,

    }
  },
  mounted() {
    this.editMode = this.$route.query.viewOnly !== 'true'
    this.init()
    this.getList()
  },
  methods: {
    getDictDatas,
    init() {
      getShortMaster(this.$route.params.id).then(res => {
        res.data.files?.forEach(a => {
          a.name = a.fileName
        })
        this.fileList = res.data?.files || []
        this.orderData = res.data
        //结束的订单不允许编辑
        if(this.orderData.status==='closed'||this.orderData.status==='cancel'){
          this.editMode=false
        }
        this.girdOption.columns = [
          ...this.staticColumn.slice(0, 8),
          ...res.data.months.map(item => {
            return {
              title: item,
              field: item,
              visible: true,
              width: 100,
              slots: { default: 'qty'},
              align: 'right'
            }
          }),
          ...this.staticColumn.slice(8)
        ]
      })
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    getList() {
      this.loading = true
      getShortMaterialDetailPage({
        ...this.queryParams,
        shortId: this.$route.params.id
      }).then(res => {
        this.loading = false
        res.data.list.forEach(a => {
          a.demandRespVO.forEach(b => {
            a[b.month] = b.quantity
          })
        })
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    viewAttachments() {
      // Logic to view attachments
    },
    submitForm() {
      saveShortMaster({ ...this.orderData,
        fileIds: this.fileList.map(a => a.fileId)
      }).then(res => {
        this.$message.success('Operation successful')
      })
    },
    onRemove(file, fileList) {
      const index = this.fileList.indexOf(file.response?.data?.fileId) || this.fileList.indexOf(file.fileId)
      this.fileList.splice(index, 1)
    },
    onSuccess(response, file, fileList) {
      this.fileList.push({
        name: file.name,
        fileUrl: response.data.url,
        fileId: response.data.id
      })
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.fileUrl) {
        window.open(file.fileUrl)
      }
    },
    handleReset() {
      this.queryParams = {
        searchKey: '',
        partNo: '',
        mfg: '',
        mpn: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      }
      this.getList()
    },

    exportExcel() {
      this.$modal.confirm(this.$t('Do you confirm the download?')).then(() => {
        this.exportLoading = true
        return exportPoDetailExcel({ ...this.queryParams,
          poId: this.$route.params.id
        })
      }).then(response => {
        this.$download.excel(response, this.orderData.poNo+ this.$t('_po info material.xlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.commonFormItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 150px);
  }
}
.poInfo{

}
.searchValue{
  width: 100%;
}
.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}
::v-deep .avpl-row{
  background:#81D3F8 !important;
}
::v-deep .avpl-edit{
  background:#FFE668 !important;
}
.button-container {
  margin-top: 20px; /* 设置按钮与表格的距离 */
  text-align: right; /* 使按钮靠右对齐 */
}
</style>
