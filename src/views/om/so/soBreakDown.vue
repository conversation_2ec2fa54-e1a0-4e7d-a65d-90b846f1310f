
<template>
  <div>
    <div style="margin-top: 25px;">
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.search"
          style="flex: 0 1 35%"
          :placeholder="$t('SO NO#/Customer/Buyer/Part No./Description/avpl supplier')"
          @keyup.enter.native="getListInit"
        />
        <el-button plain type="primary" @click="getListInit">
          {{ $t('common.search') }}
        </el-button>
        <el-button
          style="margin-left: 0"
          @click="handleReset"
        >
          {{ $t('common.reset') }}
        </el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>
      </div>

      <el-form v-show="showSearch" inline label-width="120px">

        <el-form-item class="searchItem" :label="$t('SO NO#')" prop="no">
              <el-autocomplete
                v-model="queryParams.soNo"
                :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'so')})"
                :popper-append-to-body="false"
                popper-class="el-autocomplete-suggestion"
                class="searchItem"
                @select="((item) => {handleSelect(item, 'so')})"
                @keyup.enter.native="getListInit"
              />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Part No.')" prop="">
          <el-input v-model="queryParams.partNo" :placeholder="$t('Part No.、Description')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('MFG')" prop="">
          <el-autocomplete
              v-model="queryParams.mfg"
              :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'mfg')})"
              :placeholder="$t('MFG')"
              :popper-append-to-body="false"
              popper-class="el-autocomplete-suggestion"
              class="overList"
              @select="((item) => {handleSelect(item, 'mfg')})"
              @keyup.enter.native="getListInit"
          />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('MPN')" prop="">
          <el-input v-model="queryParams.mpn" :placeholder="$t('MPN')" @keyup.enter.native="getListInit" />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Buyer')" prop="">
          <el-select v-model="queryParams.buyer" class="searchValue" clearable filterable>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_BUYER)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('SO Part Status')" prop="">
          <el-select v-model="queryParams.status" class="searchValue" clearable filterable multiple>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SO_DETAIL_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('SO Pay. Term')" prop="">
          <el-select v-model="queryParams.payTerm" class="searchValue" clearable filterable multiple>
            <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item class="searchItem" :label="$t('SO Del. Term')" prop="">
          <el-select v-model="queryParams.delTerm" class="searchValue" clearable multiple>
            <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item class="searchItem" :label="$t('AVPL Result')" prop="">
          <el-select v-model="queryParams.avplResult" class="searchValue" clearable filterable>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_CHECK_AVPL_RESULT)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Material Type')" prop="">
          <el-select v-model="queryParams.type" class="searchValue" clearable filterable>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_MATERIAL_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Customer')" prop="customer" style="width: 66%">
          <el-radio-group v-model="queryParams.customer" @change="getListInit();">
            <el-radio :label="null">{{ $t('order.whole') }}</el-radio>
            <el-radio v-for="customer in $store.getters.incapCustomers" :key="customer" :label="customer">
              {{ customer }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <vxe-grid
        ref="soBreak"
        :data="list"
        :loading="loading"
        v-bind="soBreakGrid"
        @sort-change="sortMethod"
        :header-cell-class-name="headerCellClassName"
        :cell-class-name="cellClass"
      >
        <template #soNo="{column,row}">
          <copy-button type="text" @click="$router.push(`/om/soInfo/${row.soId}?no=${row.soNo}&viewOnly=true`)">{{ row.soNo }}</copy-button>
        </template>
        <template #dict="{column,row}">
          <dict-tag :type="column.params.dict" :value="row[column.field]" />
        </template>
        <template #orderQuantity="{row}">
              <span v-if="row.orderQuantity">
                {{ row.orderQuantity?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #unitPrice="{row}">
              <span v-if="row.unitPrice">
                {{ row.unitPrice?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #unitPricePer="{row}">
              <span v-if="row.unitPricePer">
                {{ row.unitPricePer?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #totalAmount="{row}">
              <span v-if="row.totalAmount">
                {{ row.totalAmount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #avplSoPrice="{row}">
              <span v-if="row.avplSoPrice">
                {{ row.avplSoPrice?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #avplSpq="{row}">
              <span v-if="row.avplSpq">
                {{ row.avplSpq?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #avplMoq="{row}">
              <span v-if="row.avplMoq">
                {{ row.avplMoq?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #avplSupplierUnitPrice="{row}">
              <span v-if="row.avplSupplierUnitPrice">
                {{ row.avplSupplierUnitPrice?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #suggestQuantity="{row}">
              <span v-if="row.suggestQuantity">
                {{ row.suggestQuantity?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="24" style="width: 100%" class="mb8">

            <el-col :span="1.5">
              <el-button v-hasPermi="['om:so-master:send-mail']" size="mini" icon="el-icon-message" type="primary" @click="sendEmail">{{ $t('om.CustomerAVPLConfirmationEmail') }}</el-button>
              <el-button v-hasPermi="['om:so-master:query']" plain size="mini" type="primary" icon="el-icon-download" :loading="uploadLoading" @click="exportExcel">{{ $t('order.download') }}</el-button>
            </el-col>
            <right-toolbar
              :list-id="soBreakGrid.id"
              :show-search.sync="showSearch"
              :custom-columns.sync="soBreakGrid.columns"
              @queryTable="getListInit"
            />
          </el-row>
        </template>
      </vxe-grid>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getList()"
      />
    </div>
  </div>

</template>
<script>
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { getOrderTrackerPage, getSearchResult } from '@/api/om/orderTracker'
import {
  cancelSo, exportBreakDown,
  exportOrderMaster,
  getSoMasterBreakDownPage,
  getSoMasterPage,
  sendEmail
} from '@/api/om/so.js'
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE } from '@/utils/dict'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import { zipByIds } from '@/api/infra/file'
import { exportJobLogExcel } from '@/api/infra/jobLog'

export default {
  name: 'SoBreakDown',
  components: { BatchUpload, OperateDropDown },
  data() {
    return {
      queryParams: {
        search: '',
        soNo: '',
        payTerm: [],
        soStatus: [],
        supplier: '',
        buyer: '',
        delTerm: [],
        mfg:'',
        mpn:'',
        partNo: '',
        dateType: '',
        beginDate: '',
        endDate: '',
        customer: '',
        avplResult: '',
        type:'',
        status: [],
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      },
      customers: [],
      buyers: [],
      total: 0,
      list: [],
      uploadLoading:false,
      showSearch: false,
      loading: false,
      soBreakGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'soBreak',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
          { title: 'SO NO#', field: 'soNo', slots: { default: 'soNo' }, visible: true, width: 100, fixed: 'left' },
          { title: 'Part No.', field: 'partNo', visible: true, width: 100, fixed: 'left' },
          { title: 'SO Date', field: 'soDate', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100, sortable: true },
          { title: 'Customer', field: 'customer', visible: true, width: 100 },
          { title: 'Buyer', field: 'buyer', visible: true, width: 100, slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_BUYER } },
          { title: 'Vendor', field: 'supplier', visible: true, width: 100 },
          { title: 'Ship to', field: 'shipTo', visible: true, width: 100 },
          { title: 'Invoice To', field: 'invoiceTo', visible: true, width: 100 },
          { title: 'SO Pay. Term', field: 'paymentTerm', slots: { default: 'dict' }, params: { dict: DICT_TYPE.SUPPLIER_PAYMENT_DAYS }, visible: true, width: 100 },
          { title: 'SO Del. Term', field: 'delTerm', slots: { default: 'dict' }, params: { dict: DICT_TYPE.SUPPLIER_DELIVERY_CONDITION }, visible: true, width: 100 },
          { title: 'Remark', field: 'remark', visible: true, width: 100 },
          { title: 'SO Req. Del.Date', field: 'reqDelDate', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100, sortable: true },
          { title: 'SO Line No', field: 'soLine', visible: true, width: 100 ,align: 'right'},
          { title: 'Description', field: 'description', visible: true, width: 100 },
          { title: 'MPN', field: 'mpn', visible: true, width: 100 },
          { title: 'MFG', field: 'mfg', visible: true, width: 100 },
          { title: 'SO Quantity', field: 'orderQuantity', visible: true, width: 100, slots: { default: 'orderQuantity' },align: 'right' },
          { title: 'Currency', field: 'currency', slots: { default: 'dict' }, params: { dict: DICT_TYPE.COMMON_CURRENCY }, visible: true, width: 100 },
          { title: 'U/P 1 PCS', field: 'unitPrice', visible: true, width: 100, slots: { default: 'unitPrice' } ,align: 'right' },
          { title: 'U/P', field: 'unitPricePer', visible: true, width: 100, slots: { default: 'unitPricePer' },align: 'right'  },
          { title: 'Total Amount', field: 'totalAmount', visible: true, width: 100 , slots: { default: 'totalAmount' } ,align: 'right'},
          { title: 'SO Part Status', field: 'status', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SO_DETAIL_STATUS }, visible: true, width: 100 },
          { title: 'AVPL MPN', field: 'avplMpn', visible: true, width: 100 },
          { title: 'AVPL MFG', field: 'avplMfg', visible: true, width: 100 },
          { title: 'AVPL U/P', field: 'avplSoPrice', visible: true, width: 100, slots: { default: 'avplSoPrice' },align: 'right'  },
          { title: 'AVPL Spq', field: 'avplSpq', visible: true, width: 100 , slots: { default: 'avplSpq' },align: 'right' },
          { title: 'AVPL Moq', field: 'avplMoq', visible: true, width: 100 , slots: { default: 'avplMoq' },align: 'right' },
          { title: 'AVPL Supplier', field: 'avplSupplier', visible: true, width: 100 },
          { title: 'AVPL Currency', field: 'avplCurrency', slots: { default: 'dict' }, params: { dict: DICT_TYPE.COMMON_CURRENCY }, visible: true, width: 100 },
          { title: 'AVPL Supplier Currency', field: 'avplSupplierCurrency', slots: { default: 'dict' }, params: { dict: DICT_TYPE.COMMON_CURRENCY }, visible: true, width: 100 },
          { title: 'AVPL Supplier. U/P', field: 'avplSupplierUnitPrice', visible: true, width: 100 , slots: { default: 'avplSupplierUnitPrice' },align: 'right'},
          { title: 'AVPL Result', field: 'result', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_CHECK_AVPL_RESULT }, visible: true, width: 100 },
          { title: 'Suggest Qty', field: 'suggestQuantity', visible: true, width: 100 , slots: { default: 'suggestQuantity' },align: 'right' },
          { title: this.$t('Material Type'), field: 'type', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_MATERIAL_TYPE }, visible: true, width: 100 },
          // { title: "PO Remark", field: "poRemark", visible: true, width: 100 }
        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      }}
  },
  mounted() {
    this.getList()
  },
  methods: {
    headerCellClassName({ column, columnIndex }) {
      if (['avplMpn', 'avplMfg', 'avplSoPrice', 'avplSpq', 'avplMoq', 'avplSupplier',
        'avplCurrency', 'avplSupplierCurrency', 'avplSupplierUnitPrice',
        'result', 'suggestQuantity'].includes(column.field)) {
        return 'avpl-row'
      }
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.getList()
    },
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      getSoMasterBreakDownPage(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },

    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    handleSelect(item, type) {
      switch (type) {
        case 'so':
          this.queryParams.no = item.value
          break
        case 'buyer':
          this.queryParams.buyer = item.value
          break
        case 'supplier':
          this.queryParams.supplier = item.value
          break
        case 'mfg':
          this.queryParams.mfg = item.value
          break
        // eslint-disable-next-line no-fallthrough
        default:
          break
      }
    },
    querySearchAsync(queryString, cb, type) {
      if (queryString.length < 2) {
        return
      }
      getSearchResult({
        searchValue: queryString,
        type: type
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item
          }
        }))
      })
    },
    handleReset() {
      this.queryParams = {
        search: '',
        soNo: '',
        payTerm: [],
        soStatus: [],
        supplier: '',
        buyer: '',
        mfg:'',
        mpn:'',
        delTerm: [],
        partNo: '',
        dateType: '',
        beginDate: '',
        endDate: '',
        customer: '',
        type:'',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      }
      this.getList()
    },

    sendEmail() {
      const selectRows = this.$refs.soBreak.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to modify'))
        return false
      } else {
        this.$modal.confirm('Do you confirm sending the selected pending list to the customer?').then(() => {
          console.log(selectRows)
          sendEmail(selectRows.map(row => row.soDetailId).join(','))
        }).then(res => {
          if (res.data){
            this.$message.success('Operation Successful')
            this.getList()
          }
        })
      }
    },
    exportExcel() {
      this.uploadLoading=true
      exportBreakDown(this.queryParams).then(res => {
        this.$download.excel(res, 'so break down.xlsx')
      }).finally(()=>{
        this.uploadLoading=false
      })
    },
    cellClass({ row, column }) {
      if (['avplMpn', 'avplMfg', 'avplSoPrice', 'avplSpq', 'avplMoq']
        .includes(column.field) && !row[column.field + 'Result']
      ) {
        return 'avpl-edit'
      }
    }
  }
}
</script>
<style scoped lang="scss">

.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}
.searchValue {
  width: 100%;
}

 ::v-deep .avpl-row{
  background:#81D3F8 !important;
}
::v-deep .avpl-edit{
  background:#FFE668 !important;
}

</style>
