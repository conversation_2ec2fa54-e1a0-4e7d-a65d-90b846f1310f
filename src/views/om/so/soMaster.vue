
<template>
  <div>
    <div style="margin-top: 25px;">
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.search"
          style="flex: 0 1 35%"
          :placeholder="$t('SO NO#/Customer/Buyer/Part No.')"
          @keyup.enter.native="getListInit"
        />
        <el-button plain type="primary" @click="getListInit">
          {{ $t('common.search') }}
        </el-button>
        <el-button
          style="margin-left: 0"
          @click="handleReset"
        >
          {{ $t('common.reset') }}
        </el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>
      </div>

      <el-form v-show="showSearch" inline label-width="120px">

        <el-form-item class="searchItem" :label="$t('SO NO#')" prop="no">
          <el-autocomplete
            v-model="queryParams.soNo"
            :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,'so')})"
            :popper-append-to-body="false"
            popper-class="el-autocomplete-suggestion"
            class="searchItem"
            clearable
            @select="((item) => {handleSelect(item, 'so')})"
            @keyup.enter.native="getListInit"
          />
        </el-form-item>

        <el-form-item class="searchItem" :label="$t('SO Pay. Term')" prop="">
          <el-select v-model="queryParams.payTerm" class="searchValue" clearable filterable multiple>
            <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('SO Status')" prop="">
          <el-select v-model="queryParams.soStatus" class="searchValue" clearable filterable multiple>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SO_MASTER_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Buyer')" prop="">
          <el-select v-model="queryParams.buyer" class="searchValue" clearable filterable>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_BUYER)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item class="searchItem" :label="$t('SO Del.Term')" prop="">
          <el-select v-model="queryParams.delTerm" class="searchValue" clearable multiple>
            <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Part No.')" prop="">
          <el-input v-model="queryParams.partNo" :placeholder="$t('Part No.、Description')" clearable @keyup.enter.native="getListInit" />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.timeType')" prop="dateType">
          <el-select v-model="queryParams.dateType" class="searchValue" clearable>
            <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SEARCH_TIME_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Time Range" class="searchItem">
          <el-date-picker
            v-model="queryParams.time"
            class="searchValue"
            value-format="yyyy-MM-dd"
            type="daterange"
            :range-separator="$t('order.to')"
            :start-placeholder="$t('common.startDate')"
            :end-placeholder="$t('common.endDate')"
          />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('Customer')" prop="customer" style="width: 66%">
          <el-radio-group v-model="queryParams.customer" @change="getListInit();">
            <el-radio :label="null">{{ $t('order.whole') }}</el-radio>
            <el-radio v-for="customer in $store.getters.incapCustomers" :key="customer" :label="customer">
              {{ customer }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <vxe-grid
        ref="soOverview"
        :data="list"
        :loading="loading"
        @sort-change="sortMethod"
        v-bind="soOverviewGrid"
      >

        <template #dict="{column,row}">
          <dict-tag :type="column.params.dict" :value="row[column.field]" />
        </template>
        <template #soNo="{column,row}">
          <copy-button type="text" @click="$router.push(`/om/soInfo/${row.id}?no=${row.soNo}&viewOnly=true`)">
            {{ row.soNo }}
          </copy-button>
        </template>
        <template #totalPoAmount="{row}">
              <span v-if="row.totalPoAmount">
                {{ row.totalPoAmount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
        </template>
        <template #operate="{row}">
          <OperateDropDown
            :menu-item="[
              {
                name: $t('common.edit'),
                show: (row.soStatus==='ongoing'||row.soStatus==='new')&&$store.getters.permissions.includes('om:so-master:update'),
                action: (row) => $router.push(`/om/soInfo/${row.id}?no=${row.soNo}`),
                para: row
              },
              {
                name: $t('rfq.revoke'),
                show: (row.soStatus==='ongoing'||row.soStatus==='new')&&$store.getters.permissions.includes('om:so-master:cancel-so'),
                action: (row) => revokeRow(row),
                para: row
              },
              {
                name: $t('om.DownloadAttachment'),
                show: row.fileIds?.length > 0&&$store.getters.permissions.includes('om:so-master:query'),
                action: (row) => downloadRow(row,'ship'),
                para: row
              },
              {
                name: $t('om.ChangeLog'),
                show: $store.getters.permissions.includes('om:so-master:query'),
                action: (row) => showLogs(row.id,'om_so_master',['soDate','customer','buyer','soStatus','supplier','shipTo','paymentTerm','delTerm','invoiceTo','email','remark'],['soNo']),
                para: row
              },
            ]"
          />
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="24" style="width: 100%" class="mb8">
            <el-col :span="22" style="display: flex">
              <BatchUpload
                v-hasPermi="['om:so-master:upload-so']"
                :btn-name="$t('Upload So')"
                :template-download="async ()=>{ return exportOrderTemplate()}"
                template-name="so template"
                url="om/so-master/import-so-template"
                :after-upload="getList"
              />
              <el-button v-hasPermi="['om:so-master:cancel-so']" size="mini" icon="el-icon-close" plain type="danger" @click="batchRevoke">{{ $t('rfq.revoke') }}</el-button>
              <el-button v-hasPermi="['om:so-master:query']" size="mini" plain  icon="el-icon-download" type="primary" :loading="uploadLoading" @click="exportExcel">{{ $t('order.download') }}</el-button>
            </el-col>
            <el-col :span="2">
            <right-toolbar
              :list-id="soOverviewGrid.id"
              :show-search.sync="showSearch"
              :custom-columns.sync="soOverviewGrid.columns"
              @queryTable="getListInit"
            />
            </el-col>
          </el-row>
        </template>
      </vxe-grid>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getList()"
      />
    </div>
    <!--操作记录-->
    <el-dialog
      v-if="log.open"
      :close-on-click-modal="false"
      :title="$t('common.operationRecord')"
      :visible.sync="log.open"
      width="1000px"
    >
      <operation-record
        :business-id="log.businessId"
        :columns="log.columns"
        :log-visible.sync="log.open"
      />
    </el-dialog>
  </div>

</template>
<script>
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { getSearchResult } from '@/api/om/orderTracker'
import {
  cancelSo,
  exportOrderMaster,
  exportOrderTemplate,
  getSoMasterPage
} from '@/api/om/so.js'
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE } from '@/utils/dict'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import { zipByIds } from '@/api/infra/file'
import operationRecord from '@/components/OperationRecord/operationRecord.vue'

export default {
  name: 'SoMaster',
  components: { operationRecord, BatchUpload, OperateDropDown },
  data() {
    return {
      queryParams: {
        search: '',
        soNo: '',
        payTerm: [],
        soStatus: [],
        supplier: '',
        buyer: '',
        delTerm: [],
        partNo: '',
        dateType: '',
        beginDate: '',
        endDate: '',
        customer: null,
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      },
      total: 0,
      list: [],
      showSearch: false,
      uploadLoading:false,
      loading: false,
      soOverviewGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'soOverview',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns: [
          { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
          {
            field: 'operate',
            slots: { default: 'operate' }, fixed: 'left',
            title: this.$t('common.operate'), visible: true, width: 35, showOverflow: false
          },
          { title: this.$t('SO NO#'), field: 'soNo', slots: { default: 'soNo' }, visible: true, width: 100, fixed: 'left' },
          { title: this.$t('SO Date'), field: 'soDate', visible: true, width: 100, fixed: 'left', sortable: true },
          { title: this.$t('Customer'), field: 'customer', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('SO Status'), field: 'soStatus', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SO_MASTER_STATUS }, visible: true, width: 100 },
          { title: this.$t('Buyer'), field: 'buyer', visible: true, width: 100 , slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_BUYER }},
          { title: this.$t('# of Part'), field: 'numOfParts', visible: true, width: 100 ,align: 'right'},
          { title: this.$t('# of Lines'), field: 'numOfLines', visible: true, width: 100,align: 'right' },
          { title: this.$t('# of release'), field: 'numOfReleased', visible: true, width: 100,align: 'right' },
          { title: this.$t('Total Amount'), field: 'totalPoAmount', visible: true, width: 100 , slots: { default: 'totalPoAmount' },align: 'right'},
          { title: this.$t('SO Req. Del.Date F'), field: 'reqDelDateF', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: this.$t('SO Req. Del.Date T'), field: 'reqDelDateT', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: this.$t('SO Con. Del. Date F'), field: 'conDelDateF', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: this.$t('SO Con. Del. Date T'), field: 'conDelDateT', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: this.$t('Ship Date F'), field: 'shipDateF', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: this.$t('Ship Date T'), field: 'shipDateT', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 },
          { title: this.$t('Vendor'), field: 'supplier', visible: true, width: 100 },
          { title: this.$t('Ship to'), field: 'shipTo', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SHIP_TO },visible: true, width: 100 },
          { title: this.$t('Invoice to'), field: 'invoiceTo', visible: true, width: 100 },
          { title: this.$t('SO Pay. Term'), field: 'paymentTerm', slots: { default: 'dict' }, params: { dict: DICT_TYPE.SUPPLIER_PAYMENT_DAYS }, visible: true, width: 100 },
          { title: this.$t('SO Del. Term'), field: 'delTerm', slots: { default: 'dict' }, params: { dict: DICT_TYPE.SUPPLIER_DELIVERY_CONDITION }, visible: true, width: 100 },
          { title: this.$t('Remark'), field: 'remark', visible: true, width: 100 }
        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      // 操作日志
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: []
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    exportOrderTemplate,
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      getSoMasterPage(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.getList()
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    handleSelect(item, type) {
      switch (type) {
        case 'so':
          this.queryParams.no = item.value
          break
        case 'customer':
          this.queryParams.customer = item.value
          break
        case 'buyer':
          this.queryParams.buyer = item.value
          break
        case 'supplier':
          this.queryParams.supplier = item.value
          break
        case 'mfg':
          this.queryParams.mfg = item.value
          break
        // eslint-disable-next-line no-fallthrough
        default:
          break
      }
    },
    querySearchAsync(queryString, cb, type) {
      if (queryString.length < 2) {
        return
      }
      getSearchResult({
        searchValue: queryString,
        type: type
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item
          }
        }))
      })
    },
    handleReset() {
      this.queryParams = {
        search: '',
        soNo: '',
        payTerm: [],
        soStatus: [],
        supplier: '',
        buyer: '',
        delTerm: [],
        partNo: '',
        dateType: '',
        beginDate: '',
        endDate: '',
        customer: null,
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      }
      this.getList()
    },
    editRow(row, str1) {

    },
    revokeRow(row) {
      this.$modal.confirm('Do you confirm the cancellation of the order with order number"' + row.soNo + '"?').then(() => {
        return cancelSo(String(row.id))
      }).then(res => {
        if (res.data) {
          this.$message.success('Operation Successful')
          this.getList()
        }
      })
    },
    batchRevoke() {
      const selectRows = this.$refs.soOverview.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to modify'))
        return false
      } else {
        this.$modal.confirm('Do you confirm the cancellation of the selected SO orders?').then(() => {
          return cancelSo(selectRows.map(row => row.id).join(','))
        }).then(res => {
          if (res.data) {
            this.$message.success('Operation Successful')
            this.getList()
          }
        })
      }
    },
    downloadRow(row, str1) {
      if (row.fileIds.length) {
        zipByIds({
          files: row.fileIds.join(','),
          zipFileName: 'SO file'
        }).then(res => {
          this.$download.zip(res, 'SO Master File.zip')
        })
      } else {
        this.$message.error('No files available for download')
      }
    },
    exportExcel() {
      this.uploadLoading = true
      exportOrderMaster(this.queryParams).then(res => {
        this.$download.excel(res, 'SO Master.xlsx')
      }).finally(() => {
        this.uploadLoading = false
      })
    },
    // show operate log
    showLogs(businessId, table, column,keyColumns) {
      this.log.open = true
      this.log.businessId = businessId
      this.log.columns = [{
        tableName: table,
        searchColumns: column,
        keyColumns:keyColumns
      }]
    }
  }
}
</script>
<style scoped lang="scss">

.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}
.searchValue {
  width: 100%;
}

</style>
