
<template>
  <div class="app-container">

  <div class="app-title">
    <div class="app-title-tab">
      <span :class="{'app-title-tab-selected': selectedTab === 'SO'}" @click="selectedTab = 'SO'">SO Master</span>
      <span :class="{'app-title-tab-selected': selectedTab === 'Break'}" @click="selectedTab = 'Break'">SO Break Down</span>
    </div>
  </div>
    <div>
      <soMaster v-if="selectedTab === 'SO'"></soMaster>
      <soBreakDown v-if="selectedTab === 'Break'"></soBreakDown>
    </div>

</div>

</template>
<script>
import { getOrderTrackerPage, getSearchResult } from '@/api/om/orderTracker'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { parseTime } from '@/utils/ruoyi'
import SoMaster from '@/views/om/so/soMaster.vue'
import SoBreakDown from '@/views/om/so/soBreakDown.vue'

export default {
  name: "Soindex",
  components: { SoBreakDown, SoMaster, OperateDropDown },
  data() {
    return {
      selectedTab: 'SO',


    }
  },
  mounted() {
  },
  methods: {


  }
}
</script>

<style scoped lang="scss">
.app-title{
  display: flex;
  margin-bottom: 15px;
  justify-content: space-between;
  &-tab{
    flex: none;
    font-size: 24px;
    color: #383838;
    letter-spacing: 0;
    font-weight: 400;
    cursor: pointer;
    span{
      margin: 0 15px;
    }
    &-selected{
      font-size: 28px;
      color: #383838;
      letter-spacing: 0;
      font-weight: 700;
      position: relative;
    }
    &-selected::after {
      content: "";
      position: absolute;
      border-radius: 2px;
      width: 20px; /* Adjust the width of the line */
      height: 4px; /* Adjust the thickness of the line */
      background-color: black; /* Adjust the line color */
      bottom: -6px; /* Adjust the position of the line relative to the text */
      left: 50%;
      transform: translateX(-50%);
    }
  }
}


</style>
