<template>
  <div class="app-container">
    <common-card :title="$t('SO Basic Information')">
      <el-form ref="orderForm" :model="orderData" inline label-width="120px">
        <el-form-item
          :label="$t('Customer')"
          class="commonFormItem"
          prop="customer"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.customer" :dict="DICT_TYPE.OM_INCAP_CUSTOMER">
            <el-select v-model="orderData.customer" class="searchValue" :disabled="!editMode" clearable>
              <el-option v-for="customer in $store.getters.incapCustomers" :key="customer" :label="customer" :value="customer" />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('SO NO#')"
          class="commonFormItem"
          prop="soNo"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.soNo">
            <el-input v-model="orderData.soNo" :disabled="true" />
          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('Supplier')"
          class="commonFormItem"
          prop="supplier"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.supplier">
            <el-input v-model="orderData.supplier" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <!--        <el-form-item-->
        <!--          :label="$t('Supplier Code')"-->
        <!--          class="commonFormItem"-->
        <!--          prop="supplier"-->
        <!--        >-->
        <!--          <show-or-edit :disabled="!editMode" :value="orderData.supplier">-->
        <!--            <el-input v-model="orderData.supplierCode" :disabled="!editMode" />-->
        <!--&lt;!&ndash;            todo&ndash;&gt;-->
        <!--          </show-or-edit>-->
        <!--        </el-form-item>-->
        <el-form-item
          :label="$t('SO Del. Term')"
          class="commonFormItem"
          prop="delTerm"
        >
          <show-or-edit :disabled="!editMode" :dict="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION" :value="orderData.delTerm" :type="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION">
            <el-select v-model="orderData.delTerm" class="searchValue" :disabled="!editMode" clearable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('SO Pay. Term')"
          class="commonFormItem"
          prop="paymentTerm"
        >
          <show-or-edit :disabled="!editMode" :dict="DICT_TYPE.SUPPLIER_PAYMENT_DAYS" :value="orderData.paymentTerm" :type="DICT_TYPE.SUPPLIER_PAYMENT_DAYS">
            <el-select v-model="orderData.paymentTerm" class="searchValue" :disabled="!editMode" clearable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>

          </show-or-edit>
        </el-form-item>
        <el-form-item class="commonFormItem" :label="$t('Buyer')" prop="">
          <show-or-edit :disabled="!editMode" :value="orderData.buyer" :dict="DICT_TYPE.OM_BUYER" :type="DICT_TYPE.OM_BUYER">
            <el-select v-model="orderData.buyer" class="searchValue" :disabled="!editMode" clearable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_BUYER)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('Ship To')"
          class="commonFormItem"
          prop="shipTo"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.shipTo" :dict="DICT_TYPE.OM_SHIP_TO">
            <el-select v-model="orderData.shipTo" class="searchValue" clearable filterable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SHIP_TO)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('Invoice To')"
          class="commonFormItem"
          prop="invoiceTo"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.invoiceTo">
            <el-input v-model="orderData.invoiceTo" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('Total Amount')"
          class="commonFormItem"
          prop="totalPoAmount"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.totalPoAmount">
            <el-input v-model="orderData.totalPoAmount" :disabled="true" />
          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('SO Date')"
          class="commonFormItem"
          prop="soDate"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.soDate" type="Date">
            <el-date-picker
              v-model="orderData.soDate"
              class="searchValue"
              :disabled="!editMode"
              :placeholder="$t('order.selectDate')"
              type="date"
              value-format="yyyy-MM-dd"
            />
          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('Email')"
          class="commonFormItem"
          prop="email"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.email">
            <el-input v-model="orderData.email" :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('auth.uploadAttachments')"
          class="commonFormItem"
        >
          <div style="display: flex;justify-content: space-between">

            <el-upload
              class="upload-demo"
              :disabled="!editMode"
              :action="uploadUrl"
              :headers="getBaseHeader()"
              :on-remove="onRemove"
              :on-preview="onPreview"
              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
              multiple
              :limit="5"
              :show-file-list="false"
              :file-list="fileList"
            >
              <el-button
                v-if="editMode"
                v-hasPermi="['om:so-master:update-so']"
                :disabled="!editMode"
                class="uploadBtn"
                size="small"
                plain
                icon="el-icon-plus"
                type="primary"
              />
            </el-upload>
            <div>
              {{ $t('scar.viewAttachments') }}
              <el-button
                class="uploadBtn"
                size="small"
                style="padding: 5px 9px"
                :disabled="fileList.length===0"
                plain
                :type="fileList.length?'primary':''"
                @click="showFile=true"
              >
                {{ fileList.length }}
              </el-button>

              <el-dialog
                v-if="showFile"
                :visible.sync="showFile"
                :title="$t('scar.viewAttachments')"
                width="400px"
              >
                <el-upload
                  class="upload-demo"
                  :disabled="!editMode"
                  :action="uploadUrl"
                  :headers="getBaseHeader()"
                  :on-remove="onRemove"
                  :on-preview="onPreview"
                  :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                  multiple
                  :limit="5"
                  :show-file-list="true"
                  :file-list="fileList"
                />
                <div slot="footer">
                  <el-button type="primary" @click="showFile=false">{{ $t('order.close') }}</el-button>
                </div>

              </el-dialog>
            </div>
          </div>

        </el-form-item>
        <el-form-item
          :label="$t('Remark')"
          class="commonFormItem"
          style="width: 100%"
          prop="remark"
        >
          <show-or-edit :disabled="!editMode" :value="orderData.remark">
            <el-input v-model="orderData.remark" type="textarea" :rows="3" autosize :disabled="!editMode" />
          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('SO Status')"
          class="commonFormItem"
          style="width: 100%"
          prop="Status"
        >
          <dict-tag :value="orderData.soStatus" :type="DICT_TYPE.OM_SO_MASTER_STATUS" />
        </el-form-item>
        <div
          v-if="editMode"
          style="width: 100%;display:flex;justify-content: center"
        >
          <el-button v-hasPermi="['om:so-master:update-so']" icon="el-icon-edit-outline" type="primary" @click="submitForm">{{ $t('Update') }}</el-button>
        </div>

      </el-form>

    </common-card>
    <common-card
      :title="$t('SO Part List')"
    >
      <div style="margin-top: 25px;">
        <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
          <el-input
            v-model="queryParams.search"
            style="flex: 0 1 35%"
            :placeholder="$t('SO NO#/Customer/Buyer/Part No.')"
            @keyup.enter.native="getListInit"
          />
          <el-button plain type="primary" @click="getListInit">
            {{ $t('common.search') }}
          </el-button>
          <el-button
            style="margin-left: 0"
            @click="handleReset"
          >
            {{ $t('common.reset') }}
          </el-button>
          <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
            <el-button type="text">
              {{ $t('common.advancedSearch') }}
            </el-button>
            <i
              :style="showSearch? '':{transform: 'rotate(180deg)'}"
              class="el-icon-arrow-up"
              style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
            />
          </div>
        </div>

        <el-form v-show="showSearch" inline label-width="120px">
          <el-form-item class="searchItem" :label="$t('Part No.')" prop="partNo">
            <el-input v-model="queryParams.partNo" :placeholder="$t('Part No.、Description')" @keyup.enter.native="getListInit" />

          </el-form-item>

          <el-form-item class="searchItem" :label="$t('MFG')" prop="">
            <el-input v-model="queryParams.mfg" :placeholder="$t('Please enter content')" @keyup.enter.native="getListInit" />

          </el-form-item>
          <el-form-item class="searchItem" :label="$t('SO Part Status')" prop="">
            <el-select v-model="queryParams.status" class="searchValue" clearable filterable multiple>
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_SO_DETAIL_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('MPN')" prop="">
            <el-input v-model="queryParams.mpn" :placeholder="$t('Please enter content')" @keyup.enter.native="getListInit" />

          </el-form-item>
          <el-form-item class="searchItem" :label="$t('AVPL Result')" prop="">
            <el-select v-model="queryParams.avplResult" class="searchValue" clearable filterable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_CHECK_AVPL_RESULT)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('Recommend')" prop="">
            <el-select v-model="queryParams.recommend" class="searchValue" clearable filterable>
              <el-option :label="$t('All')" :value="null" />
              <el-option v-for="item in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)" :key="item.value" :label="item.label" :value="item.value === 'true'" />
            </el-select>
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('Material Type')" prop="">
            <el-select v-model="queryParams.type" class="searchValue" clearable filterable>
              <el-option v-for="item in getDictDatas(DICT_TYPE.OM_MATERIAL_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('AVPL supplier')" prop="">
            <el-input v-model="queryParams.avplSupplier" :placeholder="$t('Please enter AVPL supplier')" @keyup.enter.native="getListInit" />
          </el-form-item>
        </el-form>
        <vxe-grid
          ref="soInfo"
          :data="list"
          :loading="loading"
          :span-method="mergeRowMethod"
          :cell-class-name="cellClass"
          :header-cell-class-name="headerCellClassName"
          v-bind="soInfoGrid"
        >
          <template #partNo="{column,row}">
            <copy-button type="text">{{ row.partNo }} <i
                v-if="row.hasUpdateAvpl"
                class="el-icon-bell"
                style="font-size: 16px;margin-right: 3px;cursor: pointer"
            /></copy-button>
          </template>
          <template #dict="{column,row}">
            <dict-tag :type="column.params.dict" :value="row[column.field]" />
          </template>
          <template #orderQuantity="{row}">
              <span v-if="row.orderQuantity">
                {{ row.orderQuantity?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #unitPrice="{row}">
              <span v-if="row.unitPrice">
                {{ row.unitPrice?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #unitPricePer="{row}">
              <span v-if="row.unitPricePer">
                {{ row.unitPricePer?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #totalAmount="{row}">
              <span v-if="row.totalAmount">
                {{ row.totalAmount?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #avplSoPrice="{row}">
              <span v-if="row.avplSoPrice">
                {{ row.avplSoPrice?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #avplSpq="{row}">
              <span v-if="row.avplSpq">
                {{ row.avplSpq?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #avplMoq="{row}">
              <span v-if="row.avplMoq">
                {{ row.avplMoq?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #avplSupplierUnitPrice="{row}">
              <span v-if="row.avplSupplierUnitPrice">
                {{ row.avplSupplierUnitPrice?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #startupQuantitySlots="{row}">
            <span v-if="row.startupQuantity">
                {{ row.startupQuantity?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
            </span>
          </template>
          <template #startupCostSlots="{row}">
            <span v-if="row.startupCost">
                {{ row.startupCost?.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 8 }) }}
              </span>
          </template>
          <template #operate="{row}">
            <OperateDropDown
              :menu-item="[
                {
                  name: $t('common.edit'),
                  show: editMode&&$store.getters.permissions.includes('om:so-detail:update'),
                  action: (row) => showSoDetail(row),
                  para: row
                },
                // {
                //   name: $t('删除'),
                //   show: true,
                //   action: (row) => delRow(row),
                //   para: row
                // },
                {
                  name: $t('om.ChangeLog'),
                  show: $store.getters.permissions.includes('om:so-master:detail'),
                  action: (row) => showLogs(row.soDetailId,'om_so_detail',['partNo','description','mfg','mpn','version','orderQuantity','uom','pricePer','reqDelDate','conDelDate','remark','status','unitPricePer'],['partNo']),
                  para: row
                },
              ]"
            />
          </template>
          <template #recommend="{row}">
            <show-or-edit type="Boolean" :value="row.recommend" :disabled="!editMode||(row.status!='new'&&row.status!='pending')||row.type==='Sample'">

              <el-switch v-model="row.recommend" @change="editRecommend(row)" />
            </show-or-edit>
          </template>
          <template #suggestQuantity="{row}">
            <show-or-edit :value="row.suggestQuantity" :disabled="!editMode||row.type==='Sample'">
              <el-input-number
                v-model="row.suggestQuantity"
                style="width: 95%"
                :controls="false"
                @blur="editRecommend(row)"
              />
            </show-or-edit>

          </template>
          <template #toolbar_buttons>
            <el-row :gutter="24" style="width: 100%" class="mb8">
              <el-col :span="22" style="display: flex">

                <el-button v-if="editMode" v-hasPermi="['om:so-detail:check-avpl']" icon="el-icon-check" :loading="avplLoading" size="mini" type="primary" @click="checkAvpl">{{ $t('Check AVPL') }}</el-button>
                <el-button v-if="editMode" v-hasPermi="['om:so-info:send-mail']" size="mini" icon="el-icon-message" type="primary" @click="sendEmail">{{ $t('om.CustomerAVPLConfirmationEmail') }}</el-button>
                <el-button v-if="editMode" v-hasPermi="['om:so-detail:generate-po']" icon="el-icon-news"  size="mini" type="primary" @click="generatePo">{{ $t('Generate PO') }}</el-button>
                <el-button v-if="editMode" v-hasPermi="['om:so-detail:avpl-cover-so']" icon="el-icon-check"  size="mini" type="primary" @click="avplCoverSo">{{ $t('AVPL Cover SO') }}</el-button>
                <el-button v-if="editMode" v-hasPermi="['om:so-detail:sample-material']" icon="el-icon-check"  size="mini" type="primary" @click="showSampleMaterial">{{ $t('Set as Sample') }}</el-button>
                <el-button v-if="editMode" v-hasPermi="['om:so-detail:batch-cancel']"  type="danger" plain size="mini" icon="el-icon-close" @click="batchRevoke">{{ $t('rfq.revoke') }}</el-button>
                <el-button v-if="editMode" v-hasPermi="['om:so-detail:delete-parts']"  type="danger" plain size="mini" icon="el-icon-close" @click="deleteParts">{{ $t('Delete Parts') }}</el-button>
                <el-button :loading="exportLoading" size="mini" plain icon="el-icon-download" type="primary" @click="exportExcel">{{ $t('order.download') }}</el-button>
                <BatchUpload
                  :btn-name="$t('Upload Parts')"
                  :template-download="async ()=>{ return exportSoUploadPartsTemplate()}"
                  template-name="parts template"
                  style="padding-left: 10px"
                  v-hasPermi="['om:so-detail:upload-parts']"
                  :url ="`om/so-detail/import-parts?soId=${$route.params.id}`"
                  :after-upload="getList"/>
              </el-col>
              <el-col :span="2">
                <right-toolbar
                    :list-id="soInfoGrid.id"
                    :show-search.sync="showSearch"
                    :custom-columns.sync="soInfoGrid.columns"
                    @queryTable="getListInit"
                />
              </el-col>
            </el-row>
          </template>
        </vxe-grid>
        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNo"
          :total="total"
          @pagination="getList()"
        />
      </div>
    </common-card>
    <el-dialog
      v-if="obligatoryDialog"
      title="Force Release Remarks"
      width="1000px"
      :visible.sync="obligatoryDialog"
    >
      <div style="margin: 20px 0 20px 55px">You are forcing the inclusion of items that do not meet the Check AVPL expected results into the PO. Please provide remarks</div>
      <div>
        <el-form label-width="110px">
          <el-form-item label="Remark">
            <el-input v-model="remark" type="textarea" :rows="4" />
          </el-form-item>
        </el-form>

      </div>
      <div slot="footer">
        <el-button @click="obligatoryDialog =false">Cancel</el-button>
        <el-button type="primary" :loading="confirmLoading" @click="saveRemark">Confirm</el-button>
      </div>
    </el-dialog>
    <soDetail
      ref="soDetailDialog"
      :so-detail-visible.sync="soDetailVisible"
      @getTable="getList"
    />
    <!--操作记录-->
    <el-dialog
        v-if="log.open"
        :close-on-click-modal="false"
        :title="$t('common.operationRecord')"
        :visible.sync="log.open"
        width="1000px"
    >
      <operation-record
          :business-id="log.businessId"
          :columns="log.columns"
          :log-visible.sync="log.open"
      />
    </el-dialog>
    <!--样品订单-->
    <el-dialog
        v-if="sampleMaterial.sampleMaterialShow"
        title="Set as Sample"
        width="400px"
        :visible.sync="sampleMaterial.sampleMaterialShow"
    >
      <div>
        <el-form  ref="sampleMaterialRef"
                   :model="sampleMaterial"
                   :rules="sampleMaterialRule"
                   class="baseInfo"
                   inline
                   label-width="120px">
          <el-form-item label="Supplier" prop="supplierId">
            <el-select
                style="width: 100%"
                v-model="sampleMaterial.supplierId"
                :placeholder="$t('scar.pleaseEnterKeywordsForFuzzySearch')"
                class="valueStyle"
                clearable
                filterable
                :remote-method="doListNewAuthSupplier"
                remote
                @clear="clearSupplier"
            >
              <el-option
                  v-for="item in sampleMaterial.canAuthSuppliers"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="sampleMaterial.sampleMaterialShow =false">Cancel</el-button>
        <el-button type="primary" :loading="confirmLoading" @click="SetSampleMaterial">Confirm</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { DICT_TYPE } from '@/utils/dict'
import { parseTime } from '@/utils/ruoyi'
import {
  cancelSoDetail, checkAVPL, deleteParts,
  exportSoInfo, generatedPO,
  getSoDetailPage,
  getSoMaster, listNewSupplier,
  saveRecommend, sendEmail, SetSampleMaterial,
  updateSoMaster,
  exportSoUploadPartsTemplate
} from '@/api/om/so'
import { getBaseHeader } from '@/utils/request'
import operationRecord from '@/components/OperationRecord/operationRecord.vue'
import {coverSo} from "@/api/om/so";

export default {
  name: 'Soinfo/:id',
  components: {
    operationRecord, OperateDropDown, BatchUpload, ShowOrEdit,
    soDetail: () => import('@/views/om/soDetail') },
  data() {
    return {
      getBaseHeader,
      soDetailVisible: false,
      remark: '',
      uploadUrl: `${process.env.VUE_APP_BASE_API}/admin-api/om/order-tracker-file-rel/upload?type=om_so_master&id=${ this.$route.params.id}`,
      showFile: false,
      fileList: [],
      orderData: {
        soNo: '',
        soDate: '',
        customer: '',
        buyer: '',
        soStatus: '',
        numOfParts: null,
        numOfLines: null,
        numOfReleased: null,
        totalPoAmount: null,
        currency: null,
        supplier: '',
        supplierId: null,
        shipTo: '',
        paymentTerm: '',
        delTerm: '',
        invoiceTo: '',
        email: '',
        remark: ''
      },
      orderRules: {
        // Define your validation rules here
      },
      editMode: true,
      queryParams: {
        search: '',
        status: [],
        partNo: '',
        mfg: '',
        mpn: '',
        avplResult: '',
        recommend: null,
        type:'',
        avplSupplier:'',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      },
      total: 0,
      list: [],
      showSearch: false,
      loading: false,
      soInfoGrid: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'soInfo',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { type: 'checkbox', field: 'checkbox', width: 30, visible: true, fixed: 'left' },
          {
            field: 'operate',
            slots: { default: 'operate' }, fixed: 'left',
            title: this.$t('common.operate'), visible: true, width: 35, showOverflow: false
          },
          { title: this.$t('Part No.'), field: 'partNo',slots:{default:'partNo'}, visible: true, width: 100, fixed: 'left' },
          { title: this.$t('MPN'), field: 'mpn', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('MFG'), field: 'mfg', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('SO Quantity'), field: 'orderQuantity', visible: true, width: 100, fixed: 'left', slots: { default: 'orderQuantity' },align: 'right' }, // 订单数量
          { title: this.$t('U/P 1 PCS'), field: 'unitPrice', visible: true, width: 100, fixed: 'left', slots: { default: 'unitPrice' } ,align: 'right'}, // 单价
          { title: this.$t('U/P'), field: 'unitPricePer', visible: true, width: 100, fixed: 'left', slots: { default: 'unitPricePer' },align: 'right' }, // 单价
          { title: this.$t('Material Type'), field: 'type', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_MATERIAL_TYPE }, visible: true, width: 100, fixed: 'left'  },
          { title: this.$t('AVPL MPN'), field: 'avplMpn', visible: true, width: 100 },
          { title: this.$t('AVPL MFG'), field: 'avplMfg', visible: true, width: 100 },
          { title: this.$t('AVPL U/P'), field: 'avplSoPrice', visible: true, width: 100, slots: { default: 'avplSoPrice' } ,align: 'right' }, // 使用 'avplSoPrice'
          { title: this.$t('AVPL Spq'), field: 'avplSpq', visible: true, width: 100, slots: { default: 'avplSpq' } ,align: 'right'},
          { title: this.$t('AVPL Moq'), field: 'avplMoq', visible: true, width: 100, slots: { default: 'avplMoq' } ,align: 'right'},
          { title: this.$t('AVPL Supplier'), field: 'avplSupplier', visible: true, width: 100 },
          { title: this.$t('AVPL Currency'), field: 'avplCurrency', slots: { default: 'dict' }, params: { dict: DICT_TYPE.COMMON_CURRENCY }, visible: true, width: 100 },
          { title: this.$t('AVPL Supplier Currency'), field: 'avplSupplierCurrency', slots: { default: 'dict' }, params: { dict: DICT_TYPE.COMMON_CURRENCY }, visible: true, width: 100 },
          { title: this.$t('AVPL Supplier. U/P'), field: 'avplSupplierUnitPrice', visible: true, width: 100, slots: { default: 'avplSupplierUnitPrice' },align: 'right' },
          { title: this.$t('AVPL Pay. Term'), field: 'avplPaymentTerm', slots: { default: 'dict' }, params: { dict: DICT_TYPE.SUPPLIER_PAYMENT_DAYS }, visible: true, width: 100 }, // 使用 'res
          { title: this.$t('AVPL Del. Term'), field: 'avplDeliveryTerm', slots: { default: 'dict' }, params: { dict: DICT_TYPE.SUPPLIER_DELIVERY_CONDITION }, visible: true, width: 100 }, // 使用 'res
          { title: this.$t('Lead Time'), field: 'leadTime', visible: true, width: 100 }, // 使用 'res
          { title: this.$t('AVPL Result'), field: 'result', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_CHECK_AVPL_RESULT }, visible: true, width: 100 }, // 使用 'result' 作为检查结果
          { title: this.$t('Startup Cost'), field: 'startupCost', slots: { default: 'startupCostSlots' }, visible: true, width: 100 ,align: 'right'},
          { title: this.$t('Startup Qty'), field: 'startupQuantity', slots: { default: 'startupQuantitySlots' },visible: true, width: 100 ,align: 'right'},
          { title: this.$t('Suggest Qty'), field: 'suggestQuantity', slots: { default: 'suggestQuantity' }, visible: true, width: 100 ,align: 'right'},
          { title: this.$t('Recommend'), field: 'recommend', slots: { default: 'recommend' }, visible: true, width: 100 },
          { title: this.$t('Version'), field: 'version', visible: true, width: 100 },
          { title: this.$t('Description'), field: 'description', visible: true, width: 100 },
          { title: this.$t('SO Line No'), field: 'soLine', visible: true, width: 100 ,align: 'right'}, // 使用 'soLine'
          { title: this.$t('UOM'), field: 'uom', visible: true, width: 100 }, // 单位
          { title: this.$t('Total Amount'), field: 'totalAmount', visible: true, width: 100, slots: { default: 'totalAmount' },align: 'right' },
          { title: this.$t('SO Req. Del. Date'), field: 'reqDelDate', formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '', visible: true, width: 100 }, // 使用 'reqDelDateF'
          { title: this.$t('SO Part Status'), field: 'status', slots: { default: 'dict' }, params: { dict: DICT_TYPE.OM_SO_DETAIL_STATUS }, visible: true, width: 100 },

          { title: this.$t('Remark'), field: 'remark', visible: true, width: 100 }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      exportLoading: false,
      obligatoryDialog: false,
      confirmLoading: false,
      avplLoading:false,
      // 操作日志
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: []
      },
      /**
       * 转为样品订单
       */
      sampleMaterial:{
        sampleMaterialShow:false,
        supplierId:null,
        supplier:null,
        canAuthSuppliers: [],
        soId:null,
        soDetailIds:[]
      },
      sampleMaterialRule: {
        supplierId: [{ required: true, message: this.$t('Supplier required'), trigger: 'blur' }]
      },
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  mounted() {
    this.editMode = this.$store.getters.permissions.includes('om:so-master:update')
    this.init()
    this.getList()
  },
  methods: {
    exportSoUploadPartsTemplate,
    sendEmail() {
      const selectRows = this.$refs.soInfo.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to modify'))
        return false
      } else {
        this.$modal.confirm('Do you confirm sending the selected pending list to the customer?').then(() => {
          console.log(selectRows)
          sendEmail(selectRows.map(row => row.soDetailId).join(','))
        }).then(res => {
          if (res.data){
            this.$message.success('Operation Successful')
            this.getList()
          }
        })
      }
    },
    showSoDetail(row) {
      this.soDetailVisible = true
      this.$refs.soDetailDialog.init(row.soDetailId)
    },
    init() {
      getSoMaster(this.$route.params.id).then(res => {
        res.data.files.forEach(a => {
          a.name = a.fileName
        })
        this.fileList = res.data?.files || []
        this.orderData = res.data
        //结束的订单不允许编辑
        if(this.orderData.soStatus==='closed'||this.orderData.soStatus==='cancel'){
          this.editMode=false
        }
      })
    },
    getListInit() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      getSoDetailPage({ ...this.queryParams,
        soId: this.$route.params.id
      }).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    viewAttachments() {
      // Logic to view attachments
    },
    submitForm() {
      updateSoMaster({ ...this.orderData,
        files: this.fileList.map(a => a.fileId)
      }).then(res => {
        this.$message.success('Operation Successful')
      })
    },
    onRemove(file, fileList) {
      const index = this.fileList.indexOf(file.response?.data?.fileId) || this.fileList.indexOf(file.fileId)
      this.fileList.splice(index, 1)
    },
    onSuccess(response, file, fileList) {
      this.fileList.push({
        name: response.data.name,
        fileUrl: response.data.url,
        fileId: response.data.id
      })
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.fileUrl) {
        window.open(file.fileUrl)
      }
    },
    handleReset() {
      this.queryParams = {
        search: '',
        status: [],
        partNo: '',
        mfg: '',
        mpn: '',
        avplResult: '',
        recommend: null,
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC',
        type:'',
        avplSupplier:''
      }
      this.getList()
    },
    delRow(row) {
      cancelSoDetail().then(res => {
        this.$message.success('Operation Successful')
        this.getList()
      })
    },
    headerCellClassName({ column, columnIndex }) {
      if (['avplMpn', 'avplMfg', 'avplSoPrice', 'avplSpq', 'avplMoq', 'avplSupplier',
        'avplCurrency', 'avplSupplierCurrency', 'avplSupplierUnitPrice',
        'result', 'suggestQuantity', 'recommend','avplPaymentTerm','avplDeliveryTerm','leadTime','startupCost','startupQuantity'].includes(column.field)) {
        return 'avpl-row'
      }
    },
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const fields = ['checkbox','partNo', 'mpn', 'mfg', 'orderQuantity', 'unitPrice','unitPricePer', 'version',
        'version', 'description', 'soLine', 'uom', 'totalAmount', 'reqDelDateF', 'status', 'remark'
      ]
      const cellValue = row[column.property]
      if ((column.property==='checkbox' || cellValue !== undefined) && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow.soDetailId === row.soDetailId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow.soDetailId === row.soDetailId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    cellClass({ row, column }) {
      if (['avplMpn', 'avplMfg', 'avplSoPrice', 'avplSpq', 'avplMoq']
        .includes(column.field) && !row[column.field + 'Result']
      ) {
        return 'avpl-edit'
      }
    },
    batchRevoke() {
      const selectRows = this.$refs.soInfo.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to modify'))
        return false
      } else {
        this.$confirm('Do you confirm the cancellation?', 'Prompt', {
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          cancelSoDetail({
            soId: this.$route.params.id,
            soDetailIds: selectRows.map(row => row.soDetailId)
          }).then(res => {
            this.$message.success('Operation Successful')
            this.getList()
          })
        }).catch(() => {
        })
      }
    },
    exportExcel() {
      this.$modal.confirm(this.$t('Do you confirm the download?')).then(() => {
        this.exportLoading = true
        return exportSoInfo({ ...this.queryParams,
          soId: this.$route.params.id
        })
      }).then(response => {
        this.$download.excel(response,  this.orderData.soNo+this.$t('_so info material.xlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    generatePo() {
      const selectRows = this.$refs.soInfo.getCheckboxRecords()
      if (selectRows.some(a => a.result === 'faulty')) {
        this.$confirm('Faulty material data has been detected in the Check Result. Do you want to force the release?', 'Check AvPL does not meet release criteria', {
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          this.obligatoryDialog = true
          this.remark = ''
        }).catch(() => {

        })
      } else {
        this.$confirm('Do you want to create PO?', 'Prompt', {
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          this.saveRemark()
        }).catch(() => {
        })
      }
    },
    avplCoverSo() {
      const selectRows = this.$refs.soInfo.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to modify'))
        return false
      } else {
        this.avplLoading=true
        const params= selectRows.map(j=>{
          return{
            soDetailId:j.soDetailId,
            checkAvplId:j.checkAvplId
          }
        })
        coverSo(params).then(res => {
          if (res.data){
            this.$message.success('Operation Successful')
            this.getList()
          }else{
            this.$message.error('Operation Failed')
          }
        }).finally(() => {
          this.avplLoading = false
        })
      }
    },
    deleteParts(){
      const selectRows = this.$refs.soInfo.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to delete'))
        return false
      } else {
        this.$modal.confirm(this.$t('Do you confirm the deleted?')).then(() => {
          this.avplLoading=true
          const params= selectRows.map(j=>j.soDetailId).join(',')
          return  deleteParts(params)
        }).then(res => {
          this.avplLoading = false
          if (res.data){
            this.$message.success('Operation Successful')
            this.getList()
          }else{
            this.$message.error('Operation Failed')
          }
        }).finally(() => {
          this.avplLoading = false
        })
      }
    },
    editRecommend(row) {
      const { checkAvplId, recommend, suggestQuantity } = row
      saveRecommend({ checkAvplId, recommend, suggestQuantity }).then(res => {
        this.$message.success('Operation Successful')
      })
    },
    checkAvpl() {
      const selectRows = this.$refs.soInfo.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to check avpl'))
        return false
      } else {
        this.avplLoading=true
        checkAVPL({
          soId: this.$route.params.id,
          soDetailIds: selectRows.map(row => row.soDetailId)
          // remark: this.remark
        }).then(res => {
          this.$message.success('Operation Successful')
          this.getList()
        }).finally(() => {
          this.avplLoading = false
        })
      }
    },
    saveRemark() {
      const selectRows = this.$refs.soInfo.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to modify'))
        return false
      } else {
        this.confirmLoading = true
        generatedPO({
          soId: this.$route.params.id,
          soDetailIds: selectRows.map(row => row.soDetailId),
          remark: this.remark
        }).then(res => {
          this.$message.success('Operation Successful')
          this.obligatoryDialog = false
          this.getList()
        }).finally(() => {
          this.confirmLoading = false
        })
      }
    },
    /**
     * 显示设置样品订单页面
     * @returns {boolean}
     */
    showSampleMaterial(){
      const selectRows = this.$refs.soInfo.getCheckboxRecords()
      if (selectRows.length === 0) {
        this.$message.warning(this.$t('Please select the record to set'))
        return false
      } else {
        this.sampleMaterial.sampleMaterialShow=true
        this.sampleMaterial.soId=this.$route.params.id
        this.sampleMaterial.soDetailIds=selectRows.map(row => row.soDetailId)
        this.sampleMaterial.canAuthSuppliers=[]
        this.sampleMaterial.supplierId=null
        this.sampleMaterial.supplier=null
      }
    },
    /**
     * 样品订单保存
     * @constructor
     */
    SetSampleMaterial() {
      this.$refs['sampleMaterialRef'].validate(valid => {
        this.sampleMaterial.supplier = this.sampleMaterial.canAuthSuppliers.find(item => item.id === this.sampleMaterial.supplierId).nameShort
        if (valid) {
          this.confirmLoading=true
          SetSampleMaterial(this.sampleMaterial).then(res => {
            if (res.data){
              this.$message.success('Operation Successful')
              this.sampleMaterial.sampleMaterialShow=false
              this.getList()
            }else{
              this.$message.error('Operation Failed')
            }
          }).finally(() => {
            this.confirmLoading = false
          })
        }
      })
    },
    doListNewAuthSupplier(query) {
      if (query) {
        listNewSupplier(query).then(res => {
          this.sampleMaterial.canAuthSuppliers = res.data
        })
      }
    },
    clearSupplier() {
      this.sampleMaterial.canAuthSuppliers = []
      this.sampleMaterial.supplierId = ''
      this.sampleMaterial.supplier = ''
    },
    // show operate log
    showLogs(businessId, table, column,keyColumns) {
      this.log.open = true
      this.log.businessId = businessId
      this.log.columns = [{
        tableName: table,
        searchColumns: column,
        keyColumns:keyColumns
      }]
    }
  }
}
</script>

<style lang="scss" scoped>
.commonFormItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}
.button-container {
  margin-top: 20px; /* 设置按钮与表格的距离 */
  text-align: right; /* 使按钮靠右对齐 */
}
.overList{
  ::v-deep .el-autocomplete-suggestion{
    width: auto !important;
    min-width: 180px;
    max-width: 600px !important;
  }
}
.searchValue{
  width: 100%;
}
.searchItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
  ::v-deep .el-autocomplete {
    width: 100% !important;
  }
}
::v-deep .avpl-row{
  background:#81D3F8 !important;
}
::v-deep .avpl-edit{
  background:#FFE668 !important;
}

</style>
