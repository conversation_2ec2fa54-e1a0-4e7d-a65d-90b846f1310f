<template>
  <div class="app-container">
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input v-model="queryParams.searchText	" style="flex: 0 1 40%" :placeholder="$t('order.orderNoMaterialCodeMaterialDescription')" clearable />
      <el-button
        v-has-permi="['order:answer:query']"
        type="primary"
        plain
        @click="init"
      >{{ $t('common.search') }}
      </el-button>

      <el-button
        v-has-permi="['order:answer:query']"
        style="margin-left: 0"
        plain
        @click="handleReset"
      >{{ $t('common.reset') }}</el-button>
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button
          v-has-permi="['order:answer:query']"
          type="text"
        >
          {{ $t('common.advancedSearch') }}
        </el-button>
        <i
          class="el-icon-arrow-up"
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>

    </div>
    <!--高级搜索选项-->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="189px">
      <el-form-item class="searchItem" :label="$t('order.orderNumber')" prop="orderNo">
        <el-input v-model="queryParams.orderNo" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('common.buyer')" prop="sourcingIds">
        <el-select v-model="queryParams.sourcingIds" class="searchValue" clearable filterable multiple>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('supplier.supplier')" prop="supplier">
        <el-input v-model="queryParams.supplier" class="searchValue" clearable :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('material.factory')" prop="factoryIds">
        <el-select v-model="queryParams.factoryIds" class="searchValue" clearable multiple>
          <el-option v-for="item in getDictDatas('factory',0)" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.materialCode')" prop="materialCode">
        <el-input v-model="queryParams.materialCode" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.manufacturer')" prop="materialMfg">
        <el-input v-model="queryParams.materialMfg" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('order.answerStatus')" prop="answerStatus">
        <el-select v-model="queryParams.answerStatus" class="searchValue" clearable multiple>
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_ANSWER_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('material.materialDescription')" prop="materialDesc">
        <el-input v-model="queryParams.materialDesc" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('material.manufacturersPartNumber')" prop="materialMpn">
        <el-input v-model="queryParams.materialMpn" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>

      <!--      空item 占位的item-->

      <el-form-item class="searchItem" :label="$t('order.answerDifference')" prop="answerDifference">
        <el-select v-model="queryParams.answerDifference" class="searchValue" clearable>
          <el-option v-for="item in getDictDatas(DICT_TYPE.Order_ANSWER_DIFFERENCE)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('order.timeType')" prop="dateType">
        <el-select v-model="queryParams.dateType" class="searchValue" clearable>
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_DATE_TYPE_IN_ANSWER)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label=" " class="searchItem">
        <el-date-picker
          v-model="queryParams.time"
          class="searchValue"
          type="daterange"
          value-format="yyyy-MM-dd"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('order.dataMark')" prop="mark">
        <el-select
          v-model="queryParams.mark"
          class="searchValue"
          multiple
          @change="handleCheckedProcess"
        >
          <el-option v-for="item in processMark" :label="item.label" :value="item.value">{{ item.label }}</el-option>
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('order.overdueDeliveryAsRequired')">
        <el-select
          v-model="queryParams.required"
          class="searchValue"
          multiple
          clearable
        >
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_OVERDUE_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

      </el-form-item>
      <el-form-item class="searchItem" :label="$t('order.overdueDeliveryAsPromised')">
        <el-select
          v-model="queryParams.promised"
          class="searchValue"
          multiple
          clearable
        >
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_OVERDUE_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <!--      <div style="text-align: center">-->
      <!--        <el-button-->
      <!--          v-has-permi="['order:answer:query']"-->
      <!--          icon="el-icon-search"-->
      <!--          size="mini"-->
      <!--          type="primary"-->
      <!--          plain-->
      <!--          @click="init"-->
      <!--        >{{-->
      <!--          $t('common.search')-->
      <!--        }}-->
      <!--        </el-button>-->

      <!--        <el-button-->
      <!--          v-has-permi="['order:answer:query']"-->
      <!--          icon="el-icon-refresh"-->
      <!--          size="mini"-->
      <!--          @click="handleReset"-->
      <!--        >{{ $t('common.reset') }}</el-button>-->
      <!--      </div>-->

    </el-form>
    <!--    订单行列表维度-->
    <vxe-grid
      ref="deliveryLine"
      class="mytable-style"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
      :header-cell-class-name="headerCellClassName"
    >

      <template #factoryId="{row}">
        <dict-tag :type="'factory'" :value="row.factoryId" />
      </template>
      <template #sourcingId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcingId" />
      </template>
      <template #purchasingUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.purchasingUnit" />
      </template>
      <template #mark="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_PROCESS_MARK" :value="row.mark" />
      </template>
      <template #answerDifference="{row}">
        <dict-tag :type="DICT_TYPE.Order_ANSWER_DIFFERENCE" :value="row.answerDifference" />
      </template>
      <template #overdueDeliveryAsPromised="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_OVERDUE_TYPE" :value="row.overdueDeliveryAsPromised" />
      </template>
      <template #overdueDeliveryAsRequired="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_OVERDUE_TYPE" :value="row.overdueDeliveryAsRequired" />
      </template>
      <template #categoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
      </template>
      <template #shipmentQuantityEdit="{row}">
        <vxe-input v-model.number="row.shipmentQuantity" :min="0" type="number" @blur="validateNumber" />
      </template>
      <template #shipmentQuantity="{row}">
        <number-format :value="row.shipmentQuantity" />
      </template>
      <template #requestDeliveryDateEdit="{row}">
        <vxe-input
          v-model="row.requestDeliveryDate"
          :placeholder="$t('order.dateSelection')"
          type="date"
          transfer
          @click.native="selectAll($event)"
          @change="validateDate"
        />
      </template>
      <template #shipmentDetailRemarkEdit="{row}">
        <vxe-input v-model="row.shipmentDetailRemark" />
      </template>
      <template #answerStatus="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_ANSWER_STATUS" :value="row.answerStatus" />
      </template>
      <template #answerFinishStatus="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_LINE_ANSWER_FINISH_STATUS" :value="row.answerFinishStatus" />
      </template>
      <template #operate="{row, rowIndex }">
        <OperateDropDown
          :menu-item="[
            {
              name: $t('order.newSchedule'),

              show: $store.getters.permissions.includes('order:shipment-detail:create'),
              action: (row) => doCreateShipmentDetail(...row),
              para: [ row, rowIndex ]
            },
            {
              name: $t('order.deleteSchedule'),

              show: row.shipmentDetailId && $store.getters.permissions.includes('order:shipment-detail:delete'),
              action: (row) => deleteShipmentDetail(...row),
              para: [ row, rowIndex ]
            }
          ]
          "
        />
        <!--        <el-button-->
        <!--          v-has-permi="['order:shipment-detail:create']"-->
        <!--          type="text"-->
        <!--          @click="doCreateShipmentDetail(row, rowIndex)"-->
        <!--        >-->
        <!--          {{ $t('order.newSchedule') }}-->
        <!--        </el-button>-->
        <!--        <el-button-->
        <!--          v-show="row.shipmentDetailId"-->
        <!--          v-has-permi="['order:shipment-detail:delete']"-->
        <!--          type="text"-->
        <!--          @click="deleteShipmentDetail(row, rowIndex)"-->
        <!--        >-->
        <!--          {{ $t('order.deleteSchedule') }}-->
        <!--        </el-button>-->
      </template>
      <template #quantity="{row}">
        <number-format :value="row.quantity" />
      </template>
      <template #receiptQuantity="{row}">
        <number-format :value="row.receiptQuantity" />
      </template>
      <template #returnGoodQuantity="{row}">
        <number-format :value="row.returnGoodQuantity" />
      </template>
      <template #deliveringQuantity="{row}">
        <number-format :value="row.deliveringQuantity" />
      </template>
      <template #availableDeliveryQuantity="{row}">
        <number-format :value="row.availableDeliveryQuantity" />
      </template>
      <template #answerQuantity="{row}">
        <number-format :value="row.answerQuantity" />
      </template>
      <template #toolbar_buttons>
        <el-row :gutter="24" style="width: 100%" class="mb8">
          <el-col :span="10" style="display: flex">
            <!--            采购角色采购-->
            <el-button
              v-has-permi="['order:answer:accept']"
              size="mini"
              plain
              type="primary"
              @click="accept"
            >
              {{ $t('order.accept') }}
            </el-button>
            <el-button
              v-has-permi="['order:answer:reject']"
              size="mini"
              plain
              type="danger"
              @click="showReject"
            >
              {{ $t('order.refuse') }}
            </el-button>
            <el-button
              v-has-permi="['order:answer:temporary-receiving']"
              size="mini"
              plain
              type="primary"
              @click="temporaryAccept"
            >
              {{ $t('order.provisionalAcceptance') }}
            </el-button>
            <el-button
              v-has-permi="['order:answer:reconfirm']"
              size="mini"
              plain
              type="primary"
              @click="doReconfirm"
            >
              {{ $t('order.reconfirm') }}
            </el-button>

          </el-col>
          <el-col :span="14">
            <right-toolbar :list-id="girdOption.id" :show-search.sync="showSearch" :custom-columns.sync="girdOption.columns" @queryTable="init" />
            <div style="float:right;margin-right: 10px;">
              <el-button
                v-has-permi="['order:shipment-detail:create']"
                size="mini"
                plain
                type="primary"
                @click="publishShipmentDetail"
              >
                {{ $t('order.publishPlan') }}
              </el-button>
              <!--              <el-button-->
              <!--                v-has-permi="['order:answer:shipmentDetail:batchImport']"-->
              <!--                disabled-->
              <!--                plain-->
              <!--                size="mini"-->
              <!--                type="primary"-->
              <!--                @click="batchImportShipmentDetail"-->
              <!--              >-->
              <!--                {{ $t('order.batchSplitPlan') }}-->
              <!--              </el-button>-->
              <el-button
                v-has-permi="['order:answer:shipmentDetail:import']"
                plain
                size="mini"
                type="primary"
                @click="batchImportShipment"
              >
                {{ $t('order.importPlan') }}
              </el-button>
              <el-button
                v-has-permi="['order:answer:email:notify']"
                size="mini"
                plain
                type="primary"
                @click="emailNotify"
              >
                {{ $t('order.emailReminder') }}
              </el-button>
              <!--             采购角色、供应商角色采购 -->
              <el-button
                v-has-permi="['order:answer:export-excel']"
                size="mini"
                type="primary"
                plain
                @click="exportExcel"
              >
                {{ $t('order.download') }}
              </el-button>

            </div>
          </el-col>
        </el-row>
      </template>

    </vxe-grid>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!--    退回答交弹框-->
    <el-dialog
      v-if="visible"
      width="900px"
      :title="$t('supplier.tips')"
      :modal-append-to-body="false"
      :visible.sync="visible"
    >
      <div style="padding-bottom: 20px">
        {{ $t('order.youAreAboutToRejectTitle') }}
      </div>
      <el-input
        v-model="rejectReason"
        :placeholder="$t('auth.pleaseEnterTheReturnReason')"
        type="textarea"
        :rows="3"
        maxlength="500"
        show-word-limit
      />
      <div slot="footer">
        <el-button @click="visible=false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="doReject">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!-- 计划和导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        <div slot="tip" class="el-upload__tip text-center">
          <span>{{ $t('common.onlyXlsXlsxFormatFilesAreAllowedToBeImported') }}</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="upload.open = false"
        >{{ $t('common.cancel') }}</el-button>
        <el-button
          type="primary"
          plain
          @click="doImportTemplate"
        >  {{ $t('common.downloadTemplate') }}</el-button>
        <el-button
          type="primary"
          @click="submitFileForm"
        >  {{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>

  </div>
</template>
<script>
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import {
  acceptDelivery,
  answerPageExport,
  createShipmentDetail,
  deleteShipmentDetail,
  email,
  importTemplate,
  pagingAnswer,
  reconfirm,
  rejectDelivery,
  temporaryReceiving
} from '@/api/order/answer'
import { parseTime } from '@/utils/ruoyi'
import dayjs from 'dayjs'
import event from '@/views/dashboard/mixins/event'
import { getBaseHeader } from '@/utils/request'
import OperateDropDown from '@/components/OperateDropDown/index.vue'

export default {
  name: 'Answerindex',
  components: { OperateDropDown },
  mixins: [event],
  data() {
    return {
      visible: false,
      rejectReason: '', // 拒绝的退回原因
      showSearch: false,
      queryParams: {
        searchText: '',
        materialCode: '',
        pgIds: '',
        beginOrderDate: '',
        dateType: '',
        answerDifference: '',
        endOrderDate: '',
        factoryIds: [],
        materialDesc: '',
        materialMfg: '',
        materialMpn: '',
        answerStatus: [],
        orderNo: '',
        sortBy: '',
        sortField: '',
        sourcingIds: [],
        supplier: '',
        supplierName: '',
        time: '',
        receiptVoucherNum: '',
        mark: [],
        promised: [],
        required: [],
        pageNo: 1,
        pageSize: 10
      },
      categoryList: [],
      factoryList: [],
      list: [],
      loading: false,
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'delivery',
        rowClassName: this.rowClassName,
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        editRules: {
          shipmentQuantity: [
            {
              required: true,
              message: this.$t('order.pleaseFillIn')
            }
          ],
          requestDeliveryDate: [
            {
              required: true,
              message: this.$t('order.pleaseFillIn')
            }
          ]
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: { isCurrent: true,
          isHover: true
        },
        editConfig: {
          trigger: 'click',
          mode: 'cell',
          beforeEditMethod: this.beforeEditMethod
        },
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          // UFFF-1498： 物料编码移到订单行号后，固定订单号+行号+物料编码三列，同时取消被固定列的显隐配置，默认一直显示+固定，即往右拖动时，物料编码列位置不变
          { title: this.$t('order.orderNumber'), field: 'orderNo', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('order.orderLineNo'), field: 'orderLine', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('material.materialCode'), field: 'materialCode', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('material.materialDescription'), field: 'materialDescription', visible: true, width: 100 },
          { title: this.$t('supplier.shortNameOfSupplier'), field: 'supplierNameShort', visible: true, width: 100 },
          { title: this.$t('order.answerStatus'), slots: { default: 'answerStatus' }, field: 'answerStatus', visible: true, width: 100 },
          { title: this.$t('order.planLineNo'), field: 'shipmentDetailNo', visible: true, width: 100 },
          { title: this.$t('order.requiredArrivalDate'), field: 'requestDeliveryDate', slots: { edit: 'requestDeliveryDateEdit' }, editRender: {}, visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          { title: this.$t('order.requiredArrivalQuantity'), field: 'shipmentQuantity', slots: { edit: 'shipmentQuantityEdit', default: 'shipmentQuantity' }, editRender: {}, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.purchaseRemarks'), field: 'shipmentDetailRemark', slots: { edit: 'shipmentDetailRemarkEdit' }, editRender: {}, visible: true, width: 100 },
          { title: this.$t('order.planUpdateDate'), field: 'shipmentDetailUpdateDate', visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          { title: this.$t('order.answerCompletionStatus'), slots: { default: 'answerFinishStatus' }, field: 'answerFinishStatus', visible: true, width: 100 },
          { title: this.$t('order.answeringBankNo'), field: 'answerNo', visible: true, width: 100 },
          { title: this.$t('order.estimatedDeliveryDate'), field: 'estimateDeliveryDate', visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          { title: this.$t('order.estimatedDeliveryQuantity'), field: 'answerQuantity', slots: { default: 'answerQuantity' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.supplierRemarks'), field: 'supplierRemark', visible: true, width: 100 },
          { title: this.$t('order.answerDifference'), slots: { default: 'answerDifference' }, field: 'answerStatus', visible: true, width: 100 },
          { title: this.$t('common.buyer'), slots: { default: 'sourcingId' }, field: 'sourcingId', visible: true, width: 100 },
          { title: this.$t('material.purchasingUnit'), slots: { default: 'purchasingUnit' }, field: 'purchasingUnit', visible: true, width: 100 },
          { title: this.$t('material.category'), slots: { default: 'categoryId' }, field: 'categoryId', visible: true, width: 100 },
          { title: this.$t('order.orderLineComments'), field: 'remark', visible: true, width: 100 },
          { title: this.$t('material.revision'), field: 'materialVersion', visible: true, width: 100 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
          { title: this.$t('order.dataMark'), field: 'mark', slots: { default: 'mark' }, visible: true, width: 100 },
          { title: this.$t('material.factory'), field: 'factoryId', slots: { default: 'factoryId' }, visible: true, width: 100 },
          { title: this.$t('order.orderQuantity'), field: 'quantity', slots: { default: 'quantity' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.remainingDeliverableQuantityOfTheOrder'), slots: { default: 'availableDeliveryQuantity' }, field: 'availableDeliveryQuantity', visible: true, width: 100, align: 'right' },
          { title: this.$t('order.arrivalQuantity'), field: 'receiptQuantity', slots: { default: 'receiptQuantity' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.returnQuantity'), field: 'returnGoodQuantity', slots: { default: 'returnGoodQuantity' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.quantityInDelivery'), field: 'deliveringQuantity', slots: { default: 'deliveringQuantity' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.overdueDeliveryAsRequired'), slots: { default: 'overdueDeliveryAsRequired' }, field: 'overdueDeliveryAsRequired', visible: true, width: 100 },
          { title: this.$t('order.answerAndUpdateDate'), field: 'answerUpdateDate', visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          { title: this.$t('order.overdueDeliveryAsPromised'), slots: { default: 'overdueDeliveryAsPromised' }, field: 'overdueDeliveryAsPromised', visible: true, width: 100 },
          { field: 'operate', slots: { default: 'operate' }, fixed: 'right', title: this.$t('common.operate'), visible: true, width: 35, showOverflow: false }
        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      processMark: getDictDatas(DICT_TYPE.ORDER_PROCESS_MARK),
      total: 0,
      checkAll: false,
      isIndeterminate: false,
      exportLoading: false,
      shipEditFields: ['shipmentQuantity', 'requestDeliveryDate'],
      errMap: null, // 表单的错误消息
      // 订单导入参数
      upload: {
        // 是否显示弹出层（订单导入）
        open: false,
        // 弹出层标题（订单导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/order/shipment-detail/import'
      }
    }
  },
  computed: {
    stripeIds() {
      return [...new Set(this.list.map(item => item.orderId + item.orderLine))]
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 不同维度的表头颜色区分
    headerCellClassName({ column, columnIndex }) {
      if (column.field === 'shipmentDetailId' || column.field === 'shipmentDetailNo' ||
        column.field === 'shipmentDetailUpdateDate' ||
        column.field === 'shipmentQuantity' ||
        column.field === 'requestDeliveryDate' ||
        column.field === 'shipmentDetailRemark' ||
        column.field === 'overdueDeliveryAsRequired') {
        return 'row-green'
      }
      if (column.field === 'answerId' || column.field === 'answerNo' ||
        column.field === 'answerUpdateDate' ||
        column.field === 'answerQuantity' ||
        column.field === 'estimateDeliveryDate' ||
        column.field === 'supplierRemark' ||
        column.field === 'overdueDeliveryAsPromised') {
        return 'col-blue'
      }
      return null
    },
    init() {
      // 跨页后使用前面页数据进行搜索，此时未重置pageNo导致搜索结果为空
      this.queryParams.pageNo = 1
      this.getList()
    },
    // 获取列表数据
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      pagingAnswer(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
        // this.queryParams.pageSize = this.list?.length
      })
    },
    // 校验答交、计划的数量
    validateNumber(val) {
      if (Number(val.value) <= 0) {
        this.init()
        this.$message.error(this.$t('order.currentlyOnlyNumbersGreaterThanAreSupported'))
      }
    },
    // 校验答交、计划的日期
    validateDate(val) {
      if (val && dayjs(val.value).isBefore(dayjs(), 'day')) {
        this.init()
        this.$message.error(this.$t('order.theDateValueMustBeGreaterThanTheCurrentDay'))
      }
    },
    // 发布计划功能
    // 验证同一订单行下，所有计划行的要求到货数量之和必须等于此订单行的订单数量。
    // 发布后，将状态置为【待答交】
    async publishShipmentDetail() {
      const data = this.$refs.deliveryLine.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      // 校验必填项
      const errMap = await this.$refs.deliveryLine.validate(data).catch(errMap => errMap)
      if (errMap) {
        this.$message.error(this.$t('order.pleaseEnterRequiredItemsInTheForm'))
        return
      }
      this.$confirm(this.$t('order.youAreAboutToPublishTheCheckedOrderDataPleaseConfirm'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        const details = []
        this.list.filter(j => data.map(item => item.id).includes(j.id)).map(a => {
          const detail = details.find(i => i.orderDetailId === a.id)
          const shipment = {
            id: a.shipmentDetailId,
            orderId: a.orderId,
            orderDetailId: a.id,
            lineNo: a.shipmentDetailNo,
            shipmentQuantity: a.shipmentQuantity,
            remark: a.shipmentDetailRemark,
            requestDeliveryDate: a.requestDeliveryDate
          }
          if (detail) {
            if (a.shipmentQuantity) {
              detail.shipmentList.push(shipment)
            }
          } else {
            details.push({
              orderId: a.orderId,
              orderDetailId: a.id,
              shipmentList: a.shipmentQuantity ? [shipment] : []
            })
          }
        })

        createShipmentDetail(details).then(() => {
          this.init()
          this.$message({
            type: 'success',
            message: this.$t('order.operationSucceeded')
          })
        })
      })
    },
    // 邮件提醒功能
    // 仅 答交状态为 待答交的订单行数据支持发送
    // 【UFFF-1499】暂时增加 答交退回状态允许邮件提醒
    emailNotify() {
      const data = this.$refs.deliveryLine.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.$confirm(this.$t('order.youAreGoingToUrgeTheSupplierToSubmitTheDeliveryPlanPleaseConfirm'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        if (data.filter(i => i.answerStatus !== 'waiting_answer' && i.answerStatus !== 'reject_answer').length > 0) {
          this.$message.warning(this.$t('order.onlyOrderDataInPendingStatusIsSupported'))
          return
        }
        const ids = [...new Set(data.map(item => item.answerId))]
        email(ids).then(() => {
          this.init()
          this.$message({
            type: 'success',
            message: this.$t('order.operationSucceeded')
          })
        })
      })
    },
    // 重新确认
    // 【UFFF-2015】操作维度更新为答交行
    async doReconfirm() {
      const data = this.$refs.deliveryLine.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.$confirm(this.$t('order.youAreAboutToReconfirmTheSelectedOrderDataPleaseConfirm'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        reconfirm(data.map(item => item.answerId)).then(() => {
          this.init()
          this.$message({
            type: 'success',
            message: this.$t('order.operationSucceeded')
          })
        })
      })
    },
    // 临时接受功能
    // 【UFFF-2015】操作维度更新为答交行
    async temporaryAccept() {
      const data = this.$refs.deliveryLine.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.$confirm(this.$t('order.youAreAboutToTemporarilyAcceptTheCheckedOrderDataPleaseConfirm'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        temporaryReceiving(data.map(item => item.answerId)).then(() => {
          this.init()
          this.$message({
            type: 'success',
            message: this.$t('order.operationSucceeded')
          })
        })
      })
    },
    // 拒绝功能
    // 【UFFF-1754】拒绝交期时需要有弹框输入退回意见，并更新到采购备注一列
    // 【UFFF-2015】操作维度更新为答交行
    async doReject() {
      const data = this.$refs.deliveryLine.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      rejectDelivery({
        answerIds: data.map(item => item.answerId),
        rejectReason: this.rejectReason,
        shipmentDetailIds: data.map(i => i.shipmentDetailId)
      }).then(() => {
        this.visible = false
        this.rejectReason = ''
        this.init()
        this.$message({
          type: 'success',
          message: this.$t('order.operationSucceeded')
        })
      })
    },
    // 接受功能
    // 【UFFF-2015】操作维度更新为答交行
    async accept() {
      const data = this.$refs.deliveryLine.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.$confirm(this.$t('order.youAreAboutToAcceptTheCheckedOrderDataPleaseConfirm'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        acceptDelivery(data.map(item => item.answerId)).then(() => {
          this.init()
          this.$message({
            type: 'success',
            message: this.$t('order.operationSucceeded')
          })
        })
      })
    },
    // 行新建计划功能
    doCreateShipmentDetail(row, rowIndex) {
      // 插入当前订单行#计划行的最后一行
      //  1.计划行行数小于答交行，那么不允许创建，直接点击现有的行进行新建
      //  2.计划行行数大于等于答交行，允许创建，当前订单行的最后一行插入新行
      // 3.【UFFF-1401】2023/1/12 基于【1】的控制需要在订单号、订单行维度上进行过滤
      let planCount = 0; let answerCount = 0
      this.list.filter(item => item.orderNo === row.orderNo && item.orderLine === row.orderLine).forEach(item => {
        if (item.shipmentDetailId) {
          planCount++
        }
        if (item.answerId) {
          answerCount++
        }
      })
      if (planCount < answerCount) {
        this.$message.error(this.$t('order.pleaseAddDirectlyOnTheLatestBlankLine'))
        return
      }
      // 最新的计划行号
      // const currentOrderArr = this.list.filter(item => item.id === row.id)
      // const shipmentDetailNo = Math.max(...currentOrderArr.map(item => item.shipmentDetailNo))
      const newItem = {
        orderNo: row.orderNo,
        orderLine: row.orderLine, // 订单行号
        // shipmentDetailNo: shipmentDetailNo + 1,
        materialCode: row.materialCode,
        materialDescription: row.materialDescription,
        shortNameOfSupplier: row.supplierNameShort,
        orderId: row.orderId,
        id: row.id
      }
      // 找出当前订单行的最后一个数据，找出其索引
      var filter = this.list.filter(item => item.id === row.id)
      var lastRow = filter[filter.length - 1]
      var lastIndex = this.$refs.deliveryLine.getRowIndex(lastRow)
      this.list.splice(lastIndex + 1, 0, newItem)
      this.calculateTotal(1, true)
    },
    // 行删除计划功能
    deleteShipmentDetail(row, rowIndex) {
      this.$confirm(this.$t('order.youAreAboutToDeleteTheCurrentOrderDataPleaseConfirm'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        if (row.shipmentDetailId) {
          // 当前所属的最后一行数据不允许删除
          if (this.list.filter(item => item.id === row.id).map(item => item.shipmentDetailId).filter(item => item).length === 1) {
            this.$message.error(this.$t('order.atLeastOnePlanLineShallBeReservedForTheOrderLine'))
            return
          }
          deleteShipmentDetail(row.shipmentDetailId).then(() => {
            this.init()
            this.$message({
              type: 'success',
              message: this.$t('order.operationSucceeded')
            })
          })
        } else {
          var index = this.$refs.deliveryLine.getRowIndex(row)
          this.list.splice(index, 1)
          this.$message.success(this.$t('order.operationSucceeded'))
        }
        this.init()
      })
    },
    // 重新计算当前列表的total
    calculateTotal(num, add) {
      if (add) {
        this.total = this.total + num
      } else {
        this.total = this.total - num
      }
    },
    // todo 批量拆计划功能 P2
    batchImportShipmentDetail() {

    },
    // 导入计划
    // 针对雪榕的订单对接没有计划行，但又希望使用计划协同。因此通过此功能维护计划和
    batchImportShipment() {
      this.upload.title = this.$t('order.planLineImport')
      this.upload.open = true
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    /** 下载模板操作 */
    doImportTemplate() {
      importTemplate().then(response => {
        this.$download.excel(response, '计划行导入模板.xlsx')
      })
    },
    // 文件上传中处理
    handleFileUploadProgress() {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createOrders) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createOrders.length
      }
      if (data.failureOrders) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureOrders).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" target="_blank" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.init()
    },
    handleReset() {
      this.queryParams = {
        orderDetailId: undefined,
        searchText: '',
        materialCode: '',
        pgIds: '',
        beginOrderDate: '',
        dateType: '',
        answerDifference: '',
        endOrderDate: '',
        factoryIds: [],
        materialDesc: '',
        materialMfg: '',
        materialMpn: '',
        answerStatus: [],
        orderNo: '',
        sortBy: '',
        sortField: '',
        sourcingIds: [],
        supplier: '',
        time: '',
        receiptVoucherNum: '',
        mark: [],
        promised: [],
        required: [],
        pageNo: 1,
        pageSize: 10
      }
    },
    // 答交列表下载
    exportExcel() {
      this.$modal.confirm(this.$t('order.areYouSureYouWantToExportAllDataItemsOfTheAnsweringBank')).then(() => {
        this.exportLoading = true
        //  UFFF-1501-导出调整字段
        return answerPageExport(this.queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('order.answeringxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    // 进入行编辑前的判断方法
    // extra1- 编辑答交行维度，如果答交行行号为空，则默认填充
    // extra2- 编辑计划行维度，如果计划行行号为空，则默认填充
    // extra3- 如存在错误，则赋值errMap，交由其他主方法判断
    async beforeEditMethod({ row, rowIndex, column, columnIndex }) {
      // const $table = this.$refs.deliveryLine
      // this.errMap = await $table.validate([row]).catch(errMap => errMap)
      // 1.新建计划时，全部状态的计划行数据都可以编辑answerStatus
      if (this.shipEditFields.includes(column.field) || row.answerStatus === 'waiting_answer') {
        // if (!row.shipmendDetailId && !row.shipmentDetailNo) {
        //   const maxNo = Math.max(...this.list.filter(item => item.id === row.id).map(item => item.shipmentDetailNo))
        //   if (this.shipEditFields.includes(column.field)) {
        //     row.shipmentDetailNo = maxNo + 1
        //   }
        // }
        // 行号改为后端生成
        return true
      }
    },
    handleCheckAllChange(val) {
      this.queryParams.mark = val ? this.processMark.map(item => item.value) : []
      this.isIndeterminate = false
    },
    handleCheckedProcess(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.processMark.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.processMark.length
    },
    rowClassName({ row }) {
      // 双数行为灰色
      if (this.stripeIds.indexOf(row.orderId + row.orderLine) % 2 !== 0) {
        return 'gray-row'
      }
    },
    selectAll(e) {
      e.target.select()
    },
    showReject() {
      const data = this.$refs.deliveryLine.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.visible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.searchItem{
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 189px);
  }
}
::v-deep .gray-row{
  background: #f1f2f6;;
}
.searchTimeItem{
  margin-right: 0;
  ::v-deep .el-form-item__content{
    width: calc(100% - 82px);
  }
}

.searchValue{
  width: 95%;
}

::v-deep .mytable-style .vxe-header--column.row-green {
  background-color: #59b8c0;
  color: #fff;
}
::v-deep .mytable-style .vxe-header--column.col-blue {
  background-color: #5aa8a6;
  color: #fff;
}

</style>
