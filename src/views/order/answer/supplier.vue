<template>
  <div class="app-container">
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input v-model="queryParams.searchText	" style="flex: 0 1 40%" :placeholder="$t('order.orderNoMaterialCodeMaterialDescription')" clearable @keyup.enter.native="handleQuery" />
      <el-button
        v-has-permi="['order:answer:query']"
        type="primary"
        plain
        @click="init"
      >{{ $t('common.search') }}</el-button>
      <el-button
        v-has-permi="['order:answer:query']"
        style="margin-left: 0"
        plain
        @click="handleReset"
      >{{ $t('common.reset') }}</el-button>
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button
          v-has-permi="['order:answer:query']"
          type="text"
        >
          {{ $t('common.advancedSearch') }}
        </el-button>
        <i
          class="el-icon-arrow-up"
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>

    </div>
    <!--高级搜索选项-->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="189px">
      <el-form-item class="searchItem" :label="$t('order.orderNumber')" prop="orderNo">
        <el-input v-model="queryParams.orderNo" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('common.buyer')" prop="sourcingIds">
        <el-select v-model="queryParams.sourcingIds" class="searchValue" clearable filterable multiple>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('supplier.supplier')" prop="supplier">
        <el-input v-model="queryParams.supplier" class="searchValue" clearable :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.factory')" prop="factoryIds">
        <el-select v-model="queryParams.factoryIds" class="searchValue" clearable multiple>
          <el-option v-for="item in getDictDatas('factory',0)" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.materialCode')" prop="materialCode">
        <el-input v-model="queryParams.materialCode" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.manufacturer')" prop="materialMfg">
        <el-input v-model="queryParams.materialMfg" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('common.status')" prop="answerStatus">
        <el-select v-model="queryParams.answerStatus" class="searchValue" clearable multiple>
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_ANSWER_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('material.materialDescription')" prop="materialDesc">
        <el-input v-model="queryParams.materialDesc" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('material.manufacturersPartNumber')" prop="materialMpn">
        <el-input v-model="queryParams.materialMpn" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('order.answerDifference')" prop="answerDifference">
        <el-select v-model="queryParams.answerDifference" class="searchValue" clearable>
          <el-option v-for="item in getDictDatas(DICT_TYPE.Order_ANSWER_DIFFERENCE)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('order.timeType')" prop="dateType">
        <el-select v-model="queryParams.dateType" class="searchValue" clearable>
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_DATE_TYPE_IN_ANSWER)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label=" " class="searchItem">
        <el-date-picker
          v-model="queryParams.time"
          class="searchValue"
          type="daterange"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('order.dataMark')" prop="mark">
        <el-select
          v-model="queryParams.mark"
          multiple
          class="searchValue"
          @change="handleCheckedProcess"
        >
          <el-option v-for="item in processMark" :label="item.label" :value="item.value">{{ item.label }}</el-option>
        </el-select>

      </el-form-item>

      <el-form-item class="searchItem" :label="$t('order.overdueDeliveryAsRequired')">
        <el-select
          v-model="queryParams.required"
          class="searchValue"
          multiple
          clearable
        >
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_OVERDUE_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

      </el-form-item>
      <el-form-item class="searchItem" :label="$t('order.overdueDeliveryAsPromised')">
        <el-select
          v-model="queryParams.promised"
          class="searchValue"
          multiple
          clearable
        >
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_OVERDUE_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

    </el-form>
    <!--    订单行列表维度-->
    <vxe-grid
      ref="deliveryLine"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
    >
      <template #factoryId="{row}">
        <dict-tag :type="'factory'" :value="row.factoryId" />
      </template>
      <template #sourcingId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcingId" />
      </template>
      <template #purchasingUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.purchasingUnit" />
      </template>
      <template #mark="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_PROCESS_MARK" :value="row.mark" />
      </template>
      <template #answerDifference="{row}">
        <dict-tag :type="DICT_TYPE.Order_ANSWER_DIFFERENCE" :value="row.answerDifference" />
      </template>
      <template #overdueDeliveryAsPromised="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_OVERDUE_TYPE" :value="row.overdueDeliveryAsPromised" />
      </template>
      <template #overdueDeliveryAsRequired="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_OVERDUE_TYPE" :value="row.overdueDeliveryAsRequired" />
      </template>
      <template #categoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
      </template>
      <template #answerStatus="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_ANSWER_STATUS" :value="row.answerStatus" />
      </template>
      <template #answerQuantityEdit="{row}">
        <vxe-input v-model.number="row.answerQuantity" :min="0" type="integer" @blur="validateNumber" />
      </template>
      <template #answerQuantity="{row}">
        <number-format :value="row.answerQuantity" />
      </template>
      <template #estimateDeliveryDateEdit="{row}">
        <vxe-input
          v-model="row.estimateDeliveryDate"
          :placeholder="$t('order.dateSelection')"
          type="date"
          transfer
          @click.native="selectAll($event)"
          @change="validateDate"
        />
      </template>
      <template #supplierRemarkEdit="{row}">
        <vxe-input v-model="row.supplierRemark" />
      </template>
      <template #quantity="{row}">
        <number-format :value="row.quantity" />
      </template>
      <template #receiptQuantity="{row}">
        <number-format :value="row.receiptQuantity" />
      </template>
      <template #returnGoodQuantity="{row}">
        <number-format :value="row.returnGoodQuantity" />
      </template>
      <template #deliveringQuantity="{row}">
        <number-format :value="row.deliveringQuantity" />
      </template>
      <template #availableDeliveryQuantity="{row}">
        <number-format :value="row.availableDeliveryQuantity" />
      </template>
      <template #shipmentQuantity="{row}">
        <number-format :value="row.shipmentQuantity" />
      </template>
      <template #operate="{row}">
        <OperateDropDown
          :menu-item="[
            {
              name: $t('order.newAnswer'),
              show: $store.getters.permissions.includes('order:answer:create'),
              action: (row) => createAnswer(row),
              para: row
            },
            {
              name: $t('order.deleteAnswer'),
              show: $store.getters.permissions.includes('order:answer:delete'),
              disabled: !['waiting_answer', 'reject_answer'].includes(row.answerStatus),
              action: (row) => doDeleteAnswer(row),
              para: row
            },
            {
              name: $t('order.submitAndAnswer'),
              show: $store.getters.permissions.includes('order:answer:submit-answer'),
              disabled: !['waiting_answer', 'reject_answer', null].includes(row.answerStatus),
              action: (row) => doSubmitAnswers(...row),
              para: [row,'rowType']
            }
          ]"
        />

      </template>
      <template #toolbar_buttons>
        <el-row :gutter="24" style="width: 100%" class="mb8">
          <el-col :span="10" style="display: flex">
            <el-button
              v-has-permi="['order:answer:submit-answer']"
              size="mini"
              type="primary"
              @click="doSubmitAnswers"
            >
              {{ $t('order.submitAndAnswer') }}
            </el-button>
            <el-button
              v-has-permi="['order:answer:submit-answer']"
              size="mini"
              type="primary"
              plain
              @click="handleBatchImportAnswer"
            >
              {{ $t('order.batchAnswer') }}
            </el-button>
            <el-button
              v-has-permi="['order:answer:export-excel']"
              size="mini"
              type="primary"
              plain
              @click="exportExcel"
            >
              {{ $t('order.download') }}
            </el-button>
          </el-col>
          <el-col :span="14">
            <right-toolbar :list-id="girdOption.id" :show-search.sync="showSearch" :custom-columns.sync="girdOption.columns" @queryTable="init" />
          </el-col>
        </el-row>
      </template>

    </vxe-grid>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          class="small-padding"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="exportBatchImportAnswerTemplateData"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="doBatchAnswerSubmit"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import {
  pagingAnswer,
  answerPageExport, deleteAnswer, submitAnswers, exportBatchAnswerExcel
} from '@/api/order/answer'
import { parseTime } from '@/utils/ruoyi'
import { getBaseHeader } from '@/utils/request'
import dayjs from 'dayjs'
import event from '@/views/dashboard/mixins/event'
import OperateDropDown from '@/components/OperateDropDown/index.vue'

export default {
  name: 'Answersupplierindex',
  components: { OperateDropDown },
  mixins: [event],
  data() {
    return {
      showSearch: false,
      queryParams: {
        searchText: '',
        answerStatus: [],
        materialCode: '',
        pgIds: '',
        beginOrderDate: '',
        dateType: '',
        deliveryStatus: '',
        endOrderDate: '',
        factoryIds: '',
        materialDesc: '',
        materialMfg: '',
        materialMpn: '',
        orderNo: '',
        sortBy: '',
        sortField: '',
        sourcingIds: '',
        supplier: '',
        time: '',
        receiptVoucherNum: '',
        mark: [],
        promised: [],
        required: [],
        pageNo: 1,
        pageSize: 10
      },
      categoryList: [],
      factoryList: [],
      list: [],
      loading: false,
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'delivery',
        maxHeight: 700,
        rowClassName: this.rowClassName,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        editConfig: {
          trigger: 'click',
          mode: 'cell',
          beforeEditMethod: this.beforeEditMethod
        },
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          // UFFF-1498： 物料编码移到订单行号后，固定订单号+行号+物料编码三列，同时取消被固定列的显隐配置，默认一直显示+固定，即往右拖动时，物料编码列位置不变
          { title: this.$t('order.orderNumber'), field: 'orderNo', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('order.orderLineNo'), field: 'orderLine', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('material.materialCode'), field: 'materialCode', visible: true, width: 100, fixed: 'left' },

          // 答交行可编辑列开始
          { title: this.$t('order.estimatedDeliveryQuantity'),
            headerClassName: 'orange',
            field: 'answerQuantity',
            slots: { edit: 'answerQuantityEdit', default: 'answerQuantity' },
            editRender: {},
            visible: true,
            width: 100,
            align: 'right' },
          { title: this.$t('order.estimatedDeliveryDate'),
            headerClassName: 'orange',
            field: 'estimateDeliveryDate',
            slots: { edit: 'estimateDeliveryDateEdit' },
            editRender: {},
            visible: true,
            width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          { title: this.$t('order.supplierRemarks'),
            headerClassName: 'orange',
            field: 'supplierRemark',
            slots: { edit: 'supplierRemarkEdit' },
            editRender: {},
            visible: true,
            width: 100 },
          { title: this.$t('order.answeringBankNo'), field: 'answerNo', visible: true, width: 100 },
          { title: this.$t('order.answerAndUpdateDate'), field: 'answerUpdateDate', visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          // 新
          { title: this.$t('order.orderLineComments'), field: 'remark', visible: true, width: 100 },
          { title: this.$t('order.answerStatus'), slots: { default: 'answerStatus' }, field: 'answerStatus', visible: true, width: 100 },
          { title: this.$t('order.answerDifference'), slots: { default: 'answerDifference' }, field: 'answerStatus', visible: true, width: 100 },
          { title: this.$t('common.buyer'), slots: { default: 'sourcingId' }, field: 'sourcingId', visible: true, width: 100 },
          { title: this.$t('material.materialDescription'), field: 'materialDescription', visible: true, width: 100 },
          { title: this.$t('common.loginCategory'), slots: { default: 'categoryId' }, field: 'categoryId', visible: true, width: 100 },
          { title: this.$t('material.purchasingUnit'), slots: { default: 'purchasingUnit' }, field: 'purchasingUnit', visible: true, width: 100 },
          { title: this.$t('material.revision'), field: 'materialVersion', visible: true, width: 100 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
          { title: this.$t('supplier.shortNameOfSupplier'), field: 'supplierNameShort', visible: true, width: 100 },

          { title: this.$t('order.dataMark'), field: 'mark', slots: { default: 'mark' }, visible: true, width: 100 },
          { title: this.$t('material.factory'), field: 'factoryId', slots: { default: 'factoryId' }, visible: true, width: 100 },
          { title: this.$t('order.orderQuantity'), field: 'quantity', slots: { default: 'quantity' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.arrivalQuantity'), field: 'receiptQuantity', slots: { default: 'receiptQuantity' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.returnQuantity'), field: 'returnGoodQuantity', slots: { default: 'returnGoodQuantity' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.quantityInDelivery'), field: 'deliveringQuantity', slots: { default: 'deliveringQuantity' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.remainingDeliverableQuantityOfTheOrder'), field: 'availableDeliveryQuantity', slots: { default: 'availableDeliveryQuantity' }, visible: true, width: 100, align: 'right' },

          // 计划行开始
          { title: this.$t('order.planLineId'), field: 'shipmentDetailId', visible: false, width: 100 },
          { title: this.$t('order.planLineNo'), field: 'shipmentDetailNo', visible: true, width: 100 },
          { title: this.$t('order.planUpdateDate'), field: 'shipmentDetailUpdateDate', visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          { title: this.$t('order.requiredArrivalQuantity'), field: 'shipmentQuantity', slots: { default: 'shipmentQuantity' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.requiredArrivalDate'), field: 'requestDeliveryDate', visible: true, width: 100,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          { title: this.$t('order.purchaseRemarks'), field: 'shipmentDetailRemark', visible: true, width: 100 },
          { title: this.$t('order.overdueDeliveryAsRequired'), slots: { default: 'overdueDeliveryAsRequired' }, field: 'overdueDeliveryAsRequired', visible: true, width: 100 },
          // 计划行结束

          // 答交行开始
          { title: this.$t('order.answeringBankId'), field: 'answerId', visible: false, width: 100 },
          { title: this.$t('order.overdueDeliveryAsPromised'), slots: { default: 'overdueDeliveryAsPromised' }, field: 'overdueDeliveryAsPromised', visible: true, width: 100 },
          // 答交行结束
          { field: 'operate', slots: { default: 'operate' }, fixed: 'right', title: this.$t('common.operate'), visible: true, width: 35, showOverflow: false }
        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      processMark: getDictDatas(DICT_TYPE.ORDER_PROCESS_MARK),
      total: 0,
      checkAll: false,
      isIndeterminate: false,
      exportLoading: false,
      answerEditFields: ['answerQuantity', 'estimateDeliveryDate'],
      upload: {
        open: false,
        title: '',
        isUploading: false,
        headers: getBaseHeader(),
        url: process.env.VUE_APP_BASE_API + '/admin-api/order/answer/import'
      },
      errMap: null // 表单的错误消息
    }
  },
  computed: {
    stripeIds() {
      return [...new Set(this.list.map(item => item.orderId + item.orderLine))]
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      // 跨页后使用前面页数据进行搜索，此时未重置pageNo导致搜索结果为空
      this.queryParams.pageNo = 1
      this.getList()
    },
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0]
        this.queryParams.endDate = this.queryParams.time[1]
      }
      pagingAnswer(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    // 提交答交功能
    // 【UFFF-1602】支持答交行数据进行提交答交
    async doSubmitAnswers(row, flag) {
      var data = []
      if (flag === 'rowType') {
        // 答交行操作列- 提交答交 触发
        data.push(row)
      } else {
        // 顶部 提交答交按钮触发
        data = this.$refs.deliveryLine.getCheckboxRecords()
      }
      // 校验必填项
      const errMap = await this.$refs.deliveryLine.validate(data).catch(errMap => errMap)
      if (errMap) {
        this.$message.error(this.$t('order.pleaseEnterRequiredItemsInTheForm'))
        return
      }
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      // 2023/1/3 UFFF-1255:[订单答交行必填项问题]
      // 必填项设定逻辑改为，仅当用户输入【预计交货数量】或【预计交货日期】其中一个后，要求输入另一个字段。
      // 如果这两个字段都未输入，则允许提交，并将对应的计划行的【要求到货数量】，【要求到货日期】，赋值给这俩字段
      for (let i = 0; i < data.length; i++) {
        let emptyVal = 0
        if (!data[i].answerQuantity) {
          emptyVal++
        }
        if (!data[i].estimateDeliveryDate) {
          emptyVal++
        }
        if (emptyVal === 1) {
          this.$message.warning(this.$t('order.PleaseSureToCompleteDeliveryQuantityAndDeliveryDate'))
          return
        }
      }
      this.$confirm(this.$t('order.youAreAboutToPublishTheCheckedOrderDataPleaseConfirm'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        if (data.filter(item => item.answerStatus !== 'waiting_answer' && item.answerStatus !== 'reject_answer').length > 0) {
          this.$message.warning(this.$t('order.onlyDataInPendingAndReturnedStatusCanBeOperated'))
          return
        }

        // 待答交的答交行集合
        const details = [] // 订单行集合
        for (const item of data) {
          const temp = details.find(a => a.orderDetailId === item.id)
          // 2023/1/3 UFFF-1255:[订单答交行必填项问题]
          if (!item.answerQuantity && !item.estimateDeliveryDate) {
            item.answerQuantity = item.shipmentQuantity
            item.estimateDeliveryDate = item.requestDeliveryDate
          }
          if (temp) {
            temp.answerBaseVOList.push({
              id: item.answerId,
              orderId: item.orderId,
              orderDetailId: item.id,
              answerNo: item.answerNo,
              answerQuantity: item.answerQuantity,
              remark: item.supplierRemark,
              requestDeliveryDate: item.estimateDeliveryDate
            })
          } else {
            details.push({
              orderId: item.orderId,
              orderDetailId: item.id,
              answerBaseVOList: [{
                id: item.answerId,
                orderId: item.orderId,
                orderDetailId: item.id,
                answerNo: item.answerNo,
                answerQuantity: item.answerQuantity,
                remark: item.supplierRemark,
                requestDeliveryDate: item.estimateDeliveryDate
              }]
            })
          }
        }

        submitAnswers(details).then(res => {
          this.init()
          this.$message({
            type: 'success',
            message: this.$t('order.operationSucceeded')
          })
        })
      })
    },
    // 行新建答交功能
    // 插入当前订单行#计划行的最后一行
    // 1.答交行行数小于计划行，那么不允许创建，直接点击现有的行进行新建
    // 2.答交行行数大于等于计划行，允许创建，插入新行
    // 3.【UFFF-1372】2023/1/12
    createAnswer(row, rowIndex) {
      let planCount = 0; let answerCount = 0
      var data = this.list.filter(item => item.orderNo === row.orderNo && item.orderLine === row.orderLine)

      data.forEach(item => {
        if (item.shipmentDetailId) {
          planCount++
        }
        if (item.answerId) {
          answerCount++
        }
      })
      if (planCount > answerCount) {
        // 允许空的答交行已经填写了数量和日期后进行新建
        if (data.filter(i => !i.answerQuantity || !i.estimateDeliveryDate)) {
          this.$message.error(this.$t('order.pleaseAddDirectlyOnTheLatestBlankLine'))
          return
        }
      }
      // 新建答交应该以答交行数量是否大于订单行数量进行卡控
      var sumAnswerQuantity = 0
      for (let i = 0; i < data.length; i++) {
        sumAnswerQuantity = sumAnswerQuantity + data[i].answerQuantity
      }
      if (sumAnswerQuantity >= row.quantity) {
        this.$message.error(this.$t('答交交付数量总和超过订单行数量,不支持新建答交'))
        return
      }

      const currentOrderArr = this.list.filter(item => item.id === row.id)
      // 最新的答交行号
      const latestNo = Math.max(...currentOrderArr.map(item => item.answerNo))
      const newItem = {
        orderNo: row.orderNo,
        orderLine: row.orderLine, // 订单行号
        answerStatus: 'waiting_answer',
        answerNo: latestNo + 1,
        orderId: row.orderId,
        id: row.id
      }
      // 每次插入到订单行的末尾
      var filter = this.list.filter(item => item.id === row.id)
      var lastRow = filter[filter.length - 1]
      var lastIndex = this.$refs.deliveryLine.getRowIndex(lastRow)
      this.list.splice(lastIndex + 1, 0, newItem)
      this.calculateTotal(1, true)
    },
    // 重新计算当前列表的total
    calculateTotal(num, add) {
      if (add) {
        this.total = this.total + num
      } else {
        this.total = this.total - num
      }
    },
    // 行删除答交功能
    // 【UFFF-1370】2023/1/12 删除答交仅能删除 待答交、答交退回的单据
    doDeleteAnswer(row, rowIndex) {
      this.$confirm(this.$t('order.youAreAboutToDeleteTheCurrentOrderDataPleaseConfirm'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        if (this.list.filter(item => item.id === row.id && item.orderLine === row.orderLine).length === 1) {
          this.$message.error(this.$t('order.theOrderLineShallReserveAtLeastOneReplyBank'))
          return
        }
        if (row.answerId) {
          // 当前所属的最后一行数据不允许删除
          deleteAnswer(row.answerId).then(() => {
            this.init()
            this.$message({
              type: 'success',
              message: this.$t('order.operationSucceeded')
            })
          })
        } else {
          var index = this.$refs.deliveryLine.getRowIndex(row)
          this.list.splice(index, 1)
          this.$message.success(this.$t('order.operationSucceeded'))
          this.init()
        }
        this.calculateTotal(1, false)
      })
    },
    handleReset() {
      this.queryParams = {
        searchText: '',
        materialCode: '',
        pgIds: '',
        beginOrderDate: '',
        dateType: '',
        deliveryStatus: '',
        endOrderDate: '',
        factoryIds: [],
        materialDesc: '',
        materialMfg: '',
        materialMpn: '',
        answerStatus: [],
        orderNo: '',
        sortBy: '',
        sortField: '',
        sourcingIds: [],
        supplier: '',
        time: '',
        receiptVoucherNum: '',
        mark: [],
        promised: [],
        required: [],
        pageNo: 1,
        pageSize: 10
      }
      this.init()
    },
    // 打开批量答交的dialog
    handleBatchImportAnswer() {
      this.upload.title = '批量答交'
      this.upload.open = true
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.okNum) {
        text += this.$t('order.numberOfSuccessfulImports') + '：' + data.okNum
      }
      if (data.failureNum) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + data.failureNum
      }
      if (data.skipNum) {
        text += '<br />' + this.$t('order.totalDetected') + data.skipNum + this.$t('order.theRowDataStatusMsg')
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    /**
     * 批量答交功能-导出勾选的模板数据
     * [UFFF-3731]主版本的用户不确定，存在1对1答交，多答交，少答交多种场景，excel处理复杂多变
     * 解决方案：主版本首选满足客户需求的最大公约数，即1对1答交，其他模式做项目单独开发改造
     * 1.当前页勾选下载
     */
    exportBatchImportAnswerTemplateData() {
      var data = this.$refs.deliveryLine.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        this.upload.open = false
        return
      }
      exportBatchAnswerExcel({
        answerIds: data.map(i => i.answerId).join(',')
      }).then(response => {
        this.$download.excel(response, '供应商批量答交数据.xlsx')
      })
    },
    // 批量答交提交
    doBatchAnswerSubmit() {
      this.$refs.upload.submit()
    },
    // 答交列表下载
    exportExcel() {
      this.$modal.confirm(this.$t('order.areYouSureYouWantToExportAllDataItemsOfTheAnsweringBank')).then(() => {
        this.exportLoading = true
        return answerPageExport(this.queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('order.answeringxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    // 校验答交、计划的数量
    validateNumber(val) {
      if (val && Number(val.value) <= 0) {
        this.$message.error(this.$t('order.currentlyOnlyNumbersGreaterThanAreSupported'))
      }
    },
    // 校验答交、计划的日期
    validateDate(val) {
      if (val && dayjs(val.value).isBefore(dayjs(), 'day')) {
        this.$message.error(this.$t('order.theDateValueMustBeGreaterThanTheCurrentDay'))
      }
    },
    // 进入行编辑前的判断方法
    // extra1- 如存在错误，则赋值errMap，交由其他主方法判断
    async beforeEditMethod({ row, rowIndex, column, columnIndex }) {
      const $table = this.$refs.deliveryLine
      this.errMap = await $table.validate([row]).catch(errMap => errMap)
      if (this.answerEditFields.includes(column.field) || row.answerStatus === 'waiting_answer') {
        if (!row.answerId && !row.answerNo) {
          const maxNo = Math.max(...this.list.filter(item => item.id === row.id).map(item => item.answerNo))
          // 计划行
          if (this.answerEditFields.includes(column.field)) {
            row.answerNo = maxNo + 1
          }
          if (!row.answerStatus) {
            row.answerStatus = 'waiting_answer'
          }
        }
        return true
      }
    },
    handleCheckAllChange(val) {
      this.queryParams.mark = val ? this.processMark.map(item => item.value) : []
      this.isIndeterminate = false
    },
    handleCheckedProcess(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.processMark.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.processMark.length
    },
    rowClassName({ row }) {
      // 双数行为灰色
      if (this.stripeIds.indexOf(row.orderId + row.orderLine) % 2 !== 0) {
        return 'gray-row'
      }
    },
    selectAll(e) {
      e.target.select()
    }
  }
}
</script>

<style lang="scss" scoped>
.searchItem{
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 189px);
  }
}
::v-deep .gray-row{
  background: #f1f2f6;
}
::v-deep .orange{
  background: #ff8d1a;
}
.searchTimeItem{
  margin-right: 0;
  ::v-deep .el-form-item__content{
    width: calc(100% - 82px);
  }
}
.searchValue{
  width: 95%;
}
</style>
