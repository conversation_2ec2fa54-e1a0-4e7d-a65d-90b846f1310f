<template>
  <div class="app-container">
    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{  $t('order.confirmationOfDeliveryDateForOrderLine')}}
        <slot name="header" />
      </div>
      <el-form label-width="400px" label-position="left">
        <el-form-item :label="$t('order.timeRangeOfAutomaticAcceptanceOfDeliveryDate')">
          <span>{{ $t('order.advance') }} <el-input-number v-model="orderLineConfig.aheadDay" :min="0" />{{ $t('order.day') }}  </span>
          <span>{{ $t('order.delay') }} <el-input-number v-model="orderLineConfig.delayDay" :min="0" />{{ $t('order.day') }} </span>
        </el-form-item>
        <el-form-item :label="$t('order.whetherToEnableAutomaticAcceptanceOfDeliveryDate')">
          <el-radio-group v-model="orderLineConfig.openAutoAcceptReceiptDate">
            <el-radio-button label="true">{{ $t('order.open') }}</el-radio-button>
            <el-radio-button label="false">{{ $t('order.close') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5" :offset="0">
          <el-button
            v-hasPermi="['order:answer-config:update']"
            type="primary"
            size="medium"
            @click="handleUpdate(orderLineConfig)"
          >{{ $t('order.toUpdate') }}</el-button>
        </el-col>
      </el-row>
    </el-card>

    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('order.confirmationOfPlannedDeliveryTime') }}
        <slot name="header" />
      </div>
      <el-form label-width="400px" label-position="left">
        <el-form-item :label="$t('order.timeRangeOfAutomaticAcceptanceOfDeliveryDate')">
          <span>{{ $t('order.advance') }} <el-input-number v-model="answerConfig.aheadDay" :min="0" />{{ $t('order.day') }}  </span>
          <span>{{ $t('order.delay') }} <el-input-number v-model="answerConfig.delayDay" :min="0" />{{ $t('order.day') }} </span>
        </el-form-item>
        <el-form-item :label="$t('order.whetherToEnableAutomaticAcceptanceOfDeliveryDate')">
          <el-radio-group v-model="answerConfig.openAutoAcceptReceiptDate">
            <el-radio-button label="true">{{ $t('order.open') }}</el-radio-button>
            <el-radio-button label="false">{{ $t('order.close') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5" :offset="0">
          <el-button
            v-hasPermi="['order:answer-config:update']"
            type="primary"
            size="medium"
            @click="handleUpdate(answerConfig)"
          >{{ $t('order.toUpdate') }}</el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { updateAnswerConfig, getAnswerConfig } from '@/api/order/answerConfig'

export default {
  name: 'Deliveryshipmentconfig',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 订单行配置
      orderLineConfig: {
        openAutoAcceptReceiptDate: false,
        aheadDay: 0,
        delayDay: 0,
        type: 'orderDetail'
      },
      // 计划行配置
      answerConfig: {
        openAutoAcceptReceiptDate: false,
        aheadDay: 0,
        delayDay: 0,
        type: 'answer'
      }
    }
  },
  created() {
    this.getConfig()
  },
  methods: {
    /** 查询 */
    getConfig() {
      this.loading = true
      // 执行查询
      getAnswerConfig().then(response => {
        if (response.data) {
          response.data.forEach(item => {
            if (item.type === 'orderDetail') {
              this.orderLineConfig = item
            } else if (item.type === 'answer') {
              this.answerConfig = item
            }
          })
        }
        this.loading = false
      })
    },
    /** 修改 */
    handleUpdate(config) {
      // 执行查询
      updateAnswerConfig({ ...config }).then(res => {
        this.$modal.msgSuccess(this.$t('common.updateSuccessful'))
        this.getConfig()
      }).catch(() => {
        this.$modal.msgError(this.$t('order.updateFailed'))
      })
    }
  }
}
</script>

<style lang="scss" scoped>

.commonCard {
  margin: 10px 0;
}

.mainTab {
  color: rgba(73, 150, 184, 0.99);
  font-size: 16px;
  font-weight: 700;
}
</style>
