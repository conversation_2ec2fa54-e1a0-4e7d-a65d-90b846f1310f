<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item :label="$t('system.currency')" prop="currencyId">
        <el-select v-model="queryParams.currencyId" class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas('currency')"
            :key="dict.value"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('order.reportForm')" prop="reportId">
        <el-select v-model="queryParams.reportId" filterable :placeholder="$t('order.pleaseSelectAReport')" clearable size="small">
          <el-option v-for="(report, i) in reportList" :key="i" :label="report.name" :value="report.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          v-has-permi="['order:currency-report-rel:query']"
          type="primary"
          icon="el-icon-search"
          plain
          @click="handleQuery"
        >{{ $t('common.search') }}</el-button>
        <el-button
          v-has-permi="['order:currency-report-rel:query']"
          icon="el-icon-refresh"
          @click="resetQuery"
        >{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['order:currency-report-rel:create']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >{{ $t('common.edit') }}</el-button>
      </el-col>

      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table ref="table" v-loading="loading" :data="list">

      <el-table-column
        type="selection"
        width="30"
      />
      <el-table-column :label="$t('order.number')" align="center" prop="id" />
      <el-table-column :label="$t('system.currency')" align="center">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="scope.row.currencyId" />
        </template>
      </el-table-column>

      <el-table-column :label="$t('order.reportForm')" align="center" prop="reportName" />
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button-->
      <!--            v-hasPermi="['order:currency-report-rel:update']"-->
      <!--            size="mini"-->
      <!--            type="text"-->
      <!--            icon="el-icon-edit"-->
      <!--            @click="handleUpdate(scope.row)"-->
      <!--          >{{ $t('common.modify') }}</el-button>-->
      <!--          <el-button-->
      <!--            v-hasPermi="['order:currency-report-rel:delete']"-->
      <!--            size="mini"-->
      <!--            type="text"-->
      <!--            icon="el-icon-delete"-->
      <!--            @click="handleDelete(scope.row)"-->
      <!--          >{{ $t('common.del') }}</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('order.reportForm')" prop="reportId">
          <el-select v-model="form.reportId" filterable :placeholder="$t('order.pleaseSelectAReport')" clearable size="small">
            <el-option v-for="(report, i) in reportList" :key="i" :label="report.name" :value="report.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-has-permi="['order:currency-report-rel:create']"
          @click="cancel"
        >{{ $t('common.cancel') }}</el-button>
        <el-button
          v-has-permi="['order:currency-report-rel:create']"
          type="primary"
          @click="submitForm"
        >{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createCurrencyReportRel,
  updateCurrencyReportRel,
  deleteCurrencyReportRel,
  getCurrencyReportRel,
  getCurrencyReportRelPage
} from '@/api/order/currencyReportRel'
import { getReportNames } from '@/api/visualization/report'

export default {
  name: 'Currencyreportrel',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 币种与订单报表关系列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        currencyId: null,
        reportId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        currencyId: [{ required: true, message: this.$t('order.currencyCannotBeBlank'), trigger: 'blur' }],
        reportId: [{ required: true, message: this.$t('order.theReportCannotBeEmpty'), trigger: 'blur' }]
      },
      // 报表列表
      reportList: [],
      // 是否禁用
      isDisabled: false,
      selectList: []
    }
  },
  created() {
    this.getList()
    this.getReportNames()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getCurrencyReportRelPage(params).then(response => {
        if (response.data.list.length > 0) {
          response.data.list.map(j => {
            j.reportName = j.reportName?.replace('.ureport.xml', '')
            return j
          })
        }
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    // 获取报表列表
    getReportNames() {
      getReportNames({ template: false }).then(res => {
        if (res.data.length > 0) {
          res.data.map(j => {
            j.name = j.name.replace('.ureport.xml', '')
            return j
          })
        }
        this.reportList = res.data
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        currencyId: undefined,
        reportId: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.isDisabled = false
      this.selectList = this.$refs.table.selection
      if (this.selectList.length) {
        this.open = true
        if (this.selectList.length === 1) {
          this.form.reportId = this.selectList[0].reportId
        }
      } else {
        this.$message.error(this.$t('order.selectAtLeastOneData'))
      }
      this.title = this.$t('order.addCurrencyAndOrderReportRelationship')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.isDisabled = true
      const id = row.id
      getCurrencyReportRel(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('order.modifyTheRelationshipBetweenCurrencyAndOrderReport')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        this.selectList.map(a => {
          return {
            ...a,
            reportId: this.form.reportId
          }
        })
        updateCurrencyReportRel(
          this.selectList.map(a => {
            return {
              ...a,
              reportId: this.form.reportId
            }
          })).then(response => {
          this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm(this.$t('order.areYouSureToDeleteTheRelationshipBetweenCurrencyAndOrderReportNo') + id + this.$t('material.dataItemOf')).then(function() {
        return deleteCurrencyReportRel(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {})
    }
  }
}
</script>
