<template>

  <div style="padding: 25px 15px">
    <el-card>
      <div slot="header">
        {{ $t('order.shippingOrderHeader') }}
      </div>
      <el-form
        ref="deliveryHead"
        inline
        :model="deliveryHead"
        size="mini"
        label-width="181px"
        :rules="deliveryHeadRules"
      >
        <el-form-item class="searchItem" :label="$t('supplier.supplier')" prop="supplierId">
          <show-or-edit
            :value="deliveryHead.supplierId"
            :custom-list="deliverySupplier"
            :disabled="lockUnableFieldsForDeliveryNote"
          >
            <el-select
              v-model="deliveryHead.supplierId"
              class="deliveryHeadItem"
              :placeholder="$t('scar.pleaseEnterKeywordsForFuzzySearch')"
              clearable
              filterable
              :disabled="lockUnableFieldsForDeliveryNote"
              remote
              :remote-method="doGetDeliverySupplier"
              @change="changeSupplier"
            >
              <!--            UFFF-1537： 送货单：供应商简称改成供应商，以避免有些客户没有供应商简称的情况-->
              <el-option
                v-for="supplier in deliverySupplier"
                :key="supplier.id"
                :label="supplier.name"
                :value="supplier.id"
              />
            </el-select>

          </show-or-edit>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.customerName')">
          {{ deliveryHead.companyName }}
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('material.factory')" prop="factoryId">
          <show-or-edit
            :disabled="lockUnableFieldsForDeliveryNote"
            :value="deliveryHead.factoryId"
            :dict="DICT_TYPE.COMMON_FACTORY"
          >
            <el-select
              v-model="deliveryHead.factoryId"
              clearable
              filterable
              :disabled="lockUnableFieldsForDeliveryNote"
              class="deliveryHeadItem"
              @change="triggerFactoryAddress"
            >
              <el-option
                v-for="item in getDictDatas(DICT_TYPE.COMMON_FACTORY)"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('common.buyer')">
          {{ buyerNames }}
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.supplierContact')">
          <show-or-edit
            :value="deliveryHead.supplierContact"
            :disabled="lockUnableFieldsForDeliveryNote"
          >
            <el-input
              v-model="deliveryHead.supplierContact"
              :disabled="lockUnableFieldsForDeliveryNote"
              class="deliveryHeadItem"
            />
          </show-or-edit>

        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.deliveryNoteNo')">
          {{ deliveryHead.deliveryNoteNo }}
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.supplierContactNumber')">
          <show-or-edit
            :value="deliveryHead.supplierPhone"
            :disabled="lockUnableFieldsForDeliveryNote"
          >
            <el-input
              v-model="deliveryHead.supplierPhone"
              :disabled="lockUnableFieldsForDeliveryNote"
              class="deliveryHeadItem"
            />
          </show-or-edit>

        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.deliveryDriver')">
          <show-or-edit
            :value="deliveryHead.shipperName"
            :disabled="deliveryHead.status !== 'draft'"
          >
            <el-input v-model="deliveryHead.shipperName" :disabled="deliveryHead.status !== 'draft'" />

          </show-or-edit>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.deliveryMethod')">
          <show-or-edit
            :value="deliveryHead.deliveryMethodsCode"
            :disabled="deliveryHead.status !== 'draft'"
            :dict="DICT_TYPE.ORDER_SHIPPING_METHOD"
          >
            <el-select
              v-model="deliveryHead.deliveryMethodsCode"
              class="deliveryHeadItem"
              :disabled="deliveryHead.status !== 'draft'"
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.ORDER_SHIPPING_METHOD)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </show-or-edit>

        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.deliveryDriversTelephone')">
          <show-or-edit
            :value="deliveryHead.shipperMobile"
            :disabled="deliveryHead.status !== 'draft'"
          >
            <el-input v-model="deliveryHead.shipperMobile" :disabled="deliveryHead.status !== 'draft'" />

          </show-or-edit>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.expectedArrivalDate')">
          <show-or-edit
            type="Date"
            :value="deliveryHead.estimatedArrivalDate"
            :disabled="deliveryHead.status !== 'draft'"
          >
            <el-date-picker
              v-model="deliveryHead.estimatedArrivalDate"
              class="deliveryHeadItem"
              :placeholder="$t('order.selectDate')"
              type="date"
              placement="bottom-start"
              value-format="yyyy-MM-dd"
              :disabled="deliveryHead.status !== 'draft'"
            />
          </show-or-edit>

        </el-form-item>
        <el-form-item class="searchItem" :label="$t('material.netWeight')">
          <div style="display: flex">
            <show-or-edit
              style="flex: 1 0 75%"
              :value="deliveryHead.netWeight"
              :disabled="deliveryHead.status !== 'draft'"
            >
              <vxe-input
                v-model.number="deliveryHead.netWeight"
                size="mini"
                style="width: 100%"
                min="0"
                :disabled="deliveryHead.status !== 'draft'"
                type="number"
              />

            </show-or-edit>
            <show-or-edit
              :value="deliveryHead.netWeightUnit"
              :dict="DICT_TYPE.MATERIAL_UOM"
              style="width: 100%"
              :disabled="deliveryHead.status !== 'draft'"
            >
              <el-select v-model="deliveryHead.netWeightUnit" :disabled="deliveryHead.status !== 'draft'" filterable>
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>

          </div>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.waybillNo')">
          <show-or-edit
            :value="deliveryHead.waybillNumber"
            :disabled="deliveryHead.status !== 'draft'"
          >
            <el-input v-model="deliveryHead.waybillNumber" :disabled="deliveryHead.status !== 'draft'" />

          </show-or-edit>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('material.grossWeight')">
          <div style="display: flex">
            <show-or-edit
              :value="deliveryHead.grossWeight"
              :disabled="deliveryHead.status !== 'draft'"
              style="flex: 1 0 75%"
            >
              <vxe-input
                v-model.number="deliveryHead.grossWeight"
                style="width: 100%"
                size="mini"
                min="0"
                :disabled="deliveryHead.status !== 'draft'"
                type="number"
              />
            </show-or-edit>
            <show-or-edit
              :value="deliveryHead.grossWeightUnit"
              :disabled="deliveryHead.status !== 'draft'"
            >
              <el-select
                v-model="deliveryHead.grossWeightUnit"
                :disabled="deliveryHead.status !== 'draft'"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>

          </div>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.invoiceNo')">
          <show-or-edit
            :value="deliveryHead.invoiceNumber"
            :disabled="deliveryHead.status !== 'draft'"
          >
            <el-input v-model="deliveryHead.invoiceNumber" :disabled="deliveryHead.status !== 'draft'" />

          </show-or-edit>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.numberOfBoxes')">
          <div style="display: flex">
            <show-or-edit
              :value="deliveryHead.packingQuantity"
              :disabled="deliveryHead.status !== 'draft'"
              style="flex: 1 0 75%"
            >
              <vxe-input
                v-model.number="deliveryHead.packingQuantity"
                style="width: 100%"
                min="0"
                size="mini"
                :disabled="deliveryHead.status !== 'draft'"
                type="number"
              />

            </show-or-edit>
            <show-or-edit
              :value="deliveryHead.packingUnit"
              :disabled="deliveryHead.status !== 'draft'"
              :dict="DICT_TYPE.MATERIAL_UOM"
            >
              <el-select
                v-model="deliveryHead.packingUnit"
                :disabled="deliveryHead.status !== 'draft'"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>

          </div>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.forwardingCompany')">
          <show-or-edit
            :value="deliveryHead.forwarderCompany"
            :disabled="deliveryHead.status !== 'draft'"
          >
            <el-input v-model="deliveryHead.forwarderCompany" :disabled="deliveryHead.status !== 'draft'" />
          </show-or-edit>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('material.volume')">
          <div style="display: flex">
            <show-or-edit
              :value="deliveryHead.volume"
              :disabled="deliveryHead.status !== 'draft'"
              style="flex: 1 0 75%"
            >
              <vxe-input
                v-model.number="deliveryHead.volume"
                style="width: 100%"
                min="0"
                size="mini"
                :disabled="deliveryHead.status !== 'draft'"
                type="number"
              />

            </show-or-edit>
            <show-or-edit
              :value="deliveryHead.volumeUnit"
              :disabled="deliveryHead.status !== 'draft'"
              :dict="DICT_TYPE.MATERIAL_UOM_VOLUME"
            >
              <el-select
                v-model="deliveryHead.volumeUnit"
                :disabled="deliveryHead.status !== 'draft'"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in getMaterialDict(DICT_TYPE.MATERIAL_UOM_VOLUME)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>

          </div>
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.deliveryAddress')" prop="shippingAddress">
          <show-or-edit
            :value="deliveryHead.shippingAddress"
            :disabled="lockUnableFieldsForDeliveryNote"
          >
            <el-select
              v-model="deliveryHead.shippingAddress"
              :disabled="lockUnableFieldsForDeliveryNote"
              clearable
              show-overflow-tooltip
              class="deliveryHeadItem"
            >
              <el-option v-for="(item, index) in factoryAddress" :key="index" :label="item" :value="item" />
            </el-select>
          </show-or-edit>

        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.theDateOfIssuance')">
          {{ parseTime(deliveryHead.deliveryDate, '{y}-{m}-{d}') }}
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.shippingAddress')">
          <show-or-edit
            :value="deliveryHead.shipAddress"
            :disabled="deliveryHead.status !== 'draft'"
          >
            <el-input
              v-model="deliveryHead.shipAddress"
              :disabled="deliveryHead.status !== 'draft'"
              maxlength="50"
              show-word-limit
            />
          </show-or-edit>

        </el-form-item>
        <el-form-item class="searchItem" :label="$t('auth.uploadAttachments')">
          <div style="display: flex;justify-content: space-between">

            <el-upload
              class="upload-demo"
              :disabled="['cancel','have_arrived','printed'].includes(deliveryHead.status)"
              :action="uploadUrl"
              :headers="getBaseHeader()"
              :before-upload="beforeUpload"
              :on-remove="onRemove"
              :on-preview="onPreview"
              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
              multiple
              :limit="5"
              :show-file-list="false"
              :file-list="fileList"
            >
              <el-button
                v-if="!['cancel','have_arrived','printed'].includes(deliveryHead.status)"
                class="uploadBtn"
                size="small"
                plain
                icon="el-icon-plus"
                type="primary"
              />
            </el-upload>
            <div>
              {{ $t('scar.viewAttachments') }}
              <el-button
                class="uploadBtn"
                size="small"
                style="padding: 5px 9px"
                :disabled="fileList.length===0"
                plain
                :type="fileList.length?'primary':''"
                @click="showFile=true"
              >
                {{ fileList.length }}
              </el-button>

              <el-dialog
                v-if="showFile"
                :visible.sync="showFile"
                :title="$t('scar.viewAttachments')"
                width="400px"
              >
                <el-upload
                  class="upload-show"
                  :disabled="['cancel','have_arrived','printed'].includes(deliveryHead.status)"
                  :action="uploadUrl"
                  :headers="getBaseHeader()"
                  :before-upload="beforeUpload"
                  :on-remove="onRemove"
                  :on-preview="onPreview"
                  :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                  multiple
                  :limit="5"
                  :file-list="fileList"
                />
                <div slot="footer">
                  <el-button type="primary" @click="showFile=false">{{ $t('order.determine') }}</el-button>
                </div>

              </el-dialog>
            </div>
          </div>

        </el-form-item>
        <!--            UFFF-1523：送货单：送货单备注，文本框文本长度限制需统一，目前页面上没有文本长度的提示-->
        <el-form-item class="searchItem" style="margin-top: 10px;" :label="$t('order.deliveryNoteRemarks')">
          <show-or-edit
            :value="deliveryHead.shipperRemark"
            :disabled="deliveryHead.status !== 'draft'"
          >
            <el-input
              v-model="deliveryHead.shipperRemark"
              :disabled="deliveryHead.status !== 'draft'"
              maxlength="50"
              show-word-limit
            />
          </show-or-edit>

        </el-form-item>
        <el-form-item class="searchItem" :label="$t('order.deliveryOrderStatus')">

          <dict-tag :type="DICT_TYPE.ORDER_DELIVERY_STATUS" :value="deliveryHead.status" />

          <el-button
            v-if="deliveryHead.status === 'in_the_delivery'"
            v-has-permi="['order:delivery-note:confirmreceive']"
            style="margin-left: 10px"
            type="primary"
            plain
            @click="confirmGoods"
          >{{ $t('order.confirmReceipt') }}
          </el-button>

        </el-form-item>
        <el-row>
          <el-col :span="12">
            <br>
          </el-col>
          <el-col :span="12">
            <div style="text-align: right;width: 85%">
              <el-button
                v-if="['draft','printed','in_the_delivery'].includes(deliveryHead.status) &&deliveryHead.id "
                type="danger"
                plain
                @click="invalidDelivery"
              >{{ $t('order.toVoid') }}
              </el-button>

              <el-button
                v-if="!['printed','in_the_delivery','cancel','have_arrived'].includes(deliveryHead.status) "
                type="primary"
                plain
                @click="doSaveDeliveryHead"
              >{{ $t('common.save') }}
              </el-button>
              <el-button
                v-if="!['draft','have_arrived','cancel','in_the_delivery'].includes(deliveryHead.status) "
                type="primary"
                plain
                @click="editDeliveryHead"
              >{{ $t('order.reEdit') }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>

    </el-card>

    <el-card v-if="deliveryHead.id" style="margin-top: 10px">

      <div slot="header">
        {{ $t('order.deliveryDetailLine') }}
      </div>
      <vxe-grid
        ref="deliveryNoteGrid"
        :data="list"
        :loading="loading"
        v-bind="girdOption"
        keep-source
        @edit-closed="debouncedEditClosed"
      >
        <template #orderNo="{row}">
          <el-button type="text" @click="$router.push(`/order/orderDetail/${row.id}?id=${row.orderNo}`)">
            {{ row.orderNo }}
          </el-button>
        </template>
        <template #availableDeliveryQuantity="{row}">
          <number-format :value="row.availableDeliveryQuantity" />
        </template>
        <template #deliveryQuantity="{row}">
          <number-format :value="row.deliveryQuantity" />
        </template>
        <template #deliveryQuantityEdit="{row}">
          <el-input
            v-model.number="row.deliveryQuantity"
            :disabled="deliveryHead.status !== 'draft'"
            type="number"
          />
        </template>
        <template #produceDateEdit="{row}">
          <vxe-input
            v-model="row.produceDate"
            :disabled="deliveryHead.status !== 'draft'"
            :placeholder="$t('order.dateSelection')"
            type="date"
            transfer
          />
        </template>
        <template #batchNoEdit="{row}">
          <el-input
            v-model="row.batchNo"

            :disabled="deliveryHead.status !== 'draft'"
          />
        </template>
        <template #validityPeriodEdit="{row}">
          <vxe-input
            v-model="row.validityPeriod"
            :placeholder="$t('order.dateSelection')"
            :disabled="deliveryHead.status !== 'draft'"

            type="date"
            transfer
          />
        </template>
        <template #remarkEdit="{row}">
          <el-input
            v-model="row.remark"

            :disabled="deliveryHead.status !== 'draft'"
          />
        </template>

        <template #registrationNumberEdit="{row}">
          <el-input
            v-model="row.registrationNumber"

            :disabled="deliveryHead.status !== 'draft'"
          />
        </template>
        <template #prCertificateNoEdit="{row}">
          <el-input
            v-model="row.prCertificateNo"

            :disabled="deliveryHead.status !== 'draft'"
          />
        </template>
        <template #concentrationEdit="{row}">
          <el-input v-model="row.concentration" />
        </template>

        <template #purchasingUnit="{row}">
          <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.purchasingUnit" />
        </template>
        <template #factoryId="{row}">
          <dict-tag :type="'factory'" :value="row.factoryId" />
        </template>
        <template #currency="{row}">
          <dict-tag :type="'currency'" :value="row.currency" />
        </template>
        <template #orderType="{row}">
          <dict-tag :type="DICT_TYPE.ORDER_TYPE" :value="row.orderType" />

        </template>
        <template #status="{row}">
          <dict-tag :type="DICT_TYPE.ORDER_STATUS" :value="row.status" />

        </template>

        <template #toolbar_buttons>
          <div style="width: 100%;">
            <div style="width: 50%;margin: auto">
              <el-input
                v-model="queryParams.searchText"
                :placeholder="$t('order.orderNoAndMaterialCode')"
                clearable
                style="width: 80%"
              />
              <el-button
                v-has-permi="['order:delivery-note:query']"
                type="primary"
                @click="getDeliveryDetailTable"
              >
                {{ $t('common.search') }}
              </el-button>
            </div>
          </div>
          <el-row :gutter="24" style="width: 100%" class="mb8">
            <el-col :span="10">
              <el-button
                v-if="deliveryHead.status === 'draft'"
                v-has-permi="['order:delivery-detail:create']"
                icon="el-icon-circle-plus"
                size="mini"
                type="primary"
                @click="doAddDelivery"
              >
                {{ $t('order.addByOrder') }}
              </el-button>
              <el-button
                v-if="deliveryHead.status === 'draft'"
                v-has-permi="['order:delivery-detail:create']"
                size="mini"
                type="primary"
                plain
                @click="saveAdd('copy')"
              >
                {{ $t('order.copy') }}
              </el-button>
              <el-button
                v-if="deliveryHead.status === 'draft'"
                v-has-permi="['order:delivery-detail:delete']"
                size="mini"
                type="danger"
                plain
                @click="delDeliveryLine"
              >
                {{ $t('common.del') }}
              </el-button>
            </el-col>
            <el-col :span="14" style="text-align: right;float:right;">
              <el-button
                v-if="deliveryHead.status !== 'cancel'"
                v-has-permi="['order:delivery-note:print']"
                icon="el-icon-printer"
                type="primary"
                plain
                style="margin-right: 5px"
                @click="printDelivery"
              >
                {{ $t('order.printDeliveryNote') }}
              </el-button>
              <el-button
                v-if="deliveryHead.status === 'printed'"
                v-has-permi="['order:delivery-note:confirmdelivery']"
                type="primary"
                plain
                style="margin-right: 5px"
                @click="confirmDelivery"
              >
                {{ $t('order.confirmDelivery') }}
              </el-button>
            </el-col>
          </el-row>
        </template>
      </vxe-grid>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

    </el-card>
    <!--    附件下载-->
    <el-dialog
      v-if="showAdd"
      width="1000px"
      :visible.sync="showAdd"
      :title="$t('order.addByOrder')"
      @close="closeDialog"
    >
      <div>
        <el-form inline>
          <el-form-item :label="$t('order.orderNumber')">
            <el-input v-model="addQuery.orderNo" class="searchItemAdd" clearable />
          </el-form-item>
          <el-form-item :label="$t('material.materialCode')">
            <el-input v-model="addQuery.materialCode" class="searchItemAdd" clearable />
          </el-form-item>
          <el-form-item :label="$t('material.manufacturer')">
            <el-input v-model="addQuery.mfg" class="searchItemAdd" clearable />
          </el-form-item>
          <el-form-item :label="$t('material.manufacturersPartNumber')">
            <el-input v-model="addQuery.mpn" class="searchItemAdd" clearable />

          </el-form-item>
        </el-form>
      </div>
      <div style="display: flex;justify-content: space-between;margin-bottom: 10px">
        <div>
          <el-button
            v-has-permi="['order:delivery-detail:create']"
            type="primary"
            plain
            @click="upload.open=true"
          >{{ $t('order.batchCreateDeliveryDetails') }}
          </el-button>

        </div>
        <div>
          <el-button
            v-has-permi="['order:delivery-note:query']"
            type="primary"
            plain
            @click="getAddTable"
          >{{ $t('common.search') }}
          </el-button>
          <el-button @click="getAddTable('reset')">{{ $t('common.reset') }}</el-button>
        </div>
      </div>
      <!--按订单添加弹出框-->
      <el-table
        ref="addTable"
        :data="addTable"
      >
        <el-table-column
          type="selection"
          width="30"
        />
        <el-table-column :label="$t('order.orderNumber')" prop="orderNo" />
        <el-table-column :label="$t('order.orderLineNo')" prop="orderLine" />
        <el-table-column :label="$t('material.materialCode')" prop="materialCode" />
        <el-table-column :label="$t('order.describe')" prop="materialDescription" />
        <el-table-column :label="$t('material.manufacturer')" prop="mfg" />
        <el-table-column :label="$t('material.manufacturersPartNumber')" prop="mpn" />
        <el-table-column :label="$t('material.purchasingUnit')" prop="purchasingUnit">
          <template #default="{row}">
            <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.purchasingUnit" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('order.orderDate')" prop="orderDate">
          <template #default="scope">
            <!--            {{ parseTime(scope.row.orderDate) }}-->
            {{ scope.row.orderDate }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('order.remainingOrdersCanBeDelivered')" prop="availableDeliveryQuantity" />
      </el-table>
      <pagination
        v-show="addTotal > 0"
        :total="addTotal"
        :page.sync="addQuery.pageNo"
        :limit.sync="addQuery.pageSize"
        @pagination="getAddList"
      />
      <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
        <div class="upload">
          <p>
            {{ $t('common.uploadDescription') }}：
          </p>
          <p>
            {{ $t('common.1uploadDescriptionTitle') }}
          </p>
          <p>
            {{ $t('common.2uploadDescriptionTitle') }}
          </p>
          <p>
            {{ $t('common.3uploadDescriptionTitle') }}
          </p>
          <p>
            {{ $t('common.4uploadDescriptionTitle') }}
          </p>
        </div>
        <div class="text-center">
          <el-upload
            ref="upload"
            class="small-padding"
            :limit="1"
            accept=".xlsx, .xls"
            :headers="upload.headers"
            :action="upload.url"
            :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :auto-upload="false"
            drag
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
          </el-upload>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button
            v-has-permi="['order:delivery-detail:import']"
            @click="upload.open = false"
          >{{ $t('common.cancel') }}
          </el-button>
          <el-button
            v-has-permi="['order:delivery-detail:import']"
            type="primary"
            plain
            @click="importTemplate"
          > {{ $t('common.downloadTemplate') }}
          </el-button>
          <el-button
            v-has-permi="['order:delivery-detail:import']"
            type="primary"
            @click="submitFileForm"
          > {{ $t('common.confirm') }}
          </el-button>

        </div>
      </el-dialog>
      <template slot="footer">
        <el-button
          v-has-permi="['order:delivery-detail:create']"
          @click="showAdd = false"
        >{{ $t('common.cancel') }}
        </el-button>
        <el-button
          v-has-permi="['order:delivery-detail:create']"
          type="primary"
          @click="saveAdd('add')"
        >{{ $t('common.save') }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import store from '@/store'
import { parseTime } from '@/utils/ruoyi'
import { getDictDatas } from '@/utils/dict'
import { getSupplierContact } from '@/api/supplier/info'
import { getFactoryAddressWithFactoryId } from '@/api/order/factoryAddressRel'
import {
  attachments,
  repeatEdit,
  confirmDelivery,
  confirmReceive,
  createDeliveryAdd, delDeliveryDetail, delDeliveryFile,
  getDeliveryAddPage,
  getDeliveryDetailPage,
  getDeliveryHead,
  getDeliverySupplier, getImportTemplate, invalidDelivery, printDeliverynote,
  saveDeliveryHead, uploadDeliveryFile
} from '@/api/order/deliveryNote'
import { getBaseHeader } from '@/utils/request'
import $modal from '@/plugins/modal'
import { getConfigKey } from '@/api/infra/config'
import { getPreviewReport } from '@/api/visualization/report'
import ShowOrEdit from '@/components/ShowOrEdit'
import { debounce } from 'throttle-debounce'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'

export default {
  name: 'Orderdeliverydetail/:deliveryid',
  components: {
    ShowOrEdit
  },
  data() {
    return {
      // 当前用户是否为供应商账户
      isExternal: false,
      getBaseHeader,
      parseTime,
      // 送货单抬头处附件列表集合
      fileList: [],
      // 送货单抬头表单校验
      deliveryHeadRules: {
        supplierId: [{ required: true, message: this.$t('auth.pleaseSelectASupplier'), trigger: 'blur' }],
        factoryId: [{ required: true, message: this.$t('material.pleaseSelectAFactory'), trigger: 'blur' }],
        shippingAddress: [{ required: true, message: this.$t('order.pleaseSelectAShippingAddress'), trigger: 'blur' }]
      },
      queryParams: {
        deliveryId: 0,
        searchText: '',
        pageNo: 1,
        pageSize: 10
      },
      list: [],
      loading: false,
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'deliveryNoteDetail',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        editConfig: {
          trigger: 'click',
          mode: 'row',
          showUpdateStatus: true,
          beforeEditMethod: this.beforeEditMethod,
          autoClear: true // 自动清除编辑不能取消，否则不能进入edit-closed方法
        },
        editRules: {
          deliveryQuantity: [
            {
              required: true,
              message: this.$t('order.pleaseFillIn')
            }
          ],
          validityPeriod: [
            {
              required: true,
              message: this.$t('order.pleaseFillIn')
            }
          ],
          produceDate: [
            {
              required: true,
              message: this.$t('order.pleaseFillIn')
            }
          ]
        },
        rowConfig: { isCurrent: true,
          isHover: true
        },
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          { title: this.$t('order.deliveryLn'), field: 'deliveryItem', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('order.orderNumber'), field: 'orderNo', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('order.orderLine'), width: 100, field: 'orderLine', visible: true },
          { title: this.$t('material.materialCode'), width: 100, field: 'materialCode', visible: true },
          { title: this.$t('order.describe'), width: 100, field: 'materialDescription', visible: true },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          {
            title: this.$t('material.purchasingUnit'),
            field: 'purchasingUnit',
            slots: { default: 'purchasingUnit' },
            width: 100,
            visible: true
          },
          { title: this.$t('order.orderDate'), field: 'orderDate', visible: true, width: 100 },
          {
            title: this.$t('order.remainingDeliverableQuantityOfOrder'),
            field: 'availableDeliveryQuantity',
            slots: { default: 'availableDeliveryQuantity' },
            visible: true,
            width: 100,
            align: 'right'
          },
          {
            title: this.$t('order.deliveryQuantity'),
            editRender: {},
            slots: { edit: 'deliveryQuantityEdit', default: 'deliveryQuantity' },
            field: 'deliveryQuantity',
            visible: true,
            width: 100,
            align: 'right'
          },
          {
            title: this.$t('order.dateOfManufacture'),
            editRender: {},
            slots: { edit: 'produceDateEdit' },
            field: 'produceDate',
            visible: true,
            width: 100
          },
          {
            title: this.$t('supplier.validUntil'),
            field: 'validityPeriod',
            editRender: {},
            slots: { edit: 'validityPeriodEdit' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('order.batchNo'),
            editRender: {},
            slots: { edit: 'batchNoEdit' },
            field: 'batchNo',
            visible: true,
            width: 100
          },
          {
            title: this.$t('common.remarks'),
            editRender: {},
            slots: { edit: 'remarkEdit' },
            field: 'remark',
            visible: true,
            width: 100
          },
          {
            title: this.$t('order.externalInspectionMaterials'),
            field: 'inspectionMaterial',
            visible: true,
            width: 100
          },
          {
            title: this.$t('order.conclusionOfExternalInspection'),
            field: 'inspectionConclusion',
            visible: true,
            width: 100
          },
          {
            title: this.$t('order.registrationCertificateNo'),
            editRender: {},
            slots: { edit: 'registrationNumberEdit' },
            field: 'registrationNumber',
            visible: true,
            width: 100
          },
          {
            title: this.$t('order.productionLicenseNo'),
            editRender: {},
            slots: { edit: 'prCertificateNoEdit' },
            field: 'prCertificateNo',
            visible: true,
            width: 100
          },
          { title: this.$t('material.storageConditions'), field: 'storageCondition', visible: true, width: 100 },
          {
            title: this.$t('order.concentration'),
            editRender: {},
            slots: { edit: 'concentrationEdit' },
            field: 'concentration',
            visible: true,
            width: 100
          }
        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      total: 0,
      // 当前用户关联的供应商信息（supplier_user_rel)
      deliverySupplier: [],
      // 指定工厂相关的地址集合
      factoryAddress: [],
      deliveryHead: {
        // boxLabelStatus: '',
        reportId: undefined, // 送货单关联的pdf模板id
        boxPrintDate: '',
        companyName: '',
        supplierCode: '',
        deliveryDate: '',
        deliveryMethodsCode: '',
        deliveryNoteNo: '',
        id: null,
        estimatedArrivalDate: '',
        factoryId: '',
        forwarderCompany: '',
        grossWeight: null,
        grossWeightUnit: null,
        invoiceNumber: '',
        netWeight: null,
        netWeightUnit: null,
        packingQuantity: null,
        packingUnit: null,
        shipAddress: '',
        shipperMobile: '',
        shipperName: '',
        shipperRemark: '',
        shippingAddress: '', // 送货地址需要根据选择的工厂默认带出第一个
        sourcingId: null, // 初始化值为null，等待后端的赋值
        status: 'draft',
        supplierContact: '',
        supplierId: '',
        nameShort: '',
        supplierPhone: '',
        volume: null,
        volumeUnit: null,
        waybillNumber: ''
      },
      showAdd: false,
      addTable: [],
      addTotal: 0,
      addQuery: {
        factoryId: 0,
        materialCode: '',
        mfg: '',
        mpn: '',
        orderNo: '',
        pageNo: 1,
        pageSize: 10,
        supplierId: 0
      },
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: this.$t('order.batchCreateDeliveryDetails'),
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: ''
      },
      fileType: '',
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      showPreview: {
        // 是否显示弹出层（物料导入）
        open: false,
        reportId: undefined,
        params: null
      },
      errMap: null,
      debouncedEditClosed: debounce(50, this.editClosed), // 设置防抖延迟时间，单位毫秒
      showFile: false,
      buyerNames: ''
    }
  },
  computed: {
    // 当生成送货单id时，需要锁住部分字段，不可编辑
    lockUnableFieldsForDeliveryNote() {
      if (this.deliveryHead.id) {
        this.upload.url = `${process.env.VUE_APP_BASE_API}/admin-api/order/delivery-detail/import?deliveryId=${this.deliveryHead.id}`
        return true
      }
      return false
    }
  },
  mounted() {
    // 当前用户为外部用户时（供应商），则供应商部分数据需要默认带出 && 采购部分需要根据订单选择导出
    this.isExternal = store.getters.isExternal === 2
    if (this.isExternal) {
      // 默认展示供应商信息
      getDeliverySupplier().then(res => {
        this.deliverySupplier = res.data
        this.changeSupplier(this.deliverySupplier[0].id)
        this.deliveryHead.supplierId = this.deliverySupplier[0].id
      })
    }
    if (this.$route.params.deliveryId !== '0') {
      this.deliveryHead.id = this.$route.params.deliveryId
    }
    if (this.deliveryHead.id) {
      this.getHeader()
    }
    getConfigKey('file.type.common').then(response => {
      this.fileType = response.data
    })
    this.upload.url = `${process.env.VUE_APP_BASE_API}/admin-api/order/delivery-detail/import?deliveryId=${this.deliveryHead.id}`
  },
  methods: {
    /**
     * 根据查看还是编辑，来提供字典数据的处理
     * @param type
     */
    getMaterialDict(type) {
      return getDictDatas(type, this.viewOnly ? false : 0)
    },
    // 送货单抬头获取上传的附件
    clickDeliveryHeadUpload() {
      // 获取附件
      attachments({ 'businessId': this.deliveryHead.id, 'businessType': 'order_delivery_note' }).then(res => {
        this.fileList = []
        res.data?.forEach(item => this.fileList.push({ 'name': item.fileName, 'url': item.filePath, 'id': item.id }))
      })
    },
    // 选择工厂字段时，触发关联的地址信息默认选中
    // 1. 需要clear工厂地址信息
    triggerFactoryAddress(newVal) {
      getFactoryAddressWithFactoryId(newVal).then(res => {
        this.factoryAddress = []
        this.deliveryHead.shippingAddress = ''
        res.data?.forEach(item => this.factoryAddress.push(item.deliveryAddress))
      })
    },
    // 查询当前用户关联的供应商信息
    doGetDeliverySupplier(query) {
      if (query) {
        getDeliverySupplier(query).then(res => {
          this.deliverySupplier = res.data
        })
      }
    },
    // 获取送货单抬头信息
    getHeader() {
      getDeliveryHead({ id: this.deliveryHead.id }).then(res => {
        this.deliveryHead = res.data
        // 解析执行采购的展示
        this.buyerNames = getDictDataLabel(DICT_TYPE.COMMON_USERS, this.deliveryHead.sourcingId?.split(',')).toString()
        if (this.deliveryHead.id) {
          // 获取附件
          attachments({ 'businessId': this.deliveryHead.id, 'businessType': 'order_delivery_note' }).then(res => {
            this.fileList = []
            res.data?.forEach(item => this.fileList.push({
              'name': item.fileName,
              'url': item.filePath,
              'id': item.id
            }))
          })
          this.getDeliveryDetailTable()
        }
        if (this.deliverySupplier?.length === 0) {
          // 需要填充供应商名称
          this.deliverySupplier.push({
            id: this.deliveryHead.supplierId,
            name: this.deliveryHead.nameShort
          })
        }
      })
    },
    // 获取送货单下的送货明细列表
    getDeliveryDetailTable() {
      // 跨页后使用前面页数据进行搜索，此时未重置pageNo导致搜索结果为空
      this.queryParams.pageNo = 1
      this.getList()
    },
    // 获取按订单添加的数据
    getAddTable(type) {
      if (type === 'reset') {
        this.resetDialogForm()
      }
      this.addQuery.pageNo = 1
      this.getAddList()
    },
    changeSupplier(supplierId) {
      if (!supplierId) {
        // clear supplier rel fields
        this.deliveryHead.supplierCode = ''
        this.deliveryHead.nameShort = ''
        this.deliveryHead.supplierContact = ''
        this.deliveryHead.supplierPhone = ''
        this.deliverySupplier = []
        return
      }
      var currentSupplier = this.deliverySupplier.find(item => item.id === supplierId)
      this.deliveryHead.supplierCode = currentSupplier?.code
      this.deliveryHead.nameShort = currentSupplier?.shortname
      if (currentSupplier.shortname || currentSupplier.shortname === '') {
        this.deliveryHead.nameShort = currentSupplier?.name
      }

      getSupplierContact({ 'supplierId': supplierId, 'contactDivision': 'the_order' }).then(res => {
        this.deliveryHead.supplierContact = res.data.name
        this.deliveryHead.supplierPhone = res.data.phone
        // this.deliveryHead.supplierId = res.data.supplierId
      })
    },
    // 送货单抬头#重新编辑(仅已打印状态显示）
    editDeliveryHead() {
      repeatEdit({ id: this.deliveryHead.id }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.getHeader()
      })
    },
    doSaveDeliveryHead() {
      this.$refs['deliveryHead'].validate(valid => {
        if (!valid) {
          this.$message.error(this.$t('order.pleaseFillInTheRequiredInformation'))
          return
        }
        saveDeliveryHead(this.deliveryHead).then(res => {
          // 填充新的送货单id
          if (!this.deliveryHead.id) {
            // this.deliveryHead.id = res.data
            // this.getHeader()
            this.$tab.closeOpenPage(`/order/orderDeliveryDetail/${res.data.id}?deliveryNoteNo=${res.data.deliveryNoteNo}`)
          }
          this.$message.success(this.$t('common.savedSuccessfully'))
        })
      })
    },
    // 主列表的分页切换查询
    getList() {
      this.queryParams.deliveryId = this.deliveryHead.id
      this.loading = true
      getDeliveryDetailPage(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    // 按订单添加列表的分页切换查询
    getAddList() {
      this.addQuery.supplierId = this.deliveryHead.supplierId
      this.addQuery.factoryId = this.deliveryHead.factoryId
      getDeliveryAddPage(this.addQuery).then(res => {
        this.addTable = res.data.list
        this.addTotal = res.data.total
      })
    },
    saveAdd(type) {
      let selection = []
      if (type === 'add') {
        selection = this.$refs.addTable.selection.map(item => {
          return {
            ...item,
            deliveryId: this.deliveryHead.id
          }
        })
      } else if (type === 'copy') {
        const table = this.$refs.deliveryNoteGrid.getCheckboxRecords()
        if (table.length === 0) {
          this.$message.error(this.$t('order.selectAtLeastOneData'))
          return
        }
        selection = this.$refs.deliveryNoteGrid.getCheckboxRecords().map(item => {
          return {
            ...item,
            deliveryId: this.deliveryHead.id
          }
        })
        if (selection.length > 1) {
          this.$message.error(this.$t('order.onlyOnePieceOfDataCanBeCopied'))
          return
        }
        // 复制目前仅支持单行操作 && 可编辑字段不可复制
        selection[0].id = null
        selection[0].deliveryQuantity = 0
        // selection[0].produceDate = null
        // selection[0].validityPeriod = null
      }
      if (selection.length) {
        createDeliveryAdd({
          details: selection,
          type
        }).then(res => {
          this.showAdd = false
          this.getDeliveryDetailTable()
        })
      } else {
        this.$message.error(this.$t('supplier.pleaseSelectData'))
      }
    },
    /** 下载模板操作 */
    importTemplate() {
      getImportTemplate().then(response => {
        this.$download.excel(response, this.$t('order.batchCreateDeliveryDetailsImportTemplatexls'))
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data?.okNum) {
        text += '更新成功数量：' + data.okNum
      }

      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + data.failureNum
        text += '<br /><a class="el-button--text" target="_blank" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    submitFileForm() {
      this.$refs.upload.submit()
    },
    // 送货单上传附件
    beforeUpload(file) {
      // 【UFFF-1419】送货单未保存，不允许上传
      if (!this.deliveryHead.id) {
        this.$message({
          message: this.$t('order.pleaseSaveTheDeliveryNoteFirst'),
          type: 'warning'
        })
        return false
      }
      if (file.size > process.env.VUE_APP_FILESIZE * 1024 * 1024) {
        $modal.msgError(this.$t('common.uploadFileSizeCannotExceedm', { fileSize: process.env.VUE_APP_FILESIZE }))
        return false
      }
      if (this.fileList.length > 5) {
        $modal.msgError(this.$t('order.onlyUpToAttachmentsAreSupported'))
        return false
      }
      if (!this.fileType.split(',').includes(file.name.slice(file.name.lastIndexOf('.') + 1))) {
        $modal.msgError(this.$t('order.onlySupported') + this.fileType + this.$t('order.format'))
        return false
      }
    },
    onSuccess(response, file, fileList) {
      uploadDeliveryFile({
        deliveryId: this.deliveryHead.id,
        files: [{
          fileId: response.data.id,
          fileName: file.name,
          size: file.size
        }]
      }).then(res => {
        this.$message.success(this.$t('common.uploadSucceeded'))
        this.clickDeliveryHeadUpload()
      })
    },
    // // 送货明细行更新(仅草稿状态支持行编辑）
    beforeEditMethod({ row }) {
      if (this.errMap) {
        this.errMap = null
        return false
      } else {
        if (this.deliveryHead.status !== 'draft') {
          this.$message.error(this.$t('order.theCurrentStatusDoesNotSupportEditing'))
          return false
        }
        return true
      }
    },
    // 送货明细行编辑触发
    async editClosed({ row, rowIndex }) {
      if (rowIndex < 0) {
        return
      }
      const $table = this.$refs.deliveryNoteGrid
      this.errMap = await $table.validate([row]).catch(errMap => errMap)
      if (this.errMap) {
        await $table.reloadRow(row)
      } else {
        // 【UFFF-1420】不允许输入负数。会导致剩余可交数量大于订单数量
        if (row.deliveryQuantity < 0) {
          this.$message.error(this.$t('order.deliveryQuantityNotBiggerThanZero'))
          return
        }
        createDeliveryAdd({
          details: [row],
          type: 'save'
        }).then(res => {
          this.$message.success(this.$t('common.savedSuccessfully'))
          this.getDeliveryDetailTable()
        }).catch(_ => {
          // 恢复当前行为数据库的值
          $table.revertData(row)
        })
      }
    },
    invalidDelivery() {
      this.$confirm(this.$t('order.areYouSureYouWantToVoidThisDeliveryNoteThisOperationCannotBeRecovered'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        invalidDelivery({ id: this.deliveryHead.id }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.getHeader()
        })
      })
    },
    delDeliveryLine() {
      const data = this.$refs.deliveryNoteGrid.getCheckboxRecords().map(item => item.id)
      if (data.length < 1) {
        this.$message.error(this.$t('supplier.pleaseSelectData'))
        return
      }
      delDeliveryDetail({
        deliveryId: this.deliveryHead.id,
        ids: data.join(',')
      }).then(res => {
        this.$message.success(this.$t('common.delSuccess'))
        this.getDeliveryDetailTable()
      })
    },
    confirmDelivery() {
      confirmDelivery({ id: this.deliveryHead.id }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.getHeader()
      })
    },
    confirmGoods() {
      confirmReceive({ id: this.deliveryHead.id }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.getHeader()
      })
    },
    printDelivery() {
      printDeliverynote({
        id: this.deliveryHead.id
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        // 刷新送货抬头
        this.getHeader()
        var mapObject = new Map()
        mapObject.set('id', this.deliveryHead.id)
        this.showReportPreview(this.deliveryHead.reportId, mapObject)
      })
    },
    // 打开预览页面
    showReportPreview(reportId, params) {
      getPreviewReport({
        reportId: reportId,
        ...Object.fromEntries(params)
      }).then(res => {
        window.open(res.data)
      })
    },
    // 按订单添加操作
    doAddDelivery() {
      if (this.deliveryHead.id) {
        this.showAdd = true
        this.getAddTable()
      } else {
        this.$message.error(this.$t('order.pleaseSaveTheDeliveryNoteFirst'))
      }
    },
    /**
     * el-upload组件的{before-upload}方法return false时，会调用on-remove方法，因此需要通过status状态判断
     * @param file 未上传成功的状态是ready。
     * @param fileList
     */
    onRemove(file, fileList) {
      if (file?.status !== 'success') {
        return
      }
      this.fileList.splice(this.fileList.findIndex(a => a.id === file.id), 1)
      delDeliveryFile(file.id).then(_ => {
        this.$message.success(this.$t('common.delSuccess'))
      })
    },
    onPreview(file) {
      window.open(file.url)
    },
    resetDialogForm() {
      this.addQuery.factoryId = 0
      this.addQuery.materialCode = ''
      this.addQuery.mfg = ''
      this.addQuery.mpn = ''
      this.addQuery.orderNo = ''
      this.addQuery.pageNo = 1
      this.addQuery.pageSize = 10
      this.addQuery.supplierId = 0
    },
    closeDialog() {
      this.resetDialogForm()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

.searchItem {
  width: 50%;
  margin-right: 0;
  margin-bottom: 12px;
  ::v-deep .el-form-item__content{
    width: 70%;
  }
}

::v-deep .el-form-item.el-form-item--mini {
  //margin-bottom: 7px;
}

.deliveryHeadTwoSpanItem {
  width: 100%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 181px);
  }
}

.deliveryHeadItem {
  width: 100%;
}

::v-deep .el-form-item.el-form-item--mini + .el-form-item {
  margin-top: 0 !important;
}

.address {
  width: 100%;

  ::v-deep .el-form-item__content {
    width: calc(100% - 125px);
  }
}

.company {
  width: 100%;

  ::v-deep .el-form-item__content {
    width: calc(100% - 110px);
  }
}
.searchItemAdd{
  width: 165px;
}

</style>
