<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane :label="$t('order.deliveryNote')" name="first" />
      <el-tab-pane :label="$t('order.deliveryDetails')" name="second" />
    </el-tabs>
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.search	"
        :placeholder="$t('order.deliveryNoteNoAndSupplierAbbreviation')"
        clearable
        style="flex: 0 1 40%"
      />
      <el-button
        v-has-permi="['order:delivery-note:query']"
        type="primary"
        plain
        @click="handleSearch(true)"
      >{{ $t('common.search') }}</el-button>
      <el-button
        v-has-permi="['order:delivery-note:query']"
        style="margin-left: 0"
        plain
        @click="handleClick"
      >{{ $t('common.reset') }}</el-button>
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button
          v-has-permi="['order:delivery-note:query']"
          type="text"
        >
          {{ $t('common.advancedSearch') }}

        </el-button>
        <i
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>

    </div>
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="189px" size="small">

      <el-form-item :label="$t('order.deliveryNoteNo')" class="searchItem" prop="deliveryNoteNo">
        <el-input
          v-model="queryParams.deliveryNoteNo"
          :placeholder="$t('common.pleaseEnter')"
          class="searchValue"
          clearable
        />
      </el-form-item>
      <el-form-item :label="$t('common.buyer')" class="searchItem" prop="sourcingId">
        <el-select v-model="queryParams.sourcingId" class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('material.factory')" class="searchItem" prop="factoryIds">
        <el-select v-model="queryParams.factoryId" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas('factory',0)"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('supplier.supplier')" class="searchItem" prop="supplier">
        <el-input
          v-model="queryParams.supplier"
          :placeholder="$t('order.pleaseEnterTheSuppliersShortNameOrCode')"
          class="searchValue"
          clearable
        />
      </el-form-item>
      <el-form-item :label="$t('order.caseStickerStatus')" class="searchItem" prop="boxLabelStatus">
        <el-select v-model="queryParams.boxLabelStatus" class="searchValue" clearable disabled>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.ORDER_DETAIL_DELIVERY_STATUS)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="activeName==='second'" :label="$t('material.materialCode')" class="searchItem" prop="materialCode">
        <el-input v-model="queryParams.materialCode" :placeholder="$t('common.pleaseEnter')" class="searchValue" />
      </el-form-item>
      <el-form-item :label="$t('order.deliveryOrderStatus')" class="searchItem" prop="status">
        <el-select v-model="queryParams.status" class="searchValue" clearable multiple>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.ORDER_DELIVERY_STATUS, 0)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('order.timeType')" class="searchItem" prop="dateType">
        <el-select v-model="queryParams.dateType" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.ORDER_DATE_TYPE_IN_ORDER_DELIVERY)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label=" " class="searchItem">
        <el-date-picker
          v-model="queryParams.time"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          value-format="yyyy-MM-dd"
          class="searchValue"
          type="daterange"
        />
      </el-form-item>

      <el-form-item v-if="activeName==='second'" :label="$t('order.orderNumber')" class="searchItem" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          :placeholder="$t('common.pleaseEnter')"
          class="searchValue"
          clearable
        />
      </el-form-item>

      <!--      空item 占位的item-->
      <el-form-item v-if="activeName==='first'" label=" " class="searchItem" />
      <el-form-item v-if="activeName==='first'" label=" " class="searchItem" />
      <el-form-item v-if="activeName==='first'" label=" " class="searchItem" />

      <!--      空item 占位的item-->
      <el-form-item v-if="activeName==='second'" label=" " class="searchItem" />
    </el-form>

    <vxe-grid
      v-if="activeName === 'first'"
      ref="deliveryTable"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
      @sort-change="sortMethod"
    >
      <template #deliveryNoteNo="{row}">
        <copy-button
          type="text"
          @click="$router.push(`/order/orderDeliveryDetail/${row.id}?deliveryNoteNo=${row.deliveryNoteNo}`)"
        >
          {{ row.deliveryNoteNo }}
        </copy-button>
      </template>
      <template #factoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_FACTORY" :value="row.factoryId" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_DELIVERY_STATUS" :value="row.status" />
      </template>
      <template #deliveryMethodsCode="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_SHIPPING_METHOD" :value="row.deliveryMethodsCode" />
      </template>

      <template #toolbar_buttons>
        <el-row :gutter="10" class="mb8" style="width: 100%">
          <el-col :span="10" style="display: flex">
            <el-button
              v-hasPermi="['order:delivery-note:save']"
              icon="el-icon-plus"
              size="mini"
              type="primary"
              @click="$router.push('/order/orderDeliveryDetail/0')"
            > {{ $t('order.newDeliveryOrder') }}
            </el-button>
            <el-button
              v-has-permi="['order:delivery-detail:export']"
              size="mini"
              plain
              type="primary"
              @click="printPdf"
            >
              {{ $t('order.pdf') }}
            </el-button>
            <el-button
              v-has-permi="['order:delivery-note:export']"
              size="mini"
              plain
              type="primary"
              @click="downLoadExcel"
            >
              {{ $t('order.download') }}
            </el-button>

          </el-col>
          <el-col :span="14">
            <right-toolbar
              v-if="activeName === 'first'"
              key="orderDelivery"
              :custom-columns.sync="girdOption.columns"
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              @queryTable="init"
            />
          </el-col>
        </el-row>
      </template>

    </vxe-grid>
    <vxe-grid
      v-if="activeName === 'second'"
      ref="deliveryDetailTable"
      :data="list"
      :loading="loading"
      v-bind="girdOptionDetail"
      @sort-change="sortMethod"
    >
      <template #deliveryNoteNo="{row}">
        <copy-button
          type="text"
          @click="$router.push(`/order/orderDeliveryDetail/${row.id}?deliveryNoteNo=${row.deliveryNoteNo}`)"
        >
          {{ row.deliveryNoteNo }}
        </copy-button>
      </template>
      <template #factoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_FACTORY" :value="row.factoryId" />
      </template>
      <template #purchasingUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.purchasingUnit" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_DELIVERY_STATUS" :value="row.status" />

      </template>
      <template #categoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />

      </template>
      <template #deliveryStatus="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_DELIVERY_STATUS" :value="row.deliveryStatus" />
      </template>
      <template #deliveryMethodsCode="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_SHIPPING_METHOD" :value="row.deliveryMethodsCode" />
      </template>
      <template #deliveryQuantity="{row}">
        <number-format :value="row.deliveryQuantity" />
      </template>
      <template #toolbar_buttons>
        <el-row :gutter="10" class="mb8" style="width: 100%">
          <el-col :span="10" style="display: flex">

            <el-button
              v-has-permi="['order:delivery-detail:export']"
              size="mini"
              plain
              type="primary"
              @click="downLoadExcel"
            >
              {{ $t('order.download') }}
            </el-button>
          </el-col>
          <el-col :span="14">
            <right-toolbar
              v-if="activeName === 'second'"
              key="orderDeliveryLine"
              :custom-columns.sync="girdOptionDetail.columns"
              :list-id="girdOptionDetail.id"
              :show-search.sync="showSearch"
              @queryTable="getDeliveryDetail"
            />
          </el-col>
        </el-row>
      </template>
    </vxe-grid>

    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="handleSearch(false)"
    />

    <!-- 预览 -->
    <el-dialog
      v-if="showPreview.open"
      :close-on-click-modal="false"
      :visible.sync="showPreview.open"
      :title="$t('order.preview')"
      width="740px"
    >
      <report-preview
        :report-id="showPreview.reportId"
        :params="showPreview.params"
      />
    </el-dialog>
  </div>
</template>
<script>
import {
  exportDetailExcel,
  exportHeaderExcel,
  getOrderDeliveryDetailList,
  getOrderDeliveryList
} from '@/api/order/deliveryNote'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'
import { getBaseHeader } from '@/utils/request'
import reportPreview from '@/components/reportPreview/reportPreview'

export default {
  name: 'Deliverynoteindex',
  components: {
    reportPreview
  },
  data() {
    return {
      showSearch: false,
      // 默认的送货单状态下拉（过滤作废的状态）
      queryParams: {
        search: '',
        deliveryNoteNo: '',
        sourcingId: '',
        status: [],
        factoryId: '',
        supplier: '',
        orderNo: '',
        materialCode: '',
        pgIds: '',
        dateType: '',
        beginOrderDate: undefined,
        endOrderDate: undefined,
        sortBy: '',
        sortField: '',
        time: [],
        pageNo: 1,
        pageSize: 10
      },
      categoryList: [],
      factoryList: [],
      list: [],
      loading: false,
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'orderDelivery',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { title: this.$t('order.deliveryNoteNo'), slots: { default: 'deliveryNoteNo' }, field: 'deliveryNoteNo', visible: true, width: 200 },
          { title: this.$t('material.factory'), slots: { default: 'factoryId' }, field: 'factoryId', visible: true, width: 200 },
          { title: this.$t('common.creationDate'), sortable: true, field: 'createTime', visible: true },
          { title: this.$t('order.theDateOfIssuance'), sortable: true, field: 'deliveryDate', visible: true },
          { title: this.$t('common.buyer'), field: 'sourcingId',
            formatter: ({ cellValue }) => this.formatterBuyer(cellValue),
            visible: true },
          { title: this.$t('order.deliveryOrderStatus'), slots: { default: 'status' }, field: 'status', visible: true },
          { title: this.$t('order.expectedArrivalDate'), sortable: true, field: 'estimatedArrivalDate', visible: true },
          { title: this.$t('order.printDate'), sortable: true, field: 'boxPrintDate', visible: true },
          { title: this.$t('supplier.shortNameOfSupplier'), field: 'nameShort', visible: true },
          { title: this.$t('order.deliveryMethod'), slots: { default: 'deliveryMethodsCode' }, field: 'deliveryMethodsCode', visible: true },
          { title: this.$t('order.arrivalDate'), field: 'arrivalDate', sortable: true, visible: true }
        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      girdOptionDetail: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'orderDeliveryLine',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns: [
          { type: 'checkbox', width: 30, visible: true, fixed: 'left' },
          { title: this.$t('order.deliveryNoteNo'), slots: { default: 'deliveryNoteNo' }, field: 'deliveryNoteNo', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('order.deliveryLn'), field: 'deliveryItem', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('material.factory'), field: 'factoryId', slots: { default: 'factoryId' }, visible: true, width: 100 },
          { title: this.$t('order.orderNumber'), field: 'orderNo', visible: true, width: 100 },
          { title: this.$t('order.orderLineNo'), field: 'lineNo', visible: true, width: 100 },
          { title: this.$t('material.materialCode'), field: 'materialCode', visible: true, width: 100 },
          { title: this.$t('material.materialDescription'), field: 'materialDescription', visible: true, width: 100 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          { title: this.$t('material.purchasingUnit'), slots: { default: 'purchasingUnit' }, field: 'purchasingUnit', visible: true, width: 100 },
          { title: this.$t('order.orderDate'), sortable: true, field: 'orderDate', visible: true, width: 100 },
          { title: this.$t('order.deliveryQuantity'), slots: { default: 'deliveryQuantity' }, field: 'deliveryQuantity', visible: true, width: 100, align: 'right' },
          { title: this.$t('order.dateOfManufacture'), field: 'produceDate', visible: true, width: 100 },
          { title: this.$t('supplier.termOfValidity'), field: 'validityPeriod', visible: true, width: 100 },
          { title: this.$t('order.batchNo'), field: 'batchNo', visible: true, width: 100 },
          { title: this.$t('common.remarks'), field: 'remark', visible: true, width: 100 },
          { title: this.$t('order.storageConditions'), field: 'storageCondition', visible: true, width: 100 },
          { title: this.$t('order.theDateOfIssuance'), field: 'deliveryDate', sortable: true, visible: true, width: 100 },
          { title: this.$t('common.buyer'), field: 'sourcingId',
            formatter: ({ cellValue }) => this.formatterBuyer(cellValue),
            visible: true, width: 100 },
          { title: this.$t('order.expectedArrivalDate'), field: 'estimatedArrivalDate', sortable: true, visible: true, width: 100 },
          { title: this.$t('order.printDate'), field: 'boxPrintDate', visible: true, sortable: true, width: 100 },
          { title: this.$t('supplier.shortNameOfSupplier'), field: 'nameShort', visible: true, width: 100 },
          { title: this.$t('order.deliveryMethod'), slots: { default: 'deliveryMethodsCode' }, field: 'deliveryMethodsCode', visible: true, width: 100 },
          { title: this.$t('order.deliveryDriver'), field: 'shipperName', visible: true, width: 100 },
          { title: this.$t('order.deliveryDriversTelephone'), field: 'shipperMobile', visible: true, width: 100 },
          { title: this.$t('order.deliveryNoteRemarks'), field: 'shipperRemark', visible: true, width: 100 },
          { title: this.$t('order.supplierContact'), field: 'supplierContact', visible: true, width: 100 },
          { title: this.$t('order.supplierContactNumber'), field: 'supplierPhone', visible: true, width: 100 },
          { title: this.$t('order.deliveryAddress'), field: 'shippingAddress', visible: true, width: 100 },
          { title: this.$t('order.deliveryOrderStatus'), field: 'status', slots: { default: 'status' }, visible: true, width: 100 },
          { title: this.$t('order.concentration'), field: 'concentration', visible: true, width: 100 },
          { title: this.$t('order.externalInspectionMaterials'), field: 'inspectionMaterial', visible: true, width: 100 },
          { title: this.$t('order.conclusionOfExternalInspection'), field: 'inspectionConclusion', visible: true, width: 100 },
          { title: this.$t('order.registrationCertificateNo'), field: 'registrationNumber', visible: true, width: 100 },
          { title: this.$t('order.productionLicenseNo'), field: 'prCertificateNo', visible: true, width: 100 }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      total: 0,
      activeName: 'first',
      // 订单导入参数
      upload: {
        // 是否显示弹出层（订单导入）
        open: false,
        // 弹出层标题（订单导入）
        title: '',
        type: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/order/header/import'
      },
      showPreview: {
        // 是否显示弹出层（物料导入）
        open: false,
        reportId: undefined,
        params: null
      }
    }
  },
  mounted() {
    this.queryParams.status = this.getDictDatas(this.DICT_TYPE.ORDER_DELIVERY_STATUS, 0)?.filter(i => i.value !== 'cancel').map(i => i.value)
    this.init()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 送货单列表
    init() {
      this.loading = true
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.queryParams.time, 'orderDate')
      getOrderDeliveryList(params).then(res => {
        this.loading = false
        this.orderDetail = res.data
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    // 送货明细列表
    getDeliveryDetail() {
      this.loading = true
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.queryParams.time, 'orderDate')
      getOrderDeliveryDetailList(params).then(res => {
        this.loading = false
        this.orderDetail = res.data
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.handleSearch(false)
    },
    // 下载pdf
    printPdf() {
      const selected = this.$refs.deliveryTable.getCheckboxRecords()
      if (selected.length === 0 || selected.length > 1) {
        this.$message.warning(this.$t('order.pleaseSelectAPieceOfData'))
        return
      }
      if (!selected[0].reportId) {
        this.$message.error(this.$t('order.pleaseContactTheAdministratorToSetThePdfTemplateConfigurationOfTheDeliveryOrder'))
        return
      }
      this.showPreview.open = true
      this.showPreview.reportId = selected[0].reportId
      var mapObject = new Map()
      mapObject.set('id', selected[0].id)
      // _t 的value 决定了预览顶部展示的小工具类型
      mapObject.set('_t', '2,4')
      this.showPreview.params = mapObject
    },
    handleClick() {
      this.queryParams = {
        search: '',
        deliveryNoteNo: '',
        sourcingId: '',
        status: [],
        factoryId: '',
        supplier: '',
        orderNo: '',
        materialCode: '',
        pgIds: '',
        dateType: '',
        sortBy: '',
        sortField: '',
        time: '',
        pageNo: 1,
        pageSize: 10
      }
      this.handleSearch(true)
    },
    downLoadExcel() {
      // UFFF-1711:送货单列表默认不展示作废的单据
      if (this.queryParams.status?.length === 0) {
        this.queryParams.status = this.getDictDatas(this.DICT_TYPE.ORDER_DELIVERY_STATUS, 0)?.filter(i => i.value !== 'cancel').map(i => i.value)
      }
      switch (this.activeName) {
        case 'first':
          exportHeaderExcel(this.queryParams).then(res => {
            this.$download.excel(res, '送货单.xlsx')
          })
          break
        case 'second':
          exportDetailExcel(this.queryParams).then(res => {
            this.$download.excel(res, '送货单明细.xlsx')
          })
          break
      }
    },
    handleSearch(resetPageNo) {
      if (resetPageNo) {
        // 跨页后使用前面页数据进行搜索，此时未重置pageNo导致搜索结果为空
        this.queryParams.pageNo = 1
      }
      // UFFF-1711:送货单列表默认不展示作废的单据
      if (this.queryParams.status?.length === 0) {
        this.queryParams.status = this.getDictDatas(this.DICT_TYPE.ORDER_DELIVERY_STATUS, 0)?.filter(i => i.value !== 'cancel').map(i => i.value)
      }
      switch (this.activeName) {
        case 'first':
          this.init()
          break
        case 'second':
          this.getDeliveryDetail()
          break
      }
    },
    formatterBuyer(cellVal) {
      return getDictDataLabel(DICT_TYPE.COMMON_USERS, cellVal?.split(',')) || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 189px);
  }
}

.searchTimeItem {
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 82px);
  }
}

.searchValue {
  width: 95%;
}
</style>
