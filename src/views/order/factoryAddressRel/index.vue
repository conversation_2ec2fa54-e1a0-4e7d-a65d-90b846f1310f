<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('material.factory')" class="searchItem" prop="factoryId">
        <el-select v-model="queryParams.factoryId" class="searchValue" clearable>
          <el-option v-for="item in factoryList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('order.shippingAddress')" prop="deliveryAddress">
        <el-input
          v-model="queryParams.deliveryAddress"
          :placeholder="$t('order.pleaseEnterTheDeliveryAddress')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          v-has-permi="['order:factory-address-rel:query']"
          icon="el-icon-search"
          type="primary"
          plain
          @click="handleQuery"
        >{{ $t('common.search') }}</el-button>
        <el-button
          v-has-permi="['order:factory-address-rel:query']"
          icon="el-icon-refresh"
          @click="resetQuery"
        >{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['order:factory-address-rel:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('material.factory')" align="center" prop="factoryName" />
      <el-table-column :label="$t('order.deliveryAddress')" align="center" prop="deliveryAddress" show-overflow-tooltip />
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['order:factory-address-rel:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['order:factory-address-rel:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item
          :label="$t('material.factory')"
          class="searchItem"
          prop="factoryId"
        >
          <el-select v-model="form.factoryId" clearable style="width: 100%;">
            <el-option v-for="item in factoryList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('order.shippingAddress')" prop="deliveryAddress">
          <el-input v-model="form.deliveryAddress" :placeholder="$t('order.pleaseEnterTheDeliveryAddress')" maxlength="100" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-has-permi="['order:factory-address-rel:create']"
          @click="cancel"
        >{{ $t('common.cancel') }}</el-button>
        <el-button
          v-has-permi="['order:factory-address-rel:create']"
          type="primary"
          @click="submitForm"
        >{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createFactoryAddressRel,
  deleteFactoryAddressRel,
  getFactoryAddressRel,
  getFactoryAddressRelPage,
  updateFactoryAddressRel
} from '@/api/order/factoryAddressRel'
import { listFactoryFromCache } from '@/api/system/factory'

export default {
  name: 'Factoryaddressrel',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单/工厂与地址关系配置列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        factoryId: null,
        deliveryAddress: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        factoryId: [{ required: true, message: this.$t('order.factoryRequired'), trigger: 'blur' }],
        deliveryAddress: [{ required: true, message: this.$t('order.deliveryAddressIsRequired'), trigger: 'blur' }]
      },
      factoryList: []
    }
  },
  async mounted() {
    await this.getFactory()
    this.getList()
  },
  methods: {
    async getFactory() {
      const res = await listFactoryFromCache({ status: 0 })
      this.factoryList = res.data
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getFactoryAddressRelPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        factoryId: undefined,
        deliveryAddress: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('order.addOrderfactoryAndAddressRelationshipConfiguration')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getFactoryAddressRel(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('order.modifyOrderfactoryAndAddressRelationshipConfiguration')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateFactoryAddressRel(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createFactoryAddressRel(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm(this.$t('order.areYouSureToDeleteTheOrderfactoryAndAddressRelationshipConfigurationNumberIs') + id + this.$t('material.dataItemOf')).then(function() {
        return deleteFactoryAddressRel(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    }
  }
}
</script>
