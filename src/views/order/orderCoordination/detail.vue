<template>
  <div style="padding: 25px 15px">
    <el-card>
      <div slot="header">
        {{ $t('order.orderHeader') }}
      </div>
      <el-descriptions label-class-name="labelTitle" :colon="false" :column="2">
        <el-descriptions-item :label="$t('order.orderNumber')">{{ orderDetail.orderNo }}</el-descriptions-item>
        <el-descriptions-item :label="$t('order.orderDate')">{{ orderDetail.orderDate }}</el-descriptions-item>
        <el-descriptions-item :label="$t('order.company')">
          <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="orderDetail.buyerCompanyId" />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('system.currency')">
          <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="orderDetail.currency" />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.shortNameOfSupplier')">{{
          orderDetail.supplierNameShort
        }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('order.supplierContact')">{{
          orderDetail.supplierContact
        }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.deliveryTerms')">
          <dict-tag :type="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION" :value="orderDetail.deliveryType" />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.accountingPeriod')">
          <dict-tag :type="DICT_TYPE.SUPPLIER_PAYMENT_DAYS" :value="orderDetail.accountDay" />

        </el-descriptions-item>
        <el-descriptions-item :label="$t('order.deliveryAddress')">{{
          orderDetail.buyerDeliveryAddress
        }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('order.orderType')">
          <dict-tag :type="DICT_TYPE.ORDER_TYPE" :value="orderDetail.orderType" />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.invoiceAddress')">{{
          orderDetail.buyerInvoiceAddress
        }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('common.buyer')">
          <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="orderDetail.sourcingId" />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('order.orderRemarks')">{{
          orderDetail.remark
        }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('order.supplierContactPhone')">{{
          orderDetail.supplierContactPhone
        }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('order.frameworkContractNumber')">
          {{ orderDetail.frameworkContractNumber }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('order.contractName')">
          {{ orderDetail.contractNumber }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('order.orderSigningStatus')">
          <dict-tag :type="DICT_TYPE.ORDER_SIGN_STATUS" :value="orderDetail.signStatus" />
        </el-descriptions-item>
        <el-descriptions-item>
          <span v-if="orderDetail.signStatus === 'waiting_sign'">
            <el-button
              plain
              size="mini"
              type="primary"
              icon="el-icon-check"
              @click="acceptLine"
            >
              {{ $t('order.orderReceipt') }}
            </el-button>
            <el-button
              plain
              size="mini"
              type="danger"
              icon="el-icon-close"
              @click="rejectLine"
            >
              {{ $t('order.rejectOrder') }}
            </el-button>
          </span>
        </el-descriptions-item>
      </el-descriptions>

    </el-card>
    <el-card style="margin-top: 20px">
      <div slot="header">
        {{ $t('order.orderLine') }}
      </div>
      <vxe-grid
        ref="orderDetail"
        :data="list"
        :loading="loading"
        v-bind="girdOption"
      >
        <template #orderId>
          {{ orderDetail.orderNo }}
        </template>
        <template #factoryId="{row}">
          <dict-tag :type="'factory'" :value="row.factoryId" />
        </template>
        <template #orderDate>
          {{ orderDetail.orderDate }}
        </template>
        <template #sourcingId>
          <dict-tag :type="'userList'" :value="orderDetail.sourcingId" />
        </template>
        <template #currency>
          <dict-tag :type="'currency'" :value="orderDetail.currency" />
        </template>
        <template #purchasingUnit="{row}">
          <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.purchasingUnit" />
        </template>
        <template #status="{row}">
          <dict-tag :type="DICT_TYPE.ORDER_DETAIL_STATUS" :value="row.status" />
        </template>
        <template #categoryId="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
        </template>
        <template #deliveryConfirmStatus="{row}">
          <dict-tag :type="DICT_TYPE.ORDER_DETAIL_CONFIRM_STATUS" :value="row.deliveryConfirmStatus" />
        </template>
        <template #deliveryStatus="{row}">
          <dict-tag :type="DICT_TYPE.ORDER_DETAIL_DELIVERY_STATUS" :value="row.deliveryStatus" />
        </template>
        <template #taxRate="{row}">
          <dict-tag :type="DICT_TYPE.SUPPLIER_RATE" :value="row.taxRate" />
        </template>
        <template #receiptStatus="{row}">
          <dict-tag :type="DICT_TYPE.ORDER_DETAIL_RECEIPT_STATUS" :value="row.receiptStatus" />
        </template>
        <template #quantity="{row}">
          <number-format v-if="row.returnProject" :value="-row.quantity" :font-color="'red'" />
          <number-format v-else-if="lessThanZero(row.quantity)" :value="row.quantity" :font-color="'red'" />
          <number-format v-else :value="row.quantity" />
        </template>
        <template #unitPriceAfterTaxNoPriceUnit="{row}">
          <number-format v-if="row.returnProject" :value="-row.unitPriceAfterTaxNoPriceUnit" :font-color="'red'" />
          <number-format
            v-else-if="lessThanZero(row.unitPriceAfterTaxNoPriceUnit)"
            :value="row.unitPriceAfterTaxNoPriceUnit"
            :font-color="'red'"
          />
          <number-format v-else :value="row.unitPriceAfterTaxNoPriceUnit" />
        </template>
        <template #unitPriceBeforeTaxNoPriceUnit="{row}">
          <number-format v-if="row.returnProject" :value="-row.unitPriceBeforeTaxNoPriceUnit" :font-color="'red'" />
          <number-format
            v-else-if="lessThanZero(row.unitPriceBeforeTaxNoPriceUnit)"
            :value="row.unitPriceBeforeTaxNoPriceUnit"
            :font-color="'red'"
          />
          <number-format v-else :value="row.unitPriceBeforeTaxNoPriceUnit" />
        </template>
        <template #unitPriceAfterTax="{row}">
          <number-format v-if="row.returnProject" :value="-row.unitPriceAfterTax" :font-color="'red'" />
          <number-format
            v-else-if="lessThanZero(row.unitPriceAfterTax)"
            :value="row.unitPriceAfterTax"
            :font-color="'red'"
          />
          <number-format v-else :value="row.unitPriceAfterTax" />
        </template>
        <template #unitPriceBeforeTax="{row}">
          <number-format v-if="row.returnProject" :value="-row.unitPriceBeforeTax" :font-color="'red'" />
          <number-format
            v-else-if="lessThanZero(row.unitPriceBeforeTax)"
            :value="row.unitPriceBeforeTax"
            :font-color="'red'"
          />
          <number-format v-else :value="row.unitPriceBeforeTax" />
        </template>
        <template #amountBeforeTax="{row}">
          <number-format v-if="row.returnProject" :value="-row.amountBeforeTax" :font-color="'red'" />
          <number-format
            v-else-if="lessThanZero(row.amountBeforeTax)"
            :value="row.amountBeforeTax"
            :font-color="'red'"
          />
          <number-format v-else :value="row.amountBeforeTax" />
        </template>
        <template #amountAfterTax="{row}">
          <number-format v-if="row.returnProject" :value="-row.amountAfterTax" :font-color="'red'" />
          <number-format v-else-if="lessThanZero(row.amountAfterTax)" :value="row.amountAfterTax" :font-color="'red'" />
          <number-format v-else :value="row.amountAfterTax" />
        </template>
        <template #drawing="{row}">
          <el-button
            v-has-permi="['order:header:query']"
            plain
            disabled
            type="text"
            @click="openUrl(row.drawing)"
          >{{ $t('order.download') }}
          </el-button>
        </template>

        <template #operate="{row}">
          <OperateDropDown
            :menu-item="[
              {
                name: $t('common.operationRecord'),

                show: $store.getters.permissions.includes('order:header:query'),
                action: (row) => handleLog(row),
                para: row.id
              },

            ]"
          />
          <!--          <el-button-->
          <!--            v-has-permi="['order:header:query']"-->
          <!--            plain-->
          <!--            type="text"-->
          <!--            @click="handleLog(row.id)"-->
          <!--          >-->
          <!--            {{ $t('common.operationRecord') }}-->
          <!--          </el-button>-->
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="10" style="width: 100%" class="mb8">
            <el-col :span="10" style="display: flex">
              <el-button
                v-has-permi="['order:header:query']"
                plain
                size="mini"
                type="primary"
                icon="el-icon-download"
                @click="exportData"
              >
                {{ $t('order.download') }}
              </el-button>
              <el-button
                v-has-permi="['order:header:query']"
                plain
                type="primary"
                icon="el-icon-printer"
                @click="downloadPdf"
              >
                {{ $t('order.pdf') }}
              </el-button>
              <el-button
                v-has-permi="['order:header:query']"
                plain
                type="primary"
                icon="el-icon-folder"
                @click="showBackSignFile"
              >
                {{ $t('order.signBackFile') }}
              </el-button>
              <el-button
                v-has-permi="['order:header:query']"
                plain
                type="primary"
                @click="showHis"
              >
                {{ $t('order.historicalVersion') }}
              </el-button>
              <el-button
                v-has-permi="['order:detail:complete:delivery']"
                plain
                type="primary"
                @click="handleDelivery()"
              >
                {{ $t('order.deliveryCompleted') }}
              </el-button>

            </el-col>
            <el-col :span="14">
              <!--              <right-toolbar :list-id="girdOption.id" :show-search.sync="showSearch" :custom-columns.sync="girdOption.columns" @queryTable="getList" />-->
            </el-col>
          </el-row>
        </template>
      </vxe-grid>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="init"
      />
    </el-card>
    <!--查看订单的历史版本组件-->
    <historyversion ref="historyversion" />
    <!--    状态字段的操作记录-->
    <el-dialog
      v-if="log.open"
      :close-on-click-modal="false"
      :title="$t('common.operationRecord')"
      :visible.sync="log.open"
      width="1000px"
    >
      <operation-record
        :business-id="log.businessId"
        :columns="log.columns"
        :log-visible.sync="log.open"
      />
    </el-dialog>
    <el-dialog
      v-if="backSignFileShow"
      :title="$t('order.signBackFile')"
      :visible.sync="backSignFileShow"
      width="1000px"
    >
      <el-table :data="backSignFileList">
        <el-table-column :label="$t('order.uploadDate')" prop="createTime">
          <template #default="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('order.orderVersion')" prop="orderVersion" />
        <el-table-column :label="$t('order.uploadUsers')" prop="creator" />
        <el-table-column :label="$t('avpl.source')" prop="source">
          <template #default="scope">
            <dict-tag :value="scope.row.source" :type="DICT_TYPE.ORDER_CONFIRM_FILE_SOURCE" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('auth.documentName')" prop="name" />
        <el-table-column :label="$t('common.operate')">
          <template #default="scope">
            <el-button type="text" @click="download(scope.row)">{{ $t('order.download') }}</el-button>
            <el-button type="text" @click="delFile(scope.row)">{{ $t('common.del') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button @click="backSignFileShow=false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="showUpload">{{ $t('order.uploadSignatureFile') }}</el-button>
        <el-button type="primary" @click="backSignFileShow=false">{{ $t('order.determine') }}</el-button>
      </div>

    </el-dialog>
    <el-dialog
      v-if="uploadVisible"
      :visible.sync="uploadVisible"
      :title="$t('common.uploadFile')"
      width="400px"
    >
      <el-upload
        ref="upload"
        :action="url"
        :disabled="disabled"
        :drag="!disabled"
        :file-list="fileList"
        :headers="headers"
        :on-remove="handleRemove"
        :on-success="handleFileSuccess"
        :limit="1"
        class="upload-container"
        multiple
      >
        <i v-if="!disabled" class="el-icon-upload" />
        <div v-if="!disabled" class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
      </el-upload>

      <el-form style="margin-top: 15px">
        <el-form-item :label="$t('order.orderVersion')" required>
          <el-select v-model="orderVersion">
            <el-option
              v-for="item in versionList"
              :label="item.version"
              :value="item.version"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="!disabled"
          type="primary"
          @click="submitFileForm"
        > {{ $t('common.confirm') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  completeDelivery,
  exportDetail,
  getOrderDetail,
  pagingOrderDetails, getLatestPdfWithOrderId
} from '@/api/order/orderCoordination'
import operationRecord from '@/components/OperationRecord/operationRecord'
import historyversion from '@/views/order/orderCoordination/historyversion'
import { fileBind, getOrderConfirmFile, getOrderVersionList, orderSignFor, orderTern } from '@/api/orderSign'
import { getAccessToken } from '@/utils/auth'
import { parseTime } from '../../../utils/ruoyi'
import { delDeliveryFile } from '@/api/order/deliveryNote'
import OperateDropDown from '@/components/OperateDropDown/index.vue'

export default {
  name: 'Orderdetail/:orderid',
  components: {
    OperateDropDown,
    operationRecord, historyversion
  },
  data() {
    return {
      // 状态字段的操作记录
      isUploading: false,
      url: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      data: {},
      disabled: false,
      fileList: [],
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: [
          {
            tableName: 'order_detail',
            searchColumns: ['status']
          }
        ]
      },
      showSearch: false,
      queryParams: {
        orderId: 1,
        pageNo: 1,
        pageSize: 10,
        search: '',
        materialCode: '',
        category: '',
        purchaseType: '',
        factoryIds: '',
        pgIds: ''
      },
      categoryList: [],
      factoryList: [],
      list: null,
      loading: false,
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'order',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { type: 'checkbox', width: 30 },
          {
            field: 'operate',
            slots: { default: 'operate' },
            fixed: 'left',
            title: this.$t('common.operate'),
            visible: true,
            width: 40
          },
          { title: this.$t('order.orderLineNo'), sortable: true, field: 'lineNo', visible: true, width: 100 },
          {
            title: this.$t('material.factory'),
            field: 'factoryId',
            slots: { default: 'factoryId' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('order.orderLineStatus'),
            field: 'status',
            slots: { default: 'status' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('order.shipmentStatus'),
            slots: { default: 'deliveryStatus' },
            field: 'deliveryStatus',
            visible: true,
            width: 100
          },
          {
            title: this.$t('order.receivingStatus'),
            slots: { default: 'receiptStatus' },
            field: 'receiptStatus',
            visible: true,
            width: 100
          },
          // { title: this.$t('order.orderReviewNo'), field: 'orderReviewNumber', visible: true, width: 100 },
          { title: this.$t('order.freeService'), field: 'freeProject', visible: true, width: 100 },
          { title: this.$t('order.returnedItems'), field: 'returnProject', visible: true, width: 100 },
          {
            title: this.$t('order.orderQuantity'),
            field: 'quantity',
            slots: { default: 'quantity' },
            visible: true,
            width: 100,
            align: 'right'
          },
          {
            title: this.$t('order.orderDate'),
            slots: { default: 'orderDate' },
            field: 'orderDate',
            visible: true,
            width: 100
          },
          {
            title: this.$t('order.buyer'),
            slots: { default: 'sourcingId' },
            field: 'sourcingId',
            visible: true,
            width: 100
          },
          { title: this.$t('material.materialCode'), field: 'materialCode', visible: true, width: 100 },
          { title: this.$t('material.materialDescription'), field: 'materialDescription', visible: true, width: 100 },
          {
            title: this.$t('common.loginCategory'),
            field: 'categoryId',
            slots: { default: 'categoryId' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('material.purchasingUnit'),
            field: 'purchasingUnit',
            slots: { default: 'purchasingUnit' },
            visible: true,
            width: 100
          },
          { title: this.$t('material.revision'), field: 'materialVersion', visible: true, width: 100 },
          // XUERES-951 订单行表的型号字段名称错误
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          { title: this.$t('material.specificationAndModel'), field: 'specification', visible: true, width: 100 },
          { title: this.$t('material.brand'), field: 'brand', visible: true, width: 100 },
          {
            title: this.$t('supplier.taxRate'),
            slots: { default: 'taxRate' },
            field: 'taxRate',
            visible: true,
            width: 100
          },
          {
            title: this.$t('order.unitPriceIncludingTax'),
            slots: { default: 'unitPriceAfterTaxNoPriceUnit' },
            field: 'unitPriceAfterTaxNoPriceUnit',
            visible: true,
            width: 100,
            align: 'right'
          },
          {
            title: this.$t('order.unitPriceWithoutTax'),
            slots: { default: 'unitPriceBeforeTaxNoPriceUnit' },
            field: 'unitPriceBeforeTaxNoPriceUnit',
            visible: true,
            width: 100,
            align: 'right'
          },
          // XUERES-951 订单行表的型号字段名称错误
          {
            title: this.$t('order.unitPriceIncludingTaxincludingPriceUnit'),
            slots: { default: 'unitPriceAfterTax' },
            field: 'unitPriceAfterTax',
            visible: true,
            width: 120,
            align: 'right'
          },
          {
            title: this.$t('order.unitPriceExcludingTaxincludingPriceUnit'),
            slots: { default: 'unitPriceBeforeTax' },
            field: 'unitPriceBeforeTax',
            visible: true,
            width: 120,
            align: 'right'
          },
          { title: this.$t('material.priceUnit'), field: 'priceUnit', visible: true, width: 100 },
          {
            title: this.$t('order.amountNotTaxed'),
            field: 'amountBeforeTax',
            slots: { default: 'amountBeforeTax' },
            visible: true,
            width: 100,
            align: 'right'
          },
          {
            title: this.$t('order.amountIncludingTax'),
            field: 'amountAfterTax',
            slots: { default: 'amountAfterTax' },
            visible: true,
            width: 100,
            align: 'right'
          },
          {
            title: this.$t('order.requiredArrivalDate'),
            field: 'requestDeliveryDate',
            sortable: true,
            visible: true,
            width: 100
          },
          {
            title: this.$t('order.latestRequiredArrivalDate'),
            field: 'latestRequestDeliveryDate',
            visible: true,
            sortable: true,
            width: 100
          },
          {
            title: this.$t('order.delivery_confirmation_status'),
            slots: { default: 'deliveryConfirmStatus' },
            field: 'deliveryConfirmStatus',
            visible: true,
            width: 100
          },
          { title: this.$t('order.orderLineComments'), field: 'remarks', visible: true, width: 100 },
          {
            title: this.$t('order.drawing'),
            field: 'drawing',
            slots: { default: 'drawing' },
            visible: true,
            width: 100
          },
          { title: this.$t('material.drawingVersion'), field: 'drawVersion', visible: true, width: 100 },
          { title: this.$t('supplier.shortNameOfSupplier'), field: 'supplierNameShort', visible: true, width: 100 },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 }
        ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      total: 0,
      activeName: 'first',
      orderDetail: {},
      exportLoading: false,
      backSignFileShow: false,
      backSignFileList: [],
      uploadVisible: false,
      versionList: [],
      orderVersion: ''
    }
  },
  mounted() {
    this.queryParams.orderId = this.$route.params.orderId
    this.init()
  },
  methods: {
    parseTime,
    init() {
      this.loading = true
      // 获取订单详情：1.获取订单抬头信息；2.获取订单行分页集合
      getOrderDetail({ orderId: this.queryParams.orderId }).then(res => {
        this.orderDetail = res.data
      })
      pagingOrderDetails(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    openUrl(url) {
      window.open(url)
    },
    // 下载最新版本的pdf
    downloadPdf() {
      this.$modal.confirm(this.$t('order.areYouSureToDownloadTheOrderPdf')).then(() => {
        getLatestPdfWithOrderId(this.$route.params.orderId).then(res => {
          if (res.data) {
            window.open(res.data)
          } else {
            this.$message.warning(this.$t('order.theOrderWasNotSuccessfully'))
          }
        })
      })
    },
    handleDelivery() {
      const selected = this.$refs.orderDetail.getCheckboxRecords()
      if (selected.length === 0) {
        this.$message.warning(this.$t('order.pleaseSelectTheOrderLineToBeShipped'))
        return
      }
      completeDelivery({
        ids: selected.map(item => item.id),
        orderId: this.queryParams.orderId
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.init()
      })
    },
    handleLog(orderDetailId) {
      this.log.open = true
      this.log.businessId = orderDetailId
    },
    exportData() {
      this.$modal.confirm(this.$t('order.areYouSureToExportAllOrderDataItems')).then(() => {
        this.exportLoading = true
        return exportDetail(this.queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('order.orderxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    // 订单历史版本
    showHis() {
      this.$refs.historyversion.historyVisible = true
      this.$refs.historyversion.hisParams.orderId = this.$route.params.orderId
      this.$refs.historyversion.getHisList()
    },
    // 判断num是否小于0
    lessThanZero(num) {
      if (num) {
        return num < 0
      }
      return false
    },
    acceptLine() {
      orderSignFor([Number(this.queryParams.orderId)]).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.init()
      })
    },
    rejectLine() {
      orderTern([Number(this.queryParams.orderId)]).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.init()
      })
    },
    showBackSignFile() {
      this.backSignFileShow = true
      getOrderConfirmFile({
        businessId: Number(this.queryParams.orderId),
        businessType: 'order_confirm'
      }).then(res => {
        this.backSignFileList = res.data
      })
    },
    getVersion() {
      getOrderVersionList({
        id: Number(this.queryParams.orderId)
      }).then(res => {
        this.versionList = res.data
        // 需要默认选择最高的订单版本
        if (this.versionList) {
          this.orderVersion = Math.max(...this.versionList.map(item => item.version))
        }
      })
    },
    showUpload() {
      this.getVersion()
      this.uploadVisible = true
      this.fileList = []
      this.orderVersion = ''
    },
    submitFileForm() {
      if (!this.orderVersion) {
        this.$message.error(this.$t('order.pleaseSelectVersion'))
        return
      }
      fileBind({
        businessId: Number(this.queryParams.orderId),
        businessType: 'order_confirm',
        fileId: this.fileList.at(0)?.response.data.id,
        orderVersion: this.orderVersion,
        source: 'user_upload'
      }).then(res => {
        this.$message.success(this.$t('common.uploadSucceeded'))
        this.uploadVisible = false
        this.showBackSignFile()
        this.init()
      })
    },
    handleRemove(file, fileList) {
      this.fileList = []
    },
    handleFileSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
        this.$refs.upload.clearFiles()
        return
      }
      this.fileList = fileList
    },
    delFile(row) {
      delDeliveryFile(row.id).then(res => {
        this.$message.success(this.$t('common.delSuccess'))
        this.showBackSignFile()
      })
    },
    download(row) {
      window.open(row.path)
    }
  }

}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

::v-deep .labelTitle {
  text-align: right;
  width: 130px;
  display: inline;
}

</style>
