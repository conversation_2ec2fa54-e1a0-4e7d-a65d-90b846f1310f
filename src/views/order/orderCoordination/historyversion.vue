<template>
  <el-dialog
    v-if="historyVisible"
    :title="$t('order.historicalVersion')"
    :visible.sync="historyVisible"
    @close="cancel"
  >
    <el-table :data="historyData">
      <el-table-column :label="$t('order.edition')" prop="version" width="100px" />
      <el-table-column :label="$t('order.logInformation')" prop="logContent" width="700px">
        <template slot-scope="scope">
          <div v-if="scope.row.version&&scope.row.version!==1" v-html="scope.row.logContent" />
          <el-button
            v-else
            style="text-decoration: underline;flex:none"
            type="text"
            @click="openInfo(scope.row.logContent)"
          >{{ $t('order.orderAddition') }}</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('order.updatedBy')" prop="userName" width="100px" />
      <el-table-column :label="$t('supplier.updateTime')" prop="createTime" width="100px" :formatter="row => parseTime(row.createTime, '{y}-{m}-{d} {h}:{i}:{s}')" />
      <el-table-column :label="$t('order.download')" prop="pdf" width="100px">
        <template slot-scope="scope">
          <el-button type="text" @click="openUrl(scope.row.pdf)">{{ $t('order.download') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="hisTotal > 0"
      :total="hisTotal"
      :page.sync="hisParams.pageNo"
      :limit.sync="hisParams.pageSize"
      @pagination="getHisList"
    />
    <el-dialog
      v-if="open"
      :close-on-click-modal="false"
      append-to-body
      :title="$t('order.orderAddition')"
      :visible.sync="open"
      width="800px"
    >
      <div style="padding: 20px" v-html="logContent" />
    </el-dialog>
  </el-dialog>
</template>

<script>
import {
  getVersionHis
} from '@/api/order/orderCoordination'

export default {
  name: 'Historyversion',
  data() {
    return {
      open: false,
      logContent: false,
      historyVisible: false,
      hisParams: {
        orderId: undefined,
        pageSize: 10,
        pageNo: 1
      },
      historyData: [],
      hisTotal: 0
    }
  },
  methods: {
    openInfo(logContent) {
      this.logContent = logContent
      this.open = true
    },
    openUrl(url) {
      window.open(url)
    },
    getHisList() {
      getVersionHis(this.hisParams).then(res => {
        this.historyData = res.data.respVOS
        // 订单历史版本总数等于 version的值
        this.hisTotal = res.data.total
      })
    },
    // 取消发送
    cancel() {
      this.historyVisible = false
    }
  }
}
</script>

<style scoped>

</style>
