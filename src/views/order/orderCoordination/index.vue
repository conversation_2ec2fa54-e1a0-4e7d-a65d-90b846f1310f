<template>
  <div class="app-container">
    <StatisticsCard :item-arr="cardData" />
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane :label="$t('order.allOrders')" name="first" />
      <el-tab-pane :label="$t('order.allOrderLines')" name="second" />
      <el-tab-pane :label="$t('order.receivingDetails')" name="third" />
    </el-tabs>
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input v-model="queryParams.search	" style="flex: 0 1 40%" :placeholder="$t('order.orderNoMaterialCodeSupplierName')" clearable @keyup.enter.native="handleSearch(true)" />
      <el-button
        v-has-permi="['order:header:query']"
        type="primary"
        plain
        @click="handleSearch(true)"
      >{{ $t('common.search') }}</el-button>
      <el-button
        v-has-permi="['order:header:query']"
        style="margin-left: 0"
        plain
        @click="handleClick"
      >{{ $t('common.reset') }}</el-button>
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button
          v-has-permi="['order:header:query']"
          type="text"
        >
          {{ $t('common.advancedSearch') }}
        </el-button>
        <i
          class="el-icon-arrow-up"
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>

    </div>
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="190px">

      <el-form-item class="searchItem" :label="$t('order.orderNumber')" prop="materialCode">
        <el-input v-model="queryParams.orderNo" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('common.buyer')" prop="sourcingIds">
        <el-select v-model="queryParams.sourcingIds" class="searchValue" clearable filterable multiple>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('supplier.supplier')" prop="supplier">
        <el-input v-model="queryParams.supplier" class="searchValue" clearable :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.factory')" prop="factoryIds">
        <el-select v-model="queryParams.factoryIds" class="searchValue" clearable multiple>
          <el-option v-for="item in getDictDatas(DICT_TYPE.COMMON_FACTORY, 0)" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('material.materialCode')" prop="materialCode">
        <el-input v-model="queryParams.materialCode" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.manufacturer')" prop="materialMfg">
        <el-input v-model="queryParams.materialMfg" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>

      <el-form-item v-if="activeName==='first'" class="searchItem" :label="$t('order.orderStatus')" prop="orderStatus">
        <el-select v-model="queryParams.orderStatus" class="searchValue" clearable multiple>
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="activeName==='second'" class="searchItem" :label="$t('order.orderLineStatus')" prop="orderLineStatus">
        <el-select v-model="queryParams.orderLineStatus" class="searchValue" clearable>
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_DETAIL_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="activeName==='third'" class="searchItem" :label="$t('order.receiptVoucherNo')" prop="receiptVoucherNum">
        <el-input v-model="queryParams.receiptVoucherNum" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>

      <el-form-item v-if="activeName==='third'" class="searchItem" :label="$t('order.financialStatus')" prop="statusList">
        <el-select v-model="queryParams.statusList" class="searchValue" clearable multiple>
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_GOOD_RECEIVING_STATUS, 0)" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-else class="searchItem" :label="$t('material.materialDescription')" prop="materialDesc">
        <el-input v-model="queryParams.materialDesc" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.manufacturersPartNumber')" prop="materialMpn">
        <el-input v-model="queryParams.materialMpn" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>

      <el-form-item v-if="activeName==='third'" class="searchItem" :label="$t('order.company')" prop="companyIds">
        <el-select v-model="queryParams.companyIds" class="searchValue" clearable multiple>
          <el-option v-for="item in getDictDatas(DICT_TYPE.COMMON_COMPANY, 0)" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('order.timeType')" prop="dateType">
        <el-select v-model="queryParams.dateType" class="searchValue" clearable>
          <el-option v-for="item in dateTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label=" " class="searchItem">
        <el-date-picker
          v-model="queryParams.time"
          class="searchValue"
          value-format="yyyy-MM-dd"
          type="daterange"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        />
      </el-form-item>
      <el-form-item v-if="activeName==='second'" class="searchItem" :label="$t('order.shipmentStatus')" prop="deliveryStatus">
        <el-select v-model="queryParams.deliveryStatus" class="searchValue" clearable>
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_DETAIL_DELIVERY_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="activeName==='second'" class="searchItem" :label="$t('order.receivingStatus')" prop="receiptStatus">
        <el-select v-model="queryParams.receiptStatus" class="searchValue" clearable>
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_DETAIL_RECEIPT_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="activeName==='second'" class="searchItem" :label="$t('order.overdue')">
        <el-select
          v-model="queryParams.overdue"
          class="searchValue"
          multiple
          clearable
        >
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_OVERDUE_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('order.frameworkContractNumber')" prop="frameworkContractNumber">
        <el-input v-model="queryParams.frameworkContractNumber" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
      <!--      <div style="text-align: center">-->
      <!--        <el-button-->
      <!--          v-has-permi="['order:header:query']"-->
      <!--          icon="el-icon-search"-->
      <!--          size="mini"-->
      <!--          type="primary"-->
      <!--          plain-->
      <!--          @click="handleSearch(true)"-->
      <!--        >{{-->
      <!--          $t('common.search')-->
      <!--        }}-->
      <!--        </el-button>-->
      <!--        <el-button-->
      <!--          v-has-permi="['order:header:query']"-->
      <!--          icon="el-icon-refresh"-->
      <!--          size="mini"-->
      <!--          @click="handleClick"-->
      <!--        >{{ $t('common.reset') }}</el-button>-->
      <!--      </div>-->
    </el-form>
    <!--    全部订单tab-->
    <vxe-grid
      v-if="activeName === 'first'"
      ref="allOrderTable"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
      @sort-change="sortMethod"
    >
      <template #orderNo="{row}">
        <copy-button type="text" @click="$router.push(`/order/orderDetail/${row.id}?id=${row.orderNo}`)">
          {{ row.orderNo }}
        </copy-button>
      </template>
      <template #factoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_FACTORY" :value="row.factoryId" />
      </template>
      <template #currency="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.currency" />
      </template>
      <template #sourcingId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcingId" />
      </template>
      <template #buyerCompanyId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="row.buyerCompanyId" />
      </template>
      <template #orderType="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_TYPE" :value="row.orderType" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_STATUS" :value="row.status" />
      </template>
      <template #version="{row}">
        <el-button
          v-has-permi="['order:header:query']"
          type="text"
          @click="showHis(row.id)"
        >
          {{ row.version }}
        </el-button>
      </template>
      <!--      此处红色为真实数值为红色-->
      <template #totalAmountAfterTax="{row}">
        <number-format v-if="lessThanZero(row.totalAmountAfterTax)" :value="row.totalAmountAfterTax" :font-color="'red'" />
        <number-format v-else :value="row.totalAmountAfterTax" />
      </template>
      <template #operate="{row}">
        <OperateDropDown
          :menu-item="[
            {
              name: $t('order.pdf'),

              show: $store.getters.permissions.includes('order:header:query'),
              action: (row) => downloadPdf(row),
              para: row
            },

          ]"
        />
        <!--        <el-button-->
        <!--          v-has-permi="['order:header:query']"-->
        <!--          size="mini"-->
        <!--          type="text"-->
        <!--          @click="downloadPdf(row)"-->
        <!--        >-->
        <!--          {{ $t('order.pdf') }}-->
        <!--        </el-button>-->

      </template>

      <template #toolbar_buttons>
        <el-row :gutter="10" style="width: 100%" class="mb8">
          <el-col :span="10" style="display: flex">
            <el-button
              v-hasPermi="['order:header:import']"
              type="primary"
              size="mini"
              icon="el-icon-upload2"
              @click="handleImport('order')"
            >  {{ $t('order.importOrder') }}</el-button>
            <el-button
              v-has-permi="['order:header:export']"
              size="mini"
              plain
              type="primary"
              @click="downLoadExcel"
            >
              {{ $t('order.download') }}
            </el-button>

          </el-col>
          <el-col :span="14">
            <right-toolbar
              v-if="activeName === 'first'"
              :key="girdOption.id"
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              :custom-columns.sync="girdOption.columns"
              @queryTable="init"
            />
          </el-col>
        </el-row>
      </template>
    </vxe-grid>
    <!--    全部订单行-->
    <vxe-grid
      v-if="activeName === 'second'"
      :data="list"
      :loading="loading"
      v-bind="girdOptionDetail"
      @sort-change="sortMethod"
    >
      <template #orderNo="{row}">
        <copy-button type="text" @click="$router.push(`/order/orderDetail/${row.orderId}?id=${row.orderNo}`)">
          {{ row.orderNo }}
        </copy-button>
      </template>
      <template #factoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_FACTORY" :value="row.factoryId" />
      </template>
      <template #sourcingId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcingId" />
      </template>
      <template #overdue="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_OVERDUE_TYPE" :value="row.overdue" />
      </template>
      <template #taxRate="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_RATE" :value="row.taxRate" />
      </template>
      <template #currency="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.currency" />
      </template>
      <template #purchasingUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.purchasingUnit" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_DETAIL_STATUS" :value="row.status" />

      </template>
      <template #categoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />

      </template>
      <template #orderType="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_TYPE" :value="row.orderType" />

      </template>
      <template #deliveryStatus="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_DETAIL_DELIVERY_STATUS" :value="row.deliveryStatus" />
      </template>
      <template #receiptStatus="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_DETAIL_RECEIPT_STATUS" :value="row.receiptStatus" />
      </template>
      <template #unitPriceAfterTaxNoPriceUnit="{row}">
        <number-format v-if="row.returnProject" :value="-row.unitPriceAfterTaxNoPriceUnit" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.unitPriceAfterTaxNoPriceUnit)" :value="row.unitPriceAfterTaxNoPriceUnit" :font-color="'red'" />
        <number-format v-else :value="row.unitPriceAfterTaxNoPriceUnit" />
      </template>
      <template #returnWareHouseQuantity="{row}">
        <number-format v-if="lessThanZero(row.returnWareHouseQuantity)" :value="row.returnWareHouseQuantity" :font-color="'red'" />
        <number-format v-else :value="row.returnWareHouseQuantity" />
      </template>
      <template #returnQuantity="{row}">
        <number-format v-if="lessThanZero(row.returnQuantity)" :value="row.returnQuantity" :font-color="'red'" />
        <number-format v-else :value="row.returnQuantity" />
      </template>
      <template #unitPriceBeforeTaxNoPriceUnit="{row}">
        <number-format v-if="row.returnProject" :value="-row.unitPriceBeforeTaxNoPriceUnit" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.unitPriceBeforeTaxNoPriceUnit)" :value="row.unitPriceBeforeTaxNoPriceUnit" :font-color="'red'" />
        <number-format v-else :value="row.unitPriceBeforeTaxNoPriceUnit" />
      </template>
      <template #quantity="{row}">
        <number-format v-if="row.returnProject" :value="-row.quantity" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.quantity)" :value="row.quantity" :font-color="'red'" />
        <number-format v-else :value="row.quantity" />
      </template>
      <template #unitPriceAfterTax="{row}">
        <number-format v-if="row.returnProject" :value="-row.unitPriceAfterTax" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.unitPriceAfterTax)" :value="row.unitPriceAfterTax" :font-color="'red'" />
        <number-format v-else :value="row.unitPriceAfterTax" />
      </template>
      <template #unitPriceBeforeTax="{row}">
        <number-format v-if="row.returnProject" :value="-row.unitPriceBeforeTax" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.unitPriceBeforeTax)" :value="row.unitPriceBeforeTax" :font-color="'red'" />
        <number-format v-else :value="row.unitPriceBeforeTax" />
      </template>>
      <template #availableDeliveryQuantity="{row}">
        <number-format v-if="row.returnProject" :value="-row.availableDeliveryQuantity" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.availableDeliveryQuantity)" :value="row.availableDeliveryQuantity" :font-color="'red'" />
        <number-format v-else :value="row.availableDeliveryQuantity" />
      </template>>
      <template #amountBeforeTax="{row}">
        <number-format v-if="row.returnProject" :value="-row.amountBeforeTax" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.amountBeforeTax)" :value="row.amountBeforeTax" :font-color="'red'" />
        <number-format v-else :value="row.amountBeforeTax" />
      </template>>
      <template #amountAfterTax="{row}">
        <number-format v-if="row.returnProject" :value="-row.amountAfterTax" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.amountAfterTax)" :value="row.amountAfterTax" :font-color="'red'" />
        <number-format v-else :value="row.amountAfterTax" />
      </template>>
      <template #operate="{row}">
        <OperateDropDown
          :menu-item="[
            {
              name: $t('common.operationRecord'),

              show: $store.getters.permissions.includes('order:header:query'),
              action: (row) => handleLog(row),
              para: row.id
            },

          ]"
        />

        <!--        <el-button-->
        <!--          v-has-permi="['order:header:query']"-->
        <!--          type="text"-->
        <!--          @click="handleLog(row.id)"-->
        <!--        >-->
        <!--          {{ $t('common.operationRecord') }}-->
        <!--        </el-button>-->
      </template>

      <template #toolbar_buttons>
        <el-row :gutter="10" style="width: 100%" class="mb8">
          <el-col :span="10" style="display: flex">
            <span />
            <el-button
              v-has-permi="['order:header:query']"
              size="mini"
              plain
              type="primary"
              @click="downLoadExcel"
            >
              {{ $t('order.download') }}
            </el-button>
          </el-col>
          <right-toolbar
            v-if="activeName === 'second'"
            :key="girdOptionDetail.id"
            :list-id="girdOptionDetail.id"
            :show-search.sync="showSearch"
            :custom-columns.sync="girdOptionDetail.columns"
            @queryTable="getOrderDetail"
          />
        </el-row>
      </template>
    </vxe-grid>
    <!--    收货明细列表-->
    <vxe-grid
      v-if="activeName === 'third'"
      :data="list"
      :loading="loading"
      v-bind="girdOptionReward"
      @sort-change="sortMethod"
    >
      <template #factoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_FACTORY" :value="row.factoryId" />
      </template>
      <template #sourcingId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcingId" />
      </template>
      <template #currency="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.currency" />
      </template>
      <template #purchasingUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.purchasingUnit" />
      </template>
      <template #companyId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="row.companyId" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_GOOD_RECEIVING_STATUS" :value="row.status" />
      </template>
      <template #receiptType="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_RECEIPT_TYPE" :value="row.receiptType" />
      </template>
      <template #categoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
      </template>
      <template #receiptQuantity="{row}">
        <span v-if="row.receiptType ==='reject'">
          <number-format :value="lessThanZeroHandler(row.receiptQuantity)" :font-color="'red'" />
        </span>
        <span v-else>
          <number-format v-if="lessThanZero(row.receiptQuantity)" :value="row.receiptQuantity" :font-color="'red'" />
          <number-format v-else :value="row.receiptQuantity" />
        </span>
      </template>
      <template #quantity="{row}">
        <number-format :value="row.quantity" />
      </template>
      <template #priceBeforeTax="{row}">
        <span v-if="row.receiptType ==='reject'">
          <number-format :value="lessThanZeroHandler(row.priceBeforeTax)" :font-color="'red'" />
        </span>
        <span v-else>
          <number-format v-if="lessThanZero(row.priceBeforeTax)" :value="row.priceBeforeTax" :font-color="'red'" />
          <number-format v-else :value="row.priceBeforeTax" />
        </span>
      </template>
      <template #priceAfterTax="{row}">
        <span v-if="row.receiptType ==='reject'">
          <number-format :value="lessThanZeroHandler(row.priceAfterTax)" :font-color="'red'" />
        </span>
        <span v-else>
          <number-format v-if="lessThanZero(row.priceAfterTax)" :value="row.priceAfterTax" :font-color="'red'" />
          <number-format v-else :value="row.priceAfterTax" />
        </span>
      </template>
      <template #unitPriceAfterTaxNoPriceUnit="{row}">
        <number-format :value="row.unitPriceAfterTaxNoPriceUnit" />
      </template>
      <template #unitPriceBeforeTaxNoPriceUnit="{row}">
        <number-format :value="row.unitPriceBeforeTaxNoPriceUnit" />
      </template>
      <template #deliveryStatus="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_OVERDUE_TYPE" :value="row.deliveryStatus" />
      </template>
      <template #promiseStatus="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_OVERDUE_TYPE" :value="row.promiseStatus" />
      </template>
      <template #toolbar_buttons>
        <el-row :gutter="10" style="width: 100%" class="mb8">
          <el-col :span="10" style="display: flex">
            <el-button
              v-hasPermi="['order:header:import']"
              type="primary"
              size="mini"
              icon="el-icon-upload2"
              @click="handleImport('receiving')"
            >  {{ $t('order.receiptImport') }}</el-button>
            <el-button
              v-has-permi="['order:good-receiving:export']"
              size="mini"
              type="primary"
              plain
              @click="downLoadExcel"
            >
              {{ $t('order.download') }}
            </el-button>
          </el-col>
          <el-col :span="14">
            <right-toolbar
              v-if="activeName === 'third'"
              :key="girdOptionReward.id"
              :list-id="girdOptionReward.id"
              :show-search.sync="showSearch"
              :custom-columns.sync="girdOptionReward.columns"
              @queryTable="getReward"
            />
          </el-col>
        </el-row>
      </template>
      <template #taxRate="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_RATE" :value="row.taxRate" />
      </template>
    </vxe-grid>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="handleSearch(false)"
    />
    <!-- 订单导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        <div slot="tip" class="el-upload__tip text-center">
          <span>{{ $t('common.onlyXlsXlsxFormatFilesAreAllowedToBeImported') }}</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-has-permi="['order:header:query']"
          @click="upload.open = false"
        >{{ $t('common.cancel') }}</el-button>
        <el-button
          v-hasPermi="['order:good-receiving:import']"
          type="primary"
          plain
          @click="importTemplate"
        >  {{ $t('common.downloadTemplate') }}</el-button>
        <el-button
          v-has-permi="['order:header:query']"
          type="primary"
          @click="submitFileForm"
        >  {{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>
    <!--查看订单的历史版本组件-->
    <historyversion ref="historyversion" />
    <!--    状态字段的操作记录-->
    <el-dialog
      v-if="log.open"
      :close-on-click-modal="false"
      :title="$t('common.operationRecord')"
      :visible.sync="log.open"
      width="1000px"
    >
      <operation-record
        :business-id="log.businessId"
        :columns="log.columns"
        :log-visible.sync="log.open"
      />
    </el-dialog>
  </div>
</template>
<script>
import {
  exportDetailExcel,
  exportHeaderExcel,
  exportReceivingExcel,
  getLatestPdfWithOrderId,
  getOrderDetailList,
  getOrderList,
  getRewardList,
  importGoodReceivingTemplate,
  importTemplate,
  statisticsCard
} from '@/api/order/orderCoordination'
import { getBaseHeader } from '@/utils/request'
import operationRecord from '@/components/OperationRecord/operationRecord'
import historyversion from '@/views/order/orderCoordination/historyversion'
import event from '@/views/dashboard/mixins/event'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import dayjs from 'dayjs'

export default {
  name: 'Orderindex',
  components: {
    OperateDropDown,
    operationRecord, historyversion
  },
  mixins: [event],

  data() {
    return {
      // 数字牌数据源
      cardData: [],
      // 顶部搜索框的时间类型数据源
      // 特殊处理 activeName===third(收货明细）,需要增加一个收货日期
      dateTypeList: getDictDatas(DICT_TYPE.ORDER_DATE_TYPE_IN_ORDER),
      // 状态字段的操作记录
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: [
          {
            tableName: 'order_detail',
            keyColumns: ['status']
          }
        ]
      },
      showSearch: false,
      queryParams: {
        search: '',
        materialCode: '',
        beginOrderDate: undefined,
        endOrderDate: undefined,
        dateType: '',
        deliveryStatus: '',
        factoryIds: [],
        companyIds: [],
        statusList: [],
        materialDesc: '',
        materialMfg: '',
        materialMpn: '',
        orderLineStatus: '',
        orderNo: '',
        orderStatus: [],
        receiptStatus: '',
        sortBy: '',
        sortField: '',
        sourcingIds: [],
        supplier: '',
        overdue: [],
        frameworkContractNumber: '',
        time: '',
        receiptVoucherNum: '',
        pageNo: 1,
        pageSize: 10
      },
      categoryList: [],
      factoryList: [],
      list: [],
      loading: false,
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'order',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { type: 'checkbox', width: 30, visible: true },
          { title: this.$t('order.orderNumber'), slots: { default: 'orderNo' }, field: 'orderNo', visible: true, width: 200 },
          { title: this.$t('order.orderDate'), sortable: true, field: 'orderDate', visible: true },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
          { title: this.$t('supplier.shortNameOfSupplier'), field: 'supplierNameShort', visible: true },
          { title: this.$t('order.buyerCompany'), slots: { default: 'buyerCompanyId' }, field: 'buyerCompanyId', visible: true },
          { title: this.$t('common.buyer'), slots: { default: 'sourcingId' }, field: 'sourcingId', visible: true },
          { title: this.$t('order.orderStatus'), slots: { default: 'status' }, field: 'status', visible: true },
          { title: this.$t('order.orderType'), slots: { default: 'orderType' }, field: 'orderType', visible: true },
          { title: this.$t('order.amountIncludingTax'), slots: { default: 'totalAmountAfterTax' }, field: 'totalAmountAfterTax', visible: true, width: 100, align: 'right' },
          { title: this.$t('system.currency'), slots: { default: 'currency' }, field: 'currency', visible: true },
          { title: this.$t('order.requiredArrivalDate'), field: 'requestDeliveryDate', visible: true },
          { title: this.$t('order.latestRequiredArrivalDate'), sortable: true, field: 'latestRequestDeliveryDate', visible: true },
          { title: this.$t('order.edition'), slots: { default: 'version' }, field: 'version', visible: true },
          { title: this.$t('order.numberOfOrderLines'), field: 'orderDetailNum', visible: true },
          { title: this.$t('order.supplier'), field: 'supplyingParty', visible: true },
          { title: this.$t('order.frameworkContractNumber'), field: 'frameworkContractNumber', visible: true, width: 100 },
          { title: this.$t('order.contractName'), field: 'contractNumber', visible: true, width: 100 },
          { title: this.$t('common.operate'), field: 'operate', slots: { default: 'operate' }, showOverflow: false, fixed: 'right', visible: true, width: 35 }

        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      girdOptionDetail: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'orderLine',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { title: this.$t('order.orderNumber'), slots: { default: 'orderNo' }, field: 'orderNo', visible: true, width: 100 },
          { title: this.$t('order.orderLineNo'), field: 'lineNo', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('order.describe'), field: 'materialDescription', visible: true, width: 100 },
          { title: this.$t('material.materialCode'), field: 'materialCode', visible: true, width: 100 },
          { title: this.$t('material.specificationAndModel'), field: 'specification', visible: true, width: 100 },
          { title: this.$t('supplier.shortNameOfSupplier'), field: 'supplierNameShort', visible: true, width: 100 },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
          { title: this.$t('material.factory'), field: 'factoryId', slots: { default: 'factoryId' }, visible: true, width: 100 },
          { title: this.$t('order.orderDate'), sortable: true, field: 'orderDate', visible: true, width: 100 },
          { title: this.$t('common.buyer'), slots: { default: 'sourcingId' }, field: 'sourcingId', visible: true, width: 100 },
          { title: this.$t('order.orderLineStatus'), field: 'status', slots: { default: 'status' }, visible: true, width: 100 },
          { title: this.$t('order.orderType'), slots: { default: 'orderType' }, field: 'orderType', visible: true, width: 100 },
          { title: this.$t('common.loginCategory'), slots: { default: 'categoryId' }, field: 'categoryId', visible: true, width: 100 },
          { title: this.$t('material.purchasingUnit'), slots: { default: 'purchasingUnit' }, field: 'purchasingUnit', visible: true, width: 100 },
          { title: this.$t('order.unitPriceIncludingTax'), slots: { default: 'unitPriceAfterTaxNoPriceUnit' }, field: 'unitPriceAfterTaxNoPriceUnit', visible: true, width: 100, align: 'right' },
          { title: this.$t('order.unitPriceWithoutTax'), slots: { default: 'unitPriceBeforeTaxNoPriceUnit' }, field: 'unitPriceBeforeTaxNoPriceUnit', visible: true, width: 100, align: 'right' },
          { title: this.$t('system.currency'), slots: { default: 'currency' }, field: 'currency', visible: true, width: 100 },
          { title: this.$t('supplier.taxRate'), slots: { default: 'taxRate' }, field: 'taxRate', visible: true, width: 100 },
          { title: this.$t('order.amountNotTaxed'), slots: { default: 'amountBeforeTax' }, field: 'amountBeforeTax', visible: true, width: 100, align: 'right' },
          { title: this.$t('order.amountIncludingTax'), slots: { default: 'amountAfterTax' }, field: 'amountAfterTax', visible: true, width: 100, align: 'right' },
          { title: this.$t('order.latestRequiredArrivalDate'), field: 'latestRequestDeliveryDate', visible: true, sortable: true, width: 100 },
          { title: this.$t('order.orderQuantity'), slots: { default: 'quantity' }, field: 'quantity', visible: true, width: 100, align: 'right' },
          { title: this.$t('material.priceUnit'), field: 'priceUnit', visible: true, width: 100 },
          { title: this.$t('order.remainingOrdersCanBeDelivered'), slots: { default: 'availableDeliveryQuantity' }, field: 'availableDeliveryQuantity', visible: true, sortable: true, width: 100, align: 'right' },
          { title: this.$t('order.requiredArrivalDate'), field: 'requestDeliveryDate', visible: true, width: 100 },
          { title: this.$t('order.quantityInDelivery'), field: 'deliveringQuantity', visible: true, width: 100, align: 'right' },
          { title: this.$t('order.arrivalQuantity'), field: 'receiptQuantity', visible: true, width: 100, align: 'right' },
          { title: this.$t('order.returnQuantity'), slots: { default: 'returnQuantity' }, field: 'returnGoodQuantity', visible: true, width: 100, align: 'right' },
          { title: this.$t('order.receiptQuantity'), field: 'inventoryQuantity', visible: true, width: 100, align: 'right' },
          { title: this.$t('order.returnWareHouseQuantity'), slots: { default: 'returnWareHouseQuantity' }, field: 'returnWareHouseQuantity', visible: true, width: 100, align: 'right' },
          { title: this.$t('order.overdue'), slots: { default: 'overdue' }, field: 'overdue', visible: true, width: 100 },
          { title: this.$t('order.orderLineComments'), field: 'remarks', visible: true, width: 100 },
          { title: this.$t('order.shipmentStatus'), slots: { default: 'deliveryStatus' }, field: 'deliveryStatus', visible: true, width: 100 },
          { title: this.$t('order.receivingStatus'), slots: { default: 'receiptStatus' }, field: 'receiptStatus', visible: true, width: 100 },
          { title: this.$t('order.freeService'), field: 'freeProject', visible: true, width: 100 },
          { title: this.$t('order.returnedItems'), field: 'returnProject', visible: true, width: 100 },
          { title: this.$t('order.arrivalDate'), sortable: true, field: 'receiptDate', visible: true, width: 100 },
          { title: this.$t('material.brand'), field: 'brand', visible: true, width: 100 },
          { title: this.$t('material.revision'), field: 'materialVersion', visible: true, width: 100 },
          { title: this.$t('order.orderReviewNo'), field: 'orderReviewNumber', visible: true, width: 100 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          { title: this.$t('order.unitPriceIncludingTaxincludingPriceUnit'), slots: { default: 'unitPriceAfterTax' }, field: 'unitPriceAfterTax', visible: true, width: 120, align: 'right' },
          { title: this.$t('order.unitPriceExcludingTaxincludingPriceUnit'), slots: { default: 'unitPriceBeforeTax' }, field: 'unitPriceBeforeTax', visible: true, width: 120, align: 'right' },
          { title: this.$t('common.operate'), field: 'operate', slots: { default: 'operate' }, showOverflow: false, fixed: 'right', visible: true, width: 35 }
        ],
        sortConfig: {
          remote: false
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      girdOptionReward: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'orderReward',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { title: this.$t('order.orderNumber'), field: 'orderNo', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('order.orderLineNo'), field: 'lineNo', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('order.receiptVoucherNo'), field: 'receiptVoucherNum', sortable: true, visible: true, width: 112, fixed: 'left' },
          { title: this.$t('order.receiptLn'), sortable: true, field: 'receiptItem', visible: true, width: 100 },
          { title: this.$t('common.buyer'), slots: { default: 'sourcingId' }, field: 'sourcingId', visible: true, width: 100 },
          { title: this.$t('order.moveType'), slots: { default: 'receiptType' }, field: 'receiptType', visible: true, width: 100 },
          { title: this.$t('order.company'), slots: { default: 'companyId' }, field: 'companyId', visible: true, width: 100 },
          { title: this.$t('material.specificationAndModel'), field: 'specification', visible: true, width: 100 },
          { title: this.$t('order.financialStatus'), slots: { default: 'status' }, field: 'status', visible: true, width: 100 },
          { title: this.$t('order.receiptRelCertificate'), field: 'receiptRelCertificate', visible: true, width: 100 },
          { title: this.$t('order.quantityReceived'), field: 'receiptQuantity', slots: { default: 'receiptQuantity' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.amountNotTaxed'), field: 'priceBeforeTax', slots: { default: 'priceBeforeTax' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.amountIncludingTax'), field: 'priceAfterTax', slots: { default: 'priceAfterTax' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('material.factory'), slots: { default: 'factoryId' }, field: 'factoryId', visible: true, width: 100 },
          { title: this.$t('order.dateOfVoucher'), sortable: true, field: 'receiptDate', visible: true, width: 100 },
          { title: this.$t('material.materialCode'), field: 'materialCode', visible: true, width: 100 },
          { title: this.$t('material.materialDescription'), field: 'materialDescription', visible: true, width: 100 },
          { title: this.$t('common.loginCategory'), slots: { default: 'categoryId' }, field: 'categoryId', visible: true, width: 100 },
          { title: this.$t('material.purchasingUnit'), slots: { default: 'purchasingUnit' }, field: 'purchasingUnit', visible: true, width: 100 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          { title: this.$t('material.revision'), field: 'materialVersion', visible: true, width: 100 },
          { title: this.$t('supplier.taxRate'), slots: { default: 'taxRate' }, field: 'taxRate', visible: true, width: 100 },
          { title: this.$t('order.unitPriceIncludingTax'), field: 'unitPriceAfterTaxNoPriceUnit', slots: { default: 'unitPriceAfterTaxNoPriceUnit' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.unitPriceWithoutTax'), field: 'unitPriceBeforeTaxNoPriceUnit', slots: { default: 'unitPriceBeforeTaxNoPriceUnit' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('system.currency'), slots: { default: 'currency' }, field: 'currency', visible: true, width: 100 },
          { title: this.$t('order.orderQuantity'), field: 'quantity', slots: { default: 'quantity' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('order.receivingOrganization'), field: 'organization', visible: true, width: 100 },
          { title: this.$t('supplier.shortNameOfSupplier'), field: 'supplierNameShort', visible: true, width: 100 },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
          { title: this.$t('order.freeService'), field: 'freeProject', visible: true, width: 100 },
          { title: this.$t('order.returnedItems'), field: 'returnProject', visible: true, width: 100 },
          { title: this.$t('material.brand'), field: 'brand', visible: true, width: 100 },
          { title: this.$t('order.deliveredOnTimeAsRequired'), field: 'deliveryStatus', slots: { default: 'deliveryStatus' }, visible: true, width: 100 },
          { title: this.$t('order.deliverOnTimeAsPromised'), field: 'promiseStatus', slots: { default: 'promiseStatus' }, visible: true, width: 100 },
          { title: this.$t('rfq.exchangeRate'), field: 'exchangeRate', visible: true, width: 100 }
        ],
        sortConfig: {
          remote: false
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      total: 0,
      activeName: 'first',
      // 订单导入参数
      upload: {
        // 是否显示弹出层（订单导入）
        open: false,
        // 弹出层标题（订单导入）
        title: '',
        type: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/order/header/import'
      }
    }
  },
  mounted() {
    switch (this.activeName) {
      case 'first':
        this.init()
        break
      case 'second':
        this.getOrderDetail()
        break
      case 'third':
        this.getReward()
        break
    }
    this.getStatisticsCard()
  },

  methods: {
    // 订单首页的数字牌数据源
    getStatisticsCard() {
      statisticsCard().then(res => {
        this.cardData = [
          { label: this.$t('order.ytdTotalOrderQuantity'), value: res.data.ytdCountOfAllOrder },
          { label: this.$t('order.ytdTotalNumberOfOrderLines'), value: res.data.utdCountOfAllOrderDetail },
          { label: this.$t('order.ytdTradingSupplier'), value: res.data.ytdTradedSupplier },
          { label: this.$t('order.ytdConfirmsSupplierWithDeliveryTime'), value: res.data.ytdCountOfUsedDeliveryConfirmSupplier },
          { label: this.$t('order.ytdUsesDeliveryNoteSuppliers'), value: res.data.ytdCountOfUsedDeliverySupplier },
          { label: this.$t('order.ytdUsagePlanCollaboratesWithSuppliers'), value: res.data.ytdCountOfUsedPlanSupplier }
        ]
      }).catch(_ => {
      })
    },
    // 下载最新版本的pdf
    downloadPdf(row) {
      this.$modal.confirm(this.$t('order.areYouSureToDownloadTheOrderPdf')).then(() => {
        getLatestPdfWithOrderId(row.id).then(res => {
          if (res.data) {
            window.open(res.data)
          } else {
            this.$message.warning(this.$t('order.theOrderWasNotSuccessfully'))
          }
        })
      })
    },
    // 订单历史版本
    showHis(orderId) {
      this.$refs.historyversion.historyVisible = true
      this.$refs.historyversion.hisParams.orderId = orderId
      this.$refs.historyversion.getHisList()
    },
    // 操作记录#订单行状态字段的流转
    handleLog(orderDetailId) {
      this.log.open = true
      this.log.businessId = orderDetailId
    },
    /** 导入按钮操作 */
    handleImport(type) {
      // 订单导入和收货导入共用
      this.upload.title = type === 'order' ? this.$t('order.orderImport') : this.$t('order.receiptImport')
      this.upload.open = true
      this.upload.type = type
      this.upload.url = type === 'order' ? process.env.VUE_APP_BASE_API + '/admin-api/order/header/import' : process.env.VUE_APP_BASE_API + '/admin-api/order/good-receiving/import'
    },
    /** 下载模板操作 */
    importTemplate() {
      if (this.upload.type === 'order') {
        importTemplate().then(response => {
          this.$download.excel(response, this.$t('order.orderImportTemplateXlsx'))
        })
      } else {
        importGoodReceivingTemplate().then(response => {
          this.$download.excel(response, this.$t('order.orderReceiptImportTemplateXlsx'))
        })
      }
    },
    // 文件上传中处理
    handleFileUploadProgress() {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createOrders) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createOrders.length
      }
      if (data.updateOrders) {
        text += this.$t('sp.numberOfSuccessfulUpdates') + data.updateOrders.length
      }
      if (data.failureOrders) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureOrders).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" target="_blank" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }

      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      if (this.upload.type === 'order') {
        this.init()
      } else {
        this.getReward()
      }
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    init() {
      this.dateTypeList = this.dateTypeList.filter(i => i.value !== 'receiptDate')
      this.loading = true
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.queryParams.time, 'orderDate')
      getOrderList(params).then(res => {
        this.loading = false
        this.orderDetail = res.data
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    // 全部订单号tab列表数据请求
    getOrderDetail() {
      this.dateTypeList = this.dateTypeList.filter(i => i.value !== 'receiptDate')
      this.loading = true
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.queryParams.time, 'orderDate')
      getOrderDetailList(params).then(res => {
        this.loading = false
        this.orderDetail = res.data
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    getReward() {
      this.dateTypeList = getDictDatas(DICT_TYPE.ORDER_DATE_TYPE_IN_ORDER)
      this.loading = true
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.queryParams.time, 'orderDate')
      getRewardList(params).then(res => {
        this.loading = false
        this.orderDetail = res.data
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.handleSearch(false)
    },
    handleClick() {
      this.queryParams = {
        search: '',
        materialCode: '',
        beginOrderDate: undefined,
        endOrderDate: undefined,
        dateType: '',
        deliveryStatus: '',
        factoryIds: [],
        companyIds: [],
        statusList: [],
        overdue: [],
        materialDesc: '',
        materialMfg: '',
        materialMpn: '',
        orderLineStatus: '',
        orderNo: '',
        orderStatus: [],
        receiptStatus: '',
        sortBy: '',
        sortField: '',
        sourcingIds: [],
        supplier: '',
        time: '',
        pageNo: 1,
        pageSize: 10
      }
      this.handleSearch(true)
    },
    downLoadExcel() {
      // 处理时间参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.queryParams.time, 'orderDate')

      const formattedDate = dayjs().format('YYYYMMDD')
      switch (this.activeName) {
        case 'first':
          exportHeaderExcel(params).then(res => {
            this.$download.excel(res, this.$t('order.allOrders') + formattedDate + '.xlsx')
          })
          break
        case 'second':
          exportDetailExcel(params).then(res => {
            this.$download.excel(res, this.$t('order.allOrderLines') + formattedDate + '.xlsx')
          })
          break
        case 'third':
          exportReceivingExcel(params).then(res => {
            this.$download.excel(res, this.$t('order.receiptDetails') + formattedDate + '.xlsx')
          })
          break
      }
    },
    handleSearch(resetPageNo) {
      if (resetPageNo) {
        // 跨页后使用前面页数据进行搜索，此时未重置pageNo导致搜索结果为空
        this.queryParams.pageNo = 1
      }
      switch (this.activeName) {
        // 订单
        case 'first':
          this.init()
          break
        // 订单行
        case 'second':
          this.getOrderDetail()
          break
        // 收货明细
        case 'third':
          this.getReward()
          break
      }
    },
    // 判断num是否小于0
    lessThanZero(num) {
      if (num) {
        return num < 0
      }
      return false
    },
    lessThanZeroHandler(num) {
      if (num && num > 0) {
        return -num
      }
      return num
    }
  }
}
</script>

<style lang="scss" scoped>
.searchItem{
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 190px);
  }
}
.searchTimeItem{
  margin-right: 0;
  ::v-deep .el-form-item__content{
    width: calc(100% - 82px);
  }
}
.searchValue{
  width: 95%;
}
.red{
  color: red;
}
</style>
