
<template>
  <div class="app-container">
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input v-model="queryParams.search	" style="flex: 0 1 40%" :placeholder="$t('order.orderNoMaterialCodeSupplierName')" clearable @keyup.enter.native="handleSearch(true)" />
      <el-button
        v-has-permi="['order:delivery-confirm:queryOfSupplier']"
        type="primary"
        plain
        @click="handleSearch(true)"
      >{{ $t('common.search') }}</el-button>
      <el-button
        v-has-permi="['order:delivery-confirm:queryOfSupplier']"
        style="margin-left: 0"
        plain
        @click="handleClick"
      >{{ $t('common.reset') }}</el-button>
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button
          v-has-permi="['order:delivery-confirm:queryOfSupplier']"
          type="text"
        >
          {{ $t('common.advancedSearch') }}
        </el-button>
        <i
          class="el-icon-arrow-up"
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>

    </div>
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="189px">

      <el-form-item class="searchItem" :label="$t('order.orderNumber')" prop="materialCode">
        <el-input v-model="queryParams.orderNo" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('common.buyer')" prop="sourcingIds">
        <el-select v-model="queryParams.sourcingIds" class="searchValue" clearable filterable multiple>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('supplier.supplier')" prop="supplier">
        <el-input v-model="queryParams.supplier" class="searchValue" clearable :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.factory')" prop="companyIds">
        <el-select v-model="queryParams.factoryIds" class="searchValue" clearable multiple>
          <el-option v-for="item in getDictDatas(DICT_TYPE.COMMON_FACTORY, 0)" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('material.materialCode')" prop="materialCode">
        <el-input v-model="queryParams.materialCode" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.manufacturer')" prop="materialMfg">
        <el-input v-model="queryParams.materialMfg" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('order.delivery_confirmation_status')" prop="orderStatus">
        <el-select v-model="queryParams.deliveryConfirmStatus" class="searchValue" clearable>
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_DETAIL_CONFIRM_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('material.materialDescription')" prop="materialDesc">
        <el-input v-model="queryParams.materialDesc" class="searchValue" :placeholder="$t('common.pleaseEnter')" />

      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.manufacturersPartNumber')" prop="materialMpn">
        <el-input v-model="queryParams.materialMpn" class="searchValue" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('order.timeType')" prop="dateType">
        <el-select v-model="queryParams.dateType" class="searchValue" clearable>
          <el-option v-for="item in dateTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label=" " class="searchItem">
        <el-date-picker
          v-model="queryParams.time"
          class="searchValue"
          value-format="yyyy-MM-dd"
          type="daterange"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        />
      </el-form-item>
    </el-form>
    <vxe-grid
      ref="deliveryConfirm"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
      @sort-change="sortMethod"
    >
      <template #orderNo="{row}">
        <copy-button type="text" @click="$router.push(`/order/orderDetail/${row.orderId}?id=${row.orderNo}`)">
          {{ row.orderNo }}
        </copy-button>
      </template>
      <template #factoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_FACTORY" :value="row.factoryId" />
      </template>
      <template #sourcingId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcingId" />
      </template>
      <template #overdue="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_OVERDUE_TYPE" :value="row.overdue" />
      </template>
      <template #taxRate="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_RATE" :value="row.taxRate" />
      </template>
      <template #currency="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.currency" />
      </template>
      <template #purchasingUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.purchasingUnit" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_DETAIL_STATUS" :value="row.status" />

      </template>
      <template #deliveryConfirmStatus="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_DETAIL_CONFIRM_STATUS" :value="row.deliveryConfirmStatus" />
      </template>
      <template #categoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />

      </template>
      <template #orderType="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_TYPE" :value="row.orderType" />

      </template>
      <template #deliveryStatus="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_DETAIL_DELIVERY_STATUS" :value="row.deliveryStatus" />
      </template>
      <template #receiptStatus="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_DETAIL_RECEIPT_STATUS" :value="row.receiptStatus" />
      </template>
      <template #unitPriceAfterTaxNoPriceUnit="{row}">
        <number-format v-if="row.returnProject" :value="-row.unitPriceAfterTaxNoPriceUnit" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.unitPriceAfterTaxNoPriceUnit)" :value="row.unitPriceAfterTaxNoPriceUnit" :font-color="'red'" />
        <number-format v-else :value="row.unitPriceAfterTaxNoPriceUnit" />
      </template>
      <template #unitPriceBeforeTaxNoPriceUnit="{row}">
        <number-format v-if="row.returnProject" :value="-row.unitPriceBeforeTaxNoPriceUnit" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.unitPriceBeforeTaxNoPriceUnit)" :value="row.unitPriceBeforeTaxNoPriceUnit" :font-color="'red'" />
        <number-format v-else :value="row.unitPriceBeforeTaxNoPriceUnit" />
      </template>
      <template #quantity="{row}">
        <number-format v-if="row.returnProject" :value="-row.quantity" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.quantity)" :value="row.quantity" :font-color="'red'" />
        <number-format v-else :value="row.quantity" />
      </template>
      <template #unitPriceAfterTax="{row}">
        <number-format v-if="row.returnProject" :value="-row.unitPriceAfterTax" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.unitPriceAfterTax)" :value="row.unitPriceAfterTax" :font-color="'red'" />
        <number-format v-else :value="row.unitPriceAfterTax" />
      </template>
      <template #unitPriceBeforeTax="{row}">
        <number-format v-if="row.returnProject" :value="-row.unitPriceBeforeTax" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.unitPriceBeforeTax)" :value="row.unitPriceBeforeTax" :font-color="'red'" />
        <number-format v-else :value="row.unitPriceBeforeTax" />
      </template>>
      <template #availableDeliveryQuantity="{row}">
        <number-format v-if="row.returnProject" :value="-row.availableDeliveryQuantity" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.availableDeliveryQuantity)" :value="row.availableDeliveryQuantity" :font-color="'red'" />
        <number-format v-else :value="row.availableDeliveryQuantity" />
      </template>>
      <template #amountBeforeTax="{row}">
        <number-format v-if="row.returnProject" :value="-row.amountBeforeTax" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.amountBeforeTax)" :value="row.amountBeforeTax" :font-color="'red'" />
        <number-format v-else :value="row.amountBeforeTax" />
      </template>>
      <template #amountAfterTax="{row}">
        <number-format v-if="row.returnProject" :value="-row.amountAfterTax" :font-color="'red'" />
        <number-format v-else-if="lessThanZero(row.amountAfterTax)" :value="row.amountAfterTax" :font-color="'red'" />
        <number-format v-else :value="row.amountAfterTax" />
      </template>
      <template #deliveryDatePromisedEdit="{row}">
        <vxe-input
          v-model="row.deliveryDatePromised"
          :placeholder="$t('order.selectDate')"
          type="date"
          transfer
          :disabled-method="({date})=>date.getTime() < Date.now()"
          @change="deliveryDatePromisedChange(row)"
        />
      </template>
      <template #operate="{row}">
        <OperateDropDown
          :menu-item="[
            {
              name: $t('common.operationRecord'),
              show: $store.getters.permissions.includes('order:delivery-confirm:queryOfSupplier'),
              action: (row) => handleLog(row),
              para: row.orderLineId
            }
          ]"
        />
      </template>

      <template #toolbar_buttons>
        <el-row :gutter="10" style="width: 100%" class="mb8">
          <el-col :span="10" style="display: flex">
            <el-button
              v-has-permi="['order:detail:batch-submit']"
              size="mini"
              type="primary"
              icon="el-icon-upload2"
              plain
              @click="handleImport"
            >
              {{ $t('order.batchSubmissionLeadTime') }}
            </el-button>
            <el-button
              v-has-permi="['deliveryConfirm:supplierExport']"
              size="mini"
              type="primary"
              icon="el-icon-download"
              plain
              @click="downLoadExcel"
            >
              {{ $t('order.downloadList') }}
            </el-button>

          </el-col>
          <el-col :span="14">
            <right-toolbar
              :key="girdOption.id"
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              :custom-columns.sync="girdOption.columns"
              @queryTable="init"
            />
          </el-col>
        </el-row>
      </template>
    </vxe-grid>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="init(false)"
    />
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          class="small-padding"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="log.open"
      :close-on-click-modal="false"
      :title="$t('common.operationRecord')"
      :visible.sync="log.open"
      width="900px"
    >
      <operation-record
        :business-id="log.businessId"
        :columns="log.columns"
        :log-visible.sync="log.open"
      />
    </el-dialog>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import { DICT_TYPE, getDictDataLabel, getDictDatas } from '@/utils/dict'
import {
  batchModifySubmit, deliveryConfirmSupplier, exportDeliveryConfirmSupplier,
  getImportTemplate
} from '@/api/orderSign'
import { getBaseHeader } from '@/utils/request'
import operationRecord from '@/components/OperationRecord/operationRecord.vue'
import dayjs from 'dayjs'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { debounce } from 'throttle-debounce'

export default defineComponent({
  name: 'Deliveryconfirmationsupplier',
  components: { OperateDropDown, operationRecord },
  data() {
    return {
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null, // 业务id字段
        columns: [
          {
            tableName: 'order_detail',
            searchColumns: ['requestDeliveryDate', 'latestRequestDeliveryDate', 'deliveryDatePromised','deliveryConfirmStatus']
          }
        ]
      },
      showSearch: false,
      dateTypeList: getDictDatas(DICT_TYPE.ORDER_DATE_TYPE_IN_ORDER),
      upload: {
        open: false,
        title: '',
        isUploading: false,
        headers: getBaseHeader(),
        url: process.env.VUE_APP_BASE_API + '/admin-api/order/confirm-sign/batch-submit'
      },
      queryParams: {
        search: '',
        materialCode: '',
        beginOrderDate: undefined,
        endOrderDate: undefined,
        dateType: '',
        deliveryStatus: '',
        factoryIds: [],
        companyIds: [],
        statusList: [],
        materialDesc: '',
        materialMfg: '',
        materialMpn: '',
        orderLineStatus: '',
        deliveryConfirmStatus: '',
        orderNo: '',
        orderStatus: [],
        receiptStatus: '',
        orderSignStatus: '',
        sortBy: '',
        sortField: '',
        sourcingIds: [],
        supplier: '',
        overdue: '',
        time: '',
        receiptVoucherNum: '',
        pageNo: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      loading: false,
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'orderLine',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        editConfig: {
          trigger: 'click',
          mode: 'row',
          showUpdateStatus: true,
          beforeEditMethod: this.beforeEditMethod,
          autoClear: true // 自动清除编辑不能取消，否则不能进入edit-closed方法
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: { isCurrent: true,
          isHover: true
        },
        columns: [
          { type: 'checkbox', width: 30, visible: true, fixed: 'left' },
          { title: this.$t('order.orderNumber'), slots: { default: 'orderNo' }, field: 'orderNo', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('order.orderLineNo'), field: 'lineNo', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('order.overdue'), slots: { default: 'overdue' }, field: 'overdue', visible: true, width: 100 },
          { title: this.$t('material.factory'), field: 'factoryId', slots: { default: 'factoryId' }, visible: true, width: 100 },
          { title: this.$t('order.orderDate'), sortable: true, field: 'orderDate', visible: true, width: 100 },
          { title: this.$t('common.buyer'), slots: { default: 'sourcingId' }, field: 'sourcingId', visible: true, width: 100 },
          { title: this.$t('order.orderLineStatus'), field: 'status', slots: { default: 'status' }, visible: true, width: 100 },
          { title: this.$t('order.orderType'), slots: { default: 'orderType' }, field: 'orderType', visible: true, width: 100 },
          { title: this.$t('material.materialCode'), field: 'materialCode', visible: true, width: 100 },
          { title: this.$t('order.describe'), field: 'materialDescription', visible: true, width: 100 },
          { title: this.$t('common.loginCategory'), slots: { default: 'categoryId' }, field: 'categoryId', visible: true, width: 100 },
          { title: this.$t('material.purchasingUnit'), slots: { default: 'purchasingUnit' }, field: 'purchasingUnit', visible: true, width: 100 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          { title: this.$t('order.orderQuantity'), slots: { default: 'quantity' }, field: 'quantity', visible: true, width: 100, align: 'right' },
          { title: this.$t('order.remainingOrdersCanBeDelivered'), slots: { default: 'availableDeliveryQuantity' }, field: 'availableDeliveryQuantity', visible: true, width: 100, align: 'right' },
          { title: this.$t('order.requiredArrivalDate'), field: 'requestDeliveryDate', visible: true, width: 100 },
          { title: this.$t('order.latestRequiredArrivalDate'), field: 'latestRequestDeliveryDate', visible: true, sortable: true, width: 100 },
          { title: this.$t('order.purchaseAcceptanceArrivalDate'), headerClassName: 'green', field: 'deliveryDateAccept', visible: true, width: 100 },
          { title: this.$t('order.promiseDate'),
            headerClassName: 'orange',
            editRender: {},
            slots: { edit: 'deliveryDatePromisedEdit' },
            field: 'deliveryDatePromised',
            visible: true,
            width: 160 },
          { title: this.$t('order.delivery_confirmation_status'), slots: { default: 'deliveryConfirmStatus' }, field: 'deliveryConfirmStatus', visible: true, width: 100 },
          { title: this.$t('order.orderLineComments'), field: 'remarks', visible: true, width: 100 },
          { title: this.$t('order.orderReviewNo'), field: 'orderReviewNumber', visible: true, width: 100 },
          { title: this.$t('order.freeService'), field: 'freeProject', visible: true, width: 100 },
          { title: this.$t('order.returnedItems'), field: 'returnProject', visible: true, width: 100 },
          { title: this.$t('order.arrivalDate'), field: 'receiptDate', visible: true, width: 100 },
          { title: this.$t('supplier.shortNameOfSupplier'), field: 'supplierNameShort', visible: true, width: 100 },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
          { title: this.$t('material.specificationAndModel'), field: 'specification', visible: true, width: 100 },
          { title: this.$t('material.brand'), field: 'brand', visible: true, width: 100 },
          { title: this.$t('material.revision'), field: 'materialVersion', visible: true, width: 100 },
          { title: this.$t('common.operate'), field: 'operate', showOverflow: false, slots: { default: 'operate' }, fixed: 'right', visible: true, width: 35 }
        ],
        sortConfig: {
          remote: false
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.init()
    },
    init() {
      this.dateTypeList = this.dateTypeList.filter(i => i.value !== 'receiptDate')
      this.loading = true
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.queryParams.time, 'orderDate')
      deliveryConfirmSupplier(params).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    lessThanZero(num) {
      if (num) {
        return num < 0
      }
      return false
    },
    handleSearch() {
      this.queryParams.pageNo = 1
      this.init()
    },
    downLoadExcel() {
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.queryParams.time, 'orderDate')
      exportDeliveryConfirmSupplier(params).then(res => {
        this.$download.excel(res, this.$t('order.supplierDeliveryConfirmationXlsx'))
      })
    },
    beforeEditMethod({ row }) {
      return true
    },
    // 校验答交、计划的日期
    deliveryDatePromisedChange(row) {
      if (!row.deliveryDatePromised) {
        this.$message.error(this.$t('order.thePromisedDeliveryDateCannotBeEmpty'))
        this.init()
        return
      }
      if (dayjs(row.deliveryDatePromised).isBefore(dayjs(), 'day')) {
        this.$message.error(this.$t('order.theDateValueMustBeGreaterThanTheCurrentDay'))
        this.init()
        return
      }
      batchModifySubmit([{
        orderNo: row.orderNo,
        lineNo: row.lineNo,
        deliveryDatePromised: row.deliveryDatePromised
      }]).then(res => {
        this.init()
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    },
    // 校验答交、计划的日期
    validateDate(val) {
      if (val && dayjs(val.value).isBefore(dayjs(), 'day')) {
        this.init()
        this.$message.error(this.$t('order.theDateValueMustBeGreaterThanTheCurrentDay'))
      }
    },
    // 供应商提交交期编辑后触发
    editClosed({ row, rowIndex }) {
      if (row.deliveryDatePromised) {
        batchModifySubmit([{
          orderNo: row.orderNo,
          lineNo: row.lineNo,
          deliveryDatePromised: row.deliveryDatePromised
        }]).then(res => {
          this.init()
          this.$message.success(this.$t('order.operationSucceeded'))
        })
      }
    },
    handleImport() {
      const data = this.$refs.deliveryConfirm.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.upload.title = this.$t('order.batchSubmissionLeadTime')
      this.upload.open = true
    },
    /**
     * 批量提交交期
     * XUERES-1558：非全量下载，仅支持勾选数据进行下载模板。
     */
    importTemplate() {
      const data = this.$refs.deliveryConfirm.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      getImportTemplate(data.map(i => i.orderLineId).join(',')).then(response => {
        this.$download.excel(response, this.$t('order.submitDeliveryTemplateXlsx'))
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createList) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createList.length
      }
      if (data.failureList) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureList).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    submitFileForm() {
      this.$refs.upload.submit()
    },
    handleClick() {
      this.queryParams = {
        search: '',
        materialCode: '',
        beginOrderDate: undefined,
        endOrderDate: undefined,
        dateType: '',
        deliveryStatus: '',
        factoryIds: [],
        companyIds: [],
        statusList: [],
        materialDesc: '',
        materialMfg: '',
        materialMpn: '',
        orderLineStatus: '',
        deliveryConfirmStatus: '',
        orderNo: '',
        orderStatus: [],
        receiptStatus: '',
        orderSignStatus: '',
        sortBy: '',
        sortField: '',
        sourcingIds: [],
        supplier: '',
        overdue: '',
        time: '',
        receiptVoucherNum: '',
        pageNo: 1,
        pageSize: 10
      }
      this.init()
    },
    handleLog(orderDetailId) {
      this.log.open = true
      this.log.businessId = orderDetailId
    }
  }})
</script>

<style lang="scss" scoped>
.searchItem{
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 189px);
  }
}
::v-deep .green{
  background: #43cf7c;
}
::v-deep .orange{
  background: #ff8d1a;
}
.searchTimeItem{
  margin-right: 0;
  ::v-deep .el-form-item__content{
    width: calc(100% - 82px);
  }
}
.searchValue{
  width: 95%;
}
.red{
  color: red;
}
</style>
