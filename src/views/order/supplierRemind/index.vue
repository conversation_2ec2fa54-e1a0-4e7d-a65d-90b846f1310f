<template>
  <div class="app-container">

    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('order.emailAndReminderSubmission') }}
        <slot name="header" />
      </div>
      <el-form label-width="400px" label-position="left">
        <el-form-item :label="$t('order.automaticallySendEmailsToUrgeSuppliersToRespond')">
          <span>{{ $t('order.pendingSubmissionStatus') }} <el-input-number v-model="supplierRemindConfig.urgeSupplierDay" :min="0" />{{ $t('order.tianhou') }}  </span>
        </el-form-item>
        <el-form-item :label="$t('order.doYouWantToInitiateEmailFollowupAndSubmission')">
          <el-radio-group v-model="supplierRemindConfig.urgeSupplierEnable">
            <el-radio-button label="true">{{ $t('order.open') }}</el-radio-button>
            <el-radio-button label="false">{{ $t('order.close') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5" :offset="0">
          <el-button
            v-hasPermi="['order:answer-rel-notify-supplier-config:create']"
            type="primary"
            size="medium"
            @click="handleUpdate(supplierRemindConfig)"
          >{{ $t('order.toUpdate') }}</el-button>
        </el-col>
      </el-row>
    </el-card>

    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('order.emailReminderForShipment') }}
        <slot name="header" />
      </div>
      <el-form label-width="400px" label-position="left">
        <el-form-item :label="$t('order.automaticallySendEmailRemindersToSuppliersForShipment')">
          <span>{{ $t('order.beforeConfirmingTheDeliveryDate') }} <el-input-number v-model="supplierRemindConfig.remindSupplierDay" :min="0" />{{ $t('order.day') }}  </span>
        </el-form-item>
        <el-form-item :label="$t('order.doYouWantToEnableEmailRemindersForShipping')">
          <el-radio-group v-model="supplierRemindConfig.remindSupplierEnable">
            <el-radio-button label="true">{{ $t('order.open') }}</el-radio-button>
            <el-radio-button label="false">{{ $t('order.close') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5" :offset="0">
          <el-button
            v-hasPermi="['order:answer-rel-notify-supplier-config:create']"
            type="primary"
            size="medium"
            @click="handleUpdate(supplierRemindConfig)"
          >{{ $t('order.toUpdate') }}</el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getAnswerRelNotifySupplierConfig, saveAnswerRelNotifySupplierConfig } from '@/api/order/supplierRemind'

export default {
  name: 'SupplierRemindConfig',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      supplierRemindConfig: {
        // 跟催答交配置的天数（天）
        urgeSupplierDay: 0,
        // 跟催答交配置是否开启
        urgeSupplierEnable: false,
        // 提醒发货配置的天数（天）
        remindSupplierDay: 0,
        // 提醒发货配置是否开启
        remindSupplierEnable: false
      }
    }
  },
  created() {
    this.getConfig()
  },
  methods: {
    /** 查询 */
    getConfig() {
      this.loading = true
      // 执行查询
      getAnswerRelNotifySupplierConfig().then(response => {
        if (response.data) {
          this.supplierRemindConfig = response.data
        }
        this.loading = false
      })
    },
    /** 修改 */
    handleUpdate(config) {
      // 执行查询
      saveAnswerRelNotifySupplierConfig({ ...config }).then(res => {
        this.$modal.msgSuccess(this.$t('common.updateSuccessful'))
        this.getConfig()
      }).catch(() => {
        this.$modal.msgError(this.$t('order.updateFailed'))
      })
    }
  }
}
</script>

<style lang="scss" scoped>

.commonCard {
  margin: 10px 0;
}

.mainTab {
  color: rgba(73, 150, 184, 0.99);
  font-size: 16px;
  font-weight: 700;
}
</style>
