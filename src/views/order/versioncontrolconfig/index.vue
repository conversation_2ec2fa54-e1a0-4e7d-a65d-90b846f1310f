<template>
  <div class="app-container">

    <div>
      <el-button
        v-has-permi="['order:version-fields-rel:update']"
        size="medium"
        type="primary"
        @click="save"
      >{{ $t('order.saveVersionUpgradeFields') }}</el-button>
    </div>
    <!--    切换tab-->
    <div>
      <el-tabs v-model="selectedType" @tab-click="triggerType">
        <el-tab-pane :label="$t('order.orderHeader')" name="order_header" />
        <el-tab-pane :label="$t('order.orderLine')" name="order_detail" />
      </el-tabs>
    </div>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('order.dictionaryName')" align="center" prop="remark" />
      <el-table-column :label="$t('order.whetherToTriggerVersionUpgrading')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-radio v-model="scope.row.effect" label="true">{{ $t('order.affectVersionUpgrade') }}</el-radio>
          <el-radio v-model="scope.row.effect" label="false">{{ $t('order.doesNotAffectVersionUpgrade') }}</el-radio>
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script>
import {
  updateVersionFieldsRel,
  getFields,
  getVersionFieldsRelList
} from '@/api/order/versionFieldsRel'

export default {
  name: 'Versionfieldsrel',
  components: {
  },
  data() {
    return {
      // 待保存对象
      versionField: {},
      // tab页切换
      selectedType: 'order_header',
      // 遮罩层
      loading: true,
      // 订单升版触发的变更字段列表
      list: [],
      // 查询参数
      queryParams: {
        orderId: null,
        fields: null
      },
      // 部分字段不需要展示的在此定义
      ignoreFieldsOfDetail: ['order_id', 'line_no', 'status', 'material_description', 'specification', 'category_id', 'tax_rate', 'amount_before_tax', 'amount_after_tax', 'remarks', 'order_detail_extend_field1', 'order_detail_extend_field2', 'order_detail_extend_field3'],
      ignoreFieldsOfHeader: ['order_no', 'status', 'order_type', 'remark', 'version', 'sourcing_id', 'buyer_tel', 'buyer_contact', 'buyer_address', 'buyer_company_id', 'buyer_email', 'supplier_id',
        'supplier_name_short', 'supplier_contact', 'supplier_contact_email', 'request_delivery_date', 'latest_request_delivery_date', 'order_extend_field1', 'order_extend_field2', 'order_extend_field3']
    }
  },
  created() {
    this.getList()
  },
  methods: {
    save() {
      const allFields = []
      this.list.map(item => allFields.push({ 'columnName': item.columnName, 'selected': item.effect }))
      this.versionField.fields = JSON.stringify(allFields)
      this.versionField.type = this.selectedType

      updateVersionFieldsRel(this.versionField).then(res => {
        this.$modal.msgSuccess(this.$t('common.updateSuccessful'))
      }).catch(() => {
        this.$modal.msgError(this.$t('order.updateFailed'))
      })
    },
    triggerType() {
      this.queryParams.type = this.selectedType
      this.getList()
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getFields().then(response => {
        const arr = response.data[this.selectedType]
        if (this.selectedType === 'order_header') {
          this.list = arr.filter(item => !this.ignoreFieldsOfHeader.includes(item.columnName))
        } else {
          this.list = arr.filter(item => !this.ignoreFieldsOfDetail.includes(item.columnName))
        }
        this.getVersion()
        this.loading = false
      })
    },
    // 获取选中的版本字段
    getVersion() {
      getVersionFieldsRelList().then(res => {
        var parse = JSON.parse(res.data?.filter(item => item.type === this.selectedType)[0].fields)
        if (parse) {
          this.list.forEach(row => {
            this.$set(row, 'effect', parse.filter(field => field.columnName === row.columnName)[0].selected)
          })
        }
      })
    }
  }
}
</script>
