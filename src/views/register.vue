<template>
  <div style="display: flex;align-items: center;min-height: 100%">
    <div
        :style="{'backgroundImage':(loginBgImage!=undefined&&loginBgImage!='')?'url('+loginBgImage+')':'../../assets/images/bg.jpg'}"
        class="back-container">
      <div class="back-background">
        <div style="position: fixed;top: 15px;display: flex;align-items: center;color: #ffffff;font-size: 16px">
          <el-image v-if="loginLogoImage!=undefined&&loginLogoImage!=''" :src="loginLogoImage"
                    style="margin-left: 15px"/>
          <svg-icon v-else icon-class="logo3" style="height: 68px;width: 198px"/>
        </div>
        <div style="position: fixed;bottom: 15px;right:15px;align-items: center;color: #ffffff;font-size: 16px">
          <div style="color: black;font-size:16px ">Powered by
            <el-button type="text" @click="window.open('http://www.esicint.com')"><span
                style="text-decoration: underline;font-size:16px">ESIC</span></el-button>
          </div>
          <svg-icon v-if="language==='zh'" icon-class="logo3" style="height: 68px;width: 198px"/>
          <svg-icon v-else icon-class="logo1" style="margin-left:47px;height: 92px;width: 125px"/>
        </div>
      </div>

      <div class="register">

        <el-form
            ref="registerForm"
            :inline="true"
            :model="registerForm"
            :rules="registerFormRules"
            class="register-form"
            label-position="left"
        >

          <div class="register-title">
            {{ $t('common.welcomeToRegister', {companyName: sysCompanyName}) }}
          </div>
          <div>
            <el-form-item :label="$t('supplier.fullName')" class="registerName" label-width="90px" prop="name">
              <el-input v-model="registerForm.name" class="register-name"/>
            </el-form-item>
            <el-form-item class="registerName" label-width="90px" prop="lastName">
              <el-radio v-model="registerForm.sex" border label="1">{{ $t('common.sir') }}</el-radio>
              <el-radio v-model="registerForm.sex" border label="2">{{ $t('common.maam') }}</el-radio>
            </el-form-item>
          </div>

          <el-form-item :label="$t('supplier.mailbox')" label-width="90px" prop="email">
            <el-input v-model="registerForm.email" class="register-common">
              <el-button slot="append" :disabled="emailDisable" style="padding: 0 5px" type="text" @click="sendCaptcha">
                {{
                  emailDisable ? $t('common.reacquireAfterSeconds', {countDown: countDown}) : $t('common.sendVerificationCode')
                }}
              </el-button>
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('common.emailVerificationCode')" label-width="90px" prop="verificationCode">
            <el-input v-model="registerForm.verificationCode" class="register-common"/>
          </el-form-item>
          <el-form-item :label="$t('common.cellphoneNumber')" label-width="90px" prop="phone">
            <el-input v-model="registerForm.CountryPrefix" style="width:70px"/>

            <el-input
                v-model="registerForm.phone"
                class="register-common"
                style="margin-left:5px;width:335px"
            />

          </el-form-item>
          <div style="text-align: right">
            <el-checkbox v-model="agree" />
            <el-link style="margin-left: 10px">{{ $t('common.readAndAgreeTheSCPT') }}</el-link>

          </div>
          <div style="margin-top: 25px">
            <span>{{ $t('common.existingAccount') }}</span>
            <el-button type="text" @click="$router.push('/login')">{{ $t('common.directLogin') }}</el-button>
            <el-button id="register-btn" class="ripple" type="primary" @click="submitRegister">{{
                $t('common.submit')
              }}
            </el-button>

          </div>
        </el-form>
        <el-dialog
            :visible.sync="showWelcome"
            append-to-body
            top="30vh"
        >
          <div style="text-align: center;font-size: 20px;margin-bottom: 20px">{{ $t('common.welcome') }}</div>
          <p style="text-indent:2em;line-height:2em;"> {{ $t('common.welcomeMessage1') }}
            <span style="text-decoration: underline">{{ $t('common.welcomeMessage2') }}</span>
            {{ $t('common.welcomeMessage3') }}
          </p>
          <div style="text-align: right">
            <el-button class="toSupplierBtn" @click="showWelcome= false">{{ $t('common.cancel') }}</el-button>
            <el-button class="toSupplierBtn" type="primary" @click="confirmRegister">{{
                $t('common.confirm')
              }}
            </el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>

</template>
<script>
import {checkEmailCode, register, sendEmail} from '@/api/supplier/info'
import {getConfigKey} from '@/api/infra/config'

export default {
  name: 'Register',
  data() {
    const required = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('common.pleaseEnter')))
      } else {
        callback()
      }
    }
    const registerFormRules = {
      name: [{
        validator: required,
        trigger: 'blur'
      }],

      password: [{
        validator: required,
        trigger: 'blur'
      },
        {
          pattern: /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/,
          message: this.$t('register.passwordRule')
        }
      ],
      confirmPassword: [{
        validator: (rule, value, callback) => {
          if (value === '') {
            callback(new Error(this.$t('register.passwordText')))
          } else if (value !== this.registerForm.password) {
            callback(new Error(this.$t('register.rePasswordText')))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }],
      verificationCode: [{
        validator: required,
        trigger: 'blur'
      }],
      // companyName: [{
      //   validator: companyNameRule,
      //   trigger: 'blur'
      // }],
      regionCode: [{
        validator: required,
        trigger: 'blur'
      }],
      comType: [{
        validator: required,
        trigger: 'blur'
      }],
      email: [{
        validator: required,
        trigger: 'blur'
      },
        {
          pattern: /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
          message: this.$t('supplier.incorrectMailboxFormat'),
          trigger: 'blur'
        }
      ],
      phone: [{
        validator: required,
        trigger: 'blur'
      },
        {
          pattern: /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-7|9])|(?:5[0-3|5-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[1|89]))\d{8}$/,
          message: this.$t('supplier.pleaseEnterTheCorrectPhoneNumber'),
          trigger: 'blur'
        }
      ]

    }

    return {
      registerForm: {
        email: '',
        culture: 'zh-CN',
        CountryPrefix: '+86',
        name: '',
        verificationCode: '',
        password: '',
        confirmPassword: '',
        mobile: '',
        phone: '',
        companyName: '',
        comType: [],
        // comType: null,
        regionCode: ''
      },

      registerFormRules,
      emailDisable: false,
      agree: null,
      showWelcome: false,
      countDown: 60,
      sysCompanyName: '',
      loginBgImage: undefined,
      loginLogoImage: undefined
    }
  },
  computed: {
    language() {
      return this.$store.getters.language
    }
  },
  created() {
    this.getConfigKey('system.company.name').then(response => {
      this.sysCompanyName = response.data
    })
  },
  mounted() {
    this.getLoginImage()
  },
  methods: {
    getLoginImage() {
      getConfigKey('login.bg.image').then(response => {
        this.loginBgImage = response.data
      })
      getConfigKey('login.logo.image').then(response => {
        this.loginLogoImage = response.data
      })
    },
    getCountDown() {
      const count = this.countDown
      const interval = window.setInterval(() => {
        if (--this.countDown <= 0) {
          this.countDown = count
          window.clearInterval(interval)
        }
      }, 1000)
    },
    sendCaptcha() {
      this.$refs.registerForm.validateField('email', valid => {
        if (!valid) {
          sendEmail({
            name: this.registerForm.name,
            email: this.registerForm.email
          }).then(res => {
            this.emailDisable = true
            setTimeout(() => {
              this.emailDisable = false
            }, 60000)
            this.getCountDown()
          })
        }
      })
    },
    submitRegister() {
      if (!this.agree) {
        this.$message.error(this.$t('supplier.pleaseAgreeToTheTerms'))
        return
      }
      this.$refs.registerForm.validate((valid) => {
        if (valid) {
          checkEmailCode(
              this.registerForm
          ).then(res => {
            this.showWelcome = true
          })
        }
      })
    },
    confirmRegister() {
      register(
          this.registerForm
      ).then(res => {
        this.$router.push(`/supplierRegister/${res.data.supplierId}?token=${res.data.token}`)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.back-container {
  height: 100vh;
  width: 100%;
  // background-color: $bg;
  //background: linear-gradient(45deg, #0073b1, #0c8996);
  background-image: url("../assets/images/bg.jpg");
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-family: "Helvetica Bold", sans-serif;
      font-size: 30px;
      // color: $light_gray;
      margin: 0 auto 32px auto;
      text-align: left;
      color: #2E6F88;
      font-weight: bold;
    }

    .set-language {
      color: #fff;
      position: absolute;
      top: 8px;
      font-size: 20px;
      right: 0px;
      cursor: pointer;

      ::v-deep .el-dropdown-selfdefine {
        color: #2E6F88;
      }
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }

  .verify {
    ::v-deep .verify-bar-area {
      border: none;
      color: #909399;
    }

    ::v-deep .verify-left-bar {
      border: none;
    }

    ::v-deep .verify-move-block {
      border-radius: 5px;
      border: none;
      box-shadow: 1px 1px 5px 0px rgba(41, 119, 150, 0.54);

      .verify-icon:before {
        width: 20px;
        height: 20px;
        background-image: url(data:image/svg+xml;base64,PHN2ZyBpZD0i5Zu+5bGCXzEiIGRhdGEtbmFtZT0i5Zu+5bGCIDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDIwMCAyMDAiPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDojODBBQ0MzO308L3N0eWxlPjwvZGVmcz48dGl0bGU+5Zu+5qCHPC90aXRsZT48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0xMDMuMywxMDkuMjFhMTAuNDgsMTAuNDgsMCwwLDAsMC0xNC44MmwtNjAtNjAtMTAsMTAsNTcuNCw1Ny40Mi01Ny40LDU3LjMxLDEwLDEwLjA2Wm02Mi44MywwYTEwLjYyLDEwLjYyLDAsMCwwLC4wNS0xNUwxMDcuMTEsMzQuNDNsLTEwLjkxLDEwLDU3LjQsNTcuNDItNTcuNCw1Ny4zMSwxMCwxMC4wNloiLz48L3N2Zz4K)

      }

    }

    ::v-deep .verify-move-block:hover {
      background: #ffffff !important;
    }

    ::v-deep .verify-msg {
      font-size: 14px;
    }
  }

}

.register {
  border-radius: 10px;
  width: 650px;
  background: #F5F7FA;
  margin: 10px auto;
  padding: 60px 70px;

  .register-head {
    display: flex;
    justify-content: center;
    align-items: center;

    .head-png {
      margin: 0 50px;
      width: 100px;
    }
  }

  .agree {
    text-align: center;
    margin: 10px 0
  }

  .register-title {
    font-weight: bold;
    color: #4996B8;
    font-size: 20px;
    text-align: center;
    margin-bottom: 40px;
  }

  .register-form {
    .register-name {
      width: 211px;
    }

    .register-common {
      width: 410px
    }

    .register-checkbox {
      text-align: left;
      width: 560px;
    }

    ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #4996B8;
    }

    ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #4996B8;
      border: 1px solid #4996B8 !important
    }

    ::v-deep .el-checkbox__inner {
      border: 1px solid #4996B8 !important
    }

    ::v-deep .el-checkbox__label {
      padding-left: 40px;
    }

    ::v-deep .el-input-group__append {
      background: #ffffff;
      color: #4996B8;
    }

    .link {
      color: #3b91b6;
    }

    #register-btn {
      float: right;
      width: 55%;
      height: 39px;
      background: #4996B8;
      //box-shadow: 0px 13px 36px 0px rgba(130,184,211,0.74);
      border-radius: 6px;
      font-size: 18px
    }
  }
}

.toSupplierBtn {
  width: 80px;
  border-radius: 3px;
}

</style>
