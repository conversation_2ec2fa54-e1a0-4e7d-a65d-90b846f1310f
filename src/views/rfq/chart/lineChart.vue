<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'

import resize from '@/views/dashboard/mixins/resize'
export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '200px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ expectedData, actualData } = {}) {
      this.chart.setOption({
        xAxis: {
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          boundaryGap: false,
          axisLabel: {
            color: '#999'
          },
          axisLine: {
            color: '#999'
          },
          axisTick: {
            show: false
          }
        },
        grid: {
          left: 10,
          right: 10,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          axisLabel: {
            color: '#999'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        legend: {
          x: 'center',
          y: 'bottom',
          padding: [0, 0, 0, 0],
          data: [this.$t('rfq.timeForEvaluationAndQuotation'), this.$t('rfq.quotationSuccessRate')]
        },
        series: [{
          name: this.$t('rfq.timeForEvaluationAndQuotation'), itemStyle: {
            normal: {
              color: '#ff8d1a',
              lineStyle: {
                color: '#ff8d1a',
                width: 2
              }
            }
          },
          smooth: true,
          type: 'line',
          data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
          animationDuration: 2800,
          animationEasing: 'cubicInOut'
        },
        {
          name: this.$t('rfq.quotationSuccessRate'),
          smooth: true,
          type: 'line',
          itemStyle: {
            normal: {
              color: '#4996b8',
              lineStyle: {
                color: '#4996b8',
                width: 2
              }
            }
          },
          data: [11, 2, 12, 6, 4, 6, 7, 4, 3, 10, 2, 1],
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        }]
      })
    }
  }
}
</script>
