<template>
  <div>
    <common-card
      :title="$t('rfq.approvalFormList')"
    >
      <el-table
        ref="rfqPriceApproval"
        :data="approvalList"
        border
        max-height="200"
        current-row-key="id"
        highlight-current-row
        @row-click="changeApproval"
      >
        <el-table-column :label="$t('rfq.approvalNo')" show-overflow-tooltip prop="approvalNo" />
        <el-table-column :label="$t('rfq.approvalFormName')" show-overflow-tooltip prop="approvalName" />
        <el-table-column :label="$t('rfq.approvalApplicant')" show-overflow-tooltip prop="creator">
          <template slot-scope="scope">
            <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="scope.row.creator" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.approvalInitiationDate')" show-overflow-tooltip prop="createTime" width="150">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.quoteTemplate')" show-overflow-tooltip prop="templateName" />
        <el-table-column :label="$t('rfq.currentApprover')" show-overflow-tooltip prop="approvers">
          <template slot-scope="scope">
            <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="scope.row.approvers" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.approvalStatus')" show-overflow-tooltip prop="status" width="100">
          <template slot-scope="scope">
            <dict-tag :type="DICT_TYPE.RFQ_APPROVAL_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.approvalResults')" show-overflow-tooltip prop="flag" width="100">
          <template slot-scope="scope">
            <dict-tag :type="DICT_TYPE.RFQ_APPROVAL_FLAG" :value="scope.row.flag" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('ERP回写结果')" show-overflow-tooltip prop="erpCallBackResult" width="100">
          <template slot-scope="scope">
            <dict-tag :type="DICT_TYPE.RFQ_ERP_CALL_BACK_RESULT" :value="scope.row.erpCallBackResult" />
          </template>
        </el-table-column>
        <!--        <el-table-column :label="$t('rfq.enclosure')" show-overflow-tooltip prop="attachmentQuantity"/>-->
        <el-table-column :label="$t('rfq.comparisonOfSupplierQuantity')" show-overflow-tooltip prop="supplierQuantity" />
        <el-table-column :label="$t('rfq.materialQuantity')" show-overflow-tooltip prop="materialQuantity" />
        <el-table-column :label="$t('common.operate')" align="center">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status==='approval_completed'"
              size="mini"
              type="text"
              @click="$ApprovalRecord(scope.row.id,'rfq')"
            >{{ $t('system.approvalRecord') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </common-card>
    <el-card class="cardBefore" style="position: relative;overflow: unset;margin-top: 15px">
      <div slot="header">
        <div style="display: flex;justify-content: space-between;">
          <div style="flex: 1">
            {{ $t('rfq.approvalFormDetails') }} - {{ selectRow.approvalName }} - {{ selectRow.approvalNo }}
            <dict-tag :type="DICT_TYPE.RFQ_APPROVAL_STATUS" :value="selectRow.status" />
          </div>
          <div style="flex: 1;text-align: right;font-size: 14px;text-decoration: underline;padding-right: 10px">
            <a @click="$refs['section1'].$el.scrollIntoView({block: 'center',behavior: 'smooth'})">{{
              $t('rfq.approveBom')
            }}</a>
            /
            <a @click="$refs['section2'].scrollIntoView({block: 'center',behavior: 'smooth'})">{{
              $t('rfq.reportAndAttachments')
            }}</a>
          </div>
        </div>
      </div>
      <el-card ref="section1" style="margin-bottom: 10px">
        <div slot="header">
          {{ $t('rfq.approveBom') }}
        </div>
        <div>
          <div class="flexBtn">
            <el-input
              v-model="queryParams.search"
              :placeholder="$t('rfq.pleaseEnterCategoryNameMaterialCodeSupplierAbbreviationMpnmfg')"
              clearable
              style="width: 500px"
              @keyup.enter.native="queryParams.pageNo = 1;getList();"
            />
            <el-button plain type="primary" @click="queryParams.pageNo = 1;getList();">{{
              $t('common.search')
            }}
            </el-button>
            <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>
          </div>
          <div class="flexBtn">
            <div style="display: flex;align-items: center">
              <el-checkbox v-model="queryParams.priceTrend" :false-label="0" :true-label="1" @change="changePriceTrend">
                {{ $t('rfq.onlyLookAtThePriceRisingMaterials') }}
              </el-checkbox>
              <el-checkbox v-model="queryParams.priceTrend" :false-label="0" :true-label="2" @change="changePriceTrend">
                {{ $t('rfq.onlyLookAtThePriceReducedMaterials') }}
              </el-checkbox>
              <el-checkbox v-model="queryParams.status" :false-label="null" :true-label="'approve_return'" @change="changePriceTrend">
                {{ $t('rfq.onlyLookAtReturnedMaterials') }}
              </el-checkbox>
            </div>
          </div>
          <div class="flexBtn" style="justify-content: space-between">
            <div style="display: flex;align-items: center">
              <div v-if="queryParams.approvalId&&approvalStatus!=='approval_completed'">
                <el-button
                  v-has-permi="['rfq:approval-material-rel:create']"
                  :loading="showReturnFormLoading"
                  plain
                  type="danger"
                  @click="showReturnForm(false)"
                >{{ $t('rfq.enterRejectionComments') }}
                </el-button>
                <el-button
                  v-has-permi="['rfq:approval-material-rel:query']"
                  plain
                  @click="getReturnMaterialList"
                >{{ $t('rfq.viewDetailsOfRejectionComments') }}
                </el-button>
              </div>
            </div>

            <div style="display: flex;align-items: center">
              <div style="float: right;margin-right: 10px">
                <el-button v-has-permi="['rfq:price-approval:export']" plain type="primary" @click="exportData">{{
                  $t('order.download')
                }}
                </el-button>
              </div>
              <customFields
                ref="customFields"
                :custom-columns.sync="girdOption.columns"
                :list-id="girdOption.id"
                :only-custom="false"
                type="rfqApprovalCustom"
                @queryTable="getList"
              />
            </div>
          </div>
          <vxe-grid
            ref="approvalGrid"
            :data="list"
            :loading="loading"
            :span-method="mergeRowMethod"
            style="margin-top: 10px"
            v-bind="girdOption"
            @checkbox-change="checkBoxChange"
            @checkbox-all="checkBoxAllChange"
          >
            <template #materialCode="{row}">
              <copy-button
                type="text"
                @click="showMaterialDetail(row.materialId)"
              >
                {{ row.materialCode }}
              </copy-button>
            </template>
            <!--小数位处理-->
            <template #historyPrice="{row}">
              <el-popover
                width="210"
                placement="left"
                trigger="hover"
                :visible-arrow="false"
              >
                <el-table
                  :show-header="false"
                  :data="[
                    {key: $t('rfq.historicalPrice'), value: {type: 'number-format', val: (row.historyPrice!=null) ? row.historyPrice : 'N/A'}},
                    {key: $t('rfq.historicalSuppliers'), value: {type: 'text', val: row.historySupplierName}},
                    {key: $t('rfq.historicalCurrency'), value: {type: 'dict-tag', val: row.historyCurrency}},
                    {key: $t('rfq.rateOfChange'), value: {type: 'number-format', val: row.recommendationVsHistoricalRateChange}}
                  ]"
                >
                  <el-table-column prop="key" />
                  <el-table-column :show-overflow-tooltip="true">
                    <template slot-scope="{row}">
                      <number-format v-if="row.value.type==='number-format'" :value="row.value.val" />
                      <dict-tag v-else-if="row.value.type==='dict-tag'" :type="DICT_TYPE.COMMON_CURRENCY" :value="row.value.val" />
                      <span v-else>{{ row.value.val }}</span>
                    </template>
                  </el-table-column>
                </el-table>

                <el-button slot="reference" style="text-decoration: underline" type="text" @click="showHistoryPrice(row.materialId)">
                  <number-format
                    v-if="row.historyPrice!=null"
                    :value="row.historyPrice"
                  />
                  <span v-else>N/A</span>
                </el-button>
              </el-popover>
            </template>
            <template #conversionUnitPriceWithoutTax="{row}">
              <number-format :decimal-place="store.getters.rfqDecimalPlace" :value="row.conversionUnitPriceWithoutTax" />
            </template>
            <template #conversionUnitPriceTax="{row}">
              <number-format :decimal-place="store.getters.rfqDecimalPlace" :value="row.conversionUnitPriceTax" />
            </template>
            <template #exchangeRate="{row}">
              <number-format :decimal-place="2" :value="row.exchangeRate" />
            </template>
            <template #originalUnitPriceWithoutTax="{row}">
              <number-format :decimal-place="store.getters.rfqDecimalPlace" :value="row.originalUnitPriceWithoutTax" />
            </template>
            <template #originalUnitPriceTax="{row}">
              <el-popover
                width="260"
                placement="right"
                trigger="hover"
                :visible-arrow="false"
              >
                <el-table :data="row.listSource" :show-header="false" style="width: 100%">
                  <el-table-column prop="seq" :show-overflow-tooltip="true" width="155">
                    <template slot-scope="scope">
                      <span :class="(row.recommendedSupplierName === scope.row.supplierName && row.originalUnitPriceTax===scope.row.price) ? 'recommendClass':''">
                        {{ scope.row.seq }}.{{ scope.row.supplierName }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="price" width="60">
                    <template slot-scope="scope">
                      <span :class="(row.recommendedSupplierName === scope.row.supplierName && row.originalUnitPriceTax===scope.row.price) ? 'recommendClass':''">
                        <number-format :value="scope.row.price" />
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button slot="reference" style="text-decoration: underline;" type="text">
                  <number-format :value="row.originalUnitPriceTax" />
                </el-button>
              </el-popover>

            </template>
            <template #vsHistoricalRateChange="{row}">
              <number-format :decimal-place="2" :value="row.vsHistoricalRateChange" />
            </template>
            <template #lowestUnitPrice="{row}">
              <number-format :decimal-place="store.getters.rfqDecimalPlace" :value="row.lowestUnitPrice" />
            </template>
            <template #recommendedUnitPriceTax="{row}">
              <number-format :decimal-place="store.getters.rfqDecimalPlace" :value="row.recommendedUnitPriceTax" />
            </template>
            <template #recommendationVsHistoricalRateChange="{row}">
              <number-format :decimal-place="2" :value="row.recommendationVsHistoricalRateChange" />
            </template>
            <template #unitPriceIncludesTaxPriceUnit="{row}">
              <number-format :decimal-place="store.getters.rfqDecimalPlace" :value="row.unitPriceIncludesTaxPriceUnit" />
            </template>
            <template #unitPriceWithoutTaxPriceUnit="{row}">
              <number-format :decimal-place="store.getters.rfqDecimalPlace" :value="row.unitPriceWithoutTaxPriceUnit" />
            </template>
            <template #proportion="{row}">
              <number-format :decimal-place="2" :value="row.proportion" />
            </template>
            <!--数据字典转换处理-->
            <template #basicUnit="{row}">
              <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.basicUnit" />
            </template>
            <template #orderUnit="{row}">
              <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.orderUnit" />
            </template>
            <template #materialStatus="{row}">
              <dict-tag :type="DICT_TYPE.RFQ_MATERIAL_STATUS" :value="row.materialStatus" />

            </template>
            <template #categoryId="{row}">
              <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
            </template>
            <template #historyCurrency="{row}">
              <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.historyCurrency" />
            </template>
            <template #currency="{row}">
              <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.currency" />
            </template>
            <template #originalCurrency="{row}">
              <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.originalCurrency" />
            </template>
            <template #lowestUnitPriceCurrency="{row}">
              <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.lowestUnitPriceCurrency" />
            </template>
            <template #taxRate="{row}">
              <dict-tag :type="DICT_TYPE.SUPPLIER_RATE" :value="row.taxRate" />
            </template>
            <template #paymentMethod="{row}">
              <dict-tag :type="DICT_TYPE.SUPPLIER_PAYMENT_DAYS" :value="row.paymentMethod" />
            </template>
            <template #recommend="{row}">
              <span v-if="row.recommend===true">√</span>
            </template>

            <template #approvalCount="{row}">
              <el-button
                size="mini"
                type="text"
                @click="showOperationLog(row.materialId,'RFQ_PRICE_APPROVAL',$t('审批记录'))"
              >{{ row.approvalCount }}
              </el-button>
            </template>

            <template #quotationsCount="{row}">
              <el-button
                size="mini"
                type="text"
                @click="showOperationLog(row.materialSupplierRelId,'RFQ_QUOTATIONS',$t('报价记录'))"
              >{{ row.quotationsCount }}
              </el-button>
            </template>
            <template #toolbar_buttons>
              <el-alert
                v-if="selectedRecord.length"
                show-icon
                type="success"
              >
                <template slot="title">
                  {{ $t('common.selectedTotal', { selectedTotal: selectedRecord.map(j => j.materialId)?.length }) }}
                  <el-button style="padding: 0;margin-left: 10px" type="text" @click="clearSelected">{{
                    $t('rfq.empty')
                  }}
                  </el-button>
                </template>
              </el-alert>
            </template>
          </vxe-grid>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNo"
            :total="total"
            @pagination="getList"
          />
        </div>
      </el-card>
      <div ref="section2" class="el-card__header" style="border-bottom:unset">
        <div class="mainTab">{{ $t('rfq.reportAndAttachments') }}</div>
      </div>
      <div style="margin-left: 10px">
        <el-descriptions :column="10" style="margin-bottom: 10px">
          <el-descriptions-item />
          <el-descriptions-item span="8" :label="$t('rfq.recommendedRemarks')">
            <el-input
              v-model="recommendRemark"
              :disabled="true"
              :rows="3"
              type="textarea"
            />
          </el-descriptions-item>
          <el-descriptions-item />
        </el-descriptions>
        <el-descriptions :colon="false" direction="vertical" label-class-name="labelTitle">
          <el-descriptions-item
            :content-style="{'vertical-align': 'top'}"
            :label="$t('rfq.priceComparisonReport')"
          >
            <div v-for="item in rfqFileList.parityFile">
              <el-button style="text-decoration: underline" type="text" @click="openFile(item.filePath)">{{
                item.fileName
              }}
              </el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item
            :content-style="{'vertical-align': 'top'}"
            :label="$t('rfq.priceRecommendationAttachment')"
          >
            <div v-for="item in rfqFileList.purchaseFile">
              <el-button style="text-decoration: underline" type="text" @click="openFile(item.filePath)">{{
                item.fileName
              }}
              </el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item
            :content-style="{'vertical-align': 'top'}"
            :label="$t('rfq.supplierQuotationAttachment')"
          >
            <div v-for="item in rfqFileList.supplierFile">
              <el-button style="text-decoration: underline" type="text" @click="openFile(item.url)">{{
                item.fileName
              }}
              </el-button>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div>
        <bpm-process-instance :process-instance-id="selectRow.processInstanceId" :view-only="viewOnly" @rejectAuditEvent="submitReturnAll" @approvalAuditEvent="submitApproval" />
      </div>
    </el-card>
    <el-dialog
      v-if="returnCommentVisible"
      :title="$t('rfq.returnComments')"
      :visible.sync="returnCommentVisible"
      width="400px"
    >
      <div v-if="!returnCommentForm.isAll">
        <div>{{ $t('rfq.youAreEnteringReturnCommentsForTheFollowingMaterials') }}</div>
        <p>{{ $t('rfq.afterYouClickOkYouCanStillModifyTheReturnedCommentsOrWithdrawComments') }}</p>
        <p>{{ $t('rfq.whenYouSubmitTheApprovalFormTheSystemWillAutomaticallyReturnTheMaterialsWithComments') }}</p>
        <p style="color: red">{{ returnCommentForm.materialCodes.join(',') }}</p>
      </div>
      <div v-else>
        <p>{{ $t('rfq.youAreReturningAllMaterialsInTheApprovalForm') }}</p>
        <p>{{ $t('rfq.afterYouClickOkTheApprovalFormWillBeReturnedAutomatically') }}</p>
      </div>

      <el-input
        v-model="returnCommentForm.opinion"
        :placeholder="$t('rfq.pleaseEnterTheReturnComments')"
        :rows="5"
        type="textarea"
      />
      <div slot="footer">
        <el-button @click="returnCommentVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitReturn">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="returnMaterialVisible"
      :title="$t('rfq.viewReturnedItems')"
      :visible.sync="returnMaterialVisible"
      width="1000px"
    >
      <el-table v-loading="returnLoading" :data="returnMaterialList">
        <el-table-column :label="$t('material.materialCode')" prop="materialCode" />
        <el-table-column :label="$t('material.materialDescription')" prop="materialDescription" />
        <el-table-column :label="$t('material.manufacturer')" prop="mfg" />
        <el-table-column :label="$t('material.manufacturersPartNumber')" prop="mpn" />
        <el-table-column :label="$t('rfq.returnComments')">
          <template #default="scope">

            <el-input v-model="scope.row.opinion" />
          </template>

        </el-table-column>
        <el-table-column :label="$t('common.operate')">
          <template #default="scope">
            <el-button type="text" @click="revokeReturnOpinion(scope.row)">{{
              $t('rfq.withdrawalOfReturnedComments')
            }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="returnTotal > 0"
        :limit.sync="returnQueryParams.pageSize"
        :page.sync="returnQueryParams.pageNo"
        :total="returnTotal"
        @pagination="getReturnMaterialList"
      />
      <div slot="footer">
        <el-button :loading="submitReturnMaterialLoading" type="primary" @click="submitReturnMaterial">
          {{ $t('order.determine') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="logVisible"
      :visible.sync="logVisible"
      :title="operationLogTitle"
      width="1000px"
    >
      <rfqOperationRecord
        :business-id="businessId"
        :business-type="businessType"
        :log-visible.sync="logVisible"
      />
    </el-dialog>
    <el-dialog
      v-if="historyVisible"
      :visible.sync="historyVisible"
      width="1000px"
    >
      <historyPriceComponents
        ref="historyPriceDialog"
        :is-edit="true"
      />
      <div style="margin-top:10px;text-align: right">
        <el-button type="primary" @click="saveHistoryData">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
    <rfqMaterial
      ref="rfqMaterial"
      :material-visible.sync="materialVisible"
      :rfq-project-id="true"
      :rfq-project-status="null"
    />
  </div>
</template>

<script>
import {
  approvalListPage,
  cancelData,
  getApprovalMaterialSupplierRel,
  checkReturnComments,
  createApprovalMaterialRel,
  deleteApprovalMaterialRel,
  exportDetail,
  getApprovalList,
  getApprovalMaterialRel,
  getByApproval,
  getCustomFieldsByTemplate,
  getPriceApproval,
  getRfqFile,
  rejectPriceApproval,
  submitPriceApproval,
  updateApprovalMaterialRel,
  urgentData,
  rejectAllPriceApproval
} from '@/api/rfq/home'
import { parseTime } from '@/utils/ruoyi'
import { getPriceApprovalMaterialAllIds } from '@/api/rfq/priceApproval'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import store from '@/store'
import { updateProjectMaterialHisPrice } from '@/api/rfq/projectMaterialHisPrice'
import BpmProcessInstance from '@/components/BpmProcessInstance/index.vue'

export default {
  name: 'Approval',
  components: {
    BpmProcessInstance,
    historyPriceComponents: () => import('@/views/rfq/components/historyPrice'),
    rfqOperationRecord: () => import('@/views/rfq/components/rfqOperation'),
    rfqMaterial: () => import('@/views/rfq/components/material'),
    customFields: () => import('./customFields')

    // Steps
  },
  data() {
    return {
      // 审批工作流的显示与隐藏
      viewOnly: false,
      historyData: {
        projectMaterialId: null,
        historyPrice: null,
        historySupplierId: null,
        historySupplierName: '',
        historyCurrency: null,
        avplMaterialSupplierQuantityLadderId: null
      },
      // 审批通过loading
      submitApprovalLoad: false,
      // 审批单状态，用于控制按钮
      approvalStatus: '',
      // 审批退回
      showReturnFormLoading: false,
      // 退回
      submitReturnMaterialLoading: false,
      // 历史价格弹出框
      historyVisible: false,
      businessId: '',
      businessType: '',
      logVisible: false,
      radio: 1,
      radio1: ' ',
      materialVisible: false,
      selectRow: {},
      businessIdApprovalRecord: '',
      visibleApprovalRecord: false,
      queryParams: {
        approvalId: 0,
        categoryIds: [],
        materialIds: [],
        pageNo: 1,
        pageSize: 10,
        priceTrend: 0,
        search: '',
        status: '',
        sortBy: 'ASC',
        sortField: 'id',
        templateId: null,
        processInstanceId: null
      },
      total: 0,
      loading: false,
      list: [],
      baseColumns: [
        { title: '', type: 'checkbox', visible: true, fixed: 'left', width: 30 },
        {
          title: this.$t('material.materialCode'),
          field: 'materialCode',
          slots: { default: 'materialCode' },
          visible: true,
          width: 100
        },
        {
          title: this.$t('material.materialDescription'),
          field: 'materialDescription',
          fixed: 'left',
          visible: true,
          width: 100
        },
        {
          title: this.$t('common.status'),
          slots: { default: 'materialStatus' },
          field: 'materialStatus', visible: true, width: 100
        },
        {
          title: this.$t('material.category'),
          slots: { default: 'categoryId' },
          field: 'categoryId', visible: true, width: 100
        },
        { title: this.$t('material.specificationAndModel'), field: 'specifications', visible: true, width: 100 },
        { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
        { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
        {
          title: this.$t('rfq.estimatedAnnualConsumption'),
          headerClassName: 'yellow',
          field: 'dosage',
          visible: true,
          width: 100
        },
        {
          title: this.$t('material.basicUnit'),
          headerClassName: 'yellow',
          slots: { default: 'basicUnit' },
          field: 'basicUnit', visible: true, width: 100
        },
        {
          title: this.$t('rfq.requestedOrderQuantityFrom'),
          headerClassName: 'yellow',
          field: 'quantityFrom',
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.theOrderQuantityIsRequiredToReach'),
          headerClassName: 'yellow',
          field: 'quantityTo',
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.historicalPrice'),
          headerClassName: 'gray',
          field: 'historyPrice',
          slots: { default: 'historyPrice' },
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.historicalSuppliers'),
          field: 'historySupplierName',
          headerClassName: 'gray',
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.historicalCurrency'),
          field: 'historyCurrency',
          headerClassName: 'gray',
          visible: true,
          slots: { default: 'historyCurrency' },
          width: 100
        },
        {
          title: this.$t('rfq.numberOfApprovals'),
          slots: { default: 'approvalCount' },
          field: 'approvalCount', visible: true, width: 100
        },
        { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
        {
          title: this.$t('rfq.numberOfQuotations'),
          slots: { default: 'quotationsCount' },
          field: 'quotationsCount', visible: true, width: 100
        },
        {
          title: this.$t('rfq.orderQuantityFrom'),
          field: 'quotationQuantityFrom',
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.orderQuantityArrived'),
          field: 'quotationQuantityTo',
          visible: true,
          width: 100
        },
        {
          title: this.$t('system.currency'),
          field: 'currency',
          visible: true,
          slots: { default: 'currency' },
          width: 100
        },
        {
          title: this.$t('rfq.exchangeRate'),
          field: 'exchangeRate',
          visible: true,
          slots: { default: 'exchangeRate' },
          width: 100
        },
        {
          title: this.$t('supplier.taxRate'),
          field: 'taxRate',
          visible: true,
          slots: { default: 'taxRate' },
          width: 100
        },
        { title: this.$t('rfq.MinimumOrderQuantity'), field: 'moq', visible: true, width: 100 },
        {
          title: this.$t('rfq.quotedUnitPriceExcludingTax'),
          headerClassName: 'red',
          field: 'originalUnitPriceWithoutTax',
          visible: true,
          slots: { default: 'originalUnitPriceWithoutTax' },
          width: 100
        },
        {
          title: this.$t('rfq.quotationUnitPriceIncludingTax'),
          field: 'originalUnitPriceTax',
          headerClassName: 'red',
          slots: { default: 'originalUnitPriceTax' },
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.quotationOriginalCurrency'),
          field: 'originalCurrency',
          headerClassName: 'red',
          slots: { default: 'originalCurrency' },
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.vsHistoricalPriceChangeRate'),
          field: 'vsHistoricalRateChange',
          headerClassName: 'red',
          slots: { default: 'vsHistoricalRateChange' },
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.expiryDateOfPriceValidity'),
          field: 'priceValidUntil',
          formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.deliveryPerioddays'),
          field: 'deliveryPeriod',
          headerClassName: 'indigo',

          visible: true,
          width: 100
        },
        {
          title: this.$t('supplier.paymentMethod'),
          headerClassName: 'indigo',
          slots: { default: 'paymentMethod' },
          field: 'paymentMethod', visible: true, width: 100
        },
        {
          title: this.$t('material.purchasingUnit'),
          headerClassName: 'blue',
          slots: { default: 'orderUnit' },
          field: 'orderUnit', visible: true, width: 100
        },
        {
          title: this.$t('rfq.lowestUnitPriceWithoutTax'),
          field: 'lowestUnitPrice',
          headerClassName: 'green',
          slots: { default: 'lowestUnitPrice' },
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.minimumUnitPriceCurrency'),
          field: 'lowestUnitPriceCurrency',
          headerClassName: 'green',
          slots: { default: 'lowestUnitPriceCurrency' },
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.supplierWithLowestUnitPrice'),
          field: 'lowestSupplierName',
          headerClassName: 'green',

          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.recommendedUnitPriceExcludingTax'),
          field: 'recommendedUnitPriceTax',
          visible: true,
          slots: { default: 'recommendedUnitPriceTax' },
          width: 100
        },
        {
          title: this.$t('rfq.recommendedVsHistoricalPriceChangeRate'),
          field: 'recommendationVsHistoricalRateChange',
          visible: true,
          slots: { default: 'recommendationVsHistoricalRateChange' },
          width: 100
        },
        {
          title: this.$t('rfq.recommendedSuppliers'),
          field: 'recommendedSupplierName',
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.recommend'),
          slots: { default: 'recommend' },
          field: 'recommend', visible: true, width: 100
        },
        {
          title: this.$t('rfq.mixtureRatio'),
          // slots: { default: 'proportion' },
          slots: { default: 'proportion' },
          field: 'proportion', visible: true, width: 100
        },
        {
          title: this.$t('material.priceUnit'),
          // slots: { default: 'priceUnit' },

          field: 'priceUnit', visible: true, width: 100
        },
        {
          title: this.$t('rfq.unitPriceExcludingTaxpriceUnit'),
          field: 'unitPriceWithoutTaxPriceUnit',
          visible: true,
          width: 100,

          slots: { default: 'unitPriceWithoutTaxPriceUnit' }
        },
        {
          title: this.$t('rfq.unitPriceIncludingTaxpriceUnit'),
          field: 'unitPriceIncludesTaxPriceUnit',
          visible: true,
          width: 100,
          slots: { default: 'unitPriceIncludesTaxPriceUnit' }
        },
        {
          title: this.$t('rfq.expiryDateOfRecommendedPrice'),
          // slots: { default: 'recommendedPriceExpires' },

          field: 'recommendedPriceExpires',
          formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
          visible: true,
          width: 100
        },
        {
          title: this.$t('rfq.erpSynchronizationMessage'),
          field: 'erp',
          visible: true,
          width: 100
        }
      ],
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'approvalGrid',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        filterConfig: {
          remote: true
        },
        rowConfig: {
          keyField: 'materialId',
          isHover: true
        },
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      exportLoading: false,
      returnCommentVisible: false,
      returnCommentForm: {
        opinion: '',
        isAll: false,
        materialCodes: []
      },
      returnMaterialVisible: false,
      returnQueryParams: {
        approvalId: '',
        categoryIds: [],
        pageNo: 1,
        pageSize: 10
      },
      returnTotal: 0,
      returnLoading: false,
      returnMaterialList: [],
      rfqFileList: {
        parityFile: [],
        purchaseFile: [],
        supplierFile: []
      },
      recommendRemark: '',
      // 用来记录自定义列插入的位置。目前以采购单位列后面
      columnsStartIndex: null,
      // 自定义列的长度
      customColumnsLength: 0,
      // 合并列定义
      mergeRowFields: {
        // 根据物料+供应商合并的初始字段的长度，用于切换模块的时候进行保留列的判断
        quotationsMaterialSupplierFieldLength: 0,
        // 根据物料合并的字段
        fields: [
          'materialCode',
          'materialDescription',
          'materialStatus',
          'categoryId',
          'specifications',
          'mfg',
          'mpn',
          'dosage',
          'basicUnit',
          'quantityFrom',
          'quantityTo',
          'historyPrice',
          'historySupplierName',
          'historyCurrency',
          'historyRates',
          'lowestUnitPrice',
          'lowestUnitPriceCurrency',
          'lowestSupplierName',
          'recommendedUnitPriceTax',
          'recommendedUnitPriceWithoutTax',
          'recommendationVsHistoricalRateChange',
          'recommendedSupplierName',
          'approvalCount',
          'erp'
        ],
        // 根据物料+供应商合并的字段
        quotationsMaterialSupplierFields: [
          'supplierName',
          'quotationsCount',
          'currency',
          'exchangeRate',
          'taxRate',
          'moq',
          'deliveryPeriod',
          'orderUnit',
          'paymentMethod',
          'priceValidUntil'
        ],
        // 无论如何都根据供应商合并字段
        fullFields: [
          'proportion',
          'priceUnit',
          'recommendedPriceExpires'
        ]
      },
      selectedRecord: [],
      categoryList: [],
      approvalList: [],
      operationLogTitle: ''
    }
  },
  computed: {
    store() {
      return store
    }
  },
  async mounted() {
    // 价格录入不显示单价未税和单价含税
    // KSN-581RFS240320018 审批单中“单价未税”无显示
    if (this.$route.query.code === 'price_entry') {
    } else {
      const wholeEnquiryColumns = [{
        title: this.$t('order.unitPriceWithoutTax'),
        field: 'conversionUnitPriceWithoutTax',
        visible: true,
        slots: { default: 'conversionUnitPriceWithoutTax' },
        width: 100
      },
      {
        title: this.$t('order.unitPriceIncludingTax'),
        field: 'conversionUnitPriceTax',
        slots: { default: 'conversionUnitPriceTax' },
        visible: true,
        width: 100
      }]
      this.baseColumns.push(...wholeEnquiryColumns)
    }
    this.girdOption.columns = this.baseColumns
    console.log(this.girdOption.columns)

    // 自定义字段放在订单单位后面
    this.columnsStartIndex = this.girdOption.columns.findIndex((arr) => arr.field === 'orderUnit') >= 0 ? this.girdOption.columns.findIndex((arr) => arr.field === 'orderUnit') + 1 : this.girdOption.columns.length
    this.mergeRowFields.quotationsMaterialSupplierFieldLength = this.mergeRowFields.quotationsMaterialSupplierFields.length
    await this.init()
    this.getRecommendRemark()
    this.getFileList()
    this.getCategories()
  },
  methods: {
    // 保存选择的历史价格
    saveHistoryData() {
      if (this.$refs.historyPriceDialog.$refs.historyPriceGrid.getRadioRecord() == null) {
        this.$message.warning(this.$t('order.pleaseSelectAPieceOfData'))
        return
      }
      const row = this.$refs.historyPriceDialog.$refs.historyPriceGrid.getRadioRecord()
      this.historyData.historyPrice = row.originalUnitPriceWithoutTax
      this.historyData.historySupplierId = row.supplierId
      this.historyData.historySupplierName = row.supplierName
      this.historyData.historyCurrency = row.originalCurrency
      this.historyData.avplMaterialSupplierQuantityLadderId = row.avplMaterialSupplierQuantityLadderId
      updateProjectMaterialHisPrice(this.historyData).then(res => {
        this.historyVisible = false
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.getList()
      })
    },
    async init() {
      this.loading = true
      const res = await getApprovalList({ projectId: this.$route.query.id })
      this.approvalList = res.data
      const approvalId = this.$route.query.approvalId
      if (approvalId) {
        this.$refs.rfqPriceApproval.setCurrentRow(this.approvalList.find(a => a.id === Number(approvalId)))
      } else {
        this.$refs.rfqPriceApproval.setCurrentRow(this.approvalList[0])
      }
      this.queryParams.approvalId = this.approvalList.at(0)?.id
      this.returnQueryParams.approvalId = this.approvalList.at(0)?.id
      this.selectRow = { ...this.approvalList.at(0) }
      this.processView()
      this.loading = false
    },
    // 物料列表的跨页全选框
    checkBoxAllChange({ checked }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.approvalGrid
      if (checked) {
        const projectMaterialIds = Array.from(new Set([...this.selectedRecord.map(item => item.materialId)]))
        if (projectMaterialIds?.length + this.total > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.clearCheckboxRow()
          // 当前选中的数据key数组
          const selectIds = grid.getCheckboxRecords().map(item => item.materialId)
          // 取交集
          const arr = projectMaterialIds.filter(x => selectIds.includes(x))
          if (arr.length > 0) {
            arr.forEach((id) => {
              grid.setCheckboxRow(grid.getRowById(id), true)
            })
          }
        } else {
          this.checkAllData(checked)
        }
      } else {
        this.checkAllData(checked)
      }
    },
    checkAllData(checked) {
      getPriceApprovalMaterialAllIds(this.queryParams).then(res => {
        if (checked) {
          this.selectedRecord = Array.from(new Set([...this.selectedRecord, ...res.data]))
        } else {
          const projectMaterialIds = Array.from(new Set([...res.data.map(item => item.materialId)]))
          this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => !projectMaterialIds.includes(x.materialId))))
        }
      })
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    showMaterialDetail(id) {
      this.materialVisible = true
      this.$refs.rfqMaterial.showMaterialDetail(id)
    },
    // 物料列表的单个选择框
    checkBoxChange({ checked, row }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.approvalGrid
      if (checked) {
        const projectMaterialIds = Array.from(new Set([...this.selectedRecord.map(item => item.materialId)]))
        if (projectMaterialIds?.length + 1 > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.setCheckboxRow(grid.getRowById(row.materialId), false)
        } else {
          const newData = {
            materialId: row.materialId,
            materialCode: row.materialCode
          }
          this.selectedRecord.push(newData)
        }
      } else {
        this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => row.materialId !== x.materialId)))
      }
    },
    // 跨页全选的清空
    clearSelected() {
      const grid = this.$refs.approvalGrid
      this.selectedRecord = []
      grid.clearCheckboxRow()
    },
    showHistoryPrice(projectMaterialId) {
      this.historyData.projectMaterialId = projectMaterialId
      this.historyVisible = true
    },
    // 根据模板id获取模板的自定义列表
    getTemplateCustomeFields() {
      this.getTemplateFields()
      this.getList()
    },
    getTemplateFields() {
      getCustomFieldsByTemplate({ templateId: this.queryParams.templateId }).then(
        res => {
          // 处理表格的自定义列
          if (res.data.length > 0) {
            this.girdOption.columns.splice(this.columnsStartIndex, this.customColumnsLength, ...res.data.map(item => {
              return {
                title: item.fieldName,
                field: item.fieldCode,
                slots: item.fieldDictType ? { default: item.fieldCode } : null,
                headerClassName: 'blue',
                visible: true,
                width: 100
              }
            }))
            // 重置自定义字段列长度
            this.customColumnsLength = 0
            if (res.data) {
              this.customColumnsLength = res.data.length
            }
            // 处理合并单元格列，默认自定义列都按照物料+供应商合并
            this.mergeRowFields.quotationsMaterialSupplierFields.splice(this.mergeRowFields.quotationsMaterialSupplierFieldLength, this.mergeRowFields.quotationsMaterialSupplierFields.length - this.mergeRowFields.quotationsMaterialSupplierFieldLength, ...res.data.map(item => (item.fieldCode)))
            this.$refs.customFields.selectSchemeRadio()
          }
        }
      )
    },
    getList() {
      this.loading = true
      approvalListPage(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
        this.$nextTick(() => {
          const grid = this.$refs.approvalGrid
          const projectMaterialIds = Array.from(new Set([...this.selectedRecord.map(item => item.materialId)]))
          this.list?.forEach((row) => {
            if (projectMaterialIds?.includes(row.materialId)) {
              setTimeout(() => {
                grid.setCheckboxRow(grid.getRowById(row.materialId), true)
              }, 0)
            }
          })
          if (this.list) {
            getApprovalMaterialSupplierRel({ approvalId: this.queryParams.approvalId, projectMaterialIds: this.list.map(v => v.materialId) }).then(res => {
              const listSource = res.data
              this.list?.forEach((row) => {
                row.listSource = listSource.filter(v => v.projectMaterialId === row.materialId)
              })
              this.list = [...this.list]
            })
          }
        })
      })
    },
    getRecommendRemark() {
      if (this.queryParams.approvalId) {
        getPriceApproval({ approvalId: this.queryParams.approvalId }).then(res => {
          this.recommendRemark = res.data.recommendRemark
          this.queryParams.templateId = res.data.quotationsTemplate
          this.approvalStatus = res.data.status
          this.getTemplateCustomeFields()
        })
      }
    },
    getFileList() {
      if (this.queryParams.approvalId) {
        Promise.all([getRfqFile({
          businessId: this.queryParams.approvalId,
          businessType: 'RFQ_APPROVAL_REPORT'
        }),
        getRfqFile({
          businessId: this.queryParams.approvalId,
          businessType: 'RFQ_APPROVAL'
        }),
        getByApproval({
          approvalId: this.queryParams.approvalId
        })
        ]).then(res => {
          this.rfqFileList.parityFile = res[0].data
          this.rfqFileList.purchaseFile = res[1].data
          this.rfqFileList.supplierFile = res[2].data
        })
      }
    },
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const prevRow = visibleData[_rowIndex - 1]
      let nextRow = visibleData[_rowIndex + 1]
      // 按照物料进行合并，选择框也按照物料合并
      if (row && (['checkbox'].includes(column.type) || this.mergeRowFields.fields.includes(column.property))) {
        if (prevRow && row.materialId === prevRow.materialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.materialId === nextRow.materialId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      } else if ([...this.mergeRowFields.fullFields, ...this.mergeRowFields.quotationsMaterialSupplierFields].includes(column.property) && row) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && row.quotationsMaterialSupplierRelId === prevRow.quotationsMaterialSupplierRelId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.quotationsMaterialSupplierRelId === nextRow.quotationsMaterialSupplierRelId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    exportData() {
      this.$modal.confirm(this.$t('rfq.areYouSureToExportAllApprovedBillsOfMaterials')).then(() => {
        this.exportLoading = true
        return exportDetail(this.queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('rfq.approveMaterialxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },

    closeMaterial(close) {
      close()
    },
    showReturnForm(isAll) {
      this.returnCommentForm.isAll = isAll
      this.returnCommentForm.opinion = ''

      if (!isAll) {
        const materialCodes = this.selectedRecord.map(item => item.materialCode)
        const materialIds = this.selectedRecord.map(item => item.materialId)
        if (!materialCodes.length) {
          this.$message.error(this.$t('rfq.pleaseSelectMaterialsFirst'))
          return
        }
        this.returnCommentForm.materialCodes = [...new Set(materialCodes)]
        this.queryParams.materialIds = [...new Set(materialIds)]
      }
      checkReturnComments(this.queryParams).then(res => {
        this.returnCommentVisible = true
      })
    },
    changePriceTrend() {
      this.getList()
    },
    submitReturnAll(taskId, processInstanceId, reason) {
      rejectAllPriceApproval({
        approvalId: this.queryParams.approvalId,
        taskId: taskId,
        reason: reason
      }).then(async() => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.returnCommentVisible = false
        this.getList()
        await this.getApprovalList()
        this.emitter.emit('freshStep', this.queryParams.projectId)
        this.selectRow = this.approvalList.find(j => j.id === this.selectRow.id)
        this.processView()
      })
    },
    submitReturn() {
      if (this.returnCommentForm.isAll) {
        rejectPriceApproval({
          approvalId: this.queryParams.approvalId,
          opinion: this.returnCommentForm.opinion
        }).then(() => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.returnCommentVisible = false
          this.getApprovalList()
          this.getList()
          this.emitter.emit('freshStep', this.queryParams.projectId)
        })
      } else {
        createApprovalMaterialRel({
          approvalId: this.queryParams.approvalId,
          opinion: this.returnCommentForm.opinion,
          materialIds: this.selectedRecord.map(item => item.materialId)
        }).then(() => {
          this.$message.success(this.$t('rfq.returnCommentsEnteredSuccessfully'))
          this.returnCommentVisible = false
          this.getApprovalList()
          this.getList()
        })
      }
    },
    getReturnMaterialList() {
      this.returnMaterialVisible = true
      this.returnLoading = true
      getApprovalMaterialRel(this.returnQueryParams).then(res => {
        this.returnMaterialList = res.data.list
        this.returnTotal = res.data.total
        this.returnLoading = false
      }).catch(() => {
        this.returnLoading = false
      })
    },
    revokeReturnOpinion(obj) {
      deleteApprovalMaterialRel({
        id: obj.id
      }).then(() => {
        this.$message.success(this.$t('rfq.withdrawalOfReturnedCommentsSucceeded'))
        this.getReturnMaterialList()
      })
    },
    submitReturnMaterial() {
      this.submitReturnMaterialLoading = true
      updateApprovalMaterialRel(this.returnMaterialList).then(() => {
        this.$message.success(this.$t('rfq.successfullySubmittedReturnComments'))
        this.returnMaterialVisible = false
        this.getList()
        this.submitReturnMaterialLoading = false
      }).catch(() => {
        this.submitReturnMaterialLoading = false
      })
    },
    submitApproval(taskId, processInstanceId, reason) {
      this.submitApprovalLoad = true
      submitPriceApproval({
        approvalId: this.queryParams.approvalId,
        taskId: taskId,
        reason: reason
      }).then(async() => {
        this.$message.success(this.$t('rfq.successfullySubmittedForApproval'))
        this.getList()
        await this.getApprovalList()
        this.submitApprovalLoad = false
        this.emitter.emit('freshStep', this.queryParams.projectId)
        this.selectRow = this.approvalList.find(j => j.id === this.selectRow.id)
        this.processView()
      })
    },
    openFile(url) {
      window.open(url)
    },
    showOperationLog(obj1, obj2, title) {
      this.logVisible = true
      this.businessId = obj1
      this.businessType = obj2
      this.operationLogTitle = title
    },
    resetQuery() {
      this.queryParams.search = ''
      this.queryParams.status = ''
      this.queryParams.pageNo = 1
      this.queryParams.pageSize = 10
      this.queryParams.priceTrend = 0
      this.getList()
    },
    expedited(row) {
      urgentData({
        ids: row.id
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.getApprovalList()
      })
    },
    revoke(row) {
      this.$modal.confirm(this.$t('确定撤销此审批单吗？')).then(() => {
        cancelData({
          ids: row.id
        }).then(res => {
          if (res.data) {
            this.queryParams.approvalId = null
            this.getApprovalList()
            this.getList()
            this.$message.success(this.$t('order.operationSucceeded'))
          } else {
            this.$message.success(this.$t('操作失败'))
          }
        })
      }).catch(() => {
      })
    },
    async getApprovalList(approvalId) {
      const res = await getApprovalList({
        projectId: this.$route.query.id
      })
      this.approvalList = res.data
      // 当最后一条审批单都撤销的时候，整个页面数据清理
      if (this.approvalList == null || this.approvalList.length == 0) {
        this.queryParams.approvalId = null
        this.getList()
      }
    },
    changeApproval(row) {
      this.queryParams.approvalId = row.id
      this.returnQueryParams.approvalId = row.id
      this.selectRow = { ...row }
      this.getRecommendRemark()
      this.getFileList()
      this.processView()
    },
    processView() {
      this.viewOnly = this.selectRow.status === 'approval_completed'
    }

  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

::v-deep .labelTitle {
  font-weight: bolder !important;
  color: black;

}

.commonMaterialItem {
  width: 33%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
}

.rfq-create-upload {
  margin: 10px 0
}

.rfq-create {
  margin-top: 15px;
}

.materialItem {
  width: 178px;
}

.rfqMaterial {
  .mainTab {
    margin-bottom: 15px;
  }
}

.cardBefore::before, .cardBefore::after {
  content: "";
  position: absolute;
  left: 5%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
}

.cardBefore::before {
  top: -15px;
  border-bottom: 15px solid #b7bac0; /* 灰色边框 */
}

.cardBefore::after {
  top: -13px;
  border-bottom: 13px solid #ffffff; /* 白色三角形 */
}

.flowFixedBtn {
  position: fixed;
  margin-left: 5%;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 10px 0;
}

.flexBtn {
  display: flex;
  margin-left: 20px;
  margin-top: 10px;
  justify-content: center;
}

.recommendClass{
  color:#4996b8
}

//::v-deep .gray{
//  background: #999999;
//}
//::v-deep .yellow{
//  background: #ffeb3b;
//}
//::v-deep .red{
//  background: #ff5733;
//}
//
//::v-deep .indigo{
//  background: #a5d642;
//}
//
//::v-deep .blue{
//  background: #2684e4;
//}
//
//::v-deep .green{
//  background: #41d17b;
//}
</style>

