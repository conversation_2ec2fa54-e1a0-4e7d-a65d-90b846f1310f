<template>
  <div class="rfq-create">
    <common-card
      :title=" $t('rfq.basicInformationOfTheProject')"
    >
      <div>
        <div>
          <el-form ref="rfqProject" :model="rfqProject" :rules="rfqProjectRules" inline label-width="184px" size="mini">
            <el-form-item
              :label="$t('rfq.eventName')"

              class="commonFormItemLeft"
              label-width="184px"
              prop="eventName"
            >
              <show-or-edit
                :disabled="!rfqProjectEdit"
                :value="rfqProject.eventName"
              >
                <el-input v-model="rfqProject.eventName" :disabled="!rfqProjectEdit" />

              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('rfq.allowSuppliersToModifyPurchaseUnits')"
              class="commonFormItem"
            >
              <el-switch
                v-model="rfqProject.modifyOrderUnits"
                :disabled="!rfqProjectEdit"
              />
            </el-form-item>
            <el-form-item
              :label="$t('supplier.purchasingOrganization')"
              class="commonFormItemLeft"
              label-width="184px"
              prop="purchaseOrg"
            >
              <show-or-edit
                :dict="DICT_TYPE.COMMON_PURCHASEORG"
                :disabled="!rfqProjectEdit || rfqProject.businessModel==12 "
                :value="rfqProject.purchaseOrg"
              >
                <el-select v-model="rfqProject.purchaseOrg" :disabled="!rfqProjectEdit  || rfqProject.businessModel==12 "
                           @change="onchangePurchaseOrg"    class="content" clearable>
                  <el-option
                    v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG,0)"
                    :key="item.id"

                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>

              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('rfq.allowSuppliersToModifyPriceLadders')"
              class="commonFormItem"
            >
              <el-switch
                v-model="rfqProject.supplierPriceLadder"
                :disabled="!rfqProjectEdit"
              />
            </el-form-item>
            <el-form-item
              :label="$t('rfq.projectDeadline')"
              class="commonFormItemLeft"
              label-width="184px"
              prop="expirationDate"
            >
              <show-or-edit
                :disabled="!rfqProjectEdit"
                :value="rfqProject.expirationDate"
                type="Date"
              >
                <el-date-picker
                  v-model="rfqProject.expirationDate"
                  :disabled="!rfqProjectEdit"
                  :placeholder="$t('order.selectDate')"
                  class="content"
                  placement="bottom-start"
                  type="date"
                  value-format="yyyy-MM-dd"
                />
              </show-or-edit>

            </el-form-item>
            <el-form-item
              :label="$t('supplier.endCustomer')"
              class="commonFormItem"
            >
              <show-or-edit
                :disabled="!rfqProjectEdit"
                :value="rfqProject.endCustomer"
              >
                <el-input v-model="rfqProject.endCustomer" :disabled="!rfqProjectEdit" />

              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('rfq.sponsor')"
              class="commonFormItem"
            >
              {{
                rfqProject.creator==null ? $store.getters.nickname: getUserName(rfqProject.creator)
              }}
            </el-form-item>
            <el-form-item
              :label="$t('rfq.application')"
              class="commonFormItem"
            >
              <show-or-edit
                :disabled="!rfqProjectEdit"
                :value="rfqProject.productLine"
              >
                <el-input v-model="rfqProject.productLine" :disabled="!rfqProjectEdit" />

              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('common.sourcing')"
              class="commonFormItem"
              prop="sourcingSources"
            >
              <show-or-edit
                :custom-list="sourcingSources"
                :disabled="!rfqProjectEdit"
                :value="rfqProject.sourcingSources"
              >
                <el-select
                  v-model="rfqProject.sourcingSources"
                  :disabled="!rfqProjectEdit"
                  class="content"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="dict in sourcingSources"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>

              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('rfq.loadingDestination')"
              class="commonFormItemLeft"
              label-width="184px"
              prop="shippingDestination"
            >
              <show-or-edit
                :custom-list="factorySources"
                :disabled="!rfqProjectEdit"
                :value="rfqProject.shippingDestination"
              >
                <el-select v-model="rfqProject.shippingDestination" :disabled="!rfqProjectEdit" class="content"
                           clearable  filterable  allow-create  placeholder="Please select" >
                  <el-option
                    v-for="dict in factorySources"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>

              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('auth.uploadAttachments')"
              class="commonFormItem"
            >
              <div style="display: flex;justify-content: space-between">

                <el-upload
                  class="upload-demo"
                  :disabled="!rfqProjectEdit"
                  :action="uploadUrl"
                  :headers="getBaseHeader()"
                  :on-remove="onRemove"
                  :on-preview="onPreview"
                  :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                  multiple
                  :limit="5"
                  :show-file-list="false"
                  :file-list="fileList"
                >
                  <el-button
                    v-if="rfqProjectEdit"
                    :disabled="!rfqProjectEdit"
                    class="uploadBtn"
                    size="small"
                    plain
                    icon="el-icon-plus"
                    type="primary"
                  />
                </el-upload>
                <div>
                  {{ $t('scar.viewAttachments') }}
                  <el-button
                    class="uploadBtn"
                    size="small"
                    style="padding: 5px 9px"
                    :disabled="fileList.length===0"
                    plain
                    :type="fileList.length?'primary':''"
                    @click="showFile=true"
                  >
                    {{ fileList.length }}
                  </el-button>

                  <el-dialog
                    v-if="showFile"
                    :visible.sync="showFile"
                    :title="$t('scar.viewAttachments')"
                    width="400px"
                  >
                    <el-upload
                      class="upload-demo"
                      :disabled="!rfqProjectEdit"
                      :action="uploadUrl"
                      :headers="getBaseHeader()"
                      :on-remove="onRemove"
                      :on-preview="onPreview"
                      :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                      multiple
                      :limit="5"
                      :show-file-list="true"
                      :file-list="fileList"
                    />
                    <div slot="footer">
                      <el-button type="primary" @click="showFile=false">{{ $t('order.close') }}</el-button>
                    </div>

                  </el-dialog>
                </div>
              </div>
            </el-form-item>
            <el-form-item
              :label="$t('rfq.projectRemarks')"
              class="commonFormItem"
            >
              <template #label>
                <span>
                  <el-button
                    v-if="rfqProject.id"
                    type="text"
                    @click="showRemark= true"
                  >{{ $t('common.modify') }}</el-button>
                  {{ $t('rfq.projectRemarks') }}
                </span>
              </template>
              <template #default>
                <div v-if="rfqProject.id" style="flex: 0 1 70%;font-size: 14px">
                  <div v-for="(item,index) in rfqRemarkList" :key="index">
                    <span>
                      {{ item.creatorName }} {{ parseTime(item.createTime) }}
                    </span>
                    <!--                    <el-button style="margin-left: 10px;padding: 0 12px" type="text" @click="delRemark(item.id)">-->
                    <!--                      {{ $t('common.del') }}-->
                    <!--                    </el-button>-->
                    <div style="word-break: break-word;">
                      {{ item.remark }}
                    </div>
                  </div>
                </div>
                <el-input
                  v-else
                  v-model="rfqRemark"
                  :placeholder="$t('rfq.pleaseEnterText')"
                  :rows="1"
                  autosize
                  type="textarea"
                />

              </template>

            </el-form-item>
            <div v-show="showMore">
              <el-form-item
                :label="$t('rfq.businessModel')"
                class="commonFormItemLeft"
                label-width="184px"
              >
                <dict-tag :type="DICT_TYPE.RFQ_BUSINESS_TYPE" :value="rfqProject.businessModel" />
              </el-form-item>
              <el-form-item/>
              <el-form-item
                :label="$t('rfq.projectCreationDate')"

                class="commonFormItemLeft"
                label-width="184px"
              >
                {{ parseTime(rfqProject.createTime, '{y}-{m}-{d}') }}
              </el-form-item>
              <el-form-item
                :label="$t('rfq.purposeOfInquiry')"
                class="commonFormItemLeft"
                label-width="184px"
              >
                <show-or-edit
                  :dict="DICT_TYPE.RFQ_INQUIRY_PURPOSE"
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.inquiryPurpose"
                >
                  <el-select
                    v-model="rfqProject.inquiryPurpose"
                    :disabled="!rfqProjectEdit"
                    :placeholder="$t('common.pleaseSelect')"
                    class="content"
                    clearable
                  >
                    <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.RFQ_INQUIRY_PURPOSE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>

                </show-or-edit>
              </el-form-item>
              <el-form-item
                :label="$t('rfq.productModel')"
                class="commonFormItem"
              >
                <show-or-edit
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.productNumber"
                >
                  <el-input v-model="rfqProject.productNumber" :disabled="!rfqProjectEdit" />

                </show-or-edit>
              </el-form-item>
              <el-form-item
                :label="$t('order.customerName')"
                class="commonFormItem"
              >
                <show-or-edit
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.customName"
                >
                  <el-input v-model="rfqProject.customName" :disabled="!rfqProjectEdit" />

                </show-or-edit>
              </el-form-item>
              <el-form-item
                :label="$t('rfq.productName')"
                class="commonFormItem"
              >
                <show-or-edit
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.productName"
                >
                  <el-input v-model="rfqProject.productName" :disabled="!rfqProjectEdit" />

                </show-or-edit>
              </el-form-item>
              <el-form-item
                :label="$t('rfq.paymentMethodRequired')"
                class="commonFormItem"
              >
                <show-or-edit
                  :dict="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION"
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.requestPaymentMethod"
                >
                  <el-select
                    v-model="rfqProject.requestPaymentMethod"
                    :disabled="!rfqProjectEdit"
                    :placeholder="$t('common.pleaseSelect')"
                    class="content"
                    clearable
                  >
                    <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>

                </show-or-edit>

              </el-form-item>
              <el-form-item
                :label="$t('rfq.requestedDeliveryMethod')"
                class="commonFormItem"
              >
                <show-or-edit
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.requestDeliveryMethod"
                >
                  <el-select
                    v-model="rfqProject.requestDeliveryMethod"
                    :disabled="!rfqProjectEdit"
                    :placeholder="$t('common.pleaseSelect')"
                    class="content"
                    clearable
                  >
                    <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>

                </show-or-edit>
              </el-form-item>
              <el-form-item
                :label="$t('rfq.terminalCustomerModel')"
                class="commonFormItem"
              >
                <show-or-edit
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.endCustomerModel"
                >
                  <el-input v-model="rfqProject.endCustomerModel" :disabled="!rfqProjectEdit" />

                </show-or-edit>
              </el-form-item>

            </div>
            <div>
              <el-button style="margin-left: 110px" type="primary" plain @click="showMore=!showMore">{{ showMore?$t('common.putItAway'):$t('system.more') }}</el-button>
            </div>
          </el-form>
        </div>
      </div>
      <div style="display: flex;justify-content: center">
        <el-button v-if="rfqProjectEdit" size="mini" style="width: 15%;" type="primary" @click="saveProject">
          {{ rfqProject.id?$t('order.toUpdate'):$t('common.submit') }}
        </el-button>

      </div>
    </common-card>

    <common-card
      v-if="rfqProject.id"
      :title="$t('rfq.billOfMaterials')"
      style="margin: 20px 0"
    >

      <div style="display: flex;justify-content: space-around;align-items: center;margin-bottom: 25px;">
        <div style="display: flex">
          <el-input
            v-model="queryParams.condition"
            :placeholder="$t('material.pleaseEnterTheMaterialCodeMaterialDescriptionSpecificationMfgMpn')"
            clearable
            style="width: 500px"
            @keyup.enter.native="queryParams.pageNo = 1;getTable();"
          />
          <el-button plain type="primary" @click="queryParams.pageNo = 1;getTable();">{{ $t('common.search') }}</el-button>
          <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>

          <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
            <el-button type="text">
              {{ $t('common.advancedSearch') }}

            </el-button>
            <i
              :style="showSearch? '':{transform: 'rotate(180deg)'}"
              class="el-icon-arrow-up"
              style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
            />
          </div>

        </div>

      </div>
      <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="189px" size="small">
        <el-form-item
          :label="$t('material.materialCode')"
          class="searchItem"
          prop="materialCode"
        >
          <el-input
            v-model="queryParams.materialCode"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item
          :label="$t('material.manufacturer')"
          class="searchItem"
          prop="mfg"
        >
          <el-input
            v-model="queryParams.mfg"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />

        </el-form-item>
        <el-form-item
          :label="$t('material.manufacturersPartNumber')"
          class="searchItem"
          prop="mpn"
        >
          <el-input
            v-model="queryParams.mpn"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item
          :label="$t('material.category')"
          class="searchItem"
          prop="factoryIds"
        >
          <el-cascader
            v-model="queryParams.categories	"
            :options="categoryList"
            :placeholder="$t('material.pleaseSelectCategory')"
            :props="{ value: 'id',label:'name'}"
            class="searchValue"
            clearable
            filterable
          />
        </el-form-item>

        <el-form-item
          :label="$t('supplier.supplier')"
          class="searchItem"
          prop="pgIds"
        >
          <el-input v-model="queryParams.supplier" :placeholder="$t('common.pleaseEnter')" class="searchValue" />
        </el-form-item>
        <el-form-item
          :label="$t('rfq.priceCategory')"
          class="searchItem"
          prop="pgIds"
        >
          <el-select v-model="queryParams.priceCategories" class="searchValue" clearable filterable>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_PRICE_CATEGORY)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          :label="$t('rfq.createdBy')"
          class="searchItem"
          prop="pgIds"
        >
          <el-select v-model="queryParams.creators" class="searchValue" clearable filterable multiple>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('rfq.viewTemporaryMaterials')"
          class="searchItem"
          prop="pgIds"
        >
          <el-checkbox v-model="queryParams.isTempMaterial" style="margin-left: 10px" />

        </el-form-item>
        <el-form-item
          :label="$t('material.materialStatus')"
          class="searchItem"
          prop="pgIds"
        >
          <el-select
            v-model="queryParams.projectMaterialStatus"
            class="searchValue"
            clearable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_MATERIAL_STATUS,0)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>

        </el-form-item>
        <el-form-item
          :label="$t('rfq.quoteStatus')"
          class="searchItem"
          prop="pgIds"
        >
          <el-select
            v-model="queryParams.quotationStatus	"
            class="searchValue"
            clearable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_QUOTATION_STATUS,0)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>

        </el-form-item>

        <!--        <div style="text-align: center">-->
        <!--          <el-button icon="el-icon-search" size="mini" type="primary" plain @click="getTable">{{-->
        <!--            $t('common.search')-->
        <!--          }}-->
        <!--          </el-button>-->
        <!--          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('common.reset') }}</el-button>-->

        <!--        </div>-->

      </el-form>

      <vxe-grid
        ref="rfqMaterialGrid"
        :data="list"
        :loading="tableLoading"
        :seq-config="{startIndex: (queryParams.pageNo - 1) * queryParams.pageSize}"
        :span-method="mergeRowMethod"
        style="margin-top: 10px"
        v-bind="girdOption"
        @checkbox-change="checkBoxChange"
        @checkbox-all="checkBoxAllChange"
      >
        <template #materialCode="{row}">
          <i
            v-if="row.materialDrawing"
            class="el-icon-picture-outline"
            style="font-size: 16px;margin-right: 3px;cursor: pointer"
            @click="downloadMaterialDrawing(row.projectMaterialId,row.materialCode)"
          />
          <copy-button
            @click="showMaterialDetail(row)"
          >
            {{ row.materialCode }}
          </copy-button>
        </template>
        <template #quotationsNo="{row}">
          <copy-button
            @click="$router.push(`/rfq/processHome/${encodeURIComponent(row.quotationsNo)}?id=${rfqProject.id}&quotationId=${row.quotationId}&active=1&code=${row.businessCode}`)"
          >
            {{ row.quotationsNo }}
          </copy-button>

        </template>
        <template #categoryId="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
        </template>
        <template #creator="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.creator" />
        </template>
        <template #status="{row}">
          <dict-tag :type="DICT_TYPE.RFQ_MATERIAL_STATUS" :value="row.status" />

        </template>
        <template #quotationsStatus="{row}">
          <dict-tag :type="DICT_TYPE.RFQ_QUOTATION_MATERIAL_STATUS" :value="row.quotationsStatus" />

        </template>
        <template #moq="{row}">
          <number-format :value="row.moq" />
        </template>
        <template #estimatedAnnualUsage="{row}">
          <number-format :value="row.estimatedAnnualUsage" />
        </template>
        <template #dosage="{row}">
          <number-format :value="row.dosage" />
        </template>
        <template #operation="{row}">
          <OperateDropDown
            :menu-item="[
              {
                name: $t('rfq.deleteItem'),
                show: ['new', 'to_be_sourced','shelve'].includes(row.status) && rfqProject.businessModel!==12
                 && rfqProjectEdit&&$store.getters.permissions.includes('rfq:project-materials:deleteMaterials'),
                action: (row) => delMaterialById(row.projectMaterialId),
                para: row
              },
              {
                name: $t('rfq.terminateItem'),
                show: ['to_quote','no_quote','to_be_recommended','approve_return'].includes(row.status)  && rfqProject.businessModel!==12
                 && rfqProjectEdit&&$store.getters.permissions.includes('rfq:quotations-recommend:material-terminate'),
                action: (row) => terminateMaterialById(row.projectMaterialId),
                para: row
              },
              {
                name: $t('rfq.deleteSupplier'),
                show: row.projectMaterialSupplierRelId && rfqProjectEdit&&!row.quotationsStatus&&$store.getters.permissions.includes('rfq:material-suppliers-rel:delete'),
                action: (row) => delSupplierById(row.projectMaterialId,row.quotationsNo,row.name),
                para: row
              },
              {
                name: $t('common.operationRecord'),
                show: true,
                action: (row) => showOperationLog(row.projectMaterialId),
                para: row
              },

            ]"
          />
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="24" class="mb8" style="width: 100%">
            <el-col :span="24">
              <sticky :sticky-top="0" :z-index="999" style="width: 100%">
                <el-button
                  v-if="rfqProjectEdit && rfqProject.businessModel!==12 "
                  v-has-permi="['rfq:project-materials:create']"
                  plain
                  size="mini"
                  type="primary"
                  @click="newMaterial"
                > {{ $t('rfq.newMaterial') }}
                </el-button>
                <el-button
                  v-if="rfqProjectEdit && rfqProject.businessModel!==12 "
                  :disabled="!rfqProjectEdit"
                  plain
                  size="mini"
                  type="primary"
                  @click="upload.open = true"
                > {{ $t('rfq.batchAddMaterials') }}
                </el-button>
                <el-button
                  v-if="rfqProjectEdit"
                  v-has-permi="['rfq:project-materials:tempoToFormal']"
                  plain
                  :disabled="!rfqProjectEdit"
                  size="mini"
                  type="primary"
                  @click="upload2.open = true"
                > {{ $t('临时物料转正式') }}
                </el-button>
                <el-button
                  v-if="rfqProjectEdit"
                  v-has-permi="['rfq:project-materials:query']"
                  plain
                  size="mini"
                  type="primary"
                  @click="showSupplier"
                > {{ $t('rfq.assignVendor') }}
                </el-button>
                <el-button
                  v-if="rfqProjectEdit"
                  v-has-permi="['rfq:project-materials:shelve']"
                  plain
                  size="mini"
                  type="primary"
                  @click="shelveMaterial"
                > {{ $t('rfq.shelvedMaterial') }}
                </el-button>
                <el-button
                  v-has-permi="['rfq:project-materials:query']"
                  plain
                  size="mini"
                  type="primary"
                  @click="downloadExcel"
                > {{ $t('order.download') }}
                </el-button>

                <el-button
                  v-if="rfqProjectEdit && rfqProject.businessModel!==12 "
                  v-has-permi="['rfq:project-materials:update']"
                  plain
                  size="mini"
                  type="danger"
                  @click="terminateMaterial"
                > {{ $t('rfq.terminateItem') }}
                </el-button>
                <el-button
                  v-if="rfqProjectEdit"
                  v-has-permi="['rfq:material-suppliers-rel:delete']"
                  plain
                  size="mini"
                  type="danger"
                  @click="delSupplier"
                > {{ $t('rfq.deleteSupplier') }}
                </el-button>
                <el-button
                  v-if="rfqProjectEdit && rfqProject.businessModel!==12 "
                  v-has-permi="['rfq:project-materials:deleteMaterials']"
                  plain
                  size="mini"
                  type="danger"
                  @click="delMaterial"
                > {{ $t('rfq.deleteItem') }}
                </el-button>
                <el-button
                  v-if="rfqProjectEdit"
                  v-has-permi="['rfq:project-materials:updateSource']"
                  plain
                  size="mini"
                  type="primary"
                  @click="updateSource"
                > {{ $t('更新寻源采购') }}
                </el-button>
                <right-toolbar
                  :custom-columns.sync="girdOption.columns"
                  :list-id="girdOption.id"
                  :only-custom="false"
                  :show-search.sync="showSearch"
                  style="float: right"
                  @queryTable="init"
                />
              </sticky>

            </el-col>

          </el-row>
          <el-alert
            v-if="selectedRecord.length"
            show-icon
            type="success"
          >
            <template slot="title">{{ $t('common.selectedTotal', {selectedTotal: selectedRecord.length}) }}
              <el-button style="padding: 0;margin-left: 10px" type="text" @click="clearSelected">{{
                $t('rfq.empty')
              }}
              </el-button>
            </template>
          </el-alert>
        </template>

      </vxe-grid>
      <!--      <el-button type="primary" style="float:left;z-index: 999;top: 25px;position:relative;" @click="selectAllRecord">{{ $t('rfq.selectAll') }}</el-button>-->
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getTable"
      />
      <div style="text-align: center;margin-top: 35px">
        <el-button
          v-if="rfqProjectEdit"
          v-has-permi="['rfq:project-materials:submitSourcing']"
          size="mini"
          style="width: 15%"
          @click="submitMaterialSource"
        >{{ $t('rfq.submitSourcing') }}
        </el-button>
        <el-button
          v-if="rfqProjectEdit"
          v-has-permi="['rfq:quotations:create']"
          size="mini"
          style="width: 15%"
          type="primary"
          @click="showCreate"
        >{{ $t('rfq.createRfq') }}
        </el-button>
      </div>
      <!--      <el-alert-->
      <!--        :title="$t('rfq.copySuccessfullyPrompted')"-->
      <!--        type="info"-->
      <!--        show-icon-->
      <!--      />-->
    </common-card>

    <rfqMaterial
      ref="rfqMaterial"
      :material-visible.sync="materialVisible"
      :rfq-project-id="rfqProject.id"
      :rfq-project-status="rfqProject.status"
      @getTable="getTable"
    />
    <!--    配置询价单显示隐藏字段-->
    <el-dialog
      v-if="createVisible"
      :title="$t('rfq.configureRfq')"
      :visible.sync="createVisible"
      width="1000px"
    >
      <el-form
        ref="quotationCreate"
        :model="disPlayForm"
        :rules="{
          requestReplyDate: [
            { required: true, message: $t('rfq.pleaseEnterTheRequiredReplyDate'), trigger: 'change' },
          ]
        }"
        label-width="130px"
      >
        <el-card class="cardStyle">
          <span style="font-weight: bold;">{{ $t('正在对以下供应商发出询价单') }}</span>
          <el-form-item >
            <el-checkbox-group v-model="quotationCreate.supplierIds" style="margin-top: 10px">
              <el-checkbox
                v-for="item in quoteSupplier"
                :key="item.supplierId"
                :label="item.supplierId"
                class="configItem"
              >
                <div class="checkbox-label-container configItem">
                  <span>{{ item.supplierName }}</span>
                  <div v-if="item.quoteStatus && item.quoteStatus.includes('termination')" style="padding-left: 10px">
                    <dict-tag :type="DICT_TYPE.RFQ_QUOTATION_MATERIAL_STATUS" value="termination" />
                  </div>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-card>
        <el-card class="cardStyle">
          <span style="font-weight: bold">{{ $t('rfq.theFieldsYouCheckedWillBeDisplayedInTheBasicItemInformationColumnOfTheRoomPriceList') }}</span>
          <el-form-item prop="displayFieldList">
            <el-checkbox-group v-model="disPlayForm.displayFieldList">
              <el-checkbox
                v-for="item in Object.keys(configList)"
                :key="item"
                :label="item"
                class="configItem"
                @change="configItemChange(item,rfqProject[item])"
              >
                <div v-if="!['requestPaymentMethod','requestDeliveryMethod','shippingDestination'].includes(item)" class="configValue">
                  {{ configList[item] }} :{{ rfqProjectCopy[item] }}

                </div>
                <div v-else-if="['requestPaymentMethod','requestDeliveryMethod'].includes(item)" class="configValue">
                  {{ configList[item] }} :
                  <dict-tag
                    v-if="item === 'requestPaymentMethod'"
                    :type="DICT_TYPE.SUPPLIER_PAYMENT_DAYS"
                    :value="requestPaymentMethod"
                  />
                  <dict-tag
                    v-if="item === 'requestDeliveryMethod'"
                    :type="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION"
                    :value="requestDeliveryMethod"
                  />
                </div>
                <div v-else class="configValue">
                  {{ configList[item] }} : {{ shippingDestination }}
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-card>
        <el-form-item :label="$t('rfq.requestedResponseDate')" prop="requestReplyDate">
          <el-date-picker
            v-model="disPlayForm.requestReplyDate"
            :placeholder="$t('order.selectDate')"
            type="date"
          />
        </el-form-item>
        <el-form-item :label="$t('rfq.purchasersQuotationRequirements')">
          <el-input
            v-model="disPlayForm.quotationRequirements"
            :rows="1"
            type="textarea"
          />
        </el-form-item>
        <el-form-item :label="$t('common.uploadPurchaserAttachments')" style="width: 30%;float: left;">
          <el-upload
            :action="uploadUrl"
            :file-list="fileBuyerList"
            :headers="getBaseHeader()"
            :limit="5"
            :on-preview="onPreview"
            :on-remove="onBuyerRemove"
            :on-success="onBuyerSuccess"
            :show-file-list="false"
            multiple
          >
            <el-button
              class="uploadBtn"
              icon="el-icon-plus"
              plain
              size="mini"
              type="primary"
            />

          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('rfq.viewPurchaserAttachments')" style="width: 30%;float: left;">
          <el-button
            :disabled="fileBuyerList.length===0"
            :type="fileBuyerList.length?'primary':''"
            class="uploadBtn"
            plain
            size="small"
            style="padding: 5px 9px"
            @click="showBuyerFile=true"
          >
            {{ fileBuyerList.length }}
          </el-button>
          <el-dialog
            v-if="showBuyerFile"
            append-to-body
            :visible.sync="showBuyerFile"
            :title="$t('scar.viewAttachments')"
            width="400px"
          >
            <el-upload
              :action="uploadUrl"
              :file-list="fileBuyerList"
              :headers="getBaseHeader()"
              :limit="5"
              :on-preview="onPreview"
              :on-remove="onBuyerRemove"
              :on-success="onBuyerSuccess"
              multiple
            />
            <div slot="footer">
              <el-button type="primary" @click="showBuyerFile=false">{{ $t('order.close') }}</el-button>
            </div>
          </el-dialog>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button @click="createVisible = false">{{ $t('common.cancel') }}</el-button>
      <!--        determine-->
        <el-button :loading="createLoading" type="primary" @click="submitCreate">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="$t('rfq.drawingList')"
      width="1000px"
    >
      <div>
        <el-button type="primary">{{ $t('order.download') }}</el-button>
        <el-button type="primary">{{ $t('supplier.batchUpload') }}</el-button>
        <el-input
          :placholder="$t('rfq.materialCodeAndFileName')"
          style="width: 40%;margin:0 20px"
        />
        <el-button plain type="primary">{{ $t('common.search') }}</el-button>
        <el-button type="primary">{{ $t('rfq.synchronizeSheetLibrary') }}</el-button>
      </div>
      <el-table style="margin-top: 15px">
        <el-table-column :label="$t('material.materialCode')" />
        <el-table-column :label="$t('rfq.materialCodeCrossReference')" />
        <el-table-column :label="$t('material.materialDescription')" />
        <el-table-column :label="$t('material.manufacturer')" />
        <el-table-column :label="$t('material.manufacturersPartNumber')" />
        <el-table-column :label="$t('rfq.fileName')" />
        <el-table-column :label="$t('rfq.drawingRemarks')" />
        <el-table-column :label="$t('rfq.drawingRemarks')" />
      </el-table>
      <div slot="footer">
        <el-button type="primary">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="upload.title" :visible.sync="upload.open" append-to-body width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url"
          :auto-upload="false"
          :data="queryParams"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-preview="onPreview"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button plain type="primary" @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="upload2.title" :visible.sync="upload2.open" append-to-body width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload2"
          :action="upload2.url"
          :auto-upload="false"
          :data="{projectId:rfqProject.id}"
          :disabled="upload2.isUploading"
          :headers="upload2.headers"
          :limit="1"
          :on-preview="onPreview"
          :on-progress="handleFileUploadProgress2"
          :on-success="handleFileSuccess2"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload2.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="importTemplate2"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="$refs.upload2.submit()"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="supplierVisible"
      :before-close="closeSupplier"
      :title="$t('rfq.selectSupplier')"
      :visible.sync="supplierVisible"
      width="1000px"
    >
      <div style="display: flex;justify-content: center;height: 600px">
        <el-card style="flex: 0 0 31%">
          <el-checkbox
            v-model="supplierForm.checkAll"
            :label="$t('rfq.categorymfgmpn')"
            @change="handleCheckAllSupplierForm"
          />
          <el-checkbox-group
            v-model="supplierForm.query"
            style="margin-top: 10px"
            @change="handleCheckedSupplierFormChange"
          >
            <div v-for="item in selectedMaterial" :key="item.id" style="margin: 2px 0">
              <el-checkbox :label="item">
                <dict-tag v-if="item.categoryId" :type="DICT_TYPE.COMMON_CATEGORY" :value="item.categoryId" />
                <span v-else>{{ item.mfg }}</span>
              </el-checkbox>
            </div>

          </el-checkbox-group>

        </el-card>
        <el-card style="flex: 0 0 31%;margin: 0 10px;position:relative;">
          {{ $t('rfq.systemRecommendedSupplier') }}
          <div>
            <el-input
              v-model="supplierForm.supplierName"
              style="width: 200px;margin-top: 10px;"
              @keyup.enter.native="supplierForm.pageNo=1;getMaterialSupplierList(false);"
            />
            <el-button plain type="primary" @click="supplierForm.pageNo=1;getMaterialSupplierList(false);">{{ $t('common.search') }}</el-button>
          </div>
          <div
            v-for="item in materialSupplierList"
            :key="item.id"
            style="margin: 5px 0"
          >
            <el-checkbox
              :disabled="supplierForm.query.length===0"
              :label="item"
              @change="(val)=>checkSupplier(val,item)"
            >
              {{ item.name }}
            </el-checkbox>
          </div>

          <div
            style="position:absolute;width:86%;bottom: 20px;text-align: center"
          >
            <el-pagination
              :current-page="supplierForm.pageNo"
              :page-size="10"
              :total="supplierTotal"
              layout="prev, pager, next"
              small
              @current-change="handleSupplierFormCurrentChange"
            />
          </div>

        </el-card>
        <el-card style="flex: 0 0 31%">
          <el-scrollbar style="height: 580px">
            {{ $t('rfq.selectedSupplier') }}
            <div v-for="(item,index) in selectedSupplier" :key="index" style="margin-top: 10px">
              <dict-tag v-if="item.categoryId" :type="DICT_TYPE.COMMON_CATEGORY" :value="item.categoryId" />
              <span v-else>{{ item.mfg }}</span>
              <div
                v-for="(sup,index) in item.materialSupplierList"
                :key="index"
                style="margin: 5px 0"
              >
                <el-tag
                  closable
                  @close="delMaterialSupplier(sup)"
                >
                  {{ sup.supplierName }}
                </el-tag>
              </div>
            </div>
          </el-scrollbar>

        </el-card>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="closeSupplierEvent"> {{ $t('order.close') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="$t('rfq.itemTermination')" :visible.sync="terminateMaterialOpen" append-to-body width="1000px">
      <el-row>
        <el-col :span="24">
          <div>
            <p>{{ $t('rfq.youAreTerminatingTheMaterial') }}</p>
            <p>{{ $t('rfq.noteThisOperationCannotBeReplied') }}</p>
            <p>{{ $t('rfq.pleaseEnterTheReasonForTermination') }}</p>
          </div>
          <div>
            <el-input v-model="terminateMaterialData.content" :rows="5" type="textarea" />
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="terminateMaterialOpen = false">{{ $t('common.cancel') }}</el-button>
        <el-button @click="actionterminateMaterialUp">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="logVisible"
      :visible.sync="logVisible"
      :title="$t('common.operationRecord')"
      width="1000px"
    >
      <rfqOperationRecord
        :business-id="businessId"
        :business-type="businessType"
        :log-visible.sync="logVisible"
      />
    </el-dialog>
    <!--终止询价单物料-->
    <el-dialog
      v-if="returnCommentVisible"
      :title="$t('rfq.opinion')"
      :visible.sync="returnCommentVisible"
      width="400px"
    >
      <div>
        <p>{{ $t('rfq.youAreTerminatingRfqMaterialsPleaseEnterTerminationComments') }}</p>
      </div>
      <el-input
        v-model="returnCommentForm.opinion"
        :placeholder="$t('rfq.pleaseEnterComments')"
        :rows="5"
        type="textarea"
      />
      <div slot="footer">
        <el-button @click="returnCommentVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleTerminate">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      width="400px"
      :visible.sync="showRemark"
      :title="$t('rfq.modifyProjectNotes')"
    >
      <div v-for="(item,index) in rfqRemarkList" :key="index" style="margin: 3px 0">
        <span>
          {{ item.creatorName }} {{ parseTime(item.createTime) }}
        </span>
        <el-button style="margin-left: 10px;padding: 0 12px;color: #d43030" type="text" @click="delRemark(item.id)">
          {{ $t('common.del') }}
        </el-button>
        <div style="word-break: break-word;">
          {{ item.remark }}
        </div>
      </div>
      <div style="display: flex">
        <el-input
          v-model="rfqRemark"
          style="margin: 10px 0"
          :placeholder="$t('rfq.pleaseEnterText')"
          :rows="1"
          autosize
          type="textarea"
        />
        <el-button
          style="margin-left: 10px"
          type="text"
          @click="submitRemark"
        >
          {{ $t('common.add') }}
        </el-button>
      </div>
      <div slot="footer">
        <el-button @click="showRemark = false">   {{ $t('order.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBaseHeader } from '@/utils/request'
import {
  checkMaterialCategoryIsNull,
  checkMaterialHasQuote,
  checkMaterialStatus,
  checkSumbitToSource,
  checkTerminateMaterial,
  createFileRelForCreateProject,
  createFileRelListForCreateProject,
  createQuotation,
  createRfqRemark,
  deleteMaterials,
  updateSource,
  deleteMaterialsSupplier,
  deleteMaterialsSupplierRel,
  delRfqFile,
  delRfqRemark,
  downloadMaterialDrawings,
  downloadMaterialQuote,
  getMaterialSupplierRelByMaterialIds,
  getMfgAndSupplierRel,
  getProjectMaterialsQuotationsLists,
  getRemarkList,
  getRfqFile,
  getRfqMaterial,
  getRfqMaterialList,
  getRfqProject,
  getRfqProjectDisplay,
  importTemplate,
  importTemplate2, quoteSupplier,
  rfqFilePost,
  saveMaterialAndSupplierRel,
  saveRfqProject,
  saveRfqProjectDisplay,
  shelveRfqMaterial,
  sumbitToSource,
  terminateRfqMaterial, checkMergedRfq
} from '@/api/rfq/home'
import { DICT_TYPE, getDictData, getDictDatas } from '@/utils/dict'
import dayjs from 'dayjs'
import { terminateRecommendMaterial } from '@/api/rfq/quoationRecommend'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import Sticky from '@/components/Sticky/index.vue'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import rfq from '@/views/rfq/index.vue'
import { parseTime } from '../../../utils/ruoyi'
import {listPurchaseOrgFactoryFromCache} from "@/api/system/factory";
import {startShip} from "@/api/om/pl/pl";

export default {
  name: 'Create',
  components: {
    OperateDropDown,
    Sticky,
    ShowOrEdit,
    rfqOperationRecord: () => import('@/views/rfq/components/rfqOperation'),
    rfqMaterial: () => import('@/views/rfq/components/material')
    // Steps
  },
  props: ['steps'],
  data() {
    return {
      // 上传文件id集合
      fileIds: [],
      fileBuyerIds: [],
      fileTempList: [],
      // 配置报价单确定防止重复提交
      createLoading: false,
      // 询价单物料终止的弹出框
      returnCommentVisible: false,
      // 要求交货方式
      requestDeliveryMethod: '',
      // 要求付款方式
      requestPaymentMethod: '',
      // 装运目的地
      shippingDestination: '',
      // 询价单物料终止的表单参数
      returnCommentForm: {
        opinion: '',
        quotationsMaterialSupplierRelId: null
      },
      businessId: null,
      businessType: 'MATERIAL_OPERATION_RECORD',
      getBaseHeader,
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      // projectUploadData: {
      //   requestPage: '/rfq/project'
      // },
      fileList: [],
      fileBuyerList: [],
      showSearch: false,
      customList: [],
      terminateMaterialOpen: false,
      terminateMaterialData: {
        materialIds: [],
        content: '',
        type: 0
      },
      supplierList: [{ supplierId: 1, supplierName: 'aaa' }, { supplierId: 2, supplierName: 'bbb' }],
      configList: {
        requestPaymentMethod: this.$t('rfq.paymentMethodRequired'),
        productLine: this.$t('rfq.application'),
        requestDeliveryMethod: this.$t('rfq.requestedDeliveryMethod'),
        productName: this.$t('rfq.productName'),
        shippingDestination: this.$t('rfq.loadingDestination'),
        productNumber: this.$t('rfq.productModel'),
        customName: this.$t('order.customerName'),
        endCustomer: this.$t('supplier.endCustomer'),
        endCustomerModel: this.$t('rfq.terminalCustomerModel')
      },
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        projectId: '',
        categories: [],
        condition: '',
        isTempMaterial: false,
        materialCode: '',
        mfg: '',
        mpn: '',
        priceCategories: '',
        projectMaterialStatus: '',
        quotationStatus: '',
        sortBy: '',
        sortField: '',
        sourcing: [],
        creators: []
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'rfqCreate',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: { isCurrent: true,
          keyField: 'projectMaterialId',
          isHover: true
        },
        columns: [
          { title: '', type: 'checkbox', visible: true, width: 30, fixed: 'left' },
          // { title: this.$t('rfq.serialNo'), type: 'seq', field: 'seq', visible: true, width: 50 },todo
          {
            title: this.$t('material.materialStatus'),
            slots: {
              default: 'status'
            },

            field: 'status', visible: true, width: 100, fixed: 'left'
          },
          {
            title: this.$t('material.materialCode'),
            slots: {
              default: 'materialCode'
            },
            field: 'materialCode', visible: true, width: 100, fixed: 'left'
          },
          {
            title: this.$t('rfq.handledBy'),
            field: 'transactor', visible: true, width: 100
          },
          {
            title: this.$t('rfq.materialCodeCrossReference'),
            field: 'materialCodeControl',
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.createdBy'),

            slots: {
              default: 'creator'
            },
            field: 'creator', visible: true, width: 100
          },
          {
            title: this.$t('material.category'),
            slots: {
              default: 'categoryId'
            },
            field: 'categoryId', visible: true, width: 100
          },
          {
            title: this.$t('order.describe'),
            field: 'materialDescription',
            visible: true,
            width: 100
          },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          {
            title: this.$t('common.sourcing'),
            field: 'sourcing', visible: true, width: 100
          },
          {
            title: this.$t('rfq.estimatedAnnualConsumption'), slots: {
              default: 'estimatedAnnualUsage'
            }, field: 'estimatedAnnualUsage', visible: true, width: 100, align: 'right'
          },
          {
            title: this.$t('rfq.plannedMinimumOrderQuantity'), slots: {
              default: 'moq'
            }, field: 'moq', visible: true, width: 100, align: 'right'
          },
          { title: this.$t('rfq.requestedOrderQuantityFrom'), field: 'from', visible: true, width: 100 },
          { title: this.$t('rfq.theOrderQuantityIsRequiredToReach'), field: 'to', visible: true, width: 100 },
	        {
            title: this.$t('rfq.consumption'), field: 'dosage', slots: {

              default: 'dosage'
            }, visible: true, width: 100, align: 'right'
          },
          { title: this.$t('rfq.arrangement'), field: 'level', visible: true, width: 100, align: 'right' },
          { title: this.$t('supplier.supplierName'), field: 'name', visible: true, width: 100 },
          {
            title: this.$t('rfq.rfqNo'), field: 'quotationsNo',
            slots: {
              default: 'quotationsNo'
            }, visible: true, width: 100
          },
          {
            title: this.$t('rfq.quoteStatus'),
            field: 'quotationsStatus',
            slots: {
              default: 'quotationsStatus'
            },
            visible: true,
            width: 100
          },
          { title: this.$t('common.creationDate'), field: 'createTime', visible: true, width: 100 },
          {
            title: this.$t('rfq.requestedResponseDate'),
            field: 'dateRequested',
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.quotationResponseDate'),
            field: 'quoteReplyDate',
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.purchasersQuotationRequirements'),
            field: 'quotationRequest',
            visible: true,
            width: 100
          },
          {
            title: this.$t('common.operate'),
            field: 'operation',
            showOverflow: false,
            slots: {
              default: 'operation'
            }, fixed: 'right',
            visible: true,
            width: 35
          }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      loading: false,
      tableLoading:true,
      list: [],
      total: 0,
      quantity: [],
      quantityList: [1, 10, 50, 100, 500, 1000, 2000, 3000, 5000, 10000],
      checkList: [],
      rfqProjectRules: {
        sourcingSources: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ],
        // materialBillManagerList: [
        //   { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        // ],
        eventName: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ],
        purchaseOrg: [
          { required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }
        ],
        expirationDate: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ]
      },
      rfqProject: {
        businessModel: 0,
        customName: localStorage.getItem('customName') || '',
        endCustomer: '',
        endCustomerModel: '',
        eventName: '',
        expirationDate: '',
        id: '',
        inquiryPurpose: '',
        materialBillManagerList: [],
        sourcingSources: '',
        modifyOrderUnits: false,
        productLine: '',
        productName: '',
        productNumber: '',
        projectNo: '',
        purchaseOrg: getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0)[0].id,
        requestDeliveryMethod: '',
        requestPaymentMethod: '',
        shippingDestination: '',
        status: '',
        supplierPriceLadder: false
      },
      rfqProjectCopy: {
        businessModel: 0,
        customName: localStorage.getItem('customName') || '',
        endCustomer: '',
        endCustomerModel: '',
        eventName: '',
        expirationDate: '',
        id: '',
        inquiryPurpose: '',
        materialBillManager: '',
        sourcingSources: '',
        modifyOrderUnits: false,
        productLine: '',
        productName: '',
        productNumber: '',
        projectNo: '',
        purchaseOrg: getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0)[0].id,
        requestDeliveryMethod: '',
        requestPaymentMethod: '',
        shippingDestination: '',
        status: '',
        supplierPriceLadder: false
      },
      quotationCreate: {
        materialIds: [],
        projectId: '',
        supplierIds: [] // supplier id list who select when build quote
        ,merged:false
      },
      disPlayForm: {
        quotationRequirements: '',
        requestReplyDate: dayjs().add(3, 'day'),
        projectId: '',
        displayFieldList: [],
        displayField: ''
      },
      rfqRemarkList: [],
      rfqRemark: '',
      userName: null,
      sourcingSources: [],
      factorySources: [],
      categoryList: [],
      materialVisible: false,
      createVisible: false,
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: this.$t('rfq.batchAddMaterials'),
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/rfq/project-materials/import'
      },
      upload2: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: this.$t('临时物料转正式'),
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/rfq/project-materials/temporaryMaterialToFormal-import'
      },
      uploadVisible: false,
      materialRule: {
        priceCategory: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ],
        category: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ],
        materialDescription: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ]

      },
      supplierVisible: false,
      mfgList: [],
      mfgMpnList: [],
      selectedMaterial: [],
      supplierForm: {
        query: [],
        supplierName: '',
        checkAll: true,
        pageSize: 10,
        pageNo: 1
      },
      supplierTotal: 0,
      materialSupplierList: [],
      selectedSupplier: [],
      selectedRecord: [],
      logVisible: false,
      disabledItem: [],
      showMore: false,
      showRemark: false,
      showFile: false,
      showBuyerFile: false,
      quoteSupplier: [] // material relevance supplier list
    }
  },
  computed: {
    rfq() {
      return rfq
    },
    rfqProjectEdit() {
      return ['new', 'processing'].includes(this.rfqProject.status) || !this.rfqProject.id
    }
  },
  mounted() {
    this.rfqProject.id = this.steps?.projectId
    this.queryParams.projectId = this.steps?.projectId
    this.rfqProject.materialBillManagerList = [this.$store.getters.userId]
    this.rfqProject.sourcingSources = this.$store.getters.userId.toString()
    this.rfqProject.inquiryPurpose = 'New_Project'
    const type = this.$route.query.type
    if (type) {
      this.rfqProject.businessModel = Number(type)
    }
    this.getCategories()
    this.getSourcingSources()
    if (this.rfqProject.id) {
      this.getRfqProject()
      this.getTable()
    }

    this.getFactoryAddressByOrgId(null)
  },
  methods: {
    onchangePurchaseOrg(orgId) {
      this.getFactoryAddressByOrgId(orgId);
    },
    getFactoryAddressByOrgId(orgId) {
      if(orgId===null)
      {
        orgId=getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0)[0].id
      }
      this.factorySources.length = 0; // Clear the array
      this.rfqProject.shippingDestination=''
      listPurchaseOrgFactoryFromCache({orgIds:orgId}).then(res => {
          if(res.data.length > 0) {
              this.factorySources.push(...res.data.filter(item => item.address && item.address.trim() !== '')
                .map(item => ({
                  id: item.address,
                  name: item.address
              })))
             if(this.factorySources.length>0)
             {
               this.rfqProject.shippingDestination=this.factorySources[0].id
             }
          }

      }).catch(err => {
          console.log(err)
      })
      //customer
      console.log(getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0).find(j=>j.id===orgId))
      this.rfqProject.customName= getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0).find(j=>j.id===orgId).name

    },
    parseTime,
    getUserName(value) {
      return getDictData(DICT_TYPE.COMMON_USERS, value)?.nickname
    },
    // 寻源采购的数据源组装
    getSourcingSources() {
      this.sourcingSources.push(...getDictDatas(DICT_TYPE.RFQ_SOURCING_SOURCES, 0).map(item => ({
        id: item.value,
        name: item.label
      })))
      this.sourcingSources.push(...getDictDatas(DICT_TYPE.COMMON_USERS, 0).map(item => ({
        id: item.id + '',
        name: item.name
      })))
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    getTable() {
      getRfqMaterial(this.queryParams).then(res => {
        this.list = res.data.list
        this.total = res.data.total
        this.$nextTick(() => {
          const grid = this.$refs.rfqMaterialGrid
          this.list?.forEach((row) => {
            if (this.selectedRecord.includes(row.projectMaterialId)) {
              setTimeout(() => {
                grid.setCheckboxRow(grid.getRowById(row.projectMaterialId), true)
              }, 0)
            }
          })
        })
        this.tableLoading=false
      })
    },
    getRfqProject() {
      getRfqProject(
        {
          id: this.queryParams.projectId
        }
      ).then(res => {
        this.rfqProject = res.data
        this.rfqProjectCopy = res.data

        this.requestDeliveryMethod = this.rfqProject.requestDeliveryMethod
        this.requestPaymentMethod = this.rfqProject.requestPaymentMethod
        this.shippingDestination = this.rfqProject.shippingDestination
        this.getFileList()
        this.getRemarkList()
      })
    },
    saveProject() {
      this.$refs.rfqProject.validate((valid) => {
        if (valid) {
          saveRfqProject({ rfqRemark: this.rfqRemark, ...this.rfqProject }).then(async res => {
            const businessCode = this.$route.query.code
            const rfqId = this.rfqProject.id
            this.rfqProject = res.data
            this.queryParams.projectId = res.data.id
            if (this.rfqProject.customName) {
              localStorage.setItem('customName', this.rfqProject.customName)
            }
            // this.$store.dispatch('tagsView/updateRfqView', {
            //   path: `/rfq/processHome/${this.rfqProject.projectNo}`,
            //   fullPath: `/rfq/processHome/${this.rfqProject.projectNo}?id=${this.rfqProject.id}&projectNo=${this.rfqProject.projectNo}&code=${businessCode}`,
            //   query: {
            //     id: this.rfqProject.id,
            //     projectNo: this.rfqProject.projectNo
            //   },
            //   params: {
            //     projectId: this.rfqProject.projectNo
            //   }
            // })
            this.$message({
              message: this.$t('common.savedSuccessfully'),
              type: 'success'
            })
            this.requestDeliveryMethod = this.rfqProject.requestDeliveryMethod
            this.requestPaymentMethod = this.rfqProject.requestPaymentMethod
            this.shippingDestination = this.rfqProject.shippingDestination

            await this.saveFile()
            if (!rfqId) {
              await this.$tab.closeOpenPage(
                `/rfq/processHome/${this.rfqProject.projectNo}?id=${this.rfqProject.id}&projectNo=${this.rfqProject.projectNo}&code=${businessCode}`
              )
            }
            this.$message.success(this.$t('common.savedSuccessfully'))
          })
        }
      })
    },
    /**
     * 创建询价单 button点击后的dialog。里面出现的【被勾选的信息将展示给供应商】部分里的字段勾选点击事件
     * 全部字段都需要卡控为空时不可选
     * @param item
     * @param actualVal 实际项目内的值
     */
    configItemChange(item, actualVal) {
      console.log('actualVal', actualVal + '')
      if (!actualVal) {
        this.$message.warning(this.$t('此字段未维护，请检查项目基本信息'))
        this.disPlayForm.displayFieldList = this.disPlayForm.displayFieldList.filter(v => v !== (actualVal + ''))
      }
    },
    async saveFile() {
      console.log('saveFile')
      if (this.fileIds.length <= 0) return
      const data = await createFileRelForCreateProject({
        fileIds: this.fileIds,
        businessId: this.rfqProject.id,
        businessType: 'RFQ_PROJECT'
      })
      this.fileIds = []
      this.$message.success(this.$t('common.uploadSucceeded'))
    },
    getFileList() {
      getRfqFile({
        businessId: this.rfqProject.id,
        businessType: 'RFQ_PROJECT'
      }).then(res => {
        this.fileList = res.data.map(item => {
          return {
            name: item.fileName,
            url: item.filePath,
            id: item.id
          }
        })
      })
    },
    onRemove(file, fileList) {
      if (file.id && this.rfqProject.id) {
        delRfqFile({
          id: file.id
        }).then(res => {
          this.$message({
            message: this.$t('common.delSuccess'),
            type: 'success'
          })
          this.getFileList()
        })
      } else {
        this.fileIds.splice(this.fileIds.indexOf(file.response.data.id), 1)
      }
    },
    onSuccess(response, file, fileList) {
      if (this.rfqProject.id) {
        rfqFilePost({
          fileId: response.data.id,
          businessId: this.rfqProject.id,
          businessType: 'RFQ_PROJECT'
        }).then(res => {
          file.id = res.data
          this.$message.success(this.$t('common.uploadSucceeded'))
          this.getFileList()
        })
      } else {
        this.fileList.push({
          name: file.name,
          url: file.url,
          id: response.data.id
        })
        this.fileIds.push(response.data.id)
      }
    },
    async saveBuyerFile(businessIdList) {
      if (!this.fileBuyerList) {
        return
      }
      const list = businessIdList.map(v => {
        return {
          fileIds: this.fileBuyerList.map(v => v.id),
          businessId: v,
          businessType: 'RFQ_BUYER_QUOTATIONS'
        }
      })
      await createFileRelListForCreateProject(list)
      this.fileBuyerList = []
    },
    onBuyerRemove(file, fileList) {
      this.fileBuyerList.splice(this.fileBuyerList.findIndex(a => a.id === file.id), 1)
    },
    onBuyerSuccess(response, file, fileList) {
      this.fileTempList.push({
        name: file.name,
        url: file.url,
        id: response.data.id
      })
      if (fileList.some(a => a.status === 'ready' || a.status === 'uploading')) {
        return true
      }
      this.fileBuyerList = [...this.fileBuyerList, ...this.fileTempList]
      // 一次批量上传的临时文件集合需要在此处置空
      this.fileTempList = []
    },
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        projectId: this.queryParams.projectId,
        categories: [],
        condition: '',
        isTempMaterial: false,
        materialCode: '',
        mfg: '',
        mpn: '',
        priceCategories: '',
        projectMaterialStatus: '',
        quotationStatus: '',
        sortBy: '',
        sortField: '',
        supplier: '',
        sourcing: [],
        creators: []

      }
      this.getTable()
    },
    init() {
    },
    getRemarkList() {
      getRemarkList({
        projectId: this.rfqProject.id
      }).then(res => {
        this.rfqRemarkList = res.data
      })
    },
    delRemark(id) {
      delRfqRemark({ id }).then(res => {
        this.$message({
          message: this.$t('common.delSuccess'),
          type: 'success'
        })
        this.getRemarkList()
      })
    },
    submitRemark() {
      if (!this.rfqRemark) {
        this.$message.error(this.$t('rfq.pleaseEnterComments'))
        return
      }
      if (!this.rfqProject.id) {
        this.$message.error(this.$t('rfq.pleaseSaveTheProjectFirst'))
        return
      }
      createRfqRemark({
        projectId: this.rfqProject.id,
        remark: this.rfqRemark
      }).then(res => {
        this.$message({
          message: this.$t('common.savedSuccessfully'),
          type: 'success'
        })
        this.rfqRemark = ''
        this.getRemarkList()
      })
    },
    getSelectMaterial() {
      const selected = this.selectedRecord
      if (selected.length === 0) {
        this.$message.warning(this.$t('rfq.pleaseSelectMaterial'))
        return false
      } else {
        return selected
      }
    },
    showCreate() {
      this.$refs.rfqProject.validate((valid) => {
        if (valid) {
          saveRfqProject(this.rfqProject).then(async res => {
            this.rfqProject = res.data
            if (this.rfqProject.customName) {
              localStorage.setItem('customName', this.rfqProject.customName)
            }
            await this.saveFile()
            const selected = this.getSelectMaterial()
            if (selected) {
              const materialIds = [...new Set(selected)]
              // 判断是否已经存在询价单号，如果有则提示
              checkMaterialHasQuote({ ids: materialIds.join(',') }).then(res => {
                if (!res.data) {
                  this.$message({
                    message: this.$t('rfq.includeSuppliersForWhichRfqHasBeenCreated'),
                    type: 'warning',
                    duration: 5000
                  })
                }
                this.openGenerateQuote(materialIds)
              })
            }
          })
        }
      }
      )
    },
    openGenerateQuote(materialIds) {
      this.quotationCreate.projectId = this.rfqProject.id
      this.quotationCreate.materialIds = materialIds
      this.quotationCreate.supplierIds = []
      this.getDisplayField()
      this.getQuoteSupplier(materialIds)
      this.createVisible = true
    },
    submitCreate() {
      this.$refs.quotationCreate.validate((valid) => {
        if (valid) {
          this.disPlayForm.projectId = this.rfqProject.id
          this.disPlayForm.displayField = this.disPlayForm.displayFieldList.join(',')
          // introduce a RFQ merged strategy
          checkMergedRfq({
            projectId: this.rfqProject.id,
            supplierIds: this.quotationCreate.supplierIds
          }).then(res=>{
            if (res.data && res.data.length>0){
              const promptSupplierIds= this.quoteSupplier.filter(j=>j.quoteStatus==null).map(v=>v.supplierId);
              const supplierNames = res.data
                .filter(v => promptSupplierIds.includes(v.supplierId))
                .map(v => v.supplierName)
                .join(',');
              this.$modal.confirm(
                this.$t(`供应商：${supplierNames}有待报价的询价单尚未报价，是否把当前物料合并到最新的询价单中?`)
              ).then(() => {
                // 确认合并
                this.quotationCreate.merged = true;
                this.handleSaveAndCreate();
              }).catch(() => {
                // 取消合并
                this.quotationCreate.merged = false;
                this.handleSaveAndCreate();
              }).finally(()=>{
              });
            }else{
              this.quotationCreate.merged = false;
              this.handleSaveAndCreate();
            }
          })
        }
      })
    },
    handleSaveAndCreate() {
      this.createLoading = true
      saveRfqProjectDisplay(this.disPlayForm).then(res => {
        this.disPlayForm.id = res.data
        createQuotation(this.quotationCreate).then(pro => {
          this.$message({
            message: this.$t('common.createdSuccessfully'),
            type: 'success'
          })
          this.saveBuyerFile(pro.data.map(v => v.quoteId))
          this.createVisible = false
          this.getTable()
          this.emitter.emit('freshStep', this.queryParams.projectId)
        }).finally(() => {
          this.createLoading = false
        })
      })
    },
    getDisplayField() {
      getRfqProjectDisplay({
        projectId: this.rfqProject.id
      }).then(res => {
        if (res.data) {
          res.data.displayFieldList = res.data.displayField.split(',')
          this.disPlayForm = res.data
        }
      })
    },
    getQuoteSupplier(materialIds) {
      const material = materialIds.join(',')
      quoteSupplier({ materialIds: material }).then(res => {
        this.quoteSupplier = res.data
        this.quotationCreate.supplierIds = res.data.filter(x => !x.quoteStatus || !x.quoteStatus.includes('termination')).map(x => x.supplierId)
      })
    },
    submitMaterialSource() {
      const selected = this.getSelectMaterial()
      if (selected) {
        checkSumbitToSource({
          materialIds: selected.join(',')
        }
        ).then(res => {
          if (!res.data) {
            this.$message({
              message: this.$t('rfq.includeItemsThatDoNotNeedToBeSubmittedForSourcing'),
              type: 'warning'
            })
          }
        })

        sumbitToSource({
          materialIds: [...selected].join(',')
        }
        ).then(res => {
          if (res.data !== 0) {
            this.$message({
              message: this.$t('supplier.submittedSuccessfully'),
              type: 'success'
            })
            this.getTable()
            this.emitter.emit('freshStep', this.queryParams.projectId)
          }
        })
      }
    },
    shelveMaterial() {
      const selected = this.getSelectMaterial()
      if (selected) {
        shelveRfqMaterial({
          materialIds: [...selected].join(',')
        }
        ).then(res => {
          this.$message({
            message: this.$t('order.operationSucceeded'),
            type: 'success'
          })
          this.getTable()
          this.emitter.emit('freshStep', this.queryParams.projectId)
        })
      }
    },
    terminateMaterialById(id) {
      this.terminateMaterialData.materialIds = [id]
      this.terminateMaterialData.type = 1
      checkTerminateMaterial(this.terminateMaterialData).then(res => {
        if (res.data) {
          this.terminateMaterialOpen = true
        }
      })
    },
    terminateMaterial() {
      const selected = this.getSelectMaterial()
      if (selected) {
        this.terminateMaterialData.materialIds = [...selected]
        this.terminateMaterialData.type = 0
        checkTerminateMaterial(this.terminateMaterialData).then(res => {
          if (res.data) {
            this.terminateMaterialOpen = true
          }
        })
      }
    },
    delMaterialById(id) {
      const text = this.$t('rfq.areYouSureYouWantToDeleteTheSelectedMaterial')
      this.$modal.confirm(text).then(() => {
        deleteMaterials({
          ids: id
        }
        ).then(res => {
          if (res.data) {
            this.$message({
              message: this.$t('common.delSuccess'),
              type: 'success'
            })
            this.emitter.emit('freshStep', this.queryParams.projectId)
            this.getTable()
            this.clearSelected()
          } else {
            this.$message({
              message: this.$t('common.deleteFailed'),
              type: 'error'
            })
          }
        })
      })
    },
    delMaterial() {
      const selected = this.getSelectMaterial()
      if (selected) {
        const text = this.$t('rfq.areYouSureYouWantToDeleteTheSelectedMaterial')
        this.$modal.confirm(text).then(() => {
          deleteMaterials({
            ids: [...new Set(selected)].join(',')
          }
          ).then(res => {
            if (res.data) {
              this.$message({
                message: this.$t('common.delSuccess'),
                type: 'success'
              })
              this.emitter.emit('freshStep', this.queryParams.projectId)
              this.getTable()
              this.clearSelected()
            } else {
              this.$message({
                message: this.$t('common.deleteFailed'),
                type: 'error'
              })
            }
          })
        })
      }
    },
    updateSource() {
      let ids = []
      if (this.selectedRecord) {
        ids = [...new Set(this.selectedRecord)].join(',')
      }
      updateSource({
        ids,
        projectId: this.queryParams.projectId
      }
      ).then(res => {
        if (res.data) {
          this.$message.success(this.$t('common.updateSuccessful'))
          this.emitter.emit('freshStep', this.queryParams.projectId)
          this.getTable()
          this.clearSelected()
        } else {
          this.$message.error(this.$t('order.updateFailed'))
        }
      })
    },
    delSupplier() {
      const selected = this.getSelectMaterial()
      if (selected) {
        const text = this.$t('rfq.areYouSureYouWantToDeleteTheSelectedSupplier')
        this.$modal.confirm(text).then(() => {
          // 判断是否已经存在询价单号，如果有则提示
          const materialIds = [...new Set(selected)].join(',')
          checkMaterialHasQuote({ ids: materialIds }).then(res => {
            if (!res.data) {
              this.$message({
                message: this.$t('rfq.suppliersThatHaveCreatedRfqSheetsWillNotBeDeleted'),
                type: 'warning',
                duration: 5000
              })
            }
            this.callDeleteSupplier(materialIds)
          })
        })
      }
    },
    callDeleteSupplier(materialIds) {
      deleteMaterialsSupplier({
        ids: materialIds
      }
      ).then(res => {
        this.$message({
          message: this.$t('common.delSuccess'),
          type: 'success'
        })
        this.getTable()
      })
    },
    delSupplierById(id, quotationsNo, name) {
      const text = this.$t('rfq.areYouSureYouWantToDeleteTheSelectedSupplier')
      this.$modal.confirm(text).then(() => {
        // 判断是否已经存在询价单号，如果有则提示
        checkMaterialHasQuote({ ids: id }).then(res => {
          if (!res.data) {
            this.$message({
              message: this.$t('rfq.suppliersThatHaveCreatedRfqSheetsWillNotBeDeleted'),
              type: 'warning',
              duration: 5000
            })
          }
          this.callDeleteSupplier(id)
        })
      })
    },
    importTemplate() {
      importTemplate().then(response => {
        this.$download.excel(response, this.$t('rfq.batchCreateMaterialDetailsTemplatexls'))
      })
    },
    importTemplate2() {
      importTemplate2(this.rfqProject.id).then(response => {
        this.$download.excel(response, this.$t('临时物料转正式模板.xlsx'))
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createProjectMaterialDetail) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createProjectMaterialDetail.length
      }
      if (data.failureProjectMaterialDetail) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + Object.keys(data.failureProjectMaterialDetail).length
        for (const index in data.failureProjectMaterialDetail) {
          text += '<br />&nbsp;&nbsp;&nbsp;&nbsp; ' + this.$t('rfq.lineNumber') + index + '：' + data.failureProjectMaterialDetail[index]
        }
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getTable()
      this.clearSelected()
    },
    handleFileUploadProgress2(event, file, fileList) {
      this.upload2.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess2(response, file, fileList) {
      this.upload2.open = false
      this.upload2.isUploading = false
      this.$refs.upload2.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.projectMaterialIds) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.projectMaterialIds.length
      }
      if (data.failureProjectMaterialDetail) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + Object.keys(data.failureProjectMaterialDetail).length
        for (const index in data.failureProjectMaterialDetail) {
          text += '<br />&nbsp;&nbsp;&nbsp;&nbsp; ' + this.$t('rfq.lineNumber') + index + '：' + data.failureProjectMaterialDetail[index]
        }
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getTable()
      this.clearSelected()
    },
    submitFileForm() {
      this.$refs.upload.submit()
    },
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const fields = ['status',
        'materialCode', 'transactor', 'materialCodeControl', 'from', 'to', 'level',
        'creator', 'categoryId', 'materialDescription', 'mfg', 'mpn',
        'sourcing', 'moq', 'quantityFrom', 'quantityTo', 'dosage','estimatedAnnualUsage']

      const operationColumn = ['operationLog', 'operation']
      // 物料状态
      // const materialStatus = row['status']
      // 只有新建和待询源的时候，操作列需要合并单元格
      // if (['new', 'to_be_sourced'].includes(materialStatus)) {
      //   operationColumn.push('operation')
      // }
      const cellValue = row[column.property]
      if ((cellValue !== undefined && fields.includes(column.property))) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue && row.projectMaterialId === prevRow.projectMaterialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.projectMaterialId === nextRow.projectMaterialId && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      } else if ((['checkbox', 'seq'].includes(column.type) ||
        operationColumn.includes(column.property)) && row) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && row.projectMaterialId === prevRow.projectMaterialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.projectMaterialId === nextRow.projectMaterialId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    showSupplier() {
      const selected = this.getSelectMaterial()
      if (selected) {
        const materialIds = [...selected]
        checkMaterialCategoryIsNull({
          projectMaterialIds: materialIds.join(',')
        }).then(res => {
          if (res.data === true) {
            this.$message({
              message: this.$t('rfq.pleaseAddCategoryFirst'),
              type: 'warning'
            })
            return
          }
          checkMaterialStatus({
            materialIds: materialIds,
            checkName: 'assign_supplier'
          }).then(res => {
            if (res.data) {
              this.supplierVisible = true
              getRfqMaterialList({
                materialIds: materialIds.join(',')
              }).then(res => {
                this.supplierForm.query = res.data // 默认全选
                this.selectedMaterial = res.data
                this.supplierForm.supplierName = ''
                this.getMaterialSupplierList(true)
              })
            }
          })
        })
      }
    },
    // 选择供应商的品类/MFG/MPN全选
    handleCheckAllSupplierForm(val) {
      if (val) {
        this.supplierForm.query = this.selectedMaterial
      } else {
        this.supplierForm.query = []
      }
      this.getMaterialSupplierList(true)
    },
    // 选择供应商的品类/MFG/MPN的变更选择
    handleCheckedSupplierFormChange() {
      this.supplierForm.pageNo = 1
      this.getMaterialSupplierList(true)
    },
    // 选择供应商搜索的翻页
    handleSupplierFormCurrentChange(val) {
      this.supplierForm.pageNo = val
      this.getMaterialSupplierList(false)
    },
    getMaterialSupplierList(refreshSelected) {
      const brandNames = []
      const categoryIds = []
      let materialIds = []
      this.supplierForm.query.map(item => {
        if (item.categoryId) {
          categoryIds.push(item.categoryId)
        }
        if (item.mfg) {
          brandNames.push(item.mfg)
        }
        materialIds = [...materialIds, ...item.projectMaterialIds]
      })
      getMfgAndSupplierRel({
        brandNames,
        categoryIds,
        ...this.supplierForm
      }).then(res => {
        this.materialSupplierList = res.data.list
        this.supplierTotal = res.data.total
        if (refreshSelected) {
          this.getSelectedSupplier(materialIds)
        }
      })
    },
    checkSupplier(val, item) {
      if (val === false) {
        return
      }
      let projectMaterialIds = []
      this.supplierForm.query.map(a => {
        projectMaterialIds = [...projectMaterialIds, ...a.projectMaterialIds]
      })
      saveMaterialAndSupplierRel({
        projectMaterialIds,
        supplierId: item.id
      }).then(res => {
        this.$message({
          message: this.$t('common.savedSuccessfully'),
          type: 'success'
        })
        this.getMaterialSupplierList(true)
      })
    },
    delMaterialSupplier(item) {
      deleteMaterialsSupplierRel(item).then(res => {
        this.$message({
          message: this.$t('common.delSuccess'),
          type: 'success'
        })
        this.getMaterialSupplierList(true)
      })
    },
    getSelectedSupplier(materialIds) {
      getMaterialSupplierRelByMaterialIds({
        materialIds: [...new Set(materialIds)].join(',')
      }).then(res => {
        this.selectedSupplier = res.data
      })
    },
    showMaterialDetail(row) {
      this.materialVisible = true
      this.$refs.rfqMaterial.showMaterialDetail(row.projectMaterialId)
    },
    downloadMaterialDrawing(materialId, materialCode) {
      downloadMaterialDrawings({ projectMaterialId: materialId }).then(res => {
        this.$download.zip(res, materialCode + '.zip')
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    },
    newMaterial() {
      this.$refs.rfqMaterial.clearMaterial()
      this.materialVisible = true
    },
    // 物料列表的跨页全选框
    checkBoxAllChange({ checked }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.rfqMaterialGrid
      if (checked) {
        if (this.selectedRecord.length + this.total > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.clearCheckboxRow()
          // 当前选中的数据key数组
          const selectIds = grid.getCheckboxRecords().map(item => item.projectMaterialId)
          // 取交集
          const arr = Array.from(new Set([...this.selectedRecord].filter(x => selectIds.includes(x))))
          if (arr.length > 0) {
            arr.forEach((id) => {
              grid.setCheckboxRow(grid.getRowById(id), true)
            })
          }
        } else {
          this.checkAllData(checked)
        }
      } else {
        this.checkAllData(checked)
      }
    },
    checkAllData(checked) {
      getProjectMaterialsQuotationsLists(this.queryParams).then(res => {
        if (checked) {
          this.selectedRecord = Array.from(new Set([...this.selectedRecord, ...res.data]))
        } else {
          this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => !res.data.includes(x))))
        }
      })
    },
    // 物料列表的单个选择框
    checkBoxChange({ checked, row }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.rfqMaterialGrid
      if (checked) {
        if (this.selectedRecord.length + 1 > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.setCheckboxRow(grid.getRowById(row.projectMaterialId), false)
        } else {
          this.selectedRecord.push(row.projectMaterialId)
        }
      } else {
        this.selectedRecord.splice(this.selectedRecord.indexOf(row.projectMaterialId), 1)
      }
    },
    clearSelected() {
      const grid = this.$refs.rfqMaterialGrid
      this.selectedRecord = []
      grid.clearCheckboxRow()
    },
    downloadExcel() {
      this.$modal.confirm(this.$t('rfq.areYouSureToExportAllMaterialData')).then(() => {
        this.exportLoading = true
        return downloadMaterialQuote({ ...this.queryParams, pageNo: -1 })
      }).then(response => {
        this.$download.excel(response, this.$t('rfq.rfqMaterialxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    closeSupplier(close) {
      this.getTable()
      close()
    },
    closeSupplierEvent() {
      this.supplierVisible = false
      this.getTable()
    },
    actionterminateMaterialUp() {
      this.terminateMaterialData.materialIds = this.terminateMaterialData.type === 0 ? [...new Set(this.selectedRecord)] : this.terminateMaterialData.materialIds
      terminateRfqMaterial(
        this.terminateMaterialData
      ).then(res => {
        this.$message({
          message: this.$t('rfq.terminatedSuccessfully'),
          type: 'success'
        })
        this.terminateMaterialOpen = false
        this.getTable()
        this.clearSelected()
      })
    },
    // 终止询价单的物料
    handleTerminate() {
      terminateRecommendMaterial({
        quotationsMaterialSupplierRelId: this.returnCommentForm.quotationsMaterialSupplierRelId,
        reason: this.returnCommentForm.opinion
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.returnCommentVisible = false
        this.getTable()
        this.clearSelected()
      })
    },
    showOperationLog(obj1) {
      this.logVisible = true
      this.businessId = obj1
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.url) {
        window.open(file.url)
      }
    },
    download(url) {
      window.open(url)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

.oneLineMaterialItem {
  width: 100%;

  ::v-deep .el-form-item__content {
    width: calc(100% - 189px);
  }
}

.commonFormItem {
  width: 50%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

  ::v-deep .el-form-item__content {
    width: calc(100% - 184px);
  }
}

.commonFormItemLeft {
  width: 50%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

  ::v-deep .el-form-item__content {
    width: calc(100% - 184px);
  }
}

.commonMaterialItem {
  width: 33%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
}

::v-deep .el-form--label-top .el-form-item__label {
  padding-bottom: 0;
}

.cardStyle {
  margin-bottom: 10px;
  ::v-deep .el-card__body {
    padding: 15px 20px 0 20px;
  }
}

.rfq-create-upload {
  margin: 10px 0
}

.rfq-create {
  margin-top: 15px;
}

.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 189px);
  }
}

.materialItem {
  width: 178px;
}

.searchValue {
  width: 100%;
}

.rfqMaterial {
  .mainTab {
    margin-bottom: 15px;
  }
}

.configItem {
  width: 45%;

}

.configValue {
  position: relative;
  top: 4px;
  margin-top: 10px;
  width: 375px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.checkbox-label-container {
  display: flex; /* 开启Flexbox布局 */
  align-items: center; /* 垂直居中 */
}

.status-tag {
  margin-left: 8px; /* 添加一些左边距，使得标签不紧贴在名称后面 */
}
</style>
