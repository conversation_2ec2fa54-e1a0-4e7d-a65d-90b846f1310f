<template>
  <div class="top-right-btn">
    <el-row>
      <el-tooltip
        v-if="onlyCustom"
        :content="showSearch ? $t('common.hideSearch') : $t('common.showSearch')"
        class="item"
        effect="dark"
        placement="top"
      >
        <el-button circle icon="el-icon-search" size="mini" @click="toggleSearch()" />
      </el-tooltip>
      <el-tooltip v-if="onlyCustom" :content="$t('common.reload')" class="item" effect="dark" placement="top">
        <el-button circle icon="el-icon-refresh" size="mini" @click="refresh()" />
      </el-tooltip>
      <el-tooltip v-if="columns" :content="$t('rfq.explicitImplicitColumn')" class="item" effect="dark" placement="top">
        <el-button circle icon="el-icon-menu" size="mini" @click="showColumn()" />
      </el-tooltip>
      <el-radio-group v-model="selectedScheme" @change="selectSchemeRadio">
        <el-radio-button v-for="item in customScheme" :label="item.name" />
      </el-radio-group>
      <el-tooltip
        v-if="customColumns"
        :content="$t('rfq.explicitImplicitColumn')"
        class="item"
        effect="dark"
        placement="top"
      >
        <el-button
          circle
          icon="el-icon-menu"
          size="mini"
          style="margin-left: 10px"

          @click="settingVisible = true"
        />
      </el-tooltip>
    </el-row>
    <el-dialog :title="title" :visible.sync="open" append-to-body width="400px">
      <el-transfer
        id="transfer"
        ref="transfer"
        v-model="value"
        :data="columns"
        :titles="[$t('rfq.display'), $t('rfq.hide')]"
        @change="dataChange"
      >
        <span slot-scope="{ option }" :draggable="!option.disabled" @dragstart="drag($event,option)">{{ option.key }} - {{
          option.label
        }}</span>

      </el-transfer>
    </el-dialog>
    <el-dialog
      v-if="settingVisible"
      :before-close="setCustom"
      :title="$t('rfq.fieldsDisplayed')"
      :visible.sync="settingVisible"
      width="400px"
    >
      <div style="display: flex">
        <el-scrollbar style="height: 500px;flex: 0 0 50%">
          <draggable :list="customColumns" :set-data="setData" class="setting" group="article">
            <div
              v-for="element in customColumns"
              v-show="element.title&&!element?.fixed"
              :key="element.id"
              class="setting-item"
            >
              <div class="list-complete-item-handle">
                <el-checkbox
                  v-model="element.visible"
                >
                  {{ element.title }}
                </el-checkbox>
              </div>
            </div>
          </draggable>
        </el-scrollbar>
        <el-scrollbar style="height: 500px;flex: 0 0 50%">
          {{ $t('rfq.columnDisplayCommonScheme') }}
          <div v-for="(item,index) in customScheme" style="margin-top: 10px;margin-left: 5px">
            <el-button
              :plain="selectedScheme !== item.name"
              size="mini"
              type="primary"
              @click="selectScheme(item)"
            >{{ item.name }}
            </el-button>

            <i
              v-if="index >2"
              class="el-icon-remove-outline"
              style="float:right;font-size: 18px;cursor:pointer;margin-right: 5px;margin-top: 7px"
              @click="customScheme.splice(index,1)"
            />
            <div style="text-align: right">
              <i
                v-if="customScheme.length-1 === index"
                class="el-icon-circle-plus-outline"
                style="font-size: 18px;cursor:pointer;margin-right: 5px;margin-top: 7px"
                @click="addScheme"
              />
            </div>
          </div>

        </el-scrollbar>
      </div>

      <div slot="footer" class="dialog-footer" style="display: flex;justify-content: space-around">
        <el-button @click="confirm">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="restColumn">{{ $t('common.reset') }}</el-button>
        <el-button type="primary" @click="confirm">{{ $t('rfq.application') }}</el-button>
        <el-button type="primary" @click="saveScheme">{{ $t('rfq.saveScheme') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import draggable from 'vuedraggable'
import { clauseDetails, quotationDetails, recommendedConclusion } from '@/views/rfq/components/priceFields'

export default {
  name: 'Righttoolbar',
  components: {
    draggable
  },
  props: {
    listId: {
      type: String
    },
    type: {
      type: String
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    onlyCustom: {
      type: Boolean,
      default: true
    },
    columns: {
      type: Array
    },
    customColumns: {
      type: Array
    }
  },
  data() {
    return {
      // 显隐数据
      value: [],
      // 弹出层标题
      title: this.$t('rfq.showhide'),
      // 是否显示弹出层
      open: false,
      settingVisible: false,
      storeCustom: '',
      selectedScheme: localStorage.getItem('selectedScheme') || this.$t('rfq.quotationDetails'),
      resetCustomScheme: [{
        name: this.$t('rfq.quotationDetails'),
        list: quotationDetails
      }, {
        name: this.$t('rfq.clauseDetails'),
        list: clauseDetails
      }, {
        name: this.$t('rfq.recommendedConclusion'),
        list: recommendedConclusion
      }],
      customScheme: [{
        name: this.$t('rfq.quotationDetails'),
        list: quotationDetails
      }, {
        name: this.$t('rfq.clauseDetails'),
        list: clauseDetails
      }, {
        name: this.$t('rfq.recommendedConclusion'),
        list: recommendedConclusion
      }]
    }
  },
  mounted() {
    let customList = {}
    const temp = localStorage.getItem('customList')
    if (temp) {
      customList = JSON.parse(temp)
    } else {
      for (const customSchemeElement of this.customScheme) {
        customList[customSchemeElement.name] = customSchemeElement.list
      }
      localStorage.setItem(this.type, JSON.stringify(this.customScheme))
      localStorage.setItem('customList', JSON.stringify(customList))
    }
    // 显隐列初始默认隐藏列
    for (const item in this.columns) {
      if (this.columns[item].visible === false) {
        this.value.push(parseInt(item))
      }
    }
    if (this.listId) {
      this.setInitCustom()
      this.initCustomColumn()
    }
    if (this.getLocalCustom().length) {
      this.customScheme = this.getLocalCustom()
    }
    this.$nextTick(() => {
      this.selectSchemeRadio()
    })
  },
  methods: {
    getLocalCustom() {
      const custom = localStorage.getItem(this.type)
      if (custom) {
        return JSON.parse(custom)
      } else {
        return []
      }
    },
    setInitCustom() {
      this.storeCustom = this.customColumns.reduce(
        (a, v, index) => ({
          ...a, [v.field]: {
            visible: v.visible,
            sort: index
          }
        }), {})
      // 记录初始化状态
    },
    initCustomColumn() {
      const temp = localStorage.getItem('customList')
      try {
        const customList = JSON.parse(temp)
        // eslint-disable-next-line no-prototype-builtins
        if (customList?.hasOwnProperty(this.listId)) {
          // eslint-disable-next-line vue/no-mutating-props
          this.customColumns.sort((a, b) => {
            b.visible = customList[this.listId][b.field].visible
            a.visible = customList[this.listId][a.field].visible
            return customList[this.listId][a.field].sort - customList[this.listId][b.field].sort
          })
        }
      } catch (e) {
        console.log(e)
      }
    },
    setCustomStorage() {
      const temp = localStorage.getItem('customList')
      try {
        const customList = JSON.parse(temp) || {}
        customList[this.listId] = this.customColumns.reduce(
          (a, v, index) => ({
            ...a, [v.field]: {
              visible: v.visible,
              sort: index
            }
          }), {})
        localStorage.setItem('customList', JSON.stringify(customList))
      } catch (e) {
        console.log(e)
      }
    },
    setCustom(close) {
      // eslint-disable-next-line vue/no-mutating-props
      this.customColumns.push({})
      // eslint-disable-next-line vue/no-mutating-props
      this.customColumns.pop()
      this.setCustomStorage()

      this.settingVisible = false
      close()
    },
    setData(dataTransfer) {
      dataTransfer.setData('Text', '')
    },
    // 搜索
    toggleSearch() {
      this.$emit('update:showSearch', !this.showSearch)
    },
    // 刷新
    refresh() {
      this.$emit('queryTable')
    },
    // 右侧列表元素变化
    dataChange(data) {
      for (var item in this.columns) {
        const key = this.columns[item].key
        this.columns[item].visible = !data.includes(key)
      }
    },
    // 打开显隐列dialog
    showColumn() {
      this.open = true
    },
    // 确定生效
    confirm() {
      // eslint-disable-next-line vue/no-mutating-props
      this.customColumns.push({})
      // eslint-disable-next-line vue/no-mutating-props
      this.customColumns.pop()
      this.setCustomStorage()
      // this.saveScheme()
      this.settingVisible = false
    },
    restColumn() {
      const columnWidth = localStorage.getItem('VXE_TABLE_CUSTOM_COLUMN_WIDTH')
      const columnList = localStorage.getItem('customList')
      try {
        const width = JSON.parse(columnWidth) || {}
        width[this.listId] = {}
        localStorage.setItem('VXE_TABLE_CUSTOM_COLUMN_WIDTH', JSON.stringify(width))
        const list = JSON.parse(columnList) || {}
        list[this.listId] = this.storeCustom
        localStorage.setItem('customList', JSON.stringify(list))
      } catch (e) {
        console.log(e)
      }
      const customList = {}
      for (const customSchemeElement of this.resetCustomScheme) {
        customList[customSchemeElement.name] = customSchemeElement.list
      }
      localStorage.setItem(this.type, JSON.stringify(this.resetCustomScheme))
      localStorage.setItem('customList', JSON.stringify(customList))
      // 显隐列初始默认隐藏列
      for (const item in this.columns) {
        if (this.columns[item].visible === false) {
          this.value.push(parseInt(item))
        }
      }
      if (this.listId) {
        this.setInitCustom()
        this.initCustomColumn()
      }
      if (this.getLocalCustom().length) {
        this.customScheme = this.getLocalCustom()
      }
      this.$nextTick(() => {
        this.selectSchemeRadio()
      })
      this.customColumns.push({})
      this.customColumns.pop()
      this.settingVisible = false
    },
    addScheme() {
      this.$prompt(this.$t('rfq.pleaseEnterASchemeName'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        inputValidator: (value) => {
          if (value.length > 10) {
            return this.$t('rfq.schemeNameCannotExceedCharacters')
          }
          if (value === '') {
            return this.$t('rfq.schemeNameCannotBeEmpty')
          }
          if (this.customScheme.some(item => item.name === value)) {
            return this.$t('rfq.schemeNameAlreadyExists')
          }
        }
      }).then(({ value }) => {
        this.customScheme.push({
          name: value,
          list: []
        })
      }).catch(() => {
      })
    },
    selectSchemeRadio() {
      const item = this.customScheme.find(item => item.name === this.selectedScheme)
      if (item && Object.keys(item.list).length) {
        // eslint-disable-next-line vue/no-mutating-props
        this.customColumns.sort((a, b) => {
          b.visible = item['list'][b.field]?.visible
          a.visible = item['list'][a.field]?.visible
          return item['list'][a.field]?.sort - item['list'][b.field]?.sort
        })
      } else {
        // eslint-disable-next-line vue/no-mutating-props
        this.customColumns.sort((a, b) => {
          b.visible = this.storeCustom[b.field]?.visible
          a.visible = this.storeCustom[a.field]?.visible
          return this.storeCustom[a.field]?.sort - this.storeCustom[b.field]?.sort
        })
      }
    },
    selectScheme(item) {
      this.selectedScheme = item.name
      localStorage.setItem('selectedScheme', this.selectedScheme)
      if (Object.keys(item.list).length) {
        // eslint-disable-next-line vue/no-mutating-props
        this.customColumns.sort((a, b) => {
          b.visible = item['list'][b.field]?.visible
          a.visible = item['list'][a.field]?.visible
          return item['list'][a.field]?.sort - item['list'][b.field]?.sort
        })
      } else {
        // eslint-disable-next-line vue/no-mutating-props
        this.customColumns.sort((a, b) => {
          b.visible = this.storeCustom[b.field]?.visible
          a.visible = this.storeCustom[a.field]?.visible
          return this.storeCustom[a.field]?.sort - this.storeCustom[b.field]?.sort
        })
      }
    },
    saveScheme() {
      const index = this.customScheme.findIndex(item => item.name === this.selectedScheme)

      // if (index > 2) {
      this.customScheme[index].list = this.customColumns.reduce(
        (a, v, index) => ({
          ...a, [v.field]: {
            visible: v.visible,
            sort: index
          }
        }), {})
      localStorage.setItem(this.type, JSON.stringify(this.customScheme))
      this.$message.success(this.$t('common.savedSuccessfully'))
      // }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-transfer__button {
  border-radius: 50%;
  padding: 12px;
  display: block;
  margin-left: 0px;
}

::v-deep .el-transfer__button:first-child {
  margin-bottom: 10px;
}

.setting {
  margin-left: 30px;

  &-item {
    margin: 10px 0;
  }
}
</style>
