<template>
  <div>
    <div style="display: flex;justify-content: center;margin-bottom: 15px">
      <div>
        <el-input
          v-model="historyPriceQueryParams.searchText"
          :placeholder="$t('rfq.materialCodeSupplier')"
          style="width: 500px"
        />
        <el-button type="primary" plain @click="historyPriceQueryParams.pageNo=1;getHistoryPriceList();">{{ $t('common.search') }}</el-button>
      </div>
    </div>

    <vxe-grid
      ref="historyPriceGrid"
      :data="historyPriceList"
      :loading="loading"
      v-bind="girdOption"
    >
      <!--小数位处理-->
      <template #originalUnitPriceWithoutTax="{row}">
        <number-format :value="row.originalUnitPriceWithoutTax" />
      </template>
      <template #originalCurrency="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.originalCurrency" />
      </template>
      <!--数据字典转换处理-->
      <template #basicUnit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.basicUnit" />
      </template>
      <template #priceCategory="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_PRICE_CATEGORY" :value="row.priceCategory" />
      </template>
    </vxe-grid>
    <pagination
      v-show="total > 0"
      :limit.sync="historyPriceQueryParams.pageSize"
      :page.sync="historyPriceQueryParams.pageNo"
      :total="total"
      @pagination="getHistoryPriceList"
    />
  </div>
</template>
<script>

import { parseTime } from '@/utils/ruoyi'
import { getProjectMaterialHisPricePage } from '@/api/rfq/projectMaterialHisPrice'

export default {
  name: 'Historyprice',
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    searchText: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      historyPriceQueryParams: {
        searchText: '',
        pageNo: 1,
        pageSize: 10,
        sortBy: 'DESC',
        sortField: 'createTime'
      },
      loading: false,
      total: 0,
      historyPriceList: [],
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'historyPriceGrid',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        filterConfig: {
          remote: true
        },
        columns: [
          { title: '', type: 'radio', visible: this.isEdit, width: 50 },
          { title: this.$t('rfq.rfqNo'), field: 'quotationsNo', visible: true, width: 100 },
          { title: this.$t('material.materialCode'), field: 'materialCode', visible: true, width: 100 },
          {
            title: this.$t('material.materialDescription'),
            field: 'materialDescription',
            visible: true,
            width: 100
          },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          {
            title: this.$t('rfq.historicalSuppliers'),
            field: 'supplierName',
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.historicalPrice'),
            field: 'originalUnitPriceWithoutTax',
            visible: true,
            width: 100,
            slots: { default: 'originalUnitPriceWithoutTax' }
          },
          {
            title: this.$t('rfq.historicalCurrency'),
            field: 'originalCurrency',
            slots: { default: 'originalCurrency' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('material.basicUnit'),
            slots: { default: 'basicUnit' },
            field: 'basicUnit', visible: true, width: 100
          },
          {
            title: this.$t('rfq.priceCategory'),
            slots: { default: 'priceCategory' },
            field: 'priceCategory', visible: true, width: 100
          },
          {
            title: this.$t('rfq.approvalTime'),
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            field: 'avplMaterialsCreateTime', visible: true, width: 100
          }
        ],
        sortConfig: {
          remote: true
        }
      }
    }
  },
  mounted() {
    if (this.searchText) {
      this.historyPriceQueryParams.searchText = this.searchText
    }
    this.getHistoryPriceList()
  },
  methods: {
    // 获取列表数据
    getHistoryPriceList() {
      this.loading = true
      getProjectMaterialHisPricePage(this.historyPriceQueryParams).then(res => {
        this.historyPriceList = res.data.list
        this.total = res.data.total
        this.loading = false
      })
    }
  }
}
</script>
