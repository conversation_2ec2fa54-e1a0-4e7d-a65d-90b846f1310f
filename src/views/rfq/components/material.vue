<template>
  <el-dialog
    v-if="materialVisible"
    :before-close="closeMaterial"
    :title="$t('rfq.rfqMaterial')"
    :visible.sync="showDialog"
    class="rfqMaterial"
    width="1000px"
  >
    <el-card class="mainTab">
      <div slot="header">
        {{ $t('supplier.essentialInformation') }}
      </div>
      <el-form ref="materialInfo" :model="materialInfo" :rules="materialRule" inline label-width="120px">
        <el-form-item
          :label="$t('material.materialCode')"
          class="commonMaterialItem"
        >
          <div v-if="materialInfo.id!=null">{{ materialInfo.materialCode }}</div>
          <el-autocomplete
            v-else
            v-model="materialInfo.materialCode"
            :fetch-suggestions="querySearchAsync"
            :placeholder="$t('rfq.pleaseEnterTheContent')"
            :popper-append-to-body="false"
            popper-class="el-autocomplete-suggestion"
            class="overList"
            @change="changeMaterialCode"
            @select="handleMaterialCodeSelect"
          />
        </el-form-item>
        <el-form-item
          :label="$t('rfq.materialCodeCrossReference')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.materialCodeControl"
            :disabled="materialCodeControlEdit"
            @blur="saveMaterialCodeControl()"
          />
        </el-form-item>
        <el-form-item
          :label="$t('material.endCustomerItemNo')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.endCustomerPartNumber"
            :disabled="materialEdit||bringInfoEdit('endCustomerItemNo') || !rfqProjectEdit"
          />
        </el-form-item>
        <el-form-item
          :label="$t('rfq.priceCategory')"
          class="commonMaterialItem"
          prop="priceCategory"
        >
          <el-select
            v-model="materialInfo.priceCategory"
            :disabled="materialEdit ||bringInfoEdit('priceCategory') || !rfqProjectEdit"
            class="materialItem"
            clearable
            filterable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_PRICE_CATEGORY)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('material.purchaseType')"
          class="commonMaterialItem"
        >
          <el-select
            v-model="materialInfo.purchaseType"
            :disabled="materialEdit|| bringInfoEdit('purchaseType') || !rfqProjectEdit"
            class="materialItem"
            clearable
            filterable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_PURCHASE_TYPE,0)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('rfq.specialPurchaseType')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.specialPurchaseType"
            :disabled="materialEdit || bringInfoEdit('specialPurchaseType') || !rfqProjectEdit"
          />
        </el-form-item>

        <el-form-item
          :label="$t('material.category')"
          class="commonMaterialItem"
          prop="category"
        >
          <el-tooltip
            v-if="materialEdit || bringInfoEdit('category') || !rfqProjectEdit"
            class="item"
            effect="dark"
            placement="top"
          >
            <template #content>
              <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="materialInfo.category.at(-1)" />
            </template>
            <el-cascader
              v-model="materialInfo.category"
              :disabled="materialEdit || bringInfoEdit('category') || !rfqProjectEdit"
              :options="categoryList"
              :placeholder="$t('material.pleaseSelectCategory')"
              :props="{ value: 'id',label:'name'}"
              class="materialItem"
              clearable
              filterable
            />
          </el-tooltip>
          <el-cascader
            v-else
            v-model="materialInfo.category"
            :disabled="materialEdit || bringInfoEdit('category') || !rfqProjectEdit"
            :options="categoryList"
            :placeholder="$t('material.pleaseSelectCategory')"
            :props="{ value: 'id',label:'name'}"
            class="materialItem"
            clearable
            filterable
          />
        </el-form-item>
        <el-form-item
          :label="$t('material.materialType')"
          class="commonMaterialItem"
        >
          <el-select
            v-model="materialInfo.materialType"
            :disabled="materialEdit || bringInfoEdit('materialType')|| !rfqProjectEdit"
            class="materialItem"
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>

        </el-form-item>
        <el-form-item
          :label="$t('order.edition')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.version	"
            :disabled="materialEdit|| bringInfoEdit('version') || !rfqProjectEdit"
          />
        </el-form-item>
        <el-form-item
          :label="$t('rfq.estimatedAnnualConsumption')"
          class="commonMaterialItem"
        >
          <vxe-input
            v-model.number="materialInfo.estimatedAnnualUsage"
            :digits="2"
            :disabled="materialEdit || !rfqProjectEdit"
            :max="999999999.99"
            :min="0.01"
            type="float"
            @blur="fixZero"
          />
        </el-form-item>
        <el-form-item
          :label="$t('rfq.surfaceTreatment')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.surfaceTreatment"
            :disabled="materialEdit || !rfqProjectEdit"
          />
        </el-form-item>
        <el-form-item
          :label="$t('rfq.technologicalRequirements')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.technologicalRequirements"
            :disabled="materialEdit || !rfqProjectEdit"
          />
        </el-form-item>
        <div style="display: flex;align-items: center;justify-content: center">
          <el-checkbox-group
            v-if="!quantityEdit"
            v-model="quantity"
            :disabled="materialEdit || !rfqProjectEdit"
            size="mini"
            style="display: inline-block"
          >
            <el-checkbox-button v-for="item in quantityList" :key="item" :label="item">{{ item }}</el-checkbox-button>
          </el-checkbox-group>
          <div
            v-else
          >
            <vxe-input
              v-for="item in quantityEditList"
              v-model.number="item.val"
              size="mini"
              style="width: 75px"
              type="integer"
            />
          </div>
          <el-button
            v-if="!quantityEdit"
            style="margin-left:10px;display: inline-block;text-decoration: underline"
            type="text"
            @click="doQuantity"
          >
            {{ $t('rfq.customSettings') }}
          </el-button>
          <el-button
            v-else
            style="margin-left:10px;display: inline-block;text-decoration: underline"
            type="text"
            @click="saveQuantity"
          >
            {{ $t('common.saveSettings') }}
          </el-button>

        </div>
        <div style="display: flex;justify-content:center;margin: 15px 0">
          <div style="margin-top: 10px;margin-right: 5px"><span
            style="color: red"
          >*</span>{{ $t('rfq.requestedOrderQuantityFrom') }}
          </div>
          <div>
            <div
              v-for="(item,index) in materialInfo.moqList"
              style="margin:5px 0"
            >
              <el-form-item
                :prop="`moqList[${index}].quantityFrom`"
                :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'blur'}"
                style="margin-bottom: 5px;margin-top: 0px"
              >
                <vxe-input
                  v-model.number="item.quantityFrom"
                  :disabled="materialEdit || !rfqProjectEdit"
                  size="mini"
                  style="width: 80%"
                  type="integer"
                />
              </el-form-item>
            </div>
          </div>
          <div style="margin-top: 10px;margin-right: 5px">{{ $t('rfq.theOrderQuantityIsRequiredToReach') }}</div>
          <div>
            <div
              v-for="(item,index) in materialInfo.moqList"
              :disabled="materialEdit || !rfqProjectEdit"
              style="display: flex;margin: 5px 0"
            >
              <el-form-item style="margin-bottom: 5px;margin-top: 0px">
                <vxe-input
                  v-model="item.quantityTo"
                  :disabled="materialEdit || !rfqProjectEdit"
                  size="mini"
                  style="width: 80%"
                  type="number"
                />
              </el-form-item>
              <div style="display: flex;align-items: center;">
                <i
                  class="el-icon-circle-plus-outline"
                  style="font-size: 18px;margin: 0 5px;cursor:pointer;"
                  @click="addLadder"
                />
                <i
                  v-if="materialInfo.moqList.length > 1"
                  class="el-icon-remove-outline"
                  style="font-size: 18px;cursor:pointer;"
                  @click="delLadder(index)"
                />
              </div>
            </div>

          </div>

        </div>
        <el-form-item
          :label="$t('material.materialDescription')"
          class="oneLineMaterialItem"
          prop="materialDescription"
        >
          <el-input
            v-model="materialInfo.materialDescription"
            :disabled="materialEdit || bringInfoEdit('materialDescription')|| !rfqProjectEdit"
          />
        </el-form-item>
        <el-form-item
          :label="$t('material.materialRemarks')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.remarks	"
            :disabled="materialEdit|| bringInfoEdit('remarks')|| !rfqProjectEdit"
          />
        </el-form-item>
      </el-form>

    </el-card>
    <el-card class="mainTab">
      <div slot="header"> {{ $t('rfq.manufacturermanufacturerItemNo') }}</div>
      <el-form
        v-if="materialInfo.id!=null&&materialInfo.materialId!=null"
        inline
        label-width="120px"
        style="margin-top: 10px"
      >
        <el-form-item :label="$t('material.manufacturer')" class="commonMaterialItem">
          {{ materialInfo.mfg }}
        </el-form-item>
        <el-form-item :label="$t('material.manufacturersPartNumber')" class="commonMaterialItem">
          {{ materialInfo.mpn }}
        </el-form-item>
      </el-form>
      <div v-else-if="materialInfo.materialId!=null" style="margin-left: 120px">
        <el-checkbox-group v-model="materialInfo.mfgMpnList">
          <el-checkbox v-for="item in mfgMpnList" :label="item">
            {{ item.mfg }} {{ item.mpn }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <el-form
        v-else-if="materialInfo.id!=null && materialInfo.materialId==null"
        inline
        label-width="120px"
        style="margin-top: 10px"
      >
        <el-form-item :label="$t('material.manufacturer')" class="commonMaterialItem">
          {{ materialInfo.mfg }}
        </el-form-item>
        <el-form-item :label="$t('material.manufacturersPartNumber')" class="commonMaterialItem">
          {{ materialInfo.mpn }}
        </el-form-item>
      </el-form>
      <el-form v-else inline label-width="120px" style="margin-top: 10px">
        <el-form-item :label="$t('material.manufacturer')" class="commonMaterialItem">
          <el-input
            v-model="materialInfo.mfg"
            :disabled="materialEdit || !rfqProjectEdit"
          />
        </el-form-item>
        <el-form-item :label="$t('material.manufacturersPartNumber')" class="commonMaterialItem">
          <el-input
            v-model="materialInfo.mpn"
            :disabled="materialEdit || !rfqProjectEdit"
          />
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="mainTab">
      <div slot="header">{{ $t('order.drawing') }}</div>
      <el-upload
        :action="uploadUrl"
        :disabled="materialEdit || !rfqProjectEdit"
        :headers="getBaseHeader()"
        :on-preview="onPreview"
        :on-success="(response, file, fileList)=>onDrawingSuccess(response, file,)"
        :show-file-list="false"
        class="rfq-create-upload"
        multiple
      >
        <el-button
          v-if="rfqProjectEdit"
          :disabled="materialEdit || !rfqProjectEdit "
          plain
          size="small"
          type="primary"
        >{{ $t('rfq.uploadDrawings') }}
        </el-button>
      </el-upload>

      <el-table :data="materialInfo.fileInfos">
        <el-table-column :label="$t('rfq.fileName')" prop="fileName">
          <template slot-scope="scope">
            <el-button
              style="text-decoration: underline"
              type="text"
              @click="download(scope.row.url)"
            >
              {{ scope.row.fileName }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.drawingRemarks')" prop="remarks" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip v-if="materialEdit || !rfqProjectEdit" :content="scope.row.remarks" effect="dark" placement="top" popper-class="tooltipClass">
              <el-input v-model="scope.row.remarks" :disabled="materialEdit || !rfqProjectEdit" />
            </el-tooltip>
            <el-input v-if="!(materialEdit || !rfqProjectEdit)" v-model="scope.row.remarks" :disabled="materialEdit || !rfqProjectEdit" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.operate')">
          <template #default="scope">
            <!--              <el-button type="text">{{ $t('rfq.addTo') }}</el-button>-->
            <el-button
              :disabled="materialEdit || !rfqProjectEdit"
              type="text"
              @click="materialInfo.fileInfos.splice(scope.$index,1)"
            >{{ $t('common.del') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-card
      class="mainTab"
      style="margin-bottom: 60px"
    >
      <div slot="header">{{ $t('rfq.detailedInformation') }}</div>
      <el-form inline label-width="120px">
        <el-form-item
          :label="$t('rfq.plannedLeadTime')"
          class="commonMaterialItem"
        >
          <el-input
            v-model.number="materialInfo.deliveryTime"
            :disabled="materialEdit || bringInfoEdit('deliveryTime')|| !rfqProjectEdit"
            type="number"
          />
        </el-form-item>
        <el-form-item
          :label="$t('rfq.plannedMinimumPackagingQuantity')"
          class="commonMaterialItem"
        >
          <el-input
            v-model.number="materialInfo.mpq"
            :disabled="materialEdit || bringInfoEdit('mpq')|| !rfqProjectEdit"
            type="number"
          />
        </el-form-item>
        <el-form-item
          :label="$t('rfq.plannedMinimumOrderQuantity')"
          class="commonMaterialItem"
        >
          <el-input
            v-model.number="materialInfo.moq"
            :disabled="materialEdit || bringInfoEdit('moq')|| !rfqProjectEdit"
            type="number"
          />
        </el-form-item>
        <el-form-item
          :label="$t('material.selfMadeOrPurchased')"
          class="commonMaterialItem"
        >
          <el-select
            v-model="materialInfo.homemadeSourcing"
            :disabled="materialEdit ||bringInfoEdit('homemadeSourcing') || !rfqProjectEdit"
            class="materialItem"
            filterable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_MAKE_OR_BUY)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('material.basicUnit')"
          class="commonMaterialItem"
        >
          <el-select
            v-model="materialInfo.basicUnit"
            :disabled="materialEdit || bringInfoEdit('basicUnit')|| !rfqProjectEdit"
            class="materialItem"
            filterable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_UOM)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('material.purchasingUnit')"
          class="commonMaterialItem"
        >
          <el-select
            v-model="materialInfo.purchasingUnit"
            :disabled="materialEdit|| bringInfoEdit('purchasingUnit') || !rfqProjectEdit"
            class="materialItem"
            filterable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_UOM)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>

        </el-form-item>
        <el-form-item
          :label="$t('material.brand')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.brand"
            :disabled="materialEdit|| bringInfoEdit('brand') || !rfqProjectEdit"
          />
        </el-form-item>
        <el-form-item
          :label="$t('material.model')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.model"
            :disabled="materialEdit||bringInfoEdit('model') || !rfqProjectEdit"
          />
        </el-form-item>
        <el-form-item
          :label="$t('material.colour')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.color"
            :disabled="materialEdit || bringInfoEdit('color')|| !rfqProjectEdit"
          />
        </el-form-item>
        <el-form-item
          :label="$t('rfq.consumption')"
          class="commonMaterialItem"
        >
          <el-input
            v-model.number="materialInfo.dosage"
            :disabled="materialEdit || !rfqProjectEdit"
            type="number"
          />
        </el-form-item>
        <el-form-item
          :label="$t('rfq.arrangement')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.level"
            :disabled="materialEdit || !rfqProjectEdit"
          />
        </el-form-item>
        <el-form-item
          :label="$t('rfq.materialLevel')"
          class="commonMaterialItem"
        >
          <el-input
            v-model.number="materialInfo.materialHierarchy"
            :disabled="materialEdit || !rfqProjectEdit"
            type="number"
          />
        </el-form-item>
<!--        订单批次量-->
        <el-form-item
          :label="$t('订单批次量')"
          class="commonMaterialItem"
        >
          <el-input
            v-model.number="materialInfo.orderLotSize"
            :disabled="materialEdit || !rfqProjectEdit"
            type="number"
          />
        </el-form-item>
<!--        订单频率-->
        <el-form-item
          :label="$t('订单频率')"
          class="commonMaterialItem"
        >
          <el-select
            v-model="materialInfo.orderFrequency"
            :disabled="materialEdit|| bringInfoEdit('purchasingUnit') || !rfqProjectEdit"
            class="materialItem"
            filterable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_ORDER_FREQUENCY)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
<!--        材料牌号-->
        <el-form-item
          :label="$t('材料牌号')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.materialGrade"
            :disabled="materialEdit || !rfqProjectEdit"
          />
        </el-form-item>
<!--        原材料名称-->
        <el-form-item
          :label="$t('原材料名称')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.materialName"
            :disabled="materialEdit || !rfqProjectEdit"
          />
        </el-form-item>

        <el-form-item
          :label="$t('material.specificationAndModel')"
          class="oneLineMaterialItem"
        >
          <el-input
            v-model="materialInfo.specifications"
            :disabled="materialEdit || bringInfoEdit('specifications')|| !rfqProjectEdit"
          />
        </el-form-item>
      </el-form>

    </el-card>
    <el-card
      style="max-width: calc(100% - 87px);
       width: 943px;
    position:fixed;bottom: calc(84px - 6%)"
    >
      <div style="text-align: right">
        <el-button @click="closeDialog">{{ $t('common.cancel') }}</el-button>
        <el-button
          v-if="rfqProjectEdit&&!materialEdit"
          v-has-permi="['rfq:projects:save']"
          type="primary"
          @click="submitMaterial"
        >{{ $t('order.determine') }}
        </el-button>

      </div>
    </el-card>

  </el-dialog>
</template>

<script>
import { createMaterial, getMasterMaterialByMaterialId, getMaterialDetail, saveMaterialCodeControl } from '@/api/rfq/home'
import { getTreeMap } from '@/utils'
import { DICT_TYPE, getDictDataLabel, getDictDatas } from '@/utils/dict'
import { getBaseHeader } from '@/utils/request'
import localforage from 'localforage'
import { getMaterialInfo } from '@/api/material/main'

export default {
  props: ['materialVisible', 'rfqProjectStatus', 'rfqProjectId'],
  data() {
    return {
      disabledItem: [],
      materialInfo: {
        id: null,
        basicUnit: '',
        brand: '',
        categoryId: 0,
        category: [],
        color: '',
        currentPurchasePrice: 0,
        deliveryTime: 0,
        dosage: '',
        endCustomerPartNumber: '',
        estimatedAnnualUsage: 1,
        homemadeSourcing: '',
        level: '',
        materialCode: '',
        materialCodeControl: '',
        materialDescription: '',
        materialHierarchy: 0,
        materialName:null,    //原材料名称
        materialGrade:null,   //材料牌号
        orderLotSize:null,  //订单批次量
        materialId: null,
        materialType: '',
        mfg: '',
        fileInfos: [],
        mfgMpnList: [
          // {
          //   id: 0,
          //   mfg: '',
          //   mpn: '',
          //   projectId: 0
          // }
        ],
        model: '',
        moq: 0,
        moqList: [
          {
            quantityFrom: 1,
            quantityTo: null
          }
        ],
        mpn: '',
        mpq: 0,
        priceCategory: '',
        projectId: 0,
        purchaseType: '',
        purchasingUnit: '',
        orderFrequency:null,  //订单频率
        remarks: '',
        sourcing: 0,
        specialPurchaseType: '',
        specifications: '',
        surfaceTreatment: '',
        targetPrice: 0,
        status: '',
        technologicalRequirements: '',
        temporaryMaterial: true,
        transactor: 0,
        version: ''
      },
      materialRule: {
        priceCategory: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'change' }
        ],
        category: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'change' }
        ],
        materialDescription: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'change' }
        ]

      },
      categoryList: [],
      quantity: [],
      quantityList: [1, 10, 50, 100, 500, 1000, 2000, 3000, 5000, 10000],
      quantityEdit: false,
      mfgMpnList: [],
      getBaseHeader,
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      quantityEditList: []
    }
  },
  computed: {
    rfqProjectEdit() {
      return ['new', 'processing'].includes(this.rfqProjectStatus) || !this.rfqProjectId
    },
    // 当是正式物料或者物料状态不在以下状态中，物料编码对照不可编辑
    materialCodeControlEdit() {
      return this.materialInfo.materialId || (this.materialInfo.id && !['new', 'to_be_sourced', 'shelve', 'to_quote', 'no_quote', 'to_be_recommended'].includes(this.materialInfo.status))
    },
    materialEdit() {
      return this.materialInfo.id && !['new', 'to_be_sourced'].includes(this.materialInfo.status)
    },
    // 物料代入数据不为空则禁用
    bringInfoEdit() {
      return (item) => {
        return this.materialInfo.materialId && this.disabledItem.includes(item)
      }
    },
    showDialog: {
      get() {
        return this.materialVisible
      },
      set(val) {
        this.$emit('update:materialVisible', false)
      }
    }
  },
  watch: {
    quantity(newValue, oldValue) {
      let xor = []
      if (newValue.length > oldValue.length) {
        xor = newValue.filter(item => !oldValue.includes(item))
        this.materialInfo.moqList.push({
          quantityFrom: xor[0],
          quantityTo: null
        })
        this.materialInfo.moqList.sort((a, b) => a.quantityFrom - b.quantityFrom)
      } else {
        xor = oldValue.filter(item => !newValue.includes(item))
        this.materialInfo.moqList = this.materialInfo.moqList.filter(item => item.quantityFrom !== xor[0])
        this.materialInfo.moqList.sort((a, b) => a.quantityFrom - b.quantityFrom)
      }
      if (this.materialInfo.moqList.length === 0) {
        this.materialInfo.moqList.push({
          quantityFrom: null,
          quantityTo: null
        })
      }
    }
  },
  mounted() {
    this.getCategories()
    this.initQuantity()
  },
  methods: {
    getDictDataLabel,
    initQuantity() {
      localforage.getItem('rfqQuantity', (err, val) => {
        if (val) {
          this.quantityList = val.map(item => item.val)
        }
      })
    },
    closeDialog() {
      this.$emit('update:materialVisible', false)
    },
    closeMaterial(close) {
      this.$emit('getTable')
      this.$emit('update:materialVisible', false)
      close()
    },
    clearMaterial() {
      this.quantity.length = 0
      this.quantity = [1]
      this.mfgMpnList = []
      this.materialInfo = {
        basicUnit: '',
        brand: '',
        categoryId: 0,
        category: [],
        color: '',
        currentPurchasePrice: 0,
        deliveryTime: 0,
        dosage: '',
        endCustomerPartNumber: '',
        estimatedAnnualUsage: 1,
        homemadeSourcing: '',
        level: '',
        materialCode: '',
        materialCodeControl: '',
        materialDescription: '',
        materialHierarchy: 0,
        materialId: null,
        materialType: '',
        mfg: '',
        fileInfos: [],
        mfgMpnList: [
          // {
          //   id: 0,
          //   mfg: '',
          //   mpn: '',
          //   projectId: 0
          // }
        ],
        model: '',
        moq: 0,
        moqList: [
          // {
          //   quantityFrom: 1,
          //   quantityTo: null
          // }
        ],
        mpn: '',
        mpq: 0,
        priceCategory: 'standard_price',
        projectId: this.rfqProjectId,
        purchaseType: '',
        purchasingUnit: '',
        remarks: '',
        sourcing: 0,
        specialPurchaseType: '',
        specifications: '',
        surfaceTreatment: '',
        targetPrice: 0,
        technologicalRequirements: '',
        temporaryMaterial: true,
        transactor: 0,
        version: ''
      }
    },
    querySearchAsync(queryString, cb) {
      if (queryString.length < 2) {
        return
      }
      getMaterialInfo({
        materialCode: queryString
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item.materialCode + '-' + item.materialDescription,
            ...item
          }
        }))
      })
    },
    changeMaterialCode() {
      this.materialInfo.materialId = null
    },
    handleMaterialCodeSelect(item) {
      this.materialInfo.materialCode = item.materialCode
      this.materialInfo.materialDescription = item.materialDescription
      // 根据id去物料主数据的完整信息
      getMasterMaterialByMaterialId({
        materialCodeId: item.id
      }).then(res => {
        this.disabledItem.length = 0
        const { id, ...cloneItem } = res.data.material
        const { id: noGeneralMaterialId, ...clonenoGeneralMaterial } = res.data.noGeneralMaterial
        Object.assign(this.materialInfo, cloneItem, {
          category: getTreeMap(res.data.material.categoryId, this.categoryList)
        }, clonenoGeneralMaterial)
        for (const clonenoGeneralMaterialKey in clonenoGeneralMaterial) {
          if (clonenoGeneralMaterial[clonenoGeneralMaterialKey]) {
            this.disabledItem.push(clonenoGeneralMaterialKey)
          }
        }
        for (const cloneItemKey in cloneItem) {
          if (cloneItemKey === 'materialType') {
            continue
          }
          if (cloneItem[cloneItemKey]) {
            this.disabledItem.push(cloneItemKey)
          }
        }
        if (cloneItem.materialType) {
          this.disabledItem.push('homemadeSourcing')
        }
        this.disabledItem.push('materialCodeControl')
        if (cloneItem.categoryId) {
          this.disabledItem.push('category')
        }
        this.mfgMpnList = res.data.mfgRel
        // 物料主数据的id
        this.materialInfo.materialId = res.data.material.id
        // 当是物料主数据的时候，清空物料编码对照
        this.materialInfo.materialCodeControl = ''
        // 采购自制字段因为物料主数据有2个同名属性，所以需要手动指定
        this.materialInfo.homemadeSourcing = res.data.material.materialType
      })
    },
    addLadder() {
      this.materialInfo.moqList.push({
        quantityFrom: null,
        quantityTo: null
      })
    },
    delLadder(index) {
      this.materialInfo.moqList.splice(index, 1)
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.url) {
        window.open(file.url)
      }
    },
    fixZero() {
      if (this.materialInfo.estimatedAnnualUsage === 0) {
        this.materialInfo.estimatedAnnualUsage = 0.01
      }
    },
    download(url) {
      window.open(url)
    },
    onDrawingSuccess(response) {
      const {
        id: fileId,
        name: fileName,
        ...file
      } = response.data
      this.materialInfo.fileInfos.push({ fileId, fileName, ...file, remarks: '' })
    },
    submitMaterial() {
      if (!this.materialInfo.moqList.every((element, index, arr) => {
        if (index === 0) {
          return true
        }
        return element.quantityFrom >= arr[index - 1].quantityFrom
      })) {
        this.$message.error(this.$t('rfq.orderQuantityMustBeInPositiveOrder'))
        return
      }
      this.$refs.materialInfo.validate((valid) => {
        if (valid) {
          this.materialInfo.categoryId = this.materialInfo.category.at(-1)
          createMaterial(this.materialInfo).then(res => {
            this.$message({
              message: this.$t('common.createdSuccessfully'),
              type: 'success'
            })
            this.$emit('getTable')
            this.clearMaterial()
            this.$emit('update:materialVisible', false)
            this.emitter.emit('freshStep', this.rfqProjectId)
          })
        }
      })
    },
    saveMaterialCodeControl() {
      if (this.materialInfo.materialCodeControl && this.materialInfo.id) {
        saveMaterialCodeControl({
          id: this.materialInfo.id,
          materialCodeControl: this.materialInfo.materialCodeControl,
          projectId: this.materialInfo.projectId
        }).then(res => {
          if (res.data) {
            this.$message.success(this.$t('common.updateSuccessful'))
          }
        })
      }
    },
    showMaterialDetail(projectMaterialId) {
      getMaterialDetail({
        id: projectMaterialId
      }).then(res => {
        res.data.category = getTreeMap(res.data.categoryId, this.categoryList)
        this.mfgMpnList = res.data.mfgMpnList
        this.materialInfo = res.data
        if (!this.materialInfo.temporaryMaterial) {
          //this.onloadDisabled({ ...this.materialInfo, id: this.materialInfo.materialId })
        }
      })
    },
    onloadDisabled(item) {
      this.materialInfo.materialCode = item.materialCode
      this.materialInfo.materialDescription = item.materialDescription
      // 根据id去物料主数据的完整信息
      getMasterMaterialByMaterialId({
        materialCodeId: item.id
      }).then(res => {
        this.disabledItem.length = 0
        const { id, ...cloneItem } = res.data.material
        const { id: noGeneralMaterialId, ...clonenoGeneralMaterial } = res.data.noGeneralMaterial
        for (const clonenoGeneralMaterialKey in clonenoGeneralMaterial) {
          if (clonenoGeneralMaterial[clonenoGeneralMaterialKey]) {
            this.disabledItem.push(clonenoGeneralMaterialKey)
          }
        }
        for (const cloneItemKey in cloneItem) {
          if (cloneItemKey === 'materialType') {
            continue
          }
          if (cloneItem[cloneItemKey]) {
            this.disabledItem.push(cloneItemKey)
          }
        }
        if (cloneItem.materialType) {
          this.disabledItem.push('homemadeSourcing')
        }
        this.disabledItem.push('materialCodeControl')
        if (cloneItem.categoryId) {
          this.disabledItem.push('category')
        }
      })
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    doQuantity() {
      this.quantityEdit = true
      this.quantityEditList = this.quantityList.map(val => {
        return {
          val
        }
      })
    },
    saveQuantity() {
      localforage.setItem('rfqQuantity', this.quantityEditList)
      this.quantityList = this.quantityEditList.map(item => item.val)
      this.quantityEdit = false
    }
  }
}
</script>
<style lang="scss">
.tooltipClass{
  max-width: 80% !important;
}
</style>
<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

.oneLineMaterialItem {
  width: 100%;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.commonFormItem {
  width: 33.3%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

  ::v-deep .el-form-item__content {
    width: calc(100% - 170px);
  }
}

.commonFormItemLeft {
  width: 33.3%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

  ::v-deep .el-form-item__content {
    width: calc(100% - 115px);
  }
}

.commonMaterialItem {
  width: 49%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
}

::v-deep .el-form--label-top .el-form-item__label {
  padding-bottom: 0;
}

.rfq-create-upload {
  margin: 10px 0
}

.rfq-create {
  margin-top: 15px;
}

.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.materialItem {
  width: 178px;
}

.searchValue {
  width: 100%;
}

.rfqMaterial {
  .mainTab {
    margin-bottom: 15px;
  }
}

.configItem {
  margin: 5px 0;
  width: 50%;

}
.configValue{
  position: relative;
  top: 4px;
  margin-top: 10px;
  width: 375px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.overList{
  ::v-deep .el-autocomplete-suggestion{
    width: auto !important;
    min-width: 180px;
    max-width: 600px !important;
  }
}
</style>
