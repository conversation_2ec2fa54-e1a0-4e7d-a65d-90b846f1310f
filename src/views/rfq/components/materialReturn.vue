<template>
  <div>
    <common-card
      :title="$t('rfq.pleaseSelectTheSupplierThatNeedsToBeReturned')"
    >
      <el-checkbox-group v-model="returnCommentForm.suppliers" style="max-height: 150px;overflow: auto;">
        <el-checkbox v-for="item in selectSupplier" :key="item.supplierId" style="display:block;margin-bottom: 10px" :label="item.supplierId"> {{ item.supplierName }}</el-checkbox>
      </el-checkbox-group>
    </common-card>
    <common-card
      :title="$t('rfq.returnComments')"
    >
      <el-input
        v-model="returnCommentForm.opinion"
        :placeholder="$t('rfq.pleaseEnterTheReturnComments')"
        :rows="5"
        type="textarea"
      />
    </common-card>

    <div style="text-align: center">
      <el-button @click="close">{{ $t('common.cancel') }}</el-button>
      <el-button type="primary" :loading="returnLoading" @click="submitReturnForm">{{ $t('order.determine') }}</el-button>
    </div>
  </div>
</template>
<script>

import { batchReturnByQuoteMaterialSupplier, getReturnSuppliers } from '@/api/rfq/quoationRecommend'

export default {
  name: 'Materialreturn',
  props: ['materialIds'],
  data() {
    return {
      returnCommentForm: {
        opinion: '',
        suppliers: []
      },
      returnLoading: false,
      selectSupplier: []
    }
  },
  mounted() {
    this.getSupplier()
  },
  methods: {
    getSupplier() {
      this.returnCommentForm.opinion = ''
      this.returnCommentForm.suppliers = []
      getReturnSuppliers({
        materialIds: this.materialIds
      }).then(res => {
        this.selectSupplier = res.data
        this.selectSupplier = this.selectSupplier.map(j => {
          return {
            ...j,
            checked: false
          }
        })
      })
    },
    close() {
      this.$emit('closeMaterialReturn', false)
      // this.$parent.closeMaterialReturn(false)
    },
    submitReturnForm() {
      if (this.returnCommentForm.suppliers.length <= 0) {
        this.$message.error(this.$t('rfq.pleaseSelectTheSupplierToReturnTo'))
        return
      }
      this.returnLoading = true
      const quoteMaterialSupplierRelIds = this.selectSupplier.filter(item => this.returnCommentForm.suppliers.includes(item.supplierId)).map(item => item.quoteMaterialSupplierRelIds)
      batchReturnByQuoteMaterialSupplier({
        quoteMaterialSupplierIds: quoteMaterialSupplierRelIds.join(),
        reason: this.returnCommentForm.opinion
      }).then(res => {
        this.returnLoading = false
        if (res.data) {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.$emit('closeMaterialReturn', true)
        } else {
          this.$message.error(this.$t('auth.operationFailed'))
        }
      }).catch(() => {
        this.returnLoading = false
      })
    }
  }
}
</script>
