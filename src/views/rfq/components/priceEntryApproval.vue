<template>
  <div>
    <common-card
      :title="$t('rfq.approvalFormList')"
    >
      <el-table
        ref="rfqPriceApproval"
        border
        current-row-key="id"
        highlight-current-row
        :data="approvalList"
        @row-click="changeApproval"
      >
        <el-table-column :label="$t('common.creationDate')" prop="createTime">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.approvalNo')" prop="approvalNo">
          <template #default="scope">
            {{ scope.row.approvalNo }}
            <!--            <el-button-->
            <!--              style="text-decoration: underline"-->
            <!--              type="text"-->
            <!--              @click="$router.push(`/rfq/processHome/${scope.row.approvalNo}?id=${$route.query.id}&approvalId=${scope.row.id}&projectNo=${scope.row.approvalNo}`)"-->
            <!--            >{{ scope.row.approvalNo }}-->
            <!--            </el-button>-->
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.approvalFormName')" prop="approvalName" />
        <el-table-column :label="$t('rfq.createdBy')" prop="creator">
          <template slot-scope="scope">
            <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="scope.row.creator" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.quoteTemplate')" prop="templateName" />
        <el-table-column :label="$t('rfq.material')" prop="materialQuantity" />
        <el-table-column :label="$t('rfq.approvalStatus')" prop="status">
          <template slot-scope="scope">
            <dict-tag :type="DICT_TYPE.RFQ_APPROVAL_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.enclosure')" prop="attachmentQuantity" />
        <el-table-column :label="$t('rfq.sign')" prop="flag">
          <template slot-scope="scope">
            <dict-tag :type="DICT_TYPE.RFQ_APPROVAL_FLAG" :value="scope.row.flag" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.operate')">
          <template #default="scope">
            <el-button
              v-if="scope.row.status==='pending_approval' && !scope.row.urgent"
              v-has-permi="['rfq:quotations-recommend:urgent']"
              type="text"
              @click.stop="expedited(scope.row)"
            >{{ $t('rfq.urgentApproval') }}
            </el-button>
            <el-button
              v-if="scope.row.status==='pending_approval'"
              v-has-permi="['rfq:price-approval:cancel']"
              type="text"
              @click.stop="revoke(scope.row)"
            >{{ $t('rfq.revoke') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

    </common-card>
    <el-card>
      <div slot="header">{{ $t('rfq.reportAndAttachments') }}</div>
      <el-descriptions :colon="false" direction="vertical" label-class-name="labelTitle">
        <el-descriptions-item
          :label="$t('rfq.recommendedRemarks')"
          :span="3"
        >
          {{ recommendRemark }}
        </el-descriptions-item>
        <el-descriptions-item
          :content-style="{'vertical-align': 'top'}"
          :label="$t('rfq.uploadAttachmentsForProcurement')"
        >
          <div v-for="item in rfqFileList.purchaseFile">
            <el-button style="text-decoration: underline" type="text" @click="openFile(item.filePath)">{{
              item.fileName
            }}
            </el-button>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <el-card style="margin-top: 15px">
      <div slot="header">{{ $t('rfq.approveBom') }}
      </div>
      <div>
        <div style="display: flex;justify-content: center;margin-left: 20px">
          <el-input
            v-model="queryParams.filter"
            :placeholder="$t('rfq.pleaseEnterMaterialCodeSupplierAbbreviationMpnmfg')"
            clearable
            style="width: 500px"
            @keyup.enter.native="queryParams.pageNo = 1;getTable();"
          />
          <el-button type="primary" plain @click="queryParams.pageNo = 1;getTable();">{{ $t('common.search') }}</el-button>
          <el-button @click="resetQuery">{{ $t('common.reset') }}</el-button>
        </div>

        <div style="display: flex;justify-content: space-between;margin-top: 20px">
          <div style="display: flex;align-items: center">
            <el-radio-group v-model="queryParams.projectMaterialStatus" style="margin-right: 10px" @change="getTable">
              <el-radio-button label="">{{ $t('order.whole') }}</el-radio-button>
              <el-radio-button
                v-for="dict in getDictDatas(DICT_TYPE.RFQ_APPROVAL_MATERIAL_STATUS,0)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio-button>
            </el-radio-group>

          </div>
          <el-col :span="14">
            <customFields
              :custom-columns.sync="girdOption.columns"
              :list-id="girdOption.id"
              :only-custom="false"
              type="rfqApprovalCustom"
              @queryTable="getTable"
            />
            <div style="float: right;margin-right: 10px">
              <el-button v-has-permi="['rfq:price-approval:export']" type="primary" @click="exportData">{{
                $t('order.download')
              }}
              </el-button>
            </div>
          </el-col>
        </div>

        <vxe-grid
          ref="approvalGrid"
          :data="list"
          :loading="loading"
          :span-method="mergeRowMethod"
          style="margin-top: 10px"
          v-bind="girdOption"
          @checkbox-change="checkBoxChange"
          @checkbox-all="checkBoxAllChange"
        >
          <template #requiredField="{column}">
            <span class="requiredTitle">
              {{ column.title }}
            </span>
          </template>
          <template #checkbox="{row}">
            <el-checkbox v-model="row.selected" @change="checkBoxChange(row)" />
          </template>
          <template v-for="item in dictTemplateFields" #[item.field]="{row,$rowIndex}">
            <span v-if="item.field === 'approvalNumbers'">
              <el-button
                v-if="row.approvalNumbers === 0"
                disabled
                size="mini"
                type="text"
              >{{ row.approvalNumbers }}
              </el-button>
              <el-button
                v-if="row.approvalNumbers > 0"
                size="mini"
                style="text-decoration: underline"
                type="text"
                @click="showOperationLog(row.projectMaterialId,'RFQ_PRICE_APPROVAL')"
              >{{ row.approvalNumbers }}
              </el-button>
            </span>
            <span v-if="item.field === 'materialCode'">
              <i
                v-if="row.materialDrawing"
                class="el-icon-picture-outline"
                style="font-size: 16px;margin-right: 3px;cursor: pointer"
                @click="downloadMaterialDrawing(row.projectMaterialId,row.materialCode)"
              />
              <copy-button
                style="text-decoration: underline"
                type="text"
                @click="showMaterialDetail(row.projectMaterialId)"
              >
                {{ row.materialCode }}</copy-button>
            </span>
            <dict-tag v-if="!item.modify" :type="item.type" :value="row[item.field]" />
            <show-or-edit
              v-else-if="item.fieldType === 'Date'"
              :disabled="disabledFields(item.field)"
              :value="row[item.field]"
              type="Date"
            >
              <el-date-picker
                v-model="row[item.field]"
                :placeholder="$t('order.selectDate')"
                style="width: 140px"
                type="date"
              />
            </show-or-edit>

            <show-or-edit
              v-else-if="item.modify&&item.type&&item.fieldType==='String'"
              :disabled="disabledFields(item.field)"
              :dict="item.type"
              :value="row[item.field]"
            >
              <el-select
                v-model="row[item.field]"
              >
                <el-option
                  v-for="dict in getDictDatas(item.type)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>

            <show-or-edit
              v-else-if="item.modify&&item.type"
              :disabled="disabledFields(item.field)"
              :dict="item.type"
              :value="row[item.field]"
            >

              <el-select
                v-model="row[item.field]"
                @change="changeTaxRate(item.field,row[item.field])"
              >
                <el-option
                  v-for="dict in getDictDatas(item.type)"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>

            </show-or-edit>

            <show-or-edit
              v-else-if="item.fieldType === 'String'"
              :disabled="disabledFields(item.field)"
              :value="row[item.field]"
            >
              <el-input
                v-model="row[item.field]"
                style="width: 80%"
                type="text"
              />
            </show-or-edit>
            <show-or-edit
              v-else-if="item.fieldType === 'Integer'"
              :disabled="disabledFields(item.field)"
              :value="row[item.field]"

              type="Number"
            >
              <vxe-input
                v-model="row[item.field]"
                min="0"
                style="width: 80%;display: inline-block"
                type="integer"
                @change="changeField(item.field,row[item.field])"
              />

            </show-or-edit>
            <show-or-edit
              v-else
              :value="row[item.field]"
              :disabled="disabledFields(item.field)"

              type="Number"
            >
              <vxe-input
                v-model="row[item.field]"
                min="0"
                style="width: 80%;display: inline-block"
                type="number"
                @change="changeField(item.field,row[item.field])"
              />

            </show-or-edit>
          </template>

          <template #toolbar_buttons>
            <el-alert
              v-if="selectedRecord.length"
              show-icon
              type="success"
            >
              <template slot="title">
                {{ $t('common.selectedTotal', {selectedTotal: selectedRecord.map(j => j.materialId)?.length}) }}
                <el-button style="padding: 0;margin-left: 10px" type="text" @click="clearSelected">{{
                  $t('rfq.empty')
                }}
                </el-button>
              </template>
            </el-alert>
          </template>
        </vxe-grid>
        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNo"
          :total="total"
          @pagination="getTable"
        />
        <div style="display: flex;justify-content: space-between;margin-top: 20px">
          <div>
            <el-button v-has-permi="['rfq:price-approval:submit']" type="primary" @click="showReturnForm(true)">
              {{ $t('rfq.returnAll') }}
            </el-button>
          </div>

          <el-button
            v-has-permi="['rfq:price-approval:submit']"
            style="width: 30%"
            type="primary"
            :loading="submitApprovalLoad"
            @click="submitApproval"
          >{{ $t('rfq.submit') }}
          </el-button>

        </div>
      </div>

    </el-card>
    <el-dialog
      v-if="returnCommentVisible"
      :title="$t('rfq.returnComments')"
      :visible.sync="returnCommentVisible"
      width="400px"
    >
      <div v-if="!returnCommentForm.isAll">
        <div>{{ $t('rfq.youAreEnteringReturnCommentsForTheFollowingMaterials') }}</div>
        <p>{{ $t('rfq.afterYouClickOkYouCanStillModifyTheReturnedCommentsOrWithdrawComments') }}</p>
        <p>{{ $t('rfq.whenYouSubmitTheApprovalFormTheSystemWillAutomaticallyReturnTheMaterialsWithComments') }}</p>
        <p style="color: red">{{ returnCommentForm.materialCodes.join(',') }}</p>
      </div>
      <div v-else>
        <p>{{ $t('rfq.youAreReturningAllMaterialsInTheApprovalForm') }}</p>
        <p>{{ $t('rfq.afterYouClickOkTheApprovalFormWillBeReturnedAutomatically') }}</p>
      </div>

      <el-input
        v-model="returnCommentForm.opinion"
        :placeholder="$t('rfq.pleaseEnterTheReturnComments')"
        :rows="5"
        type="textarea"
      />
      <div slot="footer">
        <el-button @click="returnCommentVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitReturn">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
    <el-dialog
      v-if="returnMaterialVisible"
      :title="$t('rfq.viewReturnedItems')"
      :visible.sync="returnMaterialVisible"
      width="1000px"
    >
      <el-table v-loading="returnLoading" :data="returnMaterialList">
        <el-table-column :label="$t('material.materialCode')" prop="materialCode" />
        <el-table-column :label="$t('material.materialDescription')" prop="materialDescription" />
        <el-table-column :label="$t('material.manufacturer')" prop="mfg" />
        <el-table-column :label="$t('material.manufacturersPartNumber')" prop="mpn" />
        <el-table-column :label="$t('rfq.returnComments')">
          <template #default="scope">

            <el-input v-model="scope.row.opinion" />
          </template>

        </el-table-column>
        <el-table-column :label="$t('common.operate')">
          <template #default="scope">
            <el-button type="text" @click="revokeReturnOpinion(scope.row)">{{ $t('rfq.withdrawalOfReturnedComments') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="returnTotal > 0"
        :limit.sync="returnQueryParams.pageSize"
        :page.sync="returnQueryParams.pageNo"
        :total="returnTotal"
        @pagination="getReturnMaterialList"
      />
      <div slot="footer">
        <el-button type="primary" :loading="submitReturnMaterialLoading" @click="submitReturnMaterial">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="logVisible"
      :title="$t('common.operationRecord')"
      width="1000px"
      :visible.sync="logVisible"
    >
      <rfqOperationRecord
        :business-id="businessId"
        :business-type="businessType"
        :log-visible.sync="logVisible"
      />
    </el-dialog>

    <el-dialog
      v-if="historyVisible"
      width="1000px"

      :visible.sync="historyVisible"
    >
      <historyPrice
        ref="historyPriceDialog"
        :is-edit="true"
      />
      <div style="margin-top:10px;text-align: right">
        <el-button type="primary" @click="saveHistoryData">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>

    <rfqMaterial
      ref="rfqMaterial"
      :material-visible.sync="materialVisible"
      :rfq-project-status="null"
      :rfq-project-id="true"
    />
  </div>
</template>

<script>
import {
  approvalListPage, cancelData,
  checkReturnComments,
  createApprovalMaterialRel,
  deleteApprovalMaterialRel,
  exportDetailPriceEntry, getApprovalList,
  getApprovalMaterialRel,
  getByApproval,
  getFieldsByTemplatePriceEntry,
  getPriceApproval, getQuotationReplyDataForPriceEntry,
  getRfqFile,
  rejectPriceApproval,
  submitPriceApproval,
  updateApprovalMaterialRel, urgentData
} from '@/api/rfq/home'
import { getPriceApprovalMaterialAllIds } from '@/api/rfq/priceApproval'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import ShowOrEdit from '@/components/ShowOrEdit'

export default {
  name: 'PriceEntryApproval',
  components: {
    rfqOperationRecord: () => import('@/views/rfq/components/rfqOperation'),
    rfqMaterial: () => import('@/views/rfq/components/material'),
    customFields: () => import('./customFields'),
    ShowOrEdit
    // Steps
  },
  data() {
    return {
      // 审批通过loading
      submitApprovalLoad: false,
      // 审批退回
      showReturnFormLoading: false,
      // 退回
      submitReturnMaterialLoading: false,
      // 历史价格弹出框
      historyVisible: false,
      businessId: '',
      businessType: '',
      logVisible: false,
      radio: 1,
      radio1: ' ',
      materialVisible: false,
      queryParams: {
        approvalId: 0,
        categoryIds: [],
        materialIds: [],
        pageNo: 1,
        pageSize: 10,
        priceTrend: 0,
        search: '',
        status: '',
        sortBy: 'ASC',
        sortField: 'id',
        templateId: null,
        projectId: null,
        filter: null,
        projectMaterialStatus: '',
        notInFilterMaterialStatus: []
      },
      total: 0,
      loading: false,
      list: [],
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'approvalGrid',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        filterConfig: {
          remote: true
        },
        rowConfig: {
          keyField: 'materialId',
          isHover: true
        },
        columns: [
          { title: '', type: 'checkbox', visible: true, fixed: 'left', width: 30 }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      exportLoading: false,
      returnCommentVisible: false,
      returnCommentForm: {
        opinion: '',
        isAll: false,
        materialCodes: []
      },
      returnMaterialVisible: false,
      returnQueryParams: {
        approvalId: '',
        categoryIds: [],
        pageNo: 1,
        pageSize: 10
      },
      returnTotal: 0,
      returnLoading: false,
      returnMaterialList: [],
      rfqFileList: {
        parityFile: [],
        purchaseFile: [],
        supplierFile: []
      },
      recommendRemark: '',
      // 用来记录自定义列插入的位置。目前以采购单位列后面
      columnsStartIndex: null,
      // 自定义列的长度
      customColumnsLength: 0,
      // 合并列定义
      mergeRowFields: {
        // 根据物料+供应商合并的初始字段的长度，用于切换模块的时候进行保留列的判断
        quotationsMaterialSupplierFieldLength: 0,
        // 根据物料合并的字段
        fields: [
          'materialCode',
          'materialDescription',
          'materialStatus',
          'categoryId',
          'specifications',
          'mfg',
          'mpn',
          'dosage',
          'basicUnit',
          'quantityFrom',
          'quantityTo',
          'historyPrice',
          'historySupplierName',
          'historyCurrency',
          'historyRates',
          'lowestUnitPrice',
          'lowestUnitPriceCurrency',
          'lowestSupplierName',
          'recommendedUnitPriceTax',
          'recommendedUnitPriceWithoutTax',
          'recommendationVsHistoricalRateChange',
          'recommendedSupplierName',
          'approvalCount',
          'erp'
        ],
        // 根据物料+供应商合并的字段
        quotationsMaterialSupplierFields: [
          'supplierName',
          'quotationsCount',
          'currency',
          'exchangeRate',
          'taxRate',
          'moq',
          'deliveryPeriod',
          'orderUnit',
          'paymentMethod',
          'priceValidUntil'
        ],
        // 无论如何都根据供应商合并字段
        fullFields: [
          'proportion',
          'priceUnit',
          'recommendedPriceExpires'
        ]
      },
      selectedRecord: [],
      categoryList: [],
      approvalList: [],
      quotationMaterialQueryParams: {
        materialCode: '',
        pageNo: 1,
        pageSize: 10
        // supplierId: ''
      },
      dictTemplateFields: []

    }
  },
  async mounted() {
    // 自定义字段放在订单单位后面
    this.columnsStartIndex = this.girdOption.columns.findIndex((arr) => arr.field === 'orderUnit') >= 0 ? this.girdOption.columns.findIndex((arr) => arr.field === 'orderUnit') + 1 : this.girdOption.columns.length
    this.mergeRowFields.quotationsMaterialSupplierFieldLength = this.mergeRowFields.quotationsMaterialSupplierFields.length
    await this.init()
    this.getRecommendRemark()
    this.getFileList()
    this.getCategories()
  },
  methods: {
    async init() {
      this.loading = true
      this.queryParams.projectId = this.$route.query.id
      const res = await getApprovalList({ projectId: this.$route.query.id })
      this.approvalList = res.data
      const approvalId = this.$route.query.approvalId
      if (approvalId) {
        this.$refs.rfqPriceApproval.setCurrentRow(this.approvalList.find(a => a.id === Number(approvalId)))
      } else {
        this.$refs.rfqPriceApproval.setCurrentRow(this.approvalList[0])
      }
      this.queryParams.approvalId = this.approvalList.at(0).id
      this.returnQueryParams.approvalId = this.approvalList.at(0).id
      this.loading = false
    },
    // 物料列表的跨页全选框
    checkBoxAllChange({ checked }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.approvalGrid
      if (checked) {
        const projectMaterialIds = Array.from(new Set([...this.selectedRecord.map(item => item.materialId)]))
        if (projectMaterialIds?.length + this.total > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.clearCheckboxRow()
          // 当前选中的数据key数组
          const selectIds = grid.getCheckboxRecords().map(item => item.materialId)
          // 取交集
          const arr = projectMaterialIds.filter(x => selectIds.includes(x))
          if (arr.length > 0) {
            arr.forEach((id) => {
              grid.setCheckboxRow(grid.getRowById(id), true)
            })
          }
        } else {
          this.checkAllData(checked)
        }
      } else {
        this.checkAllData(checked)
      }
    },
    checkAllData(checked) {
      getPriceApprovalMaterialAllIds(this.queryParams).then(res => {
        if (checked) {
          this.selectedRecord = Array.from(new Set([...this.selectedRecord, ...res.data]))
        } else {
          const projectMaterialIds = Array.from(new Set([...res.data.map(item => item.materialId)]))
          this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => !projectMaterialIds.includes(x.materialId))))
        }
      })
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    showMaterialDetail(id) {
      this.materialVisible = true
      this.$refs.rfqMaterial.showMaterialDetail(id)
    },
    // 物料列表的单个选择框
    checkBoxChange({ checked, row }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.approvalGrid
      if (checked) {
        const projectMaterialIds = Array.from(new Set([...this.selectedRecord.map(item => item.materialId)]))
        if (projectMaterialIds?.length + 1 > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.setCheckboxRow(grid.getRowById(row.materialId), false)
        } else {
          const newData = {
            materialId: row.materialId,
            materialCode: row.materialCode
          }
          this.selectedRecord.push(newData)
        }
      } else {
        this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => row.materialId !== x.materialId)))
      }
    },
    // 跨页全选的清空
    clearSelected() {
      const grid = this.$refs.approvalGrid
      this.selectedRecord = []
      grid.clearCheckboxRow()
    },
    showHistoryPrice() {
      this.historyVisible = true
    },
    getList() {
      this.loading = true
      approvalListPage(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
        this.$nextTick(() => {
          const grid = this.$refs.approvalGrid
          const projectMaterialIds = Array.from(new Set([...this.selectedRecord.map(item => item.materialId)]))
          this.list?.forEach((row) => {
            if (projectMaterialIds?.includes(row.materialId)) {
              setTimeout(() => {
                grid.setCheckboxRow(grid.getRowById(row.materialId), true)
              }, 0)
            }
          })
        })
      })
    },
    getRecommendRemark() {
      getPriceApproval({ approvalId: this.queryParams.approvalId }).then(res => {
        this.recommendRemark = res.data.recommendRemark
        this.queryParams.templateId = res.data.quotationsTemplate
        this.getTemplateTabs()
      })
    },
    getFileList() {
      Promise.all([getRfqFile({
        businessId: this.queryParams.approvalId,
        businessType: 'RFQ_APPROVAL_REPORT'
      }),
      getRfqFile({
        businessId: this.queryParams.approvalId,
        businessType: 'RFQ_APPROVAL'
      }),
      getByApproval({
        approvalId: this.queryParams.approvalId
      })
      ]).then(res => {
        this.rfqFileList.parityFile = res[0].data
        this.rfqFileList.purchaseFile = res[1].data
        this.rfqFileList.supplierFile = res[2].data
      })
    },
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const prevRow = visibleData[_rowIndex - 1]
      let nextRow = visibleData[_rowIndex + 1]
      // 按照物料进行合并，选择框也按照物料合并
      if (row && (['checkbox'].includes(column.type) || this.mergeRowFields.fields.includes(column.property))) {
        if (prevRow && row.materialId === prevRow.materialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.materialId === nextRow.materialId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      } else if ([...this.mergeRowFields.fullFields, ...this.mergeRowFields.quotationsMaterialSupplierFields].includes(column.property) && row) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && row.quotationsMaterialSupplierRelId === prevRow.quotationsMaterialSupplierRelId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.quotationsMaterialSupplierRelId === nextRow.quotationsMaterialSupplierRelId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    exportData() {
      this.$modal.confirm(this.$t('rfq.areYouSureToExportAllApprovedBillsOfMaterials')).then(() => {
        this.exportLoading = true
        return exportDetailPriceEntry(this.queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('rfq.approveMaterialxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },

    closeMaterial(close) {
      close()
    },
    showReturnForm(isAll) {
      this.returnCommentForm.isAll = isAll
      this.returnCommentForm.opinion = ''

      if (!isAll) {
        const materialCodes = this.selectedRecord.map(item => item.materialCode)
        const materialIds = this.selectedRecord.map(item => item.materialId)
        if (!materialCodes.length) {
          this.$message.error(this.$t('rfq.pleaseSelectMaterialsFirst'))
          return
        }
        this.returnCommentForm.materialCodes = [...new Set(materialCodes)]
        this.queryParams.materialIds = [...new Set(materialIds)]
      }
      checkReturnComments(this.queryParams).then(res => {
        this.returnCommentVisible = true
      })
    },
    changePriceTrend() {
      debugger
      this.getTable()
    },
    submitReturn() {
      if (this.returnCommentForm.isAll) {
        rejectPriceApproval({
          approvalId: this.queryParams.approvalId
        }).then(() => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.returnCommentVisible = false
          this.getTable()
          this.emitter.emit('freshStep', this.queryParams.projectId)
        })
      } else {
        createApprovalMaterialRel({
          approvalId: this.queryParams.approvalId,
          opinion: this.returnCommentForm.opinion,
          materialIds: this.selectedRecord.map(item => item.materialId)
        }).then(() => {
          this.$message.success(this.$t('rfq.returnCommentsEnteredSuccessfully'))
          this.returnCommentVisible = false
          this.getTable()
        })
      }
    },
    getReturnMaterialList() {
      this.returnMaterialVisible = true
      this.returnLoading = true
      getApprovalMaterialRel(this.returnQueryParams).then(res => {
        this.returnMaterialList = res.data.list
        this.returnTotal = res.data.total
        this.returnLoading = false
      })
    },
    revokeReturnOpinion(obj) {
      console.log(obj)
      deleteApprovalMaterialRel({
        id: obj.id
      }).then(() => {
        this.$message.success(this.$t('rfq.withdrawalOfReturnedCommentsSucceeded'))
        this.getReturnMaterialList()
      })
    },
    submitReturnMaterial() {
      this.submitReturnMaterialLoading = true
      updateApprovalMaterialRel(this.returnMaterialList).then(() => {
        this.$message.success(this.$t('rfq.successfullySubmittedReturnComments'))
        this.returnMaterialVisible = false
        this.getTable()
        this.submitReturnMaterialLoading = false
      })
    },
    submitApproval() {
      this.submitApprovalLoad = true
      submitPriceApproval({
        approvalId: this.queryParams.approvalId
      }).then(() => {
        this.$message.success(this.$t('rfq.successfullySubmittedForApproval'))
        this.getTable()
        this.submitApprovalLoad = false
        this.emitter.emit('freshStep', this.queryParams.projectId)
      }).catch(() => {
        this.submitApprovalLoad = false
      })
    },
    openFile(url) {
      window.open(url)
    },
    showOperationLog(obj1, obj2) {
      this.logVisible = true
      this.businessId = obj1
      this.businessType = obj2
    },
    resetQuery() {
      this.queryParams.search = ''
      this.queryParams.projectMaterialStatus = ''
      this.queryParams.pageNo = 1
      this.queryParams.pageSize = 10
      this.queryParams.priceTrend = 0
      this.queryParams.filter = null
      this.queryParams.notInFilterMaterialStatus = []
      this.getTable()
    },
    expedited(row) {
      urgentData({
        ids: row.id
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.getApprovalList()
      })
    },
    revoke(row) {
      this.$modal.confirm(this.$t('rfq.areYouSureToRevokeThisApprovalForm')).then(() => {
        cancelData({
          ids: row.id
        }).then(res => {
          if (res.data) {
            this.getApprovalList()
            this.$message.success(this.$t('order.operationSucceeded'))
          } else {
            this.$message.success(this.$t('auth.operationFailed'))
          }
        })
      }).catch(() => {
      })
    },
    getApprovalList() {
      getApprovalList({
        projectId: this.$route.query.id
      }).then(res => {
        this.approvalList = res.data
      })
    },
    changeApproval(row) {
      this.queryParams.approvalId = row.id
      this.returnQueryParams.approvalId = row.id
      this.getRecommendRemark()
      this.getFileList()
    },
    getTemplateTabs() {
      this.getTemplateFields()
      this.getTable()
    },
    getTable() {
      getQuotationReplyDataForPriceEntry({
        ...this.queryParams, notInFilterMaterialStatus: ['new', 'to_be_sourced', 'shelve', 'to_quote', 'no_quote', 'to_be_recommended', 'completed', 'termination']
      }).then(res => {
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    getTemplateFields() {
      getFieldsByTemplatePriceEntry({
        templateId: this.activeCategory
      }).then(res => {
        this.dictTemplateFields = []
        this.materialField = []
        this.noMergeField = []
        this.girdOption.columns = res.data.filter(a => !a.hidden).sort((a, b) => a.sort - b.sort).map((item, index) => {
          if (item.merge === 0) {
            this.materialField.push(item.fieldCode)
          } else if (item.merge === 2) {
            this.noMergeField.push(item.fieldCode)
          }
          const common = {
            title: item.fieldName,
            field: item.fieldCode,
            visible: true,
            slots: {},
            width: 150,
            fixed: index === 0 ? 'left' : ''
          }
          // 物料编码需要加图纸下载
          if (item.fieldDictType || item.modify || item.fieldCode === 'materialCode' || item.fieldCode === 'approvalNumbers') {
            this.dictTemplateFields.push({
              type: item.fieldDictType?.slice(item.fieldDictType.indexOf(':') + 1),
              fieldType: item.fieldType,
              field: item.fieldCode,
              modify: item.modify,
              required: item.required
            })
            // if (item.modify) {
            //   common.slots.edit = item.fieldCode + 'Edit'
            //   common.editRender = {}
            // }
            if (item.required) {
              common.slots.header = 'requiredField'
            }
            common.slots.default = item.fieldCode
          }
          return common
        })
        this.girdOption.columns.unshift({ title: '', type: 'checkbox', visible: true, width: 30, fixed: 'left' })
      })
    },
    disabledFields(fields) {
      return true
    },
    changeField(field, value) {
      console.log(field)
      // todo 计算价格
    },
    changeTaxRate() {
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

::v-deep .labelTitle {
  font-weight: bolder !important;
  color: black;

}

.commonMaterialItem {
  width: 33%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
}

.rfq-create-upload {
  margin: 10px 0
}

.rfq-create {
  margin-top: 15px;
}

.materialItem {
  width: 178px;
}

.rfqMaterial {
  .mainTab {
    margin-bottom: 15px;
  }
}

//::v-deep .gray{
//  background: #999999;
//}
//::v-deep .yellow{
//  background: #ffeb3b;
//}
//::v-deep .red{
//  background: #ff5733;
//}
//
//::v-deep .indigo{
//  background: #a5d642;
//}
//
//::v-deep .blue{
//  background: #2684e4;
//}
//
//::v-deep .green{
//  background: #41d17b;
//}
</style>
