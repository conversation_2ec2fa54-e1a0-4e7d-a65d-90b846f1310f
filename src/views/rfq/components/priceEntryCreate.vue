<template>
  <div>
    <common-card
      :title=" $t('rfq.basicInformationOfTheProject')"
    >
      <div>
        <div>
          <el-form ref="rfqProject" :model="rfqProject" :rules="rfqProjectRules" inline label-width="166px" size="mini">
            <el-form-item
              :label="$t('rfq.eventName')"
              class="commonFormItemLeft"
              label-width="166px"
              prop="eventName"
            >
              <show-or-edit
                :disabled="!rfqProjectEdit"
                :value="rfqProject.eventName"
              >
                <el-input v-model="rfqProject.eventName" :disabled="!rfqProjectEdit" />

              </show-or-edit>
            </el-form-item>
<!--            <el-form-item-->
<!--              :label="$t('rfq.billOfMaterialsHandler')"-->
<!--              class="commonFormItem"-->
<!--              prop="materialBillManagerList"-->
<!--            >-->
<!--              <show-or-edit-->
<!--                :dict="DICT_TYPE.COMMON_USERS"-->
<!--                :disabled="true"-->
<!--                :value="rfqProject.materialBillManagerList"-->
<!--              >-->
<!--                <el-select-->
<!--                  v-model="$store.getters.userId"-->
<!--                  :disabled="!rfqProjectEdit"-->
<!--                  class="content"-->
<!--                  clearable-->
<!--                  filterable-->
<!--                >-->
<!--                  <el-option-->
<!--                    v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"-->
<!--                    :key="dict.id"-->
<!--                    :label="dict.name"-->
<!--                    :value="dict.id"-->
<!--                  />-->
<!--                </el-select>-->

<!--              </show-or-edit>-->
<!--            </el-form-item>-->
            <el-form-item
              :label="$t('rfq.sponsor')"
              class="commonFormItemLeft"
            >
              {{
                rfqProject.creator == null ? $store.getters.nickname : getUserName(rfqProject.creator)
              }}
            </el-form-item>
            <el-form-item
              class="commonFormItemLeft"
              label-width="166px"
              :label="$t('supplier.purchasingOrganization')"
              prop="purchaseOrg"
            >
              <show-or-edit
                :dict="DICT_TYPE.COMMON_PURCHASEORG"
                :disabled="!rfqProjectEdit"
                :value="rfqProject.purchaseOrg"
                class="content"
              >
                <el-select v-model="rfqProject.purchaseOrg" clearable filterable class="content"  @change="onchangePurchaseOrg" >
                  <el-option
                    v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0)"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('supplier.endCustomer')"
              class="commonFormItemLeft"
            >
              <show-or-edit
                :disabled="!rfqProjectEdit"
                :value="rfqProject.endCustomer"
              >
                <el-input v-model="rfqProject.endCustomer" :disabled="!rfqProjectEdit" />

              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('rfq.projectDeadline')"
              class="commonFormItemLeft"
              label-width="166px"
              prop="expirationDate"
            >
              <show-or-edit
                :disabled="!rfqProjectEdit"
                :value="rfqProject.expirationDate"
                type="Date"
              >
                <el-date-picker
                  v-model="rfqProject.expirationDate"
                  :disabled="!rfqProjectEdit"
                  :placeholder="$t('order.selectDate')"
                  class="content"
                  placement="bottom-start"
                  type="date"
                  value-format="yyyy-MM-dd"
                />
              </show-or-edit>

            </el-form-item>
            <el-form-item
              :label="$t('rfq.application')"
              class="commonFormItemLeft"
            >
              <show-or-edit
                :disabled="!rfqProjectEdit"
                :value="rfqProject.productLine"
              >
                <el-input v-model="rfqProject.productLine" :disabled="!rfqProjectEdit" />

              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('common.sourcing')"
              class="commonFormItemLeft"
              prop="sourcingSources"
            >
              <show-or-edit
                :dict="DICT_TYPE.COMMON_USERS"
                :disabled="true"
                :value="rfqProject.sourcingSources"
              >
                <el-select
                  v-model="rfqProject.sourcingSources"
                  :disabled="!rfqProjectEdit"
                  class="content"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="dict in sourcingSources"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>

              </show-or-edit>
            </el-form-item>

            <el-form-item
              :label="$t('rfq.loadingDestination')"
              class="commonFormItemLeft"
              prop="shippingDestination"
            >
              <show-or-edit
                :custom-list="factorySources"
                :disabled="!rfqProjectEdit"
                :value="rfqProject.shippingDestination"
              >
                <el-select v-model="rfqProject.shippingDestination" :disabled="!rfqProjectEdit" class="content"
                           clearable  filterable  allow-create  placeholder="Please select" >
                  <el-option
                    v-for="dict in factorySources"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>

              </show-or-edit>
            </el-form-item>


            <el-form-item
              :label="$t('上传附件')"
              class="commonFormItemLeft"
            >
              <div style="display: flex;justify-content: space-between">

                <el-upload
                  class="upload-demo"
                  :disabled="!rfqProjectEdit"
                  :action="uploadUrl"
                  :headers="getBaseHeader()"
                  :on-remove="onRemove"
                  :on-preview="onPreview"
                  :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                  multiple
                  :limit="5"
                  :show-file-list="false"
                  :file-list="fileList"
                >
                  <el-button
                    v-if="rfqProjectEdit"
                    :disabled="!rfqProjectEdit"
                    class="uploadBtn"
                    size="small"
                    plain
                    icon="el-icon-plus"
                    type="primary"
                  />
                </el-upload>
                <div>
                  查看附件
                  <el-button
                    class="uploadBtn"
                    size="small"
                    style="padding: 5px 9px"
                    :disabled="fileList.length===0"
                    plain
                    :type="fileList.length?'primary':''"
                    @click="showFile=true"
                  >
                    {{ fileList.length }}
                  </el-button>

                  <el-dialog
                    v-if="showFile"
                    :visible.sync="showFile"
                    title="查看附件"
                    width="400px"
                  >
                    <el-upload
                      class="upload-demo"
                      :disabled="!rfqProjectEdit"
                      :action="uploadUrl"
                      :headers="getBaseHeader()"
                      :on-remove="onRemove"
                      :on-preview="onPreview"
                      :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                      multiple
                      :limit="5"
                      :show-file-list="true"
                      :file-list="fileList"
                    />
                    <div slot="footer">
                      <el-button type="primary" @click="showFile=false">确定</el-button>
                    </div>

                  </el-dialog>
                </div>
              </div>

            </el-form-item>
            <el-form-item
              :label="$t('项目备注')"
              class="commonFormItemLeft"
            >
              <template #label>
                <span>
                  <el-button
                    v-if="rfqProject.id"
                    type="text"
                    @click="showRemark= true"
                  >修改</el-button>
                  {{ $t('项目备注') }}
                </span>
              </template>
              <template #default>
                <div v-if="rfqProject.id" style="flex: 0 1 70%;font-size: 14px">
                  <div v-for="(item,index) in rfqRemarkList" :key="index">
                    <span>
                      {{ item.creatorName }} {{ parseTime(item.createTime) }}
                    </span>
                    <!--                    <el-button style="margin-left: 10px;padding: 0 12px" type="text" @click="delRemark(item.id)">-->
                    <!--                      {{ $t('common.del') }}-->
                    <!--                    </el-button>-->
                    <div style="word-break: break-word;">
                      {{ item.remark }}
                    </div>
                  </div>
                </div>
                <el-input
                  v-else
                  v-model="rfqRemark"
                  :placeholder="$t('rfq.pleaseEnterText')"
                  :rows="1"
                  autosize
                  type="textarea"
                />

              </template>

            </el-form-item>

            <div v-show="showMore">
              <el-form-item
                :label="$t('rfq.businessModel')"
                class="commonFormItemLeft"
                label-width="166px"
              >
                <dict-tag :type="DICT_TYPE.RFQ_BUSINESS_TYPE" :value="rfqProject.businessModel" />
              </el-form-item>
             <el-form-item/>
              <el-form-item
                :label="$t('rfq.projectCreationDate')"

                class="commonFormItemLeft"
                label-width="166px"
              >
                {{ parseTime(rfqProject.createTime, '{y}-{m}-{d}') }}
              </el-form-item>
              <el-form-item
                :label="$t('rfq.purposeOfInquiry')"
                class="commonFormItemLeft"
                label-width="166px"
              >
                <show-or-edit
                  :dict="DICT_TYPE.RFQ_INQUIRY_PURPOSE"
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.inquiryPurpose"
                >
                  <el-select
                    v-model="rfqProject.inquiryPurpose"
                    :disabled="!rfqProjectEdit"
                    :placeholder="$t('common.pleaseSelect')"
                    class="content"
                    clearable
                  >
                    <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.RFQ_INQUIRY_PURPOSE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>

                </show-or-edit>
              </el-form-item>
              <el-form-item
                :label="$t('rfq.productModel')"
                class="commonFormItemLeft"
              >
                <show-or-edit
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.productNumber"
                >
                  <el-input v-model="rfqProject.productNumber" :disabled="!rfqProjectEdit" />

                </show-or-edit>
              </el-form-item>
              <el-form-item
                :label="$t('order.customerName')"
                class="commonFormItemLeft"
              >
                <show-or-edit
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.customName"
                >
                  <el-input v-model="rfqProject.customName" :disabled="!rfqProjectEdit" />

                </show-or-edit>
              </el-form-item>
              <el-form-item
                :label="$t('rfq.productName')"
                class="commonFormItemLeft"
              >
                <show-or-edit
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.productName"
                >
                  <el-input v-model="rfqProject.productName" :disabled="!rfqProjectEdit" />

                </show-or-edit>
              </el-form-item>
              <el-form-item
                :label="$t('rfq.paymentMethodRequired')"
                class="commonFormItemLeft"
              >
                <show-or-edit
                  :dict="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION"
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.requestPaymentMethod"
                >
                  <el-select
                    v-model="rfqProject.requestPaymentMethod"
                    :disabled="!rfqProjectEdit"
                    :placeholder="$t('common.pleaseSelect')"
                    class="content"
                    clearable
                  >
                    <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>

                </show-or-edit>

              </el-form-item>
              <el-form-item
                :label="$t('rfq.requestedDeliveryMethod')"
                class="commonFormItemLeft"
              >
                <show-or-edit
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.requestDeliveryMethod"
                >
                  <el-select
                    v-model="rfqProject.requestDeliveryMethod"
                    :disabled="!rfqProjectEdit"
                    :placeholder="$t('common.pleaseSelect')"
                    class="content"
                    clearable
                  >
                    <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>

                </show-or-edit>
              </el-form-item>

              <el-form-item
                :label="$t('rfq.terminalCustomerModel')"
                class="commonFormItemLeft"
              >
                <show-or-edit
                  :disabled="!rfqProjectEdit"
                  :value="rfqProject.endCustomerModel"
                >
                  <el-input v-model="rfqProject.endCustomerModel" :disabled="!rfqProjectEdit" />

                </show-or-edit>
              </el-form-item>
            </div>
            <div>
              <el-button style="margin-left: 110px" type="primary" plain @click="showMore=!showMore">
                {{ showMore ? '收起' : '更多' }}
              </el-button>
            </div>

          </el-form>
        </div>
      </div>
      <div style="display: flex;justify-content: center">
        <el-button v-if="rfqProjectEdit" size="mini" style="width: 15%;" type="primary" @click="saveProject">
          {{ rfqProject.id ? '更新' : $t('common.submit') }}
        </el-button>
      </div>
    </common-card>
    <common-card
      v-if="rfqProject.id"
      style="margin: 20px 0"
    >
      <div slot="header">
        {{ $t('rfq.billOfMaterials') }}
      </div>
      <div style="display: flex;justify-content: space-around;align-items: center;margin-bottom: 25px;">
        <div style="display: flex">
          <el-input
            v-model="quotationMaterialQueryParams.filter"
            :placeholder="$t('rfq.pleaseEnterMaterialCodeSupplierAbbreviationMpnmfg')"
            clearable
            style="width: 500px"
            @keyup.enter.native="quotationMaterialQueryParams.pageNo=1;getTable();"
          />
          <el-button plain type="primary" @click="quotationMaterialQueryParams.pageNo=1;getTable();">
            {{ $t('common.search') }}
          </el-button>
          <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
            <el-button type="text">
              {{ $t('common.advancedSearch') }}

            </el-button>
            <i
              :style="showSearch? '':{transform: 'rotate(180deg)'}"
              class="el-icon-arrow-up"
              style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
            />
          </div>

        </div>

      </div>
      <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="120px" size="small">
        <el-form-item
          :label="$t('material.materialCode')"
          class="searchItem"
          prop="materialCode"
        >
          <el-input
            v-model="quotationMaterialQueryParams.materialCode"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item
          :label="$t('material.manufacturer')"
          class="searchItem"
          prop="mfg"
        >
          <el-input
            v-model="quotationMaterialQueryParams.mfg"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />

        </el-form-item>
        <el-form-item
          :label="$t('material.manufacturersPartNumber')"
          class="searchItem"
          prop="mpn"
        >
          <el-input
            v-model="quotationMaterialQueryParams.mpn"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item
          :label="$t('material.category')"
          class="searchItem"
          prop="factoryIds"
        >
          <cascading-category
            :multiple="false"
            :original-value.sync="quotationMaterialQueryParams.categoryId"
            class="searchValue"
          />
        </el-form-item>

        <el-form-item
          :label="$t('supplier.supplier')"
          class="searchItem"
          prop="pgIds"
        >
          <el-input
            v-model="quotationMaterialQueryParams.supplier"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
          />
        </el-form-item>
        <el-form-item
          :label="$t('rfq.priceCategory')"
          class="searchItem"
          prop="pgIds"
        >
          <el-select v-model="quotationMaterialQueryParams.priceCategory" class="searchValue" clearable filterable>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_PRICE_CATEGORY)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          :label="$t('material.materialStatus')"
          class="searchItem"
          prop="pgIds"
        >
          <el-select
            v-model="quotationMaterialQueryParams.projectMaterialStatus"
            class="searchValue"
            clearable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_MATERIAL_STATUS,0)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>

        </el-form-item>

        <div style="text-align: center">
          <el-button
            icon="el-icon-search"
            plain
            size="mini"
            type="primary"
            @click="quotationMaterialQueryParams.pageNo=1;getTable();"
          >{{
            $t('common.search')
          }}
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('common.reset') }}</el-button>

        </div>

      </el-form>

      <vxe-grid
        ref="rfqMaterialGrid"
        :data="list"
        :loading="loading"
        :seq-config="{startIndex: (queryParams.pageNo - 1) * queryParams.pageSize}"
        :span-method="mergeRowMethod"
        style="margin-top: 10px"
        v-bind="girdOption"
        @checkbox-change="checkBoxChange"
        @checkbox-all="checkBoxAllChange"
      >
        <template #requiredField="{column}">
          <span class="requiredTitle">
            {{ column.title }}
          </span>
        </template>
        <template v-for="item in dictTemplateFields" #[item.field]="{row,$rowIndex}">
          <span v-if="item.field === 'approvalNumbers'">
            <el-button
              v-if="row.approvalNumbers === 0"
              disabled
              size="mini"
              type="text"
            >{{ row.approvalNumbers }}
            </el-button>
            <el-button
              v-if="row.approvalNumbers > 0"
              size="mini"
              style="text-decoration: underline"
              type="text"
              @click="showOperationLog(row.projectMaterialId,'RFQ_PRICE_APPROVAL')"
            >{{ row.approvalNumbers }}
            </el-button>
          </span>
          <span v-if="item.field === 'materialCode'">
            <i
              v-if="row.materialDrawing"
              class="el-icon-picture-outline"
              style="font-size: 16px;margin-right: 3px;cursor: pointer"
              @click="downloadMaterialDrawing(row.projectMaterialId,row.materialCode)"
            />
            <copy-button
              @click="showMaterialDetail(row)"
            >
              {{ row.materialCode }}</copy-button>
          </span>
          <dict-tag v-if="!item.modify" :type="item.type" :value="row[item.field]" />
          <show-or-edit
            v-else-if="item.fieldType === 'Date'"
            :disabled="disabledFields(row, item.field)"
            :value="row[item.field]"
            type="Date"
          >
            <el-date-picker
              v-model="row[item.field]"
              :placeholder="$t('order.selectDate')"
              style="width: 140px"
              type="date"
            />
          </show-or-edit>

          <show-or-edit
            v-else-if="item.modify&&item.type&&item.fieldType==='String'"
            :dict="item.type"
            :disabled="disabledFields(row,item.field)"
            :value="row[item.field]"
          >
            <el-select
              v-model="row[item.field]"
            >
              <el-option
                v-for="dict in getDictDatas(item.type)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </show-or-edit>

          <show-or-edit
            v-else-if="item.modify&&item.type"
            :dict="item.type"
            :disabled="disabledFields(row,item.field)"
            :value="row[item.field]"
          >

            <el-select
              v-model="row[item.field]"
              @change="changeTaxRate(item.field,row[item.field])"
            >
              <el-option
                v-for="dict in getDictDatas(item.type)"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>

          </show-or-edit>

          <show-or-edit
            v-else-if="item.fieldType === 'String'"
            :disabled="disabledFields(row,item.field)"
            :value="row[item.field]"
          >
            <el-input
              v-model="row[item.field]"
              style="width: 80%"
              type="text"
            />
          </show-or-edit>
          <show-or-edit
            v-else-if="item.fieldType === 'Integer'"
            :disabled="disabledFields(row,item.field)"
            :value="row[item.field]"
          >
            <vxe-input
              v-model="row[item.field]"
              min="0"
              style="width: 80%;display: inline-block"
              type="integer"
              @change="changeField(item.field,row[item.field])"
            />

          </show-or-edit>
          <show-or-edit
            v-else
            :disabled="disabledFields(row,item.field)"
            :value="row[item.field]"
          >
            <vxe-input
              v-model="row[item.field]"
              min="0"
              style="width: 80%;display: inline-block"
              type="number"
              @change="changeField(item.field,row[item.field])"
            />

          </show-or-edit>
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="10" class="mb8" style="width: 100%">
            <el-col :span="16">
              <sticky :sticky-top="0" :z-index="999" style="width: 100%;height: 29.6px">
                <div style="width: 900px">
                  <el-button
                    v-if="showFlag"
                    :disabled="!rfqProjectEdit"
                    size="mini"
                    type="primary"
                    @click="upload.open = true"
                  > {{ $t('rfq.batchImportOfMaterialPrices') }}
                  </el-button>
                  <el-button
                    v-if="showFlag"
                    v-has-permi="['rfq:project-materials:deleteMaterials']"
                    size="mini"
                    type="danger"
                    @click="delMaterial"
                  > {{ $t('rfq.deleteItem') }}
                  </el-button>
                </div>
              </sticky>

            </el-col>
            <el-col :span="8">

              <right-toolbar
                :custom-columns.sync="girdOption.columns"
                :list-id="girdOption.id"
                :only-custom="false"
                :show-search.sync="showSearch"
                style="float: right"
                @queryTable="init"
              />
            </el-col>
          </el-row>
          <el-alert
            v-if="selectedRecord.length"
            show-icon
            type="success"
          >
            <template slot="title">{{ $t('common.selectedTotal', { selectedTotal: selectedRecord.length }) }}
              <el-button style="padding: 0;margin-left: 10px" type="text" @click="clearSelected">{{
                $t('rfq.empty')
              }}
              </el-button>
            </template>
          </el-alert>
        </template>
      </vxe-grid>
      <pagination
        v-show="total > 0"
        :limit.sync="quotationMaterialQueryParams.pageSize"
        :page.sync="quotationMaterialQueryParams.pageNo"
        :total="total"
        @pagination="getTable"
      />
    </common-card>

    <div style="text-align: center;margin-top: 15px">
      <el-button
        v-if="rfqProject.id&&showApproveReportButton"
        style="width: 15%"
        @click="saveQuotation"
      >{{
        $t('common.save')
      }}
      </el-button>
      <el-button v-if="showApproveReportButton" style="width: 15%" type="primary" @click="showReportConfig">
        {{ $t('rfq.reportAndApproval') }}
      </el-button>
    </div>
    <rfqMaterial
      ref="rfqMaterial"
      :material-visible.sync="materialVisible"
      :rfq-project-id="rfqProject.id"
      :rfq-project-status="rfqProject.status"
      @getTable="getTable"
    />
    <el-dialog :title="upload.title" :visible.sync="upload.open" append-to-body width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url"
          :auto-upload="false"
          :data="queryParams"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-preview="onPreview"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button plain type="primary" @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="approveReportVisible"
      :title="$t('rfq.reportAndApproval')"
      :visible.sync="approveReportVisible"
      width="1000px"
    >
      <common-card
        v-if="rfqProject.id &&showFlag"
        :title="$t('rfq.reportTemplate')"
      >

        <div style="padding: 0 100px">
          <el-descriptions :colon="false" :column="2" direction="vertical" label-class-name="labelTitle">
            <el-descriptions-item :label="$t('rfq.recommendedRemarks')" :span="2">
              <el-input v-model="approvalReportConfig.recommendRemark" :row="3" type="textarea" />
            </el-descriptions-item>
            <el-descriptions-item :label="$t('rfq.purchaseUploadQuotationAttachment')">

              <el-upload
                :action="uploadUrl"
                :file-list="reportFileList"
                :headers="getBaseHeader()"
                :limit="5"
                :on-preview="onPreview"
                :on-remove="onRemoveReport"
                :on-success="(response, file, reportFileList)=>onSuccessReport(response, file, reportFileList,)"
                class="rfq-create-upload"
                multiple
              >

                <el-button size="small" plain type="primary">{{
                  $t('rfq.uploadPriceRecommendationAttachment')
                }}
                </el-button>
                <div slot="tip" class="el-upload__tip">
                  {{ $t('rfq.totalAttachmentsShouldNotExceedmb') }}
                </div>
              </el-upload>

            </el-descriptions-item>

          </el-descriptions>

        </div>

      </common-card>
      <el-card v-if="rfqProject.id && showFlag" style="margin-top: 15px">
        <div slot="header">
          {{ $t('rfq.approvalProcessConfiguration') }}
        </div>
        <div style="padding: 0 100px">
          <el-descriptions :colon="false" :column="1" label-class-name="labelTitle">
            <el-descriptions-item :label="$t('rfq.approvalFormName')" label-class-name="requiredLabelTitle">
              <el-input v-model="approvalReportConfig.approvalName	" />
            </el-descriptions-item>
            <el-descriptions-item :label="$t('rfq.urgentApproval')">
              <el-checkbox v-model="approvalReportConfig.urgent">{{ }}</el-checkbox>
            </el-descriptions-item>
          </el-descriptions>

        </div>

      </el-card>

      <div style="display: flex;justify-content: space-around;margin-top: 20px;padding: 0 64px">
        <el-tooltip
          :content="$t('rfq.abandonSubmissionAndClosePopupWindow')"
          class="item"
          effect="dark"
          placement="top"
        >
          <el-button style="width: 20%" @click="cancelApprove">{{ $t('common.cancel') }}</el-button>
        </el-tooltip>
        <el-button
          v-if="rfqProject.id&&showFlag"
          style="width: 15%"
          type="primary"
          :loading="btnLoading"
          @click="handleApprove"
        >{{ $t('rfq.submitForApproval') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="logVisible"
      :visible.sync="logVisible"
      :title="$t('common.operationRecord')"
      width="1000px"
    >
      <rfqOperationRecord
        :business-id="businessId"
        :business-type="businessType"
        :log-visible.sync="logVisible"
      />
    </el-dialog>
    <el-dialog
      width="400px"
      :visible.sync="showRemark"
      title="修改项目备注"
    >
      <div v-for="(item,index) in rfqRemarkList" :key="index" style="margin: 3px 0">
        <span>
          {{ item.creatorName }} {{ parseTime(item.createTime) }}
        </span>
        <el-button style="margin-left: 10px;padding: 0 12px;color: #d43030" type="text" @click="delRemark(item.id)">
          {{ $t('common.del') }}
        </el-button>
        <div style="word-break: break-word;">
          {{ item.remark }}
        </div>
      </div>
      <div style="display: flex">
        <el-input
          v-model="rfqRemark"
          style="margin: 10px 0"
          :placeholder="$t('rfq.pleaseEnterText')"
          :rows="1"
          autosize
          type="textarea"
        />
        <el-button
          style="margin-left: 10px"
          type="text"
          @click="submitRemark"
        >
          {{ $t('新增') }}
        </el-button>
      </div>
      <div slot="footer">
        <el-button @click="showRemark = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { DICT_TYPE, getDictData, getDictDatas } from '@/utils/dict'
import {
  checkMaterialCategoryIsNull,
  checkMaterialHasQuote,
  checkMaterialStatus,
  checkSumbitToSource,
  checkTerminateMaterial,
  createFileRelForCreateProject,
  createQuotation,
  createRfqRemark,
  deleteMaterials,
  deleteMaterialsForPriceEntry,
  deleteMaterialsSupplier,
  deleteMaterialsSupplierRel,
  delRfqFile,
  delRfqRemark,
  downloadMaterialDrawings,
  downloadMaterialQuote,
  downloadPriceDirectPriceEntry,
  getFieldsByTemplatePriceEntry,
  getMaterialByProject,
  getMaterialSupplierRelByMaterialIds,
  getMfgAndSupplierRel,
  getProjectMaterialsQuotationsLists,
  getQuotationReplyDataForPriceEntry,
  getRemarkList,
  getRfqFile,
  getRfqMaterialList,
  getRfqProject,
  getRfqProjectDisplay,
  rfqFilePost,
  saveMaterialAndSupplierRel,
  savePriceApprovalPriceEntry,
  saveQuotationMaterialPriceEntry,
  saveRfqProject,
  saveRfqProjectDisplay,
  shelveRfqMaterial,
  sumbitToSource
} from '@/api/rfq/home'
import { getBaseHeader } from '@/utils/request'
import Sticky from '@/components/Sticky/index.vue'
import dayjs from 'dayjs'
import { checkStatus } from '@/api/rfq/priceApproval'
import {listPurchaseOrgFactoryFromCache} from "@/api/system/factory";

export default defineComponent({
  name: 'PriceEntryCreate',
  components: {
    Sticky, ShowOrEdit,
    rfqMaterial: () => import('@/views/rfq/components/material'),
    rfqOperationRecord: () => import('@/views/rfq/components/rfqOperation')
  },
  props: ['steps'],
  data() {
    return {
      approvalReportConfig: {
        approvalMaterialSupplierRelIds: [],
        quotationsMaterialSupplierRelId: [],
        // 报价物料ID，rfq_project_materials表id拼接
        projectMaterialId: '',
        // 提交审批的物料id集合
        projectMaterialIds: [],
        fileList: [],
        approvalName: '',
        approvalNo: '',
        attachmentRelFileIds: [],
        files: '',
        flag: '',
        // id: 0,
        // status: 'pending_approval',
        previewRecordId: 0,
        priceApprovalApproves: [],
        projectId: null,
        quotationsTemplate: '',
        recommendRemark: '',
        reportId: null,
        urgent: false,
        uploadFileIds: []
      },

      // 上传文件id集合
      fileIds: [],
      // 配置报价单确定防止重复提交
      createLoading: false,
      // 询价单物料终止的弹出框
      returnCommentVisible: false,
      // 询价单物料终止的表单参数
      returnCommentForm: {
        opinion: '',
        quotationsMaterialSupplierRelId: null
      },
      businessId: null,
      businessType: 'MATERIAL_OPERATION_RECORD',
      getBaseHeader,
      showFile: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      fileList: [],
      reportFileList: [],
      showSearch: false,
      customList: [],
      terminateMaterialOpen: false,
      terminateMaterialData: {
        materialIds: [],
        content: '',
        type: 0
      },
      configList: {
        customName: this.$t('order.customerName'),
        endCustomer: this.$t('supplier.endCustomer'),
        productName: this.$t('rfq.productName'),
        endCustomerModel: this.$t('rfq.terminalCustomerModel'),
        productNumber: this.$t('rfq.productModel'),
        requestPaymentMethod: this.$t('rfq.paymentMethodRequired'),
        productLine: this.$t('rfq.application'),
        requestDeliveryMethod: this.$t('rfq.requestedDeliveryMethod'),
        shippingDestination: this.$t('rfq.loadingDestination')
      },
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        projectId: '',
        categories: [],
        condition: '',
        isTempMaterial: false,
        materialCode: '',
        mfg: '',
        mpn: '',
        priceCategories: '',
        projectMaterialStatus: '',
        quotationStatus: '',
        sortBy: '',
        sortField: '',
        sourcing: [],
        creators: []
      },

      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'rfqQuotation',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        editConfig: {
          trigger: 'click',
          mode: 'row',
          showUpdateStatus: true
          // beforeEditMethod: this.beforeEditMethod,
          // editClosed: this.editClosed

        },
        checkboxConfig: {
          reserve: false
        },
        rowConfig: {
          keyField: 'projectMaterialId',
          isHover: true
        },
        columns: [],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      loading: false,
      list: [],
      total: 0,
      quantity: [],
      quantityList: [1, 10, 50, 100, 500, 1000, 2000, 3000, 5000, 10000],
      checkList: [],
      rfqProjectRules: {
        sourcingSources: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ],
        // materialBillManagerList: [
        //   { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        // ],
        eventName: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ],
        purchaseOrg: [
          { required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }
        ],
        expirationDate: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ]
      },
      showRemark: false,
      showMore: false,
      btnLoading: false,
      rfqProject: {
        businessModel: 0,
        customName: localStorage.getItem('customName') || '',
        endCustomer: '',
        endCustomerModel: '',
        eventName: '',
        expirationDate: '',
        id: '',
        inquiryPurpose: '',
        materialBillManagerList: [],
        sourcingSources: '',
        modifyOrderUnits: false,
        productLine: '',
        productName: '',
        productNumber: '',
        projectNo: '',
        purchaseOrg: getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0)[0].id,
        requestDeliveryMethod: '',
        requestPaymentMethod: '',
        shippingDestination: '',
        status: '',
        supplierPriceLadder: false
      },
      quotationCreate: {
        materialIds: [],
        projectId: ''
      },
      disPlayForm: {
        quotationRequirements: '',
        requestReplyDate: dayjs().add(3, 'day'),
        projectId: '',
        displayFieldList: [],
        displayField: ''
      },
      rfqRemarkList: [],
      rfqRemark: '',
      userName: null,
      sourcingSources: [],
      factorySources: [],
      categoryList: [],
      materialVisible: false,
      createVisible: false,
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: this.$t('rfq.batchImportOfMaterialPrices'),
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/rfq/project-materials/import-price-entry-template'
      },

      uploadVisible: false,
      materialRule: {
        priceCategory: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ],
        category: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ],
        materialDescription: [
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ]

      },
      supplierVisible: false,
      mfgList: [],
      mfgMpnList: [],
      selectedMaterial: [],
      supplierForm: {
        query: [],
        supplierName: '',
        checkAll: true,
        pageSize: 10,
        pageNo: 1
      },
      supplierTotal: 0,
      materialSupplierList: [],
      selectedSupplier: [],
      selectedRecord: [],
      logVisible: false,
      disabledItem: [],
      dictTemplateFields: [],
      quotationMaterialQueryParams: {
        materialCode: '',
        pageNo: 1,
        pageSize: 10
        // supplierId: ''
      },
      showFlag: false,
      approveReportVisible: false,
      showApproveReportButton: false
    }
  },

  computed: {
    rfqProjectEdit() {
      return ['new', 'processing'].includes(this.rfqProject.status) || !this.rfqProject.id
    }
  },
  mounted() {
    this.rfqProject.id = this.steps?.projectId
    this.queryParams.projectId = this.steps?.projectId
    this.rfqProject.materialBillManagerList = [this.$store.getters.userId]
    this.rfqProject.sourcingSources = this.$store.getters.userId.toString()
    this.rfqProject.inquiryPurpose = 'New_Project'
    const type = this.$route.query.type
    if (type) {
      this.rfqProject.businessModel = Number(type)
    }
    this.getCategories()
    this.getSourcingSources()
    if (this.rfqProject.id) {
      this.getRfqProject()
      this.getTemplateTabs()
    }
    this.getFactoryAddressByOrgId(null)
  },
  methods: {
    onchangePurchaseOrg(orgId) {
      this.getFactoryAddressByOrgId(orgId);
    },
    getFactoryAddressByOrgId(orgId) {
      if(orgId===null)
      {
        orgId=getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0)[0].id
      }
      this.factorySources.length = 0; // Clear the array
      this.rfqProject.shippingDestination=''
      listPurchaseOrgFactoryFromCache({orgIds:orgId}).then(res => {
        if(res.data.length > 0) {
          this.factorySources.push(...res.data.filter(item => item.address && item.address.trim() !== '')
            .map(item => ({
              id: item.address,
              name: item.address
            })))
          if(this.factorySources.length>0)
          {
            this.rfqProject.shippingDestination=this.factorySources[0].id
          }
        }

      }).catch(err => {
        console.log(err)
      })

    },
    getUserName(value) {
      return getDictData(DICT_TYPE.COMMON_USERS, value).nickname
    },
    // 寻源采购的数据源组装
    getSourcingSources() {
      this.sourcingSources.push(...getDictDatas(DICT_TYPE.RFQ_SOURCING_SOURCES, 0).map(item => ({
        id: item.value,
        name: item.label
      })))
      this.sourcingSources.push(...getDictDatas(DICT_TYPE.COMMON_USERS, 0).map(item => ({
        id: item.id + '',
        name: item.name
      })))
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    getTable() {
      getQuotationReplyDataForPriceEntry({
        ...this.quotationMaterialQueryParams,
        projectId: this.queryParams.projectId
      }).then(res => {
        this.list = res.data.list
        this.total = res.data.total
        this.refreshShowFlag()
      })
    },
    getRfqProject() {
      getRfqProject(
        {
          id: this.queryParams.projectId
        }
      ).then(res => {
        this.rfqProject = res.data
        this.getFileList()
        this.getRemarkList()
      })
    },
    saveProject() {
      this.$refs.rfqProject.validate((valid) => {
        if (valid) {
          saveRfqProject(this.rfqProject).then(async res => {
            const businessCode = this.$route.query.code
            const firstCreate = !this.rfqProject.id
            if (this.rfqRemark && firstCreate) {
              this.submitRemark(res.data.id)
            }
            this.rfqProject = res.data
            this.queryParams.projectId = res.data.id
            if (this.rfqProject.customName) {
              localStorage.setItem('customName', this.rfqProject.customName)
            }
            await this.saveFile()
            if (firstCreate) {
              await this.$tab.closeOpenPage(
                `/rfq/processHome/${this.rfqProject.projectNo}?id=${this.rfqProject.id}&projectNo=${this.rfqProject.projectNo}&code=${businessCode}`
              )
            }
            this.$message.success('操作成功')
          })
        }
      })
    },
    async saveFile() {
      if (this.fileIds.length <= 0) return
      await createFileRelForCreateProject({
        fileIds: this.fileIds,
        businessId: this.rfqProject.id,
        businessType: 'RFQ_PROJECT'
      })
      this.fileIds = []
      this.$message.success(this.$t('common.uploadSucceeded'))
    },

    onRemoveReport(file, fileList) {
      this.approvalReportConfig.uploadFileIds = this.approvalReportConfig.uploadFileIds.filter(item => item !== file.id)
    },
    onSuccessReport(response, file, fileList) {
      this.approvalReportConfig.uploadFileIds.push(response.data.id)
    },

    handleApprove() {
      this.btnLoading = true
      getMaterialByProject({ projectId: this.queryParams.projectId }).then(res => {
        const materialIds = res.data.map(j => j.id)
        this.approvalReportConfig.projectMaterialIds = materialIds

        if (!this.approvalReportConfig.approvalName) {
          this.$message.error(this.$t('rfq.pleaseEnterTheApprovalName'))
          this.btnLoading = false
          return
        }
        this.approvalReportConfig.projectId = this.queryParams.projectId
        checkStatus({
          projectMaterialIds: materialIds.join(',')
        }).then(res => {
          const istrue = res.data
          if (istrue) {
            // 此处做自动保存
            saveQuotationMaterialPriceEntry({
              quotationMaterialInfos: this.list,
              projectId: this.queryParams.projectId
            }).then(res2 => {
              savePriceApprovalPriceEntry({
                ...this.approvalReportConfig
              }
              ).then(res => {
                this.btnLoading = false
                this.$message.success(this.$t('supplier.submittedSuccessfully'))
                this.getTable()
                this.emitter.emit('freshStep', this.queryParams.projectId)
                this.cancelApprove()
              })
            }).catch(e => {
              this.btnLoading = false
            })
          } else {
            this.$message.error(this.$t('rfq.thereAreDataInNonStatusMaterialStatustoBeRecommendedAndRfqStatusquotedSubmissionFailed'))
            this.btnLoading = false
          }
        }).catch(() => {
          this.btnLoading = false
        })
      }).catch(e => {
        this.btnLoading = false
      })
    },
    getFileList() {
      getRfqFile({
        businessId: this.rfqProject.id,
        businessType: 'RFQ_PROJECT'
      }).then(res => {
        this.fileList = res.data.map(item => {
          return {
            name: item.fileName,
            url: item.filePath,
            id: item.id
          }
        })
      })
    },
    onRemove(file, fileList) {
      if (file.id && this.rfqProject.id) {
        delRfqFile({
          id: file.id
        }).then(res => {
          this.$message({
            message: this.$t('common.delSuccess'),
            type: 'success'
          })
          this.getFileList()
        })
      } else {
        console.log(file)
        this.fileIds.splice(this.fileIds.indexOf(file.id), 1)
        this.fileList.splice(this.fileList.find(a => a.id === file.id), 1)
      }
    },
    onSuccess(response, file, fileList) {
      if (this.rfqProject.id) {
        rfqFilePost({
          fileId: response.data.id,
          businessId: this.rfqProject.id,
          businessType: 'RFQ_PROJECT'
        }).then(res => {
          file.id = res.data
          this.$message.success(this.$t('common.uploadSucceeded'))
          this.getFileList()
        })
      } else {
        this.fileList.push({
          name: file.name,
          url: file.url,
          id: response.data.id
        })
        this.fileIds.push(response.data.id)
      }
    },
    resetQuery() {
      this.quotationMaterialQueryParams = {
        pageNo: 1,
        pageSize: 10,
        projectId: this.queryParams.projectId,
        categoryId: null,
        condition: '',
        materialCode: '',
        mfg: '',
        mpn: '',
        priceCategory: '',
        sortBy: '',
        sortField: '',
        supplier: '',
        projectMaterialStatus: ''

      }
      this.getTable()
    },
    init() {
    },
    getRemarkList() {
      getRemarkList({
        projectId: this.rfqProject.id
      }).then(res => {
        this.rfqRemarkList = res.data
      })
    },
    delRemark(id) {
      delRfqRemark({ id }).then(res => {
        this.$message({
          message: this.$t('common.delSuccess'),
          type: 'success'
        })
        this.getRemarkList()
      })
    },
    submitRemark(id) {
      if (!this.rfqRemark) {
        this.$message.error(this.$t('rfq.pleaseEnterComments'))
        return
      }
      createRfqRemark({
        projectId: this.rfqProject.id || id,
        remark: this.rfqRemark
      }).then(res => {
        this.$message({
          message: this.$t('common.savedSuccessfully'),
          type: 'success'
        })
        this.rfqRemark = ''
        this.getRemarkList()
      })
    },
    getSelectMaterial() {
      const selected = this.selectedRecord
      if (selected.length === 0) {
        this.$message.warning(this.$t('rfq.pleaseSelectMaterial'))
        return false
      } else {
        return selected
      }
    },
    showCreate() {
      const selected = this.getSelectMaterial()
      if (selected) {
        const materialIds = [...new Set(selected)]
        // 判断是否已经存在询价单号，如果有则提示
        checkMaterialHasQuote({ ids: materialIds.join(',') }).then(res => {
          if (!res.data) {
            this.$message({
              message: this.$t('rfq.includeSuppliersForWhichRfqHasBeenCreated'),
              type: 'warning',
              duration: 5000
            })
          }
          this.openGenerateQuote(materialIds)
        })
      }
    },
    openGenerateQuote(materialIds) {
      this.quotationCreate.projectId = this.rfqProject.id
      this.quotationCreate.materialIds = materialIds
      this.getDisplayField()
      this.createVisible = true
    },
    submitCreate() {
      this.$refs.quotationCreate.validate((valid) => {
        if (valid) {
          this.disPlayForm.projectId = this.rfqProject.id
          this.disPlayForm.displayField = this.disPlayForm.displayFieldList.join(',')
          this.createLoading = true
          saveRfqProjectDisplay(this.disPlayForm).then(res => {
            this.disPlayForm.id = res.data
            createQuotation(this.quotationCreate).then(pro => {
              this.$message({
                message: this.$t('common.createdSuccessfully'),
                type: 'success'
              })
              this.createLoading = false
              this.createVisible = false
              this.getTable()
              this.emitter.emit('freshStep', this.queryParams.projectId)
            })
          })
        }
      })
    },
    getDisplayField() {
      getRfqProjectDisplay({
        projectId: this.rfqProject.id
      }).then(res => {
        if (res.data) {
          res.data.displayFieldList = res.data.displayField.split(',')
          this.disPlayForm = res.data
          // this.disPlayForm.displayFieldList = []
        }
      })
    },
    submitMaterialSource() {
      const selected = this.getSelectMaterial()
      if (selected) {
        checkSumbitToSource({
          materialIds: selected.join(',')
        }
        ).then(res => {
          if (!res.data) {
            this.$message({
              message: this.$t('rfq.includeItemsThatDoNotNeedToBeSubmittedForSourcing'),
              type: 'warning'
            })
          }
        })

        sumbitToSource({
          materialIds: [...selected].join(',')
        }
        ).then(res => {
          if (res.data !== 0) {
            this.$message({
              message: this.$t('supplier.submittedSuccessfully'),
              type: 'success'
            })
            this.getTable()
            this.emitter.emit('freshStep', this.queryParams.projectId)
          }
        })
      }
    },
    shelveMaterial() {
      const selected = this.getSelectMaterial()
      if (selected) {
        shelveRfqMaterial({
          materialIds: [...selected].join(',')
        }
        ).then(res => {
          this.$message({
            message: this.$t('order.operationSucceeded'),
            type: 'success'
          })
          this.getTable()
          this.emitter.emit('freshStep', this.queryParams.projectId)
        })
      }
    },
    terminateMaterialById(id) {
      this.terminateMaterialData.materialIds = [id]
      this.terminateMaterialData.type = 1
      checkTerminateMaterial(this.terminateMaterialData).then(res => {
        if (res.data) {
          this.terminateMaterialOpen = true
        }
      })
    },
    terminateMaterial() {
      const selected = this.getSelectMaterial()
      if (selected) {
        this.terminateMaterialData.materialIds = [...selected]
        this.terminateMaterialData.type = 0
        checkTerminateMaterial(this.terminateMaterialData).then(res => {
          if (res.data) {
            this.terminateMaterialOpen = true
          }
        })
      }
    },
    delMaterialById(id) {
      const text = this.$t('rfq.areYouSureYouWantToDeleteTheSelectedMaterial')
      this.$modal.confirm(text).then(() => {
        deleteMaterials({
          ids: id
        }
        ).then(res => {
          if (res.data) {
            this.$message({
              message: this.$t('common.delSuccess'),
              type: 'success'
            })
            this.emitter.emit('freshStep', this.queryParams.projectId)
            this.getTable()
          } else {
            this.$message({
              message: this.$t('common.deleteFailed'),
              type: 'error'
            })
          }
        })
      })
    },
    delMaterial() {
      const selected = this.getSelectMaterial()
      if (selected) {
        const text = this.$t('rfq.areYouSureYouWantToDeleteTheSelectedMaterial')
        this.$modal.confirm(text).then(() => {
          deleteMaterialsForPriceEntry({
            ids: [...new Set(selected)].join(','),
            projectId: this.queryParams.projectId
          }
          ).then(res => {
            if (res.data) {
              this.$message({
                message: this.$t('common.delSuccess'),
                type: 'success'
              })
              this.emitter.emit('freshStep', this.queryParams.projectId)
              this.getTable()
              this.clearSelected()
            } else {
              this.$message({
                message: this.$t('common.deleteFailed'),
                type: 'error'
              })
            }
          })
        })
      }
    },
    delSupplier() {
      const selected = this.getSelectMaterial()
      if (selected) {
        const text = this.$t('rfq.areYouSureYouWantToDeleteTheSelectedSupplier')
        this.$modal.confirm(text).then(() => {
          // 判断是否已经存在询价单号，如果有则提示
          const materialIds = [...new Set(selected)].join(',')
          checkMaterialHasQuote({ ids: materialIds }).then(res => {
            if (!res.data) {
              this.$message({
                message: this.$t('rfq.suppliersThatHaveCreatedRfqSheetsWillNotBeDeleted'),
                type: 'warning',
                duration: 5000
              })
            }
            this.callDeleteSupplier(materialIds)
          })
        })
      }
    },
    callDeleteSupplier(materialIds) {
      deleteMaterialsSupplier({
        ids: materialIds
      }
      ).then(res => {
        this.$message({
          message: this.$t('common.delSuccess'),
          type: 'success'
        })
        this.getTable()
      })
    },
    delSupplierById(id, quotationsNo, name) {
      const text = this.$t('rfq.areYouSureYouWantToDeleteTheSelectedSupplier')
      this.$modal.confirm(text).then(() => {
        // 判断是否已经存在询价单号，如果有则提示
        checkMaterialHasQuote({ ids: id }).then(res => {
          if (!res.data) {
            this.$message({
              message: this.$t('rfq.suppliersThatHaveCreatedRfqSheetsWillNotBeDeleted'),
              type: 'warning',
              duration: 5000
            })
          }
          this.callDeleteSupplier(id)
        })
      })
    },
    importTemplate() {
      downloadPriceDirectPriceEntry({}).then(response => {
        this.$download.excel(response, this.$t('rfq.batchCreateMaterialDetailsTemplatexls'))
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createProjectMaterialDetail) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createProjectMaterialDetail.length
      }
      if (data.failureProjectMaterialDetail) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + Object.keys(data.failureProjectMaterialDetail).length
        for (const index in data.failureProjectMaterialDetail) {
          text += '<br />&nbsp;&nbsp;&nbsp;&nbsp; ' + this.$t('rfq.lineNumber') + index + '：' + data.failureProjectMaterialDetail[index]
        }
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getTable()
    },
    submitFileForm() {
      this.$refs.upload.submit()
    },
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const fields = ['status',
        'materialCode', 'transactor', 'materialCodeControl', 'from', 'to', 'level',
        'creator', 'categoryId', 'materialDescription', 'mfg', 'mpn',
        'sourcing', 'moq', 'quantityFrom', 'quantityTo', 'dosage']

      const operationColumn = ['operationLog', 'operation']
      // 物料状态
      // const materialStatus = row['status']
      // 只有新建和待询源的时候，操作列需要合并单元格
      // if (['new', 'to_be_sourced'].includes(materialStatus)) {
      //   operationColumn.push('operation')
      // }
      const cellValue = row[column.property]
      if ((cellValue !== undefined && fields.includes(column.property))) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue && row.projectMaterialId === prevRow.projectMaterialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.projectMaterialId === nextRow.projectMaterialId && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      } else if ((['checkbox', 'seq'].includes(column.type) ||
        operationColumn.includes(column.property)) && row) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && row.projectMaterialId === prevRow.projectMaterialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.projectMaterialId === nextRow.projectMaterialId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    showSupplier() {
      const selected = this.getSelectMaterial()
      if (selected) {
        const materialIds = [...selected]
        checkMaterialCategoryIsNull({
          projectMaterialIds: materialIds.join(',')
        }).then(res => {
          if (res.data === true) {
            this.$message({
              message: this.$t('rfq.pleaseAddCategoryFirst'),
              type: 'warning'
            })
            return
          }
          checkMaterialStatus({
            materialIds: materialIds,
            checkName: 'assign_supplier'
          }).then(res => {
            if (res.data) {
              this.supplierVisible = true
              getRfqMaterialList({
                materialIds: materialIds.join(',')
              }).then(res => {
                this.supplierForm.query = res.data // 默认全选
                this.selectedMaterial = res.data
                this.supplierForm.supplierName = ''
                this.getMaterialSupplierList(true)
              })
            }
          })
        })
      }
    },
    // 选择供应商的品类/MFG/MPN全选
    handleCheckAllSupplierForm(val) {
      if (val) {
        this.supplierForm.query = this.selectedMaterial
      } else {
        this.supplierForm.query = []
      }
      this.getMaterialSupplierList(true)
    },
    // 选择供应商的品类/MFG/MPN的变更选择
    handleCheckedSupplierFormChange() {
      this.supplierForm.pageNo = 1
      this.getMaterialSupplierList(true)
    },
    // 选择供应商搜索的翻页
    handleSupplierFormCurrentChange(val) {
      this.supplierForm.pageNo = val
      this.getMaterialSupplierList(false)
    },
    getMaterialSupplierList(refreshSelected) {
      const brandNames = []
      const categoryIds = []
      let materialIds = []
      this.supplierForm.query.map(item => {
        if (item.categoryId) {
          categoryIds.push(item.categoryId)
        }
        if (item.mfg) {
          brandNames.push(item.mfg)
        }
        materialIds = [...materialIds, ...item.projectMaterialIds]
      })
      getMfgAndSupplierRel({
        brandNames,
        categoryIds,
        ...this.supplierForm
      }).then(res => {
        this.materialSupplierList = res.data.list
        this.supplierTotal = res.data.total
        if (refreshSelected) {
          this.getSelectedSupplier(materialIds)
        }
      })
    },
    checkSupplier(val, item) {
      if (val === false) {
        return
      }
      let projectMaterialIds = []
      this.supplierForm.query.map(a => {
        projectMaterialIds = [...projectMaterialIds, ...a.projectMaterialIds]
      })
      saveMaterialAndSupplierRel({
        projectMaterialIds,
        supplierId: item.id
      }).then(res => {
        this.$message({
          message: this.$t('common.savedSuccessfully'),
          type: 'success'
        })
        this.getMaterialSupplierList(true)
      })
    },
    delMaterialSupplier(item) {
      deleteMaterialsSupplierRel(item).then(res => {
        this.$message({
          message: this.$t('common.delSuccess'),
          type: 'success'
        })
        this.getMaterialSupplierList(true)
      })
    },
    getSelectedSupplier(materialIds) {
      getMaterialSupplierRelByMaterialIds({
        materialIds: [...new Set(materialIds)].join(',')
      }).then(res => {
        this.selectedSupplier = res.data
      })
    },
    showMaterialDetail(row) {
      this.materialVisible = true
      this.$refs.rfqMaterial.showMaterialDetail(row.projectMaterialId)
    },
    downloadMaterialDrawing(materialId, materialCode) {
      downloadMaterialDrawings({ projectMaterialId: materialId }).then(res => {
        this.$download.zip(res, materialCode + '.zip')
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    },
    newMaterial() {
      this.$refs.rfqMaterial.clearMaterial()
      this.materialVisible = true
    },
    // 物料列表的跨页全选框
    checkBoxAllChange({ checked }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.rfqMaterialGrid
      if (checked) {
        if (this.selectedRecord.length + this.total > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.clearCheckboxRow()
          // 当前选中的数据key数组
          const selectIds = grid.getCheckboxRecords().map(item => item.projectMaterialId)
          // 取交集
          const arr = Array.from(new Set([...this.selectedRecord].filter(x => selectIds.includes(x))))
          if (arr.length > 0) {
            arr.forEach((id) => {
              grid.setCheckboxRow(grid.getRowById(id), true)
            })
          }
        } else {
          this.checkAllData(checked)
        }
      } else {
        this.checkAllData(checked)
      }
    },
    checkAllData(checked) {
      getProjectMaterialsQuotationsLists(this.queryParams).then(res => {
        if (checked) {
          this.selectedRecord = Array.from(new Set([...this.selectedRecord, ...res.data]))
        } else {
          this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => !res.data.includes(x))))
        }
      })
    },
    // 物料列表的单个选择框
    checkBoxChange({ checked, row }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.rfqMaterialGrid
      if (checked) {
        if (this.selectedRecord.length + 1 > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.setCheckboxRow(grid.getRowById(row.projectMaterialId), false)
        } else {
          this.selectedRecord.push(row.projectMaterialId)
        }
      } else {
        this.selectedRecord.splice(this.selectedRecord.indexOf(row.projectMaterialId), 1)
      }
    },
    clearSelected() {
      const grid = this.$refs.rfqMaterialGrid
      this.selectedRecord = []
      grid.clearCheckboxRow()
    },
    downloadExcel() {
      this.$modal.confirm(this.$t('rfq.areYouSureToExportAllMaterialData')).then(() => {
        this.exportLoading = true
        return downloadMaterialQuote(this.queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('rfq.rfqMaterialxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    closeSupplier(close) {
      this.getTable()
      close()
    },
    closeSupplierEvent() {
      this.supplierVisible = false
      this.getTable()
    },
    showOperationLog(obj1, obj2) {
      this.logVisible = true
      this.businessId = obj1
      this.businessType = obj2
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.url) {
        window.open(file.url)
      }
    },
    download(url) {
      window.open(url)
    },
    getTemplateTabs() {
      this.getTemplateFields()
      this.getTable()
    },
    getTemplateFields() {
      getFieldsByTemplatePriceEntry({
        templateId: this.activeCategory
      }).then(res => {
        this.dictTemplateFields = []
        this.materialField = []
        this.noMergeField = []
        this.girdOption.columns = res.data.filter(a => !a.hidden).sort((a, b) => a.sort - b.sort).map((item, index) => {
          if (item.merge === 0) {
            this.materialField.push(item.fieldCode)
          } else if (item.merge === 2) {
            this.noMergeField.push(item.fieldCode)
          }
          const common = {
            title: item.fieldName,
            field: item.fieldCode,
            visible: true,
            slots: {},
            width: 150,
            fixed: index === 0 ? 'left' : ''
          }
          // 物料编码需要加图纸下载
          if (item.fieldDictType || item.modify || item.fieldCode === 'materialCode' || item.fieldCode === 'approvalNumbers') {
            this.dictTemplateFields.push({
              type: item.fieldDictType?.slice(item.fieldDictType.indexOf(':') + 1),
              fieldType: item.fieldType,
              field: item.fieldCode,
              modify: item.modify,
              required: item.required
            })
            // if (item.modify) {
            //   common.slots.edit = item.fieldCode + 'Edit'
            //   common.editRender = {}
            // }
            if (item.required) {
              common.slots.header = 'requiredField'
            }
            common.slots.default = item.fieldCode
          }
          return common
        })
        this.girdOption.columns.unshift({ title: '', type: 'checkbox', visible: true, width: 30, fixed: 'left' })
      })
    },
    disabledFields(row, fields) {
      if (this.list.filter(j => ['completed', 'pending_approval', 'approved'].includes(j.projectMaterialStatus)).length > 0) {
        return true
      }
      return false
    },
    changeField(field, value) {
      console.log(field)
      // todo 计算价格
    },
    changeTaxRate() {
    },
    saveQuotation() {
      saveQuotationMaterialPriceEntry({
        quotationMaterialInfos: this.list,
        projectId: this.queryParams.projectId
      }).then(res => {
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.getTable()
      })
    },
    refreshShowFlag() {
      if (this.list.length <= 0) {
        this.showFlag = true
        this.showApproveReportButton = false
        return
      }
      if (this.list.filter(j => ['completed', 'pending_approval', 'approved'].includes(j.projectMaterialStatus)).length > 0) {
        this.showFlag = false
        this.showApproveReportButton = false
        return
      }
      this.showApproveReportButton = true
      this.showFlag = true
      return
    },
    showReportConfig() {
      if (this.list.length <= 0) {
        this.$message.error(this.$t('rfq.pleaseImportTheMaterialsFirst'))
        return
      }
      this.approveReportVisible = true
    },
    cancelApprove() {
      this.approveReportVisible = false
      this.resetApprove()
    },
    resetApprove() {
      this.approvalReportConfig = {
        approvalMaterialSupplierRelIds: [],
        approvalName: '',
        approvalNo: '',
        attachmentRelFileIds: [],
        files: '',
        flag: '',
        // id: 0,
        // status: 'pending_approval',
        previewRecordId: 0,
        priceApprovalApproves: [],
        projectId: this.queryParams.projectId,
        quotationsTemplate: Number(this.queryParams.templateId),
        recommendRemark: '',
        reportId: null,
        urgent: false
      }
    }
  }
})
</script>
<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

.oneLineMaterialItem {
  width: 100%;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.commonFormItem {
  width: 33.3%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

  ::v-deep .el-form-item__content {
    width: calc(100% - 170px);
  }
}

.commonFormItemLeft {
  width: 50%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

  ::v-deep .el-form-item__content {
    width: calc(100% - 166px);
  }
}

.commonMaterialItem {
  width: 33%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
}

::v-deep .el-form--label-top .el-form-item__label {
  padding-bottom: 0;
}

.rfq-create-upload {
  margin: 10px 0
}

.rfq-create {
  margin-top: 15px;
}

.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.materialItem {
  width: 178px;
}

.searchValue {
  width: 100%;
}

.rfqMaterial {
  .mainTab {
    margin-bottom: 15px;
  }
}

.configItem {
  margin: 5px 0;
  width: 50%;

}

.configValue {
  position: relative;
  top: 4px;
  margin-top: 10px;
  width: 375px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

::v-deep .labelTitle {
  font-weight: bolder !important;
  color: black;
  width: 75px;
  font-size: 13px;
}

::v-deep .requiredLabelTitle::before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

::v-deep .requiredLabelTitle {
  font-weight: bolder !important;
  color: black;
  width: 80px;
  font-size: 13px;

}

.fixedBottom {
  position: fixed;
  width: calc(100% - 1100px);
  bottom: 20px;
  display: flex;
  justify-content: center;
  margin-top: 40px;
  right: 550px;
  z-index: 999;
}
</style>
