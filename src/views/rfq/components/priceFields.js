export const quotationDetails = {
  undefined: { visible: true, sort: 0 },
  materialCode: { visible: true, sort: 1 },
  materialDescription: { visible: true, sort: 2 },
  specifications: { visible: true, sort: 3 },
  supplierName: { visible: true, sort: 4 },
  quotationsCount: { visible: true, sort: 6 },
  conversionUnitPriceTax: { visible: true, sort: 7 },
  currency: { visible: true, sort: 8 },
  exchangeRate: { visible: true, sort: 9 },
  originalUnitPriceTax: { visible: true, sort: 10 },
  originalCurrency: { visible: true, sort: 11 },
  vsHistoricalRateChange: { visible: true, sort: 12 },
  recommend: { visible: true, sort: 16 },
  recommendedPriceExpires: { visible: true, sort: 17 },
  categoryId: { visible: false, sort: 19 },
  materialStatus: { visible: false, sort: 20 },
  mfg: { visible: false, sort: 21 },
  mpn: { visible: false, sort: 22 },
  dosage: { visible: false, sort: 23 },
  basicUnit: { visible: false, sort: 24 },
  quantityFrom: { visible: false, sort: 25 },
  quantityTo: { visible: false, sort: 26 },
  historyPrice: { visible: false, sort: 27 },
  historySupplierName: { visible: false, sort: 28 },
  historyCurrency: { visible: false, sort: 29 },
  quotationQuantityFrom: { visible: false, sort: 30 },
  quotationQuantityTo: { visible: false, sort: 31 },
  conversionUnitPriceWithoutTax: { visible: false, sort: 32 },
  taxRate: { visible: false, sort: 33 },
  moq: { visible: false, sort: 34 },
  originalUnitPriceWithoutTax: { visible: false, sort: 35 },
  priceValidUntil: { visible: false, sort: 36 },
  deliveryPeriod: { visible: false, sort: 37 },
  paymentMethod: { visible: false, sort: 38 },
  orderUnit: { visible: false, sort: 39 },
  lowestUnitPrice: { visible: false, sort: 40 },
  lowestUnitPriceCurrency: { visible: false, sort: 41 },
  lowestSupplierName: { visible: false, sort: 42 },
  recommendedUnitPriceTax: { visible: false, sort: 43 },
  recommendedVsHistoricalPriceChangeRate: { visible: false, sort: 44 },
  recommendationVsHistoricalRateChange: { visible: false, sort: 45 },
  recommendedSupplierName: { visible: false, sort: 46 },
  proportion: { visible: false, sort: 47 },
  priceUnit: { visible: false, sort: 48 },
  unitPriceIncludesTaxPriceUnit: { visible: false, sort: 49 },
  unitPriceWithoutTaxPriceUnit: { visible: false, sort: 50 },
  factoryLocation: { visible: false, sort: 51 },
  acceptanceMethod: { visible: false, sort: 52 },
  arrivalPriceWithoutTax: { visible: false, sort: 53 },
  grossWeight: { visible: false, sort: 54 },
  moldCurrency: { visible: false, sort: 55 },
  coreMaterial: { visible: false, sort: 56 },
  assemblyCost: { visible: false, sort: 57 },
  w_contract_price: { visible: false, sort: 58 },
  rawMaterial: { visible: false, sort: 59 },
  rawMaterialPirice: { visible: false, sort: 60 },
  packagingCosts: { visible: false, sort: 61 },
  managementFees: { visible: false, sort: 62 },
  processingCost: { visible: false, sort: 63 },
  processingTechnology: { visible: false, sort: 64 },
  profit: { visible: false, sort: 65 },
  accessoriesPrice: { visible: false, sort: 66 },
  transportationCosts: { visible: false, sort: 67 },
  materialFee: { visible: false, sort: 68 },
  surfaceTreatmentProcessMetal: { visible: false, sort: 69 },
  surfaceTreatmentFee: { visible: false, sort: 70 },
  scrapRate: { visible: false, sort: 71 },
  productWeight: { visible: false, sort: 72 },
  punchTonnage: { visible: false, sort: 73 },
  strokesMin: { visible: false, sort: 74 },
  stampingFee: { visible: false, sort: 75 },
  heatTreatmentProcess: { visible: false, sort: 76 },
  heatTreatmentFee: { visible: false, sort: 77 },
  outsourcingFee: { visible: false, sort: 78 },
  toolFee: { visible: false, sort: 79 },
  surfaceTreatmentProcessPlasticParts: { visible: false, sort: 80 },
  TonnageT: { visible: false, sort: 81 },
  consumptionKG: { visible: false, sort: 82 },
  feedback: { visible: false, sort: 83 },
  headWeightKG: { visible: false, sort: 84 },
  insertFee: { visible: false, sort: 85 },
  subtotal: { visible: false, sort: 86 },
  numberOfHoles: { visible: false, sort: 87 },
  dieCastingFee: { visible: false, sort: 88 },
  cycle: { visible: false, sort: 89 },
  injectionMoldingFee: { visible: false, sort: 90 },
  cncProcessingFee: { visible: false, sort: 91 },
  cncProcessingTimeMinutes: { visible: false, sort: 92 },
  platingFee: { visible: false, sort: 93 },
  dustingFee: { visible: false, sort: 94 },
  generalProcessingFee: { visible: false, sort: 95 },
  ordinaryMachiningTimeMin: { visible: false, sort: 96 },
  screenPrintingFee: { visible: false, sort: 97 },
  oxidationFee: { visible: false, sort: 98 },
  materialBrand: { visible: false, sort: 99 },
  materialBasePriceUnitGKgMm: { visible: false, sort: 100 },
  heightCm: { visible: false, sort: 101 },
  widthCm: { visible: false, sort: 102 },
  lengthCm: { visible: false, sort: 103 },
  mountingPaper: { visible: false, sort: 104 },
  moldingFee: { visible: false, sort: 105 },
  openVGroovePlayAngle: { visible: false, sort: 106 },
  pitPaperGrayBoard: { visible: false, sort: 107 },
  facialTissueYuan: { visible: false, sort: 108 },
  lining: { visible: false, sort: 109 },
  numberOfLayouts: { visible: false, sort: 110 },
  beer: { visible: false, sort: 111 },
  narketBasePricePaper: { visible: false, sort: 112 },
  marketBasePriceLinedCardboard: { visible: false, sort: 113 },
  printingFee: { visible: false, sort: 114 },
  marketBasePricePitPaperGrayPaper: { visible: false, sort: 115 },
  versionNumber: { visible: false, sort: 116 },
  coverFee: { visible: false, sort: 117 },
  textMaterial: { visible: false, sort: 118 },
  textSize: { visible: false, sort: 119 },
  contentUnitPricePage: { visible: false, sort: 120 },
  contentPages: { visible: false, sort: 121 },
  bindingMethod: { visible: false, sort: 122 },
  bindingCosts: { visible: false, sort: 123 },
  projectCosts: { visible: false, sort: 124 },
  moldMaterial: { visible: false, sort: 125 },
  moldMaterialFee: { visible: false, sort: 126 },
  dieLife: { visible: false, sort: 127 },
  moldWeight: { visible: false, sort: 128 },
  moldMaximumOuterDimension: { visible: false, sort: 129 },
  cavities: { visible: false, sort: 130 },
  designFee: { visible: false, sort: 131 },
  tryoutFeeTime: { visible: false, sort: 132 },
  hardwareAccessoriesFee: { visible: false, sort: 133 },
  surfaceTreatmentProcessPcb: { visible: false, sort: 134 },
  moldCost: { visible: false, sort: 135 },
  pcbSinglePieceSizeMmMm: { visible: false, sort: 136 },
  pcbPanelSizeMmMm: { visible: false, sort: 137 },
  layers: { visible: false, sort: 138 },
  moldPaymentMethod: { visible: false, sort: 139 },
  numberOfJigsawPcbsPcsPanel: { visible: false, sort: 140 },
  samplePrice: { visible: false, sort: 141 },
  sampleLeadTimeDays: { visible: false, sort: 142 },
  materialCodeControl: { visible: false, sort: 143 },
  projectMaterialMfg: { visible: false, sort: 144 },
  projectMaterialMpn: { visible: false, sort: 145 },
  priceCategory: { visible: false, sort: 146 },
  materialType: { visible: false, sort: 147 },
  level: { visible: false, sort: 148 },
  homemadeSourcing: { visible: false, sort: 149 },
  version: { visible: false, sort: 150 },
  model: { visible: false, sort: 151 },
  projectMaterialMpq: { visible: false, sort: 152 },
  targetPrice: { visible: false, sort: 153 },
  brand: { visible: false, sort: 154 },
  currentPurchasePrice: { visible: false, sort: 155 },
  color: { visible: false, sort: 156 },
  estimatedAnnualUsage: { visible: false, sort: 157 },
  endCustomerPartNumber: { visible: false, sort: 158 },
  purchasingUnit: { visible: false, sort: 159 },
  sourcing: { visible: false, sort: 160 },
  temporaryMaterial: { visible: false, sort: 161 },
  materialHierarchy: { visible: false, sort: 162 },
  technologicalRequirements: { visible: false, sort: 163 },
  surfaceTreatment: { visible: false, sort: 164 },
  materialRemarks: { visible: false, sort: 165 },
  transactor: { visible: false, sort: 166 },
  quantityLadderStatus: { visible: false, sort: 167 },
  materialDrawing: { visible: false, sort: 168 },
  supplierCode: { visible: false, sort: 169 },
  erpSynchronousMessages: { visible: false, sort: 170 },
  approvalNumbers: { visible: false, sort: 171 },
  approvalCount: { visible: false, sort: 172 },
  projectMaterialStatus: { visible: false, sort: 173 },
  testCosts: { visible: false, sort: 174 },
  plateThickness: { visible: false, sort: 175 },
  throughHoleMethod: { visible: false, sort: 176 },
  materialCost: { visible: false, sort: 177 },
  sendRecommend: { visible: true, sort: 178 },
  orderUnitQuantity: { visible: false, sort: 179 },
  quotationsStatus: { visible: false, sort: 180 },
  totalFactoryCosts: { visible: true, sort: 27 },
  productProfit: { visible: true, sort: 28 },
  totalManufacturingCost: { visible: true, sort: 29 },
  procurementCost: { visible: true, sort: 31 },
  internalProcessingCost: { visible: true, sort: 33 },
  fuelPowerCosts: { visible: true, sort: 34 },
  laborWages: { visible: true, sort: 35 },
  depreciationExpenses: { visible: true, sort: 36 },
  toolingMoldsCost: { visible: true, sort: 37 },
  auxiliaryMaterialCost: { visible: true, sort: 38 },
  laborProtectionFees: { visible: true, sort: 39 },
  repairCosts: { visible: true, sort: 40 },
  lowEasyConsume: { visible: true, sort: 41 },
  periodExpenses: { visible: true, sort: 42 },
  salesExpenses: { visible: true, sort: 44 },
  financialExpenses: { visible: true, sort: 45 },
  logisticsCosts: { visible: true, sort: 46 },
  ictTestCost: { visible: true, sort: 29 },
  componentLossCost: { visible: true, sort: 30 },
  allocationEquipmentCost: { visible: true, sort: 31 }
}
export const clauseDetails = {
  undefined: { visible: true, sort: 0 },
  materialCode: { visible: true, sort: 1 },
  materialDescription: { visible: true, sort: 2 },
  specifications: { visible: true, sort: 3 },
  dosage: { visible: true, sort: 4 },
  basicUnit: { visible: true, sort: 5 },
  supplierName: { visible: true, sort: 6 },
  moq: { visible: true, sort: 8 },
  priceValidUntil: { visible: true, sort: 9 },
  deliveryPeriod: { visible: true, sort: 10 },
  paymentMethod: { visible: true, sort: 11 },
  factoryLocation: { visible: true, sort: 12 },
  acceptanceMethod: { visible: true, sort: 13 },

  quotationsCount: { visible: false, sort: 14 },
  conversionUnitPriceTax: { visible: false, sort: 15 },
  currency: { visible: false, sort: 16 },
  exchangeRate: { visible: false, sort: 17 },
  originalUnitPriceTax: { visible: false, sort: 18 },
  originalCurrency: { visible: false, sort: 19 },
  vsHistoricalRateChange: { visible: false, sort: 20 },
  recommend: { visible: false, sort: 23 },
  priceValidBegin: { visible: true, sort: 24 },
  recommendedPriceExpires: { visible: false, sort: 25 },
  categoryId: { visible: false, sort: 27 },
  materialStatus: { visible: false, sort: 28 },
  mfg: { visible: false, sort: 29 },
  mpn: { visible: false, sort: 30 },
  quantityFrom: { visible: true, sort: 31 },
  quantityTo: { visible: true, sort: 32 },
  historyPrice: { visible: false, sort: 33 },
  historySupplierName: { visible: false, sort: 34 },
  historyCurrency: { visible: false, sort: 35 },
  quotationQuantityFrom: { visible: false, sort: 36 },
  quotationQuantityTo: { visible: false, sort: 37 },
  conversionUnitPriceWithoutTax: { visible: false, sort: 38 },
  taxRate: { visible: false, sort: 39 },
  originalUnitPriceWithoutTax: { visible: false, sort: 40 },
  orderUnit: { visible: false, sort: 41 },
  lowestUnitPrice: { visible: false, sort: 42 },
  lowestUnitPriceCurrency: { visible: false, sort: 43 },
  lowestSupplierName: { visible: false, sort: 44 },
  recommendedUnitPriceTax: { visible: false, sort: 45 },
  recommendedVsHistoricalPriceChangeRate: { visible: false, sort: 46 },
  recommendationVsHistoricalRateChange: { visible: false, sort: 47 },
  recommendedSupplierName: { visible: false, sort: 48 },
  proportion: { visible: false, sort: 49 },
  priceUnit: { visible: false, sort: 50 },
  unitPriceIncludesTaxPriceUnit: { visible: false, sort: 51 },
  unitPriceWithoutTaxPriceUnit: { visible: false, sort: 52 },
  arrivalPriceWithoutTax: { visible: false, sort: 53 },
  grossWeight: { visible: false, sort: 54 },
  moldCurrency: { visible: false, sort: 55 },
  coreMaterial: { visible: false, sort: 56 },
  assemblyCost: { visible: false, sort: 57 },
  w_contract_price: { visible: false, sort: 58 },
  rawMaterial: { visible: false, sort: 59 },
  rawMaterialPirice: { visible: false, sort: 60 },
  packagingCosts: { visible: false, sort: 61 },
  managementFees: { visible: false, sort: 62 },
  processingCost: { visible: false, sort: 63 },
  processingTechnology: { visible: false, sort: 64 },
  profit: { visible: false, sort: 65 },
  accessoriesPrice: { visible: false, sort: 66 },
  transportationCosts: { visible: false, sort: 67 },
  materialFee: { visible: false, sort: 68 },
  surfaceTreatmentProcessMetal: { visible: false, sort: 69 },
  surfaceTreatmentFee: { visible: false, sort: 70 },
  scrapRate: { visible: false, sort: 71 },
  productWeight: { visible: false, sort: 72 },
  punchTonnage: { visible: false, sort: 73 },
  strokesMin: { visible: false, sort: 74 },
  stampingFee: { visible: false, sort: 75 },
  heatTreatmentProcess: { visible: false, sort: 76 },
  heatTreatmentFee: { visible: false, sort: 77 },
  outsourcingFee: { visible: false, sort: 78 },
  toolFee: { visible: false, sort: 79 },
  surfaceTreatmentProcessPlasticParts: { visible: false, sort: 80 },
  TonnageT: { visible: false, sort: 81 },
  consumptionKG: { visible: false, sort: 82 },
  feedback: { visible: false, sort: 83 },
  headWeightKG: { visible: false, sort: 84 },
  insertFee: { visible: false, sort: 85 },
  subtotal: { visible: false, sort: 86 },
  numberOfHoles: { visible: false, sort: 87 },
  dieCastingFee: { visible: false, sort: 88 },
  cycle: { visible: false, sort: 89 },
  injectionMoldingFee: { visible: false, sort: 90 },
  cncProcessingFee: { visible: false, sort: 91 },
  cncProcessingTimeMinutes: { visible: false, sort: 92 },
  platingFee: { visible: false, sort: 93 },
  dustingFee: { visible: false, sort: 94 },
  generalProcessingFee: { visible: false, sort: 95 },
  ordinaryMachiningTimeMin: { visible: false, sort: 96 },
  screenPrintingFee: { visible: false, sort: 97 },
  oxidationFee: { visible: false, sort: 98 },
  materialBrand: { visible: false, sort: 99 },
  materialBasePriceUnitGKgMm: { visible: false, sort: 100 },
  heightCm: { visible: false, sort: 101 },
  widthCm: { visible: false, sort: 102 },
  lengthCm: { visible: false, sort: 103 },
  mountingPaper: { visible: false, sort: 104 },
  moldingFee: { visible: false, sort: 105 },
  openVGroovePlayAngle: { visible: false, sort: 106 },
  pitPaperGrayBoard: { visible: false, sort: 107 },
  facialTissueYuan: { visible: false, sort: 108 },
  lining: { visible: false, sort: 109 },
  numberOfLayouts: { visible: false, sort: 110 },
  beer: { visible: false, sort: 111 },
  narketBasePricePaper: { visible: false, sort: 112 },
  marketBasePriceLinedCardboard: { visible: false, sort: 113 },
  printingFee: { visible: false, sort: 114 },
  marketBasePricePitPaperGrayPaper: { visible: false, sort: 115 },
  versionNumber: { visible: false, sort: 116 },
  coverFee: { visible: false, sort: 117 },
  textMaterial: { visible: false, sort: 118 },
  textSize: { visible: false, sort: 119 },
  contentUnitPricePage: { visible: false, sort: 120 },
  contentPages: { visible: false, sort: 121 },
  bindingMethod: { visible: false, sort: 122 },
  bindingCosts: { visible: false, sort: 123 },
  projectCosts: { visible: false, sort: 124 },
  moldMaterial: { visible: false, sort: 125 },
  moldMaterialFee: { visible: false, sort: 126 },
  dieLife: { visible: false, sort: 127 },
  moldWeight: { visible: false, sort: 128 },
  moldMaximumOuterDimension: { visible: false, sort: 129 },
  cavities: { visible: false, sort: 130 },
  designFee: { visible: false, sort: 131 },
  tryoutFeeTime: { visible: false, sort: 132 },
  hardwareAccessoriesFee: { visible: false, sort: 133 },
  surfaceTreatmentProcessPcb: { visible: false, sort: 134 },
  moldCost: { visible: false, sort: 135 },
  pcbSinglePieceSizeMmMm: { visible: false, sort: 136 },
  pcbPanelSizeMmMm: { visible: false, sort: 137 },
  layers: { visible: false, sort: 138 },
  moldPaymentMethod: { visible: false, sort: 139 },
  numberOfJigsawPcbsPcsPanel: { visible: false, sort: 140 },
  samplePrice: { visible: false, sort: 141 },
  sampleLeadTimeDays: { visible: false, sort: 142 },
  materialCodeControl: { visible: false, sort: 143 },
  projectMaterialMfg: { visible: false, sort: 144 },
  projectMaterialMpn: { visible: false, sort: 145 },
  priceCategory: { visible: false, sort: 146 },
  materialType: { visible: false, sort: 147 },
  level: { visible: false, sort: 148 },
  homemadeSourcing: { visible: false, sort: 149 },
  version: { visible: false, sort: 150 },
  model: { visible: false, sort: 151 },
  projectMaterialMpq: { visible: false, sort: 152 },
  targetPrice: { visible: false, sort: 153 },
  brand: { visible: false, sort: 154 },
  currentPurchasePrice: { visible: false, sort: 155 },
  color: { visible: false, sort: 156 },
  estimatedAnnualUsage: { visible: false, sort: 157 },
  endCustomerPartNumber: { visible: false, sort: 158 },
  purchasingUnit: { visible: false, sort: 159 },
  sourcing: { visible: false, sort: 160 },
  temporaryMaterial: { visible: false, sort: 161 },
  materialHierarchy: { visible: false, sort: 162 },
  technologicalRequirements: { visible: false, sort: 163 },
  surfaceTreatment: { visible: false, sort: 164 },
  materialRemarks: { visible: false, sort: 165 },
  transactor: { visible: false, sort: 166 },
  quantityLadderStatus: { visible: false, sort: 167 },
  materialDrawing: { visible: false, sort: 168 },
  supplierCode: { visible: false, sort: 169 },
  erpSynchronousMessages: { visible: false, sort: 170 },
  approvalNumbers: { visible: false, sort: 171 },
  approvalCount: { visible: false, sort: 172 },
  projectMaterialStatus: { visible: false, sort: 173 },
  testCosts: { visible: false, sort: 174 },
  plateThickness: { visible: false, sort: 175 },
  throughHoleMethod: { visible: false, sort: 176 },
  materialCost: { visible: false, sort: 177 },
  sendRecommend: { visible: true, sort: 178 },
  orderUnitQuantity: { visible: false, sort: 179 },
  quotationsStatus: { visible: false, sort: 180 },
  totalFactoryCosts: { visible: true, sort: 27 },
  productProfit: { visible: true, sort: 28 },
  totalManufacturingCost: { visible: true, sort: 29 },
  procurementCost: { visible: true, sort: 31 },
  internalProcessingCost: { visible: true, sort: 33 },
  fuelPowerCosts: { visible: true, sort: 34 },
  laborWages: { visible: true, sort: 35 },
  depreciationExpenses: { visible: true, sort: 36 },
  toolingMoldsCost: { visible: true, sort: 37 },
  auxiliaryMaterialCost: { visible: true, sort: 38 },
  laborProtectionFees: { visible: true, sort: 39 },
  repairCosts: { visible: true, sort: 40 },
  lowEasyConsume: { visible: true, sort: 41 },
  periodExpenses: { visible: true, sort: 42 },
  salesExpenses: { visible: true, sort: 44 },
  financialExpenses: { visible: true, sort: 45 },
  logisticsCosts: { visible: true, sort: 46 },
  ictTestCost: { visible: true, sort: 29 },
  componentLossCost: { visible: true, sort: 30 },
  allocationEquipmentCost: { visible: true, sort: 31 }
}
export const recommendedConclusion = {
  undefined: { visible: true, sort: 0 },
  materialCode: { visible: true, sort: 1 },
  materialDescription: { visible: true, sort: 2 },
  specifications: { visible: true, sort: 3 },
  historyPrice: { visible: true, sort: 4 },
  historySupplierName: { visible: true, sort: 5 },
  historyCurrency: { visible: true, sort: 6 },
  supplierName: { visible: true, sort: 7 },
  conversionUnitPriceTax: { visible: true, sort: 9 },
  recommendedUnitPriceTax: { visible: true, sort: 12 },
  recommendationVsHistoricalRateChange: { visible: true, sort: 13 },
  recommendedSupplierName: { visible: true, sort: 14 },
  recommend: { visible: true, sort: 15 },
  priceValidBegin: { visible: true, sort: 15 },
  recommendedPriceExpires: { visible: true, sort: 16 },
  quotationsCount: { visible: false, sort: 17 },
  currency: { visible: false, sort: 18 },
  exchangeRate: { visible: false, sort: 19 },
  originalUnitPriceTax: { visible: false, sort: 20 },
  originalCurrency: { visible: false, sort: 21 },
  vsHistoricalRateChange: { visible: false, sort: 22 },
  categoryId: { visible: false, sort: 25 },
  materialStatus: { visible: false, sort: 26 },
  mfg: { visible: false, sort: 27 },
  mpn: { visible: false, sort: 28 },
  dosage: { visible: false, sort: 29 },
  basicUnit: { visible: false, sort: 30 },
  quantityFrom: { visible: false, sort: 31 },
  quantityTo: { visible: false, sort: 32 },
  quotationQuantityFrom: { visible: true, sort: 33 },
  quotationQuantityTo: { visible: true, sort: 34 },
  conversionUnitPriceWithoutTax: { visible: false, sort: 35 },
  taxRate: { visible: false, sort: 36 },
  moq: { visible: false, sort: 37 },
  originalUnitPriceWithoutTax: { visible: false, sort: 38 },
  priceValidUntil: { visible: false, sort: 39 },
  deliveryPeriod: { visible: false, sort: 40 },
  paymentMethod: { visible: false, sort: 41 },
  orderUnit: { visible: false, sort: 42 },
  lowestUnitPrice: { visible: false, sort: 43 },
  lowestUnitPriceCurrency: { visible: false, sort: 44 },
  lowestSupplierName: { visible: false, sort: 45 },
  recommendedVsHistoricalPriceChangeRate: { visible: false, sort: 46 },
  proportion: { visible: false, sort: 47 },
  priceUnit: { visible: false, sort: 48 },
  unitPriceIncludesTaxPriceUnit: { visible: false, sort: 49 },
  unitPriceWithoutTaxPriceUnit: { visible: false, sort: 50 },
  factoryLocation: { visible: false, sort: 51 },
  acceptanceMethod: { visible: false, sort: 52 },
  arrivalPriceWithoutTax: { visible: false, sort: 53 },
  grossWeight: { visible: false, sort: 54 },
  moldCurrency: { visible: false, sort: 55 },
  coreMaterial: { visible: false, sort: 56 },
  assemblyCost: { visible: false, sort: 57 },
  w_contract_price: { visible: false, sort: 58 },
  rawMaterial: { visible: false, sort: 59 },
  rawMaterialPirice: { visible: false, sort: 60 },
  packagingCosts: { visible: false, sort: 61 },
  managementFees: { visible: false, sort: 62 },
  processingCost: { visible: false, sort: 63 },
  processingTechnology: { visible: false, sort: 64 },
  profit: { visible: false, sort: 65 },
  accessoriesPrice: { visible: false, sort: 66 },
  transportationCosts: { visible: false, sort: 67 },
  materialFee: { visible: false, sort: 68 },
  surfaceTreatmentProcessMetal: { visible: false, sort: 69 },
  surfaceTreatmentFee: { visible: false, sort: 70 },
  scrapRate: { visible: false, sort: 71 },
  productWeight: { visible: false, sort: 72 },
  punchTonnage: { visible: false, sort: 73 },
  strokesMin: { visible: false, sort: 74 },
  stampingFee: { visible: false, sort: 75 },
  heatTreatmentProcess: { visible: false, sort: 76 },
  heatTreatmentFee: { visible: false, sort: 77 },
  outsourcingFee: { visible: false, sort: 78 },
  toolFee: { visible: false, sort: 79 },
  surfaceTreatmentProcessPlasticParts: { visible: false, sort: 80 },
  TonnageT: { visible: false, sort: 81 },
  consumptionKG: { visible: false, sort: 82 },
  feedback: { visible: false, sort: 83 },
  headWeightKG: { visible: false, sort: 84 },
  insertFee: { visible: false, sort: 85 },
  subtotal: { visible: false, sort: 86 },
  numberOfHoles: { visible: false, sort: 87 },
  dieCastingFee: { visible: false, sort: 88 },
  cycle: { visible: false, sort: 89 },
  injectionMoldingFee: { visible: false, sort: 90 },
  cncProcessingFee: { visible: false, sort: 91 },
  cncProcessingTimeMinutes: { visible: false, sort: 92 },
  platingFee: { visible: false, sort: 93 },
  dustingFee: { visible: false, sort: 94 },
  generalProcessingFee: { visible: false, sort: 95 },
  ordinaryMachiningTimeMin: { visible: false, sort: 96 },
  screenPrintingFee: { visible: false, sort: 97 },
  oxidationFee: { visible: false, sort: 98 },
  materialBrand: { visible: false, sort: 99 },
  materialBasePriceUnitGKgMm: { visible: false, sort: 100 },
  heightCm: { visible: false, sort: 101 },
  widthCm: { visible: false, sort: 102 },
  lengthCm: { visible: false, sort: 103 },
  mountingPaper: { visible: false, sort: 104 },
  moldingFee: { visible: false, sort: 105 },
  openVGroovePlayAngle: { visible: false, sort: 106 },
  pitPaperGrayBoard: { visible: false, sort: 107 },
  facialTissueYuan: { visible: false, sort: 108 },
  lining: { visible: false, sort: 109 },
  numberOfLayouts: { visible: false, sort: 110 },
  beer: { visible: false, sort: 111 },
  narketBasePricePaper: { visible: false, sort: 112 },
  marketBasePriceLinedCardboard: { visible: false, sort: 113 },
  printingFee: { visible: false, sort: 114 },
  marketBasePricePitPaperGrayPaper: { visible: false, sort: 115 },
  versionNumber: { visible: false, sort: 116 },
  coverFee: { visible: false, sort: 117 },
  textMaterial: { visible: false, sort: 118 },
  textSize: { visible: false, sort: 119 },
  contentUnitPricePage: { visible: false, sort: 120 },
  contentPages: { visible: false, sort: 121 },
  bindingMethod: { visible: false, sort: 122 },
  bindingCosts: { visible: false, sort: 123 },
  projectCosts: { visible: false, sort: 124 },
  moldMaterial: { visible: false, sort: 125 },
  moldMaterialFee: { visible: false, sort: 126 },
  dieLife: { visible: false, sort: 127 },
  moldWeight: { visible: false, sort: 128 },
  moldMaximumOuterDimension: { visible: false, sort: 129 },
  cavities: { visible: false, sort: 130 },
  designFee: { visible: false, sort: 131 },
  tryoutFeeTime: { visible: false, sort: 132 },
  hardwareAccessoriesFee: { visible: false, sort: 133 },
  surfaceTreatmentProcessPcb: { visible: false, sort: 134 },
  moldCost: { visible: false, sort: 135 },
  pcbSinglePieceSizeMmMm: { visible: false, sort: 136 },
  pcbPanelSizeMmMm: { visible: false, sort: 137 },
  layers: { visible: false, sort: 138 },
  moldPaymentMethod: { visible: false, sort: 139 },
  numberOfJigsawPcbsPcsPanel: { visible: false, sort: 140 },
  samplePrice: { visible: false, sort: 141 },
  sampleLeadTimeDays: { visible: false, sort: 142 },
  materialCodeControl: { visible: false, sort: 143 },
  projectMaterialMfg: { visible: false, sort: 144 },
  projectMaterialMpn: { visible: false, sort: 145 },
  priceCategory: { visible: false, sort: 146 },
  materialType: { visible: false, sort: 147 },
  level: { visible: false, sort: 148 },
  homemadeSourcing: { visible: false, sort: 149 },
  version: { visible: false, sort: 150 },
  model: { visible: false, sort: 151 },
  projectMaterialMpq: { visible: false, sort: 152 },
  targetPrice: { visible: false, sort: 153 },
  brand: { visible: false, sort: 154 },
  currentPurchasePrice: { visible: false, sort: 155 },
  color: { visible: false, sort: 156 },
  estimatedAnnualUsage: { visible: false, sort: 157 },
  endCustomerPartNumber: { visible: false, sort: 158 },
  purchasingUnit: { visible: false, sort: 159 },
  sourcing: { visible: false, sort: 160 },
  temporaryMaterial: { visible: false, sort: 161 },
  materialHierarchy: { visible: false, sort: 162 },
  technologicalRequirements: { visible: false, sort: 163 },
  surfaceTreatment: { visible: false, sort: 164 },
  materialRemarks: { visible: false, sort: 165 },
  transactor: { visible: false, sort: 166 },
  quantityLadderStatus: { visible: false, sort: 167 },
  materialDrawing: { visible: false, sort: 168 },
  supplierCode: { visible: false, sort: 169 },
  erpSynchronousMessages: { visible: false, sort: 170 },
  approvalNumbers: { visible: false, sort: 171 },
  approvalCount: { visible: false, sort: 172 },
  projectMaterialStatus: { visible: false, sort: 173 },
  testCosts: { visible: false, sort: 174 },
  plateThickness: { visible: false, sort: 175 },
  throughHoleMethod: { visible: false, sort: 176 },
  materialCost: { visible: false, sort: 177 },
  sendRecommend: { visible: true, sort: 178 },
  orderUnitQuantity: { visible: false, sort: 179 },
  quotationsStatus: { visible: false, sort: 180 },
  totalFactoryCosts: { visible: true, sort: 27 },
  productProfit: { visible: true, sort: 28 },
  totalManufacturingCost: { visible: true, sort: 29 },
  procurementCost: { visible: true, sort: 31 },
  internalProcessingCost: { visible: true, sort: 33 },
  fuelPowerCosts: { visible: true, sort: 34 },
  laborWages: { visible: true, sort: 35 },
  depreciationExpenses: { visible: true, sort: 36 },
  toolingMoldsCost: { visible: true, sort: 37 },
  auxiliaryMaterialCost: { visible: true, sort: 38 },
  laborProtectionFees: { visible: true, sort: 39 },
  repairCosts: { visible: true, sort: 40 },
  lowEasyConsume: { visible: true, sort: 41 },
  periodExpenses: { visible: true, sort: 42 },
  salesExpenses: { visible: true, sort: 44 },
  financialExpenses: { visible: true, sort: 45 },
  logisticsCosts: { visible: true, sort: 46 },
  ictTestCost: { visible: true, sort: 29 },
  componentLossCost: { visible: true, sort: 30 },
  allocationEquipmentCost: { visible: true, sort: 31 }
}
