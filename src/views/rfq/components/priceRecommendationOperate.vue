<template>
  <div style="margin-top: 15px">
    <el-card>
      <div slot="header">
        {{ $t('rfq.priceComparisonAnalysis') }}
      </div>
      <el-tabs v-model="queryParams.templateId" class="form" style=" float:none;" @tab-click="getTemplateCustomeFields">
        <el-tab-pane
          v-for="item in templateTabs"
          :key="item.templateId"
          :name="String(item.templateId)"
        >
          <span slot="label">
            {{ item.templateName }}
            <el-badge
              :value="item.materialCount>99?'99+':(item.materialCount+''==='0'?'':item.materialCount)"
              style="margin-left: 5px;height: 33px;"
            />
          </span>
        </el-tab-pane>
      </el-tabs>
      <div style="display: flex;justify-content: center;margin-bottom: 15px">
        <div>
          <el-input
            v-model="queryParams.search"
            :placeholder="$t('rfq.pleaseEnterCategoryNameMaterialCodeSupplierAbbreviationMpnmfg')"
            style="width: 500px"
          />
          <el-button plain type="primary" @click="queryParams.pageNo = 1;getList();">{{ $t('common.search') }}</el-button>
          <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>
        </div>

      </div>
      <vxe-grid
        ref="recommendationAnalysis"
        :cell-class-name="getCellClassName"
        :data="list"
        :loading="loading"
        :span-method="mergeRowMethod"
        v-bind="girdOption"
        @checkbox-change="checkBoxChange"
        @checkbox-all="checkBoxAllChange"
      >
        <template #materialCode="{row}">
          <i
            v-if="row.materialDrawing"
            class="el-icon-picture-outline"
            style="font-size: 16px;margin-right: 3px;cursor: pointer"
            @click="downloadMaterialDrawing(row.projectMaterialId,row.materialCode)"
          />
          <copy-button
            @click="showMaterialDetail(row.projectMaterialId)"
          >
            {{ row.materialCode }}
          </copy-button>
        </template>
        <!--小数位处理-->
        <template #historyPrice="{row}">
          <el-button style="text-decoration: underline" type="text" @click="showHistoryPrice(row.projectMaterialId,row.materialCode)">
            <number-format v-if="row.historyPrice!=null" :splicing="row.updated?'*':''" :value="row.historyPrice" />
            <span v-else>N/A</span></el-button>

        </template>
        <template #conversionUnitPriceWithoutTax="{row}">
          <number-format :value="row.conversionUnitPriceWithoutTax" />
        </template>
        <template #conversionUnitPriceTax="{row}">
          <number-format :value="row.conversionUnitPriceTax" />
        </template>
        <template #exchangeRate="{row}">
          <number-format :value="row.exchangeRate" />
        </template>
        <template #originalUnitPriceWithoutTax="{row}">
          <number-format :value="row.originalUnitPriceWithoutTax" />
        </template>
        <template #originalUnitPriceTax="{row}">
          <number-format :value="row.originalUnitPriceTax" />
        </template>
        <template #vsHistoricalRateChange="{row}">
          <number-format :decimal-place="2" :value="row.vsHistoricalRateChange" />
        </template>
        <template #lowestUnitPrice="{row}">
          <number-format :value="row.lowestUnitPrice" />
        </template>
        <template #recommendedUnitPriceTax="{row}">
          <number-format :value="row.recommendedUnitPriceTax" />
        </template>
        <template #recommendationVsHistoricalRateChange="{row}">
          <number-format :decimal-place="2" :value="row.recommendationVsHistoricalRateChange" />
        </template>
        <template #unitPriceIncludesTaxPriceUnit="{row}">
          <number-format :value="row.unitPriceIncludesTaxPriceUnit" />
        </template>
        <template #unitPriceWithoutTaxPriceUnit="{row}">
          <number-format :value="row.unitPriceWithoutTaxPriceUnit" />
        </template>
        <template #dosage="{row}">
          <number-format :value="row.dosage" />
        </template>
        <!--数据字典转换处理-->
        <template #basicUnit="{row}">
          <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.basicUnit" />
        </template>
        <template #orderUnit="{row}">
          <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.orderUnit" />
        </template>
        <template #materialStatus="{row}">
          <dict-tag :type="DICT_TYPE.RFQ_MATERIAL_STATUS" :value="row.materialStatus" />

        </template>
        <template #categoryId="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
        </template>
        <template #historyCurrency="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.historyCurrency" />
        </template>
        <template #currency="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.currency" />
        </template>
        <template #originalCurrency="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.originalCurrency" />
        </template>
        <template #lowestUnitPriceCurrency="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.lowestUnitPriceCurrency" />
        </template>
        <template #taxRate="{row}">
          <dict-tag :type="DICT_TYPE.SUPPLIER_RATE" :value="row.taxRate" />
        </template>
        <template #paymentMethod="{row}">
          <dict-tag :type="DICT_TYPE.SUPPLIER_PAYMENT_DAYS" :value="row.paymentMethod" />
        </template>
        <template #recommend="{row}">
          <el-switch
            v-if="row.quotationStatus==='quoted'&&(row.materialStatus==='to_be_recommended'||row.materialStatus==='approve_return')"
            v-model="row.recommend"

            @change="saveRecommendInfo(row)"
          />
          <span v-else-if="row.recommend===true && row.materialStatus!=='approve_return' "> √</span>
        </template>
        <template #proportion="{row}">
          <el-input
            v-if="row.quotationStatus==='quoted'&&(row.materialStatus==='to_be_recommended'||row.materialStatus==='approve_return')"
            v-model="row.proportion"
            type="number"
            @focus="()=> tempRow = {...row}"
            @blur="!deepEqual(tempRow, row)&&saveRecommendInfo(row)"
          />
          <span v-else> {{ row.proportion }}</span>
        </template>
        <template #priceUnit="{row}">
          <vxe-input
            v-if="row.quotationStatus==='quoted'&&(row.materialStatus==='to_be_recommended'||row.materialStatus==='approve_return')"
            v-model="row.priceUnit"
            type="integer"
            @focus="()=> tempRow = {...row}"
            @blur="!deepEqual(tempRow, row)&&saveRecommendInfo(row)"
          />
          <span v-else> {{ row.priceUnit }}</span>
        </template>
        <template #recommendedPriceExpires="{row}">
          <el-date-picker
            v-if="row.quotationStatus==='quoted'&&(row.materialStatus==='to_be_recommended'||row.materialStatus==='approve_return')"
            v-model="row.recommendedPriceExpires"
            :picker-options="pickerOptions"
            :placeholder="$t('common.pleaseSelectADate')"
            placement="bottom-start"
            type="date"
            value-format="yyyy-MM-dd"
            @focus="()=> tempRow = {...row}"
            @blur="!deepEqual(tempRow, row)&&saveRecommendInfo(row)"
          />
          <span v-else> {{ row.recommendedPriceExpires }}</span>
        </template>
        <template #toolbar_buttons>
          <el-row :gutter="10" class="mb8" style="width: 100%">
            <el-col :span="10" style="display: flex">
              <el-button
                v-has-permi="['rfq:quotations-recommend:query']"
                plain
                type="primary"
                @click="systemRecommend"
              >
                {{ $t('rfq.applicationConfiguration') }}
              </el-button>
              <el-button
                v-has-permi="['rfq:quotations-recommend:query']"
                plain
                type="primary"
                @click="showAdvancedConfig('advancedConfig')"
              >
                {{ $t('rfq.recommendedConfiguration') }}
              </el-button>
              <el-button plain type="primary" @click="showAdvancedConfig('simulate')">{{ $t('rfq.simulatedDecisionmaking') }}</el-button>
              <el-button plain type="danger" @click="showReturnForm()">{{ $t('rfq.return') }}</el-button>
              <el-checkbox v-model="queryParams.isTempMaterial" :label="$t('rfq.viewTemporaryMaterials')" style="margin-left: 30px;padding-top: 5px;" @change="queryParams.pageNo = 1;getList();" />
            </el-col>
            <el-col :span="14">
              <customFields
                ref="customFields"
                :custom-columns.sync="girdOption.columns"
                :list-id="girdOption.id"
                :only-custom="false"
                type="priceRecommendCustom"
                @queryTable="getList"
              />
              <div style="float: right;margin-right: 10px">
                <!--                <el-button v-has-permi="['rfq:quotations-recommend:query']" style="background: #ff8d1a;border-color:#ff8d1a" type="primary" @click="showCalculator">{{ $t('rfq.priceComparisonCalculator') }}</el-button>-->
                <el-button v-has-permi="['infra:file:upload']" plain type="primary" @click="handleImport">{{
                  $t('rfq.batchOperation')
                }}
                </el-button>
              </div>

            </el-col>
          </el-row>
          <el-alert
            v-if="selectedRecord.length"
            show-icon
            type="success"
          >
            <template slot="title">{{
                                     $t('common.selectedTotal', {selectedTotal: Array.from(new Set(selectedRecord.map(j => j.projectMaterialId)))?.length})
                                   }}
              <el-button style="padding: 0;margin-left: 10px" type="text" @click="clearSelected">{{
                $t('rfq.empty')
              }}
              </el-button>
            </template>
          </el-alert>
        </template>

        <template #quotationsCount="{row}">
          <el-button
            size="mini"
            type="text"
            @click="showOperationLog(row.materialSupplierRelId,'RFQ_QUOTATIONS')"
          >{{ row.quotationsCount }}
          </el-button>
        </template>

      </vxe-grid>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getList"
      />
      <div style="position:fixed;bottom:30px;left:50%">
        <el-button @click="backPrevious">{{ $t('rfq.backToOverview') }}</el-button>
        <el-button v-has-permi="['rfq:quotations-recommend:query']" type="primary" @click="showReportConfig">
          {{ $t('rfq.reportAndApproval') }}
        </el-button>
      </div>
    </el-card>
    <el-dialog
      v-if="approveReportVisible"
      :title="$t('rfq.reportAndApproval')"
      :visible.sync="approveReportVisible"
      width="1000px"
    >
      <el-card>
        <div slot="header">
          {{ $t('rfq.reportTemplate') }}
        </div>
        <div style="padding: 0 100px">
          <el-descriptions :colon="false" :column="2" direction="vertical" label-class-name="labelTitle">
            <el-descriptions-item :label="$t('rfq.reportTemplate')" :span="2" label-class-name="requiredLabelTitle">
              <el-select v-model="approvalReportConfig.reportId">
                <el-option
                  v-for="item in templateList"
                  :label="`${rfqProject.eventName}-${item.name.replace('.ureport.xml', '')}-${dayjs().format('YYYY-MM-DD')}`"
                  :value="item.id"
                />
              </el-select>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('rfq.recommendedRemarks')" :span="2">
              <el-input v-model="approvalReportConfig.recommendRemark" :row="3" type="textarea" />
            </el-descriptions-item>
            <el-descriptions-item :label="$t('rfq.supplierQuotationAttachment')">
              <div v-for="item in templateFiles" v-if="item.check">
                <i
                  class="el-icon-delete"
                  style="cursor:pointer;font-size: 18px;margin-right: 5px;"
                  @click="item.check=false"
                />
                <el-button
                  style="text-decoration: underline;font-size: 13px"
                  type="text"
                  @click="openFile(item.filePath)"
                >
                  {{ item.fileName }}
                </el-button>
              </div>
            </el-descriptions-item>
            <!-- bom模式取消采购推荐附件 -->
            <el-descriptions-item v-if="businessCode!=='bom_enquiry'" :label="$t('rfq.priceRecommendationAttachment')">

              <el-upload
                :action="uploadUrl"
                :file-list="fileList"
                :headers="getBaseHeader()"
                :limit="5"
                :on-preview="onPreview"
                :on-remove="onRemove"
                :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                class="rfq-create-upload"
                multiple
              >
                <el-button size="small" plain type="primary">{{ $t('rfq.uploadPriceRecommendationAttachment') }}</el-button>
                <div slot="tip" class="el-upload__tip">
                  {{ $t('rfq.totalAttachmentsShouldNotExceedmb') }}
                </div>
              </el-upload>
            </el-descriptions-item>
          </el-descriptions>

        </div>

      </el-card>
      <!-- 非BOM模式下显示审批单名称，审批人等信息 -->
      <el-card v-if="businessCode!=='bom_enquiry'" style="margin-top: 15px">
        <div slot="header">
          {{ $t('rfq.approvalProcessConfiguration') }}
        </div>
        <div style="padding: 0 100px">
          <el-descriptions :colon="false" :column="1" label-class-name="labelTitle">
            <el-descriptions-item :label="$t('rfq.approvalFormName')" label-class-name="requiredLabelTitle">
              <el-input v-model="approvalReportConfig.approvalName	" />
            </el-descriptions-item>
            <el-descriptions-item :label="$t('rfq.urgentApproval')">
              <el-checkbox v-model="approvalReportConfig.urgent">{{ }}</el-checkbox>
            </el-descriptions-item>
          </el-descriptions>

        </div>

      </el-card>
      <div style="display: flex;justify-content: space-around;margin-top: 20px;padding: 0 64px">
        <el-tooltip
          :content="$t('rfq.discardThePossiblyGeneratedApprovalReportAndExit')"
          class="item"
          effect="dark"
          placement="top"
        >
          <el-button style="width: 20%" @click="cancelApprove">{{ $t('common.cancel') }}</el-button>
        </el-tooltip>
        <el-tooltip
          :content="$t('rfq.clickToGeneratePreviewEffectAccordingToApprovalReportConfiguration')"
          class="item"
          effect="dark"
          placement="top"
        >
          <el-button style="width: 20%" type="primary" plain @click="handlePreview">{{ $t('rfq.previewReport') }}</el-button>
        </el-tooltip>
        <!-- 推送价格到BOM -->
        <el-button
          v-if="businessCode==='bom_enquiry'"
          v-has-permi="['rfq:price-approval:save']"
          :loading="handleApproveLoading"
          style="width: 20%"
          type="primary"
          @click="pushToBom"
        >
          {{ $t('推送价格到BOM') }}
        </el-button>

        <el-button
          v-else
          v-has-permi="['rfq:price-approval:save']"
          :loading="handleApproveLoading"
          style="width: 20%"
          type="primary"
          @click="handleApprove"
        >
          {{ $t('rfq.submitForApproval') }}
        </el-button>
      </div>
    </el-dialog>

    <!--    推荐配置弹出框-->
    <el-dialog
      :title="$t('rfq.advancedRecommendedConfiguration')"
      :visible.sync="advancedConfigVisible"
      width="1000px"
    >
      <el-form ref="advancedRef" :model="advanced" inline label-position="top">
        <el-row :gutter="30" style="width: 100%;">
          <el-col :span="10" style="width: 50%">
            <el-form-item :label="$t('rfq.currencyAllocation')" style="width: 100%">
              <div style="display: flex">
                <div>1</div>
                <div style="flex: 0 0 50%">
                  <el-select
                    v-model="advanced.targetCurrency"
                    :placeholder="$t('rfq.pleaseSelectTheTargetCurrency')"
                    clearable
                    style="width: 140px;margin-left: 8px"
                    @change="targetCurrencyChange"
                  >
                    <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                      :key="dict.id"
                      :label="dict.name"
                      :value="dict.id"
                    />
                  </el-select>
                  <i class="el-icon-sort" style="margin-left:23px;font-size: 18px;rotate: 90deg" />
                </div>
                <div style="flex: 0 0 50%">
                  <div v-for="(item,index) in advanced.configureCurrency" :key="index">
                    <vxe-input
                      v-model.number="item.amount"
                      :placeholder="$t('rfq.enterExchangeRate')"
                      min="0"
                      style="width: 140px;margin-bottom: 5px"
                      type="number"
                    />
                    <dict-tag
                      :type="DICT_TYPE.COMMON_CURRENCY"
                      :value="item.currency"
                      style="margin-left: 10px;font-size: 14px"
                    />
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="10" style="width: 50%">
            <el-form-item :label="$t('rfq.notParticipatingInPriceRecommendation')">
              <el-checkbox-group v-model="advanced.notWantSuppliers">
                <el-checkbox
                  v-for="dict in advancedRecommend.suppliers"
                  :key="dict.value"
                  :label="dict.supplierId"
                >{{ dict.supplierName }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30" style="width: 100%;">
          <el-col :span="10" style="width: 50%">
            <el-form-item :label="$t('rfq.supplierRecommendationPrinciple')">
              <div v-for="(item,index) in advanced.principles" :key="index" style="margin: 5px 0">
                <i
                  class="el-icon-delete"
                  style="cursor:pointer;font-size: 18px"
                  @click="advanced.principles.splice(index,1)"
                />
                <span style="margin: 0 10px">{{ $t('rfq.principle') }}{{ index + 1 }}</span>
                <el-select v-model="item.principle" :placeholder="$t('rfq.pleaseSelectAPrinciple')" clearable>
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.RFQ_RECOMMEND_PRINCIPLE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </div>
              <div style="text-align: right;width: 300px">
                <i class="el-icon-circle-plus-outline" style="font-size: 18px;cursor:pointer;" @click="addPrinciple" />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="10" style="width: 50%">
            <div style="position:absolute;bottom: 10px">
              <el-button type="primary" @click="saveAdvanced">{{ $t('rfq.applicationConfiguration') }}</el-button>
              <el-button @click="resetAdvanced">{{ $t('common.reset') }}</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <el-dialog
      v-if="
        calculateVisible"
      :title="$t('rfq.priceComparisonCalculator')"
      :visible.sync="calculateVisible"
      width="1000px"
    >
      <i class="el-icon-s-unfold" style="font-size: 20px" />
      <el-input
        v-model.number="quantityNum"
        clearable
        style="margin-left:10px;width: 200px"
        type="number"
        @change="freshPrice"
      />
      <el-row style="overflow-y: auto;width: 850px;margin-top: 20px" type="flex">
        <el-col v-for="item in priceTable" :span="12">
          <el-table
            ref="priceList"
            :data="item.data"
            :header-cell-style="{color:'#ffffff',background:item.color}"
            :row-class-name="tableRowClassName"
          >
            <el-table-column :label="item.supplier" align="center" header-align="center">
              <el-table-column
                :label="$t('rfq.numberOfSteps')"
                align="center"
                header-align="center"
                prop="quantity"
              />
              <el-table-column
                :label="$t('rfq.price')"
                align="center"
                header-align="center"
                prop="price"
              />
              <el-table-column :label="$t('rfq.currency')" align="center" header-align="center" prop="currency">
                <template slot-scope="scope">
                  <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="scope.row.currency" />
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>

        </el-col>
      </el-row>
      <div style="margin-top:10px;text-align: center">
        <el-button type="primary" @click="freshPrice">{{ $t('rfq.priceCalculation') }}</el-button>
        <el-button @click="calculateVisible = false">{{ $t('rfq.return') }}</el-button>
      </div>

    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url + '?type=' + upload.type"
          :auto-upload="false"
          :data="queryParams"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-preview="onPreview"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button plain type="primary" @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="logVisible"
      :visible.sync="logVisible"
      :title="$t('common.operationRecord')"
      width="1000px"
    >
      <rfqOperationRecord
        :business-id="businessId"
        :business-type="businessType"
        :log-visible.sync="logVisible"
      />
    </el-dialog>

    <el-dialog
      v-if="historyVisible"
      :title="$t('rfq.historicalPrice')"
      :visible.sync="historyVisible"
      width="1000px"
    >
      <historyPrice
        ref="historyPriceDialog"
        :is-edit="true"
        :search-text="historyPriceCode"
      />
      <div style="margin-top:20px;text-align: right">
        <el-button type="primary" @click="saveHistoryData">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>

    <rfqMaterial
      ref="rfqMaterial"
      :material-visible.sync="materialVisible"
      :rfq-project-id="true"
      :rfq-project-status="null"
    />
    <el-dialog
      v-if="simulateVisible"
      :visible.sync="simulateVisible"
      :title="$t('rfq.simulatedDecisionmaking')"
      width="1000px"
    >
      <common-card
        :title="$t('rfq.currencyAllocation')"
      >
        <div style="display: flex">
          <div style="flex: 0 0 50%;text-align: right">
            <span>1</span>
            <el-select
              v-model="simulation.targetCurrency"
              :placeholder="$t('rfq.pleaseSelectTheTargetCurrency')"
              style="width: 140px;margin-left: 8px"
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
            <i class="el-icon-sort" style="margin-left:23px;font-size: 18px;rotate: 90deg" />
          </div>
          <div style="flex: 0 0 50%">
            <div v-for="(item,index) in simulation.currencyExchangeRateList" :key="index" style="padding-left: 30px">
              <vxe-input
                v-model.number="item.rate"
                min="0"
                style="width: 140px;margin-bottom: 5px"
                type="number"
              />
              <dict-tag
                :type="DICT_TYPE.COMMON_CURRENCY"
                :value="item.currency"
                style="margin-left: 10px;font-size: 14px"
              />
            </div>
          </div>
        </div>

      </common-card>
      <common-card
        :title="$t('rfq.notParticipatingInTheSimulationPlan')"
      >
        <div style="padding: 0 150px">
          <el-checkbox-group v-model="simulation.notWantSuppliers" @change="ensureParticipate">
            <el-checkbox
              v-for="dict in advancedRecommend.suppliers"
              :key="dict.value"
              :label="dict.supplierId"
            >{{ dict.supplierName }}
            </el-checkbox>
          </el-checkbox-group>
        </div>

      </common-card>
      <common-card
        :title="$t('rfq.simulationScheme')"
      >
        <div style="padding: 0 150px;display: flex;justify-content: space-between">

          <el-select
            v-model="simulation.simulationScenario"
            :placeholder="$t('common.pleaseSelect')"
            style="width: 200px;margin:0 16px"
            @change="addSupplier"
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_SIMULATION_PROGRAM)"
              :key="dict.value"
              :disabled="simulation.simulationProgramList.findIndex(a=>a.simulationProgram === dict.value) >-1"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          <div>
            <span class="requiredLabelTitle" style="margin: 0px 10px">{{ $t('rfq.targetOrderQuantity') }}</span>
            <vxe-input v-model.number="simulation.targetOrderQuantity" min="0" type="number" />

          </div>
        </div>
        <div
          v-for="(item,index) in simulation.simulationProgramList"
          style="display: flex;
      align-items: center;
        padding: 0 150px;margin: 5px 0"
        >
          <div
            style="width: 200px;margin:0 16px"
          >
            <dict-tag :type="DICT_TYPE.RFQ_SIMULATION_PROGRAM" :value="item.simulationProgram" />
          </div>
          <div style="flex: 0 0 60%">
            <el-checkbox-group
              v-if="item.simulationProgram === 'singleSupplierSolution'"
              v-model="item.supplierIds"
              style="min-height: 32px;line-height: 32px"
            >
              <el-checkbox
                v-for="dict in participateSupplierList"
                :key="dict.value"
                :label="dict.supplierId"
              >{{ dict.supplierName }}
              </el-checkbox>

            </el-checkbox-group>
            <div
              v-else-if="item.simulationProgram === 'priceComparisonAnalysisPageSolution'"
              style="height: 32px;line-height: 32px"
            />
            <div v-else>
              {{ $t('rfq.secondPriorityPrinciple') }}
              <el-select
                v-model="item.secondSimulationProgram"
                :placeholder="$t('common.pleaseSelect')"
                style="width: 200px;margin:0 16px"
              >
                <el-option
                  v-for="dict in secondPrinciple(item.simulationProgram)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </div>

          </div>
          <i
            class="el-icon-delete"
            style="cursor:pointer;font-size: 18px;float: right;margin-left: auto"
            @click="simulation.simulationProgramList.splice(index,1)"
          />
        </div>

      </common-card>
      <div slot="footer">
        <el-button @click="simulateVisible = false"> {{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="generateSimulation"> {{ $t('rfq.generateSimulationResults') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="simulateResultVisible"
      :visible.sync="simulateResultVisible"
      width="1000px"
    >
      <div slot="title">
        <span style="color: #4996b8;font-size: 18px">
          {{ $t('rfq.simulationResults') }}
        </span>
        <el-button style="margin-left: 10px" type="primary" @click="downloadSimulateResult"> {{ $t('order.download') }}</el-button>
      </div>
      <div>
        {{ $t('rfq.simulateTheTotalQuantityOfMaterials') }}：{{ simulationMaterialNum }}
      </div>
      <el-table
        :cell-class-name="optimalResults"
        :data="simulationResult"
        border
        style="margin-top: 15px"
      >
        <el-table-column :label="$t('rfq.serialNo')" type="index" />
        <el-table-column :label="$t('rfq.simulationScheme')" prop="simulationProgram">
          <template #default="scope">
            {{
              getDictDataLabel(DICT_TYPE.RFQ_SIMULATION_PROGRAM, scope.row.simulationProgram) || scope.row.simulationProgram
            }}

          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.totalCostThisTimetargetCurrency')" prop="totalCost">
          <template #default="scope">
            <number-format :value="scope.row.totalCost" />
          </template>

        </el-table-column>
        <el-table-column :label="$t('rfq.fundOccupationtargetCurrency')" prop="fundOccupation">
          <template #default="scope">
            <number-format :value="scope.row.fundOccupation" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.longestLt')" prop="longestLT" />
        <el-table-column
          v-for="item in suppliersProportion"
          :label="item.supplierName"
          :prop="String(item.supplierId)"
        />
        <el-table-column :label="$t('rfq.applicationPlan')">
          <template #default="scope">
            <el-button type="text" @click="applySimulation(scope.row.simulationProgram)">{{ $t('rfq.application') }}</el-button>
          </template>

        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary" @click="simulateResultVisible = false">  {{ $t('rfq.returnToSimulationConfiguration') }}</el-button>
        <el-button @click="closesim">{{ $t('order.close') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="rfqReturnCommentVisible"
      :visible.sync="rfqReturnCommentVisible"
      :title="$t('rfq.returnOfMaterialsToSupplierQuotation')"
      width="1000px"
    >
      <materialreturn :material-ids="selectMaterialIds" @closeMaterialReturn="closeMaterialReturn" />
    </el-dialog>

  </div>
</template>

<script>
import dayjs from 'dayjs'
import {
  applicationResults,
  checkQuotationQuantity,
  createPreviewRecord,
  downloadMaterialDrawings,
  exportAnalyzeQuote,
  getCustomFieldsByTemplate,
  getTemplateList,
  listByBusinessIds, pushPriceToBom,
  savePriceApproval,
  simulationResults,
  getRfqProject
} from '@/api/rfq/home'
import { getExchangeRateByFromCurrencyAndToCurrency } from '@/api/system/exchangeRate'
import { getBaseHeader } from '@/utils/request'
import { parseTime } from '@/utils/ruoyi'
import { checkStatus, savePriceApprovalCompleted } from '@/api/rfq/priceApproval'
import { getPreviewReport } from '@/api/visualization/report'
import { updateProjectMaterialHisPrice } from '@/api/rfq/projectMaterialHisPrice'
import {
  checkRecommendAccess,
  checkSubmit,
  exportMaterialAnalyticsRecommendTemplate,
  getAdvancedRecommend,
  getNotRecommend,
  getRecommendMaterialAnalyticsAllIds,
  getTemplateRecommend,
  pageMaterialAnalyticsRecommend,
  saveAdvancedRecommend,
  updateMaterialAnalyticsRecommend
} from '@/api/rfq/quoationRecommend'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import store from '@/store'
import Materialreturn from '@/views/rfq/components/materialReturn.vue'
import { deepEqual } from '@/utils'
export default {
  name: 'Pricerecommendationoperate',
  components: {
    Materialreturn,
    rfqOperationRecord: () => import('@/views/rfq/components/rfqOperation'),
    historyPrice: () => import('@/views/rfq/components/historyPrice'),
    customFields: () => import('./customFields'),
    rfqMaterial: () => import('@/views/rfq/components/material')
  },
  props: ['recommendOptions'],
  data() {
    return {
      selectMaterialIds: null,
      returnMaterial: [],
      currencyRmb: 3,
      historyPriceCode: '',
      simulateVisible: false,
      returnCommentForm: {
        opinion: '',
        suppliers: []
      },
      returnLoading: false,
      // 提交审批loading
      handleApproveLoading: false,
      // 提交不审批loadding
      handleSubmitNoApproveLoading: false,
      businessId: null,
      businessType: '',
      logVisible: false,
      categoryList: [],
      // 历史价格弹出框
      historyVisible: false,
      materialVisible: false,
      getBaseHeader,
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      fileList: [],
      advancedRecommend: {},
      principle: [],
      advanced: {
        projectId: null,
        configureCurrency: [
          {
            amount: 1,
            currency: this.currencyRmb
          }
        ],
        projectMaterialIds: [],
        notWantSuppliers: [],
        principles: [],
        quotationIds: [],
        targetCurrency: null,
        templateId: null
      },
      simulation: {
        projectId: null,
        targetOrderQuantity: null,
        simulationScenario: null,
        currencyExchangeRateList: [
          {
            rate: 1,
            currency: this.currencyRmb
          }
        ],
        simulationProgramList: [],
        participate: [],
        projectMaterialIds: [],
        notWantSuppliers: [],
        principles: [],
        quotationIds: [],
        targetCurrency: this.currencyRmb,
        templateId: null
      },
      approvalReportConfig: {
        approvalMaterialSupplierRelIds: [],
        quotationsMaterialSupplierRelId: [],
        // 报价物料ID，rfq_project_materials表id拼接
        projectMaterialId: '',
        // 提交审批的物料id集合
        projectMaterialIds: [],
        fileList: [],
        approvalName: '',
        approvalNo: '',
        attachmentRelFileIds: [],
        files: '',
        flag: '',
        // id: 0,
        // status: 'pending_approval',
        previewRecordId: 0,
        projectId: null,
        quotationsTemplate: '',
        recommendRemark: '',
        reportId: null,
        urgent: false,
        uploadFileIds: []
      },
      templateList: [],
      queryParams: {
        categoryIds: [],
        pageNo: 1,
        pageSize: 10,
        projectId: null,
        quotationIds: [],
        projectMaterialIds: [],
        search: '',
        sortBy: 'ASC',
        sortField: 'id',
        templateId: 0
      },
      total: 0,
      loading: false,
      list: [],
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'recommendationAnalysis',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        filterConfig: {
          remote: true
        },
        rowConfig: { isCurrent: true,
          keyField: 'projectMaterialId',
          isHover: true
        },
        columns: [
          { title: '', type: 'checkbox', visible: true, fixed: 'left', width: 30 },

          {
            title: this.$t('material.materialCode'),
            fixed: 'left',
            field: 'materialCode', slots: { default: 'materialCode' }, visible: true, width: 100
          },
          {
            title: this.$t('material.materialDescription'),
            field: 'materialDescription',
            visible: true,
            width: 100
          },
          {
            title: this.$t('common.status'),
            slots: { default: 'materialStatus' },
            field: 'materialStatus', visible: true, width: 60
          },
          {
            title: this.$t('material.category'),
            slots: { default: 'categoryId' },
            field: 'categoryId', visible: true,
            hidden: true,
            width: 100
          },
          { title: this.$t('material.specificationAndModel'), field: 'specifications', visible: true, width: 100 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          {
            title: this.$t('rfq.estimatedAnnualConsumption'),
            // headerClassName: 'yellow',
            field: 'dosage',
            visible: true,
            slots: { default: 'dosage' },
            width: 60,
            align: 'right'
          },
          {
            title: this.$t('material.basicUnit'),
            // headerClassName: 'yellow',
            slots: { default: 'basicUnit' },
            field: 'basicUnit', visible: true, width: 60
          },
          {
            title: this.$t('rfq.requestedOrderQuantityFrom'),
            // headerClassName: 'yellow',
            field: 'quantityFrom', visible: true, width: 100
          },
          {
            title: this.$t('rfq.theOrderQuantityIsRequiredToReach'),
            // headerClassName: 'yellow',
            field: 'quantityTo', visible: true, width: 100
          },
          {
            title: this.$t('rfq.historicalPrice'),
            // headerClassName: 'gray',
            field: 'historyPrice',
            visible: true,
            width: 60,
            slots: { default: 'historyPrice' },
            align: 'right'
          },
          {
            title: this.$t('rfq.historicalSuppliers'),
            field: 'historySupplierName',
            // headerClassName: 'gray',
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.historicalCurrency'),
            field: 'historyCurrency',
            // headerClassName: 'gray',
            slots: { default: 'historyCurrency' },
            visible: true,
            width: 60
          },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
          {
            title: this.$t('rfq.numberOfQuotations'),
            slots: { default: 'quotationsCount' },
            field: 'quotationsCount', visible: true, width: 60
          },
          {
            title: this.$t('rfq.orderQuantityFrom'),
            field: 'quotationQuantityFrom',
            visible: true,
            width: 100, align: 'right'
          },
          {
            title: this.$t('rfq.orderQuantityArrived'),
            field: 'quotationQuantityTo',
            visible: true,
            width: 100, align: 'right'
          },
          {
            title: this.$t('order.unitPriceWithoutTax'),
            field: 'conversionUnitPriceWithoutTax',
            visible: true,
            slots: { default: 'conversionUnitPriceWithoutTax' },
            width: 100, align: 'right'
          },
          {
            title: this.$t('order.unitPriceIncludingTax'),
            field: 'conversionUnitPriceTax',
            slots: { default: 'conversionUnitPriceTax' },
            visible: true,
            width: 100, align: 'right'
          },
          {
            title: this.$t('system.currency'),
            field: 'currency',
            visible: true,
            width: 50,
            slots: { default: 'currency' }
          },
          {
            title: this.$t('rfq.exchangeRate'),
            field: 'exchangeRate',
            visible: true,
            width: 50,
            slots: { default: 'exchangeRate' }
          },
          {
            title: this.$t('supplier.taxRate'),
            field: 'taxRate',
            visible: true,
            width: 100,
            slots: { default: 'taxRate' }
          },
          { title: this.$t('rfq.MinimumOrderQuantity'), field: 'moq', visible: true, width: 60 },
          {
            title: this.$t('rfq.quotedUnitPriceExcludingTax'),
            // headerClassName: 'red',
            field: 'originalUnitPriceWithoutTax',
            visible: true,
            slots: { default: 'originalUnitPriceWithoutTax' },
            width: 100, align: 'right'
          },
          {
            title: this.$t('rfq.quotationUnitPriceIncludingTax'),
            field: 'originalUnitPriceTax',
            // headerClassName: 'red',
            slots: { default: 'originalUnitPriceTax' },
            visible: true,
            width: 60, align: 'right'
          },
          {
            title: this.$t('rfq.quotationOriginalCurrency'),
            field: 'originalCurrency',
            // headerClassName: 'red',
            slots: { default: 'originalCurrency' },
            visible: true,
            width: 60
          },
          {
            title: this.$t('rfq.vsHistoricalPriceChangeRate'),
            field: 'vsHistoricalRateChange',
            // headerClassName: 'red',
            slots: { default: 'vsHistoricalRateChange' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.expiryDateOfPriceValidity'),
            field: 'priceValidUntil',
            visible: true,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            width: 100
          },
          {
            title: this.$t('rfq.deliveryPerioddays'),
            field: 'deliveryPeriod',
            // headerClassName: 'indigo',

            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.paymentMethodRequired'),
            // headerClassName: 'indigo',
            slots: { default: 'paymentMethod' },
            field: 'paymentMethod', visible: true, width: 100
          },
          {
            title: this.$t('material.purchasingUnit'),
            // headerClassName: 'blue',
            slots: { default: 'orderUnit' },
            field: 'orderUnit', visible: true, width: 100
          },
          {
            title: this.$t('rfq.lowestUnitPriceWithoutTax'),
            field: 'lowestUnitPrice',
            // headerClassName: 'green',
            slots: { default: 'lowestUnitPrice' },
            visible: true,
            width: 100, align: 'right'
          },
          {
            title: this.$t('rfq.minimumUnitPriceCurrency'),
            field: 'lowestUnitPriceCurrency',
            // headerClassName: 'green',
            slots: { default: 'lowestUnitPriceCurrency' },
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.supplierWithLowestUnitPrice'),
            field: 'lowestSupplierName',
            // headerClassName: 'green',

            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.recommendedUnitPriceIncludingTax'),
            field: 'recommendedUnitPriceTax',
            visible: true,
            slots: { default: 'recommendedUnitPriceTax' },

            width: 100, align: 'right'
          },
          {
            title: this.$t('rfq.recommendedVsHistoricalPriceChangeRate'),
            field: 'recommendationVsHistoricalRateChange',
            visible: true,

            slots: { default: 'recommendationVsHistoricalRateChange' },
            width: 100
          },
          {
            title: this.$t('rfq.recommendedSuppliers'),
            field: 'recommendedSupplierName',
            visible: true,

            width: 100
          },
          {
            title: this.$t('rfq.recommend'),
            slots: { default: 'recommend' },

            field: 'recommend', visible: true, width: 50
          },
          {
            title: this.$t('rfq.mixtureRatio'),
            slots: { default: 'proportion' },

            field: 'proportion', visible: true, width: 100
          },
          {
            title: this.$t('material.priceUnit'),
            slots: { default: 'priceUnit' },

            field: 'priceUnit', visible: true, width: 60
          },
          {
            title: this.$t('rfq.unitPriceExcludingTaxpriceUnit'),
            field: 'unitPriceWithoutTaxPriceUnit',
            visible: true,
            width: 100,

            slots: { default: 'unitPriceWithoutTaxPriceUnit' }
          },
          {
            title: this.$t('rfq.unitPriceIncludingTaxpriceUnit'),
            field: 'unitPriceIncludesTaxPriceUnit',
            visible: true,
            width: 100,
            slots: { default: 'unitPriceIncludesTaxPriceUnit' }
          },
          {
            title: this.$t('rfq.expiryDateOfRecommendedPrice'),
            slots: { default: 'recommendedPriceExpires' },
            field: 'recommendedPriceExpires',
            visible: true,

            width: 120
          }
        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      templateTabs: [],
      approveReportVisible: false,
      templateFiles: [],
      advancedConfigVisible: false,
      rfqReturnCommentVisible: false,
      calculateVisible: false,
      quantityNum: 0,
      priceTable: [],
      // 用来记录自定义列插入的位置。目前以采购单位列后面
      columnsStartIndex: null,
      // 自定义列的长度
      customColumnsLength: 0,
      // 导入参数
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 人员类型:Sourcing
        type: '',
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/rfq/quotations-recommend/import-material-analytics-recommend'
      },
      mergeRowFields: {
        // 根据物料+供应商合并的初始字段的长度，用于切换模块的时候进行保留列的判断
        quotationsMaterialSupplierFieldLength: 0,
        // 根据物料合并的字段
        fields: [
          'materialCode',
          'materialDescription',
          'materialStatus',
          'categoryId',
          'specifications',
          'mfg',
          'mpn',
          'dosage',
          'basicUnit',
          'quantityFrom',
          'quantityTo',
          'historyPrice',
          'historySupplierName',
          'historyCurrency',
          'historyRates',
          'lowestUnitPrice',
          'lowestUnitPriceCurrency',
          'lowestSupplierName',
          'recommendedUnitPriceTax',
          'recommendationVsHistoricalRateChange',
          'recommendedSupplierName'
        ],
        // 根据物料+供应商合并的字段
        quotationsMaterialSupplierFields: [
          'supplierName',
          'quotationsCount',
          'currency',
          'exchangeRate',
          'taxRate',
          'moq',
          'deliveryPeriod',
          'orderUnit',
          'paymentMethod',
          'priceValidUntil'
        ],
        // 无论如何都根据供应商合并字段
        fullFields: [
          'proportion',
          'priceUnit',
          'recommendedPriceExpires'
        ]
      },
      selectedRecord: [],
      // 历史价格保存对象
      historyData: {
        projectMaterialId: null,
        historyPrice: null,
        historySupplierId: null,
        historySupplierName: '',
        historyCurrency: null,
        avplMaterialSupplierQuantityLadderId: null
      },
      simulationMaterialNum: 0,
      simulateResultVisible: false,
      simulationResult: [],
      suppliersProportion: [],
      defaultRecommendMaterial: [],
      defaultTemplateId: null,
      businessCode: null,
      tempRow: null,
      rfqProject: {}
    }
  },
  computed: {
    store() {
      return store
    },
    participateSupplierList() {
      return this.advancedRecommend?.suppliers.filter(a => !this.simulation.notWantSuppliers.includes(a.supplierId)) || []
    }
  },
  async mounted() {
    const res = await this.getConfigKey('system.currency.rmb')
    if (res.data) {
      const CNYCurrency = getDictDatas(DICT_TYPE.COMMON_CURRENCY).filter(currencyObj => currencyObj.code === res.data)
      if (CNYCurrency && CNYCurrency[0].id) {
        this.currencyRmb = CNYCurrency[0].id
      }
    }
    this.queryParams.quotationIds = this.recommendOptions.quotationIds
    this.queryParams.projectId = this.recommendOptions.projectId
    this.queryParams.projectMaterialIds = this.recommendOptions.projectMaterialIds
    this.advanced.quotationIds = this.recommendOptions.quotationIds
    this.advanced.projectId = this.recommendOptions.projectId
    this.advanced.targetCurrency = this.currencyRmb
    this.approvalReportConfig.projectId = this.recommendOptions.projectId
    // 自定义字段放在订单单位后面
    this.columnsStartIndex = this.girdOption.columns.findIndex((arr) => arr.field === 'orderUnit') >= 0 ? this.girdOption.columns.findIndex((arr) => arr.field === 'orderUnit') + 1 : this.girdOption.columns.length
    this.mergeRowFields.quotationsMaterialSupplierFieldLength = this.mergeRowFields.quotationsMaterialSupplierFields.length
    this.getTemplate()
    this.getRecommendTemplate()
    this.getCategories()
    this.getRfqProject()
    localStorage.removeItem('advanced')
    this.businessCode = this.$route.query.code
  },
  methods: {
    deepEqual,
    /**
     * 关闭退回物料的窗口
     */
    closeMaterialReturn(reload) {
      this.rfqReturnCommentVisible = false
      if (reload) {
        // 必须清空，否则会导致退回的数据还存储在前端的selectedRecord里面，再次点击老数据还存在
        this.selectedRecord = []
        this.getList()
      }
    },
    showReturnForm() {
      if (this.selectedRecord.length === 0) {
        this.$message.error(this.$t('rfq.pleaseSelectMaterialsFirst'))
        return
      }
      this.selectMaterialIds = null
      this.selectMaterialIds = [...new Set(this.selectedRecord.map(j => j.projectMaterialId))].join(',')
      this.rfqReturnCommentVisible = true
    },
    dayjs,
    getRfqProject() {
      getRfqProject(
        {
          id: this.queryParams.projectId
        }
      ).then(res => {
        this.rfqProject = res.data
      })
    },
    ensureParticipate() {
      console.log(this.simulation.notWantSuppliers)
      console.log(this.advancedRecommend.suppliers.length)
      if (this.simulation.notWantSuppliers.length === this.advancedRecommend.suppliers.length) {
        this.$message.warning(this.$t('rfq.atLeastRetainOneSupplierToParticipateInSimulationDecisionmaking'))
        this.simulation.notWantSuppliers.pop()
      }
    },
    targetCurrencyChange() {
      const reqList = []
      this.advanced.configureCurrency.forEach(v => v.amount = null)
      this.advanced.configureCurrency.forEach(v => reqList.push({ fromCurrency: this.advanced.targetCurrency, toCurrency: v.currency }))
      getExchangeRateByFromCurrencyAndToCurrency(reqList).then(res => {
        const data = res.data
        this.advanced.configureCurrency.forEach(v => {
          if (v.currency === this.advanced.targetCurrency) {
            v.amount = 1
          }
          const find = data.find(v1 => v1.toCurrency === v.currency && v1.fromCurrency === this.advanced.targetCurrency)
          if (find) {
            v.amount = find.rate
          }
        })
      })
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    // 物料列表的跨页全选框
    checkBoxAllChange({ checked }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.recommendationAnalysis
      if (checked) {
        const projectMaterialIds = Array.from(new Set([...this.selectedRecord.map(item => item.projectMaterialId)]))
        if (projectMaterialIds?.length + this.total > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.clearCheckboxRow()
          // 当前选中的数据key数组
          const selectIds = grid.getCheckboxRecords().map(item => item.projectMaterialId)
          // 取交集
          const arr = projectMaterialIds.filter(x => selectIds.includes(x))
          if (arr.length > 0) {
            arr.forEach((id) => {
              grid.setCheckboxRow(grid.getRowById(id), true)
            })
          }
        } else {
          this.checkAllData(checked)
        }
      } else {
        this.checkAllData(checked)
      }
    },
    checkAllData(checked) {
      // 先把当前页选中的添加进来，以防多浏览器的时候，数据还是老的。解决UFFF-1136的单子
      this.selectedRecord = Array.from(new Set([...this.selectedRecord, ...this.$refs.recommendationAnalysis.getCheckboxRecords().map(item => {
        return {
          projectMaterialId: item.projectMaterialId,
          quotationsId: item.quotationsId,
          quotationsMaterialSupplierRelId: item.quotationsMaterialSupplierRelId
        }
      })]))
      getRecommendMaterialAnalyticsAllIds(this.queryParams).then(res => {
        if (checked) {
          this.selectedRecord = Array.from(new Set([...this.selectedRecord, ...res.data]))
        } else {
          const projectMaterialIds = Array.from(new Set([...res.data.map(item => item.projectMaterialId)]))
          this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => !projectMaterialIds.includes(x.projectMaterialId))))
        }
      })
    },
    closeMaterial(close) {
      close()
    },
    // 物料列表的单个选择框
    checkBoxChange({ checked, row }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.$refs.recommendationAnalysis
      if (checked) {
        const projectMaterialIds = Array.from(new Set([...this.selectedRecord.map(item => item.projectMaterialId)]))
        if (projectMaterialIds?.length + 1 > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.setCheckboxRow(grid.getRowById(row.projectMaterialId), false)
        } else {
          /* 上面的取法无法取单个选择物料的所有报价ID*/
          this.selectedRecord = Array.from(new Set([...this.selectedRecord, ...this.$refs.recommendationAnalysis.getCheckboxRecords().map(item => {
            return {
              projectMaterialId: item.projectMaterialId,
              quotationsId: item.quotationsId,
              quotationsMaterialSupplierRelId: item.quotationsMaterialSupplierRelId
            }
          })]))
        }
      } else {
        this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => row.projectMaterialId !== x.projectMaterialId)))
      }
    },
    // 跨页全选的清空
    clearSelected() {
      const grid = this.$refs.recommendationAnalysis
      this.selectedRecord = []
      grid.clearCheckboxRow()
    },
    showHistoryPrice(id, code) {
      this.historyData.projectMaterialId = id
      this.historyPriceCode = code
      this.historyVisible = true
    },
    // 保存选择的历史价格
    saveHistoryData() {
      if (this.$refs.historyPriceDialog.$refs.historyPriceGrid.getRadioRecord() == null) {
        this.$message.warning(this.$t('order.pleaseSelectAPieceOfData'))
        return
      }
      const row = this.$refs.historyPriceDialog.$refs.historyPriceGrid.getRadioRecord()
      this.historyData.historyPrice = row.originalUnitPriceWithoutTax
      this.historyData.historySupplierId = row.supplierId
      this.historyData.historySupplierName = row.supplierName
      this.historyData.historyCurrency = row.originalCurrency
      this.historyData.avplMaterialSupplierQuantityLadderId = row.avplMaterialSupplierQuantityLadderId
      updateProjectMaterialHisPrice(this.historyData).then(res => {
        this.historyVisible = false
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.getList()
      })
    },
    /** 下载模板操作 */
    importTemplate() {
      exportMaterialAnalyticsRecommendTemplate(this.queryParams).then(response => {
        this.$download.excel(response, this.$t('rfq.comparisonAnalysisImportTemplatexls'))
      })
    },
    /** 批量操作 */
    handleImport() {
      this.upload.title = this.$t('rfq.batchOperation')
      this.upload.open = true
      this.upload.type = this.$route.params.type
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createData) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createData.length
      }
      if (data.failureData) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureData).length
        for (const index in data.failureData) {
          text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + this.$t('rfq.lineNumber') + index + '：' + data.failureData[index]
        }
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    getTemplateFields() {
      getCustomFieldsByTemplate({ templateId: this.queryParams.templateId }).then(
        res => {
          // 处理表格的自定义列
          this.girdOption.columns.splice(this.columnsStartIndex, this.customColumnsLength, ...res.data.map(item => {
            return {
              title: item.fieldName,
              field: item.fieldCode,
              slots: item.fieldDictType ? { default: item.fieldCode } : null,
              // headerClassName: 'blue',
              visible: true,
              width: 100
            }
          }))
          // 重置自定义字段列长度
          this.customColumnsLength = 0
          if (res.data) {
            this.customColumnsLength = res.data.length
          }
          // 处理合并单元格列，默认自定义列都按照物料+供应商合并
          this.mergeRowFields.quotationsMaterialSupplierFields.splice(this.mergeRowFields.quotationsMaterialSupplierFieldLength, this.mergeRowFields.quotationsMaterialSupplierFields.length - this.mergeRowFields.quotationsMaterialSupplierFieldLength, ...res.data.map(item => (item.fieldCode)))
          this.$refs.customFields.selectSchemeRadio()
        }
      )
    },
    getRecommendTemplate() {
      getTemplateRecommend({
        ...this.recommendOptions
      }).then(res => {
        this.templateTabs = res.data
        this.queryParams.templateId = res.data[0].templateId.toString()
        this.approvalReportConfig.quotationsTemplate = res.data[0].templateId
        this.getTemplateCustomeFields()
      })
    },
    changeTabs() {

    },
    showOperationLog(obj1, obj2) {
      this.logVisible = true
      this.businessId = obj1
      this.businessType = obj2
    },
    showAdvancedConfig(config) {
      checkRecommendAccess({
        ...this.recommendOptions
      }).then(res => {
        if (config === 'advancedConfig') {
          this.advancedConfigVisible = true
          this.resetAdvanced()
          this.getAdvanced()
        } else {
          if (this.selectedRecord.length === 0) {
            this.$message.error(this.$t('rfq.pleaseSelectMaterialsFirst'))
            return
          }
          this.getAdvancedSimulation()
          this.simulateVisible = true
          this.simulation.targetCurrency = this.currencyRmb
        }
      })
    },
    getAdvancedSimulation() {
      getAdvancedRecommend({
        ...this.recommendOptions,
        projectMaterialIds: [...new Set(this.selectedRecord.map(j => j.projectMaterialId))],
        templateId: this.queryParams.templateId
      }).then(res => {
        this.advancedRecommend = res.data
        this.simulation = {
          projectId: this.queryParams.projectId,
          targetOrderQuantity: null,
          simulationScenario: null,
          currencyExchangeRateList: [
            {
              rate: 1,
              currency: 3
            }
          ],
          simulationProgramList: [],
          participate: [],
          projectMaterialIds: [...new Set(this.selectedRecord.map(j => j.projectMaterialId))],
          notWantSuppliers: [],
          principles: [],
          quotationIds: this.advanced.quotationIds,
          targetCurrency: 3,
          templateId: Number(this.queryParams.templateId)
        }
        this.simulation.currencyExchangeRateList = [...this.simulation.currencyExchangeRateList,
          ... this.advancedRecommend.currencyList
            .filter(a => a.id !== this.currencyRmb).map(item => {
              return {
                rate: null,
                currency: item.id
              }
            })]
      })
    },
    getAdvanced() {
      const queryObj = {
        ...this.recommendOptions,
        templateId: this.queryParams.templateId
      }
      getAdvancedRecommend(queryObj).then(res => {
        this.advancedRecommend = res.data

        this.advanced.configureCurrency = this.advancedRecommend.currencyList.map(item => {
          return {
            amount: item.code === 'CNY' ? 1 : null,
            currency: item.id
          }
        })
        this.advanced.targetCurrency = this.currencyRmb
        this.advanced.amount = 1
        const item1 = localStorage.getItem('advanced')
        if (item1) {
          const parse = JSON.parse(item1)
          let flag = true
          if (parse.projectId !== queryObj.projectId) {
            flag = false
          }
          if (parse.projectMaterialIds.length !== queryObj.projectMaterialIds.length) {
            flag = false
          }
          for (const projectMaterialId of parse.projectMaterialIds) {
            if (queryObj.projectMaterialIds.findIndex(v => v === projectMaterialId) < 0) {
              flag = false
            }
          }
          if (flag) {
            this.advanced = { ...JSON.parse(item1) }
          } else {
            this.targetCurrencyChange()
            localStorage.removeItem('advanced')
          }
        } else {
          this.targetCurrencyChange()
        }
      })
    },
    saveAdvanced() {
      this.advanced.templateId = this.queryParams.templateId
      if (this.advanced.targetCurrency == null) {
        this.$message.warning(this.$t('rfq.targetCurrencyMustBeSelected'))
        return
      }
      if (!this.advanced.configureCurrency.every(item => !!item.amount)) {
        this.$message.warning(this.$t('rfq.exchangeRateRequired'))
        return
      }
      if (!this.advanced.principles || !this.advanced.principles.every(item => !!item.principle)) {
        this.$message.warning(this.$t('rfq.theSupplierRecommendationPrincipleMustBeSelected'))
        return
      }
      localStorage.setItem('advanced', JSON.stringify(this.advanced))
      saveAdvancedRecommend(this.advanced).then(res => {
        this.advancedConfigVisible = false
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.getList()
        this.refreshTemplateTab()
      })
    },
    addPrinciple() {
      this.advanced.principles.push({
        principle: ''
      })
    },
    resetAdvanced() {
      this.advanced = {
        configureCurrency: this.advancedRecommend.currencyList?.map(item => {
          return {
            amount: null,
            currency: item.id
          }
        }),
        notWantSuppliers: [],
        principles: [],
        ...this.recommendOptions,
        targetCurrency: this.currencyRmb
      }
      this.advanced.principles.push({
        principle: 'lowest_cost'
      })
      this.advanced.principles.push({
        principle: 'moq_min'
      })
      this.advanced.principles.push({
        principle: 'shortest_lead_time'
      })
    },
    getTemplate() {
      getTemplateList({ code: 'rfq_template' }).then(res => {
        this.templateList = res.data
        if (this.templateList?.length > 0) {
          this.approvalReportConfig.reportId = this.templateList.at(0).id
        }
      })
    },
    downloadMaterialDrawing(materialId, materialCode) {
      downloadMaterialDrawings({ projectMaterialId: materialId }).then(res => {
        this.$download.zip(res, materialCode + '.zip')
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    },

    showMaterialDetail(id) {
      this.materialVisible = true
      this.$refs.rfqMaterial.showMaterialDetail(id)
    },
    // 系统推荐
    systemRecommend() {
      const item1 = localStorage.getItem('advanced')
      if (item1) {
        saveAdvancedRecommend(this.advanced).then(res => {
          this.getList()
        })
        return
      }
      // 模板切换清空选择
      this.clearSelected()
      this.approvalReportConfig.quotationsTemplate = Number(this.queryParams.templateId)
      this.clearSelected()
      // XUERES - 1509 比价推荐配置优化
      getNotRecommend(this.queryParams).then(j => {
        this.defaultRecommendMaterial = this.queryParams.projectMaterialIds
        this.defaultTemplateId = Number(this.queryParams.templateId)
        this.getDefaultRecommendConfig()
      })
    },

    // 根据模板id获取模板的自定义列表
    getTemplateCustomeFields() {
      // 模板切换清空选择
      this.clearSelected()
      this.approvalReportConfig.quotationsTemplate = Number(this.queryParams.templateId)
      this.clearSelected()
      // XUERES - 1509 比价推荐配置优化
      getNotRecommend(this.queryParams).then(j => {
        this.defaultRecommendMaterial = j.data
        if (this.defaultRecommendMaterial.length > 0) {
          this.defaultTemplateId = Number(this.queryParams.templateId)
          this.getDefaultRecommendConfig()
        } else {
          this.getTemplateFields()
          this.getList()
        }
      })
    },
    getDefaultRecommendConfig() {
      checkRecommendAccess({
        ...this.recommendOptions, projectMaterialIds: this.defaultRecommendMaterial, quotationIds: null
      }).then(res => {
        this.resetAdvanced()
        this.getDefaultAdvanced()
      })
    },
    getDefaultAdvanced() {
      getAdvancedRecommend({
        ...this.recommendOptions,
        projectMaterialIds: this.defaultRecommendMaterial,
        quotationIds: null,
        templateId: this.defaultTemplateId
      }).then(res => {
        this.advancedRecommend = res.data
        this.advanced.configureCurrency = this.advancedRecommend.currencyList.map(item => {
          return {
            amount: 1,
            currency: item.id
          }
        })
        this.advanced.targetCurrency = this.currencyRmb
        this.advanced.amount = 1
        this.advanced.templateId = this.defaultTemplateId
        this.defaultRecommend()
      })
    },
    defaultRecommend() {
      saveAdvancedRecommend(this.advanced).then(res => {
        this.getTemplateFields()
        this.getList()
        this.refreshTemplateTab()
      })
    },
    getList() {
      this.loading = true
      pageMaterialAnalyticsRecommend(this.queryParams).then(res => {
        this.loading = false
        res.data.list?.forEach(item => {
          item.recommendedPriceExpires = parseTime(item.recommendedPriceExpires, '{y}-{m}-{d}')
        })
        this.list = res.data.list
        this.total = res.data.total
        this.$nextTick(() => {
          const grid = this.$refs.recommendationAnalysis
          const projectMaterialIds = Array.from(new Set([...this.selectedRecord.map(item => item.projectMaterialId)]))
          this.list?.forEach((row) => {
            if (projectMaterialIds?.includes(row.projectMaterialId)) {
              setTimeout(() => {
                grid.setCheckboxRow(grid.getRowById(row.projectMaterialId), true)
              }, 0)
            }
          })
        })
      })
    },
    saveRecommendInfo(row) {
      const {
        projectMaterialId,
        priceUnit,
        proportion,
        quotationsMaterialSupplierQuantityLadderId,
        quotationsMaterialSupplierRelId,
        recommend,
        recommendedPriceExpires
      } = row
      updateMaterialAnalyticsRecommend({
        projectMaterialId,
        priceUnit,
        proportion,
        quotationsMaterialSupplierQuantityLadderId,
        quotationsMaterialSupplierRelId,
        recommend,
        recommendedPriceExpires,
        projectId: this.queryParams.projectId,
        templateId: this.queryParams.templateId
      }).then(res => {
        this.getList()
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.refreshTemplateTab()
      })
    },
    /**
     * 刷新tab头的数字
     */
    refreshTemplateTab() {
      getTemplateRecommend({
        ...this.recommendOptions
      }).then(res => {
        this.templateTabs = res.data
        if (!this.templateTabs || this.templateTabs.length === 0) {
          // 全部提交了，返回列表页
          this.backPrevious()
        }
        if (!this.templateTabs.some(j => String(j.templateId) === String(this.queryParams.templateId))) {
          this.queryParams.templateId = String(this.templateTabs[0].templateId)
          this.getTemplateCustomeFields()
        }
      })
    },
    showReportConfig() {
      const data = this.getQuotations()
      if (this.selectedRecord.length === 0) {
        this.$message.error(this.$t('rfq.pleaseSelectMaterialsFirst'))
        return
      }
      checkSubmit({
        projectMaterialIds: [...new Set(this.selectedRecord.map(j => j.projectMaterialId))].join(',')
      }).then(res => {
        this.approvalReportConfig.projectMaterialIds = Array.from(new Set([...this.selectedRecord.map(j => j.projectMaterialId)]))
        this.approvalReportConfig.projectMaterialId = this.approvalReportConfig.projectMaterialIds.join(',')
        this.approveReportVisible = true
        this.getTemplateFile(data)
      })
    },
    getQuotations() {
      return [...new Set(this.selectedRecord.map(item => item.projectMaterialId))]
    },
    getTemplateFile(data) {
      listByBusinessIds({
        businessIds: data.join(','),
        businessType: 'RFQ_QUOTATIONS'
      }).then(res => {
        res.data.forEach(item => {
          item.check = true
        })
        this.templateFiles = res.data
      })
    },
    getCellClassName({ row, _rowIndex, column, visibleData }) {
      if (row && (['checkbox'].includes(column.type) || this.mergeRowFields.fields.includes(column.property))) {
        return ''
      } else {
        if (row.recommend) {
          return 'highlight'
        } else {
          return ''
        }
      }
    },
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const prevRow = visibleData[_rowIndex - 1]
      let nextRow = visibleData[_rowIndex + 1]
      // 按照物料进行合并，选择框也按照物料合并
      if (row && (['checkbox'].includes(column.type) || this.mergeRowFields.fields.includes(column.property))) {
        if (prevRow && row.projectMaterialId === prevRow.projectMaterialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.projectMaterialId === nextRow.projectMaterialId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      } else if ([...this.mergeRowFields.fullFields, ...this.mergeRowFields.quotationsMaterialSupplierFields].includes(column.property) && row) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && row.quotationsMaterialSupplierRelId === prevRow.quotationsMaterialSupplierRelId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.quotationsMaterialSupplierRelId === nextRow.quotationsMaterialSupplierRelId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    openFile(url) {
      window.open(url)
    },
    resetApprove() {
      this.approvalReportConfig = {
        approvalMaterialSupplierRelIds: [],
        approvalName: '',
        approvalNo: '',
        attachmentRelFileIds: [],
        files: '',
        flag: '',
        // id: 0,
        // status: 'pending_approval',
        previewRecordId: 0,
        projectId: this.queryParams.projectId,
        quotationsTemplate: Number(this.queryParams.templateId),
        recommendRemark: '',
        reportId: null,
        urgent: false
      }
    },
    checkReport() {
      if (this.approvalReportConfig.reportId === null) {
        this.$message.error(this.$t('rfq.pleaseSelectAReportTemplate'))
        return false
      } else {
        return true
      }
    },
    handleApprove() {
      if (!this.checkReport()) {
        return
      }
      this.approvalReportConfig.attachmentRelFileIds = this.templateFiles.filter(j => j.check).map(j => j.fileId)
      this.approvalReportConfig.projectMaterialIds = Array.from(new Set(this.selectedRecord.map(item => item.projectMaterialId)))
      this.approvalReportConfig.files = this.templateFiles.filter(j => j.check).map(j => j.fileName).join('/')
      if (!this.approvalReportConfig.approvalName) {
        this.$message.error(this.$t('rfq.pleaseEnterTheApprovalName'))
        return
      }
      this.approvalReportConfig.attachmentRelFileIds = this.templateFiles.filter(j => j.check).map(j => j.fileId)
      this.handleApproveLoading = true
      checkStatus({
        projectMaterialIds: this.selectedRecord
          .map(item => item.projectMaterialId).join(',')
      }).then(res => {
        const istrue = res.data
        if (istrue) {
          savePriceApproval({
            ...this.approvalReportConfig
          }
          ).then(res => {
            this.approveReportVisible = false
            this.$message.success(this.$t('supplier.submittedSuccessfully'))
            this.emitter.emit('freshStep', this.queryParams.projectId)
            this.resetApprove()
            this.getList()
            this.handleApproveLoading = false
            this.refreshTemplateTab()
          }).catch(reason => {
            this.handleApproveLoading = false
            this.refreshTemplateTab()
          })
        } else {
          this.$message.error(this.$t('rfq.thereAreDataInNonStatusMaterialStatustoBeRecommendedAndRfqStatusquotedSubmissionFailed'))
          this.handleApproveLoading = false
          this.refreshTemplateTab()
        }
      }).catch(reason => {
        this.handleApproveLoading = false
        this.refreshTemplateTab()
      })
    },
    // push to bom
    pushToBom() {
      if (!this.checkReport()) {
        return
      }
      this.approvalReportConfig.projectMaterialIds = Array.from(new Set(this.selectedRecord.map(item => item.projectMaterialId)))
      this.approvalReportConfig.files = this.templateFiles.filter(j => j.check).map(j => j.fileName).join('/')
      this.approvalReportConfig.attachmentRelFileIds = this.templateFiles.filter(j => j.check).map(j => j.fileId)
      this.handleApproveLoading = true
      checkStatus({
        projectMaterialIds: this.selectedRecord
          .map(item => item.projectMaterialId).join(',')
      }).then(res => {
        const isTrue = res.data
        if (isTrue) {
          pushPriceToBom({
            ...this.approvalReportConfig
          }
          ).then(res => {
            this.approveReportVisible = false
            this.$message.success(this.$t('操作成功'))
            this.resetApprove()
            this.getList()
            this.handleApproveLoading = false
          }).catch(reason => {
            this.handleApproveLoading = false
          })
        } else {
          this.$message.error(this.$t('所选择的物料不满足操作条件'))
          this.handleApproveLoading = false
          this.refreshTemplateTab()
        }
      }).catch(reason => {
        this.handleApproveLoading = false
        this.refreshTemplateTab()
      })
    },
    // 提交不审批
    handleSubmitNoApprove() {
      this.approvalReportConfig.projectMaterialIds = Array.from(new Set(this.selectedRecord.map(item => item.projectMaterialId)))
      this.approvalReportConfig.files = this.templateFiles.filter(j => j.check).map(j => j.fileName).join('/')

      this.approvalReportConfig.attachmentRelFileIds = this.templateFiles.filter(j => j.check).map(j => j.id)
      this.handleSubmitNoApproveLoading = true
      checkStatus({
        projectMaterialIds: this.selectedRecord
          .map(item => item.projectMaterialId).join(',')
      }).then(res => {
        const istrue = res.data
        if (istrue) {
          savePriceApprovalCompleted({ ...this.approvalReportConfig }).then(res => {
            this.approveReportVisible = false
            this.$message.success(this.$t('supplier.submittedSuccessfully'))
            this.emitter.emit('freshStep', this.queryParams.projectId)
            this.resetApprove()
            this.getList()
            this.handleSubmitNoApproveLoading = false
          })
        } else {
          this.$message.error(this.$t('rfq.thereAreDataInNonStatusMaterialStatustoBeRecommendedAndRfqStatusquotedSubmissionFailed'))
          this.handleSubmitNoApproveLoading = false
        }
      })
    },
    onRemove(file, fileList) {
      this.approvalReportConfig.uploadFileIds = this.approvalReportConfig.uploadFileIds.filter(item => item !== file.id)
    },
    onSuccess(response, file, fileList) {
      this.approvalReportConfig.uploadFileIds.push(response.data.id)
    },
    cancelApprove() {
      this.approveReportVisible = false
      this.resetApprove()
      this.getTemplate()
    },
    handlePreview() {
      if (!this.checkReport()) {
        return
      }
      // 注意，此处的templateList主版本只有一个，所以仅取第一个进行预览报告
      const name = this.rfqProject.eventName + '-' + this.templateList[0].name.replace('.ureport.xml', '') + '-' + dayjs().format('YYYY-MM-DD')
      this.approvalReportConfig.files = this.templateFiles.filter(j => j.check).map(j => j.fileName).join('/')
      createPreviewRecord(this.approvalReportConfig).then(res => {
        var mapObject = new Map()
        mapObject.set('id', res.data)
        mapObject.set('project_id', this.approvalReportConfig.projectId)
        mapObject.set('project_material_id', this.approvalReportConfig.projectMaterialId)
        mapObject.set('_n', name)
        // mapObject.set('locale', getLanguage())
        this.showPreviewReport(this.approvalReportConfig.reportId, mapObject)
      })
    },
    // 弹出预览
    showPreviewReport(reportId, mapObject) {
      getPreviewReport({
        reportId: reportId,
        ...Object.fromEntries(mapObject)
      }).then(res => {
        window.open(res.data)
      })
    },
    showCalculator() {
      this.priceTable = []
      this.quantityNum = null
      const projectMaterialIds = this.$refs.recommendationAnalysis.getCheckboxRecords().map(item => item.projectMaterialId)
      if (projectMaterialIds.length === 0) {
        this.$message.error(this.$t('rfq.pleaseSelectMaterial'))
        return
      }
      this.list.filter(item => projectMaterialIds.includes(item.projectMaterialId)).map(item => {
        const exist = this.priceTable.find(a => a.supplier === item.supplierName)
        if (exist) {
          exist.data.push({
            quantity: item.quotationQuantityFrom,
            price: item.priceUnit,
            currency: item.currency,
            flag: 0
          })
        } else {
          this.priceTable.push({
            supplier: item.supplierName,
            color: this.getRandomColor(),
            data: [{
              quantity: item.quotationQuantityFrom,
              price: item.priceUnit,
              currency: item.currency,
              flag: 0
            }]
          })
        }
      })
      this.calculateVisible = true
    },
    getRandomColor() {
      const letters = '0123456789ABCDEF'.split('')
      let color = '#'
      for (let i = 0; i < 6; i++) {
        color += letters[Math.round(Math.random() * 15)]
      }
      return color
    },

    freshPrice() {
      if (this.quantityNum <= 0 || !this.quantityNum) {
        return
      }
      let total = Number.MAX_SAFE_INTEGER
      const obj = {
        supplier: 0,
        price: 0
      }
      this.priceTable.map(item => {
        item.data.map(i => {
          if (i.quantity <= this.quantityNum &&
            i.price * this.quantityNum < total
            // todo 这里缺少汇率
          ) {
            total = i.price * this.quantityNum // todo
            obj.supplier = item.supplier
            obj.price = i.price
          }
        })
      })
      if (obj.supplier) {
        this.priceTable.find(p => p.supplier === obj.supplier)
          .data.find(da => da.price === obj.price).flag = 1
      }
    },
    tableRowClassName({ row }) {
      if (row.flag === 1) {
        return 'warning-row'
      }
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.url) {
        window.open(file.url)
      }
    },

    backPrevious() {
      this.$emit('clearRecommendOptions')
    },
    resetQuery() {
      this.queryParams.search = ''
      this.queryParams.pageNo = 1
      this.queryParams.pageSize = 10
      this.getList()
    },
    addSupplier(val) {
      this.simulation.simulationScenario = ''
      this.simulation.simulationProgramList.push({
        simulationProgram: val,
        secondSimulationProgram: '',
        supplierIds: []
      })
    },
    secondPrinciple(first) {
      return getDictDatas(DICT_TYPE.RFQ_SIMULATION_PROGRAM).filter(a => !['priceComparisonAnalysisPageSolution', 'singleSupplierSolution', first].includes(a.value))
    },
    generateSimulation() {
      if (!this.simulation.targetOrderQuantity) {
        this.$message.error(this.$t('rfq.pleaseFillInTheTargetOrderQuantity'))
        return
      }
      if (!this.simulation.currencyExchangeRateList.every(a => a.rate > 0)) {
        this.$message.error(this.$t('rfq.pleaseFillInTheCompleteExchangeRate'))
        return
      }

      const singleSupplierSolution = this.simulation.simulationProgramList.find(j => j.simulationProgram === 'singleSupplierSolution')
      if (singleSupplierSolution) {
        if (singleSupplierSolution.supplierIds.length <= 0) {
          this.$message.error(this.$t('rfq.selectAtLeastOneSupplierForASingleSupplierSolution'))
          return
        }
      }

      checkQuotationQuantity({ ...this.simulation,
        participate: this.participateSupplierList.map(a => a.supplierId)
      }).then(res => {
        if (!res.data) {
          this.$confirm(this.$t('rfq.detectedThatTheMaterialQuotationLadderDoesNotMeetTheTargetOrderQuantityClickCancelToReturnToModificationClickConfirmToSkipPricelessMaterialsAndContinueExecution'), this.$t('supplier.tips'), { type: 'warning' }).then(
            () => {
              simulationResults({ ...this.simulation,
                participate: this.participateSupplierList.map(a => a.supplierId)
              }).then(res => {
                res.data.results.forEach((a, index) => {
                  res.data.suppliersProportion.forEach(b => {
                    a[String(b.supplierId)] = b.results.at(index)
                  })
                })
                this.simulationKey = res.data.key
                this.simulationMaterialNum = res.data.materialCount
                this.simulationResult = res.data.results
                this.suppliersProportion = res.data.suppliersProportion
                this.simulateResultVisible = true
              })
            }
          )
        } else {
          simulationResults({ ...this.simulation,
            participate: this.participateSupplierList.map(a => a.supplierId)
          }).then(res => {
            res.data.results.forEach((a, index) => {
              res.data.suppliersProportion.forEach(b => {
                a[String(b.supplierId)] = b.results.at(index)
              })
            })
            this.simulationKey = res.data.key
            this.simulationMaterialNum = res.data.materialCount
            this.simulationResult = res.data.results
            this.suppliersProportion = res.data.suppliersProportion
            this.simulateResultVisible = true
          })
        }
      })
    },
    downloadSimulateResult() {
      exportAnalyzeQuote({
        key: this.simulationKey
      }).then(res => {
        this.$download.excel(res, '模拟预测结果.xlsx')
      })
    },
    applySimulation(simulationProgram) {
      applicationResults({
        key: this.simulationKey,
        simulationProgram
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.simulateVisible = false
        this.simulateResultVisible = false
        this.getList()
        this.refreshTemplateTab()
      })
    },
    closesim() {
      // this.simulateVisible = false
      this.simulateResultVisible = false
    },
    optimalResults({ row, column, rowIndex, columnIndex }) {
      console.log(11)
      if (columnIndex < 5 && columnIndex > 1) {
        const min = Math.min(...this.simulationResult.map(a => a[column.property]))
        if (min === row[column.property]) {
          return 'optimalResult'
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

::v-deep .highlight{
  background-color: #e7faf0;
}

::v-deep .row--hover{
  .highlight{
    background-color:#e6f7ff !important;
  }
}
::v-deep .row--current{
  .highlight{
    background-color: #e6f7ff !important;
  }
}

::v-deep .labelTitle {
  font-weight: bolder !important;
  color: black;
  width: 75px;
  font-size: 13px;
}

::v-deep .requiredLabelTitle::before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

::v-deep .requiredLabelTitle {
  font-weight: bolder !important;
  color: black;
  width: 80px;
  font-size: 13px;

}

::v-deep .gray {
  background: #999999;
}

::v-deep .yellow {
  background: #ffeb3b;
}

::v-deep .red {
  background: #ff5733;
}

::v-deep .indigo {
  background: #a5d642;
}

::v-deep .blue {
  background: #2684e4;
}

::v-deep .green {
  background: #41d17b;
}

::v-deep .el-table .warning-row {
  background: #b9edce;
}

::v-deep .optimalResult{
  background: #43cf7c;
}

.commonMaterialItem {
  width: 33%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
}

.rfq-create-upload {
  margin: 10px 0
}

.rfq-create {
  margin-top: 15px;
}

.materialItem {
  width: 178px;
}

.rfqMaterial {
  .mainTab {
    margin-bottom: 15px;
  }
}

</style>
