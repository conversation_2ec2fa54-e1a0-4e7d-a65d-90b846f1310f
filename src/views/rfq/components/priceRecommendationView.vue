<template>
  <div>
    <priceRecommendationOperate
      v-if="recommendOptions.quotationIds.length || recommendOptions.projectMaterialIds.length"
      :recommend-options.sync="recommendOptions"
      :steps="steps[2]"
      @clearRecommendOptions="clearRecommendOptions"
    />
    <div v-else>
      <div class="el-card__header" style="font-size: 14px;border-bottom: none">
        {{rfqProject.eventName}} /  {{rfqProject.customName}} / {{rfqProject.expirationDate}}
      </div>
      <el-card class="cardSpacing">
        <div slot="header">
          <div style="display: flex;justify-content: space-between;">
            <div style="flex-basis: 30%; flex-grow: 0;">
              {{ $t('rfq.priceComparisonAndRecommendation') }}
            </div>
            <div style="flex-basis: 70%; flex-grow: 0;display: flex; justify-content: flex-end;font-size: 14px;padding-right: 10px">
              <a style="font-size: large;margin-right: 4px">{{materialSummaryReport.total}} {{$t('In Total') }}</a>
              |
              <a style="font-size: large;margin-right: 4px;margin-left: 4px">{{materialSummaryReport.noNeedQuote}} {{$t('No Need to Quote') }}</a>
              |
              <a style="font-size: large;margin-right: 4px;margin-left: 4px">{{materialSummaryReport.missingQuote}} {{$t('Missing Quotation') }}</a>
              |
              <a style="font-size: large;margin-left: 4px">{{materialSummaryReport.quoted}} {{$t('Quoted') }}</a>
             </div>
          </div>

        </div>
        <el-tabs v-model="radio" @tab-click="getList">
          <el-tab-pane :label="$t('汇总')" name="0" />
          <el-tab-pane :label="$t('rfq.inquirySheet')" name="1" />
          <el-tab-pane :label="$t('rfq.material')" name="2" />
        </el-tabs>
        <div style="display: flex;justify-content: center;margin: 10px 0">
          <el-input
            v-if="radio==='0' && isLoadCompleted"
            v-model="queryParams.search"
            :placeholder="$t('供应商名称')"
            clearable
            style="width: 500px"
            @keyup.enter.native="queryParams.pageNo = 1;getList();"
          />
          <el-input
            v-else
            v-model="queryParams.search"
            :placeholder="$t('rfq.rfqItemNameRfqItemNumberQuotationNumberMaterialCodePurchaserSupplierAbbreviation')"
            clearable
            style="width: 500px"
            @keyup.enter.native="queryParams.pageNo = 1;getList();"
          />
          <el-button plain type="primary" @click="queryParams.pageNo = 1;getList();">{{ $t('common.search') }}</el-button>
          <el-button plain style="margin-left: 0" @click="handleClick">{{ $t('common.reset') }}</el-button>
        </div>
        <div style="display: flex;justify-content: center">
          <el-checkbox-group v-if="radio === '1'" v-model="queryParams.quotationsStatusList" @change="radioChange">
            <el-checkbox :key="'all'" label="all">{{ $t('order.whole') }}</el-checkbox>

            <el-checkbox
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_QUOTATION_STATUS,0)"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-checkbox>
          </el-checkbox-group>
          <el-checkbox-group v-else-if="radio === '2'" v-model="queryParams.quoteMaterialStatus" @change="radioChange">
            <el-checkbox :key="'all'" label="all">{{ $t('order.whole') }}</el-checkbox>

            <el-checkbox
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_MATERIAL_STATUS_SEARCH,0)"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-checkbox>
          </el-checkbox-group>
          <el-checkbox v-model="queryParams.isTempMaterial" v-if="radio!=='0'" :label="$t('rfq.viewTemporaryMaterials')" style="margin-left: 30px" @change="radioChange" />
        </div>
        <div style="display: flex;justify-content: space-between;margin-top: 10px" v-if="radio==='0'">
          <div>
            <el-button plain type="primary" @click="exportRfqSummary"  :loading="exportLoading">
              {{ $t('下载汇总报告') }}
            </el-button>
            <el-button plain type="primary" @click="exportAnalysisMfg" :loading="exportLoading">
              {{ $t('品牌竞争性分析报告') }}
            </el-button>
            <el-button plain type="primary" @click="openBestPriceDailog">
              {{ $t('情景 1-最低价') }}
            </el-button>
            <el-button plain type="primary" @click="openBestPriceDailog">
              {{ $t('生成非标报告') }}
            </el-button>
          </div>
        </div>
        <div style="display: flex;justify-content: space-between;margin-top: 10px" v-else>
          <div>
            <el-button
              v-has-permi="['rfq:quotations-recommend:query']"
              type="primary"
              @click="recommendRow"
            >{{ $t('rfq.recommend') }}
            </el-button>

            <el-button
              v-if="radio === '2'"
              v-has-permi="['rfq:quotations-recommend:material-return']"
              plain
              type="danger"
              @click="showBatchReturn"
            >{{ $t('rfq.return') }}
            </el-button>
            <el-button v-has-permi="['rfq:quotations-recommend:export']" plain type="primary" @click="handleExport">
              {{ $t('common.batchExport') }}
            </el-button>
          </div>
        </div>
        <vxe-grid
          v-if="radio === '1'"
          ref="priceRecommendProject"
          key="priceRecommendProject"
          :data="list"
          :loading="loading"
          v-bind="girdOption"
          @checkbox-change="checkBoxChange"
          @checkbox-all="checkBoxAllChange"
        >
          <template #quotationsNo="{row}">
            <copy-button
              style="text-decoration: underline"
              type="text"
              @click="showQuote(row.quotationsNo,row.supplierId,row.quotationId,row.projectId)"
            >
              {{ row.quotationsNo }}
            </copy-button>
          </template>
          <template #quotationStatus="{row}">
            <dict-tag :type="DICT_TYPE.RFQ_QUOTATION_STATUS" :value="row.quotationStatus" />

          </template>
          <template #documentSource="{row}">
            <dict-tag :type="DICT_TYPE.RFQ_SOURCE_OF_INQUIRY" :value="row.documentSource" />

          </template>
          <template #operate="{row}">
            <OperateDropDown
              :menu-item="[
                {
                  name: $t('rfq.downloadQuotationAttachment'),
                  show: $store.getters.permissions.includes('rfq:quotations:query'),
                  action: (row)=>downloadPriceFiles(...row),
                  para: [row]
                },
                {
                  name: $t('rfq.termination'),
                  show: row.quotationStatus==='to_quote'&&$store.getters.permissions.includes('rfq:quotations-recommend:quotation-return'),
                  action: (row)=>showReturnForm(...row,$t('终止意见')),
                  para: [false,row]
                },
                {
                  name: $t('rfq.followAndUrge'),
                  show: row.quotationStatus==='to_quote',
                  action: (row)=>followUp(...row),
                  para: [row]
                },
              ]"
            />
          </template>
          <template #toolbar_buttons>
            <el-alert
              v-if="selectedRecord.length"
              show-icon
              type="success"
            >
              <template slot="title">{{ $t('common.selectedTotal', {selectedTotal: selectedRecord.length}) }}
                <el-button style="padding: 0;margin-left: 10px" type="text" @click="clearSelected">{{
                  $t('rfq.empty')
                }}
                </el-button>
              </template>
            </el-alert>
          </template>
        </vxe-grid>
        <vxe-grid
          v-else-if="radio==='2' && isLoadCompleted"
          ref="priceMaterialProject"
          key="priceMaterialProject"
          :data="list"
          :loading="loading"
          :span-method="mergeMaterialRowMethod"
          v-bind="gridMaterialOption"
          @checkbox-change="checkBoxChange"
          @checkbox-all="checkBoxAllChange"
        >
          <template #materialCode="{row}">
            <i
              v-if="row.materialDrawing"
              class="el-icon-picture-outline"
              style="font-size: 16px;margin-right: 3px;cursor: pointer"
              @click="downloadMaterialDrawing(row.projectMaterialId,row.materialCode)"
            />
            <copy-button
              @click="showMaterialDetail(row.projectMaterialId)"
            >
              {{ row.materialCode }}
            </copy-button>
          </template>
          <template #approvalNo="{row}">
            <copy-button
              @click="$router.push(`/rfq/processHome/${encodeURIComponent(row.approvalNo)}?id=${row.projectId}&approvalId=${row.approvalId}&projectNo=${rfqProject.projectNo}&code=${row.businessCode}&active=3`)"
            >
              {{ row.approvalNo }}
            </copy-button>
          </template>
          <template #quotationsNo="{row}">
            <copy-button
              style="text-decoration: underline"
              type="text"
              @click="$router.push(`/rfq/processHome/${encodeURIComponent(row.quotationsNo)}?id=${row.projectId}&quotationId=${row.quotationsId}&code=${row.businessCode}&active=1`)"
            >
              {{ row.quotationsNo }}
            </copy-button>

          </template>
          <template #quotationsStatus="{row}">
            <dict-tag :type="DICT_TYPE.RFQ_QUOTATION_MATERIAL_STATUS" :value="row.quotationsStatus" />

          </template>
          <template #documentSource="{row}">
            <dict-tag :type="DICT_TYPE.RFQ_SOURCE_OF_INQUIRY" :value="row.documentSource" />
          </template>
          <template #materialStatus="{row}">
            <dict-tag :type="DICT_TYPE.RFQ_MATERIAL_STATUS" :value="row.status" />
          </template>
          <template #categoryId="{row}">
            <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
          </template>
          <template #operate="{row}">
            <OperateDropDown
              :menu-item="[
                {
                  name: $t('rfq.notApprovedTemporarily'),
                  show: radio === '2' &&$store.getters.permissions.includes('rfq:price-approval:save'),
                  action: (row)=>savePriceApprovalCompletedHandler(...row),
                  para: [row]
                },
                {
                  name: $t('rfq.recovery'),
                  show: radio === '2' &&$store.getters.permissions.includes('rfq:price-approval:save'),
                  action: (row)=>submitRecoverHandler(...row),
                  para: [row]
                },
                {
                  name: $t('rfq.termination'),
                  show: ['no_quote','to_quote','quoted'].includes(row.quotationsStatus) &&
                    !['pending_approval','approved'].includes(row.projectMaterialStatus) &&
                    $store.getters.permissions.includes('rfq:quotations-recommend:material-terminate'),
                  action: (row)=>showReturnForm(...row,$t('终止意见')),
                  para: [false,row]
                },
                {
                  name: $t('rfq.return'),
                  show: ['no_quote','quoted'].includes(row.quotationsStatus) && ['to_be_recommended','no_quote','to_quote','approve_return'].includes(row.projectMaterialStatus)
                    &&$store.getters.permissions.includes('rfq:quotations-recommend:material-return'),
                  action: (row)=>showReturnForm(...row,$t('rfq.opinion')),
                  para: [true,row]
                },
              ]"
            />
          </template>
          <template #toolbar_buttons>
            <el-alert
              v-if="selectedRecord.length"
              show-icon
              type="success"
            >
              <template slot="title">{{ $t('common.selectedTotal', {selectedTotal: selectedRecord.length}) }}
                <el-button style="padding: 0;margin-left: 10px" type="text" @click="clearSelected">{{
                  $t('rfq.empty')
                }}
                </el-button>
              </template>
            </el-alert>
          </template>
        </vxe-grid>
        <vxe-grid
          v-else
          ref="rfqSummaryGrid"
          key="rfqSummaryGrid"
          :data="list"
          :loading="loading"
          style="margin-top: 10px"
          v-bind="girdRfqSummaryOption"
        >
          <template #quotedRate="{row}">
            <number-format :value="bestPriceRateComputed(row.quotedRate)" :decimal-place="0" />
          </template>
          <template #bestPriceRate="{row}">
            <number-format :value="bestPriceRateComputed(row.bestPriceRate)" :decimal-place="0" />
          </template>
        </vxe-grid>
        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNo"
          :total="total"
          @pagination="getList"
        />
      </el-card>
    </div>
    <el-dialog :title="$t('rfq.followUpOfRfq')" :visible.sync="followUpOpen" append-to-body width="1000px">
      <el-row>
        <el-col :span="24">
          <div>
            {{ $t('rfq.youAreAboutToSendAFollowupEmailToTheSupplierPleaseEnterTheDescription') }}
          </div>
          <div>
            <el-input v-model="followUpData.content" :rows="5" type="textarea" />
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="followUpOpen = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="actionFollowUp">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
    <el-dialog
      v-if="returnCommentVisible"
      :title="returnCommentTitle"
      :visible.sync="returnCommentVisible"
      width="400px"
    >
      <div v-if="returnCommentForm.isAll">
        <div>{{ $t('rfq.pleaseEnterTheReturnCommentsOfTheRfqSheet') }}</div>
        <p>{{ $t('rfq.afterYouClickOkTheSystemWillAutomaticallyReturnTheQuotation') }}</p>
      </div>
      <div v-else-if="radio === '1'">
        <p>{{ $t('rfq.youAreTerminatingTheRfqPleaseEnterTerminationComments') }}</p>
      </div>
      <div v-else>
        <p>{{ $t('rfq.youAreTerminatingRfqMaterialsPleaseEnterTerminationComments') }}</p>
      </div>
      <el-input
        v-model="returnCommentForm.opinion"
        :placeholder="$t('rfq.pleaseEnterComments')"
        :rows="5"
        type="textarea"
      />
      <div slot="footer">
        <el-button @click="returnCommentVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button :loading="returnButtonFlag" type="primary" @click="submitReturn">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <rfqMaterial
      ref="rfqMaterial"
      :material-visible.sync="materialVisible"
      :rfq-project-id="true"
      :rfq-project-status="null"
    />
    <el-dialog
      v-if="rfqReturnCommentVisible"
      :visible.sync="rfqReturnCommentVisible"
      :title="$t('物料退回供应商报价')"
      width="1000px"
    >
      <materialreturn :material-ids="selectMaterialIds" @closeMaterialReturn="closeMaterialReturn" />
    </el-dialog>
    <!--    推荐配置弹出框-->
    <el-dialog
      :title="$t('rfq.advancedRecommendedConfiguration')"
      :visible.sync="bestPriceVisible"
      width="1000px"
    >
      <el-form ref="advancedRef" :model="bestPriceVAdvanced" inline label-position="top">
        <el-row :gutter="30" style="width: 100%;">
          <el-col :span="10" style="width: 50%">
            <el-form-item :label="$t('rfq.supplierRecommendationPrinciple')">
              <div v-for="(item,index) in bestPriceVAdvanced.principles" :key="index" style="margin: 5px 0">
                <i
                  class="el-icon-delete"
                  style="cursor:pointer;font-size: 18px"
                  @click="bestPriceVAdvanced.principles.splice(index,1)"
                />
                <span style="margin: 0 10px">{{ $t('rfq.principle') }}{{ index + 1 }}</span>
                <el-select v-model="item.principle" :placeholder="$t('rfq.pleaseSelectAPrinciple')" clearable>
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.RFQ_RECOMMEND_PRINCIPLE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </div>
              <div style="text-align: right;width: 300px">
                <i class="el-icon-circle-plus-outline" style="font-size: 18px;cursor:pointer;" @click="addPrinciple" />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="10" style="width: 50%">
            <el-form-item :label="$t('rfq.notParticipatingInPriceRecommendation')">
              <el-checkbox-group v-model="bestPriceVAdvanced.notWantSuppliers">
                <el-checkbox
                  v-for="dict in advancedRecommend.suppliers"
                  :key="dict.value"
                  :label="dict.supplierId"
                >{{ dict.supplierName }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30" style="width: 100%; display: flex; justify-content: center;">
          <el-col :span="30" style="display: flex; justify-content: center; position: relative;">
            <div style="position: absolute; bottom: 10px; display: flex; justify-content: center; width: 100%;">
              <el-button type="primary" @click="exportBestPriceReport" :loading="exportLoading">{{ $t('下载报告') }}</el-button>
              <el-button @click="bestPriceVisible=false">{{ $t('取消') }}</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import {
  checkNoQuote,
  checkRecommend,
  downloadMaterialDrawings,
  downloadPriceFiles,
  followUp,
  getApprovalList, getMaterialByProject,
  getRfqProject
} from '@/api/rfq/home'
import store from '@/store'
import {
  batchReturnRecommendMaterial, exportAnalysisMfg, exportBestPriceReport,
  exportMaterialExcel,
  exportQuotationExcel, exportRfqSummary, getAdvancedRecommend, getMaterialSummaryReport,
  getPageMaterialRecommend,
  getPageQuotationRecommend,
  getRecommendQuotationAllIds,
  getRecommendQuotationMaterialsAllIds, getRfqSummary,
  hasContainOther,
  returnRecommendMaterial,
  returnRecommendQuotation,
  terminateRecommendMaterial,
  terminateRecommendQuotation
} from '@/api/rfq/quoationRecommend'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { savePriceApprovalCompleted, submitRecover } from '@/api/rfq/priceApproval'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import Materialreturn from '@/views/rfq/components/materialReturn.vue'
import {exportForecast} from "@/api/om/orderTracker";
import dayjs from "dayjs";

export default {
  name: 'Pricerecommendationview',
  components: {
    Materialreturn,
    OperateDropDown,
    priceRecommendationOperate: () => import('@/views/rfq/components/priceRecommendationOperate'),
    rfqMaterial: () => import('@/views/rfq/components/material')
  },
  props: ['steps'],
  data() {
    return {
      exportLoading: false,
      // 退回物料窗口
      rfqReturnCommentVisible: false,
      //情景1的高级推荐配置
      bestPriceVisible: false,
      //情景1的高级推荐配置
      bestPriceVAdvanced: {
        projectId: null,
        notWantSuppliers: [],
        principles: [],
      },
      advancedRecommend: {},
      selectMaterialIds: null,
      followUpOpen: false,
      returnCommentVisible: false,
      returnCommentTitle: '',
      materialVisible: false,
      categoryList: [],
      followUpData: {
        quotationIds: [],
        content: ''
      },
      checkRecommend: {
        ids: [],
        checkRecommendTypeEnums: ''
      },
      returnCommentForm: {
        opinion: '',
        isAll: false,
        quotationsMaterialSupplierRelId: '',
        projectMaterialId: '',
        quotationId: '',
        materialCodes: [],
        batch: false,
        projectMaterialIds: []
      },
      configList: {
        customName: this.$t('order.customerName'),
        endCustomer: this.$t('supplier.endCustomer'),
        productName: this.$t('rfq.productName'),
        endCustomerModel: this.$t('rfq.terminalCustomerModel'),
        productNumber: this.$t('rfq.productModel'),
        requestPaymentMethod: this.$t('rfq.paymentMethodRequired'),
        productLine: this.$t('rfq.application'),
        requestDeliveryMethod: this.$t('rfq.requestedDeliveryMethod'),
        shippingDestination: this.$t('rfq.loadingDestination')
      },
      rfqProject: {
        businessModel: 0,
        customName: '',
        endCustomer: '',
        endCustomerModel: '',
        eventName: '',
        expirationDate: '',
        id: '',
        inquiryPurpose: '',
        materialBillManagerList: [store.getters.userId],
        modifyOrderUnits: false,
        productLine: '',
        productName: '',
        productNumber: '',
        projectNo: '',
        purchaseOrg: '',
        requestDeliveryMethod: '',
        requestPaymentMethod: '',
        shippingDestination: '',
        sourcingSources: '',
        status: '',
        supplierPriceLadder: false
      },
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        projectId: null,
        isTempMaterial: false,
        quoteMaterialStatus: ['to_be_recommended', 'approve_return'],
        quotationsStatusList: ['to_quote'],
        search: '',
        sortBy: 'ASC',
        sortField: 'id',
        quoted:null  //物料报价数量
      },
      radio: '0',
      //物料统计汇总
      materialSummaryReport:{
        total:0,
        noNeedQuote:0,
        missingQuote:0,
        quoted:0
      },
      girdRfqSummaryOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'rfqSummaryGrid',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: { isCurrent: true,
          isHover: true
        },
        columns: [
          {
            title: this.$t('Supplier'),
            field: 'supplier',
            visible: true,
            width: 300
          },
          {
            title: this.$t('Inquiry Qty'),
            field: 'inquiryMaterialQty', visible: true,
            align: 'right'
          },
          {
            title: this.$t('Quoted PNs'),
            field: 'quotedPNs', visible: true,
            align: 'right'
          },
          {
            title: this.$t('Quoted%'),
            field: 'quotedRate', visible: true,
            align: 'right',
            slots: { default: 'quotedRate' }
          },
          {
            title: this.$t('Best Price PNs'),
            field: 'bestPricePNs', visible: true,
            align: 'right'
          },
          {
            title: this.$t('Best Price Rate%'),
            field: 'bestPriceRate', visible: true,
            align: 'right',
            slots: { default: 'bestPriceRate' }
          },
          {
            title: this.$t('Remarks'),
            field: 'remark',
            visible: true
          },
        ],

        sortConfig: {
          remote: true
        }
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'priceRecommendProject',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: { isCurrent: true,
          keyField: 'quotationId',
          isHover: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        },
        columns: [
          { title: '', type: 'checkbox', visible: true, width: 30 },
          {
            title: this.$t('rfq.rfqNo'),
            field: 'quotationsNo',
            slots: { default: 'quotationsNo' },
            visible: true,
            width: 200
          },
          {
            title: this.$t('common.creationDate'),
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            field: 'createTime', visible: true
          },
          {
            title: this.$t('rfq.requestedResponseDate'),
            field: 'dateRequested',
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true
          },
          {
            title: this.$t('rfq.quotationResponseDate'),
            field: 'quoteReplyDate',
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true
          },
          { title: this.$t('supplier.shortNameOfSupplier'), field: 'nameShort', visible: true },
          { title: this.$t('common.sourcing'), field: 'sourcingNames', visible: true },
          {
            title: this.$t('rfq.documentSource'),
            slots: {
              default: 'documentSource'
            },
            field: 'documentSource', visible: true
          },
          {
            title: this.$t('rfq.quoteStatus'),
            field: 'quotationStatus',
            slots: {
              default: 'quotationStatus'
            },
            visible: true
          },
          {
            title: this.$t('common.operate'), field: 'operate',
            slots: {
              default: 'operate'
            },
            showOverflow: false,
            visible: true, width: 35, fixed: 'right'
          }

        ],

        sortConfig: {
          remote: true
        }
      },
      gridMaterialOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'priceMaterialProject',
        maxHeight: 700,
        scrollY: {
          enabled: false
          // 虚拟滚动100条以上会导致样式错乱
        },
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: { isCurrent: true,
          keyField: 'projectMaterialId',
          isHover: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        },
        columns: [
          { title: '', type: 'checkbox', visible: true, width: 30, fixed: 'left' },

          { title: this.$t('material.materialCode'), field: 'materialCode', slots: { default: 'materialCode' }, visible: true, width: 100 },
          {
            title: this.$t('material.materialDescription'),
            field: 'materialDescription',
            visible: true,
            width: 100
          },
          {
            title: this.$t('material.category'), field: 'categoryId',
            slots: {
              default: 'categoryId'
            }, visible: true, width: 100
          },
          { title: this.$t('rfq.quoteTemplate'), field: 'templateName', visible: true, width: 100 },
          {
            title: this.$t('common.sourcing'), field: 'sourcing', visible: true, width: 100
          },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          {
            title: this.$t('rfq.recommendedSuppliers'),
            field: 'recommendedSupplierName',
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.projectItemStatus'),
            field: 'status',
            slots: {
              default: 'materialStatus'
            },
            visible: true, width: 100
          },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },

          {
            title: this.$t('rfq.quoteStatus'),
            field: 'quotationsStatus',
            slots: {
              default: 'quotationsStatus'
            },
            visible: true,
            width: 100
          },
          { title: this.$t('rfq.rfqNo'), field: 'quotationsNo', slots: { default: 'quotationsNo' }, visible: true, width: 100 },
          { title: this.$t('common.creationDate'), field: 'createTime', visible: true, width: 100 },
          { title: this.$t('rfq.requestedResponseDate'), field: 'dateRequested', visible: true, width: 100 },
          { title: this.$t('rfq.approvalNo'), field: 'approvalNo', slots: { default: 'approvalNo' }, visible: true, width: 100 },
          { title: this.$t('rfq.approvalFormName'), field: 'approvalName', visible: true, width: 100 },
          { title: this.$t('rfq.returnRemarks'), field: 'remark', visible: true, width: 100 },
          {
            title: this.$t('common.operate'), slots: {
              default: 'operate'
            }, field: 'operate', visible: true, width: 35, showOverflow: false, fixed: 'right'
          }
        ],

        sortConfig: {
          remote: true
        }
      },
      mergeMaterialRowFields: {
        // 根据物料合并的字段
        fields: [
          'materialCode',
          'materialDescription',
          'categoryId',
          'templateName',
          'sourcing',
          'mfg',
          'mpn',
          'recommendedSupplierName',
          'status'
        ]
      },
      mergeQuotationRowFields: [
        'supplierName',
        'quotationsStatus',
        'quotationsNo',
        'createTime',
        'dateRequested'
      ],
      loading: false,
      list: [],
      total: 0,
      approvalList: [],
      recommendOptions: {
        projectMaterialIds: [],
        quotationIds: [],
        projectId: null
      },
      selectedRecord: [],
      returnButtonFlag: false,
      isLoadCompleted:false
    }
  },
  mounted() {
    this.queryParams.projectId = this.steps.projectId
    this.recommendOptions.projectId = this.steps.projectId
    // this.getApprovalList()
    this.getBaseInfoFieldConfig()
    this.getCategories()
    this.getMaterialSummaryReport()
  },
  methods: {
    addPrinciple() {
      this.bestPriceVAdvanced.principles.push({
        principle: ''
      })
    },
    /**
     * 弹出情景1的窗口
     */
    openBestPriceDailog(){
      this.bestPriceVisible = true
      this.resetBestPriceAdvanced()
      getAdvancedRecommend({projectId:this.rfqProject.id}).then(res => {
        this.advancedRecommend = res.data
      })
    },
    resetBestPriceAdvanced() {
      this.bestPriceVAdvanced = {
        notWantSuppliers: [],
        principles: [],
        ...this.recommendOptions,
        targetCurrency: this.currencyRmb
      }
      this.bestPriceVAdvanced.principles.push({
        principle: 'lowest_cost'
      })
      this.bestPriceVAdvanced.principles.push({
        principle: 'moq_min'
      })
      this.bestPriceVAdvanced.principles.push({
        principle: 'shortest_lead_time'
      })
    },
    /**
     * 百分比处理函数
     * @param value
     * @returns {number|*}
     */
    bestPriceRateComputed(value) {
      if(value) {
        return value * 100
      }
      return value
    },
    /**
     * 关闭退回物料的窗口
     */
    closeMaterialReturn(reload) {
      this.rfqReturnCommentVisible = false
      if (reload) {
        // 必须清空，否则会导致退回的数据还存储在前端的selectedRecord里面，再次点击老数据还存在
        this.selectedRecord = []
        this.getList()
      }
    },
    /**
     * 弹出窗口
     */
    showBatchReturn() {
      const selected = Array.from(new Set([...this.selectedRecord]))
      if (selected.length === 0) {
        this.$message.warning(this.$t('rfq.pleaseSelectTheMaterialToBeReturned'))
        return
      }
      hasContainOther({
        projectMaterialIds: selected.join()
      }).then(res => {
        if (res.data) {
          this.$message.warning(this.$t('包含其他不满足退回的记录，已跳过'))
        }
      })
      this.selectMaterialIds = null
      this.selectMaterialIds = selected.join()
      this.rfqReturnCommentVisible = true
    },
    // 物料列表的跨页全选框
    checkBoxAllChange({ checked }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.radio === '1' ? this.$refs.priceRecommendProject : this.$refs.priceMaterialProject
      if (checked) {
        if (this.selectedRecord.length + this.total > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.clearCheckboxRow()
          // 当前选中的数据key数组
          const selectIds = this.radio === '1' ? grid.getCheckboxRecords().map(item => item.quotationId) : grid.getCheckboxRecords().map(item => item.projectMaterialId)
          // 取交集
          const arr = Array.from(new Set([...this.selectedRecord].filter(x => selectIds.includes(x))))
          if (arr.length > 0) {
            arr.forEach((id) => {
              grid.setCheckboxRow(grid.getRowById(id), true)
            })
          }
        } else {
          this.checkAllData(checked)
        }
      } else {
        this.checkAllData(checked)
      }
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    checkAllData(checked) {
      // 当是询价单模式的时候
      if (this.radio === '1') {
        const obj = { ...this.queryParams }
        if (obj.quotationsStatusList.length > 0 && obj.quotationsStatusList.findIndex(v => v === 'all') >= 0) {
          obj.quotationsStatusList = []
        }
        getRecommendQuotationAllIds(obj).then(res => {
          if (checked) {
            this.selectedRecord = Array.from(new Set([...this.selectedRecord, ...res.data]))
          } else {
            this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => !res.data.includes(x))))
          }
        })
      } else { // 当是物料模式的时候
        const obj = { ...this.queryParams }
        if (obj.quoteMaterialStatus.length > 0 && obj.quoteMaterialStatus.findIndex(v => v === 'all') >= 0) {
          obj.quoteMaterialStatus = []
        }
        getRecommendQuotationMaterialsAllIds(obj).then(res => {
          if (checked) {
            this.selectedRecord = Array.from(new Set([...this.selectedRecord, ...res.data]))
          } else {
            this.selectedRecord = Array.from(new Set([...this.selectedRecord].filter(x => !res.data.includes(x))))
          }
        })
      }
    },
    // 物料列表的单个选择框
    checkBoxChange({ checked, row }) {
      const num = process.env.VUE_APP_SELECT_ALL_COUNT
      const grid = this.radio === '1' ? this.$refs.priceRecommendProject : this.$refs.priceMaterialProject
      const id = this.radio === '1' ? row.quotationId : row.projectMaterialId
      if (checked) {
        if (this.selectedRecord.length + 1 > num) {
          this.$message.error(`${this.$t('common.theDataCannotExceednum', { num: num })}`)
          grid.setCheckboxRow(grid.getRowById(id), false)
        } else {
          this.selectedRecord.push(id)
        }
      } else {
        this.selectedRecord.splice(this.selectedRecord.indexOf(id), 1)
      }
    },
    // 跨页全选的清空
    async clearSelected() {
      await this.$nextTick()
      let grid =null;
      if (this.radio === '1'){
        grid=this.$refs.priceRecommendProject
      }else if (this.radio === '2'){
        grid=this.$refs.priceMaterialProject
      }
      this.selectedRecord = []
      if (grid!=null){
        grid.clearCheckboxRow()
      }
    },
    mergeMaterialRowMethod({ row, _rowIndex, column, visibleData }) {
      const prevRow = visibleData[_rowIndex - 1]
      let nextRow = visibleData[_rowIndex + 1]
      // 按照物料进行合并，选择框也按照物料合并
      if (row && (['checkbox'].includes(column.type) || this.mergeMaterialRowFields.fields.includes(column.property))) {
        if (prevRow && row.projectMaterialId === prevRow.projectMaterialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.projectMaterialId === nextRow.projectMaterialId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      } else if (row && this.mergeQuotationRowFields.includes(column.property)) {
        if (prevRow && row.projectMaterialId === prevRow.projectMaterialId && row.quotationsId === prevRow.quotationsId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined && row.projectMaterialId === nextRow.projectMaterialId && row.quotationsId === nextRow.quotationsId) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    getBaseInfoFieldConfig() {
      getRfqProject(
        {
          id: this.queryParams.projectId
        }
      ).then(res => {
        this.rfqProject = res.data
      })
    },

    getApprovalList() {
      getApprovalList({
        projectId: this.queryParams.projectId
      }).then(res => {
        this.approvalList = res.data
      })
    },
    radioChange() {
      this.getList()
    },
    getList() {
      this.isLoadCompleted=false
      if (this.radio === '1') {
        this.clearSelected()
        this.getProjectList()
      } else if (this.radio === '2'){
        this.clearSelected()
        this.getMaterialList()
      }
      else{
        this.getRfqSummaryList()
      }
    },
    /**
     * 获取物料的询价汇总
     */
    getMaterialSummaryReport() {
      getMaterialSummaryReport({ projectId: this.queryParams.projectId }).then(res => {
        this.materialSummaryReport=res.data
        this.getList()
      })
    },
    /**
     * 获取项目询价汇总列表
     */
    getRfqSummaryList() {
      this.loading=true
      this.queryParams.quoted=this.materialSummaryReport.quoted
      const obj = { ...this.queryParams }
      getRfqSummary(obj).then(res => {
        this.list = res.data
        this.total = 0
      }).finally(() => {
        this.loading=false
      })
    },
    getProjectList() {
      const obj = { ...this.queryParams }
      if (obj.quotationsStatusList.length > 0 && obj.quotationsStatusList.findIndex(v => v === 'all') >= 0) {
        obj.quotationsStatusList = []
      }
      getPageQuotationRecommend(obj).then(res => {
        this.list = res.data.list
        this.total = res.data.total
        this.$nextTick(() => {
          const grid = this.$refs.priceRecommendProject
          this.list?.forEach((row) => {
            if (this.selectedRecord.includes(row.quotationId)) {
              setTimeout(() => {
                grid.setCheckboxRow(grid.getRowById(row.quotationId), true)
              }, 0)
            }
          })
        })
        this.isLoadCompleted=true
      })
    },
    getMaterialList() {
      const obj = { ...this.queryParams }
      if (obj.quoteMaterialStatus.length > 0 && obj.quoteMaterialStatus.findIndex(v => v === 'all') >= 0) {
        obj.quoteMaterialStatus = []
      }
      getPageMaterialRecommend(obj).then(res => {
        this.list = res.data.list
        this.total = res.data.total
        this.$nextTick(() => {
          const grid = this.$refs.priceMaterialProject
          this.list?.forEach((row) => {
            if (this.selectedRecord.includes(row.projectMaterialId)) {
              setTimeout(() => {
                grid.setCheckboxRow(grid.getRowById(row.projectMaterialId), true)
              }, 0)
            }
          })
        })
        this.isLoadCompleted=true
      })
    },
    handleClick() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        projectId: this.steps.projectId,
        quoteMaterialStatus: ['to_be_recommended', 'approve_return'],
        quotationsStatusList: ['to_quote'],
        search: '',
        sortBy: 'ASC',
        sortField: 'id'
      }
      this.handleSearch(true)
    },
    handleSearch(resetPageNo) {
      if (resetPageNo) {
        // 跨页后使用前面页数据进行搜索，此时未重置pageNo导致搜索结果为空
        this.queryParams.pageNo = 1
      }
      this.getList()
    },
    handleTerminate() {
      if (this.radio === '1') {
        terminateRecommendQuotation({
          quotationId: this.returnCommentForm.quotationId,
          reason: this.returnCommentForm.opinion
        }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.emitter.emit('freshStep', this.queryParams.projectId)

          this.returnCommentVisible = false
          this.getList()
        })
      } else {
        terminateRecommendMaterial({
          quotationsMaterialSupplierRelId: this.returnCommentForm.quotationsMaterialSupplierRelId,
          reason: this.returnCommentForm.opinion
        }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.returnCommentVisible = false
          this.getList()
          this.emitter.emit('freshStep', this.queryParams.projectId)
        })
      }
    },
    showReturnForm(isAll, row, title) {
      this.returnCommentForm.isAll = isAll
      this.returnCommentForm.opinion = ''
      this.returnCommentTitle = title
      if (isAll) {
        if (this.radio === '1') {
          this.returnCommentForm.quotationId = row.quotationId
        } else {
          this.returnCommentForm.quotationsMaterialSupplierRelId = row.quotationsMaterialSupplierRelId
          this.returnCommentForm.projectMaterialId = row.projectMaterialId
        }
      } else {
        if (this.radio === '1') {
          this.returnCommentForm.quotationId = row.quotationId
        } else {
          this.returnCommentForm.quotationsMaterialSupplierRelId = row.quotationsMaterialSupplierRelId
        }
      }
      this.returnCommentVisible = true
    },
    submitReturn() {
      if (this.returnCommentForm.batch) {
        this.returnButtonFlag = true
        this.handleBatchBack()
      } else {
        // 退回
        if (this.returnCommentForm.isAll) {
          this.handleBack()
        } else {
          this.handleTerminate()
        }
      }
    },
    downloadMaterialDrawing(materialId, materialCode) {
      downloadMaterialDrawings({ projectMaterialId: materialId }).then(res => {
        this.$download.zip(res, materialCode + '.zip')
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    },
    showMaterialDetail(id) {
      this.materialVisible = true
      this.$refs.rfqMaterial.showMaterialDetail(id)
    },
    handleBack() {
      if (this.radio === '1') {
        returnRecommendQuotation({
          projectId: this.queryParams.projectId,
          quotationId: this.returnCommentForm.quotationId,
          reason: this.returnCommentForm.opinion
        }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.emitter.emit('freshStep', this.queryParams.projectId)

          this.returnCommentVisible = false
          this.getList()
        })
      } else {
        returnRecommendMaterial({
          projectId: this.queryParams.projectId,
          quotationsMaterialSupplierRelId: this.returnCommentForm.quotationsMaterialSupplierRelId,
          projectMaterialId: this.returnCommentForm.projectMaterialId,
          reason: this.returnCommentForm.opinion
        }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.emitter.emit('freshStep', this.queryParams.projectId)

          this.returnCommentVisible = false
          this.getList()
        })
      }
    },
    handleBatchBack() {
      batchReturnRecommendMaterial({
        projectMaterialIds: this.returnCommentForm.projectMaterialIds.join(),
        reason: this.returnCommentForm.opinion
      }).then(res => {
        if (res.data) {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.emitter.emit('freshStep', this.queryParams.projectId)
          this.returnCommentVisible = false
          this.getList()
        } else {
          this.$message.error(this.$t('auth.operationFailed'))
        }
        this.returnButtonFlag = false
      }).catch(() => {
        this.returnButtonFlag = false
      })
    },
    downloadPriceFiles(row) {
      downloadPriceFiles({ businessId: row.quotationId }).then(res => {
        this.$download.zip(res, row.quotationsNo + '.zip')
      })
    },
    handleExport() {
      if (this.radio === '1') {
        const obj = { ...this.queryParams, pageNo: 1 }
        if (obj.quotationsStatusList.length > 0 && obj.quotationsStatusList.findIndex(v => v === 'all') >= 0) {
          obj.quotationsStatusList = []
        }
        exportQuotationExcel(obj).then(res => {
          this.$download.excel(res, this.$t('rfq.priceComparisonxls'))
          this.$message.success(this.$t('order.operationSucceeded'))
          this.$message.success(this.$t('order.operationSucceeded'))
        })
      } else {
        const obj = { ...this.queryParams, pageNo: 1 }
        if (obj.quoteMaterialStatus.length > 0 && obj.quoteMaterialStatus.findIndex(v => v === 'all') >= 0) {
          obj.quoteMaterialStatus = []
        }
        exportMaterialExcel(obj).then(res => {
          this.$download.excel(res, this.$t('rfq.priceComparisonxls'))
          this.$message.success(this.$t('order.operationSucceeded'))
        })
      }
    },

    recommendRow() {
      const selected = Array.from(new Set([...this.selectedRecord]))
      if (this.radio === '1') {
        if (selected.length === 0) {
          this.$message.warning(this.$t('rfq.pleaseSelectTheRfqToRecommend'))
          return
        }
        this.checkRecommend.ids = selected
        this.checkRecommend.checkRecommendTypeEnums = 'QUOTE'
        checkNoQuote(this.checkRecommend).then(res => {
          if (res.data) {
            checkRecommend(this.checkRecommend).then(res => {
              this.recommendOptions.quotationIds = selected
              this.recommendOptions.projectMaterialIds = []
            })
          } else {
            this.$confirm(this.$t('rfq.areYouSureYouWantToEnterThePriceRecommendationAnalysisForDocumentsThatHaveNotBeenQuoted'), this.$t('supplier.tips'), {
              confirmButtonText: this.$t('order.determine'),
              cancelButtonText: this.$t('common.cancel'),
              type: 'warning'
            }).then(() => {
              checkRecommend(this.checkRecommend).then(res => {
                this.recommendOptions.quotationIds = selected
                this.recommendOptions.projectMaterialIds = []
              })
            }).catch(() => {
            })
          }
        })
      } else {
        if (selected.length === 0) {
          this.$message.warning(this.$t('rfq.pleaseSelectTheMaterialToBeRecommended'))
          return
        }
        this.checkRecommend.ids = selected
        this.checkRecommend.checkRecommendTypeEnums = 'MATERIAL'
        checkNoQuote(this.checkRecommend).then(res => {
          if (res.data) {
            checkRecommend(this.checkRecommend).then(res => {
              this.recommendOptions.projectMaterialIds = selected
              this.recommendOptions.quotationIds = []
            })
          } else {
            this.$confirm(this.$t('rfq.areYouSureYouWantToEnterThePriceRecommendationAnalysisForDocumentsThatHaveNotBeenQuoted'), this.$t('supplier.tips'), {
              confirmButtonText: this.$t('order.determine'),
              cancelButtonText: this.$t('common.cancel'),
              type: 'warning'
            }).then(() => {
              checkRecommend(this.checkRecommend).then(res => {
                this.recommendOptions.projectMaterialIds = selected
                this.recommendOptions.quotationIds = []
              })
            }).catch(() => {
            })
          }
        })
      }
    },
    savePriceApprovalCompletedHandler(row) {
      // const selected = Array.from(new Set([...this.selectedRecord]))
      // if (selected.length === 0) {
      //   this.$message.warning(this.$t('rfq.pleaseSelectTheMaterialToBeRecommended'))
      //   return
      // }
      savePriceApprovalCompleted({
        projectMaterialIds: [this.radio === '1' ? row.quotationId : row.projectMaterialId],
        projectId: this.queryParams.projectId
      }).then(res => {
        if (res.data) {
          this.emitter.emit('freshStep', this.queryParams.projectId)
          this.$message.success(this.$t('order.operationSucceeded'))
        } else {
          this.$message.error(this.$t('auth.operationFailed'))
        }
        this.getList()
      })
    },
    submitRecoverHandler(row) {
      // const selected = Array.from(new Set([...this.selectedRecord]))
      // if (selected.length === 0) {
      //   this.$message.warning(this.$t('rfq.pleaseSelectTheMaterialToBeRecommended'))
      //   return
      // }
      submitRecover({
        projectMaterialIds: [this.radio === '1' ? row.quotationId : row.projectMaterialId],
        projectId: this.queryParams.projectId
      }).then(res => {
        if (res.data) {
          this.emitter.emit('freshStep', this.queryParams.projectId)
          this.$message.success(this.$t('order.operationSucceeded'))
        } else {
          this.$message.error(this.$t('auth.operationFailed'))
        }
        this.getList()
      })
    },
    followUp(row) {
      this.followUpData.quotationIds = [row.quotationId]
      this.followUpOpen = true
    },
    actionFollowUp(row) {
      followUp(this.followUpData).then(j => {
        if (j.data) {
          this.$message.success(this.$t('rfq.followUpSuccess'))
          this.followUpOpen = false
        } else {
          this.$message.error(this.$t('rfq.followUpFailed'))
        }
      })
    },
    showQuote(quotationsNo, supplierId, quotationId, projectId) {
      this.$router.push(`/rfq/supplierQuote/${quotationsNo}?headerSupplierId=${supplierId}&quotationId=${quotationId}&projectId=${projectId}`)
    },
    clearRecommendOptions() {
      this.recommendOptions = {
        projectMaterialIds: [],
        quotationIds: [],
        projectId: this.steps.projectId

      }
      this.selectedRecord = []
      this.handleClick()
    },
    /**
     * 下载汇总报告
     */
    exportRfqSummary() {
      this.$modal.confirm(this.$t('Check whether to download the attachment?')).then(() => {
        this.exportLoading = true
        return exportRfqSummary(this.queryParams)
      }).then(response => {
        const formattedDate = dayjs().format('YYYYMMDD')
        this.$download.excel(response, this.$t(this.rfqProject.projectNo+'_RFQ汇总报告_'+formattedDate+'.xlsx'))
        this.exportLoading = false
      }).finally(() => {
        this.exportLoading=false
      })
    },
    /**
     * 品牌竞争性分析报告
     */
    exportAnalysisMfg() {
      this.$modal.confirm(this.$t('Check whether to download the attachment?')).then(() => {
        this.exportLoading = true
        return exportAnalysisMfg(this.queryParams)
      }).then(response => {
        const formattedDate = dayjs().format('YYYYMMDD')
        this.$download.excel(response, this.$t(this.rfqProject.projectNo+'_品牌竞争性分析报告_'+formattedDate+'.xlsx'))
        this.exportLoading = false
      }).finally(() => {
        this.exportLoading=false
      })
    },
    /**
     * 情景1-最低价报告
     */
    exportBestPriceReport() {
      this.$modal.confirm(this.$t('Check whether to download the attachment?')).then(() => {
        this.exportLoading = true
        return exportBestPriceReport(this.bestPriceVAdvanced)
      }).then(response => {
        const formattedDate = dayjs().format('YYYYMMDD')
        this.$download.excel(response, this.$t(this.rfqProject.projectNo+'_最低价报告_'+formattedDate+'.xlsx'))
        this.exportLoading = false
      }).finally(() => {
        this.exportLoading=false
        this.bestPriceVisible=false
      })
    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

.cardSpacing {
  margin-top: 15px;
}

.oneLineMaterialItem {
  width: 100%;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.commonFormItem {
  width: 33.3%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

  ::v-deep .el-form-item__content {
    width: calc(100% - 170px);
  }
}

.commonFormItemLeft {
  width: 33.3%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

  ::v-deep .el-form-item__content {
    width: calc(100% - 115px);
  }
}

.commonMaterialItem {
  width: 33%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
}

::v-deep .el-form--label-top .el-form-item__label {
  padding-bottom: 0;
}

.rfq-create-upload {
  margin: 10px 0
}

.rfq-create {
  margin-top: 15px;
}

.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.materialItem {
  width: 178px;
}

.searchValue {
  width: 100%;
}

.rfqMaterial {
  .mainTab {
    margin-bottom: 15px;
  }
}

.configItem {
  margin: 5px 0;
  width: 40%;
}

</style>
