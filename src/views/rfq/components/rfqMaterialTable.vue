<template>
  <div>
    <div class="rfqHome-search">

      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.search	"
          :placeholder="$t('rfq.materialCodeAndMaterialDescription')"
          clearable
          style="flex: 0 1 40%"
          @keyup.enter.native="getList"
        />
        <el-button type="primary" plain @click="queryParams.pageNo = 1;getList();">{{ $t('common.search') }}</el-button>
        <el-button plain style="margin-left: 0" @click="handleClick">{{ $t('common.reset') }}</el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>

      </div>
      <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="189px" size="small">
        <el-form-item :label="$t('rfq.rfqItemNo')" class="searchItem" prop="deliveryNoteNo">
          <el-input
            v-model="queryParams.projectNo"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('material.materialCode')" class="searchItem" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('material.manufacturer')" class="searchItem" prop="mfg">
          <el-input
            v-model="queryParams.mfg"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('supplier.purchasingOrganization')" class="searchItem" prop="factoryIds">
          <el-select v-model="queryParams.purchaseOrgs" class="searchValue" clearable multiple>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG,0)"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('material.materialDescription')" class="searchItem" prop="materialDescription">
          <el-input
            v-model="queryParams.materialDescription"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('material.manufacturersPartNumber')" class="searchItem" prop="mpn">
          <el-input
            v-model="queryParams.mpn"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('rfq.eventName')" class="searchItem" prop="eventName">
          <el-input
            v-model="queryParams.eventName"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('material.materialStatus')" class="searchItem" prop="status">
          <el-select v-model="queryParams.status" class="searchValue" clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.RFQ_MATERIAL_STATUS)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('material.category')" class="searchItem" prop="categories">
          <el-cascader
            v-model="queryParams.categories	"
            :options="categoryList"
            :placeholder="$t('material.pleaseSelectCategory')"
            :props="{ value: 'id',label:'name'}"
            class="searchValue"
            clearable
            filterable
          />
        </el-form-item>
        <el-form-item :label="$t('common.sourcing')" class="searchItem" prop="sourcing">
          <el-select v-model="queryParams.sourcing" class="searchValue" clearable filterable multiple>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <!--        <div style="text-align: center">-->
        <!--          <el-button plain icon="el-icon-search" size="mini" type="primary" @click="getList">{{-->
        <!--            $t('common.search')-->
        <!--          }}-->
        <!--          </el-button>-->
        <!--          <el-button icon="el-icon-refresh" size="mini" @click="handleClick">{{ $t('common.reset') }}</el-button>-->

        <!--        </div>-->

      </el-form>

    </div>

    <vxe-grid
      ref="rfqProjectTable"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
    >
      <template #projectNo="{row}">
        <copy-button
          @click="$router.push(`/rfq/processHome/${row.projectNo}?id=${row.id}&code=${row.businessCode}`)"
        >
          {{ row.projectNo }}
        </copy-button>
      </template>
      <template #materialCode="{row}">
        <i
          v-if="row.materialDrawing"
          class="el-icon-picture-outline"
          style="font-size: 16px;margin-right: 3px;cursor: pointer"
          @click="downloadMaterialDrawing(row.projectMaterialId,row.materialCode)"
        />
        <copy-button
          @click="showMaterialDetail(row.projectMaterialId)"
        >
          {{ row.materialCode }}
        </copy-button>
      </template>
      <template #purchaseOrg="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="row.purchaseOrg" />
      </template>
      <template #purchaseType="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_PURCHASE_TYPE" :value="row.purchaseType" />
      </template>
      <template #pgId="{row}">
        <dict-tag :type="DICT_TYPE.SYSTEM_PURCHASE_GROUP" :value="row.pgId" />
      </template>
      <template #materialType="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_TYPE" :value="row.materialType" />
      </template>
      <template #categoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
      </template>
      <template #priceCategory="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_PRICE_CATEGORY" :value="row.priceCategory" />
      </template>
      <template #homemadeSourcing="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_MAKE_OR_BUY" :value="row.homemadeSourcing" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_MATERIAL_STATUS" :value="row.status" />
      </template>
      <template #targetPrice="{row}">
        <number-format :value="row.targetPrice" />
      </template>
      <template #star="{row}">
        <i v-if="row.star" class="el-icon-star-on" style="color: #FF9900" />
        <i v-else class="el-icon-star-off" />
      </template>

      <template #approvalNumbers="{row}">
        <el-button
          v-if="row.approvalNumbers === 0"
          disabled
          size="mini"
          type="text"
        >{{ row.approvalNumbers }}
        </el-button>
        <el-button
          v-if="row.approvalNumbers > 0"
          size="mini"
          style="text-decoration: underline"
          type="text"
          @click="showOperationLog(row.projectMaterialId,'RFQ_PRICE_APPROVAL')"
        >{{ row.approvalNumbers }}
        </el-button>
      </template>

      <template #inquiryNumbers="{row}">
        <el-button
          v-if="row.inquiryNumbers === 0"
          disabled
          size="mini"
          type="text"
        >{{ row.inquiryNumbers }}
        </el-button>
        <el-button
          v-if="row.inquiryNumbers > 0"
          size="mini"
          style="text-decoration: underline"
          type="text"
          @click="showOperationLog(row.materialSupplierRelIds,'RFQ_QUOTATIONS')"
        >{{ row.inquiryNumbers }}
        </el-button>
      </template>

      <template #toolbar_buttons>
        <el-row :gutter="10" class="mb8" style="width: 100%">
          <el-col :span="20">
            <el-button
              v-hasPermi="['rfq:project-materials:query']"
              size="mini"
              plain
              type="primary"
              :loading="exportLoading"
              @click="exportExcel"
              icon="el-icon-download"
            >
              {{ $t('order.download') }}
            </el-button>
          </el-col>
          <el-col :span="4">
            <right-toolbar
              :custom-columns.sync="girdOption.columns"
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              @queryTable="getList"
            />
          </el-col>
        </el-row>
      </template>

    </vxe-grid>
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <el-dialog
      v-if="logVisible"
      :title="$t('common.operationRecord')"
      width="1000px"
      :visible.sync="logVisible"
    >
      <rfqOperationRecord
        :business-id="businessId"
        :business-type="businessType"
        :log-visible.sync="logVisible"
      />
    </el-dialog>

    <rfqMaterial
      ref="rfqMaterial"
      :material-visible.sync="materialVisible"
      :rfq-project-status="null"
      :rfq-project-id="true"
    />
  </div>
</template>

<script>
import {
  downloadMaterialDrawings, exportMaterialList, exportRfsList,
  getMaterialPage,
  getMaterialPageTotal
} from '@/api/rfq/home'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import event from '@/views/dashboard/mixins/event'
import dayjs from "dayjs";

export default {
  name: 'Rfqmaterialtable',

  components: {
    rfqOperationRecord: () => import('@/views/rfq/components/rfqOperation'),
    rfqMaterial: () => import('@/views/rfq/components/material')
    // Steps
  },
  mixins: [event],
  data() {
    return {
      businessId: '',
      businessType: '',
      logVisible: false,
      categoryList: [],
      showSearch: false,
      materialVisible: false,
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        materialCode: '',
        materialDescription: '',
        mfg: '',
        mpn: '',
        eventName: '',
        projectNo: '',
        purchaseOrgs: [],
        categories: [],
        categoryId: '',
        search: '',
        sortBy: '',
        sourcing: [],
        sortField: '',
        status: ''
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'rfqHome',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          {
            title: this.$t('supplier.purchasingOrganization'),
            slots: { default: 'purchaseOrg' },
            field: 'purchaseOrg', visible: true, width: 100
          },
          { title: this.$t('rfq.eventName'), field: 'eventName', visible: true, width: 100 },
          { title: this.$t('rfq.rfqItemNo'), field: 'projectNo', slots: { default: 'projectNo' }, visible: true, width: 200 },
          {
            title: this.$t('rfq.numberOfInquiries'),
            slots: { default: 'inquiryNumbers' },
            field: 'inquiryNumbers', visible: true, width: 200
          },
          {
            title: this.$t('rfq.numberOfApprovals'),
            slots: { default: 'approvalNumbers' },
            field: 'approvalNumbers', visible: true, width: 200
          },
          { title: this.$t('material.materialType'), field: 'materialType', slots: { default: 'materialType' }, visible: true, width: 200 },
          { title: this.$t('material.materialCode'), field: 'materialCode', slots: { default: 'materialCode' }, visible: true, width: 100 },
          { title: this.$t('common.sourcing'), field: 'sourcingNames', visible: true, width: 100 },
          { title: this.$t('material.materialDescription'), field: 'materialDescription', visible: true, width: 100 },
          { title: this.$t('material.specificationAndModel'), field: 'specifications', visible: true, width: 100 },
          { title: this.$t('material.category'), field: 'categoryId', slots: { default: 'categoryId' }, visible: true, width: 200 },
          {
            title: this.$t('rfq.priceCategory'),
            field: 'priceCategory',
            slots: { default: 'priceCategory' },
            visible: true,
            width: 100
          },
          { title: this.$t('material.materialRemarks'), field: 'remarks', visible: true, width: 100 },
          { title: this.$t('rfq.arrangement'), field: 'level', visible: true, width: 100 },
          { title: this.$t('rfq.consumption'), field: 'dosage', visible: true, width: 100 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 100 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 100 },
          {
            title: this.$t('material.selfMadeOrPurchased'),
            field: 'homemadeSourcing',
            slots: { default: 'homemadeSourcing' },
            visible: true,
            width: 100
          },
          { title: this.$t('material.materialStatus'), field: 'status', slots: { default: 'status' }, visible: true, width: 100 },
          { title: this.$t('order.edition'), field: 'version', visible: true, width: 100 },
          { title: this.$t('material.purchaseType'), field: 'purchaseType', slots: { default: 'purchaseType' }, visible: true, width: 100 },
          { title: this.$t('material.procurementGroup'), field: 'pgId', visible: true, slots: { default: 'pgId' }, width: 100 },
          { title: this.$t('rfq.plannedLeadTime'), field: 'deliveryTime', visible: true, width: 100 },
          { title: this.$t('rfq.plannedMinimumPackagingQuantity'), field: 'mpq', visible: true, width: 100, align: 'right' },
          { title: this.$t('rfq.plannedMinimumOrderQuantity'), field: 'moq', visible: true, width: 100, align: 'right' },
          { title: this.$t('rfq.targetPrice'), slots: { default: 'targetPrice' }, field: 'targetPrice', visible: true, width: 100, align: 'right' }

        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      loading: false,
      list: [],
      total: 0,
      exportLoading:false
    }
  },
  mounted() {
    this.getList()
    this.getCategories()
  },
  activated() {
    this.getList()
  },
  methods: {
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    getList() {
      this.loading = true
      this.queryParams.categoryId = this.queryParams.categories.at(-1)
      getMaterialPage(this.queryParams).then(res => {
        this.list = res.data.list
        this.loading = false
      })
      getMaterialPageTotal(this.queryParams).then(res => {
        this.total = res.data
      })
    },

    handleClick() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        materialCode: '',
        materialDescription: '',
        mfg: '',
        mpn: '',
        eventName: '',
        projectNo: '',
        purchaseOrgs: [],
        categoryList: [],
        categories: [],
        categoryId: '',
        sourcing: [],
        search: '',
        sortBy: '',
        sortField: '',
        status: ''
      }
      this.getList()
    },
    downloadMaterialDrawing(materialId, materialCode) {
      downloadMaterialDrawings({ projectMaterialId: materialId }).then(res => {
        this.$download.zip(res, materialCode + '.zip')
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    },
    showMaterialDetail(id) {
      this.materialVisible = true
      this.$refs.rfqMaterial.showMaterialDetail(id)
    },
    closeMaterial(close) {
      close()
    },
    showOperationLog(obj1, obj2) {
      this.logVisible = true
      this.businessId = obj1
      this.businessType = obj2
    },
    exportExcel() {
      this.$modal.confirm(this.$t('Do you confirm the download?')).then(() => {
        this.exportLoading = true
        return exportMaterialList(this.queryParams)
      }).then(response => {
        const formattedDate = dayjs().format('YYYYMMDD')
        this.$download.excel(response, '物料列表' + formattedDate + '.xlsx')
        this.exportLoading = false
      }).catch(() => {
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 189px);
  }
}

.searchValue {
  width: 95%;
}

.searchTimeItem {
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 82px);
  }
}

.commonMaterialItem {
  width: 33%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
}

.rfq-create-upload {
  margin: 10px 0
}

.rfq-create {
  margin-top: 15px;
}

.materialItem {
  width: 178px;
}

.rfqMaterial {
  .mainTab {
    margin-bottom: 15px;
  }
}
</style>
