<template>
  <div>
    <div style="display: flex;">
      <div style="flex-basis: 90%">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in recordList"
            :key="index"
            :color="'#456291'"
          >
            <el-row align="middle" type="flex" :gutter="24">
              <el-col :span="5">{{ parseTime(item.createTime) }}</el-col>
              <el-col :span="5">{{ item.operator }}</el-col>
              <el-col :span="5">
                <dict-tag v-if="item.action" :type="DICT_TYPE.RFQ_ACTION" :value="item.action" />
                <dict-tag v-else :type="DICT_TYPE.RFQ_MATERIAL_STATUS" :value="item.businessStatus" />
              </el-col>
              <el-col :span="5">

                <el-button v-if="item.url" type="text" @click="openWindow(item.topic,item.url)">
                  {{ item.topic }}
                </el-button>
                <span v-else>
                  {{ item.topic }}
                </span>
              </el-col>
              <!--              <el-col :span="19"><div style="vertical-align: top;white-space: pre-wrap;">{{ item.content }}</div></el-col>-->
            </el-row>
          </el-timeline-item>
        </el-timeline>

      </div>
    </div>
    <div style="text-align: center">
      <el-button type="primary" @click="$emit('update:logVisible',false)">{{ $t('order.close') }}</el-button>
    </div>
  </div>

</template>

<script>
import { processRecordList } from '@/api/rfq/home'
import { parseTime } from '../../../utils/ruoyi'
import { DICT_TYPE } from '@/utils/dict'
export default {
  name: 'Operationrecord',
  props: ['businessId', 'businessType', 'columns'],
  data() {
    return {
      recordList: [{
        content: '',
        time: ''
      }]
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  mounted() {
    this.getLog()
  },
  methods: {
    parseTime,
    getLog() {
      console.log(this.businessId)
      processRecordList({
        businessId: this.businessId,
        businessType: this.businessType
      }).then(res => {
        this.recordList = res.data
        // this.recordList.forEach(item => {
        //   item.content = `${item.logContent}`
        //   item.time = `${parseTime(item.createTime)}\n${item.userName}`
        // })
      })
    },
    openWindow(topic, url) {
      window.open(`${window.location.origin}/rfq/processHome/${topic}?${url}`)
    }
  }
}
</script>

<style scoped>

</style>
