<template>
  <div>
    <div class="rfqHome-search">
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.search"
          :placeholder="$t('rfq.eventNameSupplierAbbreviationSupplierCode')"
          clearable
          style="flex: 0 1 40%"
          @keyup.enter.native="getList"
        />
        <el-button plain type="primary" @click="queryParams.pageNo = 1;getList();">{{ $t('common.search') }}</el-button>
        <el-button plain style="margin-left: 0" @click="handleClick">{{ $t('common.reset') }}</el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>
      </div>
      <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="162px" size="small">
        <el-form-item :label="$t('rfq.rfqItemNo')" class="searchItem">
          <el-input
            v-model="queryParams.projectNo"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('rfq.approvalNo')" class="searchItem">
          <el-input
            v-model="queryParams.approvalNo"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('supplier.purchasingOrganization')" class="searchItem">
          <el-select v-model="queryParams.purchaseOrgs" class="searchValue" clearable multiple>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG,0)"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('rfq.eventName')" class="searchItem">
          <el-input
            v-model="queryParams.eventName"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('rfq.createdBy')" class="searchItem">
          <el-select v-model="queryParams.creators" class="searchValue" clearable filterable multiple>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('rfq.approver')" class="searchItem">
          <el-select v-model="queryParams.approvers" class="searchValue" clearable filterable multiple>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('rfq.approvalDocStatus')" class="searchItem" prop="status">
          <el-select v-model="queryParams.statusList" class="searchValue" multiple>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_APPROVAL_STATUS)"
              :key="dict.id"
              :label="dict.label"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('order.timeType')" class="searchItem">
          <el-select v-model="queryParams.dateType" class="searchValue" clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.RFQ_APPROVAL_DATE_TYPE)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" label=" ">
          <el-date-picker
            v-model="queryParams.time"
            :end-placeholder="$t('common.endDate')"
            :range-separator="$t('order.to')"
            :start-placeholder="$t('common.startDate')"
            class="searchValue"
            type="daterange"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>

        <!--        <div style="text-align: center">-->
        <!--          <el-button plain icon="el-icon-search" size="mini" type="primary" @click="getList">{{-->
        <!--            $t('common.search')-->
        <!--          }}-->
        <!--          </el-button>-->
        <!--          <el-button icon="el-icon-refresh" size="mini" @click="handleClick">{{ $t('common.reset') }}</el-button>-->

        <!--        </div>-->

      </el-form>
    </div>

    <vxe-grid
      ref="rfqPriceApproval"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
      @sort-change="sortMethod"
    >
      <template #projectNo="{row}">
        <copy-button
          @click="$router.push(`/rfq/processHome/${row.projectNo}?id=${row.projectId}&projectNo=${row.projectNo}&code=${row.businessCode}`)"
        > {{ row.projectNo }}</copy-button>
      </template>
      <template #urgent="{row}">
        <i v-if="row.urgent" class="el-icon-message-solid" style="color:red " />
      </template>
      <template #creator="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.creator" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_APPROVAL_STATUS" :value="row.status" />
      </template>
      <template #approvalNo="{row}">
        <copy-button
          @click="jumpRouter(row.approvalNo,row.projectId,row.businessCode,row.id)"
        >
          {{ row.approvalNo }}
        </copy-button>
      </template>
      <template #flag="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_APPROVAL_FLAG" :value="row.flag" />
      </template>
      <template #purchaseOrg="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="row.purchaseOrg" />
      </template>
      <template #toolbar_buttons>
        <el-row :gutter="10" class="mb8" style="width: 100%">
          <el-col :span="10" style="display: flex">
            <el-button
              v-hasPermi="['rfq:price-approval:urgent']"
              size="mini"
              type="primary"
              @click="urgent"
            > {{ $t('rfq.urgentApproval') }}
            </el-button>
          </el-col>
          <el-col :span="14">
            <right-toolbar
              :custom-columns.sync="girdOption.columns"
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              @queryTable="getList"
            />
          </el-col>
        </el-row>
      </template>

    </vxe-grid>
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { getPriceApprovalPage, getPriceApprovalPageTotal, urgentPriceApproval } from '@/api/rfq/priceApproval'
import event from '@/views/dashboard/mixins/event'
import router from '@/router'

export default {
  name: 'Rfqprojecttable',
  mixins: [event],

  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        projectNo: '',
        approvalNo: '',
        statusList: ['pending_approval'],
        purchaseOrgs: [],
        creators: [],
        eventName: '',
        approvers: [],
        dateType: '',
        search: '',
        time: []
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'rfqPriceApproval',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: { isCurrent: true,
          isHover: true
        },
        columns: [
          { title: '', type: 'checkbox', visible: true, width: 30 },
          {
            title: this.$t('rfq.urgent'),
            slots: { default: 'urgent' },
            field: 'urgent', visible: true, width: 50
          },
          { title: this.$t('rfq.approvalNo'), field: 'approvalNo',
            slots: {
              default: 'approvalNo'
            }, visible: true, width: 100 },
          { title: this.$t('rfq.approvalFormName'), field: 'approvalName', visible: true, width: 150 },
          {
            title: this.$t('rfq.approvalFormCreationDate'), field: 'createTime',
            sortable: true,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true, width: 100
          },
          {
            title: this.$t('rfq.approvalDate'), field: 'updateTime',
            sortable: true,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true, width: 100
          },
          { title: this.$t('rfq.approvalDocCreator'), field: 'creator', slots: {
            default: 'creator'
          }, visible: true, width: 100 },
          { title: this.$t('rfq.approvalStatus'), field: 'status', slots: {
            default: 'status'
          }, visible: true, sortable: true, width: 80 },
          { title: this.$t('rfq.approver'), field: 'approvers', visible: true, width: 100 },
          { title: this.$t('rfq.quoteTemplate'), field: 'templateName', visible: true, width: 100 },
          { title: this.$t('rfq.sign'), field: 'flag', slots: { default: 'flag' }, visible: true, width: 80 },
          { title: this.$t('rfq.approveMaterialQuantity'), field: 'materialQuantity', visible: true, width: 100, align: 'right' },
          { title: this.$t('rfq.numberOfAccessories'), field: 'attachmentQuantity', visible: true, width: 80 },
          {
            title: this.$t('supplier.purchasingOrganization'),
            slots: { default: 'purchaseOrg' },
            field: 'purchaseOrg', visible: true, width: 100
          },
          { title: this.$t('rfq.eventName'), field: 'eventName', visible: true, width: 100 },
          { title: this.$t('rfq.rfqItemNo'), field: 'projectNo', slots: { default: 'projectNo' }, visible: true, width: 100 }
        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      loading: false,
      list: [],
      total: 0
    }
  },
  mounted() {
    this.getList()
  },
  activated() {
    this.getList()
  },
  methods: {
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.getList()
    },
    jumpRouter(projectNo, projectId, code, approvalId) {
      if (code === 'price_entry') {
        router.push(`/rfq/processHome/${projectNo}?id=${projectId}&approvalId=${approvalId}&projectNo=${projectNo}&code=${code}&active=1`)
      } else {
        router.push(`/rfq/processHome/${projectNo}?id=${projectId}&approvalId=${approvalId}&projectNo=${projectNo}&code=${code}&active=3`)
      }
    },
    getList() {
      this.queryParams.beginDate = undefined
      this.queryParams.endDate = undefined
      if (this.queryParams.time) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      this.loading = true
      getPriceApprovalPage({ ...this.queryParams }).then(res => {
        this.list = res.data.list
        this.loading = false
      })
      getPriceApprovalPageTotal({ ...this.queryParams }).then(res => {
        this.total = res.data
      })
    },

    handleClick() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        projectNo: '',
        approvalNo: '',
        statusList: [],
        purchaseOrgs: [],
        creators: [],
        eventName: '',
        approvers: [],
        dateType: '',
        search: '',
        time: []
      }
      this.getList()
    },
    /** 加急**/
    urgent() {
      const selected = this.$refs.rfqPriceApproval.getCheckboxRecords()
      if (selected.length === 0) {
        this.$message.warning(this.$t('rfq.pleaseSelectTheApprovalFormToBeExpedited'))
        return
      }
      urgentPriceApproval({ ids: selected.map(item => item.id).join(',') }).then(res => {
        this.$message.success(this.$t('rfq.urgentSuccess'))
        this.getList()
      })
    }

  }
}
</script>

<style lang="scss" scoped>
.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 162px);
  }
}

.searchValue {
  width: 95%;
}

.searchTimeItem {
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 82px);
  }
}
</style>
