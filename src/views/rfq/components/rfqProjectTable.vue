<template>
  <div>
    <div class="rfqHome-search">
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.search	"
          :placeholder="$t('rfq.eventNameInquiryItemNumber')"
          clearable
          style="flex: 0 1 40%"
          @keyup.enter.native="getList"
        />
        <el-button plain type="primary" @click="queryParams.pageNo = 1;getList()">{{ $t('common.search') }}</el-button>
        <el-button plain style="margin-left: 0" @click="handleClick">{{ $t('common.reset') }}</el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}

          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>
      </div>
      <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="162px" size="small">
        <el-form-item :label="$t('rfq.rfqItemNo')" class="searchItem" prop="deliveryNoteNo">
          <el-input
            v-model="queryParams.projectNo"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('rfq.sponsor')" class="searchItem" prop="creator">
          <el-select v-model="queryParams.creators" class="searchValue" clearable filterable multiple>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('order.customerName')" class="searchItem" prop="status">
          <el-input
            v-model="queryParams.customName"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>

        <el-form-item :label="$t('supplier.purchasingOrganization')" class="searchItem" prop="factoryIds">
          <el-select v-model="queryParams.purchaseOrgs" class="searchValue" clearable multiple>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG,0)"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('common.sourcing')" class="searchItem" prop="supplier">
          <el-select v-model="queryParams.sourcings	" class="searchValue" clearable filterable multiple>
            <el-option
              v-for="dict in sourcingSources"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('rfq.purposeOfInquiry')" class="searchItem" prop="boxLabelStatus">
          <el-select v-model="queryParams.inquiryPurpose" class="searchValue" clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.RFQ_INQUIRY_PURPOSE)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('rfq.eventName')" class="searchItem" prop="deliveryNoteNo">
          <el-input
            v-model="queryParams.eventName"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
<!--        <el-form-item :label="$t('rfq.billOfMaterialsHandler')" class="searchItem">-->
<!--          <el-select v-model="queryParams.materialBillManagerList	" class="searchValue" clearable filterable multiple>-->
<!--            <el-option-->
<!--              v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"-->
<!--              :key="dict.id"-->
<!--              :label="dict.name"-->
<!--              :value="dict.id"-->
<!--            />-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item :label="$t('rfq.projectStatus')" class="searchItem" prop="status">
          <el-select v-model="queryParams.statusList" class="searchValue" multiple>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_PROJECT_STATUS)"
              :key="dict.id"
              :label="dict.label"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('order.timeType')" class="searchItem" prop="dateType">

          <el-select v-model="queryParams.dateType" class="searchValue" clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.RFQ_DATE_TYPE)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" label=" " prop="deliveryNoteNo">

          <el-date-picker
            v-model="queryParams.time"
            :end-placeholder="$t('common.endDate')"
            :range-separator="$t('order.to')"
            :start-placeholder="$t('common.startDate')"
            class="searchValue"
            type="daterange"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>

        <el-form-item :label="$t('业务模式')" class="searchItem" prop="businessModelIds">
          <el-select v-model="queryParams.businessModelIds" class="searchValue" clearable multiple>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_BUSINESS_TYPE)"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>

      </el-form>
      <div style="display: flex;border-bottom: 3px solid #4996B8;">
        <div
          v-for="dict in statistics" class="headCard">
          <div class="headCard-body"
               :class="{'headCard-selected': JSON.stringify(queryParams.statusList) === JSON.stringify(dict.status)}"
               @click="handleStatusClick(dict)"
          >
            <div class="headCard-body-num"> {{ dict.value }}</div>
            <div class="headCard-body-label">   {{ dict.label }}</div>
          </div>
        </div>
      </div>
    </div>
    <vxe-grid
      ref="rfqProjectTable"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
      @sort-change="sortMethod"
    >
      <template #projectNo="{row}">
        <copy-button
          @click="$router.push(`/rfq/processHome/${row.projectNo}?id=${row.id}&code=${row.businessCode}&projectNo=${row.projectNo}`)"
        >
          {{ row.projectNo }}
        </copy-button>
      </template>
      <template #purchaseOrg="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="row.purchaseOrg" />
      </template>
      <template #businessModel="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_BUSINESS_TYPE" :value="row.businessModel" />
      </template>
      <template #creator="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.creator" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_PROJECT_STATUS" :value="row.status" />

      </template>
      <template #sourcingSources="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcingSources" />
        <dict-tag :type="DICT_TYPE.RFQ_SOURCING_SOURCES" :value="row.sourcingSources" />
      </template>
      <template #inquiryPurpose="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_INQUIRY_PURPOSE" :value="row.inquiryPurpose" />
      </template>
      <template #star="{row}">
        <i v-if="row.star" class="el-icon-star-on" style="color: #FF9900" />
        <i v-else class="el-icon-star-off" />
      </template>
      <template #operate="{row}">
        <OperateDropDown
          :menu-item="[
            {
              name: $t('rfq.revoke'),
              show: (row.canCancel &&$store.getters.permissions.includes('rfq:projects:cancel')),
              action: (row)=>revoke(row),
              para: row
            },{
              name: $t('终止'),
              show: (row.canTermination && $store.getters.permissions.includes('rfq:projects:termination')),
              action:()=>showTerminate(row),
              para:row
            },{
              name: $t('完成'),
              show: (row.status==='processing' && $store.getters.permissions.includes('rfq:projects:complete')),
              action:()=>completeProject(row.id),
              para:row
            }
          ]"
        />
      </template>
      <template #toolbar_buttons>
        <el-row :gutter="10" class="mb8" style="width: 100%">
          <el-col :span="10" style="display: flex;align-items: center">
            <el-checkbox v-model="queryParams.myTodo" style="margin-left: 20px;margin-right: 20px">
              {{ $t('rfq.myToDo') }}
            </el-checkbox>
            <el-button
              v-hasPermi="['rfq:projects:query']"
              size="mini"
              plain
              type="primary"
              :loading="exportLoading"
              @click="exportExcel"
              icon="el-icon-download"
            >
              {{ $t('order.download') }}
            </el-button>
          </el-col>
          <el-col :span="14">
            <right-toolbar
              :custom-columns.sync="girdOption.columns"
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              @queryTable="getList"
            />
          </el-col>
        </el-row>
      </template>

    </vxe-grid>
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
    <el-dialog :title="$t('rfq.projectCancellation')" :visible.sync="revokeProjectOpen" append-to-body width="400px">
      <el-row>
        <el-col :span="24">
          <div>
            <p>{{ $t('rfq.youAreCancelingTheInquiryItem') }}</p>
            <p>{{ $t('rfq.noteThisOperationCannotBeReplied') }}</p>
            <!--            <p>{{ $t('rfq.pleaseEnterTheReason') }}</p>-->
          </div>
          <div>
            <el-input
              v-model="revokeProjectOpenData.content"
              :rows="1"
              autosize
              :placeholder="$t('rfq.pleaseEnterTheReasonForRevocation')"
              type="textarea"
            />
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="revokeProjectOpen = false">{{ $t('common.cancel') }}</el-button>

        <el-button type="primary" @click="revokeProject">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="terminateCommentVisible"
      :title="$t('终止意见')"
      :visible.sync="terminateCommentVisible"
      width="400px"
    >
      <div>
        <p>{{ $t('rfq.youAreTerminatingRfqMaterialsPleaseEnterTerminationComments') }}</p>
      </div>
      <el-input
        v-model="terminateCommentForm.opinion"
        :placeholder="$t('rfq.pleaseEnterComments')"
        :rows="5"
        type="textarea"
      />
      <div slot="footer">
        <el-button @click="terminateCommentVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" :loading="terminateButtonFlag" @click="submitTerminate">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { getDictDataLabel } from '@/utils/dict'
import {cancelProject, completeProject, exportRfsList, getProjectPage, terminationProject} from '@/api/rfq/home'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import event from '@/views/dashboard/mixins/event'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import {exportPoDetailExcel} from "@/api/om/po";
import {exportOrderTracker} from "@/api/om/orderTracker";
import dayjs from "dayjs";

export default {
  name: 'Rfqprojecttable',
  components: { OperateDropDown },
  mixins: [event],
  props: {
    /* statistics */
    statistics: {
      type: Array,
      default: []
    },
  },

  data() {
    return {
      terminateCommentVisible: false,
      terminateCommentForm: {
        opinion: '',
        projectId: null
      },
      terminateButtonFlag: false,
      sourcingSources: [],
      showSearch: false,
      revokeProjectOpen: false,
      selectData: {},
      revokeProjectOpenData: {
        content: ''
      },
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        beginDate: '',
        creators: [],
        customName: '',
        dateType: '',
        endDate: '',
        eventName: '',
        inquiryPurpose: '',
        materialBillManagerList: [],
        myTodo: false,
        projectNo: '',
        purchaseOrgs: '',
        search: '',
        sortBy: '',
        sortField: '',
        sourcings: [],
        statusList: ['processing', 'new', 'completed'],
        time: [],
        businessModelIds: []
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'rfqHome',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: { isCurrent: true,
          isHover: true
        },
        columns: [
          // { title: '', type: 'radio', visible: true, width: 50 },
          // { title: this.$t('rfq.starMarker'),
          //   slots: { default: 'star' },
          //   field: 'star', visible: true, width: 50 },
          {
            title: this.$t('supplier.purchasingOrganization'),
            slots: { default: 'purchaseOrg' },
            field: 'purchaseOrg', visible: true, width: 100
          },
          { title: this.$t('rfq.eventName'), fixed: 'left', field: 'eventName', visible: true, width: 100 },
          {
            title: this.$t('rfq.rfqItemNo'), fixed: 'left',
            slots: { default: 'projectNo' },
            field: 'projectNo', visible: true, width: 200
          },
          {
            title: this.$t('rfq.businessModel'), field: 'businessModel',
            slots: { default: 'businessModel' },
            visible: true, width: 100
          },
          {
            title: this.$t('rfq.materialQuantity'), field: 'materialCount', visible: true, width: 100
          },
          {
            title: this.$t('rfq.projectCreationDate'), field: 'createTime',
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            sortable: true, visible: true, width: 100
          },
          { title: this.$t('rfq.projectDeadline'), field: 'expirationDate', visible: true, sortable: true, width: 100 },
          { title: this.$t('rfq.productName'), field: 'productName', visible: true, width: 100 },
          { title: this.$t('rfq.productModel'), field: 'productNumber', visible: true, width: 100 },
          { title: this.$t('rfq.sponsor'), field: 'creator', visible: true, width: 100, slots: { default: 'creator' }},
          {
            title: this.$t('rfq.projectStatus'),
            slots: { default: 'status' },
            sortable: true,
            field: 'status', visible: true, width: 100
          },
          {
            title: this.$t('common.sourcing'),
            field: 'sourcingSources',
            visible: true,
            slots: { default: 'sourcingSources' },
            width: 100
          },
          // {
          //   title: this.$t('rfq.billOfMaterialsHandler'),
          //   formatter: ({ cellValue }) => cellValue?cellValue.split(',').map(item => getDictDataLabel(DICT_TYPE.COMMON_USERS, item)).join('/'):'',
          //   field: 'bomBillManagerIdsStr', visible: true, width: 100
          // },
          {
            title: this.$t('rfq.purposeOfInquiry'),
            field: 'inquiryPurpose',
            slots: { default: 'inquiryPurpose' },
            visible: true,
            width: 100
          },
          { title: this.$t('order.customerName'), field: 'customName', visible: true, width: 100 },
          { title: this.$t('supplier.endCustomer'), field: 'endCustomer', visible: true, width: 100 },
          {
            title: this.$t('rfq.terminalCustomerModel'),
            field: 'endCustomerModel',
            visible: true,
            width: 130
          },
          { title: this.$t('rfq.application'), field: 'productLine', visible: true, width: 100 },
          { field: 'operate', slots: { default: 'operate' }, fixed: 'right', title: this.$t('common.operate'), visible: true, showOverflow: false, width: 35 }

        ],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      loading: false,
      list: [],
      total: 0,
      exportLoading:false
    }
  },
  mounted() {
    if (this.statistics && this.statistics.length > 0) {
      this.queryParams.statusList = this.statistics[0].status
    }
    this.getList()
    this.getSourcingSourcess()
  },
  activated() {
    this.getList()
  },
  methods: {
    showTerminate(item) {
      this.terminateCommentForm.opinion = ''
      this.terminateCommentVisible = true
      this.terminateCommentForm.projectId = item.id
    },
    submitTerminate() {
      this.terminateButtonFlag = true
      terminationProject({
        projectId: this.terminateCommentForm.projectId,
        content: this.terminateCommentForm.opinion
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.terminateCommentVisible = false
        this.getList()
        this.terminateButtonFlag = false
      }).catch(() => {
        this.terminateButtonFlag = false
      })
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.getList()
    },
    getList() {
      this.queryParams.beginDate = undefined
      this.queryParams.endDate = undefined
      if (this.queryParams.time) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      this.loading = true
      getProjectPage(this.queryParams).then(res => {
        this.list = res.data.list
        this.total = res.data.total
        this.loading = false
      })
    },
    // 项目寻源采购的数据源组装
    getSourcingSourcess() {
      this.sourcingSources.push(...getDictDatas(DICT_TYPE.RFQ_SOURCING_SOURCES, 0).map(item => ({
        id: item.value,
        name: item.label
      })))
      this.sourcingSources.push(...getDictDatas(DICT_TYPE.COMMON_USERS, 0))
    },
    handleClick() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        beginDate: '',
        creators: [],
        customName: '',
        dateType: '',
        endDate: '',
        eventName: '',
        inquiryPurpose: '',
        materialBillManagerList: [],
        myTodo: '',
        projectNo: '',
        purchaseOrgs: [],
        search: '',
        sortBy: this.queryParams.sortBy,
        sortField: this.queryParams.sortField,
        sourcings: [],
        statusList: ['processing', 'new', 'completed'],
        time: [],
        businessModelIds: []
      }
      this.getList()
    },
    revoke(row) {
      this.selectData = row
      this.revokeProjectOpen = true
    },
    revokeProject() {
      console.log(this.selectData.id)
      cancelProject({ ids: this.selectData.id, content: this.revokeProjectOpenData.content }).then(res => {
        if (res.data) {
          this.revokeProjectOpen = false
          this.$message.success(this.$t('rfq.cancellationSucceeded'))
          this.getList()
        } else {
          this.$message.success(res.msg)
        }
      })
    },
    // complete the project
    completeProject(projectId) {
      this.$modal.confirm(this.$t('确定要完成该项目吗？')).then(() => {
        return completeProject({ projectId: projectId })
      }).then(res => {
        if (res.data) {
          this.$message.success(this.$t('操作成功'))
          this.getList()
        } else {
          this.$message.success(res.msg)
        }
      }).catch(() => {
      })
    },
    handleStatusClick(dict) {
      this.queryParams.statusList = [...dict.status]
      this.getList()
    },
    exportExcel() {
      this.$modal.confirm(this.$t('Do you confirm the download?')).then(() => {
        this.exportLoading = true
        return exportRfsList(this.queryParams)
      }).then(response => {
        const formattedDate = dayjs().format('YYYYMMDD')
        this.$download.excel(response, '询价项目列表' + formattedDate + '.xlsx')
        this.exportLoading = false
      }).catch(() => {
      })
    },
  }
}
</script>

<style lang="scss" scoped>

.headCard{
  padding: 5px;
  padding-bottom: 0;
  //margin-bottom: -1px;
  cursor: pointer;
  flex: 1 1 14.2%;

  &-body {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    height: 112px;
    padding: 15px;
    background: #F9F9F9;
    border-radius: 8px 8px 0 0;
    //border-bottom: 3px solid #4996B8;

    &-num{
      font-size: 28px;
      color: #383838;
      font-weight: 700;
    }
    &-label{
      font-size: 14px;
      color: #565656;
      font-weight: 400;
    }
  }
}

.headCard-selected{
  position: relative;
  bottom: -3px;
  background: rgba(73,150,184,0.10);
  border: 3px solid #4996B8;
  border-bottom: 4px solid #fff !important;
  border-radius: 8px 8px 0 0;
}

.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 162px);
  }
}

.searchValue {
  width: 95%;
}

.searchTimeItem {
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 82px);
  }
}
</style>
