<template>
  <div>
    <div class="rfqHome-search">

      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.condition"
          :placeholder="$t('rfq.eventNameSupplierAbbreviationSupplierCode')"
          clearable
          style="flex: 0 1 40%"
          @keyup.enter.native="getList"
        />
        <el-button plain type="primary" @click="queryParams.pageNo = 1;getList();">{{ $t('common.search') }}</el-button>
        <el-button plain style="margin-left: 0" @click="handleClick">{{ $t('common.reset') }}</el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}

          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>

      </div>
      <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="162px" size="small">
        <el-form-item :label="$t('rfq.rfqItemNo')" class="searchItem" prop="deliveryNoteNo">
          <el-input
            v-model="queryParams.projectNo"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('rfq.rfqNo')" class="searchItem" prop="deliveryNoteNo">
          <el-input
            v-model="queryParams.quotationsNo"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('supplier.purchasingOrganization')" class="searchItem" prop="factoryIds">
          <el-select v-model="queryParams.purchaseOrg" class="searchValue" clearable multiple>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG,0)"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('rfq.eventName')" class="searchItem" prop="deliveryNoteNo">
          <el-input
            v-model="queryParams.eventName"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('common.sourcing')" class="searchItem" prop="sourcing">
          <el-select v-model="queryParams.sourcing" class="searchValue" clearable filterable multiple>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('supplier.supplier')" class="searchItem" prop="supplier">
          <el-input
            v-model="queryParams.supplier"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>

        <el-form-item :label="$t('order.timeType')" class="searchItem" prop="timeType">
          <el-select v-model="queryParams.timeType" class="searchValue" clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.RFQ_QUOTATION_PERSPECTIVE_DATE_TYPE)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item class="searchItem" label=" " prop="time">
          <el-date-picker
            v-model="queryParams.time"
            :end-placeholder="$t('common.endDate')"
            :range-separator="$t('order.to')"
            :start-placeholder="$t('common.startDate')"
            class="searchValue"
            type="daterange"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item :label="$t('rfq.rfqSource')" class="searchItem" prop="documentSource">
          <el-select v-model="queryParams.documentSource" class="searchValue">
            <el-option
              label=""
              value=""
            >{{ $t('order.whole') }}
            </el-option>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_SOURCE_OF_INQUIRY)"
              :key="dict.id"
              :label="dict.label"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('rfq.rfqStatus')" class="searchItem" prop="quotationsStatus">
          <el-select v-model="queryParams.quotationsStatusList" class="searchValue" multiple>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_QUOTATION_STATUS)"
              :key="dict.id"
              :label="dict.label"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item class="searchItem" label="" />
        <!--        <div style="text-align: center">-->
        <!--          <el-button plain icon="el-icon-search" size="mini" type="primary" @click="getList">{{-->
        <!--            $t('common.search')-->
        <!--          }}-->
        <!--          </el-button>-->
        <!--          <el-button icon="el-icon-refresh" size="mini" @click="handleClick">{{ $t('common.reset') }}</el-button>-->

        <!--        </div>-->
      </el-form>

    </div>

    <vxe-grid
      ref="quotationPerspective"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
      @sort-change="sortMethod"
    >
      <template #projectNo="{row}">
        <copy-button
          @click="$router.push(`/rfq/processHome/${encodeURIComponent(row.projectNo)}?id=${row.id}&code=${row.businessCode}`)"
        > {{ row.projectNo }}
        </copy-button>
      </template>
      <template #quotationsNo="{row}">
        <copy-button
          @click="$router.push(`/rfq/processHome/${encodeURIComponent(row.quotationsNo)}?id=${row.id}&quotationId=${row.quotationId}&active=1&code=${row.businessCode}`)"
        >
          {{ row.quotationsNo }}
        </copy-button>
      </template>
      <template #purchaseOrg="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="row.purchaseOrg" />
      </template>
      <template #businessModel="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_BUSINESS_TYPE" :value="row.businessModel" />
      </template>
      <template #sourcingId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcingId" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_QUOTATION_STATUS" :value="row.status" />
      </template>
      <template #materialBillManagerList="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.materialBillManagerList" />
      </template>

      <template #documentSource="{row}">
        <dict-tag :type="DICT_TYPE.RFQ_SOURCE_OF_INQUIRY" :value="row.documentSource" />
      </template>

      <template #star="{row}">
        <i v-if="row.star" class="el-icon-star-on" style="color: #FF9900" />
        <i v-else class="el-icon-star-off" />
      </template>

      <template #toolbar_buttons>
        <el-row :gutter="10" class="mb8" style="width: 100%">
          <el-col :span="20" style="display: flex">
            <el-button
              v-has-permi="['rfq:quotations-list:follow-up']"
              size="mini"
              type="primary"
              @click="followUp"
            > {{ $t('rfq.followUpOfRfq') }}
            </el-button>
            <el-button
              v-hasPermi="['rfq:quotations:query']"
              size="mini"
              plain
              type="primary"
              :loading="exportLoading"
              @click="exportExcel"
              icon="el-icon-download"
            >
              {{ $t('order.download') }}
            </el-button>

          </el-col>
          <el-col :span="4">
            <right-toolbar
              :custom-columns.sync="girdOption.columns"
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              @queryTable="getList"
            />
          </el-col>
        </el-row>
      </template>

    </vxe-grid>
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 询价单跟催 -->
    <el-dialog :title="$t('rfq.followUpOfRfq')" :visible.sync="followUpOpen" append-to-body width="1000px">
      <el-row>
        <el-col :span="24">
          <div>
            {{ $t('rfq.youAreAboutToSendAFollowupEmailToTheSupplierPleaseEnterTheDescription') }}
          </div>
          <div>
            <el-input v-model="followUpData.content" :rows="5" type="textarea" />
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="followUpOpen = false">{{ $t('common.cancel') }}</el-button>
        <el-button @click="actionFollowUp">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import {
  checkFollowUp,
  exportMaterialList, exportRfqList,
  followUp,
  getQuotationPerspectivePage,
  getQuotationPerspectivePageTotal
} from '@/api/rfq/home'
import event from '@/views/dashboard/mixins/event'
import dayjs from "dayjs";

export default {
  name: 'Rfqquotationperspective',
  mixins: [event],

  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        projectNo: '',
        quotationsNo: '',
        documentSource: '',
        timeType: '',
        from: '',
        to: '',
        purchaseOrg: [],
        sourcing: [],
        quotationsStatusList: ['to_quote'],
        eventName: '',
        supplier: '',
        condition: '',
        time: []
      },
      followUpOpen: false,
      followUpData: {
        quotationIds: [],
        content: ''
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'rfqQuotationPerspective',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: { isCurrent: true,
          isHover: true
        },
        columns: [
          { title: '', type: 'checkbox', visible: true, width: 30 },
          {
            title: this.$t('supplier.purchasingOrganization'),
            slots: { default: 'purchaseOrg' },
            field: 'purchaseOrg', visible: true, width: 100
          },
          { title: this.$t('rfq.eventName'), field: 'eventName', visible: true, width: 100 },
          {
            title: this.$t('rfq.rfqItemNo'),
            field: 'projectNo',
            visible: true,
            slots: { default: 'projectNo' },
            width: 200
          },
          {
            title: this.$t('rfq.rfqNo'),
            field: 'quotationsNo',
            visible: true,
            slots: { default: 'quotationsNo' },
            width: 200
          },
          { title: this.$t('supplier.supplier'), field: 'name', visible: true, width: 200 },
          {
            title: this.$t('common.creationDate'), field: 'createTime',
            sortable: true,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true, width: 100
          },
          {
            title: this.$t('rfq.requestedResponseDate'),
            sortable: true,
            field: 'dateRequested',
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true,
            width: 100
          },
          {
            title: this.$t('rfq.quotationResponseDate'),
            sortable: true,
            field: 'quoteReplyDate',
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true,
            width: 100
          },
          { title: this.$t('common.sourcing'), field: 'sourcingNames', visible: true, width: 100 },
          {
            title: this.$t('rfq.rfqStatus'),
            slots: { default: 'status' },
            sortable: true,
            field: 'status', visible: true, width: 100
          },
          { title: this.$t('rfq.purchasersQuotationRequirements'), field: 'quotationRequest', visible: true, width: 100 }

        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      loading: false,
      list: [],
      total: 0,
      exportLoading:false
    }
  },
  mounted() {
    this.getList()
  },
  activated() {
    this.getList()
  },
  methods: {
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.getList()
    },
    getList() {
      this.queryParams.from = undefined
      this.queryParams.to = undefined
      if (this.queryParams.time) {
        this.queryParams.from = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.to = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      this.loading = true
      getQuotationPerspectivePage(this.queryParams).then(res => {
        this.list = res.data.list
        this.loading = false
      })
      getQuotationPerspectivePageTotal(this.queryParams).then(res => {
        this.total = res.data
      })
    },

    handleClick() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        projectNo: '',
        quotationsNo: '',
        documentSource: '',
        timeType: '',
        from: '',
        to: '',
        purchaseOrg: [],
        sourcing: [],
        quotationsStatusList: [],
        eventName: '',
        supplier: '',
        condition: '',
        time: []
      }
      this.getList()
    },

    followUp() {
      const data = this.$refs.quotationPerspective.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('rfq.pleaseSelectARow'))
        return
      }
      // 检测选中报价单是否是待报价状态
      const quotationIds = data.map(j => j.quotationId)
      this.followUpData.quotationIds = quotationIds
      checkFollowUp(this.followUpData).then(j => {
        if (j.data) {
          this.followUpOpen = true
        }
      })
    },
    actionFollowUp() {
      const data = this.$refs.quotationPerspective.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('rfq.pleaseSelectARow'))
        return
      }
      const quotationIds = data.map(j => j.quotationId)
      this.followUpData.quotationIds = quotationIds
      followUp(this.followUpData).then(j => {
        if (j.data) {
          this.$message.success(this.$t('rfq.followUpSuccess'))
          this.followUpOpen = false
        } else {
          this.$message.error(this.$t('rfq.followUpFailed'))
        }
      })
    },
    exportExcel() {
      this.$modal.confirm(this.$t('Do you confirm the download?')).then(() => {
        this.exportLoading = true
        return exportRfqList(this.queryParams)
      }).then(response => {
        const formattedDate = dayjs().format('YYYYMMDD')
        this.$download.excel(response, '询价单列表' + formattedDate + '.xlsx')
        this.exportLoading = false
      }).catch(() => {
      })
    },

  }
}
</script>

<style lang="scss" scoped>
.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 162px);
  }
}

.searchValue {
  width: 95%;
}

.searchTimeItem {
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 82px);
  }
}
</style>
