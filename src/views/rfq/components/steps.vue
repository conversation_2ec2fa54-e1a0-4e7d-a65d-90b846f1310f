<template>
  <div class="rfq-step">
    <el-card
      v-for="(item,index) in steps"
      :key="item.id"
      :class="stepClass(item,index)"
      style="margin: 5px 3px;cursor:pointer;flex: 1 0 24%;color: #959595FF"
      @click.native="$emit('changeStep', index)"
    >
      <div style="display: flex;justify-content: space-between;align-items: center;height: 57px">
        <div
          :class="item.status === 'not_processed'?'disabled':''"
          style="margin:0 5px;flex-shrink: 0;font-size: 20px;font-weight: bold;"
        >
          <dict-tag :type="DICT_TYPE.RFQ_STEP_TYPE" :value="item.stepType" />

        </div>
        <div
          v-show="item.transactors && item.status!=='completed'"
          style="flex-shrink:1;margin-top: 8px;margin-left: 26px;
        text-overflow	:ellipsis;overflow: hidden;white-space: nowrap "
        >

          <el-tooltip effect="dark" :content="item.transactors" placement="bottom">
            <div>   {{ $t('rfq.handledBy') }} {{ item.transactors }}</div>
          </el-tooltip>

          <!--        <dict-tag :type="'userList'" :value="" />-->

        </div>
        <div style="flex-shrink: 0">
          <i v-if="item.status === 'processing'" class="el-icon-warning-outline commonIcon" />
          <i v-if="item.status === 'completed'" class="el-icon-circle-check commonIcon" />
        </div>

        <!--        <div class="hairline" />-->
      </div>
    </el-card>
  </div>
</template>

<script>

export default {
  name: 'Steps',
  props: ['steps', 'active'],
  methods: {
    stepClass(item, index) {
      if (item.status === 'not_processed') {
        return 'disabled'
      } else if (this.active === index) {
        return 'active'
      } else {
        return ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-card__body {
  padding: 0px 5px;

}
.circle {
  border-radius: 50%;
  width: 22px;
  height: 22px;
  padding: 4px;
  background: #4696b5;
  color: #ffffff;
  text-align: center;
  flex-shrink: 0;
  font: 14px Arial, sans-serif;
}

.circle-disabled {
  border-radius: 50%;
  flex-shrink: 0;
  width: 22px;
  height: 22px;
  padding: 3px;
  background: #e9e9e9;
  color: rgb(147, 146, 146);
  text-align: center;
  border: 1px solid #939292FF;
  font: 14px Arial, sans-serif;
}
.rfq-step{
  display: flex;
}
.hairline{
  margin: 11px 0;
  border: 0;
  border-top: 1px solid #e5e5e5;
  flex: 0 0 75%;
}
.disabled{
  cursor:not-allowed!important;
  color: rgba(149, 149, 149, 0.96)!important;

}
.commonIcon{
  //color: #a6a6a6;
  font-size: 45px;

}
.active{
  color: #4696b5!important;
  box-shadow: 0 2px 12px 0  #4696b5;
}
</style>
