<template>
  <div style="margin-bottom: 45px">
    <common-card
      style="margin: 0 0 10px 0;"
      :title="$t('rfq.rfqList')"
    >
      <el-table
        ref="quotationList"
        :data="quotationList"
        :default-sort="{}"
        border
        max-height="230"
        highlight-current-row
        size="mini"
        @row-click="changeQuotation"
        @sort-change="sortMethod"
      >
        <el-table-column :label="$t('rfq.rfqNo')" show-overflow-tooltip prop="quotationsNo" />
        <el-table-column :label="$t('supplier.supplierName')" show-overflow-tooltip prop="supplierName" />
        <el-table-column
          :label="$t('rfq.quoteStatus')"
          show-overflow-tooltip
          prop="quotationStatus"
          sortable="custom"
          width="100"
        >
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.RFQ_QUOTATION_STATUS" :value="scope.row.quotationStatus" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.creationDate')" show-overflow-tooltip prop="createTime" sortable="custom">
          <template #default="scope">
            {{ parseTime(scope.row.createTime) }}

          </template>
        </el-table-column>
        <el-table-column
          :label="$t('rfq.quotationDeadline')"
          show-overflow-tooltip
          prop="dateRequested"
          sortable="custom"
          width="130"
        >
          <template #default="scope">
            {{ parseTime(scope.row.dateRequested, '{y}-{m}-{d}') }}
          </template>

        </el-table-column>

<!--        total material-->
        <el-table-column :label="$t('物料总数')" show-overflow-tooltip prop="totalMaterials" />
<!--        quoted material-->
        <el-table-column :label="$t('报价物料数')" show-overflow-tooltip prop="quotedMaterials" />
<!--        quote rate-->
        <el-table-column :label="$t('报价率')" show-overflow-tooltip prop="quoteRate" >
          <template slot-scope="scope">
            <span>{{scope.row.quoteRate +"%"}}</span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('rfq.targetPriceStatus')" show-overflow-tooltip prop="targetPriceTag" width="100">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.RFQ_TARGET_PRICE_TAG" :value="scope.row.targetPriceTag" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.associatedRfqNo')" show-overflow-tooltip prop="parentNo" />
        <el-table-column :label="$t('rfq.reasonForReturn')" show-overflow-tooltip prop="returnComment" />
        <el-table-column v-if="$store.getters.isExternal===1" width="50" :label="$t('common.operate')">
          <template #default="{row}">
            <OperateDropDown
              :menu-item="[
                {
                  name: $t('rfq.return'),
                  show: row.canReturn,
                  action: (row)=>showRfqReturnForm(row),
                  para: row
                },
                {
                  name: $t('rfq.followAndUrge'),
                  show: row.quotationStatus==='to_quote'&&$store.getters.permissions.includes('rfq:quotations:follow-up'),
                  action: (row)=>followUp(row.quotationId),
                  para: row
                },
                {
                  name: $t('rfq.copyAddress'),
                  show: row.quotationStatus==='to_quote',
                  action: (row)=>copyUrl(row.quotationId),
                  para: row
                },
                {
                  name: $t('rfq.termination'),
                  show: row.quotationStatus==='to_quote'&&$store.getters.permissions.includes('rfq:quotations-recommend:quotation-return'),
                  action: (row)=>showReturnForm(row),
                  para: row
                },
              ]"
            />
          </template>
        </el-table-column>
      </el-table>
      <div
        v-show="false"
        ref="copyDiv"
        v-clipboard:copy="copyMessage"
        v-clipboard:success="()=>{$modal.msgSuccess($t('rfq.copiedInquiryHyperlink'))}"
      />
    </common-card>

    <el-card
      v-if="showQuote"
      class="cardSpacing cardBefore"
      style="margin-bottom: 20px;position: relative; overflow: hidden;"
    >
      <div slot="header">
        <div style="display: flex;justify-content: space-between;">
          <div style="flex: 1">
            {{ $t('rfq.inquiryDetails') }} - {{ selectRow.supplierName }} - {{ selectRow.quotationsNo }}
            <dict-tag :type="DICT_TYPE.RFQ_QUOTATION_STATUS" :value="selectRow.quotationStatus" />
          </div>
          <div style="flex: 1;text-align: right;font-size: 14px;padding-right: 10px">
            <a :class="{ 'active-link':(activeLink === 'section1' || activeLink ===''),tagLink:true }" @click="$refs['section1'].$el.scrollIntoView({block: 'center',behavior: 'smooth'});activeLink='section1'">{{ $t('rfq.quotationBom') }}</a>
            |
            <a :class="{ 'active-link': activeLink === 'section2' ,tagLink:true}" @click="$refs['section2'].scrollIntoView({block: 'center',behavior: 'smooth'});activeLink='section2'">{{ $t('rfq.purchasersQuotationRequirements') }}
              <el-badge v-if="fileBuyerList.findIndex(v=>!v.downloadFlag)>=0" value="new" />
            </a>
            |
            <a :class="{ 'active-link': activeLink === 'section3' ,tagLink:true}" @click="$refs['section3'].scrollIntoView({block: 'center',behavior: 'smooth'});activeLink='section3'">{{ $t('rfq.supplierQuotationFeedback') }}</a>
          </div>
        </div>
      </div>
      <div ref="content" class="scrollable-content">
        <el-card v-if="showQuote" ref="section1" style="margin-bottom: 20px">
          <div slot="header">
            {{ $t('rfq.quotationBom') }}
          </div>
          <el-tabs
            v-model="activeCategory"
            :before-leave="beforeLeave"
            class="form"
            style=" float:none;"
            @tab-click="changeTemplate"
          >
            <el-tab-pane
              v-for="item in templateTabs"
              :key="item.templateId"
              :name="String(item.templateId)"
            >
              <span slot="label">{{ item.templateName }}<el-badge
                :value="item.materialCount>99?'99+':(item.materialCount+''==='0'?'':item.materialCount)"
                style="margin-left: 5px;height: 33px;"
              /></span>
            </el-tab-pane>
          </el-tabs>
          <div style="display: flex;justify-content: center;margin-bottom: 15px">

            <el-input
              v-model="quotationMaterialQueryParams.materialCode"
              :placeholder="$t('rfq.pleaseEnterTheMaterialCode')"
              style="width: 500px"
            />
            <el-button plain type="primary" @click="quotationMaterialQueryParams.pageNo=1;getTable();">
              {{ $t('common.search') }}
            </el-button>
            <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>

          </div>
          <div>
            <el-button v-if="purchaseRequirements.status==='to_quote'" size="mini" plain type="primary" @click="upload.open = true">
              {{ $t('rfq.batchQuotation') }}
            </el-button>
            <el-button
              v-if="purchaseRequirements.status==='quoted'"
              size="mini"
              plain
              type="primary"
              @click="downloadSupplierQuotation">
              {{ $t('下载报价') }}
            </el-button>
            <el-button
              v-has-permi="['rfq:quotations:query']"
              plain
              size="mini"
              type="primary"
              @click="downloadMaterialFiles"
            >{{ $t('rfq.batchDownloadDrawings') }}
            </el-button>
          </div>

          <vxe-grid
            ref="rfqMaterialGrid"
            :data="list"
            :loading="loading"
            :span-method="mergeRowMethod"
            v-bind="girdOption"
          >
            <template #requiredField="{column}">
              <span class="requiredTitle">
                {{ column.title }}
              </span>
            </template>
            <template v-for="item in dictTemplateFields" #[item.field]="{row,$rowIndex}">
              <span v-if="item.field === 'materialCode'">
                <i
                  v-if="row.materialDrawing"
                  class="el-icon-picture-outline"
                  style="font-size: 16px;margin-right: 3px;cursor: pointer"
                  @click="downloadMaterialDrawing(row.projectMaterialId,row.materialCode)"
                />
                <copy-button
                  @click="showMaterialDetail(row.projectMaterialId)"
                >
                  {{ row.materialCode }}</copy-button>
              </span>
              <dict-tag v-if="!item.modify" :type="item.type" :value="row[item.field]" />
              <show-or-edit
                v-else-if="item.fieldType === 'Date'"
                :disabled="disabledFields(item.field)"
                :value="row[item.field]"
                type="Date"
              >
                <el-date-picker
                  v-model="row[item.field]"
                  :placeholder="$t('order.selectDate')"
                  style="width: 140px"
                  type="date"
                />
              </show-or-edit>

              <show-or-edit
                v-else-if="item.modify&&item.type&&item.fieldType==='String'"
                :dict="item.type"
                :disabled="disabledFields(item.field)"
                :value="row[item.field]"
              >
                <el-select
                  v-model="row[item.field]"
                >
                  <el-option
                    v-for="dict in getDictDatas(item.type)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </show-or-edit>

              <show-or-edit
                v-else-if="item.modify&&item.type"
                :dict="item.type"
                :disabled="disabledFields(item.field)"
                :value="row[item.field]"
              >

                <el-select
                  v-model="row[item.field]"
                  @change="changeTaxRate(item.field,row[item.field])"
                >
                  <el-option
                    v-for="dict in getDictDatas(item.type)"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>

              </show-or-edit>

              <show-or-edit
                v-else-if="item.fieldType === 'String'"
                :disabled="disabledFields(item.field)"
                :value="row[item.field]"
              >
                <el-input
                  v-model="row[item.field]"
                  style="width: 80%"
                  type="text"
                />
              </show-or-edit>
              <show-or-edit
                v-else-if="item.fieldType === 'Integer'"

                :disabled="disabledFields(item.field)"
                :value="row[item.field]"
                type="Number"
              >
                <vxe-input
                  v-model="row[item.field]"
                  min="0"
                  style="width: 80%;display: inline-block"
                  type="integer"
                  @change="changeField(item.field,row[item.field])"
                />

              </show-or-edit>
              <show-or-edit
                v-else

                :disabled="disabledFields(item.field)"
                :value="row[item.field]"
                type="Number"
              >
                <vxe-input
                  v-model="row[item.field]"
                  min="0"
                  style="width: 80%;display: inline-block"
                  type="number"
                  @change="changeField(item.field,row[item.field])"
                />

              </show-or-edit>
              <i
                v-if="item.field === 'quotationQuantityFrom'&&!disabledFields(item.field)"
                class="el-icon-circle-plus"
                style="cursor:pointer;margin-left: 5px;font-size: 18px"
                @click="addQuantity(row,$rowIndex)"
              />

              <i
                v-if="item.field === 'quotationQuantityTo'&&!disabledFields(item.field)"
                class="el-icon-remove"
                style="cursor:pointer;margin-left: 5px;font-size: 18px"
                @click="delQuantity(row,$rowIndex)"
              />
            </template>

          </vxe-grid>
          <pagination
            v-show="total > 0"
            :limit.sync="quotationMaterialQueryParams.pageSize"
            :page.sync="quotationMaterialQueryParams.pageNo"
            :total="total"
            @pagination="getTable"
          />
          <div class="fixedBottom">
            <el-button
              v-if="purchaseRequirements.status==='to_quote'&& store.getters.isExternal === 1"
              v-has-permi="['rfq:quotations:supplier_confirm']"
              type="primary"
              plain
              @click="sendTargetPriceFlag"
            >{{ $t('rfq.supplierConfirmationOfPrice') }}
            </el-button>
            <el-button
              v-if="purchaseRequirements.status==='to_quote'"
              v-has-permi="['rfq:quotations:update']"
              :loading="quotationSubmitLoading"
              type="primary"
              @click="quotationSubmit"
            >{{ $t('common.submit') }}
            </el-button>
            <el-button
              v-if="purchaseRequirements.status==='to_quote'"
              v-has-permi="['rfq:quotations:update']"
              plain
              type="primary"
              @click="saveQuotation"
            >{{ $t('common.save') }}
            </el-button>
            <el-button v-if="['quoted','no_quote'].includes(purchaseRequirements.status)" type="danger" plain @click="revoke">
              {{ $t('rfq.revoke') }}
            </el-button>

          </div>
        </el-card>
        <div ref="section2" class="el-card__header" style="border-bottom:unset">
          <div class="mainTab">{{ $t('rfq.purchasersRequirements') }}</div>
        </div>
        <div style="display: flex;justify-content: space-between;flex-wrap: wrap;margin-left: 20px">
          <div style="flex: 0 0 49%">
            <el-descriptions :column="1" label-class-name="purchaseRequire">
              <el-descriptions-item :label="$t('rfq.deadlineForQuotation')">
                <div style="width: 100%">
                  {{ parseTime(purchaseRequirements.dateRequested, '{y}-{m}-{d}') }}
                </div>
              </el-descriptions-item>
              <el-descriptions-item :label="$t('rfq.documentSource')">
                <dict-tag :type="DICT_TYPE.RFQ_SOURCE_OF_INQUIRY" :value="purchaseRequirements.documentSource " />
              </el-descriptions-item>
              <el-descriptions-item :label="$t('rfq.purchasersQuotationRequirements')">
                {{ purchaseRequirements.quotationRequest }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div style="flex: 0 0 49%">
            <el-descriptions :column="1" label-class-name="purchaseRequire">
              <el-descriptions-item v-if="purchaseRequirements.documentSource === 'return'" :label="$t('rfq.returnedBy')">
                {{ purchaseRequirements.rejectProcessRecord.operator }}
              </el-descriptions-item>
              <el-descriptions-item v-if="purchaseRequirements.documentSource === 'return'" :label="$t('rfq.returnTime')">
                {{ parseTime(purchaseRequirements.rejectProcessRecord?.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
              </el-descriptions-item>
              <el-descriptions-item
                v-if="purchaseRequirements.documentSource === 'return'"
                :label="$t('rfq.reasonForReturn')"
              >
                {{ purchaseRequirements.rejectProcessRecord?.comment }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <el-descriptions :column="1" label-class-name="purchaseRequire">
            <el-descriptions-item :label="$t('rfq.basicInformationOfTheProject')">
              <el-button type="text" style="padding-top: 3px;" @click="projectInfoOpen = true">{{ $t('rfq.clickToView') }}
              </el-button>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('rfq.viewPurchaserAttachments')">
              <el-button
                :disabled="fileBuyerList.length===0"
                :type="fileBuyerList.length?'primary':''"
                class="uploadBtn"
                plain
                size="small"
                style="padding: 5px 9px"
                @click="showBuyerFile=true"
              >
                {{ fileBuyerList.length }}
              </el-button>
              <el-dialog
                v-if="showBuyerFile"
                :visible.sync="showBuyerFile"
                :title="$t('scar.viewAttachments')"
                width="400px"
              >
                <el-upload
                  :action="uploadUrl"
                  :disabled="true"
                  :file-list="fileBuyerList"
                  :headers="getBaseHeader()"
                  :limit="5"
                  :on-preview="onBuyerPreview"
                  :on-remove="onRemove"
                  :on-success="(response, file, fileList)=>onSuccess(response, file, fileBuyerList,)"
                  multiple
                />
                <div slot="footer">
                  <el-button type="primary" @click="showBuyerFile=false">{{ $t('order.close') }}</el-button>
                </div>

              </el-dialog>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div ref="section3" class="el-card__header" style="border-bottom:unset">
          <div class="mainTab">{{ $t('rfq.supplierQuotationFeedback') }}</div>
        </div>
        <div style="display: flex;justify-content: space-between;flex-wrap: wrap;margin-left: 20px">
          <el-descriptions :column="1" label-class-name="purchaseRequire">
            <el-descriptions-item :label="$t('rfq.supplierQuotationRemarks')">
              <el-input
                v-model="purchaseRequirements.remark"
                :disabled="purchaseRequirements.status!=='to_quote'"
                :rows="1"
                type="textarea"
                @blur="saveQuotationRemark()"
              />
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions :column="6" label-class-name="purchaseRequire">
            <el-descriptions-item :label="$t('scar.theFieldOfContainmentMeasuresMaterialDisposalMeasuresMustBeFilledIn')">
              <template slot="label">
                <el-button
                  v-has-permi="['rfq:quotations:query']"
                  plain
                  size="mini"
                  style="float: left;"
                  type="primary"
                  @click="showPreviewReport"
                >{{ $t('rfq.generateQuotation') }}
                </el-button>
              </template>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('rfq.uploadQuotationAttachments')">
              <el-upload
                :action="uploadUrl"
                :disabled="purchaseRequirements.status!=='to_quote'"
                :file-list="fileList"
                :headers="getBaseHeader()"
                :limit="5"
                :on-preview="onPreview"
                :on-remove="onRemove"
                :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                :show-file-list="false"
                multiple
              >
                <el-button
                  v-if="purchaseRequirements.status==='to_quote'"

                  v-has-permi="['infra:file:upload']"
                  class="uploadBtn"
                  icon="el-icon-plus"
                  plain
                  size="mini"
                  type="primary"
                />

              </el-upload>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('rfq.viewQuotationAttachments')">
              <el-button
                :disabled="fileList.length===0"
                :type="fileList.length?'primary':''"
                class="uploadBtn"
                plain
                size="small"
                style="padding: 5px 9px"
                @click="showFile=true"
              >
                {{ fileList.length }}
              </el-button>
              <el-dialog
                v-if="showFile"
                :visible.sync="showFile"
                :title="$t('scar.viewAttachments')"
                width="400px"
              >
                <el-upload
                  :action="uploadUrl"

                  :disabled="purchaseRequirements.status!=='to_quote'"
                  :file-list="fileList"
                  :headers="getBaseHeader()"
                  :limit="5"
                  :on-preview="onPreview"
                  :on-remove="onRemove"
                  :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
                  multiple
                />
                <div slot="footer">
                  <el-button type="primary" @click="showFile=false">{{ $t('order.close') }}</el-button>
                </div>

              </el-dialog>
            </el-descriptions-item>
          </el-descriptions>

        </div>
      </div>
    </el-card>
    <el-dialog :title="$t('rfq.followUpOfRfq')" :visible.sync="followUpOpen" append-to-body width="1000px">
      <el-row>
        <el-col :span="24">
          <div>{{ $t('rfq.youAreAboutToSendAFollowupEmailToTheSupplierPleaseEnterTheDescription') }}</div>
          <div>
            <el-input v-model="followUpData.content" :rows="5" type="textarea" />
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="followUpOpen = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="actionFollowUp">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
    <el-dialog :title="$t('rfq.viewBasicProjectInformation')" :visible.sync="projectInfoOpen" append-to-body width="1000px">
      <el-descriptions :column="2" :label-style="{width:'120px','text-align':'right'}">
        <el-descriptions-item
          v-for="item in Object.keys(configList)"
          v-if="baseInfoFieldConfig.includes(item)"
          :key="item"
          :label="configList[item]"
        >
          <span v-if="!['requestPaymentMethod','requestDeliveryMethod'].includes(item)">
            {{ rfqProject[item] }}
          </span>
          <span v-else>

            <dict-tag
              v-if="item === 'requestPaymentMethod'"
              :type="DICT_TYPE.SUPPLIER_PAYMENT_DAYS"
              :value="rfqProject[item]"
            />
            <dict-tag
              v-if="item === 'requestDeliveryMethod'"
              :type="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION"
              :value="rfqProject[item]"
            />
          </span>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
    <el-dialog :title="upload.title" :visible.sync="upload.open" append-to-body width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url"
          :auto-upload="false"
          :data="uploadData"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-preview="onPreview"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button plain type="primary" @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="returnCommentVisible"
      :title="$t('rfq.opinion')"
      :visible.sync="returnCommentVisible"
      width="400px"
    >
      <div>
        <p>{{ $t('rfq.youAreTerminatingRfqMaterialsPleaseEnterTerminationComments') }}</p>
      </div>
      <el-input
        v-model="returnCommentForm.opinion"
        :placeholder="$t('rfq.pleaseEnterComments')"
        :rows="5"
        type="textarea"
      />
      <div slot="footer">
        <el-button @click="returnCommentVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button :loading="returnButtonFlag" type="primary" @click="submitReturn">{{
          $t('order.determine')
        }}
        </el-button>

      </div>
    </el-dialog>
    <el-dialog
      v-if="rfqReturnCommentVisible"
      :title="$t('rfq.opinion')"
      :visible.sync="rfqReturnCommentVisible"
      width="400px"
    >
      <div>
        <div>{{ $t('rfq.pleaseEnterTheReturnCommentsOfTheRfqSheet') }}</div>
        <p>{{ $t('rfq.afterYouClickOkTheSystemWillAutomaticallyReturnTheQuotation') }}</p>
      </div>
      <el-input
        v-model="returnCommentForm.opinion"
        :placeholder="$t('rfq.pleaseEnterComments')"
        :rows="5"
        type="textarea"
      />
      <div slot="footer">
        <el-button @click="rfqReturnCommentVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" :loading="rfqReturnButtonFlag" @click="submitRfqReturn">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
    <!--提交报价弹窗-->
    <el-dialog
      v-if="submitQuoteVisible"
      :title="$t('common.submit')"
      :visible.sync="submitQuoteVisible"
      width="600px"
    >
      <div>
        <div>
          <i class="el-icon-warning-outline" style="color: red;" />
          {{ $t('rfq.find') }}<span style="color:red;">{{ $t('rfq.noQuotedMaterials') }}</span>{{ $t('rfq.pleaseConfirmTheHandlingMethod') }}
        </div>
        <p style="text-indent: 2em">{{ $t('rfq.submissionAllMaterialsMustBeSubmittedMaterialsWithoutAQuotationCannotBeQuotedAgain') }}</p>
        <p style="text-indent: 2em">{{ $t('rfq.partialSubmissionOnlySubmitMaterialsWithQuotationMaterialsWithoutAQuotationCanStillBeQuoted') }}</p>
      </div>
      <div slot="footer">
        <el-button
          v-if="purchaseRequirements.status==='to_quote'"
          v-has-permi="['rfq:quotations:update']"
          :loading="submitPartLoading"
          type="primary"
          plain
          @click="submitPart"
        >{{ $t('rfq.partialSubmission') }}
        </el-button>
        <el-button
          v-if="purchaseRequirements.status==='to_quote'"
          v-has-permi="['rfq:quotations:update']"
          :loading="submitQuoLoading"
          type="primary"
          @click="submitQuo"
        >{{ $t('common.submit') }}
        </el-button>
        <el-button @click="submitQuoteVisible = false;quotationSubmitLoading = false">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>
    <rfqMaterial
      ref="rfqMaterial"
      :material-visible.sync="materialVisible"
      :rfq-project-id="true"
      :rfq-project-status="null"
    />
  </div>
</template>

<script>
import { getBaseHeader } from '@/utils/request'
import {
  cancelQuo,
  checkMaterialquote,
  checkMaterialStatusByQuotationId,
  checkQuotationCancel,
  delRfqFile,
  downloadMaterialDrawings,
  downloadMaterialFiles,
  followUp,
  getFieldsByTemplate,
  getQuotation,
  getQuotationConfig,
  getQuotationList,
  getQuotationReplyData,
  copyTokenUrl,
  getRfqFile,
  getRfqProject,
  getTemplateMaterialCount,
  quotationTemplate,
  rfqFilePost,
  saveQuotationMaterial,
  saveQuotationRemark,
  submitPart,
  submitQuo, updateTargetPriceTag, downloadSupplierQuotation
} from '@/api/rfq/home'
import { mapGetters } from 'vuex'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { getPreviewReport } from '@/api/visualization/report'
import store from '@/store'
import { returnRecommendQuotation, terminateRecommendQuotation } from '@/api/rfq/quoationRecommend'
import OperateDropDown from '@/components/OperateDropDown/index.vue'

export default {
  name: 'Supplierquote/:quotationsno',
  components: {
    OperateDropDown,
    ShowOrEdit,
    rfqMaterial: () => import('@/views/rfq/components/material')
  },
  props: ['steps'],
  data() {
    return {
      activeLink: '',
      checkQuoteStatus: null,
      partialCommitLoading: false,
      returnCommentVisible: false,
      rfqReturnCommentVisible: false,
      rfqReturnButtonFlag: false,
      quotationSubmitLoading: false,
      getBaseHeader,
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      fileList: [],
      fileBuyerList: [],
      queryParams: {
        pageNo: -1,
        pageSize: 10,
        projectId: 0,
        supplierId: ''
      },
      configList: {
        customName: this.$t('order.customerName'),
        endCustomer: this.$t('supplier.endCustomer'),
        productName: this.$t('rfq.productName'),
        endCustomerModel: this.$t('rfq.terminalCustomerModel'),
        productNumber: this.$t('rfq.productModel'),
        requestPaymentMethod: this.$t('rfq.paymentMethodRequired'),
        productLine: this.$t('rfq.application'),
        requestDeliveryMethod: this.$t('rfq.requestedDeliveryMethod'),
        shippingDestination: this.$t('rfq.loadingDestination')
      },
      quotationId: null,
      selectRow: {},
      copyMessage: '',
      rfqProject: {
        businessModel: 0,
        customName: '',
        endCustomer: '',
        endCustomerModel: '',
        eventName: '',
        expirationDate: '',
        id: '',
        inquiryPurpose: '',
        materialBillManagerList: [],
        modifyOrderUnits: false,
        productLine: '',
        productName: '',
        productNumber: '',
        projectNo: '',
        purchaseOrg: '',
        requestDeliveryMethod: '',
        requestPaymentMethod: '',
        shippingDestination: '',
        sourcingSources: '',
        status: '',
        supplierPriceLadder: false
      },
      activeCategory: 'uname',
      baseInfoFieldConfig: [],
      purchaseRequirements: {
        createTime: '',
        dateRequested: '',

        documentSource: '',
        id: 0,
        parentId: 0,
        parentNo: '',
        projectId: 0,
        quotationRequest: '',
        quotationsNo: '',
        quoteReplyDate: '',
        rejectProcessRecord: {
          action: '',
          businessId: 0,
          businessType: '',
          comment: '',
          createTime: '',
          id: 0
        },
        remark: '',
        sourcingIds: '',
        sourcingNames: '',
        status: '',
        supplierId: 0
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'rfqQuotation',
        maxHeight: 700,
        minHeight:0,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        editConfig: {
          trigger: 'click',
          mode: 'row',
          showUpdateStatus: true
          // beforeEditMethod: this.beforeEditMethod,
          // editClosed: this.editClosed

        },
        checkboxConfig: {
          reserve: true
        },
        rowConfig: {
          isCurrent: true,
          keyField: 'projectMaterialId',
          isHover: true
        },
        columns: [],

        sortConfig: {
          remote: true
        }
        // toolbarConfig: {
        //   slots: {
        //     // 自定义工具栏模板
        //     buttons: 'toolbar_buttons'
        //   }
        // }
      },

      returnCommentForm: {
        opinion: '',
        quotationId: ''
      },
      quotationList: [],
      quotationTotal: 0,
      templateTabs: [],
      dictTemplateFields: [],
      list: [],
      loading: false,
      total: 0,
      quotationMaterialQueryParams: {
        materialCode: '',
        pageNo: 1,
        pageSize: 10
        // supplierId: ''
      },
      followUpOpen: false,
      projectInfoOpen: false,
      followUpData: {
        content: '',
        quotationIds: []
      },
      recommendOptions: {
        projectMaterialIds: [],
        quotationIds: [],
        projectId: null
      },
      returnButtonFlag: false,
      bulkQuotationVisible: false,
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: this.$t('rfq.batchUploadQuotations'),
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/rfq/quotations/import'
      },
      uploadData: {
        templateId: null,
        quotationId: null
      },
      showQuote: true,
      fullFields: ['deliveryType', 'orderUnitQuantity', 'measureUnitsQuantity', 'priceValidUntil', 'deliveryPeriod', 'grossWeight', 'moldCurrency', 'coreMaterial', 'assemblyCost', 'mpq', 'moq', 'taxRate', 'originalCurrency', 'orderUnit', 'priceUnit', 'mfg', 'mpn', 'remark', 'paymentMethod', 'w_contract_price'], // 无论什么值都合并的字段
      // fullFields:[],
      materialVisible: false,
      categoryList: [],
      mfgMpnList: [],
      quantity: [],
      noMergeField: [],
      materialField: [],
      showFile: false,
      showBuyerFile: false,
      submitQuoteVisible: false, // 提交报价弹窗
      submitQuoLoading: false, // 提交报价弹窗中提交报价按钮loading
      submitPartLoading: false // 提交报价弹窗中部分提交按钮loading
    }
  },
  computed: {
    store() {
      return store
    },
    ...mapGetters([
      'permissions'
    ])
  },
  // computed: {
  //   quotationMaterialEditFields() {
  //     return this.dictTemplateFields.filter(item => item.modify)
  //   }
  //

  // },
  mounted() {
    this.rfqProject.materialBillManagerList = [this.$store.getters.userId]
    this.queryParams.projectId = this.steps?.projectId || this.$route.query.projectId
    if (this.$route.query.headerSupplierId) {
      this.queryParams.supplierId = this.$route.query.headerSupplierId
      // this.quotationMaterialQueryParams.supplierId = Number(this.$route.query.headerSupplierId)
    }
    this.init()
    this.getCategories()
  },
  methods: {
    // 弹出预览
    showPreviewReport() {
      var mapObject = new Map()
      mapObject.set('id', this.purchaseRequirements.id)
      getPreviewReport({
        reportId: this.purchaseRequirements.reportId,
        _n: this.purchaseRequirements.quotationsNo,
        ...Object.fromEntries(mapObject)
      }).then(res => {
        window.open(res.data)
      })
    },
    sortMethod({ order, prop }) {
      this.queryParams.sortBy = order === 'descending' ? 'desc' : 'asc'
      this.queryParams.sortField = prop
      this.init()
    },
    init(cacheQuotationId) {
      getQuotationList(this.queryParams).then(res => {
        this.quotationList = res.data.list
        this.quotationTotal = res.data.total
        const quotationId = Number(this.$route.query?.quotationId)
        if (quotationId) {
          this.quotationId = quotationId
        } else {
          this.quotationId = this.quotationList[0].quotationId
        }
        if (cacheQuotationId) {
          this.quotationId = cacheQuotationId
        }
        // 默认选中行的报价状态
        this.checkQuoteStatus = this.quotationList[0].quotationStatus
        const find = this.quotationList.find(item => item.quotationId === this.quotationId)
        this.selectRow = { ...find }
        this.$refs.quotationList.setCurrentRow(find)
        this.getQuotationInfo()
        this.getBaseInfoFieldConfig()
        this.getFileList()
        this.getBuyerFileList()
        this.getTemplateTabs()
      })
    },
    closeMaterial(close) {
      close()
    },
    showMaterialDetail(id) {
      this.materialVisible = true
      this.$refs.rfqMaterial.showMaterialDetail(id)
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    getQuotationInfo() {
      getQuotation({
        id: this.quotationId
      }).then(res => {
        this.purchaseRequirements = res.data
      })
    },
    getBaseInfoFieldConfig() {
      getQuotationConfig({
        id: this.quotationId
      }).then(res => {
        this.baseInfoFieldConfig = res.data?.displayField.split(',')
        getRfqProject(
          {
            id: this.queryParams.projectId
          }
        ).then(res => {
          this.rfqProject = res.data
        })
      })
    },
    onRemove(file, fileList) {
      this.fileList.splice(this.fileList.findIndex(a => a.id === file.id), 1)

      if (file.id) {
        delRfqFile({
          id: file.id
        }).then(res => {
          this.$message({
            message: this.$t('common.delSuccess'),
            type: 'success'
          })
        })
      }
    },
    onSuccess(response, file, fileList) {
      rfqFilePost({
        fileId: response.data.id,
        businessId: this.quotationId,
        businessType: 'RFQ_QUOTATIONS'
      }).then(res => {
        file.id = res.data
        this.getFileList()
        this.$message.success(this.$t('common.uploadSucceeded'))
      })
    },
    getFileList() {
      getRfqFile({
        businessId: this.quotationId,
        businessType: 'RFQ_QUOTATIONS'
      }).then(res => {
        this.fileList = res.data.map(item => {
          return {
            name: item.fileName,
            url: item.filePath,
            id: item.id
          }
        })
      })
    },
    getBuyerFileList() {
      getRfqFile({
        businessId: this.quotationId,
        businessType: 'RFQ_BUYER_QUOTATIONS'
      }).then(res => {
        this.fileBuyerList = res.data.map(item => {
          return {
            name: item.fileName,
            url: item.filePath,
            id: item.id,
            downloadFlag: item.downloadFlag
          }
        })
      })
    },
    /**
     * 刷新报价单的数字
     */
    refershTemplateTabs() {
      getTemplateMaterialCount({
        quotationId: this.quotationId
      }).then(res => {
        this.templateTabs = res.data
      })
    },
    getTemplateTabs() {
      this.showQuote = false
      getTemplateMaterialCount({
        quotationId: this.quotationId
      }).then(res => {
        this.showQuote = true
        this.templateTabs = res.data
        this.activeCategory = this.templateTabs[0].templateId.toString()
        this.getTemplateFields()
        this.getTable()
      })
    },
    getTemplateFields() {
      getFieldsByTemplate({
        templateId: this.activeCategory
      }).then(res => {
        this.dictTemplateFields = []
        this.materialField = []
        this.noMergeField = []
        const fixedCol = ['materialDescription']
        this.girdOption.columns = res.data.filter(a => !a.hidden).sort((a, b) => a.sort - b.sort).map((item, index) => {
          if (item.merge === 0) {
            this.materialField.push(item.fieldCode)
          } else if (item.merge === 2) {
            this.noMergeField.push(item.fieldCode)
          }
          const common = {
            title: item.fieldName,
            field: item.fieldCode,
            visible: true,
            slots: {},
            width: 150,
            fixed: fixedCol.findIndex(v => v === item.fieldCode) >= 0 ? 'left' : ''
          }
          // 物料编码需要加图纸下载
          if (item.fieldDictType || item.modify || item.fieldCode === 'materialCode') {
            this.dictTemplateFields.push({
              type: item.fieldDictType?.slice(item.fieldDictType.indexOf(':') + 1),
              fieldType: item.fieldType,
              field: item.fieldCode,
              modify: item.modify,
              required: item.required
            })
            // if (item.modify) {
            //   common.slots.edit = item.fieldCode + 'Edit'
            //   common.editRender = {}
            // }
            if (item.required) {
              common.slots.header = 'requiredField'
            }
            common.slots.default = item.fieldCode
          }
          return common
        })
      })
    },
    showReturnForm(row) {
      this.returnCommentVisible = true
      this.returnCommentForm.quotationId = row.quotationId
    },
    showRfqReturnForm(row) {
      this.rfqReturnCommentVisible = true
      this.returnCommentForm.quotationId = row.quotationId
      this.returnCommentForm.opinion = ''
    },
    submitRfqReturn() {
      returnRecommendQuotation({
        projectId: this.queryParams.projectId,
        quotationId: this.returnCommentForm.quotationId,
        reason: this.returnCommentForm.opinion
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.rfqReturnCommentVisible = false
        this.init(this.quotationId)
      })
    },
    submitReturn() {
      terminateRecommendQuotation({
        quotationId: this.returnCommentForm.quotationId,
        reason: this.returnCommentForm.opinion
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.returnCommentVisible = false
        this.init(this.quotationId)
        this.emitter.emit('freshStep', this.queryParams.projectId)
      })
    },
    changeQuotation(row, column) {
      // 选中行的报价状态
      this.checkQuoteStatus = row.quotationStatus
      this.quotationId = row.quotationId
      this.selectRow = row
      this.getQuotationInfo()
      this.getBaseInfoFieldConfig()
      this.getFileList()
      this.getBuyerFileList()
      this.getTemplateTabs()
    },
    downloadMaterialDrawing(materialId, materialCode) {
      downloadMaterialDrawings({ projectMaterialId: materialId }).then(res => {
        this.$download.zip(res, materialCode + '.zip')
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    },
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      // const fields = ['quotationQuantityFrom',
      //   'quotationQuantityTo', 'originalUnitPriceTax', 'originalUnitPriceWithoutTax', 'recommend']
      const cellValue = row[column.property]
      if ((cellValue !== undefined && !this.noMergeField.includes(column.property))) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow &&
          ((prevRow[column.property] === cellValue && this.materialField.includes(column.property)) ||
            (row.quotationsMaterialSupplierRelId === prevRow.quotationsMaterialSupplierRelId && !this.materialField.includes(column.property))) &&
          row.projectMaterialId === prevRow.projectMaterialId) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow !== undefined &&
            row.projectMaterialId === nextRow.projectMaterialId &&
            ((nextRow[column.property] === cellValue && this.materialField.includes(column.property)) ||
              (row.quotationsMaterialSupplierRelId === nextRow.quotationsMaterialSupplierRelId && !this.materialField.includes(column.property)))
          ) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    beforeLeave() {
      if (!this.validRequired()) {
        return false
      }
      saveQuotationMaterial({
        quotationMaterialInfos: this.list,
        templateId: Number(this.activeCategory),
        projectId: this.queryParams.projectId
      })
      return true
    },
    changeTemplate() {
      this.getTemplateFields()
      this.getTable()
    },
    getTable() {
      this.loading = true
      getQuotationReplyData({
        ...this.quotationMaterialQueryParams,
        quotationId: this.quotationId,
        templateId: Number(this.activeCategory)
      }).then(res => {
        this.list = res.data.list
        this.total = res.data.total
        this.loading = false
      })
    },
    followUp(id) {
      this.followUpOpen = true
      this.followUpData.quotationIds = [id]
    },
    sendBack(id) {
      this.$message.warning('暂未开发')
    },
    copyUrl(id) {
      copyTokenUrl({
        quotationId: id
      }).then(res => {
        console.log('copyUrl', res)
        if (!res.data) {
          this.$modal.msgError('该询价单暂无链接')
          return
        }
        this.copyMessage = res.data
        this.$nextTick(() => {
          this.$refs.copyDiv.click()
        })
      })
    },
    actionFollowUp() {
      followUp(this.followUpData).then(res => {
        this.$message.success(this.$t('rfq.followUpSuccess'))
        this.followUpOpen = false
      })
    },
    downloadMaterialFiles() {
      downloadMaterialFiles({ quotationId: this.quotationId }).then(res => {
        this.$download.zip(res, this.$t('rfq.materialDrawingszip'))
        this.$message.success(this.$t('order.operationSucceeded'))
      }).catch(() => {
      })
    },
    addQuantity(row, index) {
      this.list.splice(index + 1, 0, {
        ...row,
        id: '',
        quotationQuantityFrom: null,
        quotationQuantityTo: null,
        originalUnitPriceTax: null,
        originalUnitPriceWithoutTax: null
      })
    },
    delQuantity(row, index) {
      const materialList = this.list.filter(item => item.quotationsMaterialSupplierRelId === row.quotationsMaterialSupplierRelId)
      if (materialList.length > 1) {
        this.list.splice(index, 1)
      } else {
        this.$message.error(this.$t('rfq.cannotDeleteAllQuotations'))
      }
    },

    saveQuotation() {
      if (this.validRequired()) {
        saveQuotationMaterial({
          quotationMaterialInfos: this.list,
          templateId: Number(this.activeCategory),
          projectId: this.queryParams.projectId
        }).then(res => {
          this.$message.success(this.$t('common.savedSuccessfully'))
          this.getTable()
          this.refershTemplateTabs()
        })
      }
    },
    returnBack() {
    },
    validRequired() {
      let pass = true
      this.dictTemplateFields.forEach(item => {
        if (item.required) {
          const projectMaterialIds = []
          const isMergeField = this.fullFields.includes(item.field)
          this.list.forEach(row => {
            if (row.originalUnitPriceTax || row.originalUnitPriceWithoutTax) {
              if (isMergeField) {
                // 被合并的字段仅验证第一条
                if (!row[item.field] && !projectMaterialIds.includes(row.projectMaterialId)) {
                  pass = false
                } else if (row[item.field]) {
                  projectMaterialIds.push(row.projectMaterialId)
                }
              } else {
                // eslint-disable-next-line no-empty
                if (!row[item.field]) {
                  pass = false
                }
              }
            }
          })
        }
      })
      if (!pass) {
        this.$message.error(this.$t('rfq.pleaseMaintainTheCurrentMaterialLineInformationCompletely'))
      }
      return pass
    },
    partialCommit() {
      if (this.validRequired()) {
        this.partialCommitLoading = true
        checkMaterialStatusByQuotationId({
          quotationId: this.quotationId
        }).then(res => {
          const data = res.data
          if (!data) {
            const text = this.$t('rfq.rfqAlreadyExistsPleaseDoNotSubmitAgain')
            this.$modal.confirm(text).then(() => {
              this.partialCommitLoading = false
              return false
            }).catch(() => {
              this.partialCommitLoading = false
            })
          } else {
            saveQuotationMaterial({
              quotationMaterialInfos: this.list,
              templateId: Number(this.activeCategory),
              projectId: this.queryParams.projectId
            }).then(res => {
              const returnMsgList = [
                this.$t('rfq.transferToSubmit'),
                this.$t('rfq.doYouWantToContinue')
              ]
              const newDatas = []
              const h = this.$createElement
              for (const i in returnMsgList) {
                newDatas.push(h('p', null, returnMsgList[i]))
              }
              this.$confirm(h('div', null, newDatas), this.$t('rfq.partialSubmission'), {
                confirmButtonText: this.$t('order.determine'),
                cancelButtonText: this.$t('common.cancel'),
                type: 'warning'
              }).then(() => {
                submitPart({
                  quotationId: this.quotationId
                }).then(res => {
                  this.$message.success(this.$t('supplier.submittedSuccessfully'))
                  //addition: close current page if url include supplierId and the current account is external
                  if (this.queryParams.supplierId && this.$store.getters.isExternal === 1) {
                    window.close()
                  }
                  this.partialCommitLoading = false
                  this.emitter.emit('freshStep', this.queryParams.projectId)
                  this.init(this.quotationId)
                })
              })
            }).catch(() => {
              this.partialCommitLoading = false
            })
          }
        })
      }
    },
    submitPart() {
      this.submitPartLoading = true
      submitPart({
        quotationId: this.quotationId
      }).then(res => {
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
        //addition: close current page if url include supplierId and the current account is external
        if (this.queryParams.supplierId && this.$store.getters.isExternal === 1) {
          window.close()
        }
        this.submitPartLoading = false
        this.quotationSubmitLoading = false
        this.submitQuoteVisible = false
        this.emitter.emit('freshStep', this.queryParams.projectId)
        this.init(this.quotationId)
      }).catch(() => {
        this.submitPartLoading = false
        this.quotationSubmitLoading = false
      })
    },
    quotationSubmit() {
      if (this.validRequired()) {
        this.quotationSubmitLoading = true
        checkMaterialStatusByQuotationId({
          quotationId: this.quotationId
        }).then(res => {
          const data = res.data
          if (!data) {
            const text = this.$t('rfq.rfqAlreadyExistsPleaseDoNotSubmitAgain')
            this.$modal.confirm(text).then(() => {
              this.quotationSubmitLoading = false
              return false
            }).catch(() => {
              this.quotationSubmitLoading = false
            })
          } else {
            saveQuotationMaterial({
              quotationMaterialInfos: this.list,
              templateId: Number(this.activeCategory),
              projectId: this.queryParams.projectId
            }).then(res => {
              this.refershTemplateTabs()
              checkMaterialquote({
                quotationId: this.quotationId
              }).then(res => {
                const data = res.data
                if (data.length) {
                  this.submitQuoteVisible = true
                } else {
                  submitQuo({
                    quotationId: this.quotationId
                  }).then(res => {
                    this.$message.success(this.$t('supplier.submittedSuccessfully'))
                    //addition: close current page if url include supplierId and the current account is external
                    if (this.queryParams.supplierId && this.$store.getters.isExternal === 1) {
                      window.close()
                    }
                    this.quotationSubmitLoading = false
                    this.emitter.emit('freshStep', this.queryParams.projectId)
                    this.init(this.quotationId)
                  }).catch(() => {
                    this.quotationSubmitLoading = false
                  })
                }
              }).catch(() => {
                this.quotationSubmitLoading = false
              })
            }).catch(() => {
              this.quotationSubmitLoading = false
            })
          }
        })
      }
    },
    submitQuo() {
      this.submitQuoLoading = true
      submitQuo({
        quotationId: this.quotationId
      }).then(res => {
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
        //addition: close current page if url include supplierId and the current account is external
        if (this.queryParams.supplierId && this.$store.getters.isExternal === 1) {
          window.close()
        }
        this.submitQuoLoading = false
        this.quotationSubmitLoading = false
        this.submitQuoteVisible = false
        this.emitter.emit('freshStep', this.queryParams.projectId)
        this.init(this.quotationId)
      }).catch(() => {
        this.submitQuoLoading = false
        this.quotationSubmitLoading = false
      })
    },
    revoke() {
      const returnMsgList = [
        this.$t('rfq.alreadyQuotationCannotUndo'),
        this.$t('rfq.doYouWantToContinue')
      ]
      const newDatas = []
      const h = this.$createElement
      for (const i in returnMsgList) {
        newDatas.push(h('p', null, returnMsgList[i]))
      }
      this.$confirm(h('div', null, newDatas), this.$t('rfq.revoke'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        checkQuotationCancel({
          quotationId: this.quotationId
        }).then(res => {
          cancelQuo({
            quotationId: this.quotationId
          }).then(res => {
            this.$message.success(this.$t('rfq.cancellationSucceeded'))
            this.init(this.quotationId)
          })
        })
      }).catch(() => {
      })
    },
    importTemplate() {
      quotationTemplate({
        ...this.quotationMaterialQueryParams,
        quotationId: this.quotationId,
        templateId: Number(this.activeCategory)
      }).then(response => {
        this.$download.excel(response, this.$t('rfq.batchQuoteTemplatexls'))
      })
    },
    downloadSupplierQuotation() {
      downloadSupplierQuotation({
        ...this.quotationMaterialQueryParams,
        quotationId: this.quotationId,
        templateId: Number(this.activeCategory)
      }).then(response => {
        this.$download.excel(response, this.selectRow.supplierName+ this.$t('报价.xlsx'))
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createProjectMaterialDetail) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createProjectMaterialDetail.length
      }
      if (data.failureProjectMaterialDetail) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureProjectMaterialDetail).length
        for (const index in data.failureProjectMaterialDetail) {
          text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + this.$t('rfq.lineNumber') + index + '：' + data.failureProjectMaterialDetail[index]
        }
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      // this.$message.success(this.$t('rfq.importSucceeded'))
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getTable()
    },
    submitFileForm() {
      this.uploadData.quotationId = this.quotationId
      this.uploadData.templateId = Number(this.activeCategory)
      this.$refs.upload.submit()
    },
    disabledFields(fields) {
      // 当前选中行的报价状态不等于待报价，那么就不让编辑
      if (this.checkQuoteStatus !== 'to_quote') {
        return true
      }
      const quantity = ['quotationQuantityFrom', 'quotationQuantityTo']
      const orderUnit = ['orderUnit']
      if (quantity.includes(fields)) {
        if (!this.rfqProject.supplierPriceLadder) {
          return true
        }
      }
      if (orderUnit.includes(fields)) {
        if (!this.rfqProject.modifyOrderUnits) {
          return true
        }
      }
      return false
    },
    changeField(field, value) {
      console.log(field)
      // todo 计算价格
    },
    changeTaxRate() {
    },
    onPreview(file) {
      this.downloadFile(file)
    },
    onBuyerPreview(file) {
      this.downloadFile(file)
      this.getBuyerFileList()
    },
    saveQuotationRemark() {
      if (this.purchaseRequirements.remark) {
        saveQuotationRemark({
          id: this.quotationId,
          remark: this.purchaseRequirements.remark
        }).then(res => {
          if (res.data) {
            this.$message.success(this.$t('rfq.updateQuotationNotesSucceeded'))
          }
        })
      }
    },
    resetQuery() {
      this.quotationMaterialQueryParams = {
        materialCode: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getTable()
    },
    /**
     * 发供应商确认价格
     */
    sendTargetPriceFlag() {
      if (this.validRequired()) {
        saveQuotationMaterial({
          quotationMaterialInfos: this.list,
          templateId: Number(this.activeCategory),
          projectId: this.queryParams.projectId
        }).then(res => {
          checkMaterialquote({
            quotationId: this.quotationId
          }).then(res => {
            const data = res.data
            if (data.length) {
              const msg =
                `<div><p>${this.$t('rfq.find')}<span style="color: red">${this.$t('rfq.noQuotedMaterials')}</span>${this.$t('rfq.pleaseReturnAndFillInAllPricesBeforeClickingOnsendSupplierToConfirmPrice')}</p></div>`
              this.$alert(msg, this.$t('rfq.supplierConfirmationOfPrice'), {
                dangerouslyUseHTMLString: true,
                type: 'warning'
              })
            } else {
              updateTargetPriceTag({
                quotationMaterialInfos: this.list,
                templateId: Number(this.activeCategory),
                projectId: this.queryParams.projectId
              }).then(res => {
                this.$message.success(this.$t('rfq.publishedSuccessfully'))
                this.getTable()
              })
            }
          })
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

::v-deep .el-table__body tr.current-row > td.el-table__cell {
  box-shadow: 0 12px 12px -2px rgba(0, 0, 0, 0.1), 0 -10px 10px -5px rgba(0, 0, 0, 0.1);
  color: #ffffff;
  background: #4996b8;

  .el-button {
    color: #ffffff;
  }
}

::v-deep .el-card__header{
  font-weight: 400 !important;
}

::v-deep .el-table .el-dropdown-link {
  color: #606266
}

::v-deep .el-table .current-row .el-dropdown-link {
  color: #ffffff;
}

.fixedBottom {
  position: fixed;
  width: calc(100% - 231px);
  bottom: 20px;
  display: flex;
  justify-content: center;
  margin-top: 40px;
  right: 30px;
  z-index: 999;
}

::v-deep .requiredTitle::before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

.cardSpacing {
  margin-top: 15px;
}

::v-deep .labelTitle {
  font-weight: bold;
  color: black;
  justify-content: flex-end;
}

::v-deep .purchaseRequire {
  width: 120px;
  justify-content: flex-end;
}

::v-deep .el-badge__content {
  line-height: 14px;
}

::v-deep .purchaseRequireNoLabel {
  width: 0;
  justify-content: flex-end;
}

.oneLineMaterialItem {
  width: 100%;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.commonMaterialItem {
  width: 33%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
}

.rfq-create-upload {
  margin: 10px 0
}

.rfq-create {
  margin-top: 15px;
}

.materialItem {
  width: 178px;
}

.cardBefore::before, .cardBefore::after {
  content: "";
  position: absolute;
  left: 5%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
}

.cardBefore::before {
  top: -15px;
  border-bottom: 15px solid #b7bac0; /* 灰色边框 */
}

.cardBefore::after {
  top: -13px;
  border-bottom: 13px solid #ffffff; /* 白色三角形 */
}

.rfqMaterial {
  .mainTab {
    margin-bottom: 15px;
  }
}
.scrollable-content{
  height: 500px;
  overflow-y: auto; /* 启用垂直滚动 */
}

.active-link {
  text-decoration: underline;
}
.tagLink{
  padding-bottom: 10px;
}
</style>
