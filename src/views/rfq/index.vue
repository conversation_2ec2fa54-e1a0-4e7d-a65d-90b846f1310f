<template>
  <div class="rfqHome">
    <StatisticsCard :item-arr="itemArr" />
    <el-button
      v-for="item in getDictDatas(DICT_TYPE.RFQ_BUSINESS_TYPE).filter(v=>v.visible)"
      :key="item.id"
      icon="el-icon-plus"
      size="mini"
      plain
      type="primary"
      @click="createNewRfq(item)"
    >{{ item.name }}</el-button>

    <div>
      <el-tabs v-model="activeName">
        <el-tab-pane :name="'first'" :label="$t('rfq.project')">
          <span slot="label">
            <el-radio v-model="activeName" label="first">{{ $t('rfq.project') }}</el-radio>
          </span>
        </el-tab-pane>
        <el-tab-pane :name="'second'" :label="$t('rfq.inquirySheet')">
          <span slot="label">
            <el-radio v-model="activeName" label="second">{{ $t('rfq.inquirySheet') }}</el-radio>
          </span>
        </el-tab-pane>
        <el-tab-pane :name="'third'" :label="$t('rfq.projectMaterial')">
          <span slot="label">
            <el-radio v-model="activeName" label="third">{{ $t('rfq.projectMaterial') }}</el-radio>
          </span>
        </el-tab-pane>
        <el-tab-pane :name="'fourth'" :label="$t('rfq.approvalForm')">
          <span slot="label">
            <el-radio v-model="activeName" label="fourth">{{ $t('rfq.approvalForm') }}</el-radio>
          </span>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="rfqHome-table">
      <rfqProjectTable v-show="activeName === 'first'" ref="rfqProjectTable" :statistics="statisticsItemArr" />
      <rfqQuotationPerspective v-show="activeName === 'second'" ref="rfqQuotationPerspective" />
      <rfqMaterialTable v-show="activeName === 'third'" ref="rfqMaterialTable" />
      <rfqPriceApprovalTable v-show="activeName === 'fourth'" />
    </div>

  </div>
</template>

<script>
import {getInquiryChart, overview, statisticsCard} from '@/api/rfq/home'
import dayjs from 'dayjs'
import rfqProjectTable from '@/views/rfq/components/rfqProjectTable'
import rfqQuotationPerspective from '@/views/rfq/components/rfqQuotationPerspective'
import rfqPriceApprovalTable from '@/views/rfq/components/rfqPriceApprovalTable'
import rfqMaterialTable from '@/views/rfq/components/rfqMaterialTable'
import event from '@/views/dashboard/mixins/event'
import {DICT_TYPE, getDictDatas} from "@/utils/dict";

export default {
  name: 'Rfqindex',
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  components: {
    rfqProjectTable,
    rfqQuotationPerspective,
    rfqMaterialTable,
    rfqPriceApprovalTable
  },
  mixins: [event],
  data() {
    return {
      itemArr: [],
      statisticsItemArr:[],
      showHead: true,
      activeName: 'first',
      chartData: null
    }
  },
  mounted() {
    this.getRfqChart()
    this.getStatisticsCard()
  },
  methods: {
    getDictDatas,
    getStatisticsCard() {
      overview().then(res => {
        this.itemArr = [
          { label: this.$t('RFQ项目关闭平均时长'), value: res.data.closeProjectAvgQuotedDays,unit: this.$t('日')},
          { label: this.$t('询价单关闭平均时长'), value: res.data.closeQuotedAvgDays,unit: this.$t('日') },
          { label: this.$t('RFQ完成率'), value: res.data.completionRate ,unit: "%"},
          { label: this.$t('RFQ授予率'), value: res.data.rfqAwardRate,unit: "%" },
          { label: this.$t('报价竞争性比率'), value: res.data.competitivenessRate,unit:"%"}
        ]
        this.statisticsItemArr = [
          { label: this.$t('YTD RFQ 项目'), value: res.data.projectCount,status:['processing', 'new', 'completed']},
          { label: this.$t('RFQ 新建项目'), value: res.data.projectNewCount,status: ["new"] },
          { label: this.$t('RFQ 项目进行中'), value: res.data.projectProcessCount,status: ["processing"]},
          { label:this.$t('RFQ 完成询价单'), value: res.data.quoteCount,status: ["completed"]}
        ]
      }).catch(_ => {
      })
    },
    getRfqChart() {
      getInquiryChart({
        year: dayjs().year()
      }).then(res => {
        this.chartData = res.data
      })
    },

    createNewRfq(item) {
      this.$router.push(`/rfq/processHome/0?type=${item.id}&code=${item.code}&id=`)
    }
  }
}
</script>

<style lang="scss" scoped>

.rfqHome {
  padding: 15px 20px;

  &-head {
    .quick-item {
      cursor: pointer;
      text-align: center;
      flex: 0 0 50%;
      padding: 5px;
    }

    .quick-item-deal {
      background: #f2fbff;
      padding: 20px;
      text-align: left;
      //display: flex;
      //justify-content: center;
      //flex-direction: column;
      //align-items: center;
      border: 1px solid transparent;
      height: 155px;

    }
    .quick-item-deal:hover{
      box-shadow: 0 8px 16px rgb(27 154 238 / 20%);
      background: #dcf3ff;
      border-color: #87d2ff;

    }
    .quick-item-deal:hover >.quick-item-describe{
      opacity: 1;

    }
    .quick-item-describe{
      opacity: 0;
    }

    height: 200px;
    display: flex;
  }

  &-search {
    margin: 20px 0
  }
}

.scale-leave-active {
  -webkit-animation: scale-down-tr 0.4s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  animation: scale-down-tr 0.4s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}

.scale-enter-active {
  -webkit-animation: scale-up-tr 0.4s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
  animation: scale-up-tr 0.4s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
}

@-webkit-keyframes scale-up-tr {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@keyframes scale-up-tr {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@-webkit-keyframes scale-down-tr {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@keyframes scale-down-tr {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

</style>
