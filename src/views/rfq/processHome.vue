<template>
  <div class="processHome">
    <!--   审批页隐藏步骤条-->
    <steps :steps="steps" :active="active" @changeStep="changeStep" />

    <!--    完整询价-->
    <div v-if="steps.length ===4">
      <create
        v-if="active===0&&steps[0].projectId!==null"
        :steps="steps[0]"
        @freshStep="getStep"
      />
      <supplierQuote
        v-if="active===1&&steps[1].projectId!==null"
        :steps="steps[1]"

        @freshStep="getStep"
      />
      <priceRecommendationView
        v-if="active===2&&steps[2].projectId!==null"
        :steps="steps[2]"

        @freshStep="getStep"
      />
      <approval
        v-if="active===3&&steps[3].projectId!==null"
        :steps="steps[3]"
        @freshStep="getStep"
      />

    </div>
    <!--    价格录入-->
    <div v-else>
      <priceEntry
        v-if="active===0&&steps[0].projectId!==null"
        :steps="steps[0]"
        @freshStep="getStep"
      />
      <approval
        v-if="active===1&&steps[1].projectId!==null"
        :steps="steps[1]"
        @freshStep="getStep"
      />
    </div>

  </div>
</template>

<script>
import steps from '@/views/rfq/components/steps'
import { getRfqStep } from '@/api/rfq/home'

export default {
  name: 'Processhome/:projectid',
  components: {
    steps,
    priceEntry: () => import('@/views/rfq/components/priceEntryCreate.vue'),
    priceEntryApproval: () => import('@/views/rfq/components/priceEntryApproval.vue'),
    create: () => import('@/views/rfq/components/create'),
    supplierQuote: () => import('@/views/rfq/components/supplierQuote'),
    priceRecommendationView: () => import('@/views/rfq/components/priceRecommendationView'),
    approval: () => import('@/views/rfq/components/approval')
  },
  data() {
    return {
      steps: [{
        projectId: null
      },
      {
        projectId: null
      },
      {
        projectId: null
      },
      {
        projectId: null
      }],
      active: 0
    }
  },
  mounted() {
    this.emitter.on('freshStep', id => {
      this.getStep(id)
    })
    this.getStep()
    if (this.$route.query.active) {
      this.active = Number(this.$route.query.active)
    }
  },
  activated() {
    this.emitter.on('freshStep', id => {
      this.getStep(id)
    })
    this.getStep()
    if (this.$route.query.active) {
      this.active = Number(this.$route.query.active)
    }
    // if (this.$route.query.approvalId) {
    //   // 审批页面进入入口
    //   if (!this.active) {
    //     this.active = 3
    //   }
    // }
  },
  beforeDestroy() {
    this.emitter.off('freshStep')
  },
  methods: {
    getStep(id) {
      const projectId = this.$route.query.id || id
      const code = this.$route.query.code
      // if (projectId) {
      getRfqStep({ projectId, code }).then(res => {
        if (res.data[0].projectId === null) {
          res.data[0].projectId = ''
        }
        this.steps = res.data
      })
      // }
    },
    changeStep(index) {
      if (this.steps.at(index).status !== 'not_processed') {
        this.active = index
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.processHome {
  padding: 15px 20px;
}
</style>
