<template>
  <div class="supplierHome">

    <el-radio-group v-model="activeName" style="margin-bottom: 15px" @change="handleClick">
      <el-radio :label="'first'"> {{ $t('rfq.inquirySheet') }}</el-radio>
      <el-radio :label="'second'"> {{ $t('rfq.material') }}</el-radio>
    </el-radio-group>
    <div v-if="activeName === 'first'">
      <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="189px" size="small">
        <el-form-item :label="$t('rfq.rfqNo')" class="searchItem" prop="deliveryNoteNo">
          <el-input
            v-model="queryParams.quotationsNo"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('common.sourcing')" class="searchItem" prop="sourcingId">
          <el-select v-model="queryParams.sourcing" class="searchValue" clearable filterable multiple>
            <el-option
              v-for="dict in sourcingSources"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('rfq.documentSource')" class="searchItem" prop="factoryIds">
          <el-select v-model="queryParams.documentSource" class="searchValue">
            <el-option
              label=""
              value=""
            >{{ $t('order.whole') }}
            </el-option>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_SOURCE_OF_INQUIRY)"
              :key="dict.id"
              :label="dict.label"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('order.timeType')" class="searchItem" prop="timeType">

          <el-select v-model="queryParams.timeType" class="searchValue" clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.RFQ_QUOTATION_PERSPECTIVE_DATE_TYPE)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="searchItem" label=" " prop="deliveryNoteNo">

          <el-date-picker
            v-model="queryParams.time"
            :end-placeholder="$t('common.endDate')"
            :range-separator="$t('order.to')"
            :start-placeholder="$t('common.startDate')"
            class="searchValue"
            type="daterange"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item :label="$t('rfq.rfqStatus')" class="searchItem" prop="factoryIds">
          <el-select v-model="queryParams.quotationStatus" class="searchValue">
            <el-option
              label=""
              value=""
            >{{ $t('order.whole') }}
            </el-option>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_QUOTATION_STATUS)"
              :key="dict.id"
              :label="dict.label"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-option>
          </el-select>
        </el-form-item>
        <div style="text-align: center">
          <el-button plain icon="el-icon-search" size="mini" type="primary" @click="queryParams.pageNo = 1;getList();">{{
            $t('common.search')
          }}
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="handleClick">{{ $t('common.reset') }}</el-button>

        </div>

      </el-form>
      <vxe-grid
        ref="rfqSupplierQuoteGrid"

        :data="list"
        :loading="loading"
        style="margin-top: 20px"
        v-bind="girdOption"
      >
        <template #quotationsNo="{row}">
          <copy-button
            @click="showQuote(row.supplierId,row.quotationId,row.projectId,row.quotationsNo)"
          >
            {{ row.quotationsNo }}
          </copy-button>
        </template>
        <template #quotationStatus="{row}">
          <dict-tag :type="DICT_TYPE.RFQ_QUOTATION_STATUS" :value="row.quotationStatus" />
        </template>

        <template #documentSource="{row}">
          <dict-tag :type="DICT_TYPE.RFQ_SOURCE_OF_INQUIRY" :value="row.documentSource" />
        </template>
        <template #creator="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.creator" />
        </template>
        <template #transactor="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.transactor" />
        </template>
        <template #status="{row}">
          <dict-tag :type="DICT_TYPE.RFQ_MATERIAL_STATUS" :value="row.status" />

        </template>
        <template #quotationsStatus="{row}">
          <dict-tag :type="DICT_TYPE.RFQ_QUOTATION_STATUS" :value="row.quotationsStatus" />

        </template>

      </vxe-grid>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getList"
      />
    </div>

    <div v-if="activeName === 'second'">
      <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="189px" size="small">
        <el-form-item :label="$t('rfq.rfqNo')" class="searchItem" prop="deliveryNoteNo">
          <el-input
            v-model="queryParams.quotationsNo	"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('material.materialCode')" class="searchItem" prop="sourcingId">

          <el-input
            v-model="queryParams.materialCode"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('material.manufacturer')" class="searchItem" prop="sourcingId">

          <el-input
            v-model="queryParams.mfg"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('material.manufacturersPartNumber')" class="searchItem" prop="sourcingId">

          <el-input
            v-model="queryParams.mpn"
            :placeholder="$t('common.pleaseEnter')"
            class="searchValue"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('common.sourcing')" class="searchItem" prop="sourcingId">
          <el-select v-model="queryParams.sourcing" class="searchValue" clearable filterable multiple>
            <el-option
              v-for="dict in sourcingSources"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('rfq.quoteStatus')" class="searchItem" prop="factoryIds">
          <el-select v-model="queryParams.quotationStatus" class="searchValue">
            <el-option
              label=""
              value=""
            >{{ $t('order.whole') }}
            </el-option>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_QUOTATION_MATERIAL_STATUS)"
              :key="dict.id"
              :label="dict.label"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-option>
          </el-select>
        </el-form-item>
        <div style="text-align: center;">
          <el-button plain icon="el-icon-search" size="mini" type="primary" @click="queryParams.pageNo = 1;getTable();">{{
            $t('common.search')
          }}
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="handleClick">{{ $t('common.reset') }}</el-button>

        </div>

      </el-form>
      <vxe-grid
        ref="rfqSupplierMaterialGrid"
        :data="list"
        :loading="loading"
        style="margin-top: 20px"
        v-bind="girdMaterialOption"
      >
        <template #itemQuotationsNo="{row}">
          <copy-button
            @click="showQuote(row.supplierId,row.quotationId,row.projectId,row.quotationsNo)"
          >
            {{ row.quotationsNo }}
          </copy-button>
        </template>
        <template #materialCode="{row}">
          <copy-button
            @click="showMaterialDetail(row.projectMaterialId)"
          >
            {{ row.materialCode }}
          </copy-button>
        </template>
        <template #basicUnit="{row}">
          <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.basicUnit" />

        </template>
        <template #categoryId="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
        </template>
        <template #creator="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.creator" />
        </template>
        <template #transactor="{row}">
          <dict-tag :type="DICT_TYPE.MATERIAL_PURCHASE_TYPE" :value="row.priceCategory" />
        </template>
        <template #quotationsStatus="{row}">
          <dict-tag :type="DICT_TYPE.RFQ_QUOTATION_MATERIAL_STATUS" :value="row.quotationsStatus" />
        </template>

      </vxe-grid>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getTable"
      />
    </div>
    <rfqMaterial
      ref="rfqMaterial"
      :material-visible.sync="materialVisible"
      :rfq-project-status="null"
      :rfq-project-id="true"
    />
  </div>
</template>

<script>
import { pageQuotationSupplier, pageQuotationSupplierMaterial } from '@/api/rfq/home'
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import event from '@/views/dashboard/mixins/event'

export default {
  name: 'Supplierindex',
  components: {
    rfqMaterial: () => import('@/views/rfq/components/material')
  },
  mixins: [event],
  data() {
    return {
      sourcingSources: [],
      activeName: 'first',
      list: [],
      total: 0,
      materialVisible: false,
      categoryList: [],
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'rfqSupplierQuoteGrid',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        checkboxConfig: {
          reserve: true
        },
        rowConfig: {
          keyField: 'quotationId',
          isHover: true
        },
        columns: [
          {
            title: this.$t('rfq.rfqNo'), field: 'quotationsNo', slots: {
              default: 'quotationsNo'
            }, visible: true
          },
          {
            title: this.$t('common.creationDate'), field: 'createTime',
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true
          },
          {
            title: this.$t('rfq.requestedResponseDate'),
            field: 'dateRequested',
            visible: true,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''

          },
          {
            title: this.$t('rfq.quotationResponseDate'),
            field: 'quoteReplyDate',
            visible: true,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''

          },
          { title: this.$t('common.sourcing'), field: 'sourcingNames', visible: true },
          {
            title: this.$t('rfq.rfqStatus'),
            field: 'quotationStatus',
            visible: true,
            slots: { default: 'quotationStatus' }

          },
          {
            title: this.$t('rfq.purchasersQuotationRequirements'),
            field: 'quotationRequest',
            visible: true

          }
        ],

        sortConfig: {
          remote: true
        }
        // toolbarConfig: {
        //   slots: {
        //     // 自定义工具栏模板
        //     buttons: 'toolbar_buttons'
        //   }
        // }
      },
      girdMaterialOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'rfqSupplierMaterialGrid',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        checkboxConfig: {
          reserve: true
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          {
            title: this.$t('rfq.rfqNo'), field: 'quotationsNo', slots: {
              default: 'itemQuotationsNo'
            }, visible: true
          },
          { title: this.$t('common.sourcing'), field: 'sourcingNames', visible: true },
          { title: this.$t('material.materialCode'), field: 'materialCode', slots: { default: 'materialCode' }, visible: true },
          {
            title: this.$t('material.materialDescription'),
            field: 'materialDescription',
            visible: true

          },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true },
          { title: this.$t('material.specificationAndModel'), field: 'specifications', visible: true },
          {
            title: this.$t('material.category'),
            slots: { default: 'categoryId' },

            field: 'categoryId', visible: true
          },
          {
            title: this.$t('material.basicUnit'),
            slots: { default: 'basicUnit' },

            field: 'basicUnit', visible: true
          },
          { title: this.$t('material.revision'), field: 'version', visible: true },
          { title: this.$t('material.drawingVersion'), field: 'drawingVersion', visible: true },
          {
            title: this.$t('rfq.priceCategory'),
            slots: { default: 'transactor' },

            field: 'priceCategory', visible: true
          },
          {
            title: this.$t('rfq.quoteStatus'),
            slots: { default: 'quotationsStatus' },

            field: 'status', visible: true
          }
        ],

        sortConfig: {
          remote: true
        }
        // toolbarConfig: {
        //   slots: {
        //     // 自定义工具栏模板
        //     buttons: 'toolbar_buttons'
        //   }
        // }
      },

      queryParams: {
        beginDate: '',
        documentSource: '',
        endDate: '',
        materialCode: '',
        materialStatus: '',
        mfg: '',
        mpn: '',
        pageNo: 1,
        pageSize: 10,
        quotationStatus: '',
        quotationsNo: '',
        sourcingNames: '',
        timeType: ''
      },
      loading: false
    }
  },
  mounted() {
    this.getList()
    this.getCategories()
    this.getSourcingSourcess()
  },
  methods: {
    getList() {
      this.queryParams.beginDate = undefined
      this.queryParams.endDate = undefined
      if (this.queryParams.time) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      pageQuotationSupplier(this.queryParams).then(res => {
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    // 项目寻源采购的数据源组装
    getSourcingSourcess() {
      this.sourcingSources.push(...getDictDatas(DICT_TYPE.COMMON_USERS, 0))
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    handleClick() {
      this.queryParams = {
        beginDate: '',
        documentSource: '',
        endDate: '',
        materialCode: '',
        materialStatus: '',
        mfg: '',
        mpn: '',
        pageNo: 1,
        pageSize: 10,
        quotationStatus: '',
        quotationsNo: '',
        sourcingNames: '',
        timeType: ''
      }
      if (this.activeName === 'first') {
        this.getList()
      } else {
        this.getTable()
      }
    },

    getTable() {
      if (this.queryParams.time) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + ' 00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + ' 23:59:59' : undefined
      }
      pageQuotationSupplierMaterial(this.queryParams).then(res => {
        this.list = res.data.list
        this.total = res.data.total
      })
    },

    closeMaterial(close) {
      close()
    },
    showQuote(supplierId, quotationId, projectId, quotationsNo) {
      this.$router.push(`/rfq/supplierQuote/${quotationsNo}?headerSupplierId=${supplierId}&quotationId=${quotationId}&projectId=${projectId}`)
    },
    showMaterialDetail(id) {
      this.materialVisible = true
      this.$refs.rfqMaterial.showMaterialDetail(id)
    }
  }
}
</script>
<style lang="scss" scoped>
.supplierHome {
  padding: 25px 20px;
}

.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 189px);
  }
}

.searchValue {
  width: 95%;
}

.searchTimeItem {
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 82px);
  }
}

.commonMaterialItem {
  width: 33%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
}

.rfq-create-upload {
  margin: 10px 0
}

.rfq-create {
  margin-top: 15px;
}

.materialItem {
  width: 178px;
}

.rfqMaterial {
  .mainTab {
    margin-bottom: 15px;
  }
}
</style>
