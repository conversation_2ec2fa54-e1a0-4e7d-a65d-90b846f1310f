<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('material.category')" prop="categoryName">
        <el-input
          v-model="queryParams.categoryName"
          :placeholder="$t('rfq.pleaseEnterCategoryCodename')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('common.status')" clearable>
          <el-option
            v-for="dict in statusDictDatas"
            :key="parseInt(dict.value)"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('rfq.quoteTemplate')" prop="templateId">
        <el-select v-model="queryParams.templateId" class="searchValue">
          <el-option
            v-for="item in templateList"
            :key="item.id"
            :label="item.templateName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['rfq:quotations-template-category-rel:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd()"
        >{{ $t('rfq.templateConfiguration') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table
      v-if="refreshTable"
      ref="multipleTable"
      v-loading="loading"
      :data="list"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      row-key="id"
      @select="handleSelect"
      @selection-change="handleSelectionChange"
      @select-all="handleSelectAll"
    >
      <el-table-column
        type="selection"
        width="30"
      />
      <el-table-column :label="$t('common.categoryCode')" align="left" prop="code" />
      <el-table-column :label="$t('common.name')" align="center" prop="name" />
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('rfq.templateName')" align="center" prop="templateName" />
    </el-table>

    <!-- 寻源采购 -->
    <el-dialog :title="$t('rfq.templateConfiguration')" :visible.sync="open" append-to-body width="400px">
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item :label="$t('rfq.templateName')" prop="templateId">
          <el-select v-model="form.templateId" class="searchValue" filterable>
            <el-option
              v-for="item in templateList"
              :key="item.id"
              :label="item.templateName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import {
  assignTemplateCategories,
  getQuotationsTemplateCategoryRelList,
  getQuotationsTemplateList
} from '@/api/rfq/templateCategory'
import store from '@/store'

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Categories',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 品类树选项
      categoryOptions: [],
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 品类列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        categoryName: null,
        templateId: null,
        status: null,
        pageNo: 1,
        locale: store.getters.language
      },
      // 表单参数
      form: {
        templateId: null
      },
      templateList: [],
      // 选中数组值
      multipleSelection: [],
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS)
    }
  },
  created() {
    this.getTemplateList()
  },
  methods: {
    handleSelectAll() {
      const isAllSelected = this.$refs.multipleTable.store.states.isAllSelected
      const _handleSelectAll = data => {
        data.forEach(item => {
          item.select
          this.$refs.multipleTable.toggleRowSelection(item, isAllSelected)
          _handleSelectAll(item.children || [])
        })
      }
      _handleSelectAll(this.list)
    },
    handleSelect(selection, current) {
      // 判断selection中是否存在current,若是存在那么就代表是被勾选上了,若是不存在代表是取消勾选了
      const isChecked = !!selection.find(item => item.id === current.id)
      // 如果当前项被取消勾选
      if (!isChecked) {
        // 那么其所有的祖先也应该被取消勾选
        this.uncheckedParents(selection, current)
        // 那么其所有的后代也应该被取消勾选
        this.toggleCheckedChildrens(selection, current, false)
      } else {
        // 如果当前项被勾选
        // 那么若同一组的元素都被勾选了,那么父元素将也被勾选,依次往上类推
        this.checkedParents(selection)
        // 那么其所有的后代都要被勾选
        this.toggleCheckedChildrens(selection, current, true)
      }
    },
    uncheckedParents(selection, item) {
      const _uncheckedParents = data => {
        return data.find(element => {
          if (element.id === item.id) {
            return true
          } else if (_uncheckedParents(element.children || [])) {
            this.$refs.multipleTable.toggleRowSelection(element, false)
            for (let i = selection.length - 1; i >= 0; i--) {
              if (selection[i].id === element.id) {
                selection.splice(i, 1)
                break
              }
            }
            return true
          } else {
            return false
          }
        })
      }
      _uncheckedParents(this.list)
    },
    toggleCheckedChildrens(selection, item, isChecked) {
      const _toggleCheckedChildrens = data => {
        data.find(element => {
          this.$refs.multipleTable.toggleRowSelection(element, isChecked)
          if (isChecked && !selection.find(item => item.id === element.id)) {
            selection.push(element)
          } else if (!isChecked && selection.find(item => item.id === element.id)) {
            for (let i = selection.length - 1; i >= 0; i--) {
              if (selection[i].id === element.id) {
                selection.splice(i, 1)
                break
              }
            }
          }
          _toggleCheckedChildrens(element.children || [])
        })
      }
      _toggleCheckedChildrens(item.children || [])
    },
    checkedParents(selection) {
      const _checkedParents = element => {
        const children = element.children
        if (children && children.length) {
          const allChildrenChecked = children.every(child => {
            return _checkedParents(child)
          })
          if (allChildrenChecked) {
            this.$refs.multipleTable.toggleRowSelection(element, true)
            if (!selection.find(item => item.id === element.id)) {
              selection.push(element)
            }
          }
        }
        return selection.find(item => item.id === element.id)
      }
      this.list.forEach(element => {
        _checkedParents(element)
      })
    },
    /**
     * 选择框
     * @param val
     */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /**
     * 获取人员列表
     */
    getTemplateList() {
      getQuotationsTemplateList().then(response => {
        this.templateList = []
        this.templateList.push(...response.data)
        this.getList()
      })
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getQuotationsTemplateCategoryRelList({ ...this.queryParams }).then(response => {
        this.list = this.handleTree(response.data, 'id')
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 转换品类数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    /** 表单重置 */
    reset() {
      this.resetForm('form')
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false
      this.isExpandAll = !this.isExpandAll
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      if (this.multipleSelection == null || this.multipleSelection.length === 0) {
        this.$modal.msgError(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.reset()
      this.open = true
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        this.form.categoryIds = this.multipleSelection.map(i => {
          return i.id
        })
        assignTemplateCategories(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          this.open = false
          this.getList()
        })
        return
      })
    }
  }
}
</script>
