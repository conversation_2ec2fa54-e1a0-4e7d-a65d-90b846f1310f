<template>
  <div class="app-container">
    <el-form
      label-position="left"
      label-width="300px"
      inline
    >
      <el-form-item :label="$t('rfq.attributeNamechineseattributeNameenglishencodingtranslationUniqueKey')">
        <el-input v-model="search"/>
      </el-form-item>
      <el-form-item label=" " label-width="0px">
        <el-button plain type="primary" @click="getList">{{ $t('common.reload') }}</el-button>
        <el-button plain type="primary" @click="handleAdd">{{ $t('common.add') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="fieldDataSourceComputed" style="margin-top: 15px">
      <el-table-column :label="$t('rfq.attributeNamemiddle')" prop="fieldName"/>
      <el-table-column :label="$t('rfq.attributeNameenglish')" prop="fieldNameEn"/>
      <el-table-column :label="$t('system.code')" prop="fieldCode"/>
      <el-table-column :label="$t('system.type')" prop="fieldType"/>
      <el-table-column :label="$t('rfq.sourceTable')" prop="source">
        <template slot-scope="scope">
          {{ sourceDict.find(item => item.value === scope.row.source).label }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('rfq.dictionaries')" prop="fieldDictType"/>
      <el-table-column :label="$t('rfq.translationUniqueKey')" prop="fieldNameLocalId"/>
      <el-table-column :label="$t('common.operate')" width="100">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="handleAdd(scope.row)"
          >{{ $t('common.edit') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :title="$t('rfq.attributeSettings')"
      :visible.sync="openFlag"
      width="400px"
    >
      <el-form
        :model="editRow"
        label-position="left"
        label-width="100px"
      >
        <el-form-item :label="$t('rfq.attributeNamemiddle')" prop="fieldName">
          <el-input v-model="editRow.fieldName"/>
        </el-form-item>
        <el-form-item :label="$t('rfq.attributeNameenglish')" prop="fieldNameEn">
          <el-input v-model="editRow.fieldNameEn"/>
        </el-form-item>
        <el-form-item :label="$t('system.code')" prop="fieldCode">
          <el-input v-model="editRow.fieldCode"/>
        </el-form-item>
        <el-form-item :label="$t('system.type')" prop="fieldType">
          <el-select v-model="editRow.fieldType" placeholder="请选择" style="width: 100%">
            <el-option
              v-for="item in fieldTypeDict"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('rfq.sourceTable')" prop="source">
          <el-select v-model="editRow.source" placeholder="请选择" style="width: 100%">
            <el-option
              v-for="item in sourceDict"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('rfq.dictionaries')" prop="fieldDictType">
          <el-input v-model="editRow.fieldDictType"/>
        </el-form-item>
        <el-form-item :label="$t('rfq.translationUniqueKey')" prop="fieldNameLocalId">
          <el-input disabled v-model="editRow.fieldNameLocalId"/>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button :loading="loading" type="primary" @click="submit">{{ $t('common.submit') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getFieldListAll, saveOrEditField } from '@/api/rfq/templateHandler'

export default {
  name: 'TemplateFiledHandler',
  data() {
    return {
      loading: false,
      search: '',
      editRow: {},
      fieldDataSource: [],
      openFlag: false,
      sourceDict: [
        { label: 'rfq_project_materials', value: 1 },
        { label: 'rfq_material_suppliers_rel', value: 2 },
        { label: 'rfq_quotations_material_supplier_rel', value: 3 },
        { label: 'rfq_quotations_material_supplier_quantity_ladder', value: 4 },
        { label: 'rfq_quotations_template_fields_value', value: 5 },
        { label: 'rfq_approval_material_supplier_rel', value: 6 }
      ],
      fieldTypeDict: [
        { label: 'BigDecimal', value: 'BigDecimal' },
        { label: 'Boolean', value: 'Boolean' },
        { label: 'Date', value: 'Date' },
        { label: 'Integer', value: 'Integer' },
        { label: 'Long', value: 'Long' },
        { label: 'String', value: 'String' }
      ],
      // 0按物料合并，1按询价物料合并，2不合并
      mergeDict: [
        { label: this.$t('rfq.mergeByMaterial'), value: 0 },
        { label: this.$t('rfq.mergeByInquiryMaterials'), value: 1 },
        { label: this.$t('rfq.doNotMerge'), value: 2 }
      ]

    }
  },
  created() {

  },
  mounted() {
    this.getList()
  },
  computed: {
    fieldDataSourceComputed() {
      return this.fieldDataSource.filter(item => item.fieldName.includes(this.search) || item.fieldNameEn.includes(this.search) || item.fieldCode.includes(this.search) || item.fieldNameLocalId?.includes(this.search))
    }
  },
  methods: {
    /** 查询列表 */
    getList() {
      getFieldListAll().then(response => {
        this.fieldDataSource = response.data
      })
    },
    handleAdd(data) {
      if (data) {
        this.editRow = { ...data }
      } else {
        this.editRow = {}
      }
      this.openFlag = true
    },
    submit() {
      this.loading = true
      saveOrEditField(this.editRow).then(response => {
        this.getList()
        this.openFlag = false
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
