<template>
  <div class="app-container">

    <el-button plain type="primary" @click="getList">{{ $t('common.reload') }}</el-button>
    <el-button plain type="primary" @click="handleAdd()">{{ $t('common.add') }}</el-button>

    <el-table :data="list" style="margin-top: 15px">
      <el-table-column :label="$t('rfq.templateNamemiddle')" prop="templateName" />
      <el-table-column :label="$t('rfq.templateNameenglish')" prop="templateNameEn" />
      <el-table-column :label="$t('system.code')" prop="templateCode" />
      <el-table-column :label="$t('common.operate')" width="100">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="handleAdd(scope.row)"
          >{{ $t('common.edit') }}
          </el-button>
          <el-button
            type="text"
            size="mini"
            @click="handleDelete(scope.row)"
          >{{ !scope.row.enable ? $t('common.disable') : $t('common.enable') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="$t('rfq.templateSettings')"
      :visible.sync="openFlag"
      width="1400px"
    >
      <el-form
        :model="editRow"
        label-position="right"
        label-width="100px"
        inline
      >
        <el-form-item :label="$t('rfq.templateNamemiddle')" prop="templateName">
          <el-input v-model="editRow.templateName	" />
        </el-form-item>
        <el-form-item :label="$t('rfq.templateNameenglish')" prop="templateNameEn">
          <el-input v-model="editRow.templateNameEn	" />
        </el-form-item>
        <el-form-item :label="$t('system.code')" prop="templateCode">
          <el-input v-model="editRow.templateCode	" />
        </el-form-item>
      </el-form>
      <el-button style="margin-bottom: 15px" plain type="primary" @click="handleAddFiled(-1)">{{
        $t('supplier.newLine')
      }}
      </el-button>
      <el-table
        ref="xTable1"
        border
        row-key="id"
        :data="editRow.filedList"
      >
        <el-table-column width="50" align="center">
          <template #default>
            <span class="drag-btn">
              <i class="vxe-icon--menu" />
            </span>
          </template>
        </el-table-column>
        <el-table-column width="450" :label="$t('rfq.attribute')" show-overflow-tooltip>
          <template #default="{ row }">
            <el-select v-model="row.fieldId" placeholder="请选择" style="width: 100%" filterable>
              <el-option
                v-for="item in fieldDataSource"
                :key="item.id"
                :label="item.fieldName+' - '+item.fieldCode"
                :value="item.id"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column width="80" :label="$t('rfq.required')">
          <template #default="{ row }">
            <el-switch v-model="row.required" />
          </template>
        </el-table-column>
        <el-table-column width="80" :label="$t('rfq.hide')">
          <template #default="{ row }">
            <el-switch v-model="row.hidden" />
          </template>
        </el-table-column>
        <el-table-column width="80" :label="$t('rfq.hideExcel')">
          <template #default="{ row }">
            <el-switch v-model="row.excelHidden" />
          </template>
        </el-table-column>
        <el-table-column width="80" :label="$t('必填依赖')">
          <template #default="{ row }">
            <el-input v-model="row.dependency" />
          </template>
        </el-table-column>
        <el-table-column width="80" :label="$t('rfq.allowModification')">
          <template #default="{ row }">
            <el-switch v-model="row.modify" />
          </template>
        </el-table-column>
        <el-table-column width="170" :label="$t('rfq.merge')">
          <template #default="{ row }">
            <el-select v-model="row.merge" placeholder="请选择" style="width: 100%">
              <el-option :label="$t('rfq.mergeByMaterial')" :value="0" />
              <el-option :label="$t('rfq.mergeByInquiryMaterials')" :value="1" />
              <el-option :label="$t('rfq.doNotMerge')" :value="2" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.defaultValue')">
          <template #default="{ row }">
            <el-input v-model="row.defaultValue" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.operate')" width="140">
          <template #default="{ $index,row }">
            <el-button
              type="text"
              size="mini"
              @click="handleAddFiled($index)"
            >{{ $t('rfq.insertDownwards') }}
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="deleteFiled($index)"
            >{{ $t('common.del') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button :loading="loading" type="primary" @click="submit">{{ $t('common.submit') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, getFieldListAll, saveOrEdit, deleteTemp } from '@/api/rfq/templateHandler'
import Sortable from 'sortablejs'
import { generateUUID } from '../../../utils/ruoyi'

export default {
  name: 'TemplateHandler',
  data() {
    return {
      loading: false,
      list: [],
      editRow: {
        id: '',
        templateName: '',
        templateNameEn: '',
        templateCode: '',
        nameLocalId: '',
        filedList: []
      },
      fieldDataSource: [],
      openFlag: false
    }
  },
  created() {

  },
  mounted() {
    this.getList()
    getFieldListAll().then(response => {
      this.fieldDataSource = response.data
    })
  },
  methods: {
    /** 查询列表 */
    getList() {
      // 执行查询
      getList().then(response => {
        this.list = response.data
      })
    },
    handleAdd(data) {
      if (data) {
        this.editRow = { ...data, filedList: [...data.filedList] }
      } else {
        this.editRow = {
          id: '',
          templateName: '',
          templateNameEn: '',
          templateCode: '',
          nameLocalId: '',
          filedList: []
        }
      }
      this.openFlag = true
      this.rowDrop()
    },
    handleDelete(data) {
      this.$confirm(this.$t('rfq.areYouSureToDisableenableThisTemplate')).then(() => {
        deleteTemp(data).then(response => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.getList()
        })
      })
    },
    handleAddFiled(index) {
      // 向下插入一行
      this.editRow.filedList.splice(index + 1, 0, {
        //id: generateUUID(),
        fieldId: null,
        required: false,
        hidden: false,
        excelHidden: false,
        modify: false
      })
    },
    deleteFiled(index) {
      this.editRow.filedList.splice(index, 1)
    },
    submit() {
      // 属性不能为空
      if (this.editRow.filedList.some(item => !item.fieldId)) {
        this.$message.error(this.$t('rfq.attributeCannotBeEmpty'))
        return
      }
      // 重新排序sort
      this.editRow.filedList.forEach((item, index) => {
        item.sort = index
      })
      this.loading = true
      saveOrEdit(this.editRow).then(response => {
        this.getList()
        this.openFlag = false
      }).finally(() => {
        this.loading = false
      })
    },
    rowDrop() {
      this.$nextTick(() => {
        const xTable = this.$refs.xTable1
        Sortable.create(xTable.$el.querySelector('.el-table__body-wrapper > table > tbody'), {
          handle: '.drag-btn',
          onEnd: (x) => {
            const targetRow = this.editRow.filedList.splice(x.oldIndex, 1)[0]
            this.editRow.filedList.splice(x.newIndex, 0, targetRow)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
