<template>
  <div class="overview-container">
    <!-- 统计卡片 -->
    <StatisticsCard :item-arr="itemArr" />
    
    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>{{ $t('scar.statusDistribution') }}</span>
            </div>
            <div class="chart-container" ref="statusChart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>{{ $t('scar.monthlyTrend') }}</span>
            </div>
            <div class="chart-container" ref="trendChart"></div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>{{ $t('scar.supplierRanking') }}</span>
            </div>
            <div class="chart-container" ref="supplierChart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>{{ $t('scar.categoryDistribution') }}</span>
            </div>
            <div class="chart-container" ref="categoryChart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-card>
        <div slot="header" class="clearfix">
          <span>{{ $t('scar.recentRecords') }}</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="viewAllRecords">
            {{ $t('common.viewAll') }}
          </el-button>
        </div>
        <el-table :data="recentRecords" style="width: 100%">
          <el-table-column prop="scarCode" :label="$t('scar.scarCode')" width="120">
            <template slot-scope="scope">
              <el-button type="text" @click="viewRecord(scope.row)">
                {{ scope.row.scarCode }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="supplierName" :label="$t('supplier.supplierName')" width="150" />
          <el-table-column prop="materialCode" :label="$t('material.materialCode')" width="120" />
          <el-table-column prop="scarStatus" :label="$t('common.status')" width="100">
            <template slot-scope="scope">
              <dict-tag :type="DICT_TYPE.SCAR_STATUS" :value="scope.row.scarStatus" />
            </template>
          </el-table-column>
          <el-table-column prop="publishDate" :label="$t('scar.releaseDate')" width="120" />
          <el-table-column prop="currentUsers" :label="$t('scar.currentHandler')" />
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script>
import { statisticsCard, pageRecord, getOverviewChartData } from '@/api/scar'
import { DICT_TYPE } from '@/utils/dict'
import * as echarts from 'echarts'

export default {
  name: 'ScarOverview',
  data() {
    return {
      DICT_TYPE,
      itemArr: [],
      recentRecords: [],
      chartData: {
        statusData: [],
        trendData: [],
        supplierData: [],
        categoryData: []
      },
      charts: {
        statusChart: null,
        trendChart: null,
        supplierChart: null,
        categoryChart: null
      }
    }
  },
  mounted() {
    this.initData()
  },
  beforeDestroy() {
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
  },
  methods: {
    async initData() {
      await this.getStatisticsCard()
      await this.getRecentRecords()
      await this.getChartData()
      this.$nextTick(() => {
        this.initCharts()
      })
    },
    
    getStatisticsCard() {
      return statisticsCard().then(res => {
        this.itemArr = [
          { label: 'YTD 发布SCAR', value: res.data.publishCount },
          { label: 'YTD 发布进料检验改善单', value: res.data.publishIqcCount },
          { label: 'YTD 发布在线不良改善单', value: res.data.publishScCount },
          { label: 'YTD 已完成SCAR个数', value: res.data.completedCount },
          { label: 'YTD 进行中SCAR', value: res.data.underWayCount }
        ]
      }).catch(() => {})
    },
    
    getRecentRecords() {
      const queryParams = {
        pageNo: 1,
        pageSize: 10,
        sortBy: 'desc',
        sortField: 'publishDate'
      }
      return pageRecord(queryParams).then(res => {
        this.recentRecords = res.data.list || []
      }).catch(() => {})
    },
    
    getChartData() {
      // 这里应该调用实际的图表数据API
      // 暂时使用模拟数据
      this.chartData = {
        statusData: [
          { name: '新建', value: 10 },
          { name: '供应商回复', value: 25 },
          { name: 'SQE审核', value: 15 },
          { name: '会签', value: 8 },
          { name: '已完成', value: 42 }
        ],
        trendData: {
          months: ['1月', '2月', '3月', '4月', '5月', '6月'],
          values: [12, 19, 15, 25, 22, 18]
        },
        supplierData: [
          { name: '供应商A', value: 15 },
          { name: '供应商B', value: 12 },
          { name: '供应商C', value: 10 },
          { name: '供应商D', value: 8 },
          { name: '供应商E', value: 6 }
        ],
        categoryData: [
          { name: '电子元件', value: 30 },
          { name: '机械零件', value: 25 },
          { name: '原材料', value: 20 },
          { name: '包装材料', value: 15 },
          { name: '其他', value: 10 }
        ]
      }
    },
    
    initCharts() {
      this.initStatusChart()
      this.initTrendChart()
      this.initSupplierChart()
      this.initCategoryChart()
    },
    
    initStatusChart() {
      this.charts.statusChart = echarts.init(this.$refs.statusChart)
      const option = {
        tooltip: {
          trigger: 'item'
        },
        series: [{
          type: 'pie',
          radius: '50%',
          data: this.chartData.statusData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      this.charts.statusChart.setOption(option)
    },
    
    initTrendChart() {
      this.charts.trendChart = echarts.init(this.$refs.trendChart)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: this.chartData.trendData.months
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: this.chartData.trendData.values,
          type: 'line',
          smooth: true
        }]
      }
      this.charts.trendChart.setOption(option)
    },
    
    initSupplierChart() {
      this.charts.supplierChart = echarts.init(this.$refs.supplierChart)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: this.chartData.supplierData.map(item => item.name)
        },
        series: [{
          type: 'bar',
          data: this.chartData.supplierData.map(item => item.value)
        }]
      }
      this.charts.supplierChart.setOption(option)
    },
    
    initCategoryChart() {
      this.charts.categoryChart = echarts.init(this.$refs.categoryChart)
      const option = {
        tooltip: {
          trigger: 'item'
        },
        series: [{
          type: 'doughnut',
          radius: ['40%', '70%'],
          data: this.chartData.categoryData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      this.charts.categoryChart.setOption(option)
    },
    
    viewRecord(row) {
      this.$router.push(`/scar/${row.scarType}/see/${row.scarCode}?id=${row.id}&viewOnly=1`)
    },
    
    viewAllRecords() {
      this.$emit('switch-tab', 'Scar')
    }
  }
}
</script>

<style lang="scss" scoped>
.overview-container {
  padding: 20px;
}

.charts-section {
  margin: 20px 0;
}

.chart-card {
  height: 400px;
  
  .chart-container {
    height: 320px;
  }
}

.table-section {
  margin-top: 20px;
}
</style>
