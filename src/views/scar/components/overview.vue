<template>
  <div class="overview-container">
    <!-- 统计卡片 -->
    <StatisticsCard :item-arr="itemArr" />

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>状态分布</span>
            </div>
            <div class="chart-container">
              <div style="text-align: center; padding: 50px; color: #999;">
                图表功能开发中...
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>月度趋势</span>
            </div>
            <div class="chart-container">
              <div style="text-align: center; padding: 50px; color: #999;">
                图表功能开发中...
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>供应商排名</span>
            </div>
            <div class="chart-container">
              <div style="text-align: center; padding: 50px; color: #999;">
                图表功能开发中...
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>类别分布</span>
            </div>
            <div class="chart-container">
              <div style="text-align: center; padding: 50px; color: #999;">
                图表功能开发中...
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-card>
        <div slot="header" class="clearfix">
          <span>{{ $t('scar.recentRecords') }}</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="viewAllRecords">
            {{ $t('common.viewAll') }}
          </el-button>
        </div>
        <el-table :data="recentRecords" style="width: 100%">
          <el-table-column prop="scarCode" :label="$t('scar.scarCode')" width="120">
            <template slot-scope="scope">
              <el-button type="text" @click="viewRecord(scope.row)">
                {{ scope.row.scarCode }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="supplierName" :label="$t('supplier.supplierName')" width="150" />
          <el-table-column prop="materialCode" :label="$t('material.materialCode')" width="120" />
          <el-table-column prop="scarStatus" :label="$t('common.status')" width="100">
            <template slot-scope="scope">
              <dict-tag :type="DICT_TYPE.SCAR_STATUS" :value="scope.row.scarStatus" />
            </template>
          </el-table-column>
          <el-table-column prop="publishDate" :label="$t('scar.releaseDate')" width="120" />
          <el-table-column prop="currentUsers" :label="$t('scar.currentHandler')" />
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script>
import { statisticsCard, pageRecord } from '@/api/scar'
import { DICT_TYPE } from '@/utils/dict'

export default {
  name: 'ScarOverview',
  data() {
    return {
      DICT_TYPE,
      itemArr: [],
      recentRecords: []
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    async initData() {
      await this.getStatisticsCard()
      await this.getRecentRecords()
    },

    getStatisticsCard() {
      return statisticsCard().then(res => {
        this.itemArr = [
          { label: 'YTD 发布SCAR', value: res.data.publishCount },
          { label: 'YTD 发布进料检验改善单', value: res.data.publishIqcCount },
          { label: 'YTD 发布在线不良改善单', value: res.data.publishScCount },
          { label: 'YTD 已完成SCAR个数', value: res.data.completedCount },
          { label: 'YTD 进行中SCAR', value: res.data.underWayCount }
        ]
      }).catch(() => {})
    },

    getRecentRecords() {
      const queryParams = {
        pageNo: 1,
        pageSize: 10,
        sortBy: 'desc',
        sortField: 'publishDate'
      }
      return pageRecord(queryParams).then(res => {
        this.recentRecords = res.data.list || []
      }).catch(() => {})
    },

    viewRecord(row) {
      this.$router.push(`/scar/${row.scarType}/see/${row.scarCode}?id=${row.id}&viewOnly=1`)
    },

    viewAllRecords() {
      this.$emit('switch-tab', 'Scar')
    }
  }
}
</script>

<style lang="scss" scoped>
.overview-container {
  padding: 20px;
}

.charts-section {
  margin: 20px 0;
}

.chart-card {
  height: 400px;

  .chart-container {
    height: 320px;
  }
}

.table-section {
  margin-top: 20px;
}
</style>
