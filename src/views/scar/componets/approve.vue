<template>
  <div>
    <common-card
      :title="$t('scar.approval')"
    >
      <el-form v-if="!viewOnly" label-width="100px" style="margin-top: 15px">
        <el-form-item :label="$t('auth.electronicSignature')" required>
          <show-or-edit
            :value="scarInfo.signature"
            :disabled="approveDisable"
          >
            <el-input
              v-model="scarInfo.signature"
              :disabled="approveDisable"
              type="password"
            />
          </show-or-edit>
        </el-form-item>
        <el-form-item :label="$t('auth.approvalComments')">
          <show-or-edit
            :value="scarInfo.approvalContent"
            :disabled="approveDisable"
          >
            <el-input
              v-model="scarInfo.approvalContent"
              :disabled="approveDisable"
              type="textarea"
              maxlength="1000"
              show-word-limit
            />
          </show-or-edit>
        </el-form-item>
      </el-form>

      <!--      【UFFF-1697】查看页面仅展示 审批记录。参考云效单号的备注-->
      <div v-else>
        <el-table :data="approveUserRecords">
          <el-table-column :label="$t('rfq.approver')">
            <template slot-scope="scope">
              <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="scope.row.userId" />
            </template>
          </el-table-column>
          <el-table-column :label="$t('rfq.approvalDate')" prop="approveDate">
            <template slot-scope="scope">
              <span v-if="scope.row.sectionStatus === 'submit'">{{ scope.row.approveDate }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('auth.approvalComments')" prop="approveContent" show-overflow-tooltip />

        </el-table>
      </div>

      <div v-if="!approveDisable" class="fixedBottom">
        <back-record />

        <el-button class="commonBtn" type="primary" @click="submitGlobal">{{ $t('common.submit') }}</el-button>
        <el-button plain class="commonBtn" type="primary" @click="saveGlobal">{{ $t('common.save') }}</el-button>
        <!--        <el-button class="commonBtn" type="primary" @click="$router.back()">{{ $t('common.cancel') }}</el-button>-->
      </div>
    </common-card>
  </div>
</template>

<script>
import { getRecordApproveUserRelList, saveGlobal, submitGlobal, validatePassword } from '@/api/scar'
import backRecord from '@/views/scar/componets/backRecord.vue'
import { getPath } from '@/utils/ruoyi'

export default {
  name: 'Approve',
  components: {
    backRecord
  },
  inject: ['app'],
  props: ['viewOnly'],
  data() {
    return {
      approveUserRecords: []
    }
  },
  computed: {
    scarInfo() {
      return this.app.scarInfo
    },
    approveDisable() {
      return ['revoked', 'completed'
      ].includes(this.scarInfo.scarStatus) || this.viewOnly
    }
  },
  created() {
    // 【UFFF-1697】会签节点的查看页面-批准节点部分改成 会签记录
    if (this.viewOnly) {
      getRecordApproveUserRelList(this.app.scarInfo.id).then(res => {
        this.approveUserRecords = res.data
      })
    }
  },
  methods: {
    // 批准状态的保存
    saveGlobal() {
      if (!this.scarInfo.signature) {
        this.$message.error(this.$t('scar.pleaseEnterAnElectronicSignature'))
        return
      }
      validatePassword({
        password: this.scarInfo.signature
      }).then(res => {
        if (res.data) {
          if (this.scarInfo.checkSupplierReplyTextRelBaseVO) {
            this.scarInfo.checkSupplierReplyTextRelBaseVO.scarId = this.scarInfo.id
          }
          saveGlobal(this.scarInfo).then(res => {
            this.$message.success(this.$t('common.savedSuccessfully'))
            // this.$emit('initScar')
          })
        } else {
          this.$message.error(this.$t('scar.passwordError'))
        }
      })
    },
    // 批准状态的提交
    submitGlobal() {
      if (!this.scarInfo.signature) {
        this.$message.error(this.$t('scar.pleaseEnterAnElectronicSignature'))
        return
      }
      validatePassword({
        password: this.scarInfo.signature
      }).then(res => {
        if (res.data) {
          if (this.scarInfo.checkSupplierReplyTextRelBaseVO) {
            this.scarInfo.checkSupplierReplyTextRelBaseVO.scarId = this.scarInfo.id
          }

          submitGlobal(this.scarInfo).then(res => {
            this.$message.success(this.$t('supplier.submittedSuccessfully'))
            if (this.$store.getters.userId === -1) {
              location.href = getPath('/')
            } else {
              this.$tab.closeOpenPage('/scar/scarindex')
            }
          })
        } else {
          this.$message.error(this.$t('scar.passwordError'))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mainTab{
  margin-top: 15px;
}
.commonBtn{
  min-width: 80px;
}

.required::before{
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}
</style>
