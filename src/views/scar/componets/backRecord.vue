<template>
  <div style="display: inline-flex;clear: both;text-align: left;margin: 0 10px">
    <el-button v-has-permi="['scar:record:reject']" plain class="danger" type="primary" @click="visible=true">{{ $t('rfq.return') }}</el-button>
    <el-dialog
      v-if="visible"
      append-to-body
      :visible.sync="visible"
      :title="$t('rfq.reasonForReturn')"
      width="1000px"
    >
      <span slot="title" style="font-size: 18px" class="required">{{ $t('rfq.reasonForReturn') }}</span>

      <show-or-edit :value="content">
        <el-input
          v-model="content"
          :rows="3"
          maxlength="200"
          show-word-limit
          type="textarea"
        />
      </show-or-edit>
      <div slot="footer">
        <el-button @click="visible=false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitBack">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { rejectContainmentAction, rejectGlobal } from '@/api/scar'
import ShowOrEdit from '@/components/ShowOrEdit'

export default {
  name: 'Backrecord',
  components: {
    ShowOrEdit
  },
  inject: ['app'],
  props: {
    rejectType: {
      type: String,
      default: 'global'
    }
  },
  data() {
    return {
      visible: false,
      content: ''
    }
  },
  computed: {
    scarInfo() {
      return this.app.scarInfo
    }
  },

  methods: {
    submitBack() {
      if (!this.content) {
        this.$message.error(this.$t('scar.pleaseFillInTheReason'))
        return
      }
      if (this.rejectType === 'global') {
        this.scarInfo.approvalContent = this.content
        rejectGlobal({ ...this.scarInfo, fileRelList: [] }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.$tab.closeOpenPage('/scar/scarindex')
        })
      } else {
        rejectContainmentAction({
          scarId: this.scarInfo.id,
          containmentActionRelList: this.scarInfo.containmentActionRelList,
          reason: this.content
        }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.visible = false
          // 触发初始化操作
          this.$emit('refreshRecord')
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.commonBtn {
  min-width: 80px;
}
.required::before{
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}
</style>
