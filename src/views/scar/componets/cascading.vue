<template>
  <el-cascader
    v-model="value"
    class="content"
    filterable
    :disabled="disabled"
    clearable
    :props="{ value: 'id',label:'name',multiple: multiple}"
    :options="badTypeList"
    @change="changeVal"
    @visible-change="openDefectType"
  />
</template>

<script>
import { getTreeMap } from '@/utils'
import { getDefectTypeListCache } from '@/api/scar/defectType'

export default {
  name: 'Cascading',
  props: {
    originalValue: {
    },
    multiple: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    orgId: {
      type: Number
    }
  },
  data() {
    return {
      value: [],
      badTypeList: []
    }
  },
  watch: {
    originalValue: {
      immediate: true,
      handler() {
        if (this.multiple) {
          this.originalValue.map(item => {
            this.value.push(getTreeMap(item, this.badTypeList))
          })
        } else {
          this.value = getTreeMap(this.originalValue, this.badTypeList)
        }
      }
    }
  },
  methods: {
    changeVal() {
      if (this.multiple) {
        this.$emit('update:originalValue', this.value.map(item => item.at(-1)) || [])
      } else {
        this.$emit('update:originalValue', this.value.at(-1) || '')
      }
    },
    openDefectType(val) {
      if (val && this.orgId) {
        getDefectTypeListCache(null, null, null).then(res => {
          this.badTypeList = this.handleTree(res.data, 'id')
          if (this.badTypeList?.length <= 0) {
            this.$message.error(this.$t('scar.thePurchasingOrganizationHasNotConfiguredADefectType'))
          }
        })
      }
    }
  }
}
</script>

<style scoped>

</style>
