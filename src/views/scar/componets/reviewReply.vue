<template>
  <div>
    <common-card>
      <span slot="header" class="required">
        {{ $t('scar.reviewSupplierResponse') }}
      </span>
      <div class="mainTab">
        <el-checkbox v-model="checked" :disabled="reviewReplyDisable" style="margin-bottom: 15px">{{ $t('scar.afterTheMeasuresAreImported') }}</el-checkbox>
        <show-or-edit
          :value="scarInfo.checkSupplierReplyTextRelBaseVO.qualifiedBatchNoAfterImplementationCorrectiveActionText"
          :disabled="reviewReplyDisable"
        >
          <el-input
            v-show="checked"
            v-model="scarInfo.checkSupplierReplyTextRelBaseVO.qualifiedBatchNoAfterImplementationCorrectiveActionText"
            :disabled="reviewReplyDisable"
            class="mainTab"
            maxlength="500"
            show-word-limit
          />
        </show-or-edit>
      </div>
      <div class="mainTab">
        <el-checkbox
          v-model="checked1"
          :disabled="reviewReplyDisable"
          style="margin-bottom: 15px"
        >{{ $t('scar.evidenceOfOnsiteAuditMeasuresImplementationAndVerificationResults') }}</el-checkbox>
        <div v-if="reviewReplyDisable" v-html="scarInfo.checkSupplierReplyTextRelBaseVO.evidenceSiteAuditsVerificationResultText" />
        <tinymce
          v-if="checked1&&!reviewReplyDisable"
          v-model="scarInfo.checkSupplierReplyTextRelBaseVO.evidenceSiteAuditsVerificationResultText"
          :height="160"
        />
      </div>
      <div class="mainTab">
        <el-checkbox
          v-model="checked2"
          :disabled="reviewReplyDisable"
          style="margin-bottom: 15px"
        >{{ $t('supplier.other') }}</el-checkbox>
        <div v-if="reviewReplyDisable" v-html="scarInfo.checkSupplierReplyTextRelBaseVO.otherText" />
        <tinymce
          v-if="checked2&&!reviewReplyDisable"
          v-model="scarInfo.checkSupplierReplyTextRelBaseVO.otherText	"
          :height="160"
        />
      </div>
      <div style="display: flex;align-items: center;margin-top: 20px;">
        <span>{{ (viewOnly || !['sqe_review','countersign'].includes(scarInfo.scarStatus))?$t('scar.seeFile'):$t('auth.uploadAttachments') }}</span>
        <scar-file
          :disabled="viewOnly || !['sqe_review','countersign'].includes(scarInfo.scarStatus) "
          :business-value="'review_supplier_response'"
          style="margin-left: 10px"
        />
      </div>

      <el-form label-width="100px" inline style="margin-top: 15px">
        <el-form-item
          :label="$t('scar.inviteReviewers')"
        >
          <show-or-edit
            :value="scarInfo.reviewers"
            :disabled="reviewReplyDisable"
          >
            <el-select
              v-model="scarInfo.reviewers"
              :disabled="reviewReplyDisable"
              class="searchValue"
              multiple
              clearable
              filterable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
      </el-form>
      <div v-if="scarInfo.scarStatus === 'sqe_review'&&!viewOnly" class="fixedBottom">
        <!--        退回-->
        <back-record />
        <el-button v-has-permi="['scar:record:submit']" class="commonBtn" type="primary" @click="submitGlobal">{{ $t('common.submit') }}</el-button>

        <el-button v-has-permi="['scar:record:save']" plain class="commonBtn" type="primary" @click="saveGlobal">{{ $t('common.save') }}</el-button>
        <!--        <el-button v-has-permi="['scar:record:query']" class="commonBtn" type="primary" @click="$router.back()">{{$t('common.cancel')}}</el-button>-->
      </div>
    </common-card>
  </div>
</template>

<script>
import { saveGlobal, submitGlobal } from '@/api/scar'
import ScarFile from '@/views/scar/componets/scarFile.vue'
import BackRecord from '@/views/scar/componets/backRecord.vue'
import { getPath } from '@/utils/ruoyi'

export default {
  // SQE 评审页面
  name: 'Reviewreply',
  components: { ScarFile, BackRecord },
  inject: ['app'],
  props: ['viewOnly'],
  data() {
    return {
      checked: false,
      checked1: false,
      checked2: false,
      content: ''
    }
  },
  computed: {
    scarInfo() {
      return this.app.scarInfo
    },
    reviewReplyDisable() {
      if (this.viewOnly) {
        return true
      }
      return ['revoked', 'completed'].includes(this.scarInfo.scarStatus)
    }
  },
  watch: {
    'scarInfo.checkSupplierReplyTextRelBaseVO.qualifiedBatchNoAfterImplementationCorrectiveActionText': {
      immediate: true,
      handler(val) {
        if (val) {
          this.checked = true
        }
      }
    },
    'scarInfo.checkSupplierReplyTextRelBaseVO.evidenceSiteAuditsVerificationResultText': {
      immediate: true,

      handler(val) {
        if (val) {
          this.checked1 = true
        }
      }
    },
    'scarInfo.checkSupplierReplyTextRelBaseVO.otherText': {
      immediate: true,

      handler(val) {
        if (val) {
          this.checked2 = true
        }
      }
    }
  },

  methods: {
    saveGlobal() {
      this.scarInfo.checkSupplierReplyTextRelBaseVO.scarId = this.scarInfo.id
      saveGlobal(this.scarInfo).then(res => {
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.$emit('initScar')
      })
    },
    submitGlobal() {
      this.scarInfo.checkSupplierReplyTextRelBaseVO.scarId = this.scarInfo.id

      submitGlobal(this.scarInfo).then(res => {
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
        if (this.$store.getters.userId === -1) {
          location.href = getPath('/')
        } else {
          this.$tab.closeOpenPage('/scar/scarindex')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.commonBtn{
  min-width: 80px;
}
.mainTab{
  margin-top: 15px;
}
.required::before{
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

</style>
