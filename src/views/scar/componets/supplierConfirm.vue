<template>
  <div>
    <common-card
      :title="$t('scar.supplierConfirmation')"
    >
      <div style="display: flex;align-items: center;margin-bottom: 20px;">
        <span>{{ supplierConfirmDisable?$t('scar.seeFile'):$t('auth.uploadAttachments') }}</span>
        <scar-file
          :disabled="supplierConfirmDisable"
          :business-value="'supplier_confirmation'"
          style="margin-left: 10px"
        />
      </div>

      <div v-if="supplierConfirmDisable" v-html="scarInfo.supplierReplyTextRelBaseVO.supplierConfirmText" />

      <tinymce v-else v-model="scarInfo.supplierReplyTextRelBaseVO.supplierConfirmText" :height="160" />
      <div v-if="!supplierConfirmDisable" class="fixedBottom">
        <el-button v-has-permi="['scar:supplier-reply-text-rel:supplier-confirm']" class="commonBtn" type="primary" @click="submitSupplierConfirm('submit')">{{ $t('common.submit') }}</el-button>
        <el-button v-has-permi="['scar:supplier-reply-text-rel:supplier-confirm']" plain class="commonBtn" type="primary" @click="submitSupplierConfirm('save')">{{ $t('common.save') }}</el-button>
        <!--        <el-button class="commonBtn" type="primary" @click="$router.back()">{{ $t('common.cancel')}}</el-button>-->
      </div>
    </common-card>
  </div>
</template>

<script>
import { supplierConfirm } from '@/api/scar'
import ScarFile from '@/views/scar/componets/scarFile.vue'
import { getPath } from '@/utils/ruoyi'

export default {
  name: 'Supplierconfirm',
  components: { ScarFile },
  inject: ['app'],
  props: ['viewOnly'],
  data() {
    return {
      content: null
    }
  },
  computed: {
    scarInfo() {
      return this.app.scarInfo
    },
    supplierConfirmDisable() {
      return ['revoked', 'completed', 'countersign'].includes(this.scarInfo.scarStatus) || this.viewOnly
    }
  },
  methods: {
    submitSupplierConfirm(sectionStatus) {
      if (!this.scarInfo.supplierReplyTextRelBaseVO.supplierConfirmText && sectionStatus === 'submit') {
        this.$message.error(this.$t('order.pleaseFillInTheRequiredInformation'))
        return
      }
      this.scarInfo.supplierReplyTextRelBaseVO.scarId = this.scarInfo.id
      supplierConfirm({
        ...this.scarInfo.supplierReplyTextRelBaseVO,
        sectionStatus,
        fileRelList: this.app.scarInfo.fileRelList.filter(a => a.businessValue === 'supplier_confirmation')
      }).then(res => {
        if (sectionStatus === 'save') {
          this.scarInfo.supplierReplyTextRelBaseVO.id = res.data
          this.$message.success(this.$t('common.savedSuccessfully'))
        } else {
          this.$message.success(this.$t('supplier.submittedSuccessfully'))
          if (this.$store.getters.userId === -1) {
            location.href = getPath('/')
          } else {
            this.$tab.closeOpenPage('/scar/scarindex')
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.commonBtn{
  min-width: 80px;
}

</style>
