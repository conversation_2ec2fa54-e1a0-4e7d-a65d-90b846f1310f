<template>
  <div>
    <common-card
      :title="$t('scar.supplierResponse')"
    >
      <common-card
        v-if="['iqc','sc'].includes(scarInfo.scarType)"
      >
        <span slot="header" class="required">
          {{ $t('scar.containmentAction') }}
        </span>
        <el-table
          :data="scarInfo.containmentActionRelList"
        >
          <el-table-column :label="$t('scar.materialLocation')" prop="location" show-overflow-tooltip>
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.SCAR_LOCATION" :value="scope.row.location" />
            </template>
          </el-table-column>

          <el-table-column
            :show-overflow-tooltip="containmentActionDisable"
            :label="$t('scar.materialDisposalMeasures')"
            prop="disposal"
            width="350px"
          >
            <template #default="scope">
              <el-input
                v-if="!containmentActionDisable"
                v-model="scope.row.disposal"
                :disabled="containmentActionDisable"
                maxlength="500"
                show-word-limit
              />
              <span v-else>{{ scope.row.disposal }}</span>
            </template>
            <template #header>
              <span class="required">
                {{ $t('scar.materialDisposalMeasures') }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('scar.totalQuantity')" prop="sumQuantity">
            <template #default="scope">
              <el-input
                v-model.number="scope.row.sumQuantity"
                :disabled="containmentActionDisable"
                type="number"
                @blur="fixNumber('sumQuantity', scope.$index)"
                @change="calculateDefectPercent(scope.$index)"
              />
            </template>
            <template #header>
              <span class="required">
                {{ $t('scar.totalQuantity') }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('scar.numberOfDefects')" prop="quantity">
            <template #default="scope">
              <el-input
                v-model.number="scope.row.quantity"
                type="number"
                :disabled="containmentActionDisable"
                @blur="fixNumber('quantity', scope.$index)"
                @change="calculateDefectPercent(scope.$index)"
              />
            </template>
            <template #header>
              <span class="required">
                {{ $t('scar.numberOfDefects') }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('scar.defectiveRate')" prop="defectRate" width="120px">
            <template #default="scope">
              <el-input
                v-model.number="scope.row.defectRate"
                disabled
                type="number"
              >
                <span slot="suffix" style="font-size: 15px;position:relative;top: 4px;"> %</span>
              </el-input>
            </template>
            <template #header>
              <span class="required">
                {{ $t('scar.defectiveRate') }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('system.personInCharge')" prop="verifiedBy">
            <template #default="scope">
              <el-input
                v-model="scope.row.verifiedBy"
                :disabled="containmentActionDisable"
                maxlength="50"
                show-word-limit
              />

            </template>
            <template #header>
              <span class="required">
                {{ $t('system.personInCharge') }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('scar.completionTime')" prop="verificationDate" width="180px">
            <template #default="scope">
              <el-date-picker
                v-model="scope.row.verificationDate"
                :disabled="containmentActionDisable"
                :placeholder="$t('common.pleaseSelectADate')"
                placement="bottom-start"
                style="width: 160px"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </template>
            <template #header>
              <span class="required">
                {{ $t('scar.completionTime') }}
              </span>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="scarInfo.scarStatus === 'supplier_reply'&&!viewOnly" style="text-align: center;margin-top: 20px">
          <el-button
            v-if="!containmentActionDisable"
            v-has-permi="['scar:record:submit-containment-action']"
            class="commonBtn"
            type="primary"
            @click="submitContainment"
          >
            {{ $t('common.submit') }}
          </el-button>
          <!--          IQC\SC 供应商回复页面的 【围堵措施】节点的退回 仅用户类型为sqe才可见。-->
          <back-record
            v-if="scarInfo.userType === 'sqe' && scarInfo.containmentActionRelList[0].sectionStatus === 'submit'"
            v-has-permi="['scar:record:reject-containment-action']"
            :reject-type="'containment'"
            @refreshRecord="refreshRecord"
          />
        </div>
      </common-card>
      <common-card>
        <span slot="header" class="required">
          {{ $t('scar.rootCauseAnalysis') }}
        </span>
        <div style="display: flex;align-items: center;margin-bottom: 20px;">
          <span>{{ supplierReplyDisable?$t('scar.seeFile'):$t('auth.uploadAttachments') }}</span>
          <scar-file
            :business-value="'root_cause_analysis'"
            :disabled="supplierReplyDisable"
            style="margin-left: 10px"
          />
        </div>

        <div v-if="supplierReplyDisable" v-html="scarInfo.supplierReplyTextRelBaseVO.rootCauseAnalysisText" />

        <tinymce v-else v-model="scarInfo.supplierReplyTextRelBaseVO.rootCauseAnalysisText" :height="160" />
        <el-form inline label-width="70px" style="margin-top: 15px">
          <el-form-item :label="$t('scar.personLiable')" required>
            <show-or-edit
              :value="scarInfo.supplierReplyTextRelBaseVO.rootCauseAnalysisUserRel"
              :disabled="supplierReplyDisable"
            >
              <el-input
                v-model="scarInfo.supplierReplyTextRelBaseVO.rootCauseAnalysisUserRel"
                :disabled="supplierReplyDisable"
              />
            </show-or-edit>
          </el-form-item>
        </el-form>
      </common-card>
      <common-card
        v-if="scarInfo.iqcScType === '3D' || scarInfo.scarType ==='sp'"
      >
        <span slot="header" class="required">
          {{ $t('scar.permanentCorrectiveMeasuresAndImplementation') }}
        </span>
        <div style="display: flex;align-items: center;margin-bottom: 20px;">
          <span>{{ supplierReplyDisable?$t('scar.seeFile'):$t('auth.uploadAttachments') }}</span>
          <scar-file
            :business-value="'permanent_corrective_action'"
            :disabled="supplierReplyDisable"
            style="margin-left: 10px"
          />
        </div>

        <div v-if="supplierReplyDisable" v-html="scarInfo.supplierReplyTextRelBaseVO.permanentCorrectiveActionText" />

        <tinymce v-else v-model="scarInfo.supplierReplyTextRelBaseVO.permanentCorrectiveActionText" :height="160" />
        <el-form inline label-width="70px" style="margin-top: 15px">
          <el-form-item :label="$t('scar.personLiable')" required>
            <show-or-edit
              :value="scarInfo.supplierReplyTextRelBaseVO.permanentCorrectiveActionUserRel"
              :disabled="supplierReplyDisable"
            >
              <el-input
                v-model="scarInfo.supplierReplyTextRelBaseVO.permanentCorrectiveActionUserRel"
                :disabled="supplierReplyDisable"
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item label-width="150px" :label="$t('scar.completionTimeOfCorrectiveMeasures')" required>
            <show-or-edit
              type="Date"
              :value="scarInfo.supplierReplyTextRelBaseVO.permanentCorrectiveActionDate"
              :disabled="supplierReplyDisable"
            >
              <el-date-picker
                v-model="scarInfo.supplierReplyTextRelBaseVO.permanentCorrectiveActionDate"
                :disabled="supplierReplyDisable"
                :placeholder="$t('common.pleaseSelectADate')"
                placement="bottom-start"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </show-or-edit>
          </el-form-item>
        </el-form>
      </common-card>
      <common-card
        v-if="scarInfo.iqcScType === '8D'"
      >
        <span slot="header" class="required">
          {{ $t('scar.correctiveMeasuresAndImplementation') }}
        </span>
        <div style="display: flex;align-items: center;margin-bottom: 20px;">
          <span>{{ supplierReplyDisable?$t('scar.seeFile'):$t('auth.uploadAttachments') }}</span>
          <scar-file
            :business-value="'permanent_corrective_action'"
            :disabled="supplierReplyDisable"
            style="margin-left: 10px"
          />
        </div>

        <div v-if="supplierReplyDisable" v-html="scarInfo.supplierReplyTextRelBaseVO.permanentCorrectiveActionText" />

        <tinymce v-else v-model="scarInfo.supplierReplyTextRelBaseVO.permanentCorrectiveActionText" :height="160" />
        <el-form inline label-width="70px" style="margin-top: 15px">
          <el-form-item :label="$t('scar.personLiable')" required>
            <show-or-edit
              :value="scarInfo.supplierReplyTextRelBaseVO.permanentCorrectiveActionUserRel"
              :disabled="supplierReplyDisable"
            >
              <el-input
                v-model="scarInfo.supplierReplyTextRelBaseVO.permanentCorrectiveActionUserRel"
                :disabled="supplierReplyDisable"
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('scar.completionTimeOfCorrectiveMeasures')" label-width="150px" required>
            <show-or-edit
              type="Date"
              :value="scarInfo.supplierReplyTextRelBaseVO.permanentCorrectiveActionDate"
              :disabled="supplierReplyDisable"
            >
              <el-date-picker
                v-model="scarInfo.supplierReplyTextRelBaseVO.permanentCorrectiveActionDate"
                :disabled="supplierReplyDisable"
                :placeholder="$t('common.pleaseSelectADate')"
                placement="bottom-start"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </show-or-edit>
          </el-form-item>
        </el-form>
      </common-card>
      <common-card
        v-if="scarInfo.iqcScType === '8D'"
      >
        <span slot="header" class="required">
          {{ $t('scar.confirmationAndVerificationOfCorrectiveMeasures') }}
        </span>
        <div style="display: flex;align-items: center;margin-bottom: 20px;">
          <span>{{ supplierReplyDisable?$t('scar.seeFile'):$t('auth.uploadAttachments') }}</span>
          <scar-file
            :business-value="'corrective_action'"
            :disabled="supplierReplyDisable"
            style="margin-left: 10px"
          />
        </div>

        <div v-if="supplierReplyDisable" v-html="scarInfo.supplierReplyTextRelBaseVO.verificateCorrectiveActionText" />

        <tinymce v-else v-model="scarInfo.supplierReplyTextRelBaseVO.verificateCorrectiveActionText" :height="160" />
        <el-form inline label-width="70px" style="margin-top: 15px">
          <el-form-item :label="$t('scar.personLiable')" required>
            <show-or-edit
              :value="scarInfo.supplierReplyTextRelBaseVO.verificateCorrectiveActionUserRel"
              :disabled="supplierReplyDisable"
            >
              <el-input
                v-model="scarInfo.supplierReplyTextRelBaseVO.verificateCorrectiveActionUserRel"
                :disabled="supplierReplyDisable"
              />
            </show-or-edit>
          </el-form-item>
        </el-form>
      </common-card>
      <common-card
        v-if="scarInfo.iqcScType === '8D'"
      >
        <span slot="header" class="required">
          {{ $t('scar.preventingRecurrence') }}
        </span>
        <div style="display: flex;align-items: center;margin-bottom: 20px;">
          <span>{{ supplierReplyDisable?$t('scar.seeFile'):$t('auth.uploadAttachments') }}</span>
          <scar-file
            :business-value="'prevent_recurrence'"
            :disabled="supplierReplyDisable"
            style="margin-left: 10px"
          />
        </div>

        <div v-if="supplierReplyDisable" v-html="scarInfo.supplierReplyTextRelBaseVO.preventRecurrenceText" />

        <tinymce v-else v-model="scarInfo.supplierReplyTextRelBaseVO.preventRecurrenceText" :height="160" />
        <el-form inline label-width="70px" style="margin-top: 15px">
          <el-form-item :label="$t('scar.personLiable')" required>
            <show-or-edit
              :value="scarInfo.supplierReplyTextRelBaseVO.preventRecurrenceUserRel"
              :disabled="supplierReplyDisable"
            >
              <el-input
                v-model="scarInfo.supplierReplyTextRelBaseVO.preventRecurrenceUserRel"
                :disabled="supplierReplyDisable"
              />
            </show-or-edit>
          </el-form-item>
        </el-form>
      </common-card>
      <div v-if="scarInfo.scarStatus === 'supplier_reply'&&!viewOnly" class="fixedBottom">
        <back-record />

        <el-button v-has-permi="['scar:record:submit']" class="commonBtn" type="primary" @click="doSubmitGlobal">{{ $t('common.submit') }}</el-button>
        <el-button v-has-permi="['scar:record:save']" plain class="commonBtn" type="primary" @click="doSaveGlobal">{{ $t('common.save') }}</el-button>
        <!--        <el-button v-has-permi="['scar:record:query']" class="commonBtn" type="primary" @click="$router.back()">{{ $t('common.cancel') }}</el-button>-->
      </div>
    </common-card>

  </div>
</template>

<script>
import { saveGlobal, submitContainmentAction, submitGlobal } from '@/api/scar'
import BackRecord from '@/views/scar/componets/backRecord.vue'
import ScarFile from '@/views/scar/componets/scarFile.vue'
import { getPath } from '@/utils/ruoyi'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'

export default {
  name: 'Supplierreply',
  components: { ShowOrEdit, BackRecord, ScarFile },
  inject: ['app'],
  props: ['viewOnly'],
  data() {
    return {
      content: null
    }
  },
  computed: {
    scarInfo() {
      return this.app.scarInfo
    },
    supplierReplyDisable() {
      return ['completed', 'countersign'
      ].includes(this.scarInfo.scarStatus) || this.viewOnly
    },
    // 围堵措施的disable规则。用户类型为supplier 且 围堵措施已提交，则不允许再次编辑
    containmentActionDisable() {
      if (this.viewOnly) {
        return true
      }
      if (['countersign', 'completed', 'revoked'].includes(this.scarInfo.scarStatus)) {
        return true
      }
      return this.scarInfo.containmentActionRelList[0].sectionStatus === 'submit'
    },
    commonDisable() {
      return this.scarInfo.scarStatus !== 'supplier_reply' || this.viewOnly
    }
  },
  methods: {
    doSaveGlobal() {
      this.scarInfo.supplierReplyTextRelBaseVO.scarId = this.scarInfo.id
      saveGlobal(this.scarInfo).then(res => {
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.$emit('initScar')
      })
    },
    // 退回围堵措施后 刷新当前单据
    refreshRecord() {
      this.$emit('initScar')
    },
    doSubmitGlobal() {
      if (['iqc', 'sc'].includes(this.scarInfo.scarType)) {
        // 2. 围堵措施是否已经提交
        if (!this.scarInfo.containmentActionRelList.every(item => item.sectionStatus === 'submit')) {
          this.$message.error(this.$t('scar.pleaseSubmitContainmentMeasuresFirst'))
          return
        }
      }
      this.scarInfo.supplierReplyTextRelBaseVO.scarId = this.scarInfo.id
      submitGlobal(this.scarInfo).then(res => {
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
        if (this.$store.getters.userId === -1) {
          location.href = getPath('/')
        } else {
          this.$tab.closeOpenPage('/scar/scarindex')
        }
      })
    },
    submitContainment() {
      // 校验围堵措施必填信息
      let pass = true
      this.scarInfo.containmentActionRelList.forEach(a => {
        if (!a.sumQuantity) {
          a.sumQuantity = 0
        }
        if (!a.quantity) {
          a.quantity = 0
        }
        if (a.sumQuantity === 0) {
          a.defectRate = 0
        } else {
          a.defectRate = (a.quantity / a.sumQuantity * 100).toFixed(2)
        }
        // 当不良数量为0时，则允许disposal（物料处置措施）允许为空
        // extra: 其余的责任人和完成时间的必填交给后端校验
        if (a.quantity !== 0) {
          if (!a.disposal) {
            this.$message.error(this.$t('scar.theFieldOfContainmentMeasuresMaterialDisposalMeasuresMustBeFilledIn'))
            pass = false
            return
          }
        }
      })
      if (!pass) {
        return
      }
      submitContainmentAction({
        scarId: this.scarInfo.id,
        containmentActionRelList: this.scarInfo.containmentActionRelList
      }).then(res => {
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
        this.scarInfo.containmentActionRelList = res.data
      })
    },
    // 围堵措施表单数量类型固定小数位
    fixNumber(type, index) {
      const count = (numb) => {
        if (Number.isInteger(numb)) {
          return 0
        } else {
          return numb.toString().split('.')[1].length
        }
      }
      // 格式化输入的数量格式（将数值四舍五入并格式化为固定小数位【2】的字符串）
      if (count(this.scarInfo.containmentActionRelList[index][type])) {
        this.scarInfo.containmentActionRelList[index][type] = this.scarInfo.containmentActionRelList[index][type].toFixed(2)
      }
    },
    // 计算围堵措施表单数据的不良率
    calculateDefectPercent(index) {
      if ((this.scarInfo.containmentActionRelList[index].sumQuantity && this.scarInfo.containmentActionRelList[index].quantity) ||
        this.scarInfo.containmentActionRelList[index].sumQuantity === 0 ||
        this.scarInfo.containmentActionRelList[index].quantity === 0) {
        this.scarInfo.containmentActionRelList[index].defectRate = (this.scarInfo.containmentActionRelList[index].quantity / this.scarInfo.containmentActionRelList[index].sumQuantity * 100).toFixed(2)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.mainTab {
  margin-top: 15px;
}

.required::before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

.commonBtn {
  min-width: 80px;
}

</style>
