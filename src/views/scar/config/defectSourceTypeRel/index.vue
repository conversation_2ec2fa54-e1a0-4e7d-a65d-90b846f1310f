<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('scar.formName')" prop="scarType">
        <el-select v-model="queryParams.scarType" clearable size="small">
          <el-option
            v-for="dict in recordTypeSearchDictDatas"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('scar.badSource')" prop="badSource">
        <el-select v-model="queryParams.badSource" clearable size="small">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SCAR_DEFECT_SOURCE, 0)"
            :key="dict.id"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['scar:bad-source-type-rel:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        :label="$t('scar.formName')"
        align="left"
        prop="scarType"
      >
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SCAR_RECORD_TYPE" :value="scope.row.scarType" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('scar.badSource')"
        align="center"
        prop="badSource"
      >
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SCAR_DEFECT_SOURCE" :value="scope.row.badSource" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.status')"
        align="center"
        prop="status"
      >
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('system.operationTime')"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['scar:bad-source-type-rel:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="400px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('scar.formName')" prop="scarType">
          <el-select v-model="form.scarType">
            <el-option
              v-for="dict in recordTypeSearchDictDatas"
              :key="dict.value"
              :disabled="form.id!=null"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('scar.badSource')" prop="badSource">
          <el-select v-model="form.badSource">
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SCAR_DEFECT_SOURCE, 0)"
              :key="dict.id"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('common.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import {
  createBadSourceTypeRel,
  getBadSourceTypeRel,
  getBadSourceTypeRelPage,
  updateBadSourceTypeRel
} from '@/api/scar/badSourceTypeRel'

export default {
  name: 'Badsourcetyperel',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        scarType: null,
        badSource: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        scarType: [{ required: true, message: this.$t('scar.theFormNameIsNotEmpty'), trigger: 'change' }],
        status: [{ required: true, message: this.$t('scar.statusIsNotEmpty'), trigger: 'blur' }]
      },
      // 数据字典
      recordTypeDictDatas: getDictDatas(DICT_TYPE.SCAR_RECORD_TYPE, 0),
      recordTypeSearchDictDatas: getDictDatas(DICT_TYPE.SCAR_RECORD_TYPE, 0)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      // 执行查询
      getBadSourceTypeRelPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        scarType: undefined,
        badSource: undefined,
        status: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      if (this.list) {
        const existScarTypes = []
        this.list.forEach(item => existScarTypes.push(item.scarType))
        this.recordTypeDictDatas = this.recordTypeDictDatas.filter(item => {
          return !existScarTypes.includes(item.value)
        })
      }
      this.open = true
      this.title = this.$t('scar.defectSourceAndDocumentAssociation')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      this.recordTypeDictDatas = getDictDatas(DICT_TYPE.SCAR_RECORD_TYPE, 0)
      getBadSourceTypeRel(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('scar.defectSourceAndDocumentAssociation')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 校验 表单+不良来源的唯一性
        const source = this.form.badSource
        const type = this.form.scarType
        var tmp = this.list
        if (this.form.id) {
          tmp = tmp.filter(item => item.id !== this.form.id)
        }
        if (tmp?.filter(item => item.badSource === source && item.scarType === type)?.length > 0) {
          this.$message.error(this.$t('scar.duplicateDocumentTypeAndDefectSourceAreNotAllowed'))
          return
        }

        // 修改的提交
        if (this.form.id != null) {
          updateBadSourceTypeRel(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createBadSourceTypeRel(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    }
  }
}
</script>
