<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="98px" size="small">
      <el-form-item :label="$t('scar.defectTypeCode')" prop="code">
        <el-input
          v-model="queryParams.code"
          :placeholder="$t('scar.pleaseEnterTheDefectTypeCode')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.name')" prop="name">
        <el-input v-model="queryParams.name" :placeholder="$t('common.pleaseEnterAName')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('supplier.pleaseSelectStatus')" clearable>
          <el-option
            v-for="dict in statusDictDatas"
            :key="parseInt(dict.value)"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['scar:defect-type:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd()"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['scar:defect-type:export']"
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleImport"
        >{{ $t('supplier.batchImport') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['scar:defect-type:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >{{ $t('common.export') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="list"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      row-key="id"
    >
      <el-table-column :label="$t('scar.defectTypeCode')" align="left" prop="code" />
      <el-table-column :label="$t('scar.nameOfDefectType')" align="center" prop="name" />
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div>
            <el-button
              v-hasPermi="['scar:defect-type:update']"
              icon="el-icon-edit"
              size="mini"
              type="text"
              @click="handleUpdate(scope.row)"
            >{{ $t('common.modify') }}
            </el-button>
            <el-button
              v-if="scope.row.level === 1"
              v-hasPermi="['scar:defect-type:create']"
              icon="el-icon-plus"
              size="mini"
              type="text"
              @click="handleAdd(scope.row)"
            >{{ $t('common.add') }}
            </el-button>
            <el-button
              v-hasPermi="['scar:defect-type:delete']"
              icon="el-icon-delete"
              size="mini"
              type="text"
              @click="handleDelete(scope.row)"
            >{{ $t('common.del') }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="400px">
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('scar.parentDefectType')">
              <treeselect
                v-model="form.parentId"
                :normalizer="normalizer"
                :options="defectTypeOptions"
                :placeholder="$t('scar.pleaseSelectTheParentDefectType')"
                :show-count="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('scar.defectTypeCode')" prop="code">
              <el-input v-model="form.code" :placeholder="$t('scar.pleaseEnterTheDefectTypeCode')" />
            </el-form-item>
          </el-col>
          <el-col v-for="(item,i) in form.translations" :key="i" :span="24">
            <el-form-item
              :label="$t('scar.nameOfDefectType')"
              :prop="`translations[${i}].translation`"
              :rules="{required: true, message: $t('scar.pleaseEnterTheNameOfTheDefectType'), trigger: 'blur'}"
            >
              <el-input v-model="item.translation">
                <svg-icon slot="suffix" :icon-class="item.locale" />
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('common.status')" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="parseInt(dict.value)">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          class="small-padding"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="exportAllDefectType"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="doImport"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  createDefectType,
  deleteDefectType,
  exportDefectTypeExcel,
  getDefectType,
  getDefectTypeList,
  updateDefectType
} from '@/api/scar/defectType'
import { getBaseHeader } from '@/utils/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import Treeselect from '@riophae/vue-treeselect'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'

export default {
  name: 'Defecttype',
  components: {
    Treeselect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 不良类型树选项
      defectTypeOptions: [],
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // Scar不良类型列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        code: null,
        name: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        code: [{ required: true, message: this.$t('scar.defectTypeCodeCannotBeEmpty'), trigger: 'blur' }],
        nameLocalId: [{ required: true, message: this.$t('system.translationCannotBeEmpty'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }]
      },
      upload: {
        open: false,
        title: '',
        isUploading: false,
        headers: getBaseHeader(),
        url: process.env.VUE_APP_BASE_API + '/admin-api/scar/defect-type/import'
      },
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 打开上传不良类型的dialog
    handleImport() {
      this.upload.title = '上传不良类型'
      this.upload.open = true
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.okNum) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.okNum
      }
      if (data.failureNum) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + data.failureNum
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getDefectTypeList(params).then(response => {
        this.list = this.handleTree(response.data, 'id')
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 转换不良类型数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      getDefectTypeList({ ordIds: [] }).then(response => {
        this.defectTypeOptions = []
        const defectType = { id: 0, name: this.$t('supplier.mainCategory'), children: [] }
        defectType.children = this.handleTree(response.data.filter(x => x.level === 1), 'id')
        this.defectTypeOptions.push(defectType)
      })
    },
    /** 表单重置 */
    reset() {
      this.form = {
        code: undefined,
        parentId: undefined,
        nameLocalId: undefined,
        name: undefined
      }
      this.resetForm('form')
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false
      this.isExpandAll = !this.isExpandAll
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      this.open = true
      this.title = this.$t('scar.addScarDefectType')
      // 新增时初始化中文翻译框
      this.$set(this.form, 'translations',
        [{
          translation: '',
          locale: 'zh'
        },
        {
          translation: '',
          locale: 'en'
        }
        ]
      )
      this.getTreeselect()
      if (row != null && row.id) {
        this.form.parentId = row.id
      } else {
        this.form.parentId = 0
      }
      // 默认状态开启
      this.form.status = 0
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getTreeselect()
      getDefectType(row.id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('scar.modifyScarDefectType')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.form.name = this.form.translations?.find(item => item.locale === 'zh').translation
        // 修改的提交
        if (this.form.id != null) {
          updateDefectType(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createDefectType(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm(this.$t('scar.areYouSureToDeleteTheScarDefectTypeNumberAs') + row.name + this.$t('material.dataItemOf')).then(function() {
        return deleteDefectType(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      // 执行导出
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      this.exportLoading = true
      return exportDefectTypeExcel(params).then(response => {
        this.$download.excel(response, this.$t('scar.scarDefectTypexlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    exportAllDefectType() {
      exportDefectTypeExcel({}).then(response => {
        this.$download.excel(response, this.$t('scar.scarDefectTypexlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    doImport() {
      this.$refs.upload.submit()
    }
  }
}
</script>
