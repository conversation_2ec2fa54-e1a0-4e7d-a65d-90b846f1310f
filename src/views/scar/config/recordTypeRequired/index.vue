<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('scar.formName')">
        <el-select v-model="queryParams.scarRecordTypes" :placeholder="$t('system.pleaseSelectAForm')" clearable multiple size="small">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SCAR_RECORD_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('supplier.purchasingOrganization')">
        <el-select v-model="queryParams.orgIds" class="searchValue" multiple>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0)"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('material.factory')">
        <el-select v-model="queryParams.factoryIds" class="searchValue" multiple>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.COMMON_FACTORY, 0)"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.status')">
        <el-select v-model="queryParams.dropStatus" :placeholder="$t('supplier.pleaseSelectStatus')" clearable size="small">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('scar.formTypes')" align="center" prop="scarRecordType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SCAR_RECORD_TYPE" :value="scope.row.scarRecordType" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('supplier.purchasingOrganization')" align="center" prop="orgId">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="scope.row.orgId" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('material.factory')" align="center" prop="factoryId">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_FACTORY" :value="scope.row.factoryId" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" append-to-body width="400px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('scar.formName')" prop="scarRecordType">
          <dict-tag :type="DICT_TYPE.SCAR_RECORD_TYPE" :value="form.scarRecordType" />
        </el-form-item>
        <el-form-item :label="$t('supplier.purchasingOrganization')" prop="orgId">
          <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="form.orgId" />
        </el-form-item>
        <el-form-item v-show="['iqc', 'sc'].includes(form.scarRecordType)" :label="$t('material.factory')" prop="factoryId">
          <dict-tag :type="DICT_TYPE.COMMON_FACTORY" :value="form.factoryId" />
        </el-form-item>
        <el-form-item :label="$t('common.status')" prop="status">
          <el-radio-group v-model="form.selectedStatus">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getRecordTypeRequiredConfig,
  getRecordTypeRequiredConfigPage,
  saveRecordTypeRequiredConfig
} from '@/api/scar/recordtyperequiredconfig'

export default {
  // 邀请评审人必填配置
  name: 'Recordtyperequiredconfig',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 邀请评审人必填配置列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        scarRecordTypes: [],
        orgIds: [],
        factoryIds: [],
        dropStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dropStatus: [{ required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      const params = { ...this.queryParams }
      // 执行查询
      getRecordTypeRequiredConfigPage(params).then(response => {
        this.list = response.data
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined, // 表单的id
        scarRecordType: undefined,
        orgId: undefined,
        selectedStatus: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 处理bool类型的转换
      // COMMON_STATUS("0":开启；“1”：关闭)。需要取反
      if (this.queryParams.dropStatus) {
        this.queryParams.status = true
        if (this.queryParams.dropStatus === '1') {
          this.queryParams.status = false
        }
      }
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.scarRecordTypes = []
      this.queryParams.orgIds = []
      this.queryParams.factoryIds = []
      this.queryParams.dropStatus = null
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('scar.addAMandatoryConfigurationForInvitingReviewers')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true
      this.title = this.$t('scar.modificationInvitationRequired')
      getRecordTypeRequiredConfig({
        type: row.scarRecordType,
        orgId: row.orgId,
        factoryId: row.factoryId
      }).then(response => {
        response.data.selectedStatus = response.data.status ? 0 : 1
        this.form = response.data
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 处理bool类型的转换
        // COMMON_STATUS("0":开启；“1”：关闭)。需要取反
        if (this.form.selectedStatus === 0) {
          this.form.status = true
        } else {
          this.form.status = false
        }
        // 添加的提交
        saveRecordTypeRequiredConfig(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('order.operationSucceeded'))
          this.open = false
          this.getList()
        })
      })
    }
  }
}
</script>
