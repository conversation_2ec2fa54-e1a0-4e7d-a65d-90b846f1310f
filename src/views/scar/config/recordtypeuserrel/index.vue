<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">

      <el-form-item :label="$t('auth.documentType')" prop="scarRecordType">
        <el-select v-model="queryParams.scarRecordType" class="searchValue" clearable>
          <el-option
            v-for="type in scarTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('supplier.purchasingOrganization')" prop="orgId">
        <el-select v-model="queryParams.orgId" class="searchValue" clearable>
          <el-option v-for="item in orgList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('material.factory')" prop="factoryId">
        <el-select v-model="queryParams.factoryId" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.COMMON_FACTORY, 0)"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('scar.sqe')" prop="userIds">
        <el-select v-model="queryParams.userIds" class="searchValue" filterable multiple>
          <el-option v-for="item in userList" :key="item.id" :label="item.nickname" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>
    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['scar:record-type-user-rel-config:save']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="configSqe"
        >{{ $t('scar.sqeConfiguration') }}
        </el-button>
      </el-col>
      <!--        批量创建-->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['scar:record-type-user-rel-config:import']"
          :loading="exportLoading"
          icon="el-icon-upload2"
          size="mini"
          type="primary"
          @click="handleImport"
        >{{ $t('supplier.batchImport') }}
        </el-button>
      </el-col>
      <!--        批量导出-->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['scar:record-type-user-rel-config:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          type="primary"
          @click="handleExport"
        >{{ $t('common.batchExport') }}
        </el-button>
      </el-col>

      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table
      ref="multipleTable"
      v-loading="loading"
      :data="list"
      class="disabledAllCheckBox"
      @select="selectCheck"
      @selection-change="handleSelectionChange"
      @row-click="clickRow"
    >
      <el-table-column
        type="selection"
        width="30"
      />
      <el-table-column :label="$t('scar.formName')" align="center" prop="scarRecordType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SCAR_RECORD_TYPE" :value="scope.row.scarRecordType" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('supplier.purchasingOrganization')" align="center" prop="orgId">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="scope.row.orgId" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('material.factory')" align="center" prop="factoryId">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_FACTORY" :value="scope.row.factoryId" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('scar.sqe')" align="left" prop="users" />
    </el-table>

    <!-- 采购配置 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="400px">
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item :label="$t('supplier.user')" prop="userIds">
          <el-select v-model="form.userIds" class="searchValue" filterable multiple>
            <el-option v-for="item in userListNew" :key="item.id" :label="item.nickname" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url + '?type=' + upload.type"
          :auto-upload="false"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>

        <el-button
          v-hasPermi="['scar:record-type-user-rel-config:export']"
          plain
          :loading="exportLoading"
          type="primary"
          @click="handleExport"
        >{{ $t('common.downloadTemplate') }}
        </el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPurchaseOrgCache } from '@/api/system/purchaseOrg'
import { getUsersByOrgId, getUsersCache } from '@/api/system/user'
import { getBaseHeader } from '@/utils/request'
import {
  exportRecordTypeUserRelExcel,
  getRecordTypeUserRelConfigList,
  getUsersByTypeAndOrgId,
  saveRecordTypeUserRelConfig
} from '@/api/scar/recordtypeuserrelconfig'

export default {
  // 表单任务接收人配置
  // 【UFFF-2153】移除SP、SA类型
  name: 'Userrel',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应商与人员关系列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 当前维护的人员类型
      type: '',
      // 查询参数
      queryParams: {
        orgId: null,
        userIds: [],
        locale: null,
        scarRecordType: ''
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 人员类型:Sourcing
        type: 'SQE',
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/scar/record-type-user-rel-config/import'
      },
      // 表单参数
      form: {
        userIds: []
      },
      orgList: [],
      userList: [],
      userListNew: [],
      scarRecordTypeList: [],
      // 选中数组值
      multipleSelection: [],
      scarTypes: []
    }
  },
  async mounted() {
    await this.getOrgList()
    await this.getUserList()
    this.getList()
    this.type = this.$route.params.type
    this.scarTypes = this.getDictDatas(this.DICT_TYPE.SCAR_RECORD_TYPE, 0).filter(item => ['iqc', 'sc'].includes(item.value))
  },
  methods: {
    /** 批量创建 */
    handleImport() {
      this.upload.title = this.$t('common.batchCreation')
      this.upload.open = true
      this.upload.type = 'SQE'
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createUsers) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createUsers.length
      }
      if (data.failureUsers) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureUsers).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    clickRow(row) {
      this.$refs.multipleTable.clearSelection()
      this.$refs.multipleTable.toggleRowSelection(row, true)
      this.multipleSelection = [row]
    },
    /**
     * 选择框
     * @param val
     */
    handleSelectionChange(val) {
      console.log(val)
      // this.multipleSelection =  val
    },
    selectCheck(selection, row) {
      console.log(row)
      this.$refs.multipleTable.clearSelection()
      this.$refs.multipleTable.toggleRowSelection(row, true)
      this.multipleSelection = [row]
    },
    /**
     * 获取人员列表
     */
    getUserList() {
      getUsersCache({
        status: 0,
        isExternal: 1
      }).then(response => {
        this.userList = []
        this.userList.push(...response.data)
      })
    },
    /**
     * 获取采购组织列表
     */
    getOrgList() {
      getPurchaseOrgCache({
        status: 0
      }).then(response => {
        this.orgList = []
        this.orgList.push(...response.data)
      })
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getRecordTypeUserRelConfigList(this.queryParams).then(response => {
        this.list = response.data
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        supplierId: undefined,
        orgId: undefined,
        userId: undefined,
        userIds: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** SQE配置功能 */
    configSqe() {
      if (this.multipleSelection == null || this.multipleSelection.length === 0) {
        this.$modal.msgError(this.$t('supplier.pleaseSelectData'))
        return
      } else {
        if (this.multipleSelection.length > 1) {
          this.$modal.msgError(this.$t('scar.pleaseSelectADocument'))
          return
        }
        // 采购组织未配置工厂时，该条数据不允许配置SQE
        if (!this.multipleSelection[0].factoryId) {
          this.$modal.msgError(this.$t('scar.thePurchasingOrganizationHasNotConfiguredAFactoryPleaseConfigureTheFactoryFirst'))
          return
        }
        this.reset()
        this.open = true
        this.title = this.$t('scar.configureFormsAndPersonnelRelationships')
        // 根据采购组织获取人员数据
        if (this.multipleSelection.length === 1) {
          const selectedOrgId = this.multipleSelection[0].orgId
          const selectedFactoryId = this.multipleSelection[0].factoryId
          console.log('this.multipleSelection[0]', this.multipleSelection[0])
          getUsersByOrgId({
            orgId: selectedOrgId
          }).then(response => {
            this.userListNew = []
            this.userListNew.push(...response.data)
          })
          // 获得拥有的人员集合
          getUsersByTypeAndOrgId(
            {
              type: this.multipleSelection[0].scarRecordType,
              orgId: selectedOrgId,
              factoryId: selectedFactoryId
            }
          ).then(response => {
            // 设置选中
            this.form.userIds = response.data
          })
        }
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        this.form.scarRecordType = this.multipleSelection[0].scarRecordType
        this.form.orgId = this.multipleSelection[0].orgId
        this.form.factoryId = this.multipleSelection[0].factoryId
        // 修改的提交
        saveRecordTypeUserRelConfig(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      this.queryParams.type = 'SQE'
      // 处理查询参数
      const params = { ...this.queryParams }
      // 执行导出
      this.exportLoading = true
      exportRecordTypeUserRelExcel(params).then(res => {
        this.$download.excel(res, this.$t('scar.formTaskRecipientXlsx'))
        this.exportLoading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.disabledAllCheckBox{
  ::v-deep .el-table__header {
    .el-table-column--selection{
      .cell{
        display: none;
      }
    }
  }
}
</style>
