<template>
  <div>
    <div style="margin-top: 30px; margin-left: 20px">
      <span style="font-size: larger">{{ $t('scar.replyDateConfiguration') }}</span>
    </div>
    <el-divider />
    <div style="width: 800px; margin-left: 200px">
      {{ $t('scar.releaseDatePlus') }}
      <el-input-number v-model="containmentActionDate" :max="9999" :min="0" />
      {{ $t('scar.oneNaturalDayIsEqualToTheDateWhenTheSupplierIsRequiredToRespondToTheContainmentMeasures') }}
    </div>
    <div style="margin-top: 20px;margin-bottom: 20px" />
    <div style="width: 800px; margin-left: 200px">
      {{ $t('scar.releaseDatePlus') }}
      <el-input-number v-model="permanentActionDate" :max="9999" :min="0" />
      {{ $t('scar.oneNaturalDayIsEqualToTheDateWhenTheSupplierIsRequiredToRespondToLongtermCountermeasures') }}
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :offset="5" :span="1.5">
        <el-button
          v-hasPermi="['scar:reply-date-config:update']"
          size="medium"
          type="primary"
          @click="handleUpdateForSpot"
        >{{ $t('common.save') }}
        </el-button>
      </el-col>
    </el-row>

    <el-divider />
  </div>
</template>

<script>
import { getConfig, updateConfig } from '@/api/scar/replyDate'

export default {
  name: 'Replydate',
  components: {},
  data() {
    return {
      containmentActionDate: 0,
      permanentActionDate: 0
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      // 获取当前配置项
      getConfig().then(res => {
        if (res.data.containmentActionDate) {
          this.containmentActionDate = res.data.containmentActionDate
        }
        if (res.data.permanentActionDate) {
          this.permanentActionDate = res.data.permanentActionDate
        }
        this.id = res.data.id
      })
    },
    //
    handleUpdateForSpot() {
      const obj = {
        containmentActionDate: this.containmentActionDate,
        permanentActionDate: this.permanentActionDate,
        id: this.id
      }
      updateConfig(obj).then(res => {
        this.$message.success(this.$t('common.updateSuccessful'))
      }).catch(_ => {
        this.$message.error(this.$t('order.updateFailed'))
      })
    }
  }
}
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
  padding-top: 10px;
}
</style>
