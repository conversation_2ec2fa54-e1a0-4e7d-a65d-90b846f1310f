<template>
  <!--入口页面-->
  <div class="app-container">
    <div class="app-title">
      <div class="app-title-tab">
        <span :class="{'app-title-tab-selected': selectedTab === 'Scar'}" @click="switchTab('Scar')">Scar</span>
        <span :class="{'app-title-tab-selected': selectedTab === 'Overview'}" @click="switchTab('Overview')">Overview</span>
      </div>
      <div style="flex: 0 1 940px">

      </div>
    </div>

    <!-- 使用v-if重新渲染组件 -->
    <scar-list
      v-if="selectedTab === 'Scar'"
      :key="'scar-' + tabKey"
      :item-arr="itemArr"
      @copy-address="handleCopyAddress"
      @postpone-record="handlePostponeRecord"
      @handle-log="handleLog"
      @update-assignee="handleUpdateAssignee"
    />

    <scar-overview
      v-if="selectedTab === 'Overview'"
      :key="'overview-' + tabKey"
      @switch-tab="switchTab"
    />


    <!--    质量单据的操作记录-->
    <el-dialog :visible="openOperateRecord" :title="$t('common.operationRecord')"
class="logDialog" @close="openOperateRecord = false">
      <el-table :data="operateRecordList" style="width: 100%">
        <el-table-column :label="$t('auth.operationNode')" width="180">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SCAR_OPERATE_STEP" :value="scope.row.operatorSection" />
          </template>
        </el-table-column>
        <el-table-column prop="operator" :label="$t('system.operator')" width="180">
          <template #default="scope">
            <span>{{ scope.row.operator }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="recordCreateTime" :label="$t('common.createTime')" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.recordCreateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="operateTime" :label="$t('system.operationTime')" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.operateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.operate')" width="180">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SCAR_OPERATE_TYPE" :value="scope.row.operate" />
          </template>
        </el-table-column>
        <el-table-column prop="checkContent" :label="$t('auth.approvalComments')" width="180" />
      </el-table>
    </el-dialog>

    <!--    单据延期的弹框-->
    <el-dialog :visible="openPostponeRecord" :title="$t('scar.documentExtension')" @close="beforeClosePostponeRecord">
      <el-form ref="postponeRecordForm" :model="postponeParams">
        <el-form-item
          :label="$t('scar.latestRequestForSupplierResponseDate')"
          :rules="{ required: true, message: $t('scar.extensionDateRequired'), trigger: ['blur', 'change'] }"
          prop="postponeDate"
        >
          <el-date-picker
            v-model="postponeParams.postponeDate"
            label-width="100px"
            type="date"
            :placeholder="$t('common.pleaseSelectADate')"
            :picker-options="pickerOptions"
          />
        </el-form-item>
        <el-form-item
          :label="$t('scar.reasonForDelay')"
          :rules="{ required: true, message: $t('scar.reasonForExtensionIsRequired'), trigger: ['blur', 'change'] }"
          prop="postponeReason"
        >
          <el-input
            v-model="postponeParams.postponeReason"
            type="textarea"
            :rows="3"
            :placeholder="$t('rfq.pleaseEnterTheContent')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button size="mini" @click="beforeClosePostponeRecord">{{ $t('common.cancel') }}</el-button>
        <el-button size="mini" type="primary" @click="doPostponeRecord">{{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>

    <!--    新建质量单据弹框-->
    <el-dialog :visible="openCreateRecord" :title="$t('scar.selectDocument')" @close="afterCloseDialog">
      <el-radio-group v-model="newScarType">
        <div v-for="dict in getDictDatas(DICT_TYPE.SCAR_RECORD_TYPE, 0)">
          <el-radio
            style="padding-top: 15px"
            :label="dict.value"
          >
            {{ dict.label }}</el-radio>
        </div>
      </el-radio-group>
      <div slot="footer">
        <el-button size="mini" @click="afterCloseDialog">{{ $t('common.cancel') }}</el-button>
        <el-button
          size="mini"
          type="primary"
          @click="toCreateNewRecord"
        >{{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <!-- 对话框(转派审批人) -->
    <el-dialog :title="$t('system.transferApprover')" :visible.sync="updateAssignee.open" append-to-body width="500px">
      <el-form ref="updateAssigneeForm" :model="updateAssignee.form" :rules="updateAssignee.rules" label-width="110px">
        <el-form-item :label="$t('system.newApprover')" prop="assigneeUserId">
          <el-select v-model="updateAssignee.form.assigneeUserId" style="width: 100%" class="searchValue" clearable filterable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
              :key="parseInt(item.id)"
              :label="item.nickname"
              :value="parseInt(item.id)"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelUpdateAssigneeForm">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitUpdateAssigneeForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
    <el-dialog
      :title="$t('common.shareInvitationLink')"
      :visible.sync="ShareInvitationLink.open"
      append-to-body
      width="500px"
    >
      <el-form :model="ShareInvitationLink" label-width="130px">
        <span>{{ ShareInvitationLink.supplierInvitationAddress }}</span>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-clipboard:copy="ShareInvitationLink.supplierInvitationAddress" v-clipboard:success="()=>{$modal.msgSuccess($t('system.successfullyCopied'));ShareInvitationLink.open =false}" type="primary">{{ $t('order.copy') }}</el-button>

        <el-button @click="ShareInvitationLink.open = false">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE } from '@/utils/dict'
import {
  getDataOperateRecordPage,
  postponeRecord,
  statisticsCard,
  updateTaskAssignee
} from '@/api/scar'
import dayjs from 'dayjs'
import event from '@/views/dashboard/mixins/event'
import ScarList from './components/scarList.vue'
import ScarOverview from './components/overview.vue'

export default {
  name: 'ScarIndex',
  components: {
    ScarList,
    ScarOverview
  },
  mixins: [event],

  data() {
    return {
      DICT_TYPE,
      parseTime,
      // 当前选中的标签页
      selectedTab: 'Scar',
      // 用于强制重新渲染的key
      tabKey: 0,
      // 统计卡片数据
      itemArr: [],
      // 单据操作记录的弹框控制
      openOperateRecord: false,
      operateRecordList: [],
      // 单据延期的弹框控制
      openPostponeRecord: false,
      postponeParams: {
        id: '',
        // 延期日期
        postponeDate: '',
        // 延期理由
        postponeReason: ''
      },
      // 转办人
      updateAssignee: {
        open: false,
        form: {
          assigneeUserId: undefined
        },
        rules: {
          assigneeUserId: [{ required: true, message: this.$t('system.newApproverCannotBeEmpty'), trigger: 'change' }]
        }
      },
      // 分享邀请链接
      ShareInvitationLink: {
        // 是否显示弹出层
        open: false,
        // 供应商名称
        supplierInvitationAddress: ''
      },
      pickerOptions: {
        disabledDate(time) {
          return dayjs().isAfter(dayjs(time), 'day')
        }
      }
    }
  },
  mounted() {
    this.getStatisticsCard()
  },
  methods: {
    // 切换标签页
    switchTab(tab) {
      if (this.selectedTab !== tab) {
        this.selectedTab = tab
        // 增加key值强制重新渲染
        this.tabKey++
      }
    },

    // 获取统计卡片数据
    getStatisticsCard() {
      statisticsCard().then(res => {
        this.itemArr = [
          { label: 'YTD 发布SCAR', value: res.data.publishCount },
          { label: 'YTD 发布进料检验改善单', value: res.data.publishIqcCount },
          { label: 'YTD 发布在线不良改善单', value: res.data.publishScCount },
          { label: 'YTD 已完成SCAR个数', value: res.data.completedCount },
          { label: 'YTD 进行中SCAR', value: res.data.underWayCount }
        ]
      }).catch(_ => {
      })
    },

    // 处理复制地址
    handleCopyAddress(address) {
      this.ShareInvitationLink.open = true
      this.ShareInvitationLink.supplierInvitationAddress = address
    },

    // 处理延期记录
    handlePostponeRecord(id) {
      this.postponeParams.id = id
      this.openPostponeRecord = true
    },

    // 处理日志
    handleLog(row) {
      this.openOperateRecord = true
      getDataOperateRecordPage(row.id).then(res => {
        this.operateRecordList = res.data
      })
    },
    // 处理转派审批人
    handleUpdateAssignee(id) {
      // 设置表单
      this.resetUpdateAssigneeForm()
      this.updateAssignee.form.id = id
      // 设置为打开
      this.updateAssignee.open = true
    },

    // 重置转派审批人
    resetUpdateAssigneeForm() {
      this.updateAssignee.form = {
        id: undefined,
        assigneeUserId: undefined
      }
      this.resetForm('updateAssigneeForm')
    },

    // 提交转派审批人
    submitUpdateAssigneeForm() {
      this.$refs['updateAssigneeForm'].validate(valid => {
        if (!valid) {
          return
        }
        updateTaskAssignee(this.updateAssignee.form).then(response => {
          this.$modal.msgSuccess(this.$t('system.successfullyTransferredTask'))
          this.updateAssignee.open = false
          // 刷新当前组件
          this.tabKey++
        })
      })
    },

    // 取消转派审批人
    cancelUpdateAssigneeForm() {
      this.updateAssignee.open = false
      this.resetUpdateAssigneeForm()
    },

    // 单据延期
    doPostponeRecord() {
      this.$refs.postponeRecordForm.validate(async valid => {
        if (!valid) {
          return
        }
        postponeRecord({
          scarId: this.postponeParams.id,
          latestDate: this.postponeParams.postponeDate,
          reason: this.postponeParams.postponeReason
        }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.openPostponeRecord = false
          // 刷新当前组件
          this.tabKey++
        })
      })
    },

    // 关闭单据延期弹框的事件处理
    beforeClosePostponeRecord() {
      this.openPostponeRecord = false
      this.postponeParams.postponeDate = ''
      this.postponeParams.postponeReason = ''
      if (this.$refs['postponeRecordForm']) {
        this.$refs['postponeRecordForm'].resetFields()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.greenDot {
  height: 20px;
  width: 20px;
  background-color: #008000;
  border-radius: 50%;
  display: inline-block;
}
.redDot {
  height: 20px;
  width: 20px;
  background-color: #ff0000;
  border-radius: 50%;
  display: inline-block;
}
.searchItem{
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 170px);
  }
}
.searchValue{
  width: 95%;
}

.app-title{
  display: flex;
  margin-bottom: 0px;
  justify-content: space-between;
  &-tab{
    flex: none;
    font-size: 24px;
    color: #383838;
    letter-spacing: 0;
    font-weight: 400;
    cursor: pointer;
    span{
      margin: 0 15px;
    }
    &-selected{
      font-size: 28px;
      color: #383838;
      letter-spacing: 0;
      font-weight: 700;
      position: relative;
    }
    &-selected::after {
      content: "";
      position: absolute;
      border-radius: 2px;
      width: 20px; /* Adjust the width of the line */
      height: 4px; /* Adjust the thickness of the line */
      background-color: black; /* Adjust the line color */
      bottom: -6px; /* Adjust the position of the line relative to the text */
      left: 50%;
      transform: translateX(-50%);
    }
  }
}

</style>
