<template>
  <!--  scar基本信息-->
  <div>
    <common-card
      :title="$t('scar.scarBasicInformationIncomingInspectionImprovementFormIqc')"
    >
      <div>
        <el-form
          ref="baseInfo"
          :model="scarInfo"
          :rules="baseInfoRule"
          inline
          label-width="212px"
        >
          <div style="font-weight: bold">{{ $t('scar.factoryInformation') }}</div>
          <el-form-item
            class="commonFormItem"
            :label="$t('material.factory')"
            prop="factoryId"
          >
            <show-or-edit
              :disabled="baseDisable"
              :value="scarInfo.factoryId"
              :dict="DICT_TYPE.COMMON_FACTORY"
            >
              <el-select
                v-model="scarInfo.factoryId"
                :disabled="baseDisable"
                :placeholder="$t('material.pleaseSelectAFactory')"
                class="content"
                clearable
                filterable
              >
<!--              带当前用户权限的工厂数据（目前工厂没有直接到人的关系，需要走采购组中转下。 人到采购组织；采购组织到人）-->
                <el-option
                  v-for="item in getDictDatas(DICT_TYPE.COMMON_FACTORY_WITH_AUTH)"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('supplier.purchasingOrganization')"
            prop="orgId"
          >
            <show-or-edit
              :disabled="baseDisable"
              :value="scarInfo.orgId"
              :dict="DICT_TYPE.COMMON_PURCHASEORG"
            >
              <el-select
                v-model="scarInfo.orgId"
                :disabled="baseDisable"
                :placeholder="$t('auth.pleaseSelectAPurchasingOrganization')"
                class="content"
                clearable
                filterable
              >
                <el-option
                  v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0)"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('supplier.supplierName')"
            prop="supplierName"
          >
            <show-or-edit
              :value="scarInfo.supplierName"
              :disabled="baseDisable"
              :custom-list="supplierList"
            >
              <el-select
                v-model="scarInfo.supplierName"
                :disabled="baseDisable"
                :placeholder="$t('scar.pleaseEnterKeywordsForFuzzySearch')"
                :remote-method="getSupplierDetail"
                class="content"
                clearable
                filterable
                remote
                @change="selectSupplier"
              >
                <el-option
                  v-for="dict in supplierList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.name"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('supplier.supplierCode')"
            prop="supplierCode"
          >
            <show-or-edit
              :value="scarInfo.supplierCode"
              :disabled="true"
            >
              <el-input
                v-model="scarInfo.supplierCode"
                class="content"
                disabled
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item class="commonFormItem" prop=" " />

          <el-form-item
            class="commonFormItem"
            :label="$t('scar.qualityPersonnelEmail')"
            prop="emails"
          >
            <show-or-edit
              :value="scarInfo.emails"
              :disabled="baseDisable"
            >
              <el-input
                v-model="scarInfo.emails	"
                :disabled="baseDisable"
                class="content"
              />
            </show-or-edit>
          </el-form-item>
          <div style="font-weight: bold">{{ $t('scar.materialInformation') }}</div>
          <el-form-item
            :label="$t('material.materialCode')"
            class="commonFormItem"
            prop="materialCode"
          >
            <show-or-edit
              :value="scarInfo.materialCode"
              :disabled="baseDisable"
            >
              <el-autocomplete
                v-model="scarInfo.materialCode	"
                :disabled="baseDisable"
                :fetch-suggestions="querySearchAsync"
                :placeholder="$t('rfq.pleaseEnterTheContent')"
                class="content"
                @select="handleMaterialCodeSelect"
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('material.category')"
            prop="categoryId"
          >
            <show-or-edit
              :disabled="baseDisable"
              :value="scarInfo.categoryId"
              :dict="DICT_TYPE.COMMON_CATEGORY"
            >
              <cascading-category
                :disabled="baseDisable"
                :multiple="false"
                :original-value.sync="scarInfo.categoryId"
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('material.materialDescription')"
            class="commonFormItem"
            prop="materialDescription"
          >
            <show-or-edit
              :value="scarInfo.materialDescription"
              :disabled="baseDisable"
            >
              <el-input
                v-model="scarInfo.materialDescription"
                :disabled="baseDisable"
                class="content"
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('scar.inspectionBatchNumber')"
            class="commonFormItem"
            prop="inspectionNo"
          >
            <show-or-edit
              :value="scarInfo.inspectionNo"
              :disabled="baseDisable"
            >
              <el-input
                v-model="scarInfo.inspectionNo"
                class="content"
                :disabled="baseDisable"
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('PO#')"
            class="commonFormItem"
            prop="poNumber"
          >
            <show-or-edit
              :value="scarInfo.poNumber"
              :disabled="baseDisable"
              :custom-list="poList"
            >
              <el-select
                v-model="scarInfo.poNumber"
                :disabled="baseDisable"
                :placeholder="$t('scar.pleaseEnterKeywordsForFuzzySearch')"
                :remote-method="getPoNumber"
                class="content"
                clearable
                filterable
                remote
                @change="selectPoNO"
              >
                <el-option
                  v-for="dict in poList"
                  :key="dict.key"
                  :label="dict.value"
                  :value="dict.key"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>

          <div style="font-weight: bold">{{ $t('scar.improvementRequirements') }}</div>
          <el-form-item
            class="commonFormItem"
            :label="$t('scar.typesOfDefects')"
            prop="qualityBadType"
          >
            <show-or-edit
              :value="scarInfo.qualityBadType"
              :disabled="baseDisable"
              :dict="DICT_TYPE.SCAR_DEFECT_TYPE"
            >
              <cascading
                ref="badTypeCasRef"
                :disabled="baseDisable"
                :multiple="false"
                :original-value.sync="scarInfo.qualityBadType"
                :org-id="scarInfo.orgId"
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('scar.sourceOfTheProblem')"
            prop="questionFromType"
          >
            <show-or-edit
              :value="scarInfo.questionFromType"
              :disabled="baseDisable"
              :custom-list="sourceProblemList"
            >
              <el-select
                v-model="scarInfo.questionFromType"
                :disabled="baseDisable"
                class="content"
                :placeholder="$t('common.pleaseSelect')"
                clearable
              >
                <el-option
                  v-for="dict in sourceProblemList"
                  :key="dict.id"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('scar.qualityComplaintType')"
            class="commonFormItem"
            prop="qualityComplaintType"
          >
            <show-or-edit
              :value="scarInfo.qualityComplaintType"
              :disabled="baseDisable"
              :dict="DICT_TYPE.SCAR_QUANTITY_COMPLAIN_TYPE"
            >
              <el-select
                v-model="scarInfo.qualityComplaintType"
                :disabled="baseDisable"
                class="content"
                :placeholder="$t('common.pleaseSelect')"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SCAR_QUANTITY_COMPLAIN_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('scar.permanentCountermeasureRequestReplyDate')"
            prop="permanentActionDate"
          >
            <show-or-edit
              type="Date"
              :value="scarInfo.permanentActionDate"
              :disabled="baseDisable"
            >
              <el-date-picker
                v-model="scarInfo.permanentActionDate"
                :disabled="baseDisable"
                :picker-options="pickerOptions"
                :placeholder="$t('common.pleaseSelectADate')"
                placement="bottom-start"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('scar.responseDateRequiredForContainmentMeasures')"
            prop="containmentActionDate"
          >
            <show-or-edit
              type="Date"
              :value="scarInfo.containmentActionDate"
              :disabled="baseDisable"
            >
              <el-date-picker
                v-model="scarInfo.containmentActionDate"
                :disabled="baseDisable"
                :picker-options="pickerOptions"
                :placeholder="$t('common.pleaseSelectADate')"
                placement="bottom-start"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('scar.severity')"
            class="commonFormItem"
          >
            <show-or-edit
              :value="scarInfo.severityLevel"
              :disabled="baseDisable"
              :dict="DICT_TYPE.SCAR_SEVERITY_DEGREE"
            >
              <el-select
                v-model="scarInfo.severityLevel"
                :disabled="baseDisable"
                class="content"
                :placeholder="$t('common.pleaseSelect')"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SCAR_SEVERITY_DEGREE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('scar.selectTheTypeOfDocumentToPublish')"
            prop="iqcScType"
          >
            <el-radio-group
              v-model="scarInfo.iqcScType"
              :disabled="baseDisable"
            >
              <el-radio
                v-for="dict in publishType"
                :key="dict.value"
                :disabled="dict.disable"
                :label="dict.value"
              >{{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('scar.areThereAnyDuplicateDefects')"
          >
            <show-or-edit
              type="Boolean"
              :disabled="baseDisable"
              :value="scarInfo.isRepetitionBad"
            >
              <el-radio-group
                v-model="scarInfo.isRepetitionBad"
                :disabled="baseDisable"
              >
                <el-radio :label="true">{{ $t('auth.yes') }}</el-radio>
                <el-radio :label="false">{{ $t('auth.no') }}</el-radio>
              </el-radio-group>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('scar.whetherToStopTheLine')"
          >
            <show-or-edit
              type="Boolean"
              :disabled="baseDisable"
              :value="scarInfo.isStopLine"
            >
              <el-radio-group
                v-model="scarInfo.isStopLine"
                :disabled="baseDisable"
              >
                <el-radio :label="true">{{ $t('auth.yes') }}</el-radio>
                <el-radio :label="false">{{ $t('auth.no') }}</el-radio>
              </el-radio-group>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="baseDisable?$t('scar.seeFile'):$t('common.uploadFile')"
          >
            <scar-file
              :business-value="'iqc'"
              :disabled="baseDisable"
            />
          </el-form-item>
          <el-form-item
            :label="$t('scar.problemDescription')"
            prop="questionDescText"
            style="width: 100%;margin-right: 0;margin-top: 10px"
          >
            <div v-if="baseDisable" v-html="scarInfo.questionDescText" />
            <tinymce v-else v-model="scarInfo.questionDescText	" :height="160" />
          </el-form-item>
          <div>
            <el-button type="text" @click="showDetail= !showDetail">{{ $t('system.more') }}</el-button>
          </div>
          <div v-show="showDetail">

            <el-form-item
              :label="$t('scar.scarCode')"
              class="commonFormItem"
            >
              <show-or-edit
                :value="scarInfo.scarCode"
                :disabled="true"
              >
                <el-input
                  v-model="scarInfo.scarCode"
                  disabled
                  class="content"
                />
              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('common.creationDate')"
              class="commonFormItem"
            >
              <show-or-edit
                :value="scarInfo.createDate"
                :disabled="true"
              >
                <el-input
                  v-model="scarInfo.createDate"
                  class="content"
                  disabled
                />
              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('rfq.createdBy')"
              class="commonFormItem"
            >
              <dict-tag :type="DICT_TYPE.COMMON_USERS_INCLUDES_ALL" :value="scarInfo.creator" />
            </el-form-item>
            <el-form-item
              :label="$t('order.orderNumber')"
              class="commonFormItem"
            >
              <show-or-edit
                :value="scarInfo.orderNo"
                :disabled="baseDisable"
              >
                <el-input
                  v-model="scarInfo.orderNo"
                  class="content"
                  :disabled="baseDisable"
                />
              </show-or-edit>
            </el-form-item>

            <el-form-item
              :label="$t('scar.batchQuantityOfIncomingMaterials')"
              class="commonFormItem"
            >
              <show-or-edit
                :value="scarInfo.incomingMaterialQuantity"
                :disabled="baseDisable"
              >
                <vxe-input
                  v-model.number="scarInfo.incomingMaterialQuantity"
                  :disabled="baseDisable"
                  type="number"
                  class="content"
                  @blur="fixeNumber('incomingMaterialQuantity')"
                />
              </show-or-edit>
            </el-form-item>

            <el-form-item
              :label="$t('material.specificationAndModel')"
              class="commonFormItem"
            >
              <show-or-edit
                :value="scarInfo.specifications"
                :disabled="baseDisable"
              >
                <el-input
                  v-model="scarInfo.specifications	"
                  :disabled="baseDisable"
                  class="content"
                />
              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('order.batchNo')"
              class="commonFormItem"
            >
              <show-or-edit
                :value="scarInfo.batchNo"
                :disabled="baseDisable"
              >
                <el-input
                  v-model="scarInfo.batchNo"
                  :disabled="baseDisable"
                  class="content"
                />
              </show-or-edit>
            </el-form-item>
            <el-form-item
              class="commonFormItem"
              :label="$t('scar.inspectionMethod')"
            >
              <show-or-edit
                :value="scarInfo.inspectType"
                :disabled="baseDisable"
                :dict="DICT_TYPE.SCAR_INSPECTION_METHOD"
              >
                <el-select
                  v-model="scarInfo.inspectType	"
                  :disabled="baseDisable"
                  class="content"
                  :placeholder="$t('common.pleaseSelect')"
                  clearable
                >
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.SCAR_INSPECTION_METHOD)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </show-or-edit>
            </el-form-item>

            <el-form-item
              :label="$t('material.manufacturer')"
              class="commonFormItem"
            >
              <show-or-edit
                :value="scarInfo.mfg"
                :disabled="baseDisable"
              >
                <el-input
                  v-model="scarInfo.mfg"
                  :disabled="baseDisable"
                  class="content"
                />
              </show-or-edit>
            </el-form-item>
            <el-form-item
              v-if="scarInfo.inspectType === 'random'"
              class="commonFormItem"
              :label="$t('scar.numberOfSamples')"
            >
              <show-or-edit
                style="flex: 0 1 33%"
                :value="scarInfo.selectiveInspectSampleQuantity"
                :disabled="baseDisable"
              >
                <el-input
                  v-model="scarInfo.selectiveInspectSampleQuantity"
                  :disabled="baseDisable"
                  class="content"
                  @blur="fixeNumber('selectiveInspectSampleQuantity')"
                />
              </show-or-edit>

            </el-form-item>
            <el-form-item
              v-else-if="scarInfo.inspectType === 'full'"
              class="commonFormItem"
              :label="$t('scar.numberOfDefects')"
            >
              <show-or-edit
                style="flex: 0 1 33%"
                :value="scarInfo.fullInspectDefectQuantity"
                :disabled="baseDisable"
              >
                <vxe-input
                  v-model.number="scarInfo.fullInspectDefectQuantity"
                  :disabled="baseDisable"
                  type="number"
                  class="content"
                  @blur="fixeNumber('fullInspectDefectQuantity')"
                />
              </show-or-edit>

            </el-form-item>

            <el-form-item v-else class="commonFormItem" prop=" " />
            <el-form-item
              class="commonFormItem"
              :label="$t('scar.manufacturersNumber')"
            >
              <show-or-edit
                :value="scarInfo.mpn"
                :disabled="baseDisable"
              >
                <el-input
                  v-model="scarInfo.mpn"
                  :disabled="baseDisable"
                  class="content"
                />
              </show-or-edit>
            </el-form-item>
            <!--校验方式-抽检-->

            <el-form-item
              v-if="scarInfo.inspectType === 'random'"
              class="commonFormItem"
              :label="$t('scar.numberOfDefects')"
            >
              <show-or-edit
                style="flex: 0 1 33%"
                :value="scarInfo.fullInspectDefectQuantity"
                :disabled="baseDisable"
              >
                <vxe-input
                  v-model.number="scarInfo.fullInspectDefectQuantity"
                  :disabled="baseDisable"
                  type="number"
                  class="content"
                  @blur="fixeNumber('fullInspectDefectQuantity')"
                />
              </show-or-edit>

            </el-form-item>
            <el-form-item
              v-else-if="scarInfo.inspectType === 'full'"
              class="commonFormItem"
              :label="$t('scar.defectiveRate')"
            >
              <show-or-edit
                style="flex: 0 1 33%"
                :value="scarInfo.inspectDefectPercent"
                :disabled="true"
              >
                <el-input
                  v-model.number="scarInfo.inspectDefectPercent"
                  class="content"
                  disabled
                  type="number"
                  @blur="fixeNumber('inspectDefectPercent')"
                >
                  <span slot="suffix" style="font-size: 20px"> %</span>
                </el-input>
              </show-or-edit>

            </el-form-item>
            <el-form-item v-else class="commonFormItem" prop=" " />

          </div>

        </el-form>
        <div v-if="!baseDisable" class="fixedBottom">
          <back-record v-if="scarInfo.scarStatus=== 'to_be_released'" />

          <!--  extra：Jerry：待发布状态下，提交到SQE置灰    -->
          <el-button
            v-has-permi="['scar:record:submit-sqe']"
            plain
            :disabled="'to_be_released' === scarInfo?.scarStatus"
            class="commonBtn"
            type="primary"
            @click="toSQE"
          >{{ $t('scar.submitToSqe') }}
          </el-button>
          <el-button v-has-permi="['scar:record:submit']" class="commonBtn" type="primary" @click="toSupplier">{{ $t('scar.publishToSuppliers') }}</el-button>
          <el-button v-has-permi="['scar:record:save']" plain class="commonBtn" type="primary" @click="saveBaseInfo">{{ $t('common.save') }}</el-button>
        </div>
      </div>
    </common-card>
  </div>

</template>

<script>

import tinymce from '@/components/tinymce'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import {
  getSourceOfProblem,
  getSupplierDetail,
  saveRecord,
  submitIqc,
  submitSqe,
  undoRecord,
  validRepeatDefect
} from '@/api/scar'
import { getMultiContactSimple } from '@/api/supplier/info'
import Cascading from '@/views/scar/componets/cascading.vue'
import ScarFile from '@/views/scar/componets/scarFile.vue'
import dayjs from 'dayjs'
import backRecord from '@/views/scar/componets/backRecord.vue'
import ShowOrEdit from '@/components/ShowOrEdit'
import { getDefectTypeListCache } from '@/api/scar/defectType'
import { getTreeMap } from '@/utils'
import { getMaterialMFGInfo } from '@/api/material/main'
import { dropdownPoNumber } from '@/api/om/po'

export default {
  name: 'Baseinfo',
  components: {
    ScarFile,
    Cascading,
    tinymce,
    backRecord,
    ShowOrEdit
  },
  inject: ['app'],
  props: ['viewOnly'],
  data() {
    const required = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('scar.pleaseEnterOrSelectData')))
      } else {
        callback()
      }
    }
    const baseInfoRule = {
      inspectionNo: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      factoryId: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      orgId: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      supplierName: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      materialCode: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      emails: [
        { pattern: /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})(;([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4}))*$/, message: this.$t('supplier.pleaseEnterTheCorrectEmailAddress'), trigger: ['blur', 'change'] }
      ],
      materialDescription: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      qualityComplaintType: [{
        validator: required,
        trigger: 'change', required: true
      }],
      questionFromType: [{
        validator: required,
        trigger: 'change', required: true
      }],
      qualityBadType: [{
        validator: required,
        trigger: 'change', required: true
      }],
      categoryId: [{
        validator: required,
        trigger: 'change', required: true
      }],
      questionDescText: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      containmentActionDate: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      permanentActionDate: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      iqcScType: [{
        validator: required,
        trigger: 'change', required: true
      }]
    }
    return {
      showDetail: false,
      baseInfoRule,
      sourceProblemList: [],
      tempSupplierName: '',
      supplierList: [],
      poList: [],
      publishType: getDictDatas(DICT_TYPE.SCAR_IQC_SC_PUBLISH_TYPE).map(a => {
        return {
          ...a,
          disable: false
        }
      }),
      pickerOptions: {
        disabledDate(time) {
          return dayjs().isAfter(dayjs(time), 'day')
        }
      }
    }
  },
  computed: {
    scarInfo() {
      return this.app.scarInfo
    },
    baseDisable() {
      return ['revoked', 'completed', 'countersign', 'sqe_review', 'supplier_reply'].includes(this.scarInfo.scarStatus) || this.viewOnly
    }
  },
  watch: {
    'scarInfo.qualityComplaintType': {
      immediate: true,
      handler(val) {
        if (val === 'warning') {
          // 质量警示单只有3D格式，没有8D
          this.scarInfo.iqcScType = '3D'
          this.publishType.find(item => item.value === '8D').disable = true
        } else {
          this.publishType.forEach(item => {
            item.disable = false
          })
        }
      }
    },
    'scarInfo.qualityBadType': {
      handler(val) {
        if (this.scarInfo.qualityBadType && this.scarInfo.materialCode && this.scarInfo.supplierId) {
          this.getRepeatDefect()
        }
      }
    },
    'scarInfo.supplierId': {
      handler(val) {
        if (this.scarInfo.qualityBadType && this.scarInfo.materialCode && this.scarInfo.supplierId) {
          this.getRepeatDefect()
        }
      }
    },
    // 来料批数量
    'scarInfo.incomingMaterialQuantity': {
      handler(val) {
        this.calculateDefective()
      }
    },
    // 不良数量
    'scarInfo.fullInspectDefectQuantity': {
      handler(val) {
        this.calculateDefective()
      }
    },
    // 样本数
    'scarInfo.selectiveInspectSampleQuantity': {
      handler(val) {
        this.calculateDefective()
      }
    }
  },
  async mounted() {
    this.sourceProblemList = await getSourceOfProblem('iqc')
    this.scarInfo.questionFromType = this.sourceProblemList.find(i => i.value === 'check_psi').value
    this.getSupplierDetail()

    if (this.scarInfo.iqcScType === null || this.scarInfo.iqcScType === '') {
      this.scarInfo.iqcScType = '3D'
    }
    this.$nextTick(() => {
      this.$refs.baseInfo.clearValidate()
    })
  },
  methods: {
    // 被父组件调用
    getDefectType() {
      getDefectTypeListCache(null, 0, null).then(res => {
          this.defectTypes = this.handleTree(res.data, 'id')
          // 查看页面没有这个对象，会报错，需要增加判断
          if (this.$refs.badTypeCasRef) {
            this.$refs.badTypeCasRef.badTypeList = this.defectTypes
            this.$refs.badTypeCasRef.value = getTreeMap(this.scarInfo.qualityBadType, this.defectTypes)
          }
        })
    },
    // 全检方式-计算不良率
    calculateDefective() {
      if (this.scarInfo.incomingMaterialQuantity < 0) {
        this.$message.error(this.$t('scar.theNumberOfIncomingBatchesIsLessThanPleaseConfirmAgain'))
        this.scarInfo.incomingMaterialQuantity = 0
        return
      }
      if (this.scarInfo.fullInspectDefectQuantity < 0) {
        this.$message.error(this.$t('scar.theNumberOfDefectsIsLessThanPleaseConfirmAgain'))
        this.scarInfo.fullInspectDefectQuantity = 0
        return
      }
      if (this.scarInfo.selectiveInspectSampleQuantity < 0) {
        this.$message.error(this.$t('scar.theSampleQuantityIsLessThanPleaseConfirmAgain'))
        this.scarInfo.selectiveInspectSampleQuantity = 0
        return
      }
      if (!this.scarInfo.incomingMaterialQuantity) {
        this.scarInfo.incomingMaterialQuantity = 0
      }
      // 校验方式-全检
      this.checkQuantity()
      if (this.scarInfo.fullInspectDefectQuantity >= 0) {
        if (this.scarInfo.incomingMaterialQuantity === 0) {
          this.scarInfo.inspectDefectPercent = 0
        } else {
          this.scarInfo.inspectDefectPercent = (this.scarInfo.fullInspectDefectQuantity / this.scarInfo.incomingMaterialQuantity * 100).toFixed(2)
        }
      }
    },
    // 全检、抽检方式校验输入的数量
    checkQuantity() {
      // UFFF-1647：数量限制
      // 1（检验方式）选择（全检），需要填入不良数量，不能大于（来料批数量），示信息：提示：不良数量大于来料数量，请再确认
      // 2（检验方式）选择（抽检），需要填入（样本数，不良数量），数量均不能大于（来料批数量），并且（不良数量）要小于等于样本数
      // 3.（来料批数量）未填写时作为0处理，此时填写（抽检方式）的时候也能给与数量的提示信息：提示：存在抽检信息，来料数量不能为0
      // 4.（来料批数量）做大于0的限制，不能小于0

      // [1] 校验方式-全检
      if (this.scarInfo.fullInspectDefectQuantity) {
        // 校验方式-全检
        if (this.scarInfo.inspectType === 'full') {
          if (this.scarInfo.fullInspectDefectQuantity > this.scarInfo.incomingMaterialQuantity) {
            this.$message.error(this.$t('scar.theQuantityOfDefectsIsGreaterThanTheQuantityOfIncomingMaterialsPleaseConfirmAgain'))
            this.scarInfo.fullInspectDefectQuantity = 0
            return
          }
        }
      }

      // [2] 校验方式-抽检
      if (this.scarInfo.inspectType === 'random') {
        if (this.scarInfo.selectiveInspectSampleQuantity) {
          if (this.scarInfo.selectiveInspectSampleQuantity > this.scarInfo.incomingMaterialQuantity) {
            this.$message.error(this.$t('scar.theSampleQuantityIsGreaterThanTheIncomingMaterialQuantityPleaseConfirmAgain'))
            this.scarInfo.selectiveInspectSampleQuantity = 0
            return
          }
        }
        if (this.scarInfo.fullInspectDefectQuantity) {
          if (this.scarInfo.fullInspectDefectQuantity > this.scarInfo.incomingMaterialQuantity) {
            this.$message.error(this.$t('scar.theQuantityOfDefectsIsGreaterThanTheQuantityOfIncomingMaterialsPleaseConfirmAgain'))
            this.scarInfo.fullInspectDefectQuantity = 0
            return
          }
        }
        if (this.scarInfo.selectiveInspectSampleQuantity && this.scarInfo.fullInspectDefectQuantity) {
          if (this.scarInfo.fullInspectDefectQuantity > this.scarInfo.selectiveInspectSampleQuantity) {
            this.$message.error(this.$t('scar.theNumberOfDefectsIsGreaterThanTheNumberOfSamplesPleaseConfirmAgain'))
            this.scarInfo.fullInspectDefectQuantity = 0
          }
        }
      }
    },
    getRepeatDefect() {
      if (this.scarInfo.qualityBadType && this.scarInfo.materialCode && this.scarInfo.supplierId) {
        validRepeatDefect({
          defectType: this.scarInfo.qualityBadType,
          materialCode: this.scarInfo.materialCode,
          supplierId: this.scarInfo.supplierId,
          scarId: this.scarInfo.id
        }).then(res => {
          this.scarInfo.isRepetitionBad = res.data
        })
      }
    },
    // 选择供应商事件多次切换需要保存和提交时及时clear为空的字段，导致该字段不被数据库更新为空，通过详情拉出旧值
    clearSupplierRelFields() {
      if (!this.scarInfo.emails) {
        this.scarInfo.emails = ''
      }
      if (!this.scarInfo.supplierCode) {
        this.scarInfo.supplierCode = ''
      }
      if (!this.scarInfo.nameShort) {
        this.scarInfo.nameShort = ''
      }
      if (!this.scarInfo.supplierId) {
        this.scarInfo.supplierId = ''
      }
    },
    // 保存
    saveBaseInfo() {
      this.clearSupplierRelFields()
      saveRecord(this.scarInfo).then(res => {
        this.scarInfo.id = res.data
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.$emit('initScar', res.data)
      })
    },
    // 选择供应商下拉
    // 【UFFF-2021】选择供应商时需要判断 工厂、采购组织是否必填
    getSupplierDetail(query) {
      if (query) {
        if (!this.scarInfo.factoryId) {
          this.$message.error(this.$t('scar.pleaseSelectAFactoryFirst'))
          return
        }
        if (!this.scarInfo.orgId) {
          this.$message.error(this.$t('scar.pleaseSelectAPurchasingOrganizationFirst'))
          return
        }
        getSupplierDetail({
          fuzzySupplierName: query,
          orgId: this.scarInfo.orgId
        }).then(res => {
          this.supplierList = res.data
        })
      } else {
        this.supplierList = []
      }
    },
    // 选择PO号下拉
    getPoNumber(query) {
      if (query) {
        dropdownPoNumber({
          fuzzyPoNo: query
        }).then(res => {
          this.poList = res.data
        })
      } else {
        this.poList = []
      }
    },
    selectSupplier(item) {
      if (item) {
        // UFFF-1653:待发布：进入单据，重新选择供应商，选择没有编码的供应商，保存单据后，供应商编码，质量人员邮箱没有置空
        // 处理多次选择供应商时，处理旧值问题
        const temp = this.supplierList.find(a => a.name === item)
        this.scarInfo.supplierCode = temp.code
        this.scarInfo.nameShort = temp.nameShort
        this.scarInfo.emails = ''
        this.scarInfo.supplierId = temp.id
        getMultiContactSimple({ supplierId: temp.id, contactDivision: 'quality_complaints' }).then(res => {
          this.scarInfo.emails = res.data?.map(i => i.email).join(';')
        })
      }
    },
    selectPoNO(item) {
      if (item) {
        // UFFF-1653:待发布：进入单据，重新选择供应商，选择没有编码的供应商，保存单据后，供应商编码，质量人员邮箱没有置空
        // 处理多次选择供应商时，处理旧值问题
        const temp = this.poList.find(a => a.key === item)
        this.scarInfo.poNumber = temp.value
        this.scarInfo.poId = temp.key
      }
    },
    revoke() {
      this.$confirm(this.$t('scar.areYouSureToCancelTheDocumentAndNotPublishItToTheSupplier'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        undoRecord({ ids: [this.scarInfo.id] }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))

          this.$tab.closeOpenPage('/scar/scarindex')
        })
      })
    },
    // 发布给供应商
    toSupplier() {
      this.$refs.baseInfo.validate(
        (valid, obj) => {
          if (valid) {
            if (this.scarInfo.emails) {
              submitIqc(this.scarInfo).then(res => {
                this.$message.success(this.$t('supplier.submittedSuccessfully'))

                this.$tab.closeOpenPage('/scar/scarindex')
              })
            } else {
              this.$confirm(this.$t('scar.withoutEmailTheSupplierNotBeAbleToConfirmPleaseConfirmWhetherToSubmitOrNot'), this.$t('supplier.tips'), {
                confirmButtonText: this.$t('order.determine'),
                cancelButtonText: this.$t('common.cancel'),
                type: 'warning'
              }).then(() => {
                submitIqc(this.scarInfo).then(res => {
                  this.$message.success(this.$t('supplier.submittedSuccessfully'))

                  this.$tab.closeOpenPage('/scar/scarindex')
                })
              })
            }
          }
        }
      )
    },
    // 提交到SQE
    // 1.不更新质量单据的状态，仅更新单据的处理人信息
    // 2.UFFF-1650: 点击发给SQE时，不用验证必填
    toSQE() {
      submitSqe(this.scarInfo).then(res => {
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
        this.$tab.closeOpenPage('/scar/scarindex')
      })
    },
    querySearchAsync(queryString, cb) {
      if (queryString.length < 2) {
        return
      }
      getMaterialMFGInfo({
        materialCode: queryString
      }).then(res => {
        cb(res.data.map(item => {
          const mfg = item.mfg === null ? '' : item.mfg
          const mpn = item.mpn === null ? '' : item.mpn
          item.materialCode = item.partNo
          item.materialDescription = item.description
          return {
            value: item.materialCode + '-' + item.materialDescription,
            ...item
          }
        }))
      })
    },
    handleMaterialCodeSelect(item) {
      const { id, ...cloneItem } = item
      Object.assign(this.scarInfo, cloneItem)
      this.getRepeatDefect()
    },
    fixeNumber(event) {
      const count = (numb) => {
        if (Number.isInteger(numb)) {
          return 0
        } else {
          return numb.toString().split('.')[1]?.length
        }
      }
      if (count(this.scarInfo[event])) {
        this.scarInfo[event] = this.scarInfo[event].toFixed(3)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.commonBtn {
  min-width: 80px;
}
.commonFormItem {
  width: 50%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

}
.inspection{
  width: 50%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
  display: inline-flex;
  justify-content: flex-end;
  align-items: center
}

.inspectionItem{
  flex: none;
  margin: 0 10px;
}
.inspectionNum{
  flex: 0 1 33%;
}
::v-deep .el-form-item__content {
  width: calc(100% - 212px);
}

</style>
