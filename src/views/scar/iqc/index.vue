<template>
  <!--iqc页面-->
  <div class="iqc">
    <base-info
      ref="iqcInfo"
      :view-only="viewOnly"
      @initScar="init"
    />
    <supplierconfirm
      v-if="['supplier_reply','completed'].includes(app.scarInfo.scarStatus)&&app.scarInfo.qualityComplaintType==='warning'"
      :view-only="viewOnly"
      @initScar="init"
    />
    <div v-else>
      <supplier-reply
        v-if="['supplier_reply','sqe_review','countersign','completed'].includes(app.scarInfo.scarStatus)"
        :view-only="viewOnly"
        @initScar="init"
      />
      <reviewReply
        v-if="['sqe_review','countersign','completed'].includes(app.scarInfo.scarStatus)"
        :view-only="viewOnly"
        @initScar="init"
      />
      <approve
        v-if="['countersign','completed'].includes(app.scarInfo.scarStatus)"
        :view-only="viewOnly"
        @initScar="init"
      />
    </div>

  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import baseInfo from '@/views/scar/iqc/baseInfo.vue'
import supplierReply from '@/views/scar/componets/supplierReply.vue'
import reviewReply from '@/views/scar/componets/reviewReply.vue'
import approve from '@/views/scar/componets/approve.vue'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getRecord } from '@/api/scar'
import Supplierconfirm from '@/views/scar/componets/supplierConfirm.vue'
import dayjs from 'dayjs'

export default {

  name: 'Iqc/:code',
  components: {
    Supplierconfirm,
    baseInfo,
    supplierReply,
    reviewReply,
    approve
  },
  provide() {
    return {
      app: this.app,
      badTypeList: this.badTypeList
    }
  },
  data() {
    const badTypeList = this.handleTree(getDictDatas(DICT_TYPE.SCAR_DEFECT_TYPE, 0), 'id')

    return {
      app: {
        scarInfo: {
          orgId: getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0)[0].id,
          factoryId: getDictDatas(DICT_TYPE.COMMON_FACTORY, 0)[0].id,
          scarCode: '',
          questionFromType: '',
          scarType: 'iqc',
          supplierId: null,
          supplierName: '',
          permanentActionDate: dayjs().add(5, 'day').format('YYYY-MM-DD'), // 当前日期加5天
          incomingMaterialQuantity: null,
          supplierCode: '',
          containmentActionDate: dayjs().add(2, 'day').format('YYYY-MM-DD'), // 当前日期加2天,
          scDefectPercent: null,
          severityLevel: '',
          orderNo: '',
          qualityBadType: null,
          specifications: '',
          materialDescription: '',
          selectiveInspectSampleQuantity: null,
          fullInspectDefectQuantity: null,
          questionDescText: '',
          mrbNo: '',
          supplierFirstReplyDate: '',
          inspectionNo: '',
          onTime: false,
          inspectDefectPercent: null,
          nameShort: '',
          qualityComplaintType: getDictDatas(DICT_TYPE.SCAR_QUANTITY_COMPLAIN_TYPE, 0).find(i => i.value === 'improvement').value,
          scUsingQuantity: null,
          materialCode: '',
          inspectType: '',
          approveDate: '',
          isStopLine: false,
          passDate: '',
          emails: '',
          isRepetitionBad: false,
          mpn: '',
          spApproveDate: '',
          iqcScType: '',
          categoryId: null,
          batchNo: '',
          mfg: '',
          qualityBadTypeList: [],
          supplierReplyTextRelBaseVO: {},
          fileRelList: []
        }
      },
      badTypeList,
      viewOnly: false
    }
  },
  created() {
    this.init()
    this.viewOnly = this.$route.query.viewOnly === '1'
  },
  methods: {
    init(saveId) {
      if (saveId) {
        getRecord({ id: saveId }).then(res => {
          this.$tab.closeOpenPage({ path: `/scar/${res.data.scarType}/${res.data.scarCode}?id=${saveId}` })
        })
      } else {
        const id = this.$route.query.id || this.app.scarInfo.id
        if (id) {
          getRecord({ id }).then(res => {
            if (res.data.containmentActionRelList) {
              res.data.containmentActionRelList?.forEach((item, index) => {
                item.sort = index // 后端要求sort
              })
            }
            if (res.data.sqeApproveUsers) {
              res.data.reviewers = res.data.sqeApproveUsers.split(',').map(Number)
            }
            this.app.scarInfo = res.data
            this.app.scarInfo.createDate = parseTime(this.app.scarInfo.createTime, '{y}-{m}-{d}')
            this.$refs.iqcInfo.getDefectType()
          })
        } 
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.iqc {
  padding: 15px 20px;
}
</style>
