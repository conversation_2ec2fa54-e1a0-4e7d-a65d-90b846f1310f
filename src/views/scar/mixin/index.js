
import { getConfig } from '@/api/scar/replyDate'
import dayjs from 'dayjs'

export default {
  name: 'Index',
  methods: {
    getDateConfig() {
      getConfig().then(res => {
        if (res.data.containmentActionDate) {
          this.app.scarInfo.containmentActionDate = dayjs().add(res.data.containmentActionDate, 'day').format('YYYY-MM-DD')
        }
        if (res.data.permanentActionDate) {
          // 要求供应商回复日期（IQC、SC的永久对策要求回复日期；SP、SA的要求供应商回复日期)
          this.app.scarInfo.permanentActionDate = dayjs().add(res.data.permanentActionDate, 'day').format('YYYY-MM-DD')
        }
      })
    }
  }
}

