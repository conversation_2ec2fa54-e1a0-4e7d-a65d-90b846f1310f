<template>
  <!--  scar基本信息-->
  <div class="sa">
    <common-card :title="$t('scar.scarBasicInformationAuditNonconformanceImprovementFormSa')">
      <div>
        <el-form
          ref="saBaseInfo"
          :model="app.scarInfo"
          :rules="saFormRule"
          inline
          label-width="163px"
        >
          <div style="font-weight: bold">{{ $t('scar.factoryInformation') }}</div>
          <el-form-item
            :label="$t('order.company')"
            class="commonFormItem"
            prop="companyId"
          >
            <show-or-edit
              :value="app.scarInfo.companyId"
              :disabled="baseDisable"
              :dict="DICT_TYPE.COMMON_COMPANY"
            >
              <el-select
                v-model="app.scarInfo.companyId"
                :disabled="baseDisable"
                :placeholder="$t('auth.pleaseSelectACompany')"
                class="content"
                clearable
                filterable
              >
                <el-option
                  v-for="item in getDictDatas(DICT_TYPE.COMMON_COMPANY)"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('supplier.purchasingOrganization')"
            class="commonFormItem"
            prop="orgId"
          >
            <show-or-edit
              :disabled="baseDisable"
              :value="app.scarInfo.orgId"
              :dict="DICT_TYPE.COMMON_PURCHASEORG"
            >
              <el-select
                v-model="app.scarInfo.orgId"
                :placeholder="$t('auth.pleaseSelectAPurchasingOrganization')"
                class="content"
                :disabled="baseDisable"
                clearable
                filterable
              >
                <el-option
                  v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0)"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('supplier.supplierName')"
            class="commonFormItem"
            prop="supplierName"
          >
            <show-or-edit
              :value="app.scarInfo.supplierName"
              :disabled="baseDisable"
              :custom-list="supplierList"
            >
              <el-select
                v-model="app.scarInfo.supplierName"
                class="content"
                :placeholder="$t('scar.pleaseEnterKeywordsForFuzzySearch')"
                clearable
                filterable
                remote
                :remote-method="doGetSupplierList"
                :disabled="baseDisable"
                @change="selectSupplier"
              >
                <el-option
                  v-for="dict in supplierList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.name"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('supplier.supplierCode')"
            class="commonFormItem"
            prop="supplierCode"
          >
            <show-or-edit
              :value="app.scarInfo.supplierCode"
              :disabled="true"
            >
              <el-input v-model="app.scarInfo.supplierCode" disabled class="content" />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            v-if="app.scarInfo.scarStatus === undefined || ['new', 'to_be_released'].includes(app.scarInfo.scarStatus)"
            class="commonFormItem"
            :label="$t('scar.documentProcessor')"
            prop="nextOperatorIds"
          >
            <show-or-edit
              :value="app.scarInfo.nextOperatorIds"
              :disabled="viewOnly"
              :dict="DICT_TYPE.COMMON_USERS"
            >
              <el-select
                v-model="app.scarInfo.nextOperatorIds"
                class="content"
                :disabled="viewOnly"
                filterable
                multiple
              >
                <el-option
                  v-for="user in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
                  :key="user.id"
                  :label="user.name"
                  :value="user.id"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item v-else class="commonFormItem" label=" " />
          <el-form-item
            :label="$t('scar.qualityPersonnelEmail')"
            class="commonFormItem"
            prop="emails"
          >
            <show-or-edit
              :value="app.scarInfo.emails"
              :disabled="baseDisable"
            >
              <el-input v-model="app.scarInfo.emails" :disabled="baseDisable" class="content" />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('scar.auditDate')"
            prop="saApproveDate"
          >
            <show-or-edit
              type="Date"
              :value="app.scarInfo.saApproveDate"
              :disabled="viewOnly"
            >
              <el-date-picker
                v-model="app.scarInfo.saApproveDate"
                class="content"
                :placeholder="$t('common.pleaseSelectADate')"
                :picker-options="approvePickerOptions"
                type="date"
                :disabled="baseDisable"
                placement="bottom-start"
                value-format="yyyy-MM-dd"
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('scar.reviewer')"
            class="commonFormItem"
          >
            <show-or-edit
              :value="app.scarInfo.approveUser"
              :disabled="baseDisable"
            >
              <el-input v-model="app.scarInfo.approveUser" :disabled="baseDisable" class="content" />
            </show-or-edit>
          </el-form-item>
          <div style="font-weight: bold">{{ $t('scar.improvementRequirements') }}</div>
          <el-form-item
            :label="$t('material.category')"
            class="commonFormItem"
            prop="categoryId"
          >
            <show-or-edit
              :disabled="baseDisable"
              :value="app.scarInfo.categoryId"
              :dict="DICT_TYPE.COMMON_CATEGORY"
            >
              <cascading-category
                :multiple="false"
                :disabled="baseDisable"
                :original-value.sync="app.scarInfo.categoryId"
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('scar.sourceOfTheProblem')"
            class="commonFormItem"
            prop="questionFromType"
          >
            <show-or-edit
              :value="app.scarInfo.questionFromType"
              :disabled="baseDisable"
              :custom-list="sourceProblemList"
            >
              <el-select
                v-model="app.scarInfo.questionFromType"
                class="content"
                :disabled="baseDisable"
                :placeholder="$t('common.pleaseSelect')"
                clearable
              >
                <el-option
                  v-for="dict in sourceProblemList"
                  :key="dict.id"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form
            ref="saBaseInfo"
            :model="app.scarInfo"
            :rules="saFormRule"
            inline
            label-width="163px"
          >
            <el-form-item
              class="commonFormItem"
              :label="$t('scar.requestSupplierResponseDate')"
              prop="permanentActionDate"
            >
              <show-or-edit
                type="Date"
                :value="app.scarInfo.permanentActionDate"
                :disabled="baseDisable"
              >
                <el-date-picker
                  v-model="app.scarInfo.permanentActionDate"
                  :disabled="baseDisable"
                  :placeholder="$t('common.pleaseSelectADate')"
                  :picker-options="pickerOptions"
                  type="date"
                  placement="bottom-start"
                  value-format="yyyy-MM-dd"
                />
              </show-or-edit>
            </el-form-item>
          </el-form>
          <div>
            <el-button type="text" @click="showDetail= !showDetail">{{ $t('system.more') }}</el-button>
          </div>
          <div v-show="showDetail">

            <el-form-item
              :label="$t('scar.scarCode')"
              class="commonFormItem"
            >
              <show-or-edit
                :value="app.scarInfo.scarCode"
                :disabled="true"
              >
                <el-input
                  v-model="app.scarInfo.scarCode"
                  disabled
                  class="content"
                />
              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('common.creationDate')"
              class="commonFormItem"
            >
              <show-or-edit
                :value="app.scarInfo.createDate"
                :disabled="true"
              >
                <el-input
                  v-model="app.scarInfo.createDate"
                  disabled
                  class="content"
                />
              </show-or-edit>
            </el-form-item>
            <el-form-item
              :label="$t('rfq.createdBy')"
              class="commonFormItem"
            >
              <dict-tag :type="DICT_TYPE._INCLUDES_ALL" :value="app.scarInfo.creator" class="content" />
            </el-form-item>

            <el-form-item class="commonFormItem" label=" " />
            <el-form-item
              :label="baseDisable?$t('scar.seeFile'):$t('common.uploadFile')"
              class="commonFormItem"
            >
              <scar-file
                :disabled="baseDisable"
                :business-value="'sa'"
                style="margin-bottom: 20px"
              />
            </el-form-item>
          </div>

        </el-form>
      </div>
    </common-card>
    <common-card :title="$t('scar.problemDescription')">
      <!--        SA 问题描述-->
      <el-table :data="app.scarInfo.saQuestionDescRelList">
        <el-table-column width="450" :label="$t('scar.reviewNonConformities')" prop="reviewNonconformance">
          <template #header>
            <span :class="'required'">
              {{ $t('scar.reviewNonConformities') }}
            </span>
          </template>
          <template #default="scope">
            <!--            extra: el-table-column的show-overflow-tooltip属性如果同时需要编辑和查看时，且文本内容过长（超出列的宽度且需要鼠标悬停上去时展示完整内容）。目前需要在查看状态时改成文本框（span、p等文本标签）-->
            <!--            extra: 最新实现，移除 show-overflow-tip属性，同时增加template标签接收值-->
            <el-input
              v-if="!baseDisable"
              v-model="scope.row.reviewNonconformance"
              :disabled="baseDisable"
              maxlength="500"
              show-word-limit
              autosize
              type="textarea"
            />
            <template v-else>{{ scope.row.reviewNonconformance }}</template>
          </template>
        </el-table-column>
        <el-table-column :label="$t('scar.severityLevel')" prop="severtiyLevel" width="130px">
          <template #header>
            <span :class="'required'">
              {{ $t('scar.severityLevel') }}
            </span>
          </template>
          <template #default="scope">
            <el-select v-model="scope.row.severtiyLevel" :disabled="baseDisable" filterable>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SCAR_SEVERITY_LEVEL)"
                :key="dict.id"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column width="450" :label="$t('scar.causeAnalysis')" prop="rootCause">
          <template #header>
            <span :class="['countersign', 'sqe_review', 'supplier_reply', 'completed'].includes(app.scarInfo.scarStatus)?'required': ''">
              {{ $t('scar.causeAnalysis') }}
            </span>
          </template>
          <template #default="scope">
            <el-input
              v-if="!questionDescDisable"
              v-model="scope.row.rootCause"
              :disabled="questionDescDisable"
              autosize
              maxlength="500"
              type="textarea"
              show-word-limit
            />
            <template v-else>{{ scope.row.rootCause }}</template>
          </template>
        </el-table-column>
        <el-table-column :label="$t('scar.improvementMeasures')" width="450" prop="correctiveAction">
          <template #header>
            <span :class="['countersign', 'sqe_review', 'supplier_reply', 'completed'].includes(app.scarInfo.scarStatus)?'required': ''">
              {{ $t('scar.improvementMeasures') }}
            </span>
          </template>
          <template #default="scope">
            <el-input
              v-if="!questionDescDisable"
              v-model="scope.row.correctiveAction"
              :disabled="questionDescDisable"
              maxlength="500"
              autosize
              type="textarea"
              show-word-limit
            />
            <template v-else>{{ scope.row.correctiveAction }}</template>
          </template>
        </el-table-column>
        <el-table-column :label="$t('scar.completionDate')" prop="completionDate" width="120px">
          <template #header>
            <span :class="['countersign', 'sqe_review', 'supplier_reply', 'completed'].includes(app.scarInfo.scarStatus)?'required': ''">
              {{ $t('scar.completionDate') }}
            </span>
          </template>
          <template #default="scope">
            <el-date-picker
              v-model="scope.row.completionDate"
              style="width: 160px"
              :placeholder="$t('common.pleaseSelectADate')"
              :disabled="questionDescDisable"
              type="date"
              placement="bottom-start"
              value-format="yyyy-MM-dd"
            />
          </template>

        </el-table-column>
        <el-table-column :label="$t('scar.improvementResponsiblePerson')" prop="owner" width="150px">
          <template #header>
            <span :class="['countersign', 'sqe_review', 'supplier_reply', 'completed'].includes(app.scarInfo.scarStatus)?'required': ''">
              {{ $t('scar.improvementResponsiblePerson') }}
            </span>
          </template>
          <template #default="scope">
            <el-input
              v-model="scope.row.owner"
              :disabled="questionDescDisable"
              maxlength="50"
              show-word-limit
            />
          </template>
        </el-table-column>

        <!--     仅SQE评审、会签状态 支持编辑   -->
        <el-table-column
          :label="$t('scar.confirmationOfCompletionStatus')"
          width="150"
          prop="correcompletionStatus"
        >
          <template #header>
            <span :class="['countersign', 'sqe_review', 'completed'].includes(app.scarInfo.scarStatus)?'required': ''">
              {{ $t('scar.confirmationOfCompletionStatus') }}
            </span>
          </template>
          <template #default="scope">
            <el-input
              v-if="!correcompletionStatusDisable"
              v-model="scope.row.correcompletionStatus"
              :disabled="correcompletionStatusDisable"
              autosize
              type="textarea"
              maxlength="100"
              show-word-limit
            />
            <template v-else>{{ scope.row.correcompletionStatus }}</template>
          </template>
        </el-table-column>

        <el-table-column
          fixed="right"
          :label="$t('scar.relatedDocuments')"
          width="200"
        >
          <template #default="scope">
            <!--  仅未保存、新建、待发布的质量单据状态 支持展示该按钮-->
            <div>
              <el-button
                v-if="commonDisable && !viewOnly"
                type="text"
                @click="openUpload('sa_problem_description_attachment', false, scope.$index)"
              >{{ $t('scar.uploadProblemDescriptionAttachment') }}
                <span v-show="app.scarInfo.saQuestionDescRelList[scope.$index].fileRelList?.length">({{ app.scarInfo.saQuestionDescRelList[scope.$index].fileRelList?.filter(a => a.businessValue === 'sa_problem_description_attachment')?.length }})</span>
              </el-button>
            </div>
            <!--  仅未保存、新建、待发布的质量单据状态不展示该按钮          -->
            <el-button
              v-if="app.scarInfo.scarStatus && app.scarInfo.scarStatus !== 'new' && app.scarInfo.scarStatus !== 'to_be_released'"
              style="padding-bottom: 1px"
              type="text"
              @click="openUpload('sa_problem_description_attachment', true, scope.$index)"
            >{{ $t('scar.viewProblemDescriptionAttachment') }}
              <span v-show="app.scarInfo.saQuestionDescRelList[scope.$index].fileRelList?.length">({{ app.scarInfo.saQuestionDescRelList[scope.$index].fileRelList?.filter(a => a.businessValue === 'sa_problem_description_attachment')?.length }})</span>
            </el-button>
            <!--  仅供应商回复、SQE评审的质量单据状态 支持展示该按钮-->
            <div>
              <el-button
                v-if="improvementMeasureDiable && !viewOnly"
                type="text"
                @click="openUpload('sa_improvement_measures', false, scope.$index)"
              >{{ $t('scar.uploadAttachmentsRelatedToImprovementMeasures') }}
                <span v-show="app.scarInfo.saQuestionDescRelList[scope.$index].fileRelList?.length">({{ app.scarInfo.saQuestionDescRelList[scope.$index].fileRelList?.filter(a => a.businessValue === 'sa_improvement_measures')?.length }})</span>
              </el-button>
            </div>
            <!--  仅会签、已完成的质量单据状态 支持展示该按钮-->
            <div>
              <el-button
                v-if="['countersign','completed'].includes(app.scarInfo.scarStatus)"
                style="padding-top: 1px"
                type="text"
                @click="openUpload('sa_improvement_measures', true, scope.$index)"
              >{{ $t('scar.viewAttachmentsRelatedToImprovementMeasures') }}
                <span v-show="app.scarInfo.saQuestionDescRelList[scope.$index].fileRelList?.length">({{ app.scarInfo.saQuestionDescRelList[scope.$index].fileRelList?.filter(a => a.businessValue === 'sa_improvement_measures')?.length }})</span>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <!--  仅未保存、新建、待发布的质量单据状态 支持展示该按钮-->
        <el-table-column
          v-if="commonDisable && !viewOnly"
          fixed="right"
          :label="$t('common.operate')"
          width="100px"
        >
          <template #default="scope">
            <el-button type="text" @click="addNewSaQuestionDesc()">{{ $t('rfq.addTo') }}</el-button>
            <!--            真删除，存在id则call 后端删除方法
                            仅存在一条数据时不允许删除-->
            <el-button type="text" @click="doDeleteSaQuestionRel(scope.row.id, scope.$index)">{{ $t('common.del') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div style="padding-top: 20px" />

      <!--       【邀请评审人】 仅SQE评审节点展示-->
      <div v-if="['sqe_review'].includes(app.scarInfo.scarStatus)" style="padding-top: 20px">
        <el-form label-width="100px" inline style="margin-top: 15px">
          <el-form-item :label="$t('scar.inviteReviewers')">
            <show-or-edit
              :value="app.scarInfo.reviewers"
              :disabled="reviewReplyDisable"
              :dict="DICT_TYPE.COMMON_USERS"
            >
              <el-select
                v-model="app.scarInfo.reviewers"
                :disabled="reviewReplyDisable"
                class="searchValue"
                multiple
                clearable
                filterable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
        </el-form>
      </div>

    </common-card>

    <!--    会签节点-->
    <approve
      v-if="['countersign', 'completed'].includes(app.scarInfo.scarStatus)"
      :view-only="viewOnly"
    />

    <div class="fixedBottom">

      <!-- 【提交、退回】 仅 供应商回复、SQE评审状态时 展示该按钮      -->
      <el-button
        v-if="submitButtonDisable"
        class="commonBtn"
        type="primary"
        @click="doSubmitGlobal"
      >{{ $t('common.submit') }}</el-button>

      <!-- 退回 UFFF-1635: 待发布状态支持办理人退回至 新建状态 -->
      <back-record v-if="!viewOnly && ['to_be_released', 'supplier_reply', 'sqe_review'].includes(app.scarInfo.scarStatus)" />

      <!--    【提交到SQE】    仅 新建单据时 or 单据状态为 新建、待发布时 展示该按钮-->
      <el-button
        v-if="commonDisable && !viewOnly"
        v-has-permi="['scar:record:submit-sqe']"
        plain
        :disabled="['to_be_released'].includes(app.scarInfo.scarStatus)"
        class="commonBtn"
        type="primary"
        @click="doSubmitToSQE"
      >{{ $t('scar.submitToSqe') }}</el-button>
      <!--   【发布给供应商】     仅 新建单据时 or 单据状态为 新建、待发布时 展示该按钮-->
      <el-button
        v-if="commonDisable && !viewOnly"
        class="commonBtn"
        type="primary"
        @click="doSubmitToSupplier"
      >{{ $t('scar.publishToSuppliers') }}</el-button>

      <!--    【保存】  全状态都存在-->
      <el-button v-if="commonDisable && !viewOnly" v-has-permi="['scar:record:save']" plain class="commonBtn" type="primary" @click="saveBaseInfo">{{ $t('scar.preservation') }}</el-button>
      <el-button v-if="submitButtonDisable && !viewOnly" v-has-permi="['scar:record:save']" plain class="commonBtn" type="primary" @click="saveBaseInfo('global')">{{ $t('scar.preservation') }}</el-button>

      <!--     【取消】 会签、已完成、已撤销时隐藏。会签时是有approve组件的取消按钮-->
      <!--      <el-button v-if="!viewOnly && !['countersign', 'completed', 'revoked'].includes(app.scarInfo.scarStatus)" class="commonBtn" type="primary" @click="$router.push('/scar/scarindex')">{{ $t('scar.cancel') }}</el-button>-->

      <!--    【撤销单据】    仅 新建单据时 or 单据状态为 新建、待发布时 展示该按钮-->
      <!--      <el-button-->
      <!--        v-if="commonDisable"-->
      <!--        class="commonBtn"-->
      <!--        type="danger"-->
      <!--        @click="doRevoke"-->
      <!--      >{{ $t('scar.notPublish') }}</el-button>-->

      <!--   【删除单据】     仅 新建单据时 or 单据状态为 新建、待发布时 展示该按钮-->
      <!--      <el-button-->
      <!--        v-if="commonDisable"-->
      <!--        class="commonBtn"-->
      <!--        type="danger"-->
      <!--        @click="doDeleteRecord"-->
      <!--      >{{ $t('scar.deleteDocument') }}</el-button>-->
    </div>

    <sa-file ref="saFile" :business-value="businessValue" :see-sa-file="seeSaFile" />
  </div>
</template>

<script>

import {
  deleteRecord,
  getSARecord,
  getSourceOfProblem,
  getSupplierDetail,
  getSupplierDetailById,
  saSubmitToSupplier,
  saveGlobal,
  saveRecord,
  submitGlobal,
  submitSqe,
  undoRecord
} from '@/api/scar'
import { deleteSaQuestionDescRel } from '@/api/scar/sa'
import { getMultiContactSimple } from '@/api/supplier/info'
import ScarFile from '@/views/scar/componets/scarFile.vue'
import SaFile from '@/views/scar/sa/components/saFile.vue'
import BackRecord from '@/views/scar/componets/backRecord.vue'
import approve from '@/views/scar/componets/approve.vue'
import { getAccessToken } from '@/utils/auth'
import mixin from '@/views/scar/mixin'
import dayjs from 'dayjs'
import { getPath, parseTime } from '@/utils/ruoyi'
import ShowOrEdit from '@/components/ShowOrEdit'
import { getUsers } from '@/api/supplier/userCompanyRel'
import { getUsersByCategoryAndCompanyId } from '@/api/system/userCategoryCompany'

export default {
  // SA单据从认证模块的现场审核节点触发，供应商存在没有关联采购组织的情况，因此在该类型的创建中移除供应商和采购组织的关联关系
  name: 'Sa/:code',
  components: {
    ScarFile, BackRecord, SaFile, approve, ShowOrEdit
  },
  mixins: [mixin],
  provide() {
    return {
      app: this.app
    }
  },
  data() {
    const required = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('scar.pleaseEnterOrSelectData')))
      } else {
        callback()
      }
    }
    const saFormRule = {
      questionFromType: [{
        validator: required,
        trigger: 'change', required: true
      }],
      saApproveDate: [{
        validator: required,
        trigger: 'change', required: true
      }],
      categoryId: [{
        validator: required,
        trigger: 'change', required: true
      }],
      companyId: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      orgId: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      emails: [
        { pattern: /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})(;([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4}))*$/, message: this.$t('supplier.pleaseEnterTheCorrectEmailAddress'), trigger: ['blur', 'change'] }
      ],
      supplierName: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      permanentActionDate: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      nextOperatorIds: [{
        validator: required,
        trigger: 'change', required: true
      }]
    }
    return {
      showDetail: false,
      url: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      // SA单据类型form表单的校验规则
      saFormRule,
      supplierList: [],
      // 问题描述集合
      sourceProblemList: [],
      // scar单据对象
      app: {
        scarInfo: {
          scarType: 'sa',
          scarStatus: 'new',
          fileRelList: [],
          authId: '',
          questionFromType: '',
          saApproveDate: '',
          approveUser: '',
          supplierReplyTextRelBaseVO: {},
          saQuestionDescRelList: [],
          permanentActionDate: ''
        }
      },
      businessValue: '',
      seeSaFile: false,
      // SA-审核日期禁用晚于昨天的的日期选择
      approvePickerOptions: {
        disabledDate(time) {
          return dayjs().isBefore(dayjs(time), 'day')
        }
      },
      // SA-要求供应商回复日期禁用今天之前的日期选择
      pickerOptions: {
        disabledDate(time) {
          return dayjs().isAfter(dayjs(time), 'day')
        }
      },
      viewOnly: false,
      commonDisable: false
    }
  },
  computed: {
    baseDisable() {
      if (this.viewOnly) {
        return true
      }
      return ['revoked', 'completed', 'countersign', 'sqe_review', 'supplier_reply'].includes(this.app.scarInfo.scarStatus)
    },
    // 问题描述-原因分析至改善责任人的disable
    questionDescDisable() {
      if (this.viewOnly) {
        return true
      }
      return !['revoked', 'completed', 'sqe_review', 'supplier_reply'].includes(this.app.scarInfo.scarStatus)
    },
    // 完成情况确认 字段的disable规则（仅SQE评审、会签状态 支持编辑）
    correcompletionStatusDisable() {
      if (this.viewOnly) {
        return true
      }
      return !['sqe_review', 'countersign'].includes(this.app.scarInfo.scarStatus)
    },
    // 邀请评审人是否disable（详情查看）
    reviewReplyDisable() {
      return this.viewOnly || ['revoked', 'completed'].includes(this.app.scarInfo.scarStatus)
    },
    // 会签 密码输入框是否disable（详情查看）
    approveDisable() {
      return this.viewOnly || ['revoked', 'completed'].includes(this.app.scarInfo.scarStatus)
    },
    // 非新建、待发布、会签状态的提交按钮控制（会签状态由 approve组件的按钮控制）
    submitButtonDisable() {
      if (this.viewOnly) {
        return false
      }
      return ['supplier_reply', 'sqe_review'].includes(this.app.scarInfo.scarStatus)
    },
    // 上传改善措施相关附件 按钮控制
    improvementMeasureDiable() {
      if (this.viewOnly) {
        return false
      }
      return ['supplier_reply', 'sqe_review'].includes(this.app.scarInfo.scarStatus)
    }
  },
  watch: {
    'app.scarInfo.companyId': {
      immediate: true,
      handler(val) {
        this.populateDefaultProcessor()
      }
    },
    'app.scarInfo.supplierId': {
      immediate: true,
      handler(val) {
        this.populateDefaultProcessor()
      }
    },
    'app.scarInfo.categoryId': {
      immediate: true,
      handler(val) {
        this.populateDefaultProcessor()
      }
    }
  },
  created() {
    this.viewOnly = this.$route.query.viewOnly === '1'
    this.app.scarInfo.authId = this.$route.query.authId
    this.app.scarInfo.approveUser = this.$route.query.approveUser
    this.app.scarInfo.saApproveDate = this.$route.query.saApproveDate
    this.app.scarInfo.questionFromType = this.$route.query.questionFromType
    this.init()
  },
  async mounted() {
    this.sourceProblemList = await getSourceOfProblem('sa')
    // 获取供应商下拉列表
    this.doGetSupplierList()
    this.$nextTick(() => {
      this.$refs.saBaseInfo.clearValidate()
    })
  },
  methods: {
    // 底部的操作按钮控制
    doCommonDisable() {
      if (this.viewOnly) {
        this.commonDisable = false
      }
      this.commonDisable = ['new', 'to_be_released'].includes(this.app.scarInfo.scarStatus)
    },
    // SA单据获取默认处理人
    // 1.优先根据 品类+公司获取默认处理人
    // 2.再根据 供应商+公司获取默认处理人
    // 3.无论【1、2】是否获取到处理人，都可以选择其余的用户
    populateDefaultProcessor() {
      if (this.app.scarInfo.companyId && this.app.scarInfo.categoryId) {
        getUsersByCategoryAndCompanyId({
          categoryId: this.app.scarInfo.categoryId,
          userType: 'SQE',
          companyId: this.app.scarInfo.companyId
        }).then(res => {
          if (res.data?.length > 0) {
            res.data.forEach(i => {
              if (!this.app.scarInfo.nextOperatorIds.includes(i)) {
                this.app.scarInfo.nextOperatorIds.push(i)
              }
            })
          }
        })
      }

      if (this.app.scarInfo.nextOperatorIds?.length === 0 && this.app.scarInfo.supplierId && this.app.scarInfo.companyId) {
        getUsers({
          supplierId: this.app.scarInfo.supplierId,
          type: 'SQE',
          companyId: this.app.scarInfo.companyId
        }).then(res => {
          if (res.data?.length > 0) {
            res.data.forEach(i => {
              if (!this.app.scarInfo.nextOperatorIds.includes(i)) {
                this.app.scarInfo.nextOperatorIds.push(i)
              }
            })
          }
        })
      }
    },
    // SA单据 问题描述处上传附件成功时
    handleFileSuccess(response, file, fileList, businessValue) {
      this.app.scarInfo.fileRelList.push({
        name: file.name,
        fileName: file.name,
        fileId: response.data.id,
        scarId: this.app.scarInfo.id,
        businessValue: businessValue,
        filePath: response.data.url
      })
    },
    // SA单据 问题描述处附件移除时
    handleRemove(file, fileList) {
      this.app.scarInfo.fileRelList = this.app.scarInfo.fileRelList.filter(a => a.fileId !== file.fileId)
    },
    init() {
      const id = this.$route.query.id
      if (id) {
        this.getSaDetail(id)
      } else {
        this.doCommonDisable()
        this.addNewSaQuestionDesc()
        this.getDateConfig()
        this.app.scarInfo.supplierId = this.$route.query.supplierId
        if (this.app.scarInfo.supplierId) {
          getSupplierDetailById({
            supplierId: this.app.scarInfo.supplierId
          }).then(res => {
            this.app.scarInfo.nameShort = res.data.nameShort
            this.app.scarInfo.supplierName = res.data.name
            this.app.scarInfo.supplierCode = res.data.code
            this.app.scarInfo.supplierId = res.data.supplierId
            // 获取关联的邮箱
            getMultiContactSimple({
              supplierId: this.app.scarInfo.supplierId,
              contactDivision: 'quality_complaints'
            }).then(res => {
              this.$set(this.app.scarInfo, 'emails', res.data?.map(i => i.email).join(';'))
            })
          })
        }
      }
    },
    // 根据scarId获取单据的详情
    getSaDetail(scarId) {
      getSARecord({ id: scarId }).then(res => {
        if (res.data.sqeApproveUsers) {
          res.data.reviewers = res.data.sqeApproveUsers.split(',').map(Number)
        }
        this.app.scarInfo.saQuestionDescRelList = res.data.saQuestionDescRelList
        this.app.scarInfo = res.data
        if (!this.app.scarInfo.saQuestionDescRelList || this.app.scarInfo.saQuestionDescRelList.length === 0) {
          // 初始化空行
          this.addNewSaQuestionDesc()
        }
        // 格式化创建日期
        this.app.scarInfo.createDate = parseTime(this.app.scarInfo.createTime, '{y}-{m}-{d}')
        // 审批人
        if (this.app.scarInfo.spSaNextOperatorIds) {
          this.app.scarInfo.nextOperatorIds = this.app.scarInfo.spSaNextOperatorIds.split(',').map(Number)
        }
        // 在此计算commonDisable。否则scarStatus的值是默认new
        this.doCommonDisable()
      })
    },
    // 页面创建新的问题描述记录
    addNewSaQuestionDesc() {
      // 初始化空行
      this.app.scarInfo.saQuestionDescRelList.push({
        reviewNonconformance: '',
        severtiyLevel: '',
        rootCause: '',
        correctiveAction: '',
        completionDate: '',
        owner: '',
        correcompletionStatus: '',
        fileRelList: []
      })
    },
    // 删除scar-SA单据关联的问题描述记录
    doDeleteSaQuestionRel(id, index) {
      if (this.app.scarInfo.saQuestionDescRelList?.length <= 1) {
        this.$message.error(this.$t('scar.pleaseKeepAtLeastOneLineOfProblemDescription'))
        return
      }
      if (id) {
        deleteSaQuestionDescRel({ id: id, scarId: this.app.scarInfo.id })
      }
      // 从集合中移除指定的id
      this.app.scarInfo.saQuestionDescRelList?.splice(index, 1)
      this.$message.success(this.$t('common.delSuccess'))
    },
    // 选择供应商事件多次切换需要保存和提交时及时clear为空的字段，导致该字段不被数据库更新为空，通过详情拉出旧值
    clearSupplierRelFields() {
      if (!this.app.scarInfo.emails) {
        this.app.scarInfo.emails = ''
      }
      if (!this.app.scarInfo.supplierCode) {
        this.app.scarInfo.supplierCode = ''
      }
      if (!this.app.scarInfo.nameShort) {
        this.app.scarInfo.nameShort = ''
      }
      if (!this.app.scarInfo.supplierId) {
        this.app.scarInfo.supplierId = ''
      }
    },
    // 保存
    // 新建单据保存
    saveBaseInfo(global) {
      if (this.app.scarInfo.saQuestionDescRelList) {
        this.app.scarInfo.saQuestionDescRelList?.forEach((item, index) => {
          item.sort = index + 1
        })
      }
      if (global && global === 'global') {
        // 非 新建、待发布状态的保存
        saveGlobal(this.app.scarInfo).then(res => {
          this.$message.success(this.$t('common.savedSuccessfully'))
          this.getSaDetail(this.app.scarInfo.id)
        })
      } else {
        // 新建、待发布状态的保存
        this.clearSupplierRelFields()
        saveRecord(this.app.scarInfo).then(res => {
          this.$message.success(this.$t('common.savedSuccessfully'))
          getSARecord({ id: res.data }).then(res => {
            this.$tab.closeOpenPage({ path: `/scar/${res.data.scarType}/${res.data.scarCode}?id=${res.data.id}` })
          })
        })
      }
    },
    // 获取供应商下拉列表
    doGetSupplierList(query) {
      if (query) {
        getSupplierDetail({
          fuzzySupplierName: query
        }).then(res => {
          this.supplierList = res.data
        })
      } else {
        this.supplierList = []
      }
    },
    // 选中指定的供应商的编码、邮箱
    selectSupplier(item) {
      if (item) {
        const temp = this.supplierList.find(a => a.name === item)
        this.app.scarInfo.nameShort = temp.nameShort
        this.app.scarInfo.supplierCode = temp.code
        this.app.scarInfo.supplierId = temp.id
        this.$set(this.app.scarInfo, 'emails', '')
        getMultiContactSimple({ supplierId: temp.id, contactDivision: 'quality_complaints' }).then(res => {
          // 扩展：v-model绑定的是scarInfo对象，其中emails属性不是通过<el-input>组件疏通并且绑定至scarInfo对象中，原先是 this.scarInfo.emails = val;这种方式不会触发vue的页面刷新，
          // 因此，使用$set方式强制赋值。
          this.$set(this.app.scarInfo, 'emails', res.data?.map(i => i.email).join(';'))
        })
      }
    },
    // 单据撤销
    doRevoke() {
      this.beforeCallRevokeOrDel()
      this.$confirm(this.$t('scar.areYouSureToCancelTheDocumentAndNotPublishItToTheSupplier'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        undoRecord({ ids: [this.app.scarInfo.id] })
      })
    },
    // 单据删除
    doDeleteRecord() {
      this.beforeCallRevokeOrDel()
      this.$confirm(this.$t('scar.areYouSureToDeleteTheDocumentAndNotPublishItToTheSupplier'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        deleteRecord({ ids: [this.app.scarInfo.id] })
      })
    },
    // 撤销、删除操作之前的校验
    // 1.当前单据必须存在id
    beforeCallRevokeOrDel() {
      if (!this.app.scarInfo.id) {
        this.$message.warning(this.$t('scar.theCurrentDocumentHasNotBeenSavedAndDoesNotSupportOperation'))
      }
    },
    // 提交至供应商
    doSubmitToSupplier() {
      this.$refs.saBaseInfo.validate((valid, obj) => {
        if (valid) {
          if (this.app.scarInfo.emails) {
            saSubmitToSupplier(this.app.scarInfo).then(res => {
              this.$message.success(this.$t('order.operationSucceeded'))
              this.$tab.closeOpenPage('/scar/scarindex')
            })
          } else {
            this.$confirm(this.$t('scar.withoutEmailTheSupplierNotBeAbleToConfirmPleaseConfirmWhetherToSubmitOrNot'), this.$t('supplier.tips'), {
              confirmButtonText: this.$t('order.determine'),
              cancelButtonText: this.$t('common.cancel'),
              type: 'warning'
            }).then(() => {
              saSubmitToSupplier(this.app.scarInfo).then(res => {
                this.$message.success(this.$t('order.operationSucceeded'))
                this.$tab.closeOpenPage('/scar/scarindex')
              })
            })
          }
        }
      })
    },
    // 提交到SQE
    // 1.不更新质量单据的状态，仅更新单据的处理人信息
    // 2.UFFF-1650: 点击发给SQE时，不用验证必填
    doSubmitToSQE() {
      submitSqe(this.app.scarInfo).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.$tab.closeOpenPage('/scar/scarindex')
      })
    },
    // 非新建状态的单据提交
    // 1.当前单据状态为SQE评审时需要检验密码
    doSubmitGlobal() {
      this.$refs.saBaseInfo.validate((valid, obj) => {
        if (valid) {
          submitGlobal(this.app.scarInfo).then(res => {
            this.$message.success(this.$t('order.operationSucceeded'))
            if (this.$store.getters.userId === -1) {
              location.href = getPath('/')
            } else {
              this.$tab.closeOpenPage('/scar/scarindex')
            }
          })
        }
      })
    },
    // 问题描述上传附件
    /**
     * @param businessVal
     * @param seeSaFile
     * @param row
     * @param rowIndex 当前行不存在id时，使用index进行文件过滤
     */
    openUpload(businessVal, seeSaFile, rowIndex) {
      this.$refs.saFile.visible = true
      // 此业务id非真实的附件关联的业务id，存在当前行未保存时上传附件，需要按行维度来维护数据
      this.$refs.saFile.rowIndex = rowIndex
      this.businessValue = businessVal
      this.seeSaFile = seeSaFile
      const a = this.app.scarInfo.saQuestionDescRelList[rowIndex].fileRelList?.filter(a => a.businessValue === businessVal)
      a?.forEach(b => {
        b.name = b?.fileName
      })
      this.$refs.saFile.fileList = a || []
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";
.commonBtn{
  min-width: 80px;
}
.commonFormItem {
  width: 50%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

}

::v-deep .el-form-item__content {
  width: calc(100% - 163px);
}
.required::before{
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}
.sa {
  padding: 15px 20px;
}

</style>
