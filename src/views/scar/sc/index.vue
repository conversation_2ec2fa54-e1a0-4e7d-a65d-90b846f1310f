<template>
  <div class="sc">
    <base-info
      ref="scInfo"
      :view-only="viewOnly"

      @initScar="init"
    />
    <supplierconfirm
      v-if="['supplier_reply','completed'].includes(app.scarInfo.scarStatus)&&app.scarInfo.qualityComplaintType==='warning'"

      :view-only="viewOnly"
      @initScar="init"
    />
    <div v-else>
      <supplier-reply
        v-if="['supplier_reply','sqe_review','countersign','completed'].includes(app.scarInfo.scarStatus)"
        :view-only="viewOnly"
        @initScar="init"
      />
      <reviewReply
        v-if="['sqe_review','countersign','completed'].includes(app.scarInfo.scarStatus)"
        :view-only="viewOnly"
        @initScar="init"
      />
      <approve
        v-if="['countersign','completed'].includes(app.scarInfo.scarStatus)"
        :view-only="viewOnly"
        @initScar="init"
      />
    </div>
  </div>
</template>

<script>
import baseInfo from '@/views/scar/sc/baseInfo.vue'
import supplierReply from '@/views/scar/componets/supplierReply.vue'
import reviewReply from '@/views/scar/componets/reviewReply.vue'
import approve from '@/views/scar/componets/approve.vue'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getSC } from '@/api/scar'
import Supplierconfirm from '@/views/scar/componets/supplierConfirm.vue'
import mixin from '@/views/scar/mixin'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'Sc/:code',
  components: {
    Supplierconfirm,
    baseInfo,
    supplierReply,
    reviewReply,
    approve
  },
  mixins: [mixin],

  provide() {
    return {
      app: this.app,
      badTypeList: this.badTypeList
    }
  },
  data() {
    const badTypeList = this.handleTree(getDictDatas(DICT_TYPE.SCAR_DEFECT_TYPE, 0), 'id')

    return {
      app: {
        scarInfo: {
          orgId: null,
          scarCode: '',
          questionFromType: '',
          scarType: 'sc',
          supplierId: null,
          supplierName: '',
          permanentActionDate: '',
          incomingMaterialQuantity: null,
          supplierCode: '',
          containmentActionDate: '',
          scDefectPercent: null,
          severityLevel: '',
          orderNo: '',
          qualityBadType: null,
          specifications: '',
          materialDescription: '',
          selectiveInspectSampleQuantity: null,
          fullInspectDefectQuantity: null,
          questionDescText: '',
          mrbNo: '',
          supplierFirstReplyDate: '',
          inspectionNo: '',
          onTime: false,
          inspectDefectPercent: null,
          nameShort: '',
          qualityComplaintType: '',
          scUsingQuantity: null,
          materialCode: '',
          inspectType: '',
          approveDate: '',
          isStopLine: false,
          passDate: '',
          emails: '',
          isRepetitionBad: false,
          mpn: '',
          spApproveDate: '',
          iqcScType: '',
          categoryId: null,
          batchNo: '',
          mfg: '',
          qualityBadTypeList: [],
          supplierReplyTextRelBaseVO: {},
          fileRelList: []
        }
      },
      badTypeList,
      viewOnly: false

    }
  },
  created() {
    this.init()
    this.viewOnly = this.$route.query.viewOnly
  },
  methods: {
    init(saveId) {
      if (saveId) {
        getSC({ id: saveId }).then(res => {
          this.$tab.closeOpenPage({ path: `/scar/${res.data.scarType}/${res.data.scarCode}?id=${saveId}` })
        })
      } else {
        const id = this.$route.query.id || this.app.scarInfo.id
        if (id) {
          getSC({ id }).then(res => {
            if (res.data.containmentActionRelList) {
              res.data.containmentActionRelList?.forEach((item, index) => {
                item.sort = index // 后端要求sort
              })
            }
            if (res.data.sqeApproveUsers) {
              res.data.reviewers = res.data.sqeApproveUsers.split(',').map(Number)
            }
            this.app.scarInfo = res.data
            this.app.scarInfo.createDate = parseTime(this.app.scarInfo.createTime, '{y}-{m}-{d}')
            this.$refs.scInfo.getDefectType()
            if (this.app.scarInfo.factoryId) {
              this.$refs.scInfo.selectFactory(this.app.scarInfo.factoryId,'noclear')
            }
          })
        } else {
          this.getDateConfig()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sc {
  padding: 15px 20px;
}
</style>
