<template>
  <!--  scar基本信息-->
  <div>
    <common-card
      :title="$t('scar.scarBasicInformationsupplierPerformanceImprovementFormSp')"
    >
      <div>
        <el-form
          ref="baseInfo"
          :model="scarInfo"
          :rules="baseInfoRule"
          inline
          label-width="178px"
        >
          <div style="font-weight: bold">{{ $t('supplier.companyInformation') }}</div>
          <el-form-item
            class="commonFormItem"
            :label="$t('order.company')"
            prop="companyId"
          >
            <show-or-edit
              :value="scarInfo.companyId"
              :disabled="baseDisable"
              :dict="DICT_TYPE.COMMON_COMPANY"
            >
              <el-select
                v-model="scarInfo.companyId"
                :disabled="baseDisable"
                :placeholder="$t('auth.pleaseSelectACompany')"
                class="content"
                clearable
                filterable
              >
                <el-option
                  v-for="item in getDictDatas(DICT_TYPE.COMMON_COMPANY)"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('supplier.purchasingOrganization')"
            prop="orgId"
          >
            <show-or-edit
              :disabled="baseDisable"
              :value="scarInfo.orgId"
              :dict="DICT_TYPE.COMMON_PURCHASEORG"
            >
              <el-select
                v-model="scarInfo.orgId"
                :disabled="baseDisable"
                :placeholder="$t('auth.pleaseSelectAPurchasingOrganization')"
                class="content"
                clearable
                filterable
              >
                <el-option
                  v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0)"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('supplier.supplierName')"
            prop="supplierName"
          >
            <show-or-edit
              :value="scarInfo.supplierName"
              :disabled="baseDisable"
              :custom-list="supplierList"
            >
              <el-select
                v-model="scarInfo.supplierName"
                :disabled="baseDisable"
                :placeholder="$t('scar.pleaseEnterKeywordsForFuzzySearch')"
                :remote-method="doGetSupplierDetail"
                class="content"
                clearable
                filterable
                remote
                @change="selectSupplier"
              >
                <el-option
                  v-for="dict in supplierList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.name"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('supplier.supplierCode')"
            prop="supplierCode"
          >
            <show-or-edit
              :value="scarInfo.supplierCode"
              :disabled="true"
            >
              <el-input
                v-model="scarInfo.supplierCode"
                class="content"
                disabled
              />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            v-if="scarInfo.scarStatus === undefined || ['new', 'to_be_released'].includes(scarInfo.scarStatus)"
            class="commonFormItem"
            :label="$t('scar.documentProcessor')"
            prop="nextOperatorIds"
          >
            <show-or-edit
              :value="scarInfo.nextOperatorIds"
              :disabled="viewOnly"
              :dict="DICT_TYPE.COMMON_USERS"
            >
              <el-select
                v-model="scarInfo.nextOperatorIds"
                class="content"
                :disabled="viewOnly"
                filterable
                multiple
                @visible-change="beforeOpenSelectUser"
              >
                <el-option
                  v-for="user in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
                  :key="user.id"
                  :label="user.name"
                  :value="user.id"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item v-else class="commonFormItem" label=" " />
          <el-form-item
            class="commonFormItem"
            :label="$t('scar.qualityPersonnelEmail')"
            prop="emails"
          >
            <show-or-edit
              :value="scarInfo.emails"
              :disabled="baseDisable"
            >
              <el-input
                v-model="scarInfo.emails"
                :disabled="baseDisable"
                class="content"
              />
            </show-or-edit>
          </el-form-item>

          <div style="font-weight: bold">{{ $t('scar.improvementRequirements') }}</div>
          <el-form-item
            class="commonFormItem"
            :label="$t('scar.sourceOfTheProblem')"
            prop="questionFromType"
          >
            <show-or-edit
              :value="scarInfo.questionFromType"
              :disabled="baseDisable"
              :custom-list="sourceProblemList"
            >
              <el-select
                v-model="scarInfo.questionFromType"
                :disabled="baseDisable"
                :placeholder="$t('common.pleaseSelect')"
                class="content"
                clearable
              >
                <el-option
                  v-for="dict in sourceProblemList"
                  :key="dict.id"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            class="commonFormItem"
            :label="$t('scar.requestSupplierResponseDate')"
            prop="permanentActionDate"
          >
            <show-or-edit
              type="Date"
              :value="scarInfo.permanentActionDate"
              :disabled="baseDisable"
            >
              <el-date-picker
                v-model="scarInfo.permanentActionDate"
                :disabled="baseDisable"
                :picker-options="pickerOptions"
                :placeholder="$t('common.pleaseSelectADate')"
                placement="bottom-start"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </show-or-edit>
          </el-form-item>

          <div>
            <el-button type="text" @click="showDetail= !showDetail">{{ $t('system.more') }}</el-button>
          </div>
          <div v-show="showDetail">
            <el-form-item
              class="commonFormItem"
              :label="$t('scar.scarCode')"
            >
              <show-or-edit
                :value="scarInfo.scarCode"
                :disabled="true"
              >
                <el-input
                  v-model="scarInfo.scarCode"
                  class="content"
                  disabled
                />
              </show-or-edit>
            </el-form-item>

            <el-form-item
              class="commonFormItem"
              :label="$t('common.creationDate')"
            >
              <show-or-edit
                :value="scarInfo.createDate"
                :disabled="true"
              >
                <el-input
                  v-model="scarInfo.createDate"
                  class="content"
                  disabled
                />
              </show-or-edit>
            </el-form-item>

            <el-form-item
              class="commonFormItem"
              :label="$t('rfq.createdBy')"
            >
              <dict-tag :type="DICT_TYPE.COMMON_USERS_INCLUDES_ALL" :value="scarInfo.creator" />

            </el-form-item>
            <!--            占位-->
            <el-form-item class="commonFormItem" label=" " />
          </div>

          <el-form-item
            class="commonFormItem"
            :label="baseDisable?$t('scar.seeFile'):$t('common.uploadFile')"
          >
            <scar-file
              :business-value="'sp'"
              :disabled="baseDisable"
            />
          </el-form-item>

          <div>
            <el-form-item
              :label="$t('scar.problemDescription')"
              prop="questionDescText"
              style="width: 100%;margin-right: 0;margin-top: 10px"
            >
              <div v-if="baseDisable" v-html="scarInfo.questionDescText" />
              <tinymce v-else v-model="scarInfo.questionDescText	" :height="160" />
            </el-form-item>
          </div>
          <div />
        </el-form>
        <div v-if="!baseDisable" class="fixedBottom">
          <back-record v-if="scarInfo.scarStatus=== 'to_be_released'" />
          <el-button v-has-permi="['scar:record:submit-sqe']" plain class="commonBtn" type="primary" @click="toSQE">{{ $t('scar.submitToSqe') }}</el-button>

          <el-button v-has-permi="['scar:record:submit']" class="commonBtn" type="primary" @click="toSupplier">{{ $t('scar.publishToSuppliers') }}</el-button>
          <el-button v-has-permi="['scar:record:save']" plain class="commonBtn" type="primary" @click="saveBaseInfo">{{ $t('common.save') }}</el-button>
        </div>
      </div>

    </common-card>
  </div>
</template>

<script>

import tinymce from '@/components/tinymce'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import {
  deleteRecord,
  getFileDetail,
  getSourceOfProblem,
  getSupplierDetail,
  saveRecord,
  submitSp,
  submitSqe,
  undoRecord
} from '@/api/scar'
import { getMultiContactSimple, getPurchaseRel } from '@/api/supplier/info'
import ScarFile from '@/views/scar/componets/scarFile.vue'
import dayjs from 'dayjs'
import BackRecord from '@/views/scar/componets/backRecord.vue'
import { getUsers } from '@/api/supplier/userCompanyRel'
import ShowOrEdit from '@/components/ShowOrEdit'
export default {
  name: 'Baseinfo',
  components: {
    BackRecord,
    ScarFile,
    tinymce,
    ShowOrEdit
  },
  inject: ['app'],
  props: ['viewOnly', 'perfid', 'fileId', 'companyId'],
  data() {
    const required = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('common.pleaseEnter')))
      } else {
        callback()
      }
    }
    const baseInfoRule = {
      orgId: [{
        validator: required,
        trigger: 'change', required: true
      }],
      supplierName: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      emails: [
        { pattern: /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})(;([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4}))*$/, message: this.$t('supplier.pleaseEnterTheCorrectEmailAddress'), trigger: ['blur', 'change'] }
      ],
      questionFromType: [{
        validator: required,
        trigger: 'change', required: true
      }],
      questionDescText: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      permanentActionDate: [{
        validator: required,
        trigger: 'blur', required: true
      }],
      companyId: [{
        validator: required,
        trigger: 'change', required: true
      }],
      nextOperatorIds: [{
        validator: required,
        trigger: 'change', required: true
      }]
    }
    return {
      showDetail: false,
      baseInfoRule,
      sourceProblemList: [],
      tempSupplierName: '',
      supplierList: [],
      publishType: getDictDatas(DICT_TYPE.SCAR_IQC_SC_PUBLISH_TYPE).map(a => {
        return {
          ...a,
          disable: false
        }
      }),
      pickerOptions: {
        disabledDate(time) {
          return dayjs().isAfter(dayjs(time), 'day')
        }
      }
    }
  },
  computed: {
    scarInfo() {
      return this.app.scarInfo
    },
    baseDisable() {
      return ['revoked', 'completed', 'countersign', 'sqe_review',
        'supplier_reply'
      ].includes(this.scarInfo.scarStatus) || this.viewOnly
    }
  },
  watch: {
    'scarInfo.companyId': {
      immediate: true,
      handler(val) {
        this.populateDefaultProcessor()
      }
    },
    'scarInfo.supplierId': {
      immediate: true,
      handler(val) {
        this.populateDefaultProcessor()
      }
    }
  },
  async mounted() {
    this.sourceProblemList = await getSourceOfProblem('sp')
    this.doGetSupplierDetail()
    // extra：赋值perfid（如有）至单据对象中
    if (this.perfid) {
      this.app.scarInfo.perfid = this.perfid
    }
    // extra：赋值fileID（如有）至单据对象中
    if (this.fileId) {
      getFileDetail(this.fileId).then(res => {
        if (res.data) {
          // 默认填充SP单据对象的文件对象
          this.scarInfo.fileRelList.push({
            name: res.data[0].name,
            fileName: res.data[0].name,
            filePath: res.data[0].url,
            fileId: Number(this.fileId),
            scarId: this.scarInfo.id,
            businessValue: 'sp'
          })
        }
      })
    }
    // extra: 默认填充公司(绩效模块触发）
    if (this.companyId) {
      this.scarInfo.companyId = Number(this.companyId)
    }
    this.$nextTick(() => {
      this.$refs.baseInfo.clearValidate()
    })
  },
  methods: {
    // 下拉选择单据处理人时触发
    beforeOpenSelectUser(visible) {
      if (visible) {
        if (!this.scarInfo.supplierId || !this.scarInfo.companyId) {
          this.$message.error(this.$t('scar.pleaseSelectASupplierAndPurchasingOrganizationFirst'))
        }
      }
    },
    populateDefaultProcessor() {
      if (this.scarInfo.supplierId && this.scarInfo.companyId) {
        getUsers({
          supplierId: this.scarInfo.supplierId,
          type: 'SQE',
          companyId: this.scarInfo.companyId
        }).then(res => {
          if (res.data?.length > 0) {
            res.data.forEach(i => {
              if (!this.scarInfo.nextOperatorIds.includes(i)) {
                this.scarInfo.nextOperatorIds.push(i)
              }
            })
          }
        })
      }
    },
    // 选择供应商事件多次切换需要保存和提交时及时clear为空的字段，导致该字段不被数据库更新为空，通过详情拉出旧值
    clearSupplierRelFields() {
      if (!this.scarInfo.emails) {
        this.scarInfo.emails = ''
      }
      if (!this.scarInfo.supplierCode) {
        this.scarInfo.supplierCode = ''
      }
      if (!this.scarInfo.nameShort) {
        this.scarInfo.nameShort = ''
      }
      if (!this.scarInfo.supplierId) {
        this.scarInfo.supplierId = ''
      }
    },
    // 保存
    saveBaseInfo() {
      this.clearSupplierRelFields()
      saveRecord(this.scarInfo).then(res => {
        this.scarInfo.id = res.data
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.$emit('initScar', res.data)
      })
    },
    // 选择供应商下拉
    // 【UFFF-2153】SP单由绩效模块触发，则需要自动带入 公司、供应商（没有工厂维度）
    doGetSupplierDetail(query) {
      if (query) {
        getSupplierDetail({
          fuzzySupplierName: query,
          orgId: this.scarInfo.orgId
        }).then(res => {
          this.supplierList = res.data
        })
      } else {
        this.supplierList = []
      }
    },
    selectSupplier(item) {
      if (item) {
        const temp = this.supplierList.find(a => a.name === item)
        this.scarInfo.supplierCode = temp.code
        this.scarInfo.nameShort = temp.nameShort
        this.scarInfo.emails = ''
        this.scarInfo.supplierId = temp.id
        getMultiContactSimple({ supplierId: temp.id, contactDivision: 'quality_complaints' }).then(res => {
          this.scarInfo.emails = res.data?.map(i => i.email).join(';')
        })
      }
    },
    revoke() {
      this.$confirm(this.$t('scar.areYouSureToCancelTheDocumentAndNotPublishItToTheSupplier'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        undoRecord({ ids: [this.scarInfo.id] }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))

          this.$tab.closeOpenPage('/scar/scarindex')
        })
      })
    },
    deleteRecord() {
      this.$confirm(this.$t('scar.areYouSureToDeleteTheDocumentAndNotPublishItToTheSupplier'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        deleteRecord({ ids: [this.scarInfo.id] }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))

          this.$tab.closeOpenPage('/scar/scarindex')
        })
      })
    },
    // 发布给供应商
    toSupplier() {
      this.$refs.baseInfo.validate(
        (valid, obj) => {
          if (valid) {
            if (this.scarInfo.emails) {
              submitSp(this.scarInfo).then(res => {
                this.$message.success(this.$t('supplier.submittedSuccessfully'))
                this.$tab.closeOpenPage('/scar/scarindex')
              })
            } else {
              this.$confirm(this.$t('scar.withoutEmailTheSupplierNotBeAbleToConfirmPleaseConfirmWhetherToSubmitOrNot'), this.$t('supplier.tips'), {
                confirmButtonText: this.$t('order.determine'),
                cancelButtonText: this.$t('common.cancel'),
                type: 'warning'
              }).then(() => {
                submitSp(this.scarInfo).then(res => {
                  this.$message.success(this.$t('supplier.submittedSuccessfully'))
                  this.$tab.closeOpenPage('/scar/scarindex')
                })
              })
            }
          }
        }
      )
    },
    // 提交到SQE
    // 1.不更新质量单据的状态，仅更新单据的处理人信息
    // 2.UFFF-1650: 点击发给SQE时，不用验证必填
    toSQE() {
      submitSqe(this.scarInfo).then(res => {
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
        this.$tab.closeOpenPage('/scar/scarindex')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.commonBtn {
  min-width: 80px;
}

.commonFormItem {
  width: 50%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

}

.inspection {
  width: 50%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
  display: inline-flex;
  justify-content: flex-end;
  align-items: center
}

.inspectionItem {
  flex: none;
  margin: 0 10px;
}

.inspectionNum {
  flex: 0 1 33%;
}

::v-deep .el-form-item__content {
  width: calc(100% - 178px);
}

</style>
