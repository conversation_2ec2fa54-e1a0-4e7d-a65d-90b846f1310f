<template>
  <div class="sp">
    <base-info
      ref="baseInfo"
      :perfid="perfid"
      :file-id="fileId"
      :company-id="companyId"
      :view-only="viewOnly"
      @initScar="init"
    />
    <supplierconfirm
      v-if="['supplier_reply','completed'].includes(app.scarInfo.scarStatus)&&app.scarInfo.qualityComplaintType==='warning'"
      :view-only="viewOnly"
      @initScar="init"
    />
    <div v-else>
      <supplier-reply
        v-if="['supplier_reply','sqe_review','countersign','completed'].includes(app.scarInfo.scarStatus)"
        :view-only="viewOnly"
        @initScar="init"
      />
      <reviewReply
        v-if="['sqe_review','countersign','completed'].includes(app.scarInfo.scarStatus)"
        :view-only="viewOnly"
        @initScar="init"
      />
      <approve
        v-if="['countersign','completed'].includes(app.scarInfo.scarStatus)"
        :view-only="viewOnly"
        @initScar="init"
      />
    </div>
  </div>
</template>

<script>
import baseInfo from '@/views/scar/sp/baseInfo.vue'
import supplierReply from '@/views/scar/componets/supplierReply.vue'
import reviewReply from '@/views/scar/componets/reviewReply.vue'
import approve from '@/views/scar/componets/approve.vue'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getSP } from '@/api/scar'
import Supplierconfirm from '@/views/scar/componets/supplierConfirm.vue'
import mixin from '../mixin/index'
import { parseTime } from '@/utils/ruoyi'
import { getSupplierContact } from '@/api/supplier/info'

export default {
  // SP单据从绩效模块触发，业务上不存在供应商没有关联采购组织的情况，因此在该类型的创建中存在供应商和采购组织的关联关系
  name: 'Sp/:code',
  components: {
    Supplierconfirm,
    baseInfo,
    supplierReply,
    reviewReply,
    approve
  },
  mixins: [mixin],
  provide() {
    return {
      app: this.app,
      badTypeList: this.badTypeList
    }
  },
  data() {
    const badTypeList = this.handleTree(getDictDatas(DICT_TYPE.SCAR_DEFECT_TYPE, 0), 'id')
    return {
      app: {
        scarInfo: {
          companyId: null,
          orgId: null,
          scarCode: '',
          questionFromType: '',
          scarType: 'sp',
          supplierId: null,
          supplierName: '',
          permanentActionDate: '',
          incomingMaterialQuantity: null,
          supplierCode: '',
          containmentActionDate: '',
          scDefectPercent: null,
          severityLevel: '',
          orderNo: '',
          qualityBadType: null,
          specifications: '',
          materialDescription: '',
          selectiveInspectSampleQuantity: null,
          fullInspectDefectQuantity: null,
          questionDescText: '',
          mrbNo: '',
          supplierFirstReplyDate: '',
          inspectionNo: '',
          onTime: false,
          inspectDefectPercent: null,
          nameShort: '',
          qualityComplaintType: '',
          scUsingQuantity: null,
          materialCode: '',
          inspectType: '',
          approveDate: '',
          isStopLine: false,
          passDate: '',
          emails: '',
          isRepetitionBad: true,
          mpn: '',
          spApproveDate: '',
          iqcScType: '',
          categoryId: null,
          batchNo: '',
          mfg: '',
          qualityBadTypeList: [],
          supplierReplyTextRelBaseVO: {},
          fileRelList: [],
          nextOperatorIds: []
        }
      },
      badTypeList,
      viewOnly: false,
      // 绩效反馈跟踪字段1：id，来源入口为绩效反馈跟踪页面，用于创建SP类型单据
      // 当此字段不为空时，则创建SP类型的质量单据时会调用绩效的api回写scarId至绩效反馈跟踪记录中。
      perfid: '',
      // 绩效反馈跟踪字段2: fileId, 用于默认展示绩效处的报告（仅一个）
      fileId: '',
      // 绩效反馈跟踪字段3: companyId, 用于默认展示SP单据的公司
      companyId: ''
    }
  },
  created() {
    this.init()
    this.viewOnly = this.$route.query.viewOnly
    if (this.$route.query.perfid) {
      this.perfid = Number(this.$route.query.perfid)
    }
    this.fileId = this.$route.query.fileId
    this.companyId = this.$route.query.companyId
  },
  methods: {
    init(saveId) {
      if (saveId) {
        getSP({ id: saveId }).then(res => {
          this.$tab.closeOpenPage({ path: `/scar/${res.data.scarType}/${res.data.scarCode}?id=${saveId}` })
        })
      } else {
        const id = this.$route.query.id || this.app.scarInfo.id
        if (id) {
          getSP({ id }).then(res => {
            if (res.data.containmentActionRelList) {
              res.data.containmentActionRelList?.forEach((item, index) => {
                item.sort = index // 后端要求sort
              })
            }
            if (res.data.sqeApproveUsers) {
              res.data.reviewers = res.data.sqeApproveUsers.split(',').map(Number)
            }
            if (res.data.spSaNextOperatorIds) {
              res.data.nextOperatorIds = res.data.spSaNextOperatorIds.split(',').map(Number)
            }
            this.app.scarInfo = res.data
            this.app.scarInfo.createDate = parseTime(this.app.scarInfo.createTime, '{y}-{m}-{d}')
            this.$refs.baseInfo.userList = getDictDatas(DICT_TYPE.COMMON_USERS).filter(user => this.app.scarInfo.nextOperatorIds.includes(user.id))
          })
        } else {
          this.getDateConfig()
          // 以下部分是用于支持绩效那边跳转过来的传参接收，并且初始化SCAR的页面数据 by alex
          if (this.$route.query.supplierId) {
            this.app.scarInfo.supplierId = Number(this.$route.query.supplierId)
            getSupplierContact({
              supplierId: this.app.scarInfo.supplierId,
              contactDivision: 'quality_complaints'
            }).then(res => {
              this.app.scarInfo.emails = res.data?.email
            })
          }
          this.app.scarInfo.questionFromType = this.$route.query.questionFromType
          this.app.scarInfo.supplierName = this.$route.query.supplierName
          this.app.scarInfo.supplierCode = this.$route.query.supplierCode
          this.app.scarInfo.nameShort = this.$route.query.supplierName
        }
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.sp {
  padding: 15px 20px;
}
</style>
