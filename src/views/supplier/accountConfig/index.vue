<template>
  <div class="app-container">
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.selectInfo"
        :placeholder="$t('supplier.pleaseEnterSupplierNameSupplierCodeLoginNameAndUserName')"
        clearable
        style="flex: 0 1 35%"
        @keyup.enter.native="handleQuery"
      />
      <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
    </div>
    <div style="padding-top: 20px">
      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            plain
            size="mini"
            type="primary"
            @click="changeStatus"
          >{{ $t('supplier.enableDisable') }}
          </el-button>
          <download-excel ref="downloadExcel" @click="downLoadExcel" />
        </el-col>

        <el-col :span="1.5" style="float: right">
          <el-button
            icon="el-icon-plus"
            plain
            size="mini"
            type="primary"
            @click="showNew"
          >{{ $t('common.newAccount') }}
          </el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 列表 -->
    <el-table ref="applyTable" :data="list" :loading="loading">
      <el-table-column type="selection" width="30" />
      <el-table-column :label="$t('supplier.supplierCode')" align="left" prop="supplierCode" />
      <el-table-column :label="$t('supplier.supplierName')" align="left" prop="supplierName" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('common.loginName')" align="left" prop="username" />
      <el-table-column :label="$t('common.userName')" align="left" prop="nickname" />
      <el-table-column :label="$t('common.status')" align="left" prop="status" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />

        </template>

      </el-table-column>
      <el-table-column :label="$t('supplier.authorizationRole')" align="left" prop="roles" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('common.email')" align="left" prop="email" />
      <el-table-column :label="$t('common.remarks')" align="left" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column
        :label="$t('supplier.passwordExpirationPerioddays')"
        align="left"
        prop="passwordExpiredPeriod"
        width="100"
      />
      <el-table-column
        :label="$t('supplier.passwordExpirationTime')"
        align="left"
        prop="passwordExpiredTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.passwordExpiredTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center">
        <template #default="scope">
          <el-button type="text" @click="showEdit(scope.row)">{{ $t('common.edit') }}</el-button>
          <el-button type="text" @click="showPass(scope.row)">{{ $t('common.changePassword') }}</el-button>
          <el-button v-hasPermi="['system:mock:user']" type="text" @click="doMockAccount(scope.row)">{{ $t('supplier.simulatedAccount') }}</el-button>
          <el-button v-hasPermi="['system:mock:setPasswordExpiredPeriod']" type="text" @click="setPasswordExpiredPeriod(scope.row)">{{ $t('supplier.setPasswordExpirationPeriod') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <el-dialog
      v-if="newVisible"
      :title="$t('common.newAccount')"
      :visible.sync="newVisible"
      append-to-body
      width="1000px"
    >
      <div style="padding: 0 15px">

        <div style="display: flex;align-items: center">
          <span>
            {{ $t('supplier.supplierName') }}
          </span>
          <el-autocomplete
            v-model="supplierName"
            :fetch-suggestions="querySearchAsync"
            :placeholder="$t('supplier.enterSupplierName')"
            style="width: 60%;margin-left: 20px"
            @select="handleSelect"
            @keyup.enter.native="getAccount"
          />
          <el-button plain type="primary" @click="getAccount">{{ $t('common.search') }}</el-button>
        </div>
        <div style="margin-top: 20px">
          {{ $t('supplier.establishedAccount') }}
          <el-table :data="accountInfo.respList" :max-height="400" style="margin-top: 20px">
            <el-table-column :label="$t('common.userName')" align="center" prop="nickname" />
            <el-table-column :label="$t('common.loginName')" align="center" prop="username" />
            <el-table-column :label="$t('supplier.authorizationRole')" align="center" prop="roles" />
            <el-table-column :label="$t('common.email')" align="center" prop="email" />
          </el-table>
        </div>
      </div>

      <el-card style="margin-top: 35px">
        <el-table ref="account" :data="accountInfo.contactList">
          <el-table-column type="selection" width="30" />
          <el-table-column
            :label="$t('common.userName')"
            align="center"
            prop="nickname"
            width="130"
          >
            <template #default="scope">
              <el-input v-if="scope.row.custom" v-model="scope.row.nickname" />
              <span v-else>
                {{ scope.row.nickname }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('supplier.authorizationRole')">
            <template #default="scope">
              <el-checkbox-group v-model="scope.row.roleIds">
                <el-checkbox v-for="item in roleList" :key="item.id" :label="item.id"> {{ item.name }}</el-checkbox>
              </el-checkbox-group>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('common.email')"
            align="center"
            width="150"
          >
            <template #default="scope">
              <el-input v-model="scope.row.email" />
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('common.remarks')"
            align="center"
            width="150"
          >
            <template #default="scope">
              <el-input v-model="scope.row.remark" />
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('supplier.passwordExpirationPerioddays')"
            align="center"
            width="160"
          >
            <template #default="scope">
              <el-input-number
                v-model="scope.row.passwordExpiredPeriod"
                :min="1"
                :precision="0"
                style="width: 100%"
              />
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('common.operate')"
            align="center"
            width="100"
          >
            <template #default="scope">
              <el-button v-if="scope.row.custom" type="text" @click="accountInfo.contactList.splice(scope.$index,1)">
                {{ $t('common.del') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <i
          v-if="accountInfo.supplierId"
          class="el-icon-circle-plus"
          style="margin-top:20px;margin-left:10px;font-size: 18px;cursor: pointer"
          @click="accountInfo.contactList.push({
            roleIds : [],
            remark : '',
            nickname : '',
            custom : true,
            supplierId:accountInfo.supplierId
          })"
        />
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="newVisible = false"
        >{{ $t('common.cancel') }}
        </el-button>
        <el-button
          type="primary"
          @click="submitSupplier"
        > {{ $t('common.confirm') }}
        </el-button>

      </div>
    </el-dialog>
    <el-dialog
      v-if="mailVisible"
      :before-close="closeMail"
      :title="$t('common.newAccount')"
      :visible.sync="mailVisible"
      append-to-body
      width="1000px"
    >
      <div>
        {{ $t('supplier.supplier') }}
        <span style="font-weight: bold">{{ mailInfo.supplierName }}</span>
        {{ $t('supplier.theAccountHasBeenCreatedDoYouWantToSendAnEmailToTheSupplierAdministrator') }}

      </div>
      <el-table :data="mailInfo.createList" style="margin-top: 20px">
        <el-table-column :label="$t('common.userName')" align="center" prop="nickname" />
        <el-table-column :label="$t('common.loginName')" align="center" prop="username" />
        <el-table-column :label="$t('common.loginPassword')" align="center" prop="password" />
        <el-table-column :label="$t('supplier.authorizationRole')" align="center" prop="roles" />
        <el-table-column :label="$t('common.email')" align="center" prop="email" />
      </el-table>
      <div style="display: flex;justify-content:center;margin-top: 20px">
        <el-form
          ref="mailInfoRef"
          :model="mailInfo"
          label-width="110px"
          style="width: 400px"
        />

      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="()=>{mailVisible = false;newVisible=false}">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="sendNoticeMail"> {{ $t('supplier.sendNotificationEmail') }}</el-button>

      </div>
    </el-dialog>
    <el-dialog
      v-if="editVisible"
      :title="$t('supplier.editAccount')"
      :visible.sync="editVisible"
      width="1000px"
    >
      <el-table :data="editForm" max-height="600px">
        <el-table-column
          :label="$t('common.loginName')"
          prop="username"
          width="130"
        />
        <el-table-column
          :label="$t('common.userName')"
          align="center"
          prop="nickname"
          width="130"
        >
          <template #default="scope">
            <el-input v-model="scope.row.nickname" />

          </template>
        </el-table-column>
        <el-table-column :label="$t('supplier.authorizationRole')">
          <template #default="scope">
            <el-checkbox-group
              v-model="scope.row.roleIds"
            >
              <el-checkbox v-for="item in roleList" :key="item.id" :label="item.id"> {{ item.name }}</el-checkbox>
            </el-checkbox-group>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('common.email')"
          prop="email"
          width="140"
        />
        <el-table-column
          :label="$t('common.remarks')"
          align="center"
          width="150"
        >
          <template #default="scope">
            <el-input v-model="scope.row.remark" />
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitUpdate"> {{ $t('common.confirm') }}</el-button>

      </div>

    </el-dialog>
    <el-dialog
      v-if="passVisible"
      :title="$t('common.changePassword')"
      :visible.sync="passVisible"
      width="1000px"
    >
      <div style="display: flex;justify-content:center;margin-top: 20px">
        <el-form
          ref="pass"
          :model="passForm"
          :rules="passRule"
          label-width="160px"
          style="width: 400px"
        >
          <el-form-item :label="$t('common.loginName')">
            <el-input v-model="passForm.username" disabled />
          </el-form-item>
          <el-form-item :label="$t('common.loginPassword')" prop="password">
            <el-input v-model="passForm.password" type="password" />

          </el-form-item>
          <el-form-item :label="$t('common.confirmPassword')" prop="confirmPassword">
            <el-input v-model="passForm.confirmPassword" type="password" />

          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitPass"> {{ $t('supplier.saveAndSendNotificationEmail') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAllRoles } from '@/api/system/role'
import {
  createAccountRel,
  getAccountRel,
  getAccountRelList,
  getAccountRelPage,
  getSupplierName,
  sendMailAccountRel,
  updateAccountRel,
  updatePass,
  updateStatus
} from '@/api/supplier/account'
import {
  setPasswordExpiredPeriod
} from '@/api/system/user'
import { mockAccount } from '@/api/login'
import { setMockUserToken } from '@/utils/auth'
import { getConfigKey } from '@/api/infra/config'

export default {
  name: 'Accountconfig',
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.passForm.password !== value) {
        callback(new Error(this.$t('common.thePasswordsEnteredTwiceAreInconsistent')))
      } else {
        callback()
      }
    }
    return {
      queryParams: {
        selectInfo: '',
        pageSize: 10,
        pageNo: 1
      },
      total: 0,
      list: [],
      loading: false,
      newVisible: false,
      supplierName: '',
      supplierId: null,
      mailVisible: false,
      passVisible: false,
      roleList: [],
      accountInfo: {
        supplierId: null,
        contactList: [],
        respList: []
      },
      mailInfo: {
        createList: [],
        supplierMail: ''
      },
      editVisible: false,
      editForm: [],
      passForm: {
        password: '',
        confirmPassword: '',
        supplierMail: ''
      },
      passRule: {
        password: [{ required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }],
        confirmPassword: [{ required: true, validator: equalToPassword, trigger: 'blur' }],
        supplierMail: [
          { type: 'email', message: this.$t('supplier.pleaseEnterTheCorrectEmailAddress'), trigger: ['blur', 'change'] },
          { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
        ]
      },
      supplierNameList: [],
      defPasswordExpiredPeriod: 90
    }
  },
  mounted() {
    this.init()
    this.getList()
    getConfigKey('sys.supplier.defPasswordExpiredPeriod').then(response => {
      this.defPasswordExpiredPeriod = response.data
    })
  },
  methods: {
    async init() {
      const data = await listAllRoles({
        isExternal: 2
      })
      this.roleList = data.data
    },
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    changeStatus() {
      const data = this.$refs.applyTable.selection
      if (data.length === 0) {
        this.$message.error(this.$t('supplier.pleaseSelectData'))
        return
      }
      const postData = data.map(item => {
        return {
          ...item,
          status: item.status ? 0 : 1
        }
      })
      updateStatus(postData).then(res => {
        this.$message.success(this.$t('common.updateSuccessful'))
        this.getList()
      })
    },
    getList() {
      this.loading = true
      getAccountRelPage(this.queryParams).then(res => {
        this.list = res.data.list
        this.total = res.data.total
        this.loading = false
      })
    },
    getAccount() {
      getAccountRel({
        supplierId: this.supplierId
      }).then(res => {
        res.data.contactList.forEach(item => {
          item.roleIds = []
          item.remark = ''
          item.nickname = item.name
          item.custom = false
          item.passwordExpiredPeriod = item.passwordExpiredPeriod ? item.passwordExpiredPeriod : this.defPasswordExpiredPeriod
        })
        this.accountInfo = res.data
      })
    },
    showNew() {
      this.newVisible = true
      this.supplierName = ''
      this.supplierId = null
      this.getAccount()
    },
    submitSupplier() {
      const data = this.$refs.account.selection
      if (!data.length) {
        this.$message.error(this.$t('supplier.pleaseSelectAnAccount'))
        return
      }
      if (data.some(item => !item.nickname)) {
        this.$message.error(this.$t('supplier.pleaseFillInTheUserName'))
        return
      }
      if (data.some(item => item.roleIds?.length === 0)) {
        this.$message.error(this.$t('supplier.pleaseSelectAnAuthorizationRole'))
        return
      }
      if (data.some(item => item.passwordExpiredPeriod === undefined || item.passwordExpiredPeriod === null || item.passwordExpiredPeriod === '')) {
        this.$message.error(this.$t('supplier.pleaseEnterThePasswordExpirationPerioddays'))
        return
      }
      createAccountRel(data).then(res => {
        this.$message.success(this.$t('common.createdSuccessfully'))
        this.mailVisible = true
        this.mailInfo = { ...this.mailInfo, ...res.data }
        this.mailInfo.supplierMail = this.mailInfo.supplierContactEmail
      })
    },
    downLoadExcel() {
      getAccountRelPage({ ...this.queryParams, pageNo: -1 }).then(res => {
        const list = res.data.list
        list.forEach(v => v.status = this.getDictDataLabel(this.DICT_TYPE.COMMON_STATUS, v.status))
        this.$refs.downloadExcel.downLoadExcel(
          '供应商账户管理.xlsx',
          '供应商账户管理',
          this.$refs.downloadExcel.exportByElTableRef(this.$refs.applyTable, res.data.list),
          []
        )
      })
    },
    closeMail(close) {
      this.newVisible = false
      close()
      this.getList()
    },
    sendNoticeMail() {
      this.$refs.mailInfoRef.validate(valid => {
        if (valid) {
          sendMailAccountRel({
            ...this.mailInfo,
            infoList: this.mailInfo.createList
          }).then(res => {
            this.$message.success(this.$t('common.successfullySent'))
            this.newVisible = false
            this.mailVisible = false
            this.getList()
          })
        }
      })
    },
    showEdit(row) {
      this.editVisible = true
      getAccountRelList({
        supplierId: row.supplierId
      }).then(res => {
        this.editForm = res.data
      })
    },
    submitUpdate() {
      if (this.editForm.some(item => !item.nickname)) {
        this.$message.error(this.$t('supplier.pleaseFillInTheUserName'))
        return
      }
      if (this.editForm.some(item => item.roleIds?.length === 0)) {
        this.$message.error(this.$t('supplier.pleaseSelectAnAuthorizationRole'))
        return
      }
      updateAccountRel(this.editForm).then(res => {
        this.$message.success(this.$t('common.updateSuccessful'))
        this.editVisible = false
        this.getList()
      })
    },
    showPass(row) {
      this.passVisible = true
      this.passForm = {
        ...this.passForm,
        ...row,
        password: '',
        confirmPassword: '',
        supplierMail: row.email
      }
    },
    setPasswordExpiredPeriod(row) {
      this.$prompt(this.$t('supplier.pleaseEnterThePasswordExpirationPerioddays'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        inputValue: row.passwordExpiredPeriod,
        inputValidator: (value) => {
          if (value === undefined || value === null || value === '') {
            return this.$t('supplier.pleaseEnterThePasswordExpirationPerioddays')
          }
          const regex = new RegExp('^[1-9]\\d*$')
          const isPositiveInteger = regex.test(value)
          if (!isPositiveInteger) {
            return this.$t('supplier.passwordExpirationPerioddaysFormatIncorrect')
          }
          if (Number(value) < 1) {
            return this.$t('supplier.passwordExpirationPerioddaysCannotBeLessThan')
          }
        }
      }).then(({ value }) => {
        setPasswordExpiredPeriod({ id: row.userId, passwordExpiredPeriod: value }).then(() => {
          this.$message.success(this.$t('common.updateSuccessful'))
          this.getList()
        })
      })
    },
    doMockAccount(row) {
      // 模拟账户
      // 1.仅支持选中一个用户
      this.openMockDialog = false
      mockAccount(row.userId).then(res => {
        this.$store.dispatch('ClearRoles')
        setMockUserToken(res.data)
        this.$tab.closeAllPage().then(() => {
          this.$router.push('/index')
        })
      })
    },
    submitPass() {
      this.$refs.pass.validate(valid => {
        if (valid) {
          updatePass({
            ...this.passForm,
            infoList: [{
              nickname: this.passForm.nickname,
              password: this.passForm.password,
              roles: this.passForm.roles,
              userId: this.passForm.userId,
              username: this.passForm.username
            }]
          }).then(res => {
            this.$message.success(this.$t('common.updateSuccessful'))
            this.passVisible = false
            this.getList()
          })
        }
      })
    },
    querySearchAsync(queryString, cb) {
      if (queryString) {
        getSupplierName({ supplierName: this.supplierName }).then(res => {
          res.data.length ? cb(res.data.map(item => {
            return {
              value: item.slice(item.indexOf('-') + 1),
              name: item
            }
          })) : cb([])
        })
      }
    },
    handleSelect(item) {
      this.supplierName = item.name.slice(item.name.lastIndexOf('-') + 1)
      this.supplierId = item.name.slice(0, item.name.indexOf('-'))
      this.getAccount()
    }
  }
}
</script>

<style scoped>

</style>
