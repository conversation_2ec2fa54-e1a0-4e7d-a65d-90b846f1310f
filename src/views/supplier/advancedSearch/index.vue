<template>
  <div class="advance">

    <div style="margin-top: 35px;">
      <div style="display: flex;">
        <search-item
          v-for="item in searchList.searchTypeList"
          style="flex: 0 1 25%"
          :card-data="item"
        />
      </div>

      <div style="margin-top: 35px;">
        <div style="margin-bottom: 20px;margin-left:13px;font-size: 25px">
          {{$t('supplier.country')}}
        </div>

        <div style="display: flex;">
          <region-item
            v-for="i in searchList.regionList"
            style="flex: 0 1 25%"
            :card-data="i"
          />
        </div>

      </div>
      <div style="margin-top: 35px;">
        <div style="margin-bottom: 20px;margin-left:13px;font-size: 25px">
          {{$t('supplier.overview')}}
        </div>

        <div style="display: flex;">
          <region-item
            v-for="i in searchList.entranceList"
            style="flex: 0 1 25%"
            :card-data="i"
          />
        </div>

      </div>

    </div>
  </div></template>
<script>
import SearchItem from '@/views/supplier/advancedSearch/searchItem.vue'
import { getHomepageDetails } from '@/api/supplier/search'
import RegionItem from '@/views/supplier/advancedSearch/regionItem.vue'

export default {
  name: 'AdvancedSearch',
  components: { RegionItem, SearchItem },
  data() {
    return {
      searchList: {
        searchTypeList: [],
        regionList: []
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      getHomepageDetails().then(res => {
        res.data.searchTypeList = Object.keys(res.data.searchTypeList).map(key => {
          return {
            name: key,
            btList: res.data.searchTypeList[key]
          }
        })
        res.data.entranceList = [
          {
            name: '总览',
            url: '/supplier/analysis'
          },
          {
            name: '所有供应商列表',
            url: '/supplier/resource'
          }]
        this.searchList = res.data
        this.$store.commit('setSearchTypeList', res.data.searchTypeList)
      })
    }

  }
}
</script>

<style scoped lang="scss">
.advance{
  padding: 15px 25px;
}

</style>
