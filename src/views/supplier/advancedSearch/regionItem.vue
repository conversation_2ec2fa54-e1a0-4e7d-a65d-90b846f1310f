
<template>
  <div style="padding: 0 10px">
    <el-card class="box-card">
      <div
        style="display: flex;
      flex-wrap: wrap;
      height: 131px;
      justify-content: center;
     font-size: 22px;
     cursor:pointer;
      align-items: center"
        @click="$router.push(cardData.url)"
      >
        {{ getDictDataLabel(DICT_TYPE.SYSTEM_COUNTRY_REGION ,cardData.name) || cardData.name }}
        <span v-if="cardData.count">
          ({{ cardData.count }})
        </span>

      </div>

    </el-card>
  </div>
</template>
<script>
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'

export default {
  name: 'RegionItem',
  props: ['cardData'],
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  methods: { getDictDataLabel }
}
</script>

<style scoped lang="scss">
.box-card {
  padding: 10px;
  border-radius: 8px;
}
::v-deep .el-card__header{
  border-bottom: none;
}
.item{
  flex: 1 1 50%;
  padding:0 5px ;
}
::v-deep .el-button + .el-button{
  margin-left: 0;
}
.item-button{
  border-radius: 20px;
  width: 100%;
  margin-bottom: 10px
}
</style>
