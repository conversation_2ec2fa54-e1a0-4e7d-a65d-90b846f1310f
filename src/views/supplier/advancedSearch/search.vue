<template>
  <div class="search">
    <el-card v-if="queryParams.searchName&& btList.length" style="padding: 10px 5px">
      <div style="font-size: 20px;font-weight: bold;margin-bottom: 15px">{{ queryParams.searchName }}</div>
      <expandable :max-height="42">
        <el-button
          v-for="item in btList"
          :key="item.name"
          style="min-width: 110px;margin-top: 5px"
          round
          :type="item.selected ? 'primary':''"
          @click="item.selected = !item.selected;btList = [...btList];init()"
        >
          <dict-tag :value="item.name" :type="queryParams.searchType" />
        </el-button>
      </expandable>

    </el-card>
    <div style="display: flex">
      <div style="width: 338px;flex:none;padding: 10px 5px">
        <el-card>
          <template #header>
            <div style="display: flex;justify-content: space-between">
              <span style="font-size: 16px">
                <svg-icon icon-class="filter" />
               {{$t('supplier.screen')}}
              </span>
              <el-button type="text" @click="clearFilter">{{$t('rfq.empty')}}</el-button>
            </div>

          </template>
          <div v-for="item in searchQuery" class="filter">
            <div style="margin: 10px 0;font-size: 14px; color: #929292">{{ item.name }}</div>
            <div v-if="item.searchType === 'category'">
              <ul
                class="menu"
                style="
              cursor: pointer;
              padding: 20px 10px;
              border: 1px solid #dfe4ef;
              border-radius: 5px;
            "
              >
                <div style="margin-top: 10px">

                  <li v-if="showFlag1" style="position: relative">
                    <div
                      style="margin-bottom: 5px;"
                      @click="flagLevel1 = !flagLevel1"
                    >
                      一级品类:
                      <i
                        class="iconPost"
                        :class="
                          flagLevel1 ? 'el-icon-caret-bottom' : 'el-icon-caret-top'
                        "
                        size="mini"
                      />
                    </div>

                    <el-collapse>
                      <div
                        v-show="flagLevel1"
                        style="width: 100%; margin-left: 0px"
                      >
                        <div
                          style="
                        border: 1px solid #dfe4ef;
                        text-align: center;
                        padding: 10px 0px;
                        margin-top: 15px;
                        border-radius: 5px;
                      "
                        >
                          <span
                            v-for="item in getDictDatas(DICT_TYPE.COMMON_CATEGORY,0).filter(a=>a.level ===1)"
                            :key="item.id"
                            style="display: inline-block; width: 85%"
                            :class="[
                              selected1 === item.id
                                ? 'selectedMenu'
                                : 'unselectedMenu',
                            ]"
                            @click="selectMenu(item, 1)"
                          >{{ item.name }}</span>
                        </div>
                      </div>
                    </el-collapse>
                  </li>

                  <li v-if="showFlag2" style="position: relative">
                    <div
                      style="border-bottom: 2px solid"

                      @click="flagLevel2 = !flagLevel2"
                    >
                      二级品类:
                      <i
                        class="iconPost"
                        :class="
                          flagLevel2 ? 'el-icon-caret-bottom' : 'el-icon-caret-top'
                        "
                        size="mini"
                      />
                    </div>

                    <el-collapse v-model="flagLevel2">
                      <div
                        v-show="flagLevel2"
                        style="width: 100%; margin-left: 0px"
                      >
                        <div
                          style="
                        border: 1px solid #dfe4ef;
                        text-align: center;
                        padding: 10px 0px;
                        margin-top: 15px;
                        border-radius: 5px;
                      "
                        >
                          <span
                            v-for="item in catalogList2"
                            :key="item.id"
                            style="display: inline-block; width: 85%"
                            :class="[
                              selected2 == item.id
                                ? 'selectedMenu'
                                : 'unselectedMenu',
                            ]"
                            @click="selectMenu(item, 2)"
                          >{{ item.name }}
                          </span>
                        </div>
                      </div>
                    </el-collapse>
                  </li>
                  <li
                    v-if="showFlag3 && catalogList3.length > 0"
                    style="position: relative"
                  >
                    <div
                      style="border-bottom: 2px solid"

                      @click="flagLevel3 = !flagLevel3"
                    >
                      三级品类:
                      <i
                        class="iconPost"
                        :class="
                          flagLevel3 ? 'el-icon-caret-bottom' : 'el-icon-caret-top'
                        "
                        size="mini"
                      />
                    </div>

                    <el-collapse>
                      <div
                        v-show="flagLevel3"
                        style="width: 100%; margin-left: 0px"
                      >
                        <div
                          style="
                        border: 1px solid #dfe4ef;
                        text-align: center;
                        padding: 10px 0px;
                        margin-top: 15px;
                        border-radius: 5px;
                      "
                        >
                          <span
                            v-for="(item, index) in catalogList3"
                            :key="index"
                            style="display: inline-block; width: 85%"
                            :class="[
                              selected3.includes(item.id)
                                ? 'selectedMenu'
                                : 'unselectedMenu',
                            ]"
                            @click="selectMenu(item, 3)"
                          >{{ item.name }}
                          </span>
                        </div>
                      </div>
                    </el-collapse>
                  </li>
                </div>
              </ul>

            </div>

            <div v-if="['staff','sale'].includes(item.searchType)">
              <div style="display: flex">
                <vxe-input
                  v-model.number="item.searchValues[0]"
                  type="number"
                  :placeholder="$t('supplier.least')"
                  style="width: 80px"
                  clearable />
                <span style="margin: 0 5px">——</span>
                <vxe-input
                  v-model.number="item.searchValues[1]"
                  type="number"
                  :placeholder="$t('supplier.most')"
                  style="width: 80px"
                  clearable />
              </div>
              <div style="text-align: right">
                <el-button type="primary" @click="init">{{$t('order.determine')}}</el-button>
              </div>
            </div>

            <el-scrollbar class="filterItem">
              <el-checkbox-group
                v-if="[
                  'supplier_company_nature',
                  'supplier_type',
                  'qualification_and_certification',
                  'province'
                ].includes(item.searchType)"
                v-model="item.searchValues"
                clearable
                filterable
                @change="init"
              >
                <el-checkbox
                  v-for="re in getDictDatas(item.searchType)"
                  :key="re.keyId"
                  style="display: block;margin-bottom: 5px"
                  :label="re.value"
                  :value="re.value"
                >
                  {{ re.label }}
                </el-checkbox>
                <el-checkbox
                  v-for="re in getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(item => item.type === 'region')"
                  v-if="item.searchType === 'province'"
                  :key="re.keyId"
                  style="display: block;margin-bottom: 5px"
                  :label="re.id"
                  :value="re.id"
                >
                  {{ re.name }}
                </el-checkbox>
              </el-checkbox-group>
            </el-scrollbar>
          </div>

        </el-card>
      </div>

      <div style="flex: auto;padding: 10px 5px">
        <el-card style="padding: 10px">
          <div class="searchText" style="width: 60%;display: inline-flex;margin-bottom: 15px">
            <el-input v-model="queryParams.searchText" @keyup.enter.native="init" />
            <el-button
              style="border-radius: 0px 25px 25px 0px;padding: 0 26px"
              icon="el-icon-search"
              type="primary"
              round
              @click="init"
            />
          </div>

          <div
            v-loading="loading"
          >

            <el-descriptions
              v-for="item in tableData"
              class="description"
              :label-style="{ color: '#929292'}"
              :column="7"
              direction="vertical"
            >
              <template #title>
                <div style="display: flex;justify-content: space-between;width: 100%">
                  <div style="font-size: 18px;font-weight: bold">{{ item.supplierName }}</div>
                  <el-button
                    plain
                    type="primary"
                    round
                    @click="$router.push(`/supplier/supplierinfo/${item.supplierId}?supplierName=${item.supplierName}&viewOnly=true`)"
                  >{{$t('system.details')}}</el-button>
                </div>
              </template>
              <el-descriptions-item :label="$t('supplier.region')">
                <dict-tag :type="DICT_TYPE.COMMON_COUNTRY" :value="item.province" />
              </el-descriptions-item>
              <el-descriptions-item :label="$t('material.category')">
                <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="item.categoryId" />
              </el-descriptions-item>
              <el-descriptions-item :label="$t('supplier.numberOfPeople')">{{ item.staff }}</el-descriptions-item>
              <el-descriptions-item :label="$t('supplier.level')">
                <dict-tag :type="DICT_TYPE.SL_SUPPLIER_LEVEL" :value="item.levelStatus" />

              </el-descriptions-item>
              <el-descriptions-item :label="$t('supplier.achievements')">{{ item.pi }}</el-descriptions-item>
              <el-descriptions-item :label="$t('supplier.establishedTime')">
                {{ item.establishedTime?dayjs(item.establishedTime).format('YYYY-MM-DD'):'' }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('supplier.salesVolume')">{{ item.sale }}
                <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="item.currency" />
              </el-descriptions-item>

            </el-descriptions>
            <el-empty v-if="tableData.length === 0" :image-size="200" />
            <pagination
              v-show="total > 0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNo"
              :total="total"
              @pagination="init"
            />
          </div>

        </el-card>
      </div>
    </div>
  </div>
</template>
<script>
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { pagingSearchResult } from '@/api/supplier/search'
import dayjs from 'dayjs'
import { mapState } from 'vuex'
import Expandable from '@/components/Expand/index.vue'

export default {
  name: 'Search',
  components: { Expandable },
  data() {
    return {
      tableData: [
      ],
      countryRegion: [],
      searchQuery: [
        {
          name: '品类',
          searchType: 'category',
          searchValues: []
        },
        {
          name: '地区',
          searchType: 'province',
          searchValues: []
        },
        {
          name: '销售额',
          searchType: 'sale',
          searchValues: [null, null]
        },
        {
          name: '公司性质',
          searchType: 'supplier_company_nature',
          searchValues: []
        },
        {
          name: '公司类型',
          searchType: 'supplier_type',
          searchValues: []
        },
        {
          name: '人数',
          searchType: 'staff',
          searchValues: [null, null]
        },
        {
          name: '经营资质',
          searchType: 'qualification_and_certification',
          searchValues: []
        }
      ],
      catalogList2: [],
      catalogList3: [],
      selected: '',
      selected1: '',
      selected2: '',
      selected3: [],
      flag1: true,
      flagLevel0: true,
      flagLevel1: true,
      flagLevel2: false,
      flagLevel3: false,
      flagSearch: true,
      showFlag1: true,
      showFlag2: false,
      showFlag3: false,
      total: 0,
      queryParams: {
        pageSize: 10,
        pageNo: 1,
        searchText: '',
        searchType: '',
        searchValues: []
      },
      loading: false,
      btList: []
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    },
    ...mapState({
      searchTypeList: state => state.advanceSearch.searchTypeList
    })
    // filterSearch: this.searchTypeList.filter(a=>a.searchType === )
  },
  mounted() {
    this.queryParams.searchType = this.$route.query.searchType
    this.queryParams.searchName = this.$route.query.searchName
    const temp = this.searchTypeList.filter(a => a.name === this.$route.query.searchType)[0].btList
    temp.forEach(a => {
      a.selected = a.name === this.$route.query.selected
    })
    this.btList = temp
    this.init()
  },
  methods: {
    dayjs, getDictDatas,
    selectMenu(item, num1) {
      if (num1 === 1) {
        this.selected1 = item.id
        this.selected2 = ''
        this.selected3 = []
        this.flagLevel1 = true
        this.flagLevel2 = true
        this.showFlag1 = true
        this.showFlag2 = true
        this.catalogList2 = this.getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0).filter(a => a.level === 2 && a.parentId === item.id)
      } else if (num1 === 2) {
        this.selected2 = item.id
        this.selected3 = []
        this.flagLevel2 = true
        this.flagLevel3 = true
        this.showFlag2 = true
        this.showFlag3 = true
        this.catalogList3 = this.getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0).filter(a => a.level === 3 && a.parentId === item.id)
      } else {
        this.flagLevel2 = true
        this.flagLevel3 = true
        if (this.selected3.includes(item.id)) {
          this.selected3 = this.selected3.filter(a => a !== item.id)
        } else {
          this.selected3.push(item.id)
        }
      }
      this.init()
    },
    init() {
      this.loading = true
      this.searchQuery.find(a => a.searchType === 'category').searchValues = this.selected3.length ? this.selected3 : this.selected2 ? [this.selected2] : [this.selected1]
      pagingSearchResult({ ...this.queryParams,
        searchValues: this.btList.filter(a => a.selected).map(a => a.name),
        filterReqVOS: this.searchQuery.filter(a => a.searchValues.length && a.searchValues.every(b => ![null, ''].includes(b)))
      }).then(res => {
        this.loading = false
        this.tableData = res.data.list
        this.total = res.data.total
      })
    },
    clearFilter() {
      this.selected1 = ''
      this.selected2 = ''
      this.selected3 = []
      this.searchQuery = [
        {
          name: '品类',
          searchType: 'category',
          searchValues: []
        },
        {
          name: '地区',
          searchType: 'province',
          searchValues: []
        },
        {
          name: '销售额',
          searchType: 'sale',
          searchValues: [null, null]
        },
        {
          name: '公司性质',
          searchType: 'supplier_company_nature',
          searchValues: []
        },
        {
          name: '公司类型',
          searchType: 'supplier_type',
          searchValues: []
        },
        {
          name: '人数',
          searchType: 'staff',
          searchValues: [null, null]
        },
        {
          name: '经营资质',
          searchType: 'qualification_and_certification',
          searchValues: []
        }
      ]
      this.init()
    }
  }
}
</script>

<style scoped lang="scss">
.filter{
  padding-bottom: 10px;
  border-bottom: 1px solid #eaeaea;
  .filterItem {
    ::v-deep .el-scrollbar__wrap {
      max-height: 200px;
      overflow-x: hidden;
    }
  }
}

.search{
  padding: 15px 25px;
}
.searchText{
  ::v-deep .el-input__inner{
    border-radius: 25px 0 0 25px
  }
}

::v-deep .el-descriptions__header{
  margin-bottom: 0;
}

::v-deep .el-descriptions__title{
  width: 100%;
}
.description:not(:last-child){
  border-bottom: 1px solid #eaeaea;
  margin-bottom: 10px;
}
.menu {
  list-style: none;
  margin: 10px 0;
  font-size: 14px;
  li {
    width: 100%;
    margin: 5px 0;
    // display: flex;
    div {
      // width: 85%;
      margin-left: 5px;
    }
    .select-label {
      width: 100%;
      margin-top: 10px;
      font-weight: bold;
      font-size: 14px;
      // position:absolute;
      // top:5px
    }
  }
}
.selectedMenu {
  background: #4d93b9;
  color: white;
  padding: 2px 15px;
  margin-right: 3%;
  border-radius: 2px;
  display: inline-block;
  min-width: 70px;
}
.unselectedMenu {
  color: #295b93;
  padding: 2px 15px;
  margin-right: 3%;
  display: inline-block;
  min-width: 70px;
}
</style>
