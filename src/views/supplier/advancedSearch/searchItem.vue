
<template>
  <div style="padding: 0 10px">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <el-row :gutter="35">
          <el-col :span="4">
            <i
              class="el-icon-s-data"
              style="color: #4d93b9; font-size: 36px;"
            />
          </el-col>
          <el-col
            :span="20"
          >
            <div>Search by</div>
            <div
              style="cursor:pointer;font-weight: bold;font-size: 18px"
              @click="toSearch()"
            >
              {{ filterMap[cardData.name] }}
              &gt;</div>
          </el-col>
        </el-row>
      </div>
      <div style="display: flex;flex-wrap: wrap;height: 131px">
        <div
          v-for="item in cardData.btList.filter((item, index) => index <= (cardData.btList.length === 6? 5: 4))"
          class="item"
          :style="{
            flex: `0 1 ${cardData.btList.length > 3? 50: 100}%`
          }"
        >
          <el-button
            class="item-button"
            @click="toSearch(item)"
          >
            <dict-tag :value="item.name" :type="cardData.name" />({{ item.count }})
          </el-button>

        </div>
        <div
          v-if="cardData.btList.length > 6"
          class="item"
          :style="{
            flex: `0 1 ${cardData.btList.length > 3? 50: 100}%`
          }"
        >
          <el-button
            class="item-button"

            @click="toSearch()"
          >...</el-button>
        </div>
      </div>

    </el-card>
  </div>
</template>
<script>
export default {
  name: 'SearchItem',
  props: ['cardData'],
  data() {
    return {
      filterMap: {
        category: '品类',
        region: '省份',
        supplier_level_status: '供应商级别'
      }
    }
  },
  methods: {
    toSearch(item) {
      this.$router.push({
        path: '/supplier/search',
        query: {
          searchType: this.cardData.name,
          searchName: this.filterMap[this.cardData.name],
          selected: item?.name
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.box-card {
  padding: 10px;
  border-radius: 8px;
}
::v-deep .el-card__header{
  border-bottom: none;
}
.item{
  flex: 1 1 50%;
  padding:0 5px ;
}
::v-deep .el-button + .el-button{
  margin-left: 0;
}
.item-button{
  border-radius: 20px;
  width: 100%;
  margin-bottom: 10px
}
</style>
