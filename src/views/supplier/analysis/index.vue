<template>
  <div class="analysis">
    <div style="display: flex;justify-content: space-between;">

      <div style="font-size: 28px;font-weight: 700">{{$t('supplier.panoramicViewOfSupplierLibrary')}}</div>
      <div style="display: flex">
        <div class="headFlex" style="border-right: 1px solid #eaeaea;">
          <div class="commonTitle">{{$t('supplier.totalNumberOfSuppliers')}}</div>
          <div class="commonValue">{{ supplier.countAll }}</div>
        </div>
        <div class="headFlex">
          <div class="commonTitle">{{$t('supplier.totalNumberOfActiveSuppliers')}}</div>
          <div class="commonValue">{{ supplier.countActive }}</div>
        </div>
        <div class="headFlex">
          <div class="commonTitle">{{$t('supplier.proportionOfActiveSuppliers')}}</div>
          <div class="commonValue">{{ (supplier.countActive/supplier.countAll*100).toFixed(0) }}%</div>
        </div>
      </div>

    </div>
    <div class="grid-container" style="margin-top: 20px">
      <div class="grid-item" style="border-top: 1px solid #eaeaea;">
        <div class="commonTitle" style="margin-top: 25px;margin-left: 20px">{{$t('supplier.distributionArea')}}</div>
        <new-pie
          :chart-data="province"
          height="250px"
          :key-value="{ value: 'countNum', name: 'provinceId',dict:DICT_TYPE.COMMON_COUNTRY }"
        />
      </div>
      <div class="grid-item" style="border-top: 1px solid #eaeaea;">
        <div class="commonTitle" style="margin-top: 25px;margin-left: 20px">{{$t('system.type')}}</div>
        <new-pie
          height="250px"
          :chart-data="companyCategory"
          :key-value="{ value: 'countNum', name: 'companyCategory' ,dict:DICT_TYPE.SUPPLIER_TYPE}"
        />

      </div>
      <div class="grid-item">
        <div class="commonTitle" style="margin-top: 25px;margin-left: 20px">
          {{ $t('supplier.ytdSupplierLevel')}}
          <el-popover
            placement="top-start"
            width="350"
            trigger="hover"
            :content="$t('supplier.distributionOfSupplierLevelsFromThisYearToTodayyearToDate')"
          >
            <i slot="reference" class="el-icon-info" style="font-size: 14px" />
          </el-popover>
        </div>

        <bar-chart
          :chart-data="ytdLevel"
        />
      </div>
      <div class="grid-item">
        <div class="commonTitle" style="margin-top: 25px;margin-left: 20px;display: flex;justify-content: space-between">
          <div>
            {{ $t('supplier.ytdSupplierPerformance')}}
            <el-popover
              placement="top-start"
              width="350"
              trigger="hover"
              :content="$t('supplier.distributionOfSupplierLevelsFromThisYearToTodayyearToDate')"
            >
              <i slot="reference" class="el-icon-info" style="font-size: 14px" />
            </el-popover>
          </div>
          <span style="color: #929292;font-size: 14px">
            {{ $t('supplier.theScoreRangeForAbcdLevelsMayVaryDependingOnTheCompanysConfigurationForMoreInformationPleaseClickHere')}}
            <el-button type="text" @click="$router.push('/sp/overview')">{{$t('system.details')}}</el-button>
          </span>

        </div>
        <div>
          <performance-chart :chart-data="ytdData" />
        </div>

      </div>
      <div class="grid-item" style="border-bottom: 1px solid #eaeaea;">
        <div class="commonTitle" style="margin-top: 25px;margin-left: 20px;display: flex;justify-content: space-between">
          <span>
            {{ $t('supplier.introductioneliminationOfSuppliersInThePastMonths')}}
          </span>
          <el-button type="text" @click="downloadSupplier">{{$t('order.download')}}</el-button>

        </div>
        <div style="padding: 10px">

          <el-table
            style="margin-top: 15px"
            :data="supplierData"
          >
            <el-table-column width="80" :label="$t('supplier.time')" prop="monthAndYear" align="right" />
            <el-table-column :label="$t('supplier.registerSuppliers')" prop="registration" />
            <el-table-column :label="$t('supplier.introduceSuppliers')" prop="introduce">
              <template #default="scope">
                <div style="display: flex;background: #EADFFF;padding-left: 5px">
                  {{ scope.row.introduce }}
                  <div style="background: #EADFFF;" :style="`width:${scope.row.introduce * 100 / maxIntroduce}%`" />
                  <div style="background: #f9f9f9;" :style="`width:${100 - scope.row.introduce * 100 / maxIntroduce}%`" />
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('supplier.eliminateSuppliers')" prop="elimination">
              <template #default="scope">
                <div style="display: flex;background: #EAEAEA;padding-left: 5px">
                  {{ scope.row.elimination }}
                  <div style="background: #EAEAEA;" :style="`width:${scope.row.elimination * 100 / maxElimination}%`" />
                  <div style="background: #f9f9f9;" :style="`width:${100 - scope.row.elimination * 100 / maxElimination}%`" />
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

      </div>
      <div class="grid-item" style="border-bottom: 1px solid #eaeaea;">
        <div class="commonTitle" style="margin-top: 25px;margin-left: 20px;display: flex;justify-content: space-between">
          <span>
            {{ $t('supplier.topCategorySupplierGrading')}}
            <el-popover
              placement="top-start"
              width="300"
              trigger="hover"
            >
              {{ $t('supplier.accordingToTheSizeOfThePurchaseAmountTheLevelOfSuppliersInTheTopCategories')}}
              <div style="margin: 5px 0;display: flex">
                <div class="square" style="background: #F7BCC0 " />
                {{ $t('supplier.thereIsOnlyOneQualifiedAndAboveNonFrozenAndNonBlacklistedSupplier')}}
              </div>
              <div style="display: flex">
                <div class="square" style="background: #F8BD83 " />
                {{ $t('supplier.thereAreOnlyQualifiedAndAboveNonFrozenAndNonBlacklistedSuppliers')}}
              </div>

              <i slot="reference" class="el-icon-info" style="font-size: 14px" />
            </el-popover>
          </span>
          <el-button type="text" @click="$router.push('/sl/supplierLevel')">{{$t('system.details')}}</el-button>

        </div>
        <div style="padding: 10px">
          <el-table
            style="margin-top: 15px"
            :data="slData"
          >
            <el-table-column type="index" :label="$t('supplier.ranking')" width="60" align="center" />
            <el-table-column :label="$t('material.category')">
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="scope.row.categoryId" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('supplier.location')" prop="position">
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY" :value="scope.row.position" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('supplier.qualifiedSupplier')" prop="countNumS4">
              <template #default="scope">
                <el-button
                  :class="slClass(scope.row,scope.row.countNumS4)"
                  style="width: 80%;margin: 5px 0"
                >
                  {{ scope.row.countNumS4 }}

                </el-button>
              </template>
            </el-table-column>
            <el-table-column :label="$t('supplier.preferredSupplier')" prop="countNumS5">
              <template #default="scope">
                <el-button
                  :class="slClass(scope.row,scope.row.countNumS5)"
                  style="width: 80%;margin: 5px 0"
                >
                  {{ scope.row.countNumS5 }}
                </el-button>
              </template>

            </el-table-column>
            <el-table-column :label="$t('supplier.potentialSuppliers')" prop="countNumS1">
              <template #default="scope">
                <el-button
                  :class="slClass(scope.row,scope.row.countNumS1)"
                  style="width: 80%;margin: 5px 0"
                >
                  {{ scope.row.countNumS1 }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>

        </div>
      </div>
    </div>

  </div>
</template>
<script>
import {
  statisticsSupplierAll,
  statisticsSupplierCompanyCategory,
  statisticsSupplierLevelCategory,
  statisticsSupplierLevelDistribution,
  statisticsSupplierOverMonth, statisticsSupplierOverMonthExport,
  statisticsSupplierProvince,
  statisticsSupplierProvinceExport,
  statisticsSupplierSp,
  statisticsSupplierSpExample
} from '@/api/supplier/resourceRepository'
import { DICT_TYPE } from '@/utils/dict'
import NewPie from '@/views/supplier/charts/newPie.vue'
import BarChart from '@/views/supplier/charts/barChart.vue'
import PerformanceChart from '@/views/supplier/charts/performanceChart.vue'

export default {
  name: 'Index',
  components: { PerformanceChart, BarChart, NewPie },
  data() {
    return {
      supplier: {
        countAll: 0,
        countActive: 0
      },
      province: [],
      companyCategory: [],
      exportLoading: false,
      ytdLevel: [],
      ytdData: [],
      ytdExample: [],
      slData: [],
      supplierData: []
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    },
    maxIntroduce() {
      return Math.max(...this.supplierData.map(a => a.introduce))
    },
    maxElimination() {
      return Math.max(...this.supplierData.map(a => a.elimination))
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getSupplierTotal()
      this.getProvince()
      this.getCompanyCategory()
      this.getYTDLevel()
      this.getYTDData()
      this.getSlData()
      this.getSupplierData()
    },
    getSupplierTotal() {
      statisticsSupplierAll().then(res => {
        this.supplier = res.data
      })
    },
    getProvince() {
      statisticsSupplierProvince().then(res => {
        this.province = res.data
      })
    },
    getCompanyCategory() {
      statisticsSupplierCompanyCategory().then(res => {
        this.companyCategory = res.data
      })
    },
    downloadProvince() {
      this.$modal.confirm(this.$t('supplier.areYouSureToExport')).then(() => {
        this.exportLoading = true
        return statisticsSupplierProvinceExport()
      }).then(response => {
        this.$download.excel(response, this.$t('supplier.supplierRegionalStatisticsXlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    downloadSupplier() {
      this.$modal.confirm(this.$t('supplier.areYouSureToExport')).then(() => {
        this.exportLoading = true
        return statisticsSupplierOverMonthExport()
      }).then(response => {
        this.$download.excel(response, this.$t('supplier.introductioneliminationOfSuppliersXlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    getYTDLevel() {
      statisticsSupplierLevelDistribution().then(res => {
        this.ytdLevel = res.data
      })
    },
    getYTDData() {
      statisticsSupplierSp().then(res => {
        this.ytdData = res.data
      })
      statisticsSupplierSpExample().then(res => {
        this.ytdExample = res.data
      })
    },
    getSlData() {
      statisticsSupplierLevelCategory().then(res => {
        this.slData = res.data
      })
    },
    getSupplierData() {
      statisticsSupplierOverMonth().then(res => {
        this.supplierData = res.data
      })
    },
    slClass(row, val) {
      let temp = ''
      if (row.countNumS4 + row.countNumS5 + row.countNumS1 === 1) {
        temp = 'danger'
      } else if (row.countNumS4 + row.countNumS5 + row.countNumS1 === 2) {
        temp = 'warn'
      }
      if (val === 0) {
        return 'blank'
      } else {
        return temp
      }
    }
  }
}
</script>

<style scoped lang="scss">
.danger{
  background: #F7BCC0
}
.warn{
  background: #F8BD83
}
.blank{
  background: #F9F9F9;
  color: #929292;
  border: none
}
.analysis {
  padding: 15px 25px;
}
.commonTitle{
  font-size: 18px;
  margin-right: 20px;
}
.commonValue{
  font-weight: 700;
  font-size: 28px;
}
.headFlex{
  display: flex;
  align-items: center;
  margin-right: 20px;
  padding-right: 20px;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 3 列网格 */
  grid-template-rows: repeat(2, 372px); /* 2 行网格，每行高度 100px */
  gap: 1px; /* 控制网格轴线的宽度 */
  background: #eaeaea;
}

.grid-item {
  background-color: #fff; /* 网格单元背景色 */
  //display: flex;
  //justify-content: center;
  //align-items: center;
  font-size: 20px;
  background-clip: content-box; /* 内容裁剪，确保背景只应用于内容，不包括间隙 */
}

.realTab {
  width: 220px;
  height: 140px;
  font-size: 16px;
  background: rgb(242, 244, 249);
  color: #000000;
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding: 21px;
}

.realNum {
  text-align: center;
  color: #327da1;
  font-size: 25px;
}

.chartTitle {
  color: #4d93b9;
  font-weight: bold;
  font-size: 18px;
}

::v-deep .el-table .el-table__header-wrapper tr{
  background: #fff;
}
::v-deep .el-table .el-table__header-wrapper th{
  background: #fff;
  color: #807D7D;
  border: none;
  font-size: 14px!important;
}
::v-deep .el-table::before, .el-table--group::after, .el-table--border::after{
  background: #fff;
}
.el-table{
  ::v-deep td{
    border: none;
  }
}
.square {
  width: 10px;
  height: 10px;
  flex: none;
  margin-right: 5px;
  margin-top: 6px;
}
</style>
