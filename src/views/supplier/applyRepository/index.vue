<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.name"
        :placeholder="$t('supplier.pleaseEnterTheSupplierName')"
        clearable
        style="flex: 0 1 35%"
        @keyup.enter.native="handleQuery"
      />
      <el-button icon="el-icon-search" type="primary" plain @click="handleQuery">{{ $t('common.search') }}</el-button>
    </div>

    <!--    切换tab-->
    <div>
      <el-tabs v-model="selectedSourcingType" @tab-click="triggerSourcingType">
        <el-tab-pane :label="$t('supplier.invitationRegistry')" name="invite" />
        <el-tab-pane :label="$t('supplier.independentRegistry')" name="self" />
      </el-tabs>
    </div>
    <div style="padding-top: 20px">
      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-if="selectedSourcingType === 'self'"
            v-hasPermi="['supplier:apply:introduction']"
            icon="el-icon-check"
            size="mini"
            plain
            type="primary"
            @click="allowIntroduction"
          >{{ $t('supplier.permissionToIntroduce') }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['supplier:apply:invite']"
            :loading="exportLoading"
            icon="el-icon-plus"
            size="mini"
            type="primary"
            @click="openInviteModal"
          >{{ $t('supplier.inviteNewSuppliers') }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['supplier:apply:delete']"
            :loading="exportLoading"
            icon="el-icon-delete"
            plain
            size="mini"
            type="danger"
            @click="deleteSupplier"
          >{{ $t('common.del') }}
          </el-button>
        </el-col>

      </el-row>
    </div>

    <!-- 列表 -->
    <el-table ref="applyTable" :data="list" :header-row-style="{height: '48px'}" :loading="loading" border>
      <el-table-column type="selection" width="30" />
      <el-table-column :label="$t('supplier.supplierName')" align="left" prop="name">
        <template slot-scope="scope">
          <copyButton
            :key="scope.row.name"
            type="text"
            @click="$router.push(`/supplier/supplierinfo/${scope.row.id}?supplierName=${scope.row.name}`)"
          >
            {{ scope.row.name }}</copyButton>
        </template>
      </el-table-column>
      <el-table-column :label="$t('supplier.companyWebsite')" align="left" prop="webSite" />
      <el-table-column :label="$t('common.status')" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SUPPLIER_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('supplier.updateTime')" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('supplier.nameOfSuppliedProduct')" align="left" prop="productName" />
      <el-table-column
        v-if="selectedSourcingType === 'invite'"
        :label="$t('common.operate')"
        align="center"
        prop="invitationModification"
        width="50"
      >
        <template slot-scope="{row}">
          <OperateDropDown
            :menu-item="[
              {
                name: $t('supplier.invitationModification'),
                show: $store.getters.permissions.includes('supplier:resource:invite:modify'),
                type: 'text',
                action: (row) => openInviteUpdateModal(row),
                para: row
              }
            ]"
          />
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
    <!--邀请供应商注册-->
    <InviteNewSupplier ref="inviteNewComponent" :type="'apply'" @refresh="getList" />

    <!--邀请供应商修改-->
    <el-dialog :before-close="cancel" :title="title" :visible.sync="visible" append-to-body width="500px">
      <div>
        <span style="margin-top: 10px;">{{ $t('supplier.youAreAboutToShareTitle') }}</span>
        <el-divider />
        <el-form inline label-position="left">
          <el-form-item :label="$t('supplier.invitationEmailAddress')">
            <div v-for="(item,index) in emailList" :key="index">
              <el-input v-model="emailList[index]" style="width: 200px" />
              <i
                class="el-icon-circle-plus"
                style="margin-left:10px;font-size: 18px;cursor: pointer"
                @click="emailList.push('')"
              />
              <i
                v-if="index !==0"
                class="el-icon-remove"
                style="margin-left:10px;font-size: 18px;cursor: pointer"
                @click="emailList.splice(index,1)"
              />
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="sendEmail">{{ $t('common.submit') }}</el-button>

      </div>
    </el-dialog>
    <el-dialog
      :title="$t('common.shareInvitationLink')"
      :visible.sync="shareInvitationLink.open"
      append-to-body
      width="500px"
    >
      <el-form :model="shareInvitationLink" label-width="130px">
        <span>{{ shareInvitationLink.supplierInvitationAddress }}</span>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="shareInvitationLink.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button v-clipboard:copy="shareInvitationLink.supplierInvitationAddress" v-clipboard:success="()=>{$modal.msgSuccess('复制成功');shareInvitationLink.open =false}" type="primary">{{ $t('order.copy') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deleteSupplier, getApplySupplierPage, introduction } from '@/api/supplier/applyRepository'
import { inviteModify } from '@/api/supplier/resourceRepository'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import InviteNewSupplier from '../inviteNewSupplier'
import OperateDropDown from '@/components/OperateDropDown/index.vue'

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Apply',
  components: { OperateDropDown, InviteNewSupplier },
  data() {
    return {
      // 供应商来源type，默认是invite-邀请登记库
      selectedSourcingType: 'invite',
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应商列表
      list: [],
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        type: this.selectedSourcingType,
        name: null
      },
      // 表单参数
      form: {},
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS),
      // 遮罩层
      visible: false,
      title: this.$t('supplier.shareLinksToSuppliers'),
      // 邀请修改的供应商存在主要联系人的邮箱，则需要再次进行默认展示
      emailList: [],
      // 变更供应商状态
      shareInvitationLink: {
        // 是否显示弹出层
        open: false,
        // 供应商名称
        supplierInvitationAddress: ''
      },
      // 触发modal的供应商id
      currentSupplierId: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 准许引入供应商
    allowIntroduction() {
      const rows = this.$refs.applyTable.selection
      if (rows.length === 0) {
        this.$message.warning(this.$t('sl.pleaseSelectAtLeastOneDataItem'))
        return
      }
      const supplierIds = rows.map(row => {
        return row.id
      })
      this.$modal.confirm(this.$t('supplier.importSelectedSupplier')).then(function() {
        return introduction({ 'supplierIds': supplierIds })
      }).then(() => {
        this.$modal.msgSuccess(this.$t('supplier.successfullyImported'))
        this.getList()
      }).catch(() => {
      })
    },
    // 打开邀请供应商弹框
    openInviteModal() {
      this.$refs.inviteNewComponent.visible = true
    },
    // 打开供应商邀请弹框
    openInviteUpdateModal(row) {
      this.visible = true
      // 添加默认显示的供应商邮件
      this.emailList.push(row.mainEmail)
      this.currentSupplierId = row.id
    },
    // 删除
    deleteSupplier() {
      const rows = this.$refs.applyTable.selection
      if (rows.length === 0) {
        this.$message.warning(this.$t('sl.pleaseSelectAtLeastOneDataItem'))
        return
      }
      const supplierIds = rows.map(row => {
        return row.id
      })
      this.$modal.confirm(this.$t('supplier.areYouSureToDeleteTheSelectedSupplier')).then(function() {
        return deleteSupplier({ 'supplierIds': supplierIds })
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      this.queryParams.type === undefined || this.queryParams.type === '' ? this.queryParams.type = 'invite' : this.queryParams.type
      const params = { ...this.queryParams }
      // 执行查询
      getApplySupplierPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 切换供应商来源类型 */
    triggerSourcingType() {
      this.queryParams.type = this.selectedSourcingType
      this.getList()
    },
    // 发送邮件给供应商进行修改
    sendEmail() {
      if (this.emailList.filter(email => email !== undefined && email !== '').length === 0) {
        this.$message.error(this.$t('supplier.pleaseEnterEmail'))
        return
      }
      // 校验邮箱格式
      if (!this.emailList.every(email => /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(email))) {
        this.$message.error(this.$t('supplier.incorrectMailboxFormat'))
        return
      }

      inviteModify({'emails': this.emailList, 'supplierId': this.currentSupplierId}).then(res => {
        this.shareInvitationLink.open = true
        this.shareInvitationLink.supplierInvitationAddress = res.data
      }).catch(() => {
        this.$message.error(this.$t('supplier.sendingInvitationModificationLinkFailed'))
      })
      this.cancel()
    },
    // 取消发送
    cancel() {
      this.visible = false
      this.emailList = []
    }
  }
}
</script>
