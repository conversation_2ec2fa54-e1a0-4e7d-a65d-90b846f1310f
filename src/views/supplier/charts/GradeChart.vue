<template>
  <div style="padding: 0 52px;width: 100%">
    <!--    <div-->
    <!--      style="cursor:pointer;color: #4d93b9;font-weight: bold;font-size: 18px"-->
    <!--      @click="toDetail()"-->
    <!--    >-->
    <!--      {{ $t('sp.ytdSupplierPerformanceLevelDistribution') }}-->
    <!--    </div>-->
    <div v-if="chartData.length" ref="chart" :class="className" :style="{height:height,width:width}" />
    <el-empty v-else :description="$t('sp.noDataAvailableAtTheMoment')" />

  </div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '@/views/dashboard/mixins/resize'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'
require('echarts/theme/macarons') // echarts theme

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '230px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Array,
      required: true
    },
    exampleData: {
      type: Array,
      required: true
    }

  },
  data() {
    return {
      chart: null,
      show: true
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        if (this.chartData.length) {
          this.$nextTick(() => {
            this.initChart()
          })
        }
      }
    }
  },

  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    showChart() {
      this.show = true
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chart, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions(data) {
      // const colorList = ['#64BD3D', '#EE9201', '#29AAE3', '#B74AE5', '#0AAF9F', '#E89589', '#16A085', '#4A235A', '#F9E79F', '#BA4A00', '#C33531', '#EFE42A']
      // this.chartData.months.forEach(a => {
      //   data[a.month - 1] = a.piResult
      // })
      this.chart.setOption({
        color: ['#5C7BD9', '#9FE080', '#ffff00', '#FFDC60'],
        tooltip: {
          trigger: 'item',
          formatter: `{b} : {c}`
        },
        // legend: {
        //   data: ['Show', 'Click', 'Visit', 'Inquiry', 'Order']
        // },
        series: [
          {
            type: 'funnel',
            left: '8%',
            top: 40,
            bottom: 60,
            width: '80%',
            height: '80%',
            min: 0,
            max: 100,
            minSize: '0%',
            maxSize: '60%',
            sort: 'none',
            gap: 2,
            label: {
              show: true,
              color: '#000000',
              textBorderColor: 'transparent',
              fontSize: 15,
              position: 'inside',
              formatter: function(param) {
                return param.data.name + '  ' + param.data.value
              }
            },
            labelLine: {
              length: 10,
              lineStyle: {
                width: 1,
                type: 'solid'
              }
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            },
            emphasis: {
              label: {
                fontSize: 20
              }
            },
            data: data?.map(a => {
              return {
                value: a.countNum,
                name: a.gradeName
              }
            })
          }
        ],
        graphic: {
          type: 'text',
          right: 10,
          bottom: 10,
          style: {
            text: data?.reduce((acc, cur) => {
              return acc + '\n' + cur?.gradeName + getDictDataLabel(DICT_TYPE.SP_OPERATOR_SYMBOL, cur.opSymbol) + cur?.opValue
            }, ''),
            fill: '#333'
          }
        }
      }

      )
    },
    toDetail() {
      this.$router.push(`/sp/supplier`)
    }
  }
}
</script>

<style lang="scss" scoped>

::v-deep .el-scrollbar__wrap{
  overflow-x: auto;
  max-height: 600px;

  height: calc(100% + 20px); //多出来的20px是横向滚动条默认的样式
}
::v-deep .el-scrollbar__view{
  overflow-x: initial;
}
::v-deep .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view{
  white-space: nowrap;
  display: inline-block;
}
</style>
