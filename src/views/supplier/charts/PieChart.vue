<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
import resize from '@/views/dashboard/mixins/resize'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Array
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    labelPosition: {
      type: String,
      default: 'outside'
    },
    keyValue: {
      type: Object,
      default: { value: 'value', name: 'name' }
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.initChart(val)
      }
    }
  },
  // computed: {
  //   supplierTotal() {
  //     return this.chartData.reduce((acc, cur) => acc + cur.result, 0)
  //   }
  // },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeD<PERSON>roy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption({
        // color: ['#6A9BAE', '#D9D9D9', '#5B9CD7', '#C0DDE7'],
        tooltip: {
          trigger: 'item',
          formatter: `{b} : {c}`
        },

        series: [
          {
            type: 'pie',
            radius: '95%',
            label: {
              show: true,
              position: this.labelPosition,
              formatter: '{b} {c}'
            },
            data: this.chartData.map(a => {
              return {
                value: a[this.keyValue.value],
                name: getDictDataLabel(this.keyValue.dict, a[this.keyValue.name]) || a[this.keyValue.name]
              }
            }),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ] })
    }
  }
}
</script>
