<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Array
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
      levelMap: {
        'S1': '潜在',
        'S4': '合格',
        'S5': '优选',
        'S6,S7': '战略',
        'S3': '限制',
        'FL': '冻结',
        'BL': '黑名单'
      }
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.initChart(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      const levelData = Object.keys(this.levelMap).map(key => {
        return this.chartData.find(item => item.level === key)?.countNum || 0
      })
      levelData.splice(3, 1,
        (this.chartData.find(item => item.level === 'S6')?.countNum || 0) +
        (this.chartData.find(item => item.level === 'S7')?.countNum || 0)
      )
      const sumData = levelData.reduce((acc, val, i) => {
        if (i === 0) {
          acc.push(val)
        } else {
          acc.push(acc[i - 1] + val)
        }
        return acc
      }, [])
      sumData.unshift(0)
      sumData.pop()
      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let tar
            if (params[1] && params[1].value !== '-') {
              tar = params[1]
            } else {
              tar = params[2]
            }
            return tar && tar.name + '<br/>' + tar.seriesName + ' : ' + tar.value
          }
        },

        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: Object.values(this.levelMap),
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: 'Placeholder',
            type: 'bar',
            stack: 'Total',
            silent: true,
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent'
            },
            emphasis: {
              itemStyle: {
                borderColor: 'transparent',
                color: 'transparent'
              }
            },
            data: sumData
          },
          {
            name: 'Income',
            type: 'bar',
            stack: 'Total',
            itemStyle: {
              color(params) {
                const colorList = ['#6A9BAE', '#6A9BAE', '#6A9BAE', '#6A9BAE', 'yellow', 'red', 'black']
                return colorList[params.dataIndex]
              }
            },
            label: {
              show: true,
              position: 'top'
            },
            data: levelData

          }

        ]
      })
    }
  }
}
</script>
