<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
import resize from '@/views/dashboard/mixins/resize'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    labelPosition: {
      type: String,
      default: 'outside'
    },
    // keyValue: {
    //   type: Object,
    //   default: { value: 'value', name: 'name' }
    // }
  },
  data() {
    return {
      chart: null,
      levelMap: {
        BL: '黑名单',
        FL: '冻结',
        S3: '限制',
        S1: '潜在',
        S4: '合格',
        S5: '优选',
        'S6,S7': '战略'
      }
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.initChart(val)
      }
    }
  },
  // computed: {
  //   supplierTotal() {
  //     return this.chartData.reduce((acc, cur) => acc + cur.result, 0)
  //   }
  // },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      const color = [
        '#D83933',
        '#EC7C74',
        '#F89B59',
        '#CBCBCB',
        '#5990AE',
        '#1B5C7E',
        '#A7D0DF'
      ]
      const levelData = Object.keys(this.levelMap).map(key => {
        return this.chartData.find(item => item.level === key)?.countNum || 0
      })
      levelData.splice(3, 1,
        (this.chartData.find(item => item.level === 'S6')?.countNum || 0) +
        (this.chartData.find(item => item.level === 'S7')?.countNum || 0)
      )
      this.chart.setOption({
        grid: {
          top: '10%',    // 距离容器顶部的距离
          bottom: '10%', // 距离容器底部的距离
          left: '8%',    // 距离容器左侧的距离
          right: '8%'    // 距离容器右侧的距离
        },
        xAxis: {
          type: 'category',
          data: Object.values(this.levelMap),
          axisTick: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#C3C3C3' // 将X轴设置为黑色
            }
          },
          axisLabel: {
            fontSize: 16,
            color: '#000'
          }
        },

        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#C3C3C3' // 将Y轴刻度标签设置为灰色
          },
          splitLine: {
            lineStyle: {
              type: 'dashed' // 虚线
            },
          },
          splitArea: {
            show: false // 禁用背景条纹
          }
        },
        series: [
          {
            label: {
              show: true,
              position: 'top'
            },
            data: levelData.map((a, index) => {
              return {
                value: a,
                itemStyle: {
                  color: color[index]
                }
              }
            }),
            barWidth: '30%', // 控制柱子宽度，数值越小柱子越窄
            type: 'bar'
          }
        ]
      })
    }
  }
}
</script>
