<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
import resize from '@/views/dashboard/mixins/resize'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Array
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    labelPosition: {
      type: String,
      default: 'outside'
    },
    keyValue: {
      type: Object,
      default: { value: 'value', name: 'name' }
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.initChart(val)
      }
    }
  },
  // computed: {
  //   supplierTotal() {
  //     return this.chartData.reduce((acc, cur) => acc + cur.result, 0)
  //   }
  // },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeD<PERSON>roy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption({
        legend: {
          orient: 'vertical', // 图例垂直排列
          right: '25%', // 距离右侧的距离
          top: 'center', // 图例居中显示
          icon: 'roundRect',
          padding: [35, 10],
          itemGap: 18,
          formatter: (name) => {
            return name + ' ' + this.chartData.find(a => a[this.keyValue.name] === name ||
              getDictDataLabel(this.keyValue.dict, a[this.keyValue.name]) === name)?.[this.keyValue.value] || 0
          }
        },

        tooltip: {
          trigger: 'item',
          formatter: `{b} : {c}`
        },
        color: ['#4996b8',
          '#b996ff',
          '#f8954e',
          '#ef7880',
          '#a3e34b',
          '#21acac',
          '#155885',
          '#8658ff',
          '#e77504',
          '#e0383f',
          '#67c915',
          '#158383',
          '#c8dfea',
          '#eadfff',
          '#f8bd83',
          '#f7bcc0',
          '#c0e7b2',
          '#73d6d6'],

        series: [
          {
            type: 'pie',
            radius: ['30%', '70%'],
            center: ['25%', '50%'], // 调整圆环图位置

            avoidLabelOverlap: false,
            // itemStyle: {
            //   // borderRadius: 10,
            //   borderColor: '#fff',
            //   borderWidth: 2
            // },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.chartData.map(a => {
              return {
                value: a[this.keyValue.value],
                name: getDictDataLabel(this.keyValue.dict, a[this.keyValue.name]) || a[this.keyValue.name]
              }
            })
          }
        ]
      })
    }
  }
}
</script>
