<template>
  <div style="display: flex">
    <div class="chart-container">
      <div
        v-for="(item, index) in data"
        :key="index"
        class="bar"
        :style="{ width: item.percentage + '%', backgroundColor: item.color,'min-width': '15px' }"
      >
        <div class="percentage">
          <span v-show="item.percentage > 0">
            {{ item.percentage }}%
          </span>
        </div>
      </div>
    </div>
    <div class="chart-container" style="align-items: flex-start">
      <div
        v-for="(item, index) in data"
        style="height: 50px;display: flex;align-items: center;margin: 5px 0"
      >
        <div class="dashed-line" />
        <div style="padding-left: 10px;font-size: 16px">{{ item.label }}绩效 {{ item.value }}</div>

      </div>
    </div>
  </div>

</template>

<script>
export default {
  props: ['chartData'],
  data() {
    return {
      data: [
        { label: 'A', value: 0, percentage: 0, color: '#155885' },
        { label: 'B', value: 0, percentage: 0, color: '#4996B8' },
        { label: 'C', value: 0, percentage: 0, color: '#8AABC2' },
        { label: 'D', value: 0, percentage: 0, color: '#EF7880' }
      ]
    }
  },
  mounted() {
    const total = this.chartData.reduce((acc, item) => acc + item.countNum, 0)
    this.data.forEach((item) => {
      const a = this.chartData.find(b => b.gradeName === item.label)
      if (a) {
        this.$set(item , 'value', a.countNum)
        this.$set(item , 'percentage', (a.countNum*100 / total).toFixed(0))
      }
    })
  }
}
</script>

<style scoped>
.chart-container {
  width: 50%;
  display: flex;
  padding: 20px;
  border-radius: 8px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.bar {
  height: 50px;
  margin: 5px 0;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}

.percentage {
  font-size: 14px;
}

.label {
  margin-left: auto;
  padding-right: 10px;
}
.dashed-line {
  width: 200px; /* 控制虚线宽度 */
  height: 2px; /* 控制虚线的粗细 */
  background: repeating-linear-gradient(to right,#EAEAEA, #EAEAEA 5px, transparent 5px, transparent 10px);
}

</style>
