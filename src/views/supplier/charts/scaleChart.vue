<template>
    <div :class="className" :style="{height:height,width:width}" />

</template>

<script>
import * as echarts from 'echarts'
import resize from '@/views/dashboard/mixins/resize'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    legend: {
      type: Array,
      default: () => []

    },
    color: {
      type: String,
      default: 'chart'
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.initChart(val)
      }
    }
  },
  // computed: {
  //   supplierTotal() {
  //     return this.chartData.reduce((acc, cur) => acc + cur.result, 0)
  //   }
  // },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption({
        grid: {
          top: '15%', // 距离容器顶部的距离
          bottom: '10%', // 距离容器底部的距离
          left: '7%', // 距离容器左侧的距离
          right: '7%' // 距离容器右侧的距离
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          data: this.legend,
          top: '3%',
          left: 'left'
        },
        xAxis: [
          {
            axisTick: {
              show: false
            },
            type: 'category',
            // data: ['2019', '2020', '2021', '2022', '2023'],
            data: this.chartData.map(a => a.year),
            axisPointer: {
              type: 'shadow'
            },
            axisLine: {
              lineStyle: {
                color: '#C3C3C3' // 将X轴设置为黑色
              }
            },
            axisLabel: {
              fontSize: 16,
              color: '#000'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              formatter: '{value} 万元',
              color: '#C3C3C3'
            },
            splitLine: {
              lineStyle: {
                type: 'dashed' // 虚线
              }
            },
            splitArea: {
              show: false // 禁用背景条纹
            }
          },
          {
            type: 'value',
            splitLine: {
              lineStyle: {
                type: 'dashed' // 虚线
              }
            },
            axisLabel: {
              color: '#C3C3C3',
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: '供应商销售额',
            type: 'bar',
            data: this.chartData.map(a => a.value),
            // data: [40, 62.4, 91, 67.34, 80.81],
            barWidth: '25%',
            itemStyle: {
              color: this.color // Bar color
            }
          },
          {
            name: '销售额变化率',
            type: 'line',
            yAxisIndex: 1,
            // data: [0, 56, 46, -26, 20],
            data: this.chartData.map(a => a.value1),
            itemStyle: {
              color: '#567D94' // Line color
            },
            label: {
              show: true,
              formatter: '{c}%',
              position: 'top'
            }
          }
        ]
      })
    }
  }
}
</script>
