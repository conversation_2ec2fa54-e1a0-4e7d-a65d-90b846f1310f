<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
import resize from '@/views/dashboard/mixins/resize'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Array
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    labelPosition: {
      type: String,
      default: 'outside'
    },
    keyValue: {
      type: Object,
      default: { value: 'value', name: 'name' }
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.initChart(val)
      }
    }
  },
  // computed: {
  //   supplierTotal() {
  //     return this.chartData.reduce((acc, cur) => acc + cur.result, 0)
  //   }
  // },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeD<PERSON>roy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption(
        {
          legend: {
            orient: 'vertical', // 图例垂直排列
            right: '11%', // 距离右侧的距离
            top: 'center', // 图例居中显示
            icon: 'roundRect',
            padding: [90, 10],
            itemGap: 18,
            formatter: (name) => {
              return name + ' ' + this.chartData.find(a => a[this.keyValue.name] === name ||
              getDictDataLabel(this.keyValue.dict, a[this.keyValue.name]) === name)?.[this.keyValue.value] || 0
            }
          },
          title: [
            {
              text: this.chartData.reduce((acc, cur) => {
                return acc + cur.countNum
              }, 0),
              left: '16%',
              top: '40%',
              textStyle: {
                fontSize: 30,
                color: '#333'
              }
            },
            {
              subtext: 'YTD不良类型统计',
              itemGap: 12, // 主副标题间距
              left: '10%', // 副标题位置
              top: '50%',
              subtextStyle: {
                fontSize: 14,
                color: '#333',
                align: 'right'
              }
            }
          ],

          tooltip: {
            trigger: 'item',
            formatter: `{b} : {c}`
          },
          series: [
            {
              name: '不良类型',
              type: 'pie',
              radius: ['50%', '70%'],
              center: ['20%', '50%'], // 调整圆环图位置
              avoidLabelOverlap: false,
              color: [
                '#4A90E2', '#B39DDB', '#FF8A65', '#F06292', '#AED581', '#26C6DA',
                '#1E88E5', '#7E57C2', '#FF7043', '#E53935', '#66BB6A', '#00ACC1',
                '#B3E5FC', '#D1C4E9', '#FFCC80', '#F48FB1', '#C5E1A5', '#80DEEA'
              ],
              label: {
                show: false,
                position: 'outside',
                formatter: '{b}: {c}' // 显示类别和数值
              },
              data: this.chartData.map(a => {
                return {
                  value: a[this.keyValue.value],
                  name: getDictDataLabel(this.keyValue.dict, a[this.keyValue.name]) || a[this.keyValue.name]
                }
              })
            }
          ] })
    }
  }
}
</script>
