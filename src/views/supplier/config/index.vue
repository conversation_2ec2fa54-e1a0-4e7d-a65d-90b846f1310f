<template>

  <div class="app-container">
    <span style="margin-left: 18px">
      {{ $t('supplier.verificationConditions') }}：
    </span>
    <el-select v-model="value" :placeholder="$t('common.pleaseSelect')">
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-button v-hasPermi="['supplier:config:save']" type="primary" @click="save">{{ $t('common.save') }}</el-button>
  </div>
</template>

<script>
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { doSave, detail } from '@/api/supplier/config'

export default {
  name: 'Supplierconfig',
  data() {
    return {
      // 配置值
      value: '',
      // 选项值
      options: getDictDatas(DICT_TYPE.SUPPLIER_UNIQUE_VALIDATION)
    }
  },
  created() {
    this.populate()
  },
  methods: {
    // 填充默认值
    populate() {
      detail().then(res => {
        this.value = res.data
      })
    },
    // save
    save() {
      doSave({ 'value': this.value }).then(res => {
        this.$message.success(this.$t('common.savedSuccessfully'))
      }).catch(() => {
        this.$message.error(this.$t('common.saveFailed'))
      })
    }
  }
}
</script>

<style scoped>

</style>
