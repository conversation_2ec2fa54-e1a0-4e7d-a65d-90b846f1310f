<template>
  <div class="cooperHis">
    <el-card>
      <div style="display: flex;justify-content: space-between;align-items: center">
        <span style="font-size: 28px;font-weight: 700">
          {{ baseInfo.name }}
          <el-button type="text" @click="$router.push(`/supplier/supplierinfo/${baseInfo.id}?supplierName=${baseInfo.name}`)">{{ $t('system.details') }}</el-button>
        </span>
        <div style="display: flex;justify-content: space-around;font-size: 18px">
          <div style="margin: 0 5px">{{$t('supplier.informationCompleteness')}}
            <span style="font-weight: 700;margin-left: 12px">{{ baseInfo.infoPercent }}%</span>
            (890/1000)</div>
          <span style="color: #EAEAEA;margin: 0 40px">|</span>
          <div style="margin: 0 5px">{{$t('supplier.numberOfFiles')}} {{ baseInfo.fileCount }}</div>
        </div>
      </div>

      <div style="display: flex;margin-top: 20px;padding: 10px 10px 20px;">
        <div style="border-bottom: 1px solid #EAEAEA;">
          <el-descriptions
            class="baseinfo"
            :column="2"
            :label-style="{
              color: '#929292',
              width: '96px',
              'font-size': '16px',
              'justify-content':'left',
            }"
            :content-style="{
              color: '#929292',
              'font-size': '16px',
            }"
          >
            <el-descriptions-item :label="$t('supplier.establishedTime')">
              {{
                baseInfo.establishedTime?dayjs(baseInfo.establishedTime).format(`YYYY.MM.DD`):'' }}</el-descriptions-item>
            <el-descriptions-item :label="$t('supplier.totalNumberOfPersonnel')">{{
                baseInfo.dictRelCreateReqVO?.find(a=>a.dictRelCreateReqVO==='all_staff')?.value
              }}</el-descriptions-item>
            <el-descriptions-item :label="$t('supplier.companyRegisteredAddress')">{{
                baseInfo.registeredAddress
              }}</el-descriptions-item>
            <el-descriptions-item :label="$t('supplier.mainProducts')">{{
                baseInfo.productInfoCreateReqVO?.at(0)?.name
              }}</el-descriptions-item>
            <el-descriptions-item :label="$t('supplier.legalRepresentative')">{{
                baseInfo.legalPerson
              }}</el-descriptions-item>
            <el-descriptions-item :label="$t('supplier.primaryContact')">{{
                baseInfo.contactCreateReqVO?.at(0)?.name
              }}</el-descriptions-item>

            <el-descriptions-item :label="$t('auth.category')">
              <dict-tag :value="baseInfo.companyCategory" :type="DICT_TYPE.SUPPLIER_TYPE" />

            </el-descriptions-item>
            <el-descriptions-item :label="$t('supplier.contactInformation')">{{
                baseInfo.contactCreateReqVO?.at(0)?.phone
              }}</el-descriptions-item>
            <el-descriptions-item :label="$t('supplier.nature')">
              <dict-tag :value="baseInfo.companyNature" :type="DICT_TYPE.SUPPLIER_COMPANY_NATURE" />
            </el-descriptions-item>

            <el-descriptions-item :label="$t('supplier.emailAddress')">{{
                baseInfo.email
              }}</el-descriptions-item>

            <el-descriptions-item :label="$t('supplier.areaCovered')">{{
                baseInfo.coversArea
              }} {{$t('supplier.squareMeter')}}</el-descriptions-item>

          </el-descriptions>

        </div>
      </div>

      <div class="info-flex">
        <div class="info-flex-item" style="padding-left: 0">
          <div>{{ topLevel }}</div>
          <span>{{$t('supplier.obtainedTheHighestLevel')}}</span>
        </div>
        <div class="info-flex-item" style="flex: 0 0 40%;">
          <div>{{ latestCompanyCategory }}</div>
          <span>{{$t('supplier.approvedCategory')}}</span>
        </div>
        <div class="info-flex-item" style="flex: 0 0 25%;">
          <div>{{ latestCompany }}</div>
          <span>{{$t('supplier.approvedCompany')}}</span>
        </div>
        <div class="info-flex-item" style="flex: 0 0 25%;display: flex;align-self: flex-end">
          <el-button v-if="showCategory" type="text" @click="showCategory = !showCategory">{{$t('supplier.viewTheHighestLevelOfAllCategories')}}</el-button>
          <el-button v-else type="text" @click="showCategory = !showCategory">{{$t('common.putItAway')}}</el-button>
        </div>

      </div>
      <div v-if="!showCategory" style="margin: 10px -20px -20px -20px;background: #f8f8f8">
        <div style="padding: 20px">
          <el-table class="supplierTable" :data="supplierLevelList">
            <el-table-column :label="$t('supplier.level')" prop="currentLevel" min-width="10%">
              <template #default="scope">
                <span style="font-weight: bold">
                  {{ getDictDataLabel(DICT_TYPE.SL_SUPPLIER_LEVEL,scope.row.currentLevel) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('material.category')" prop="categoryId" min-width="40%">
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="scope.row.categoryId" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('supplier.approvedCompany')" prop="companyId" min-width="25%">
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="scope.row.companyId" />
              </template>
            </el-table-column>
            <el-table-column label=" " min-width="25%" />

          </el-table>
          <pagination
            v-show="total > 0"
            style="background: #f8f8f8"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNo"
            :total="total"
            @pagination="getSupplierLevel"
          />
        </div>

      </div>

    </el-card>
    <el-card style="margin-top: 20px">
      <div class="cardTitle">{{$t('supplier.supplierStatusChangeHistory')}}</div>

      <div v-for="item in supplierLevel" class="changeHis">
        <div style="display:flex;color: #929292;align-items: center;padding-right: 25px">
          {{ dayjs(item.createTime).format('YYYY-MM-DD') }}

        </div>
        <div class="changeHis-step">
          <div style="font-size: 16px;font-weight: bold;flex: 0 0 10%">
            <dict-tag :value="item.adjustmentType" :type="DICT_TYPE.SL_ADJUSTMENT_TYPE" />

          </div>
          <div
            class="changeHis-step-item"
            style="flex: 1 1 8%;"
          >
            <span style=" color: #929292;">{{$t('supplier.level')}}</span>
            <div>{{ item.currentLevel }}</div>
          </div>
          <div
            class="changeHis-step-item"
            style="flex: 1 1 30%;"
          >
            <span style=" color: #929292;">{{$t('material.category')}}</span>
            <div>
              <dict-tag :value="item.categoryId" :type="DICT_TYPE.COMMON_CATEGORY" />
            </div>

          </div>
          <div
            class="changeHis-step-item"
            style="flex: 1 1 50%;"
          >
            <span style=" color: #929292;">{{$t('supplier.approvedCompany')}}</span>
            <div>
              <dict-tag :value="item.companyId" :type="DICT_TYPE.COMMON_COMPANY" />
            </div>

          </div>
          <div
            class="changeHis-step-item"
            style="flex: 0 0 5%;"
          >
            <el-button type="text" @click="$router.push(`sl/detail/${item.applicationId}?no=${item.applicationNo}`)">{{$t('supplier.viewDocuments')}}</el-button>
          </div>
        </div>
      </div>
    </el-card>
    <el-card style="margin-top: 20px">
      <div class="cardTitle">{{$t('supplier.scale')}}</div>
      <div style="display: flex;">
        <div style="flex: 0 0 50%;border-right: 1px solid #eaeaea;font-size: 16px">
          <div>{{$t('supplier.supplierSalesRevenue')}}</div>
          <scale-chart
            v-if="purchaseAmount?.length"
            color="#C1E8B3"
            :legend="['供应商销售额', '销售额变化率']"
            :chart-data="purchaseAmount"
          />
          <el-empty v-else :description="$t('sp.noDataAvailableAtTheMoment')" />

        </div>
        <div style="flex: 0 0 50%;font-size: 16px;padding-left: 20px">
          <div style="display: flex;justify-content: space-between;align-items: center">
            <span>{{$t('supplier.ourCompanysProcurementAmount')}}</span>
            <div v-if="turnOver?.length" style="display: flex;align-items: center">
              <span style="font-size: 14px;color: #565656;">{{$t('supplier.proportionFromThePreviousYear')}}</span>
              <span style="font-size: 24px;font-weight: 700;margin: 0 8px">{{ turnOver?.at(-1).value1 }}%</span>
              <el-popover
                placement="bottom-start"
                width="350"
                trigger="hover"
                :content="`上一年，我司采购额占供应商销售额比例为${turnOver?.at(-1).value1}%`"
              >
                <i slot="reference" class="el-icon-info" style="font-size: 14px" />
              </el-popover>
            </div>
          </div>
          <scale-chart
            v-if="turnOver?.length"
            color="#F8BD83"
            :legend="['我司采购额', '采购额变化率']"
            :chart-data="turnOver"
          />
          <el-empty v-else :description="$t('sp.noDataAvailableAtTheMoment')" />

        </div>
      </div>
    </el-card>
    <el-card style="margin-top: 20px">
      <div class="cardTitle">{{$t('supplier.orderDelivery')}}</div>
      <div style="display: flex;margin: 10px 0 ">
        <div style="flex: 0 1 50%;border-right: 1px solid #eaeaea;padding-right: 15px">

          <el-table :data="orderPass">
            <el-table-column
              width="100px"
              :label="$t('supplier.year')"
              prop="year"
            />
            <el-table-column :label="$t('supplier.completeOrder')" prop="completeCount">
              <template #default="scope">
                {{ scope.row.completeCount }}
                <span :style="{color: rateColor(scope.row.changeRateOrder)}">
                  <i v-if="scope.row.changeRateOrder > 0" class="el-icon-caret-top" style="font-size: 18px" />
                  <i v-else-if="scope.row.changeRateOrder < 0" class="el-icon-caret-bottom" style="font-size: 18px" />
                  <span v-else-if="scope.row.changeRateOrder === 0">-</span>
                  {{ scope.row.changeRateOrder }}%
                </span>

              </template>
            </el-table-column>
            <!--            <el-table-column :label="$t('supplier.changeRate')" prop="changeRateOrder" />-->
            <el-table-column :label="$t('supplier.collaborativeMaterials')" prop="materialCount">
              <template #default="scope">
                {{ scope.row.materialCount }}
                <span :style="{color: rateColor(scope.row.changeRateMaterial)}">
                  <i v-if="scope.row.changeRateMaterial > 0" class="el-icon-caret-top" style="font-size: 18px" />
                  <i v-else-if="scope.row.changeRateMaterial < 0" class="el-icon-caret-bottom" style="font-size: 18px" />
                  <span v-else-if="scope.row.changeRateMaterial === 0">-</span>
                  {{ scope.row.changeRateMaterial }}%
                </span>

              </template>
            </el-table-column>
            <!--            <el-table-column :label="$t('supplier.changeRate')" prop="changeRateMaterial" />-->

          </el-table>
        </div>

        <div style="flex: 0 1 50%;">
          <div class="info-flex" style="flex-wrap: wrap">
            <div class="info-flex-item order-item">
              <div>{{ orderPassChart?.completeCountYtd || 0 }}</div>
              <span>
                {{$t('supplier.ytdCompletesTheOrder')}}
                <el-button type="text" style="margin-left: 5px" @click="$router.push(`/om/omOrderTrackerIndex?supplier=${baseInfo.name}`);$store.commit('setParameters', 'ytdOrder')">{{$t('supplier.viewDocuments')}}</el-button>
              </span>
            </div>
            <div class="info-flex-item order-item">
              <div>{{ orderPassChart?.toBeCompleteCount|| 0 }}</div>
              <span>
               {{$t('supplier.pendingOrders')}}
                <el-button type="text" style="margin-left: 5px" @click="$router.push(`/om/omOrderTrackerIndex?supplier=${baseInfo.name}`);$store.commit('setParameters', 'ytdOrderTodo')">{{$t('supplier.viewDocuments')}}</el-button>
              </span>
            </div>
            <div class="info-flex-item order-item">
              <div>{{ orderPassChart?.orderFrequencyToWeekYtd|| 0 }}</div>
              <span> {{$t('supplier.ytdWeeklyOrderFrequency')}}</span>
            </div>
            <div class="info-flex-item order-item">
              <div>{{ orderPassChart?.averageLtYtd || 0 }}</div>
              <span>{{$t('supplier.ytdAverageLt')}}</span>
            </div>
          </div>
        </div>
      </div>

    </el-card>
    <el-card style="margin-top: 20px">
      <div class="cardTitle">{{$t('rfq.material')}}</div>
      <div style="display: flex;margin: 10px 0 ">
        <div style="flex: 0 1 50%;border-right: 1px solid #eaeaea;padding-right: 15px">

          <el-table :data="supplierMaterial">
            <el-table-column
              width="100px"
              :label="$t('supplier.year')"
              prop="year"
            />
            <el-table-column :label="$t('supplier.participateInInquiryAndQuotation')" prop="rfqCount">
              <template #default="scope">
                {{ scope.row.rfqCount }}
                <span :style="{color: rateColor(scope.row.rfqChangeRate)}">
                  <i v-if="scope.row.rfqChangeRate > 0" class="el-icon-caret-top" style="font-size: 18px" />
                  <i v-else-if="scope.row.rfqChangeRate < 0" class="el-icon-caret-bottom" style="font-size: 18px" />
                  <span v-else-if="scope.row.rfqChangeRate === 0">-</span>
                  {{ scope.row.rfqChangeRate }}%
                </span>

              </template>
            </el-table-column>
            <el-table-column :label="$t('supplier.numberOfApprovedMaterials')" prop="materialCount">
              <template #default="scope">
                {{ scope.row.materialCount }}
                <span :style="{color: rateColor(scope.row.changeRateMaterial)}">
                  <i v-if="scope.row.changeRateMaterial > 0" class="el-icon-caret-top" style="font-size: 18px" />
                  <i v-else-if="scope.row.changeRateMaterial < 0" class="el-icon-caret-bottom" style="font-size: 18px" />
                  <span v-else-if="scope.row.changeRateMaterial === 0">-</span>
                  {{ scope.row.changeRateMaterial }}%
                </span>

              </template>
            </el-table-column>
            <!--            <el-table-column :label="$t('supplier.changeRate')" prop="changeRateMaterial" />-->

          </el-table>
        </div>

        <div style="flex: 0 1 50%;">
          <div class="info-flex" style="flex-wrap: wrap">
            <div class="info-flex-item order-item">
              <div>{{ orderPassChart?.materialActivityCount || 0 }}</div>
              <span>
                {{$t('supplier.materialsDeliveredInThePastMonths')}}
              </span>
            </div>
            <div class="info-flex-item order-item">
              <div>{{ orderPassChart?.materialYtdNewCount|| 0 }}</div>
              <span>
                {{$t('supplier.ytdAddsApprovedMaterials')}}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="cardTitle">{{$t('supplier.latestPriceAward')}}</div>

      <el-table :data="supplierMaterialTable">

        <el-table-column :label="$t('material.materialCode')" prop="materialCode" />
        <el-table-column :label="$t('material.materialDescription')" prop="materialDescription" />
        <el-table-column :label="$t('supplier.grantTime')" prop="generateTime">
          <template slot-scope="scope">
            {{ scope.row.generateTime?dayjs(scope.row.generateTime).format('YYYY-MM-DD'):'' }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('material.category')" prop="materialDescription">
          <template slot-scope="scope">
            <dict-tag :value="scope.row.categoryId" :type="DICT_TYPE.COMMON_CATEGORY" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('avpl.unitPriceIncludingTax')" prop="originalUnitPriceTax" />
        <el-table-column :label="$t('system.currency')" prop="originalCurrency">
          <template slot-scope="scope">
            <dict-tag :value="scope.row.originalCurrency" :type="DICT_TYPE.COMMON_CURRENCY" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('supplier.validUntil')" prop="recommendedPriceExpires">
          <template slot-scope="scope">
            {{ scope.row.recommendedPriceExpires?dayjs(scope.row.recommendedPriceExpires).format('YYYY-MM-DD') :'' }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('supplier.ladderPrice')" prop="ladderCount">
          <template slot-scope="scope">
            {{ scope.row.ladderCount > 1 ? '是':'否' }}
          </template>
        </el-table-column>

      </el-table>

    </el-card>
    <el-card style="margin-top: 20px">
      <div class="cardTitle">{{$t('supplier.qualityEvents')}}</div>
      <div style="display: flex;margin: 10px 0 ">
        <div style="flex: 0 1 50%;border-right: 1px solid #eaeaea;padding-right: 15px">

          <el-table :data="orderPass">
            <el-table-column
              width="100px"
              :label="$t('supplier.year')"
              prop="year"
            />
            <el-table-column :label="$t('supplier.numberOfQualityIncidents')" prop="completeCount">
              <template #default="scope">
                {{ scope.row.countMaterial }}
                <span :style="{color: rateColor(scope.row.countMaterialChangeRate)}">
                  <i v-if="scope.row.countMaterialChangeRate > 0" class="el-icon-caret-top" style="font-size: 18px" />
                  <i v-else-if="scope.row.countMaterialChangeRate < 0" class="el-icon-caret-bottom" style="font-size: 18px" />
                  <span v-else-if="scope.row.countMaterialChangeRate === 0">-</span>
                  {{ scope.row.countMaterialChangeRate }}%
                </span>

              </template>
            </el-table-column>
            <!--            <el-table-column :label="$t('supplier.changeRate')" prop="changeRateOrder" />-->
            <el-table-column :label="$t('supplier.collaborativeMaterials')" prop="materialCount">
              <template #default="scope">
                {{ scope.row.countNum }}
                <span :style="{color: rateColor(scope.row.changeRate)}">
                  <i v-if="scope.row.changeRate > 0" class="el-icon-caret-top" style="font-size: 18px" />
                  <i v-else-if="scope.row.changeRate < 0" class="el-icon-caret-bottom" style="font-size: 18px" />
                  <span v-else-if="scope.row.changeRate === 0">-</span>
                  {{ scope.row.changeRate }}%
                </span>

              </template>
            </el-table-column>

          </el-table>
        </div>

        <div style="flex: 0 1 50%;display: flex">
          <div style="flex: 0 1 35%;">
            <div class="info-flex" style="flex-wrap: wrap;flex-direction: column">
              <div class="info-flex-item order-item" style="border: none">
                <div>{{ scarChart?.ytdScarCompleted || 0 }}</div>
                <span>
                  {{$t('supplier.ytdScarCompleted')}}
                  <el-popover
                    placement="bottom-start"
                    width="220"
                    trigger="hover"
                    :content="`SCAR：供应商纠正措施报告`"
                  >
                    <i slot="reference" class="el-icon-info" style="font-size: 14px" />
                  </el-popover>
                </span>
              </div>
              <div class="info-flex-item order-item" style="border: none">
                <div>{{ scarChart?.ytdScarInProgress || 0 }}</div>
                <span>
                  {{$t('supplier.ytdScarInProgress')}}
                </span>
              </div>
              <div class="info-flex-item order-item" style="border: none">
                <div>{{ scarChart?.ytdMajorQualityAbnormal || 0 }}</div>
                <span>
                  {{$t('supplier.ytdMajorQualityAbnormalities')}}
                </span>
              </div>
            </div>

          </div>
          <div style="flex: 0 1 80%;">
            <ytdPie
              v-if="scarPie?.length"
              :chart-data="scarPie"
              height="300px"
              :key-value="{
                value: 'countNum',
                name: 'type'
              }"
            />
            <el-empty :description="$t('sp.noDataAvailableAtTheMoment')" />
          </div>
        </div>
      </div>

    </el-card>

    <el-card style="margin-top: 20px">
      <div class="cardTitle">{{$t('supplier.performancePerformance')}}</div>
      <div v-if="spData?.length>0" style="display: flex">
        <div style="flex: 1 1 50%;border-right: 1px solid #eaeaea;padding: 15px 36px;">
          <div style="display: flex;width: 100%;font-size: 16px">

            <svg-icon icon-class="trend-down" class-name="up" />
            <div style="width: 100%;padding-left: 15px">
              <div style="color: #67C23A">
                {{$t('supplier.optimum')}}
              </div>
              <div style="display: flex;justify-content: space-between">
                <span style="font-weight: bold">
                  <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="spDataTop?.companyId" />

                </span>
                <el-button type="text" @click="$router.push(spDataTop?.link)">{{$t('system.details')}}</el-button>
              </div>
            </div>

          </div>
          <div class="info-flex" style="flex-wrap: wrap;padding-bottom: 0">
            <div class="info-flex-item performance-item">
              <div>{{ spDataTop?.piScoreYtm || 0 }}（{{ spDataTop?.gradeName }}）</div>
              <span>
                {{$t('supplier.ytdPerformanceTotalScore')}}
              </span>
            </div>
            <div class="info-flex-item performance-item">
              <div>{{ spDataTop?.otd|| 0 }}%</div>
              <span>
                {{$t('supplier.ytdOtd')}}
              </span>
            </div>
            <div class="info-flex-item performance-item">
              <div>{{ spDataTop.incomingBatchPassRate|| 0 }}%</div>
              <span>
                {{$t('supplier.ytdIncomingMaterialQualificationRate')}}
              </span>
            </div>
          </div>
        </div>

        <div style="flex: 1 1 50%;border-right: 1px solid #eaeaea;padding: 15px 36px;">
          <div style="display: flex;width: 100%;font-size: 16px">

            <svg-icon icon-class="trend-down" class-name="down" />
            <div style="width: 100%;padding-left: 15px">
              <div style="color: #FA5151">
                {{$t('supplier.worst')}}
              </div>
              <div style="display: flex;justify-content: space-between">
                <span style="font-weight: bold">
                  <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="spDataWorse?.companyId" />

                </span>
                <el-button type="text" @click="$router.push(spDataWorse?.link)">{{$t('system.details')}}</el-button>
              </div>
            </div>

          </div>
          <div class="info-flex" style="flex-wrap: wrap;padding-bottom: 0">
            <div class="info-flex-item performance-item">
              <div>{{ spDataWorse?.piScoreYtm || 0 }}（{{ spDataWorse?.gradeName }}）</div>
              <span>
                {{$t('supplier.ytdPerformanceTotalScore')}}
              </span>
            </div>
            <div class="info-flex-item performance-item">
              <div>{{ spDataWorse?.otd|| 0 }}%</div>
              <span>
                {{$t('supplier.ytdOtd')}}
              </span>
            </div>
            <div class="info-flex-item performance-item">
              <div>{{ spDataWorse?.incomingBatchPassRate|| 0 }}%</div>
              <span>
                {{$t('supplier.ytdIncomingMaterialQualificationRate')}}
              </span>
            </div>
          </div>
        </div>

      </div>
      <div v-else>
        <el-empty :description="$t('sp.noDataAvailableAtTheMoment')" />
      </div>
    </el-card>

  </div>

</template>
<script>
import {
  getBaseInfo,
  getPurchaseAmountBySupplier,
  getSupplierCompleteOrderStatistics, getSupplierLevelListBySupplierId,
  getSupplierMaterialAvplList,
  getSupplierMaterialStatisticsBoard,
  getSupplierMaterialYearList,
  getSupplierScarStatisticsBoard,
  getSupplierScarStatisticsFigure,
  getSupplierScarStatisticsList, getSupplierSP,
  getSupplierStatisticsBoard,
  getTurnover,
  supplierLevelLogListBySupplierId
} from '@/api/supplier/resourceRepository'
import dayjs from 'dayjs'
import { DICT_TYPE, getDictDataLabel, getDictDatas } from '@/utils/dict'
import PieChart from '@/views/supplier/charts/PieChart.vue'
import ScaleChart from '@/views/supplier/charts/scaleChart.vue'
import YtdPie from '@/views/supplier/charts/ytdPie.vue'

export default {
  name: 'CooperHis',
  components: { YtdPie, ScaleChart, PieChart },
  data() {
    return {
      baseInfo: {
        fileCount: null,
        creditCode: '',
        name: '',
        nameShort: '',
        code: '',
        telephone: '',
        fax: '',
        webSite: '',
        establishedTime: null,
        registeredAddress: '',
        countryRegion: 0,
        provinceCity: 0,
        street: '',
        zipCode: '',
        registeredCapital: 0,
        registeredCapitalCurrency: 0,
        companyCategory: '',
        legalPerson: '',
        email: '',
        position: '',
        phone: null,
        coversArea: 0,
        buildProperty: '',
        productionArea: 0,
        companyNature: '',
        exportQualification: null,
        cleanRoom: null,
        cleanRoomLevel: null,
        laboratoryEquipment: null,
        emsOther: null,
        sourcing: '',
        status: 0,
        levelStatus: '',
        infoPercent: 0,
        id: 0,
        createTime: 0,
        factoryAddressCreateReqVO: [],
        contactCreateReqVO: [],
        associatedCompanyCreateReqVO: [],
        shareholdersCreateReqVO: [],
        turnoverCreateReqVO: [],
        customerCreateReqVO: [],
        productInfoCreateReqVO: [],
        rawMaterialCreateReqVO: [],
        equipmentCreateReqVO: [],
        processCapabilityCreateReqVO: {},
        paymentInfoRespVO: {},
        dictRelCreateReqVO: [],
        contactSimpleVO: null,
        isSupplier: false,
        allInfoPoint: 0,
        canInfoEdit: false
      },
      supplierId: null,
      supplierLevel: [],
      turnOver: [],
      purchaseAmount: [],
      orderPass: [],
      supplierMaterial: [],
      supplierMaterialChart: [],
      supplierMaterialTable: [],
      orderPassChart: [],
      scarTable: [],
      scarChart: [],
      scarPie: [],
      spData: [],
      showCategory: true,
      spDataTop: {

      },
      spDataWorse: {},
      supplierLevelList: [],
      queryParams: {
        pageNo: 1,
        pageSize: 10
      },
      total: 0
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    },
    topLevel() {
      return null;
      /*  const data = [...this.supplierLevel]
        const getSort = (item) => {
          return getDictDatas(DICT_TYPE.SL_SUPPLIER_LEVEL, false).find(a => a.label === item.currentLevel)?.extendedValue
        }
        data?.sort((a, b) => {
          return Number(getSort(a)) - Number(getSort(b))
        })
        return getDictDataLabel(DICT_TYPE.SL_SUPPLIER_LEVEL, data?.at(0)?.currentLevel)*/
    },
    latestCompany() {
      return null;
      /*  const data = [...this.supplierLevel]
        data?.sort((a, b) => {
          return a.levelEffectiveTime - b.levelEffectiveTime
        })
        return getDictDataLabel(DICT_TYPE.COMMON_COMPANY, data?.at(0)?.companyId)*/
    },
    latestCompanyCategory() {
      return null;
      /* const data = [...this.supplierLevel]
       data?.sort((a, b) => {
         return a.levelEffectiveTime - b.levelEffectiveTime
       })
       return getDictDataLabel(DICT_TYPE.COMMON_CATEGORY, data?.at(0)?.categoryId)*/
    }
  },
  mounted() {
    this.supplierId = this.$route.query.supplierId
    this.getBaseInfo()
    this.getLevel()
    this.getTurnover()
    this.getPurchaseAmount()
    this.getOrderPass()
    this.getOrderPassChart()
    this.getSupplierMaterial()
    this.getSupplierMaterialChart()
    this.getMaterialTable()
    this.getScarTable()
    this.getScarChart()
    this.getScarPie()
    this.getSpData()
    this.getSupplierLevel()
  },
  methods: {
    getDictDataLabel,
    dayjs,
    getBaseInfo() {
      getBaseInfo(this.supplierId).then(res => {
        this.baseInfo = res.data
      })
    },
    getLevel() {
      supplierLevelLogListBySupplierId(this.supplierId).then(res => {
        this.supplierLevel = res.data
      })
    },
    getSupplierLevel() {
      getSupplierLevelListBySupplierId({
        ...this.queryParams,
        supplierId: this.supplierId })
        .then(res => {
          this.supplierLevelList = res.data?.list
          this.total = res.data?.total
        })
    },
    getTurnover() {
      getTurnover(this.supplierId).then(res => {
        this.turnOver = res.data?.map(a => {
          return {
            year: a.year,
            value: a.turnoverToCNY,
            value1: a.rateOfChange
          }
        })
      })
    },
    getPurchaseAmount() {
      getPurchaseAmountBySupplier(this.supplierId).then(res => {
        this.purchaseAmount = res.data?.map(a => {
          return {
            year: a.year,
            value: a.purchaseAmount,
            value1: a.rateOfChange
          }
        })
      })
    },
    getOrderPass() {
      getSupplierCompleteOrderStatistics(this.supplierId).then(res => {
        this.orderPass = res.data
      })
    },
    getOrderPassChart() {
      getSupplierStatisticsBoard(this.supplierId).then(res => {
        // this.orderPassChart = [{
        //   value: res.data.completeCountYtd,
        //   label: this.$t('supplier.ytdCompletesTheOrder'),
        //   link: '/order/orderIndex',
        //   action: 'ytdOrder',
        //   linkName: this.$t('financial.relatedDocuments')
        // },
        // {
        //   value: res.data.toBeCompleteCount,
        //   label: this.$t('supplier.pendingOrders'),
        //   link: '/order/orderIndex',
        //   action: 'ytdOrderTodo',
        //   linkName: this.$t('financial.relatedDocuments'),
        //   unit: this.$t('supplier.individual')
        //
        // }, {
        //   value: res.data.orderFrequencyToWeekYtd,
        //   label: this.$t('supplier.ytdOrderFrequencyweekly'),
        //   unit: this.$t('sl.second')
        //
        // },
        // {
        //   value: res.data.averageLtYtd,
        //   label: this.$t('supplier.ytdAverageLt'),
        //   unit: this.$t('order.day')
        //
        // }
        // ]
        this.orderPassChart = res.data
      })
    },
    getSupplierMaterial() {
      getSupplierMaterialYearList(this.supplierId).then(res => {
        this.supplierMaterial = res.data
      })
    },
    getSupplierMaterialChart() {
      getSupplierMaterialStatisticsBoard(this.supplierId).then(res => {
        this.supplierMaterialChart = res.data
        // this.supplierMaterialChart = [{
        //   value: res.data.materialActivityCount,
        //   label: this.$t('supplier.numberOfActiveMaterials'),
        //   unit: this.$t('supplier.individual')
        // },
        // {
        //   value: res.data.materialYtdNewCount.length,
        //   action: 'avplSupplierCount',
        //   label: this.$t('supplier.ytdNewMaterialQuantity'),
        //   link: `/avpl/Avplindex?materialCodeList=${res.data.materialYtdNewCount}`,
        //   linkName: this.$t('financial.relatedDocuments'),
        //   unit: this.$t('supplier.individual')
        // }
        // ]
      })
    },
    getMaterialTable() {
      getSupplierMaterialAvplList(this.supplierId).then(res => {
        this.supplierMaterialTable = res.data
      })
    },
    getScarTable() {
      getSupplierScarStatisticsList(this.supplierId).then(res => {
        this.scarTable = res.data
      })
    },
    getScarChart() {
      getSupplierScarStatisticsBoard(this.supplierId).then(res => {
        // this.scarChart = [{
        //   value: res.data.ytdScarCompleted,
        //   label: this.$t('supplier.ytdScarCompletionCount'),
        //   linkName: this.$t('financial.relatedDocuments'),
        //   link: '/scar/scarindex',
        //   action: 'ytdScar'
        //
        // },
        // {
        //   value: res.data.ytdScarInProgress,
        //   label: this.$t('supplier.scarInProgress'),
        //   linkName: this.$t('financial.relatedDocuments'),
        //   link: '/scar/scarindex',
        //   action: 'ytdScarTodo'
        //
        // }, {
        //   value: res.data.ytdMajorQualityAbnormal,
        //   label: this.$t('supplier.ytdMajorQualityAbnormalities'),
        //   linkName: this.$t('financial.relatedDocuments'),
        //   link: '/scar/scarindex',
        //   action: 'ytdSignificantQualityEvent'
        // }
        // ]
        this.scarChart = res.data
      })
    },
    getScarPie() {
      getSupplierScarStatisticsFigure(this.supplierId).then(res => {
        this.scarPie = res.data
      })
    },
    getSpData() {
      /*  getSupplierSP(this.supplierId).then(res => {
          res.data?.forEach((a, index) => {
            a.link = `/sp/supplier?supplierId=${this.supplierId}&companyId=${a.companyId}&year=${dayjs().year()}`
            if (index === 0) {
              a.manifestations = this.$t('supplier.bestPerformance')
            }
            if (index === res.data.length - 1 && index > 0) {
              a.manifestations = this.$t('supplier.worstPerformance')
            }
            a.boardData = [{
              value: res.data.piScoreYtm,
              label: this.$t('supplier.totalPerformanceScore')

            }, {
              value: res.data.otd,
              label: 'YTD OTD'

            }, {
              value: res.data.incomingBatchPassRate,
              label: this.$t('supplier.ytdIncomingMaterialQualificationRate')
            }
            ]
          })
          if (res.data?.length < 2) {
            this.spData = res.data
          } else {
            this.spDataTop = res.data?.at(0)
            this.spDataWorse = res.data?.at(1)
          }
        })*/
    },
    orderCell(row, columnIndex, field, data) {
      if ([1].includes(columnIndex)) {
        let max = 0
        let num = 0
        let color = ''
        switch (columnIndex) {
          case 1:
            max = Math.max(...data.map(a => a[field]))
            num = row[field]
            color = '#6392CB'
            break
        }
        return {
          background: `linear-gradient(to right, ${color},#ffffff ${num * 100 / max}%, #ffffff ${100 - num * 100 / max}%)`
        }
      }
    },
    rateColor(num) {
      if (num > 0) {
        return '#67C23A'
      } else if (num < 0) {
        return '#FA5151'
      } else {
        return '#FF994F'
      }
    }

  }
}
</script>

<style scoped lang="scss">
.cooperHis {
  padding: 15px 25px;
  margin-bottom: 35px;
}

::v-deep .flexContainer {
  margin: 0;

  div {
    flex: 0 0 25%;
    padding-left: 15px;
  }

  min-width: 500px
}

::v-deep .realTab {
  width: 100%;
  padding: 10px;
  margin-left: 0;
}

.baseinfo {
  ::v-deep .el-descriptions {
    //background: #F2F4F8;
    padding: 5px;
  }

  ::v-deep .el-descriptions__body {
    //background: #F2F4F8;
    padding: 5px;
  }
}

.cooperTitle {
  //background: #F7F7F8;
  padding: 5px 10px;
  font-size: 18px;
}

.marginClass {
  margin: 10px 0;

}

.flex-center {
  display: flex;
  justify-content: center;
  flex-direction: column;
  height: 55px
}

.cooper-center {
  text-align: center;
}

.info-flex{
  padding: 10px 10px 20px;
  display: flex;
  &-item{
    flex: 0 0 10%;
    border-right: 1px solid #EAEAEA;
    padding-left: 40px;
    font-size: 16px;
    div{
      color: #383838;
      line-height: 28px;
      font-weight: 700;
    }
    span{
      color: #929292;
    }
  }
  &-item:last-child {
    border-right: none;
  }
}
.cardTitle{
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 30px;
}
.changeHis{
  margin: 10px 0;
  display: flex;
  &-step{
    background: #FFFFFF;
    border: 1px solid #EAEAEA;
    border-radius: 8px;
    flex: 1 0 90%;
    display: flex;
    padding: 8px 28px;
    &-item{
      font-size: 14px;
      display: flex;
      span{

        margin-right: 5px;
      }
    }
  }

}
.changeHis:not(:last-child)::before{
  content: "|";
  color: #4996B8;
  top: 35px;
  position: relative;
  left: 131px;
}
.order-item{
  flex: 0 0 50%;
  padding:15px 40px;
  margin-bottom: 20px;
}
.up{
  border-radius: 19px;
  padding: 3px;
  background: #67C23A;
  color: rgb(255, 255, 255);
  font-size: 36px;
}
.down{
  border-radius: 19px;
  padding: 3px;
  background: #FA5151;
  color: rgb(255, 255, 255);
  font-size: 36px;
}
.performance-item{
  flex: 0 0 33%;
  padding:0px 40px;
}

::v-deep  .supplierTable .el-table__header-wrapper tr{
  background: #F8F8F8;
}
::v-deep .supplierTable .el-table__header-wrapper th{
  background: #F8F8F8;
  color: #807D7D;
  border: none;
  font-size: 14px!important;
}
::v-deep  .supplierTable::before, .el-table--group::after, .el-table--border::after{
  background: #F8F8F8;
}
.supplierTable{
  ::v-deep td{
    //border: none;
    background: #F8F8F8;
  }
}
.supplierTable{
  ::v-deep .el-table__empty-block{
    background: #F8F8F8;
  }
}

</style>
