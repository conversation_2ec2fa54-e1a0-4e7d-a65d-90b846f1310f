<template>
  <div style="padding: 15px 20px">
    <common-card
      :title="$t('supplier.selectCreditEvaluationProject')"
    >
      <el-table :data="list">
        <el-table-column :label="$t('supplier.creditAssessmentProject')" prop="content" />
        <el-table-column :label="$t('supplier.fullMarksForIndividualItems')" width="80" prop="score" />
        <el-table-column :label="$t('sp.unit')" width="100" prop="unit" />
        <el-table-column :label="$t('supplier.referenceValue')" width="100" prop="referenceValue" />
        <el-table-column :label="$t('supplier.scoringCriteria')" prop="contentDescription" />
        <el-table-column :label="$t('common.operate')" width="120">
          <template #default="scope">
            <el-button type="text" size="mini" @click="editEvaluate(scope.row)">{{ $t('common.edit') }}</el-button>
            <el-button v-if="!scope.row.enabled" type="text" size="mini" @click="switchEvaluate(scope.row)">{{ $t('common.enable') }}</el-button>
            <el-button v-else type="text" size="mini" @click="switchEvaluate(scope.row)">{{ $t('common.disable') }}</el-button>
          </template>

          <!--          <el-button type="text" size="mini">{{ $t('common.disable') }}</el-button>-->
        </el-table-column>
      </el-table>
    </common-card>
    <common-card
      :title="$t('supplier.creditEvaluationConclusionConfiguration')"
    >
      <span>{{ $t('supplier.byScore') }}</span>
      >=
      <el-input-number v-model="passScore" style="margin: 0 25px" :precision="0"  />
      <!--      <el-button type="primary" plain @click="saveScore">{{ $t('common.save') }}</el-button>-->
    </common-card>
    <div class="fixedBottom" style="text-align: right;font-size: 22px;margin-top: 10px;left:calc(100% - 125px);">
      <el-button :loading="saveLoading" type="primary" style="width: 200px" @click="saveScore">{{ $t('common.save') }}</el-button>

    </div>

    <el-dialog
      v-if="dialogVisible"
      :title="$t('supplier.editRating')"
      width="1000px"
      :visible.sync="dialogVisible"
    >
      <el-form
        label-width="100px"
      >
        <el-form-item :label="$t('supplier.creditAssessmentProject')">
          {{ form.content }}
        </el-form-item>
        <el-form-item :label="$t('supplier.fullMarksForIndividualItems')">
          <el-input v-model.number="form.score" style="width: 100px" type="number" />
        </el-form-item>

        <el-form-item :label="$t('supplier.scoringCriteria')">
          <el-table v-if="form.contentType" :data="form.scoreConfigList">
            <el-table-column :label="form.unit? `规则 （单位：${form.unit}）`:`规则` ">
              <template #default="scope">
                <div style="display: flex">
                  <span style="flex: none">>=</span>
                  <el-input v-model="scope.row.opValue" style="width: 120px;margin-left: 20px" type="number" />
                </div>

              </template>
            </el-table-column>
            <el-table-column :label="$t('sp.score')">
              <template #default="scope">
                <el-input v-model.number="scope.row.score" style="width: 100px" type="number" />
              </template>

            </el-table-column>
            <el-table-column :label="$t('common.operate')" width="120">
              <template #default="scope">
                <i
                  class="el-icon-circle-plus"
                  style="margin-top:20px;margin-left:10px;font-size: 18px;cursor: pointer"
                  @click="form.scoreConfigList.push({
                    opValue: '',
                    score: null
                  })"
                />
                <i
                  v-if="form.scoreConfigList.length>1"
                  class="el-icon-remove"
                  style="margin-top:20px;margin-left:10px;font-size: 18px;cursor: pointer"
                  @click="form.scoreConfigList.splice(scope.$index, 1)"
                />
              </template>
            </el-table-column>
          </el-table>

          <el-table v-else :data="form.scoreConfigList">
            <el-table-column :label="$t('supplier.rule')">
              <!--              <template #default="scope">-->
              <!--                <span>{{ scope.row.opValue }}</span>-->
              <!--               -->
              <!--              </template>-->
              <template slot-scope="scope">
                <dict-tag :type="DICT_TYPE.COMMON_Y_N" :value="scope.row.opValue" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('sp.score')">
              <template #default="scope">
                <el-input v-model.number="scope.row.score" style="width: 100px" type="number" />
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

        <el-form-item :label="$t('supplier.scoreCriteriaDescription')">
          <el-input v-model="form.contentDescription" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button plain @click="saveEva">{{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  enabledCreditItemConfig,
  getCreditItemConfig,
  getCreditItemConfigById,
  saveCreditConfig, updateCreditItemConfig
} from '@/api/supplier/credit'
// import { DICT_TYPE } from '@/utils/dict'

import { DICT_TYPE, getDictDatas } from '@/utils/dict'

export default {
  name: 'Config',
  data() {
    return {
      dialogVisible: null,
      list: null,
      passScore: null,
      form: {
        content: '',
        contentType: true,
        unit: null,
        score: 5,
        scoreConfigList: [],
        contentDescription: ''
      },
      saveLoading: false
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      getCreditItemConfig({ versionId: null }).then(res => {
        this.list = res.data.itemConfigList
        this.passScore = res.data.passScore
      })
    },
    saveScore() {
      this.saveLoading = true
      saveCreditConfig({ score: this.passScore },
        this.list).then(res => {
        this.saveLoading = false
        this.$message.success(this.$t('common.savedSuccessfully'))
      })
    },
    switchEvaluate(row) {
      row.enabled = !row.enabled
      // enabledCreditItemConfig(row.id, !row.enabled).then(res => {
      //   this.$message.success(this.$t('order.operationSucceeded'))
      //   this.init()
      // })
    },
    editEvaluate(row) {
      // getCreditItemConfigById({ id: row.id }).then(res => {
      //   this.dialogVisible = true
      //   if (res.data.scoreConfigList.length === 0) {
      //     res.data.scoreConfigList.push({
      //       opValue: '',
      //       score: null
      //     })
      //   }
      // })
      this.dialogVisible = true

      this.form = row
    },
    saveEva() {
      this.dialogVisible = false

      // updateCreditItemConfig(this.form).then(res => {
      //   this.$message.success(this.$t('common.savedSuccessfully'))
      //   this.dialogVisible = false
      //   this.init()
      // })
    }
  }
}
</script>
<style scoped lang="scss">

</style>
