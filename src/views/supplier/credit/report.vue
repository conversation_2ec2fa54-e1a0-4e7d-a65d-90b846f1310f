<template>
  <div style="padding: 15px 20px">
    <div>
      <div
        style="text-align: right;color: #689CB7;font-size: 14px;cursor:pointer;"
        @click="showHead =!showHead"
      >
        {{ $t('common.statisticians') }}
        <i class="el-icon-s-data" style="font-size: 20px;" />
      </div>
      <Transition name="scale">
        <div v-show="showHead" class="flexContainer" :style="`justify-content: ${contentStyle}`">
          <div v-for="item in supplierReport">

            <div class="realTab">
              <div style="display: flex;justify-content: center">
                <span>{{ item.label }}</span>
              </div>
              <div class="realNum">
                <div />
                <number-format
                  :decimal-place="item.decimalPlace?item.decimalPlace:store.getters.decimalPlace"
                  :value="item.value"
                />
                <div style="font-size: 14px;color: #000">
                  <div> {{ item.unit1 }}</div>
                  <div> {{ item.unit2 }}</div>

                </div>
              </div>
            </div>
          </div>

        </div>
      </Transition>
    </div>
    <div style="  display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-autocomplete
        v-model="queryParams.supplierName"
        :placeholder="$t('system.companyNameEnter')"
        :fetch-suggestions="getSupplierDetail"
        class="content"
        clearable
        filterable
        remote
        style="flex: 0 1 35%"
        value-key="name"
        @select="handleQuery"
        @change="changeSupplierName"
      >
        <!--        <el-option-->
        <!--          v-for="dict in supplierList"-->
        <!--          :key="dict.id"-->
        <!--          :label="dict.name"-->
        <!--          :value="dict.id"-->
        <!--        />-->
      </el-autocomplete>
      <el-button
        icon="el-icon-search"
        type="primary"
        :loading="submitLoading"
        @click="createReport"
      >{{ $t('common.confirm') }}
      </el-button>
    </div>

    <div v-if="showReport">
      <common-card
        :default-close="true"
      >
        <span slot="header">
          {{ $t('supplier.supplierRiskScore') }}

          <span :style="`color: ${!result ? 'red':''}`">
            <span style="font-weight: bolder;">{{ score }}</span>
            <span style="position:absolute;left: 50%">{{ result ?$t('supplier.adopt') : $t('supplier.notPassed') }}</span>
          </span>

        </span>

        <div>
          <el-form
            ref="baseInfo"
            inline
            label-width="163px"
          >
            <el-form-item
              :label="$t('supplier.supplierName')"
              class="commonFormItemLeft"
              prop="result"
            >
              <span style="font-weight: bold;color:#4996b8 ">
                {{ queryParams.tempSupplierName }}
              </span>
            </el-form-item>
            <el-form-item class="commonFormItemRight">
              <el-button
                icon="el-icon-download"
                plain
                size="mini"
                type="primary"
                @click="downLoadCreditReport"
              > {{ $t('auth.downloadReport') }}
              </el-button>
            </el-form-item>
            <el-form-item class="commonFormItemRight">
              <el-button
                v-hasPermi="['supplier:credit-investigation:createpdf']"
                icon="el-icon-download"
                plain
                size="mini"
                type="primary"
                @click="createCreditReportPDF"
              > {{ $t('supplier.generateReportPdf') }}
              </el-button>
            </el-form-item>
          </el-form>

        </div>

        <div>
          <el-table
            :class="result ? '':'rejectTable' "
            :data="list"
            :header-cell-style="headerColor"
            :summary-method="getSummaries"
            border
            show-summary
            style="margin-top: 10px"
          >
            <el-table-column :label="$t('rfq.serialNo')" type="index" />
            <el-table-column :label="$t('supplier.creditAssessmentProject')" width="150" prop="content" />
            <el-table-column :label="$t('supplier.tianyanchaInformation')" width="100" prop="creditResult" />
            <el-table-column :label="$t('sp.unit')" width="100" prop="unit" />
            <el-table-column :label="$t('sp.score')" width="100" prop="creditScore" align="center" />
            <el-table-column :label="$t('supplier.scoringCriteria')" prop="contentDescription" />

          </el-table>
          <el-form
            ref="baseInfo"
            inline
            label-width="163px"
          >
            <el-form-item
              :label="$t('supplier.evaluationResults')"
              class="commonFormItem"
              prop="result"
            >
              <span>{{ $t('supplier.resultPassedFailed') }}</span>
            </el-form-item>
            <el-form-item
              :label="$t('supplier.assessmentDate')"
              class="commonFormItem"
              prop="creditDate"
            >
              <span>{{ parseTime(creditDate) }}</span>
            </el-form-item>
            <el-form-item
              :label="$t('supplier.evaluationCriteria')"
              class="commonFormItem"
              prop="passScore"
            >
              <span> >= {{ passScore }} {{ $t('supplier.dividedBy') }}</span>
            </el-form-item>

          </el-form>
        </div>

      </common-card>
    </div>
    <div v-if="queryParams.tempSupplierName ">
      <div class="form-title">{{ queryParams.tempSupplierName }}</div>
      <div class="form-title">{{ $t('system.companyName') }}</div>
      <el-descriptions
        :column="2"
        :label-style="{
          width: '220px',
          'justify-content': 'right'
        }"

        size="medium"
      >
        <el-descriptions-item :label="$t('supplier.telephone')">{{ supplierBase.phoneNumber }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.website')">{{ supplierBase.webSite }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.email')">{{ supplierBase.email }}</el-descriptions-item>

      </el-descriptions>
      <div class="form-title">{{ $t('supplier.basicBusinessInformation') }}</div>
      <el-descriptions
        :label-style="{
          width: '220px',
          'justify-content': 'right'
        }"

        :column="2"
        size="medium"
      >
        <el-descriptions-item :label="$t('supplier.legalPerson')">{{ supplierBase.legalPerson }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.registeredCapital')">{{ supplierBase.registeredCapitalOriginal }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.openingTime')">{{ parseTime(supplierBase.establishedTime) }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.businessStatus')">{{ supplierBase.regStatus }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.registrationNumber')">{{ supplierBase.regNumber }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.unifiedSocialCreditCode')">{{ supplierBase.creditCode }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.organizationalCode')">{{ supplierBase.orgNumber }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.taxpayerIdentificationNumber')">{{ supplierBase.taxNumber }}</el-descriptions-item>
        <el-descriptions-item :label="$t('sp.companyType')">{{ supplierBase.companyOrgType }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.operatingTimeendTime')">
          {{ supplierBase.fromTime>0? parseTime(supplierBase.fromTime) :'' }}-{{ supplierBase.toTime>0?parseTime(supplierBase.toTime ) :'' }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.natureOfBusiness')">{{ supplierBase.businessScope }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.registrationAuthority')">{{ supplierBase.regInstitute }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.approvalTime')">{{ supplierBase.approvedTime>0 ?parseTime(supplierBase.approvedTime) :'' }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.companyNameenglish')">{{ supplierBase.property3 }}</el-descriptions-item>
        <el-descriptions-item :label="$t('supplier.companyRegisteredAddress')">{{ supplierBase.registeredCapitalOriginal }}</el-descriptions-item>
      </el-descriptions>
      <div v-if="supplierBase.staffList.length">
        <div class="form-title">{{ $t('supplier.mainPersonnel') }}</div>
        <el-descriptions
          :label-style="{
            width: '220px',

            'justify-content': 'right'
          }"

          :column="1"
          size="medium"
        >
          <el-descriptions-item :label="$t('supplier.personnel')">{{ $t('supplier.position') }}</el-descriptions-item>
          <el-descriptions-item v-for="item in supplierBase.staffList" :label="item.name">{{ item.typeJoin?.join(',') }}</el-descriptions-item>

        </el-descriptions>
      </div>

    </div>
  </div>
</template>

<script>
import {
  getSupplierCreditReport,
  createSupplierCreditReport,
  getSupplierDetail,
  createSupplierCreditReportPDF,
  getSupplierReportDashboard, getSupplierCompanyInfo
} from '@/api/supplier/credit'
import { parseTime } from '@/utils/ruoyi'
import store from '@/store'

export default {
  name: 'SupplierCreditReport',
  data() {
    return {
      list: null,
      passScore: null,
      result: null,
      score: null,
      creditDate: null,
      creditReportUrl: null,
      supplierList: [],
      canAuthSuppliers: [],
      queryParams: {
        supplierName: '',
        tempSupplierName: '',
        creditId: '',
        supplierId: ''
      },
      supplierReport: [],
      showHead: !this.$route.query.creditId,
      contentStyle: '',
      submitLoading: false,
      showReport: false,
      supplierBase: {
        staffList: []
      }
    }
  },
  computed: {
    store() {
      return store
    }
  },
  mounted() {
    const supplierId = this.$route.query.supplierId
    const creditId = this.$route.query.creditId
    if (creditId) {
      this.queryParams.supplierId = supplierId
      this.queryParams.creditId = creditId
      this.init(supplierId, creditId)
    }
    this.getReport()
  },
  methods: {
    parseTime,
    getSupplierBase() {
      getSupplierCompanyInfo({ supplierName: this.queryParams.supplierName || this.$route.query.supplierName }).then(res => {
        this.supplierBase = res.data
      })
    },
    getReport() {
      getSupplierReportDashboard().then(res => {
        this.supplierReport = [
          {
            label: this.$t('supplier.queryFrequency'),
            value: res.data.supplierCount,
            unit1: 'YTD',
            unit2: res.data.supplierCountYtd
          },
          {
            label: this.$t('supplier.passingTimes'),
            value: res.data.supplierCountPass,
            unit1: `${res.data.supplierCountPassRate}%`,
            unit2: this.$t('supplier.passingRate')
          },
          {
            label: this.$t('supplier.averageCreditScore'),
            value: res.data.supplierCountAvgScore,
            unit1: 'YTD',
            unit2: res.data.supplierCountAvgScoreYtd
          }
        ]
      })
    },
    // 选择供应商下拉
    getSupplierDetail(query, cb) {
      if (query) {
        getSupplierDetail({
          fuzzySupplierName: query
        }).then(res => {
          this.supplierList = res.data
          cb(this.supplierList)
        })
      } else {
        cb([])
      }
    },
    /** 搜索按钮操作 */
    handleQuery(item) {
      this.queryParams.supplierName = item.name
      this.queryParams.supplierId = item.id
      this.init(item.id, '')
    },
    async init(supplierId, creditId) {
      const res = await getSupplierCreditReport({ supplierId: supplierId, creditId: creditId })
      this.list = res.data.detailList
      this.passScore = res.data.passScore
      this.score = res.data.score
      this.result = res.data.result
      this.creditDate = res.data.creditDate
      this.creditReportUrl = res.data.creditReportUrl
      this.queryParams.tempSupplierName = res.data.supplierName
      this.getSupplierBase()
      this.showHead = false
      this.showReport = true
    }, // 选择供应商事件-次切换需要保存和提交时及时clear为空的字段,防止拉出旧值
    clearSupplierRelFields() {
      if (!this.queryParams.supplierName) {
        this.queryParams.supplierName = ''
      }
    },
    createReport() {
      if (!this.queryParams.supplierName) {
        this.$message.info(this.$t('supplier.pleaseEnterTheSupplierFirst'))
        return
      }
      this.submitLoading = true
      this.queryParams.creditId = null
      createSupplierCreditReport({ supplierName: this.queryParams.supplierName, supplierId: this.queryParams.supplierId }).then(res => {
        this.queryParams.creditId = res.data
        this.init(this.queryParams.supplierId, res.data)
      }).finally(() => {
        this.submitLoading = false
      })
    },
    async downLoadCreditReport() {
      await this.init(this.queryParams.supplierId, this.queryParams.creditId)
      const url = this.creditReportUrl
      if (url === null || url === '') {
        this.$message.info(this.$t('supplier.evaluationReportNotFoundYetPleaseTryAgainLater'))
        return
      } else {
        window.open(url, '_blank')
      }
    },
    createCreditReportPDF() {
      const supplierId = this.queryParams.supplierName
      if (supplierId === '' || supplierId === null) {
        this.$message.info(this.$t('scar.pleaseSelectASupplierFirst'))
        return
      }
      createSupplierCreditReportPDF({ supplierId: supplierId }).then(res => {
        this.init(supplierId, res.data)
      })
    },
    headerColor({ row, columnIndex }) {
      if (columnIndex > 4) {
        if ((columnIndex - 4) % 2 === 0) {
          return {
            background: '#bccddd',
            color: '#fff'
          }
        }
      }
      if (columnIndex === 3) {
        return {
          background: '#4996b8',
          color: '#fff'
        }
      }
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = this.$t('supplier.total')
          return
        }
        if (index === 4 || index === 2 || index === 5) {
          sums[index] = ''
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    changeSupplierName(val) {
      this.queryParams.supplierId = null
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .rejectTable{
  .el-table__footer-wrapper{
    .is-center{
      .cell{
        color: red;
      }
    }
  }
}
.commonBtn {
  min-width: 80px;
}
.commonFormItem {
  width: 50%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }
}
.commonFormItemLeft {
  width: 50%;
  padding-bottom: 5px;
  margin: 1px 0 !important;

  .content {
    width: 100%;
  }
}
.commonFormItemRight {
  width: 50%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
  text-align: right;
  .content {
    width: 100%;
  }
}
.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.searchValue {
  width: 80%;
}

.flexContainer {
  display: flex;
  justify-content: center;
  min-width: 1000px;
  margin: 20px 0
}

.realTab {
  width: 220px;
  height: 90px;
  font-size: 16px;
  background: rgb(242, 244, 249);
  color: #000000;
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding: 21px;
  margin-left: 30px;
}

.realNum {
  color: #327da1;
  display: flex;
  justify-content: space-between;
  font-size: 25px;
}

.scale-leave-active {
  -webkit-animation: scale-down-tr 0.4s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  animation: scale-down-tr 0.4s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}

.scale-enter-active {
  -webkit-animation: scale-up-tr 0.4s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
  animation: scale-up-tr 0.4s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
}

@-webkit-keyframes scale-up-tr {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@keyframes scale-up-tr {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@-webkit-keyframes scale-down-tr {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@keyframes scale-down-tr {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}
.form-title {
  border-left: 6px solid #376092;
  margin: 10px 0;
  padding: 10px 30px;
  font-size: 16px;
  font-weight: bold;
  background-color: #f1f1f1;
}
</style>
