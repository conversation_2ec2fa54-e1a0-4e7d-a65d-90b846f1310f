<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item :label="$t('supplier.creditReportingCurrency')" prop="creditCurrency">
        <el-input v-model="queryParams.creditCurrency" :placeholder="$t('supplier.pleaseEnterTheCreditReportingCurrency')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item :label="$t('supplier.systemCurrency')" prop="systemCurrencyCode">
        <el-select v-model="queryParams.systemCurrencyCode" :placeholder="$t('supplier.pleaseEnterTheSystemCurrency')" style="width: 100%" clearable>
          <el-option v-for="item in getDictDatas(DICT_TYPE.COMMON_CURRENCY)" :key="item.name" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('avpl.source')" prop="sourceCode">
        <el-input v-model="queryParams.sourceCode" :placeholder="$t('auth.pleaseEnterTheSource')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >{{ $t('common.add') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <!--        <el-button-->
        <!--          type="warning"-->
        <!--          plain-->
        <!--          icon="el-icon-download"-->
        <!--          size="mini"-->
        <!--          :loading="exportLoading"-->
        <!--          @click="handleExport"-->
        <!--        >{{ $t('common.export') }}</el-button>-->
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">

      <el-table-column :label="$t('supplier.creditReportingCurrency')" align="center" prop="creditCurrency" />
      <el-table-column :label="$t('supplier.systemCurrency')" align="center" prop="systemCurrencyCode">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="scope.row.systemCurrencyCode" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('avpl.source')" align="center" prop="sourceCode" />

      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('supplier.creditReportingCurrency')" prop="creditCurrency">
          <el-input v-model="form.creditCurrency" :placeholder="$t('supplier.pleaseEnterTheCreditReportingCurrency')" />
        </el-form-item>

        <el-form-item :label="$t('supplier.systemCurrency')" prop="systemCurrencyCode">
          <el-select v-model="form.systemCurrencyCode" :placeholder="$t('supplier.pleaseEnterTheSystemCurrency')" style="width: 100%">
            <el-option v-for="item in getDictDatas(DICT_TYPE.COMMON_CURRENCY)" :key="item.name" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('avpl.source')" prop="sourceCode">
          <el-input v-model="form.sourceCode" :placeholder="$t('auth.pleaseEnterTheSource')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>
        <el-button @click="cancel">{{ $t('sp.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createCreditCurrency, updateCreditCurrency, deleteCreditCurrency, getCreditCurrency, getCreditCurrencyPage, exportCreditCurrencyExcel } from '@/api/supplier/creditCurrency'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'

export default {
  name: 'Creditcurrency',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 征信币种汇率配置列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        creditCurrency: null,
        exchangeRate: null,
        systemCurrencyCode: null,
        sourceCode: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        creditCurrency: [{required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur'}],
        systemCurrencyCode: [{required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur'}]
      }
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getDictDatas,
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = {...this.queryParams}
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getCreditCurrencyPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        creditCurrency: undefined,
        exchangeRate: undefined,
        systemCurrencyCode: undefined,
        sourceCode: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('supplier.addCreditCurrencyExchangeRateConfiguration')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getCreditCurrency(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('supplier.modifyTheCurrencyExchangeRateConfigurationForCreditReporting')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.form.systemCurrencyId = getDictDatas(DICT_TYPE.COMMON_CURRENCY).find(a => a.code === this.form.systemCurrencyCode)?.id
        // 修改的提交
        if (this.form.id != null) {
          updateCreditCurrency(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createCreditCurrency(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm(this.$t('supplier.areYouSureToDeleteTheCreditReportingCurrencyExchangeRateConfigurationNumber') + id + this.$t('material.dataItemOf')).then(function () {
        return deleteCreditCurrency(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = {...this.queryParams}
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行导出
      this.$modal.confirm(this.$t('supplier.areYouSureToExportAllCreditCurrencyExchangeRateConfigurationDataItems')).then(() => {
        this.exportLoading = true
        return exportCreditCurrencyExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('supplier.currencyExchangeRateConfigurationForCreditReportingXlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>
