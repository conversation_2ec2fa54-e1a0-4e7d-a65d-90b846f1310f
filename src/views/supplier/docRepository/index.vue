<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px" @submit.native.prevent>
      <el-form-item :label="$t('supplier.documentName')" prop="name">
        <el-input v-model="queryParams.name" :placeholder="$t('supplier.documentName')" clearable @keyup.enter.native="handleFilter" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('common.search') }}</el-button>
        <!--        Lisa- 重置功能键取消-->
        <!--        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>-->

        <el-button type="primary" plain @click="expandAll">{{ $t('common.unfoldcollapse') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <!--        Lisa- 新增功能键取消-->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          v-hasPermi="['supplier:doc-repository:create']"-->
      <!--          :disabled="lock"-->
      <!--          type="primary"-->
      <!--          plain-->
      <!--          icon="el-icon-plus"-->
      <!--          size="mini"-->
      <!--          @click="handleAdd"-->
      <!--        >{{ $t('common.add') }}</el-button>-->
      <!--      </el-col>-->
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table
      v-if="freshTable"
      ref="tables"
      v-loading="loading"
      :default-expand-all="isExpandAll"
      :data="tableList"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      row-key="index"
    >
      <el-table-column show-overflow-tooltip :min-width="!supplierId?200:300" :label="$t('supplier.documentName')" prop="name">
        <template #default="scope">
          <i v-if="scope.row.type==='folder'" style="margin-right: 5px" class="el-icon-folder" />
          <span v-if="scope.row.type==='folder'">
            <el-button v-if="supplierId&&parentIndexList.includes(scope.row.index)" type="text" @click="handleTemplate(scope.row)">{{ scope.row.name }}</el-button>
            <span v-else>{{ scope.row.name }}</span>
          </span>
          <el-button v-else icon="el-icon-document" type="text" @click="downloadFile(scope.row.path)">
            <div :style="overVlow">
              {{ scope.row.name }}
            </div>
          </el-button>

        </template>
      </el-table-column>
      <el-table-column
        v-if="!supplierId"
        show-overflow-tooltip
        width="80"
        :label="$t('common.fileSize')"
        prop="size"
      >
        <template #default="scope">
          {{ scope.row.size?formatBytes(scope.row.size): '' }}

        </template>
      </el-table-column>
      <el-table-column
        v-if="!supplierId"
        align="center"
        width="130"
        :label="$t('common.enabled')"
        prop="status"
      >
        <template slot-scope="scope">
          <dict-tag v-if="scope.row.type==='folder'" :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="!supplierId"
        align="center"
        width="130"
        :label="$t('supplier.openToSuppliers')"
        prop="openToSupplier"
      >
        <template slot-scope="scope">
          <dict-tag v-if="scope.row.type==='folder'" :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.openToSupplier" />
        </template>
      </el-table-column>
      <el-table-column
        width="100"
        :label="$t('supplier.lastOperator')"
        prop="operator"
      >
        <template #default="scope">
          <span v-if="scope.row.type ==='file'">{{ scope.row.operator }}</span>
        </template>
      </el-table-column>
      <el-table-column
        width="100"
        :label="$t('supplier.updateTime')"
        prop="updateTime"
      >
        <template #default="scope">
          <span v-if="scope.row.type ==='file'">{{

            parseTime(scope.row.updateTime,'{y}-{m}-{d}')
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="supplierId"
        width="90"
        :label="$t('supplier.termOfValidity')"
        prop="expireDate"
      >
        <template #default="scope">
          <span>{{

            parseTime(scope.row.expireDate,'{y}-{m}-{d}')
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        width="100"
        show-overflow-tooltip
        :label="$t('common.remarks')"
        prop="remark"
      >
        <template #default="scope">
          <span
            v-if="scope.row.type === 'folder'"
          >
            {{ scope.row.remark }}
          </span>
          <span v-else>
            {{ scope.row.fileRemark }}

          </span>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        width="240"
        :label="$t('common.operate')"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.type === 'folder'"
            v-hasPermi="['supplier:doc-repository:create']"
            type="text"
            icon="el-icon-plus"
            :disabled="lock"
            @click="handleAdd(scope.row)"
          >{{ $t('common.add') }}</el-button>

          <el-button
            v-if="scope.row.type === 'folder'"
            v-hasPermi="['infra:file:upload']"
            type="text"
            icon="el-icon-upload2"
            :disabled="lock"
            @click="handleUpload(scope.row)"
          >{{ supplierId? $t('common.upload'):$t('common.uploadTemplate') }}</el-button>
          <el-button
            v-if="scope.row.type === 'folder'"
            v-hasPermi="['supplier:doc-repository:update']"
            type="text"
            icon="el-icon-edit"
            :disabled="lock||(supplierId&&scope.row.supplierId !== Number(supplierId))"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.edit') }}</el-button>
          <el-button
            v-hasPermi="['supplier:doc-repository:delete']"
            type="text"
            icon="el-icon-delete"
            :disabled="lock||(supplierId&&scope.row.supplierId !== Number(supplierId))"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}</el-button>

        </template>
      </el-table-column>
    </el-table>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="400px" append-to-body>
      <el-form ref="forms" :model="form" :rules="rules" label-width="115px">
        <el-row>
          <el-col v-for="(item,i) in form.translations" :key="i" :span="24">
            <el-form-item
              :label="$t('supplier.documentName')"
              :prop="`translations[${i}].translation`"
              :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'blur'}"
            ><el-input v-model="item.translation" :placeholder="$t(`common.pleaseEnterLanguageIn${item.locale}`)">
              <svg-icon slot="suffix" :icon-class="item.locale" />
            </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$t('supplier.parentFolder')" prop="parentId">
          <treeselect
            v-model="form.parentId"
            :normalizer="normalizer"
            :options="listOptions"
            :show-count="true"
            :placeholder="$t('supplier.pleaseSelectAParentFolder')"
          />
        </el-form-item>
        <el-form-item v-if="!supplierId" :label="$t('supplier.openToSuppliers')" prop="openToSupplier">
          <el-switch v-model="form.openToSupplier" :active-value="0" :inactive-value="1" @change="handleStatusChange(scope.row)" />
        </el-form-item>
        <el-form-item :label="$t('supplier.documentLibraryNotes')" prop="remark">
          <el-input v-model="form.remark" :placeholder="$t('supplier.pleaseEnterADocumentLibraryComment')" />
        </el-form-item>
        <el-form-item
          v-if="!supplierId"
          :label="$t('common.enableStatus')"
          prop="status"
        >
          <el-switch v-model="form.status" :active-value="0" :inactive-value="1" @change="handleStatusChange(scope.row)" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>
    <el-dialog
      v-if="showUpload"
      :title="$t('common.uploadFile')"
      width="400px"
      append-to-body
      :before-close="clearUpload"
      :visible.sync="showUpload"
    >
      <el-form ref="uploadForm" :rules="uploadRules" :model="uploadInfo" label-width="110px" inline>
        <el-form-item
          v-if="supplierId"
          prop="alwaysValid"
          :label="$t('supplier.permanentlyValid')"
        >
          <el-radio-group v-model="uploadInfo.alwaysValid">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="uploadInfo.alwaysValid ==='false'"
          prop="expireDate"
          :label="$t('supplier.validUntil')"
        >
          <el-date-picker
            v-model="uploadInfo.expireDate"
            type="date"
          />
        </el-form-item>
        <el-upload
          ref="upload"
          :auto-upload="false"
          class="upload-demo"
          drag
          :headers="getBaseHeader()"
          :action="uploadUrl"
          :before-upload="beforeUpload"
          :on-success="(response, file, fileList)=>OnSuccess(response, file, fileList,)"
          multiple
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>

        <el-form-item
          style="margin-top: 20px"
          :label="$t('supplier.fileComments')"
        >
          <el-input
            v-model="uploadInfo.remark"
            style="width: 220px"
            :autosize="{ minRows: 2}"
            type="textarea"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUpload">{{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>
    <el-dialog
      v-if="templateVisible"
      :title="$t('common.downloadTemplate')"
      width="400px"
      append-to-body
      :visible.sync="templateVisible"
    >
      <div v-for="(item,index) in templateFile" :key="index" style="margin: 5px 20px">
        <el-link @click="downloadFile(item.path)">
          {{ item.name }}
        </el-link>
      </div>

    </el-dialog>
  </div>
</template>

<script setup>
import {
  createDocRepository,
  updateDocRepository,
  deleteDocRepository,
  getDocRepository,
  getDocRepositoryPage,
  repositoryFileBind, deleteDocRepositoryFile
} from '@/api/supplier/docRepository'
import { defineComponent, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n-composable'
import { handleTree } from '@/utils/ruoyi'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { getBaseHeader, handleAuthorized } from '@/utils/request'
import $modal from '@/plugins/modal'
import { formatBytes } from '@/utils'
import { parseTime } from '@/utils/ruoyi'
import { getConfigKey } from '@/api/infra/config'
import { addTranslation } from '@/utils/i18n'
import router from '@/router'

defineComponent({
  Treeselect
})

const { t } = useI18n()
const $t = t
// 遮罩层
const loading = ref(true)

// 显示搜索条件
const showSearch = ref(true)

// 供应商文档库列表
const list = ref([])
const tableList = ref([])

// 弹出层标题
const title = ref('')

// 是否显示弹出层
const open = ref(false)

// 展示上传
const showUpload = ref(false)

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: null
})

const uploadUrl = process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload'

const form = reactive({
  name: undefined,
  parentId: undefined,
  openToSupplier: undefined,
  remark: undefined,
  status: 0,
  translations: [{
    translation: '',
    locale: 'zh'
  },
  {
    translation: '',
    locale: 'en'
  }
  ]
})
// 表单参数
const rules = ref({
  parentId: [{ required: true, message: $t('supplier.documentLibraryFolderParentIdCannotBeEmpty'), trigger: 'change' }],
  openToSupplier: [{ required: true, message: $t('supplier.openToSupplierrequired'), trigger: 'change' }],
  status: [{ required: true, message: $t('common.enabledStatusrequired'), trigger: 'change' }]
}
)
const props = defineProps(['supplierIdRef', 'lock'])
const supplierId = ref(props.supplierIdRef)

onMounted(() => {
  getList()
  getConfigKey('file.type.common').then(response => {
    fileType.value = response.data
  })
})

const normalizer = (node) => {
  if (node.children && !node.children.length) {
    delete node.children
  }
  if (node.type === 'file') {
    return
  } else {
    return {
      id: node.id,
      label: node.name,
      children: node.children
    }
  }
}

const tempList = ref([])
// watch(() => queryParams.name,
//   (name) => {
//
//   }
// )

const overVlow = ref({
  display: 'inline-block',
  width: !supplierId.value ? '200px' : '180px',
  overflow: 'hidden',
  'text-overflow': 'ellipsis',
  'white-space': 'nowrap'
})

const handleFilter = async() => {
  await getList()
  if (queryParams.name) {
    tempList.value = []
    const data = []
    const traverse = (item) => {
      if (item.parentIndex !== 0) {
        const temp = list.value.find(val => val.index === item.parentIndex)
        if (temp === undefined) {
          return
        }
        if (!data.some(data => data.index === temp.index)) {
          data.push(temp)
          traverse(temp)
        }
      }
    }
    list.value.map(item => {
      if (item.name?.toLocaleLowerCase().includes(queryParams.name.toLowerCase())) {
        if (!data.some(data => data.index === item.index)) {
          data.push(item)
          traverse(item)// 递归查找父节点
        }
      }
    })

    tempList.value.push(...handleTree(data, 'index', 'parentIndex'))
  }
  tableList.value = tempList.value
  expandAll(true)
}

const parentIndexList = ref([])
/** 查询列表 */
const getList = async() => {
  loading.value = true
  // 处理查询参数
  // 执行查询
  const response = await getDocRepositoryPage({
    supplierId: supplierId.value
  })
  // 查询参数
  listOptions.value = []
  const opt = { id: 0, name: $t('supplier.mainCategory'), children: [] }
  opt.children = handleTree(response.data, 'index', 'parentIndex')
  listOptions.value.push(opt)

  list.value = response.data.slice(0)

  if (supplierId.value) {
    parentIndexList.value = response.data.map(item => {
      if (item.parentIndex && item.type !== 'folder' && !item.supplierId) {
        return item.parentIndex
      }
    }
    ) // 记录存在子集的文件夹id
    response.data = response.data.filter(item => item.type === 'folder' || item.supplierId === Number(supplierId.value))
    // 供应商信息登记入口 不展示树形文件 改为弹出框下载模板
  }
  tableList.value = []
  tempList.value = []
  tableList.value.push(...handleTree(response.data, 'index', 'parentIndex'))
  tempList.value.push(...handleTree(response.data, 'index', 'parentIndex'))
  loading.value = false
}

const isExpandAll = ref(false)
const freshTable = ref(true)
const expandAll = (isExpand) => {
  freshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    freshTable.value = true
  })
}

const listOptions = ref([])
/** 查询菜单下拉树结构 */
const getTreeselect = () => {
  getDocRepositoryPage({
    supplierId: supplierId.value
  }).then(response => {
    listOptions.value = []
    const opt = { id: 0, name: $t('supplier.mainCategory'), children: [] }
    opt.children = handleTree(response.data, 'index', 'parentIndex')
    listOptions.value.push(opt)
  })
}

/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}
/** 表单重置 */
const reset = () => {
  Object.assign(
    form,
    {
      id: undefined,
      name: undefined,
      parentId: undefined,
      openToSupplier: undefined,
      remark: undefined,
      status: 0,
      translations: [{
        translation: '',
        locale: 'zh'
      },
      {
        translation: '',
        locale: 'en'
      }
      ],
      supplierId: supplierId.value || null

    }
  )
  forms.value?.resetFields()
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
/** 重置按钮操作 */

const queryForm = ref(null)
/** 新增按钮操作 */
const handleAdd = (row) => {
  getTreeselect()
  reset()
  if (row) {
    Object.assign(form, { parentId: row.id })
  }
  title.value = $t('supplier.addVendorDocumentLibrary')
  open.value = true
}
/** 修改按钮操作 */
const handleUpdate = (row) => {
  getTreeselect()
  reset()
  const id = row.id
  getDocRepository(id).then(response => {
    Object.assign(form, response.data)
    if (form.translations?.length === 0) {
      // 新增时初始化中文翻译框
      addTranslation(form)
    }
    title.value = $t('supplier.modifySupplierDocumentLibrary')
    open.value = true
  })
}

const uploadInfo = reactive({
  alwaysValid: 'false',
  expireDate: '',
  files: [
    {
      fileId: '',
      name: '',
      size: ''
    }
  ],
  remark: '',
  supplierId: supplierId.value || null
})

const uploadRules = ref({
  alwaysValid: [{ required: true, message: $t('common.pleaseSelect'), trigger: 'change' }],
  expireDate: [{ required: true, message: $t('common.pleaseEnter'), trigger: 'blur' }]
}
)
const upload = ref(null)
const uploadForm = ref(null)
const submitUpload = (row) => {
  uploadForm.value.validate(valid => {
    if (!valid) {
      return
    }
    upload.value.submit()
  })
}
const handleUpload = (row) => {
  reset()
  Object.assign(form, row)
  showUpload.value = true
}

/** 提交按钮 */
const forms = ref(null)
const submitForm = () => {
  forms.value.validate(valid => {
    if (!valid) {
      return
    }
    form.name = form.translations?.find(item => item.locale === 'zh').translation
    // 修改的提交
    if (form.id != null) {
      updateDocRepository(form).then(response => {
        $modal.msgSuccess($t('common.modifiedSuccessfully'))
        open.value = false
        getList()
      })
      return
    }
    // 添加的提交
    createDocRepository(form).then(response => {
      $modal.msgSuccess($t('common.addSuccess'))
      open.value = false
      getList()
    })
  })
}
/** 删除按钮操作 */
const handleDelete = (row) => {
  const id = row.id
  $modal.confirm($t('common.areYouSureToDeleteThisDataItem')).then(function() {
    if (row.type === 'file') {
      return deleteDocRepositoryFile(id)
    } else {
      return deleteDocRepository({ id, supplierId: supplierId.value })
    }
  }).then(() => {
    getList()
    $modal.msgSuccess($t('common.delSuccess'))
  }).catch(() => {})
}

const clearUpload = (close) => {
  Object.assign(
    uploadInfo,
    {
      alwaysValid: 'false',
      expireDate: '',
      files: [
        {
          fileId: '',
          name: '',
          size: ''
        }
      ],
      remark: '',
      supplierId: supplierId.value || null
    }
  )
  close()
}

const fileType = ref('')

const beforeUpload = (file) => {
  if (file.size > process.env.VUE_APP_FILESIZE * 1024 * 1024) {
    $modal.msgError($t('common.uploadFileSizeCannotExceedm', { fileSize: process.env.VUE_APP_FILESIZE }))
    return false
  }
  if (!fileType.value.split(',').includes(file.name.slice(file.name.lastIndexOf('.') + 1))) {
    $modal.msgError('只支持' + fileType.value + '格式')
    return false
  }
}

// 上传成功绑定
const OnSuccess = (response, file, fileList) => {
  if (response.code === 401) {
    return handleAuthorized()
  }
  uploadInfo.files = [
    {
      fileId: response.data.id,
      name: file.name,
      size: file.size
    }
  ]
  uploadInfo.folderId = form.id
  repositoryFileBind(uploadInfo).then(res => {
    $modal.msgSuccess($t('common.uploadSucceeded'))
    Object.assign(
      uploadInfo,
      {
        alwaysValid: 'false',
        expireDate: '',
        files: [
          {
            fileId: '',
            name: '',
            size: ''
          }
        ],
        remark: '',
        supplierId: supplierId.value || null
      }
    )
    uploadForm.value?.resetFields()
    nextTick(() => {
      showUpload.value = false
      getList()
    })
  })
}

const templateVisible = ref(false)
const templateFile = ref([])
const handleTemplate = (row) => {
  templateVisible.value = true
  const index = row.index
  templateFile.value = list.value.filter(item => item.type === 'file' && item.parentIndex === index && !item.supplierId)
}

const downloadFile = (url) => {
  window.open(url)
}
</script>
