<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="86px" size="small">
      <el-form-item :label="$t('supplier.dictionaryLabel')" prop="uploadType">
        <el-select
          v-model="queryParams.uploadType"
          :placeholder="$t('supplier.pleaseSelectDictionaryLabel')"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in supplierUploadTypeDictDatas"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('supplier.folderName')" prop="folderName">
        <el-input
          v-model="queryParams.folderName"
          :placeholder="$t('supplier.pleaseEnterAFolderName')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('common.sort')" align="center" prop="sort" />
      <el-table-column :label="$t('supplier.dictionaryLabel')" align="center" prop="label" />
      <el-table-column :label="$t('supplier.dictionaryKeyValue')" align="center" prop="value" />
      <el-table-column :label="$t('supplier.folderName')" align="center" prop="folderName" />
      <el-table-column :label="$t('supplier.dictionaryNotes')" align="center" prop="remark" />
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['supplier:doc-repository-upload-entry-rel:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('supplier.dictionaryLabel')" prop="label">
          <el-input v-model="form.label" disabled />
        </el-form-item>
        <el-form-item :label="$t('supplier.dictionaryKeyValue')" prop="uploadType">
          <el-input v-model="form.value" disabled />
        </el-form-item>
        <el-form-item :label="$t('common.folder')">
          <el-checkbox v-model="folderExpand" @change="handleCheckedTreeExpand($event)">{{ $t('common.expandCollapse') }}</el-checkbox>
          <el-tree
            ref="folder"
            :check-strictly="form.folderCheckStrictly"
            :data="folderOptions"
            :props="defaultProps"
            class="tree-border"
            :empty-text="$t('common.loadingPleaseWait')"
            node-key="id"
            show-checkbox
            @check-change="handleCheckChange"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDocRepositoryTreeSimple,
  getDocRepositoryUploadEntryRelPage,
  updateDocRepositoryUploadEntryRel
} from '@/api/supplier/docRepositoryUploadEntryRel'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'

export default {
  name: 'Docrepositoryuploadentryrel',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 文档库和上传入口的绑定关系列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      folderExpand: false,
      folderNodeAll: false,
      // 文档库文件夹目录
      folderOptions: [],
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        uploadType: null,
        folderName: null
      },
      // 表单参数
      form: {},
      defaultProps: {
        folderId: 'id',
        label: 'name',
        children: 'children'
      },
      // 表单校验
      rules: {},
      // 数据字典
      supplierUploadTypeDictDatas: getDictDatas(DICT_TYPE.SUPPLIER_UPLOAD_TYPE)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getDocRepositoryUploadEntryRelPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.folderExpand = false
      this.folderNodeAll = false
      this.form = {
        id: undefined,
        folderId: undefined,
        uploadType: undefined,
        folderCheckStrictly: true
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.form = row
      this.open = true
      this.title = this.$t('supplier.modifyTheBindingRelationshipBetweenTheDocumentLibraryAndTheUploadPortal')

      // 获得文档库文件夹列表
      getDocRepositoryTreeSimple().then(response => {
        // 处理 folderOptions 参数
        this.folderOptions = []
        this.folderOptions.push(...this.handleTree(response.data, 'id'))
        // 设置已配置的文件夹选中
        // 设置为严格，避免设置父节点自动选中子节点，解决半选中问题
        this.form.folderCheckStrictly = true
        // 设置选中
        this.$refs.folder.setCheckedKeys([this.form.folderId])
        // 设置为非严格，继续使用半选中
        this.form.folderCheckStrictly = false
      })
    },
    /** 单选功能 */
    handleCheckChange(data, checked) {
      if (checked) {
        this.form.folderId = data.id
        this.$refs.folder.setCheckedKeys([data.id])
      }
    },
    /** 树权限（展开/折叠） */
    handleCheckedTreeExpand(value) {
      const treeList = this.folderOptions
      for (let i = 0; i < treeList.length; i++) {
        this.$refs.folder.store.nodesMap[treeList[i].id].expanded = value
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        updateDocRepositoryUploadEntryRel(
          {
            id: this.form.id,
            folderId: this.$refs.folder.getCheckedKeys().length > 1 ? this.$refs.folder.getCheckedKeys()[1] : this.$refs.folder.getCheckedKeys()[0],
            uploadType: this.form.value
          }).then(response => {
          if (this.form.id != null) {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          } else {
            this.$modal.msgSuccess(this.$t('common.addSuccess'))
          }
          this.open = false
          this.getList()
        })
      })
    }
  }
}
</script>
