<template>
  <div style="display: flex;flex-wrap: wrap;width: 900px;margin: 15px auto">
    <div>
      <show-or-edit
        disabled="true"
        :value="supplierNameAndNo"
        style="font-size: 16px; font-weight: 700; color: rgba(73, 150, 184, 0.99); padding-left: 10px;"
      >
        <el-input v-model="supplierNameAndNo" />
      </show-or-edit>
    </div>

    <el-form ref="payment" :model="paymentInfo" :rules="paymentInfoRule" inline label-width="155px">
      <el-card
        style="margin-bottom: 15px"
        class="shadow "
      >
        <el-form-item :label="$t('supplier.unifiedSocialCreditCode')" prop="creditCode">
          <show-or-edit
            :disabled="viewOnly"
            :value="paymentInfo.creditCode"
          >
            <el-input v-model="paymentInfo.creditCode" />
          </show-or-edit>
        </el-form-item>
        <el-form-item :label="$t('supplier.paymentMethod')" prop="paymentType">
          <show-or-edit
            :disabled="paymentInfo.processInstanceId"
            :value="paymentInfo.paymentType"
            :dict="DICT_TYPE.SUPPLIER_TERMS_OF_PAYMENT"
          >
            <el-select
              v-model="paymentInfo.paymentType"
              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
              filterable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_TERMS_OF_PAYMENT)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item :label="$t('supplier.taxRate')" prop="taxRate">
          <show-or-edit
            :disabled="paymentInfo.processInstanceId"
            :value="paymentInfo.taxRate"
            :dict="DICT_TYPE.SUPPLIER_RATE"
          >
            <el-select
              v-model="paymentInfo.taxRate"
              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
              filterable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_RATE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item :label="$t('supplier.invoiceType')">
          <show-or-edit
            :disabled="paymentInfo.processInstanceId"
            :value="paymentInfo.invoiceType"
            :dict="DICT_TYPE.SUPPLIER_INVOICE_TYPE"
          >
            <el-select
              v-model="paymentInfo.invoiceType"
              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
              filterable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_INVOICE_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item :label="$t('supplier.termOfPayment')" prop="accountDay" required>
          <show-or-edit
            :disabled="paymentInfo.processInstanceId"
            :value="paymentInfo.accountDay"
            :dict="DICT_TYPE.SUPPLIER_PAYMENT_DAYS"
          >
            <el-select
              v-model="paymentInfo.accountDay"
              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
              filterable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item :label="$t('supplier.serviceChargePayer')">
          <show-or-edit
            :disabled="paymentInfo.processInstanceId"
            :value="paymentInfo.feeBearer"
            :dict="DICT_TYPE.SUPPLIER_FEE_BEARER"
          >
            <el-select
              v-model="paymentInfo.feeBearer"
              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
              filterable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_FEE_BEARER)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item :label="$t('supplier.deliveryTerms')">
          <show-or-edit
            :disabled="paymentInfo.processInstanceId"
            :value="paymentInfo.deliveryType"
            :dict="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION"
          >
            <el-select
              v-model="paymentInfo.deliveryType"
              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
              filterable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item :label="$t('supplier.countryregion')" prop="countryRegion">
          <show-or-edit
            :disabled="paymentInfo.processInstanceId"
            :value="paymentInfo.countryRegion"
            :custom-list="region"
          >
            <el-select
              v-model="paymentInfo.countryRegion"
              class="smallSelect"
              clearable
              filterable
              remote
              @change="(val)=>{changeRegion(val,paymentInfo)}"
            >
              <el-option v-for="re in region" :key="re.keyId" :label="re.name" :value="re.id" />
            </el-select>
          </show-or-edit>
        </el-form-item>
      </el-card>
      <el-card>
        <div
          v-for="(item,index) in paymentInfo.bankInfoCreateReqVO"
          :key="index"
          class="shadow shadowPadding"
        >
          <el-form-item
            :label="$t('system.currency')"
            :prop="`bankInfoCreateReqVO[${index}].currency`"
          >
            <show-or-edit
              :disabled="paymentInfo.processInstanceId"
              :value="item.currency"
              :dict="DICT_TYPE.COMMON_CURRENCY"
            >
              <el-select v-model="item.currency" class="smallSelect" filterable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                  :key="dict.value"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('supplier.invoiceAddress')"
            :prop="`bankInfoCreateReqVO[${index}].invoiceAddress`"
          >
            <show-or-edit
              :disabled="paymentInfo.processInstanceId"
              :value="item.invoiceAddress"
            >
              <el-input v-model="item.invoiceAddress" />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('supplier.bankOfDeposit')"
            :prop="`bankInfoCreateReqVO[${index}].bankName`"
            :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'change'}"
            style="width: 100%;"
          >
            <show-or-edit
              :disabled="paymentInfo.processInstanceId"
              :value="item.bankName"
            >
              <el-input v-model="item.bankName" class="bigInput" />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('supplier.branchBranchName')"
            :prop="`bankInfoCreateReqVO[${index}].subbranchName`"
            :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'change'}"
            style="width: 100%;"
          >
            <show-or-edit
              :disabled="paymentInfo.processInstanceId"
              :value="item.subbranchName"
            >
              <el-input v-model="item.subbranchName" class="bigInput" />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('supplier.bankAccount')"
            :prop="`bankInfoCreateReqVO[${index}].bankAccount`"
            :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'change'}"
            style="width: 100%;"
          >
            <show-or-edit
              :disabled="paymentInfo.processInstanceId"
              :value="item.bankAccount"
            >
              <el-input v-model="item.bankAccount" class="bigInput" />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('supplier.accountName')"
            :prop="`bankInfoCreateReqVO[${index}].accountName`"
            :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'change'}"
            style="width: 100%;"
          >
            <show-or-edit
              :disabled="paymentInfo.processInstanceId"
              :value="item.accountName"
            >
              <el-input v-model="item.accountName" class="bigInput" />
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.swiftCode')" style="width: 100%;">
            <show-or-edit
              :disabled="paymentInfo.processInstanceId"
              :value="item.swiftCode"
            >
              <el-input v-model="item.swiftCode" class="bigInput" />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            :label="$t('supplier.bankNo')"

            :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'change'}"
            style="width: 100%;"
            :prop="`bankInfoCreateReqVO[${index}].bankNo`"
          >
            <show-or-edit
              :disabled="paymentInfo.processInstanceId"
              :value="item.bankNo"
            >
              <el-input v-model="item.bankNo" class="bigInput" />
            </show-or-edit>
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('supplier.bankInformationWithOfficialSeal')">
            <div style="display: flex;justify-content: space-between">

              <el-upload
                class="upload-demo"
                :disabled="paymentInfo.processInstanceId"
                :action="uploadUrl"
                :headers="getBaseHeader()"
                :before-upload="beforeUpload"
                :on-remove="(file,fileList)=>onRemove(file,fileList,item)"
                :on-preview="onPreview"
                :on-success="(response, file, fileList)=>onSuccess(response, file, item)"
                multiple
                :limit="5"
                :show-file-list="false"
                :file-list="item.fileRelList"
              >
                <el-button
                  v-if="!viewOnly"
                  class="uploadBtn"
                  size="small"
                  plain
                  icon="el-icon-plus"
                  type="primary"
                />
              </el-upload>
              <div style="margin-left: 15px">

                {{ $t('scar.viewAttachments') }}
                <el-button
                  class="uploadBtn"
                  size="small"
                  style="padding: 5px 9px"
                  :disabled="item.fileRelList?.length===0"
                  plain
                  :type="item.fileRelList?.length?'primary':''"
                  @click="item.showFile=true"
                >
                  {{ item.fileRelList?.length }}
                </el-button>

                <el-dialog
                  v-if="item.showFile"
                  :visible.sync="item.showFile"
                  :title="$t('scar.viewAttachments')"
                  width="400px"
                >
                  <el-upload
                    class="upload-show"
                    :disabled="paymentInfo.processInstanceId"
                    :action="uploadUrl"
                    :headers="getBaseHeader()"
                    :before-upload="beforeUpload"
                    :on-remove="(file,fileList)=>onRemove(file,fileList,item)"
                    :on-preview="onPreview"
                    :on-success="(response, file, fileList)=>onSuccess(response, file, item,)"
                    multiple
                    :limit="5"
                    :file-list="item.fileRelList"
                  />
                  <div slot="footer">
                    <el-button type="primary" @click="item.showFile=false">{{ $t('order.determine') }}</el-button>
                  </div>

                </el-dialog>
              </div>
            </div>

          </el-form-item>

          <div v-if="!viewOnly">
            <i
              class="el-icon-circle-plus"
              style="margin-top:8px;margin-left:10px;font-size: 18px;cursor: pointer"
              @click="paymentInfo.bankInfoCreateReqVO.push({
                accountName: '',
                bankAccount: '',
                bankName: '',
                nccSubbranchId: '',
                bankNo: '',
                currency: '',
                invoiceAddress: '',
                subbranchName: '',
                fileRelList: [],
                swiftCode: '',
                showFile: false
              })"
            />
            <i
              v-if="paymentInfo.bankInfoCreateReqVO.length>1"
              class="el-icon-remove"
              style="margin-top:8px;margin-left:10px;font-size: 18px;cursor: pointer"
              @click="paymentInfo.bankInfoCreateReqVO.length===1?
                paymentInfo.bankInfoCreateReqVO= [
                  {
                    accountName: '',
                    bankAccount: '',
                    bankName: '',
                    nccSubbranchId: '',
                    bankNo: '',
                    currency: '',
                    invoiceAddress: '',
                    subbranchName: '',
                    fileRelList: [],
                    swiftCode: '',
                    showFile: false,
                  }
                ]
                :paymentInfo.bankInfoCreateReqVO.splice(index,1)"
            />
          </div>

        </div>
        <el-form-item :label="$t('supplier.modifyRemarks')">
          <show-or-edit
            :disabled="paymentInfo.processInstanceId"
            :value="paymentInfo.remark"
          >
            <el-input v-model="paymentInfo.remark" class="bigInput" type="textarea" style="margin-bottom: 40px" />
          </show-or-edit>
        </el-form-item>
      </el-card>

    </el-form>

    <bpm-process-instance
      v-if="paymentInfo.processInstanceId&&!viewOnly"
      style="flex: auto"
      :process-instance-id="paymentInfo.processInstanceId"
      @rejectAuditEvent="reject"
      @submitAuditEvent="submit"
      @approvalAuditEvent="submit"
      @closeAuditEvent="closeAuditEvent"
    />
    <div
      v-if="!paymentInfo.processInstanceId&&!viewOnly"
      class="fixedBottom"
    >
      <el-button @click="$tab.closePage()">{{ $t('sp.cancel') }}</el-button>
      <el-button type="primary" @click="submit()">{{ $t('common.submit') }}</el-button>
    </div>
  </div>
</template>

<script>
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { detailBankInfo, rejectBankInfo, submitBankInfo } from '@/api/supplier/info'
import { getBaseHeader } from '@/utils/request'
import $modal from '@/plugins/modal'
import BpmProcessInstance from '@/components/BpmProcessInstance/index.vue'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { cancelRecord } from '@/api/supplier/infoChangeRecord'

export default {
  name: 'Bank/:id',
  components: { ShowOrEdit, BpmProcessInstance },
  data() {
    return {
      region: getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(item => item.type === 'country'),
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      fileList: [],
      supplierNameAndNo: '',
      paymentInfo: {
        supplierName: '',
        creditCode: '',
        accountDay: '', // 金信诺描述调整：账期=>付款条件
        bankInfoCreateReqVO: [{
          accountName: '',
          bankAccount: '',
          bankName: '',
          nccSubbranchId: '',
          bankNo: '',
          currency: '',
          invoiceAddress: '',
          subbranchName: '',
          swiftCode: '',
          openBankSources: [],
          bankSources: [],
          fileRelList: [],
          showFile: false,
          optionsObj: {}
        }],
        countryRegion: '',
        deliveryType: '',
        feeBearer: '',
        invoiceType: '',
        paymentType: '',
        taxRate: '',
        paymentInfoEdit: false
      },
      paymentInfoRule: {
        accountDay: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }]
      },
      supplierId: null,
      viewOnly: false
    }
  },
  mounted() {
    this.supplierId = this.$route.query.supplierId
    this.viewOnly = this.$route.query.viewOnly === 'true'
    this.init()
  },
  methods: {
    getBaseHeader,
    // 社会统一信用代码为中国时添加必填验证
    getCreditCodeRule() {
      this.getConfigKey('system.china').then(response => {
        if (response.data) {
          // 配置的是国家的编码，转换为id集合
          const chinaIds = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(item => response.data.includes(item.code))?.map(item => (item.id))
          if (chinaIds && chinaIds.includes(this.paymentInfo.countryRegion)) {
            this.$set(this.paymentInfoRule, 'creditCode',
              [{ required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }]
            )
          } else {
            this.$delete(this.paymentInfoRule, 'creditCode')
          }
        }
        this.$nextTick(() => {
          this.$refs.payment.clearValidate()
        })
      })
    },
    onRemove(file, fileList, item) {
      item.fileRelList.splice(item.fileRelList.find(a => a.id === file.id), 1)
    },
    onSuccess(response, file, item) {
      item.fileRelList.push({
        name: file.name,
        url: response.data.url,
        fileId: response.data.id
      })
    },
    onPreview(file) {
      window.open(file.url)
    },
    beforeUpload(file) {
      if (file.size > 5242880) {
        $modal.msgError(this.$t('common.uploadFileSizeCannotExceedm'))
        return false
      }
      if (this.fileList.length > 5) {
        $modal.msgError(this.$t('order.onlyUpToAttachmentsAreSupported'))
        return false
      }
    },
    init() {
      detailBankInfo({
        supplierId: this.supplierId,
        recordId: this.$route.params.id === '0' ? null : this.$route.params.id
      }).then(async res => {
        res.data.bankInfoCreateReqVO.forEach(a => {
          a.showFile = false
          if (!a.fileRelList) {
            a.fileRelList = []
          }
        })
        this.paymentInfo = res.data
        await this.$nextTick()
        this.$refs.payment.clearValidate()
        this.getCreditCodeRule()

        if (this.paymentInfo.supplierCode){
          this.supplierNameAndNo = this.paymentInfo.supplierName + ' - ' + this.paymentInfo.supplierCode
        }else {
          this.supplierNameAndNo = this.paymentInfo.supplierName
        }
      })
    },
    changeRegion(val, item) {
      this.getCreditCodeRule()
      item.province = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(a => a.parentId === val)
      item.provinceCity = ''
    },
    submit(taskId, processInstanceId, processInstanceReason) {
      this.$refs.payment.validate((valid) => {
        if (valid) {
          submitBankInfo({ ...this.paymentInfo, supplierId: this.supplierId, taskId, processInstanceId, processInstanceReason }).then(res => {
            this.$message.success(this.$t('order.operationSucceeded'))
            this.$tab.closePage()
          })
        }
      })
    },
    closeAuditEvent() {
      cancelRecord(this.$route.params.id).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.$tab.closeOpenPage('/supplier/info-change-record')
      })
    },
    reject(taskId, processInstanceId, processInstanceReason) {
      rejectBankInfo({ ...this.paymentInfo, taskId, processInstanceId, processInstanceReason }).then(res => {
        this.$tab.closePage()
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    }
  }
}
</script>

<style scoped lang="scss">
.shadowPadding{
  padding: 5px 0 ;margin: 15px
}
.smallSelect {
  width: 188px;
}
.bigInput {
  width: 591px
}
.shadow {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}
.fixedBottom {
  position: fixed;
  width: calc(100% - 231px);
  bottom: 20px;
  display: flex;
  justify-content: center;
  margin-top: 40px;
  right: 30px;
}
</style>
