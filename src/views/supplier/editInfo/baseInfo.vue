<template>
  <div class="supplier">
    <div v-if="supplierShow" style="background-color: white">
      <div>
        <show-or-edit
          disabled="true"
          :value="supplierNameAndNo"
          style="font-size: 16px; font-weight: 700; color: rgba(73, 150, 184, 0.99); padding-left: 10px;"
        >
          <el-input v-model="supplierNameAndNo" />
        </show-or-edit>
      </div>

      <div id="basic" class="form-title">1.1 <span style="margin-left:5px">{{
        $t('supplier.companyInformation')
      }}</span></div>
      <div style="padding: 10px 126px">
        <el-form
          ref="baseInfo"
          :model="supplierInfo"
          :rules="supplierInfoRule"
          class="baseInfo"
          inline
          label-width="155px"
        >
          <el-form-item
            ref="name"
            :label="$t('supplier.supplierName')"
            prop="name"
          >
            <show-or-edit
                :disabled="!editMode"
              :value="supplierInfo.name"
            >
              <el-input v-model="supplierInfo.name" />
            </show-or-edit>
            <el-button
              v-if="editMode&&$store.getters.creditModule"
              size="mini"
              type="primary"
              @click="getTYCData()"
            >{{
              $t('supplier.obtainTianyancha')
            }}
            </el-button>
          </el-form-item>
          <el-form-item ref="nameEn" :label="$t('supplier.supplierNameEn')" prop="nameEn">
            <show-or-edit :value="supplierInfo.nameEn"
                          :disabled="!editMode"
            >
              <el-input v-model="supplierInfo.nameEn" />
            </show-or-edit>
          </el-form-item>
          <el-form-item ref="nameShort" :label="$t('supplier.shortNameOfSupplier')" prop="nameShort">
            <show-or-edit :value="supplierInfo.nameShort"
                          :disabled="!editMode"
            >
              <el-input v-model="supplierInfo.nameShort" />
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.supplierCode')">
            <show-or-edit :value="supplierInfo.code"
                          :disabled="!editMode"
            >
              <el-input v-model="supplierInfo.code" />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            ref="registeredAddress"
            :label="$t('supplier.registeredAddressOfTheCompany')"
            prop="registeredAddress"
          >
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.registeredAddress"
            >
              <el-input v-model="supplierInfo.registeredAddress" />
            </show-or-edit>
          </el-form-item>
          <el-form-item
              ref="registeredAddressSub"
              :label="$t('supplier.registeredAddressOfTheCompany2')"
              prop="registeredAddressSub"
          >
            <show-or-edit
                :disabled="!editMode"
                :value="supplierInfo.registeredAddressSub"
            >
              <el-input v-model="supplierInfo.registeredAddressSub" />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            ref="countryRegion"
            :label="$t('supplier.countryregion')"
            prop="countryRegion"
          >
            <show-or-edit
              :dict="DICT_TYPE.COMMON_COUNTRY"
              :disabled="true"
              :value="supplierInfo.countryRegion"
            >
              <el-select
                v-model="supplierInfo.countryRegion"
                class="smallSelect"
                clearable
                filterable
                remote
                @change="(val)=>{changeRegion(val)}"
              >
                <el-option v-for="re in region" :key="re.keyId" :label="re.name" :value="re.id" />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            ref="creditCode"
            :label="$t('supplier.unifiedSocialCreditCode')"
            prop="creditCode"
          >
            <show-or-edit
              disabled="true"
              :value="supplierInfo.creditCode"
            >
              <el-input v-model="supplierInfo.creditCode" />
            </show-or-edit>
          </el-form-item>

          <el-form-item
            ref="provinceCity"
            :label="$t('supplier.provinceCity')"
            prop="provinceCity"
          >
            <show-or-edit
              :dict="DICT_TYPE.COMMON_COUNTRY"
              :disabled="!editMode"
              :value="supplierInfo.provinceCity"
            >
              <el-select v-model="supplierInfo.provinceCity" class="smallSelect" filterable>
                <el-option v-for="re in province" :key="re.keyId" :label="re.name" :value="re.id" />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            ref="registeredCapital"
            :label="$t('supplier.registeredCapital')"
            prop="registeredCapital"
          >
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.registeredCapital"
            >
              <vxe-input v-model="supplierInfo.registeredCapital" class="smallSelect" max="99999999999.99" min="0" type="number" />
            </show-or-edit>
          </el-form-item>

          <el-form-item
            ref="street"
            :label="$t('supplier.street')"
            prop="street"
          >
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.street"
            >
              <el-input v-model="supplierInfo.street" style="width: 129px" />
              <el-button v-if="editMode" size="mini" type="primary" @click="cpStreet()">{{
                $t('supplier.ditto')
              }}
              </el-button>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            ref="registeredCapitalCurrency"
            :label="$t('supplier.registeredCapitalCurrency')"
            prop="registeredCapitalCurrency"
          >
            <show-or-edit
              :dict="DICT_TYPE.COMMON_CURRENCY"
              :disabled="!editMode"
              :value="supplierInfo.registeredCapitalCurrency"
            >

              <el-select
                v-model="supplierInfo.registeredCapitalCurrency"
                class="smallSelect"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>

          <el-form-item
            ref="companyCategory"
            :label="$t('supplier.companyType')"
            prop="companyCategory"
          >
            <show-or-edit
              :dict="DICT_TYPE.SUPPLIER_TYPE"
              :disabled="!editMode"
              :value="supplierInfo.companyCategory"
            >

              <el-select
                v-model="supplierInfo.companyCategory"
                :placeholder="$t('supplier.pleaseSelectStatus')"
                class="smallSelect"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>

          <el-form-item :label="$t('supplier.companyWebsite')" style="width: 100%">
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.webSite"
            >
              <el-input v-model="supplierInfo.webSite" class="bigBaseInput" />
            </show-or-edit>
          </el-form-item>

          <el-form-item :label="$t('supplier.zipCode')">
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.zipCode"
            >

              <el-input v-model="supplierInfo.zipCode" />

            </show-or-edit>
          </el-form-item>
          <div>
            <el-form-item ref="establishedTime" :label="$t('supplier.establishedTime')" prop="establishedTime">
              <show-or-edit
                :disabled="!editMode"
                :value="supplierInfo.establishedTime"
                type="Date"
              >
                <el-date-picker
                  v-model="supplierInfo.establishedTime"
                  :placeholder="$t('common.pleaseSelectADate')"
                  class="smallSelect"
                  type="date"
                />
              </show-or-edit>
            </el-form-item>
            <span
              style="float: right;color: #4185A3;cursor:pointer;"
              @click="showDetail= !showDetail"
            >{{ $t('system.more') }}
              <i
                :style="showDetail? '':{transform: 'rotate(180deg)'}"
                class="el-icon-arrow-up"
                style="font-size: 18px;float: right;transition: transform .3s;
                margin-left: 7px;
          font-weight: bold;"
              />
            </span>
          </div>
          <div
            v-for="(item,index) in supplierInfo.factoryAddressCreateReqVO"
            v-show="showDetail"
            :key="index"
            class="shadow"
            style="padding: 10px 0 5px 0;margin: 10px 0 "
          >
            <div>
              <el-form-item :label="$t('supplier.factoryAddress')">
                <div class="verticalMiddle">
                  <el-button v-if="editMode" size="mini" type="primary" @click="cpAddress(item)">{{
                    $t('supplier.ditto')
                  }}
                  </el-button>
                  <i
                    v-if="editMode"
                    class="el-icon-circle-plus"
                    style="margin-left:10px;font-size: 18px;cursor: pointer"
                    @click="supplierInfo.factoryAddressCreateReqVO.push({
                      countryRegion: '',
                      street: '',
                      provinceCity: '',
                      zipCode: '',
                      province:[]

                    })"
                  />
                  <i
                    v-if="editMode"
                    class="el-icon-remove"
                    style="margin-left:10px;font-size: 18px;cursor: pointer"
                    @click="supplierInfo.factoryAddressCreateReqVO.length===1?
                      supplierInfo.factoryAddressCreateReqVO= [
                        {
                          countryRegion: '',
                          street: '',
                          provinceCity: '',
                          zipCode: '',
                          province: []
                        }
                      ]
                      :supplierInfo.factoryAddressCreateReqVO.splice(index,1)"
                  />
                </div>
              </el-form-item>
            </div>
            <el-form-item :label="$t('supplier.countryregion')">
              <show-or-edit
                :dict="DICT_TYPE.COMMON_COUNTRY"
                :disabled="!editMode"
                :value="item.countryRegion"
              >
                <el-select
                  ref="mainFactory"
                  v-model="item.countryRegion"
                  class="smallSelect"
                  clearable
                  filterable
                  remote
                  @change="(val)=>{changeRegion(val,item)}"
                >
                  <el-option v-for="re in region" :key="re.keyId" :label="re.name" :value="re.id" />
                </el-select>
              </show-or-edit>

            </el-form-item>
            <el-form-item :label="$t('supplier.street')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.street"
              >
                <el-input v-model="item.street" />

              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.provinceCity')">
              <show-or-edit
                :dict="DICT_TYPE.COMMON_COUNTRY"
                :disabled="!editMode"
                :value="item.provinceCity"
              >
                <el-select v-model="item.provinceCity" :disabled="!editMode" class="smallSelect" filterable>
                  <el-option v-for="re in item.province" :key="re.keyId" :label="re.name" :value="re.id" />
                </el-select>
              </show-or-edit>

            </el-form-item>
            <el-form-item :label="$t('supplier.zipCode')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.zipCode"
              >
                <el-input v-model="item.zipCode" :disabled="!editMode" />

              </show-or-edit>
            </el-form-item>
          </div>

        </el-form>
      </div>

      <div class="form-title">1.2 <span style="margin-left:5px">{{ $t('supplier.relatedCompanyInformation') }}</span>
        <i
          :style="showDetail1? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail1= !showDetail1"
        />
      </div>
      <div
        v-show="showDetail1"

        v-disable-all="!editMode"
        class="form-main"
        style="padding: 10px 126px"
      >
        <el-form class="associatedCompany" inline label-width="155px">
          <div
            v-for="(item,index) in supplierInfo.associatedCompanyCreateReqVO"
            :key="index"
            class="shadow"
            style="padding: 10px 0 5px 0;margin: 10px 0 "
          >
            <div>
              <el-form-item :label="$t('system.companyName')" style="width: 100%">
                <show-or-edit
                  :disabled="!editMode"
                  :value="item.name"
                >
                  <div class="verticalMiddle">

                    <el-input v-model="item.name" />
                    <i
                      v-if="editMode"

                      class="el-icon-circle-plus"
                      style="margin-left:10px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.associatedCompanyCreateReqVO.push({
                        relationship: '',
                        countryRegion: '',
                        street: '',
                        provinceCity: '',
                        zipCode: '',
                        province: []
                      })"
                    />
                    <i
                      v-if="editMode"

                      class="el-icon-remove"
                      style="margin-left:10px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.associatedCompanyCreateReqVO.length===1?
                        supplierInfo.associatedCompanyCreateReqVO= [
                          {
                            relationship: '',
                            countryRegion: '',
                            street: '',
                            provinceCity: '',
                            zipCode: '',
                            province: []
                          }
                        ]
                        :supplierInfo.associatedCompanyCreateReqVO.splice(index,1)"
                    />

                  </div>
                </show-or-edit>

              </el-form-item>
            </div>

            <el-form-item :label="$t('supplier.countryregion')">
              <show-or-edit
                :dict="DICT_TYPE.COMMON_COUNTRY"
                :disabled="!editMode"
                :value="item.countryRegion"
              >
                <el-select
                  v-model="item.countryRegion"
                  :disabled="!editMode"
                  class="smallSelect"
                  clearable
                  filterable
                  remote
                  @change="(val)=>{changeRegion(val,item)}"
                >
                  <el-option v-for="re in region" :key="re.keyId" :label="re.name" :value="re.id" />
                </el-select>
              </show-or-edit>

            </el-form-item>
            <el-form-item :label="$t('supplier.relationshipWithTheMainCompany')">
              <show-or-edit
                :dict="DICT_TYPE.SUPPLIER_WITH_SUPPLIER_REL"
                :disabled="!editMode"
                :value="item.relationship"
              >
                <el-select
                  v-model="item.relationship"
                  :disabled="!editMode"
                  :placeholder="$t('supplier.pleaseSelectStatus')"
                  class="smallSelect"
                  clearable
                >
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_WITH_SUPPLIER_REL)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>

              </show-or-edit>
            </el-form-item>

            <el-form-item :label="$t('supplier.provincesAndCities')">
              <show-or-edit
                :dict="DICT_TYPE.COMMON_COUNTRY"
                :disabled="!editMode"
                :value="item.provinceCity"
              >
                <el-select v-model="item.provinceCity" :disabled="!editMode" class="smallSelect">
                  <el-option v-for="re in item.province" :key="re.keyId" :label="re.name" :value="re.id" />
                </el-select>
              </show-or-edit>

            </el-form-item>
            <el-form-item :label="$t('supplier.street')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.zipCode"
              >
                <el-input v-model="item.street" :disabled="!editMode" />

              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.zipCode')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.zipCode"
              >
                <el-input v-model="item.zipCode" :disabled="!editMode" />
              </show-or-edit>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <div class="form-title">1.3 <span style="margin-left:5px">{{ $t('supplier.personnelInformation') }}</span></div>
      <div v-disable-all="!editMode" class="form-main">
        <el-form
          ref="personInfo"
          :model="supplierInfo"
          :rules="supplierInfoRule"
          class="shadow"
          inline
          label-width="185px"
          style="padding: 10px 0 5px 0;margin: 15px "
        >

          <el-form-item
            ref="legalPerson"
            :label="$t('supplier.legalRepresentative')"
            prop="legalPerson"
          >
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.legalPerson"
            >
              <el-input v-model="supplierInfo.legalPerson" />
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.mailbox')" label-width="50px" prop="notRequiredEmail">
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.email"
            >
              <el-input v-model="supplierInfo.email" />
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.position')">
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.position"
            >
              <el-input v-model="supplierInfo.position" />
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.telephone')" label-width="50px">
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.phone"
            >
              <el-input v-model="supplierInfo.phone" />
            </show-or-edit>
          </el-form-item>

        </el-form>
        <div class="shadow shadowPadding">
          <div class="required tableTitle">
            {{ $t('supplier.primaryContact') }}
          </div>
          <div class="tablePadding">
            <el-table
              :data="supplierInfo.contactCreateReqVO"
            >
              <el-table-column :label="$t('supplier.fullName')" prop="name">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.name"
                  >
                    <el-input ref="mainContact" v-model="scope.row.name" />
                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.position')" prop="position">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.position"
                  >
                    <el-input v-model="scope.row.position" />
                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.contactNumber')" prop="phone">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.phone"
                  >
                    <el-input v-model="scope.row.phone" />
                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column min-width="110" prop="email">
                <template #header>
                  <div class="required">{{ $t('supplier.email') }}</div>
                </template>
                <template slot-scope="scope">
                  <el-form :ref="`contactItem${scope.$index}`" :model="scope.row">
                    <el-form-item
                      :rules=" { required: true, type: 'email', message: $t('supplier.pleaseEnterTheCorrectEmailAddress'), trigger: ['blur'] } "
                      prop="email"
                      style="width: 100%;margin: auto !important;"
                    >
                      <show-or-edit
                        :disabled="!editMode"
                        :value="scope.row.email"
                      >
                        <el-input v-model="scope.row.email" />
                      </show-or-edit>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.contactDivision')" min-width="160" prop="division">
                <template #header>
                  <div class="required">{{ $t('supplier.contactDivision') }}</div>
                </template>
                <template slot-scope="scope">
                  <el-form :ref="`contactItemDivision${scope.$index}`" :model="scope.row">
                    <el-form-item
                      :rules=" { required: true, message: $t('common.pleaseEnter'), trigger: [ 'change'] } "
                      prop="division"
                      style="width: 100%;margin: auto !important;"
                    >
                      <show-or-edit
                        :dict="DICT_TYPE.SUPPLIER_CONTACT_DIVISION"
                        :disabled="!editMode"
                        :value="scope.row.division"
                      >
                        <el-select v-model="scope.row.division" clearable multiple :placeholder="$t('supplier.multipleOptionsAvailable')" style="width:100%">
                          <el-option
                            v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CONTACT_DIVISION)"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>
                      </show-or-edit>
                    </el-form-item>
                  </el-form>

                </template>
              </el-table-column>
              <el-table-column :v-if="editMode" prop="op" width="80">
                <template slot-scope="scope">
                  <div style="text-align: right;margin-top: 5px">
                    <i
                      v-if="editMode"
                      class="el-icon-circle-plus"
                      style="margin-left:2px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.contactCreateReqVO.push({name: '',position: '',phone: '',email: '',division: [],businessDictRelStr: ''})"
                    />
                    <i
                      v-if="editMode"
                      class="el-icon-remove"
                      style="margin-left:5px;font-size: 18px;cursor: pointer"
                      @click="
                        supplierInfo.contactCreateReqVO.length===1?
                          supplierInfo.contactCreateReqVO=[{name: '',position: '',phone: '',email: '',division: [],businessDictRelStr: ''}]:supplierInfo.contactCreateReqVO.splice(scope.$index,1)"
                    />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="shadow shadowPadding">
          <div class="tableTitle">{{ $t('supplier.shareholderInformation') }}
            <span style="float: right;color: #4185A3;cursor:pointer;font-weight: 400;margin-right: 50px" @click="showDetail0= !showDetail0">
              {{ $t('system.more') }}
              <i
                :style="showDetail0? '':{transform: 'rotate(180deg)'}"
                class="el-icon-arrow-up"
                style="font-size: 18px;float: right;transition: transform .3s;margin-left: 7px;font-weight: bold;"
              />
            </span>
          </div>
          <div v-show="showDetail0" class="tablePadding">
            <el-table :data="supplierInfo.shareholdersCreateReqVO">
              <el-table-column :label="$t('supplier.nameDepartment') " prop="name">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.name"
                  >
                    <el-input v-model="scope.row.name" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.shareProportion') " prop="stake">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.stake"
                  >
                    <el-input-number
                      v-model="scope.row.stake"
                      :max="100"
                      :min="0"
                      class="smallInput"
                      style="margin-right: 5px"
                    />
                  </show-or-edit>
                  %
                </template>
              </el-table-column>
              <el-table-column :v-if="editMode" prop="op" width="80">
                <div style="text-align: right;margin-top: 5px">
                  <i
                    v-if="editMode"
                    class="el-icon-circle-plus"
                    style="margin-left:2px;font-size: 18px;cursor: pointer"
                    @click="supplierInfo.shareholdersCreateReqVO.push({stake: '',name: '',})"
                  />
                  <i
                    v-if="editMode"
                    class="el-icon-remove"
                    style="margin-left:5px;font-size: 18px;cursor: pointer"
                    @click="supplierInfo.shareholdersCreateReqVO.length===1?supplierInfo.shareholdersCreateReqVO=[{name: '',stake: ''}]
                      :supplierInfo.shareholdersCreateReqVO.splice(index,1)"
                  />
                </div>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="shadow shadowPadding">
          <div class="tableTitle">{{ $t('supplier.staffInfo') }}
            <span style="float: right;color: #4185A3;cursor:pointer;font-weight: 400;margin-right: 50px" @click="showDetail11= !showDetail11">
              {{ $t('system.more') }}
              <i
                :style="showDetail11? '':{transform: 'rotate(180deg)'}"
                class="el-icon-arrow-up"
                style="font-size: 18px;float: right;transition: transform .3s;margin-left: 7px;font-weight: bold;"
              />
            </span>
          </div>
          <div v-show="showDetail11" style="margin:10px 0;padding-left: 70px;display: flex;justify-content: space-around;font-size: 14px;">
            <div>{{ $t('supplier.numberOfEmployees') }}</div>
            <div>{{ $t('supplier.qualificationCertificateOfSpecialTypeOfWork') }}</div>
            <div>{{ $t('supplier.rDPersonnelQualifications') }}</div>
          </div>
          <el-form v-show="showDetail11" class="supplierStaff" inline label-width="139px">
            <el-row>
              <el-col :span="8">
                <el-form-item
                  v-for="dict in supplierStaff"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                  class="centerRow"
                  style="font-weight: normal;width: 100%"
                >

                  <show-or-edit
                    :disabled="!editMode"
                    :value="dict.inputValue"
                  >
                    <el-input-number v-model="dict.inputValue" :min="0" class="smallInput" />
                  </show-or-edit>

                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  v-for="dict in supplierCertificate"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                  class="centerRow"
                >
                  <show-or-edit
                    :disabled="!editMode"
                    :value="dict.inputValue"
                  >
                    <el-input-number v-model="dict.inputValue" :min="0" class="smallInput" />

                  </show-or-edit>

                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  v-for="dict in supplierDev "
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                  class="centerRow"
                >
                  <show-or-edit
                    :disabled="!editMode"
                    :value="dict.inputValue"
                  >
                    <el-input-number
                      v-model="dict.inputValue"
                      :max="100"
                      :min="0"
                      style="width: 145px"
                    />
                  </show-or-edit>
                  %
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>

      <div id="business" class="form-title">2.1 <span style="margin-left:5px">{{
        $t('supplier.businessInformation')
      }}</span>
        <i
          :style="showDetail2? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail2= !showDetail2"
        /></div>
      <div
        v-show="showDetail2"

        v-disable-all="!editMode"
        class="form-main"
      >
        <el-form class="shadow" inline label-width="155px" style="padding: 0 100px">
          <el-form-item :label="$t('supplier.areaCovered')">

            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.coversArea"
            >

              <el-input-number v-model="supplierInfo.coversArea" :min="0" class="smallInput" />

            </show-or-edit>

            {{ $t('supplier.squareMeter') }}
          </el-form-item>
          <el-form-item :label="$t('supplier.propertyRightOfPlant')">
            <show-or-edit
              :dict="DICT_TYPE.SUPPLIER_BUILDING_PROPERTY_RIGHT"
              :disabled="!editMode"
              :value="supplierInfo.buildProperty"
            >
              <el-select
                v-model="supplierInfo.buildProperty"

                :placeholder="$t('supplier.pleaseSelectStatus')"
                class="smallSelect"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_BUILDING_PROPERTY_RIGHT)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.productionArea')">

            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.productionArea"
            >

              <el-input-number v-model="supplierInfo.productionArea" :min="0" class="smallInput" />

            </show-or-edit>
            {{ $t('supplier.squareMeter') }}
          </el-form-item>
          <el-form-item :label="$t('supplier.natureOfTheCompany')">
            <show-or-edit
              :dict="DICT_TYPE.SUPPLIER_COMPANY_NATURE"
              :disabled="!editMode"
              :value="supplierInfo.companyNature"
            >
              <el-select
                v-model="supplierInfo.companyNature"
                :disabled="!editMode"
                :placeholder="$t('supplier.pleaseSelectStatus')"
                class="smallSelect"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_COMPANY_NATURE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>

          </el-form-item>
          <el-form-item :label="$t('supplier.importAndExportQualification')">
            <show-or-edit
              :dict="DICT_TYPE.COMMON_Y_N"
              :disabled="!editMode"
              :value="supplierInfo.exportQualification"
            >
              <el-select
                v-model="supplierInfo.exportQualification"
                :disabled="!editMode"
                :placeholder="$t('supplier.pleaseSelectStatus')"
                class="smallSelect"
                clearable
                style="width:161px"
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_Y_N)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
        </el-form>
      </div>

      <div class="form-title">2.2 <span style="margin-left:5px">{{ $t('supplier.salesInformation') }}</span>
        <i
          :style="showDetail3? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail3= !showDetail3"
        />
      </div>
      <div
        v-show="showDetail3"
        v-disable-all="!editMode"
        class="form-main"
      >
        <div
          class="shadow shadowPadding"
        >
          <div class="tableTitle"> {{ $t('supplier.turnoverinRecentYears') }}</div>
          <div class="tablePadding">
            <el-table :data="supplierInfo.turnoverCreateReqVO">
              <el-table-column :label="$t('supplier.year')" prop="year" />
              <el-table-column :label="$t('supplier.money')" prop="turnover">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.turnover"
                    class="smallInput"
                  >
                    <vxe-input
                      v-model.number="scope.row.turnover"
                      :digits="2"
                      :placeholder="$t('supplier.enterNumber')"
                      class="smallInput"
                      max="999999999.99"
                      min="0"
                      type="float"
                    />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('sp.unit')" prop="unit">
                <template slot-scope="scope">
                  <show-or-edit
                    :dict="DICT_TYPE.SUPPLIER_CURRENCY_UNIT"
                    :disabled="!editMode"
                    :value="scope.row.unit"
                    class="smallInput"
                  >
                    <el-select
                      v-model="scope.row.unit"
                      :disabled="!editMode"
                      class="smallInput"
                      filterable
                      style="margin: 0 10px"
                    >
                      <el-option
                        v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CURRENCY_UNIT)"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('system.currency')" prop="currency">
                <template slot-scope="scope">
                  <show-or-edit
                    :dict="DICT_TYPE.COMMON_CURRENCY"
                    :disabled="!editMode"
                    :value="scope.row.currency"
                    class="smallInput"
                  >
                    <el-select v-model="scope.row.currency" :disabled="!editMode" filterable>
                      <el-option
                        v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                        :key="dict.value"
                        :label="dict.name"
                        :value="dict.id"
                      />
                    </el-select>
                  </show-or-edit>
                </template>
              </el-table-column>
            </el-table>
          </div>

        </div>
        <div
          class="shadow shadowPadding"
        >
          <div class="tableTitle">{{ $t('supplier.mainCustomers') }}</div>
          <div class="tablePadding">
            <el-table :data="supplierInfo.customerCreateReqVO">
              <el-table-column :label="$t('supplier.customerName')" prop="name">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.name"
                    class="customItem"
                  >
                    <el-input v-model="scope.row.name" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.endCustomer')" prop="terminalCustomer">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.terminalCustomer"
                    class="customItem"
                  >
                    <el-input v-model="scope.row.terminalCustomer" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.industry')" prop="industry">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.industry"
                    class="customItem"
                  >
                    <el-input v-model="scope.row.industry" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.proportion')" prop="proportion">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.proportion"
                    class="customItem"
                  >
                    <el-input-number
                      v-model="scope.row.proportion"
                      :max="100"
                      :min="0"
                      style="margin: 0 10px;width: 80%"
                    />

                  </show-or-edit>
                  %
                </template>
              </el-table-column>
              <el-table-column prop="op" width="80">
                <template slot-scope="scope">
                  <div style="flex: 0 0 5%;display:flex;width: 60px;text-align: right">
                    <i
                      v-if="editMode"

                      class="el-icon-circle-plus"
                      style="margin-left:2px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.customerCreateReqVO.push({
                        terminalCustomer: '',
                        name: '',
                        industry: '',
                        proportion: '',
                      })"
                    />
                    <i
                      v-if="editMode"
                      class="el-icon-remove"
                      style="margin-left:5px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.customerCreateReqVO.length===1?
                        supplierInfo.customerCreateReqVO= [
                          {
                            terminalCustomer: '',
                            name: '',
                            industry: '',
                            proportion: '',
                          }
                        ]
                        :supplierInfo.customerCreateReqVO.splice(index,1)"
                    />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div
          class="shadow shadowPadding"
        >
          <div class="tableTitle">{{ $t('supplier.esicCustomers') }}</div>
          <div class="tablePadding">
            <el-table :data="supplierInfo.esicCustomerCreateReqVO">
              <el-table-column :label="$t('supplier.customerName')" prop="name">
                <template slot-scope="scope">
                  <show-or-edit
                    :dict="DICT_TYPE.INCAP_CUSTOMER"
                    :disabled="!editMode"
                    :value="scope.row.name"
                    class="smallInput"
                  >
                    <el-select v-model="scope.row.name" :disabled="!editMode" filterable>
                      <el-option
                        v-for="dict in getDictDatas(DICT_TYPE.INCAP_CUSTOMER)"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.customerIntroductionTime')" prop="involveTime">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.involveTime"
                    type="Date"
                  >
                    <el-date-picker
                      v-model="scope.row.involveTime"
                      :placeholder="$t('common.pleaseSelectADate')"
                      class="smallSelect"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                  </show-or-edit>
                </template>
              </el-table-column>

              <el-table-column prop="op" width="80">
                <template slot-scope="scope">
                  <div style="flex: 0 0 5%;display:flex;width: 60px;text-align: right">
                    <i
                      v-if="editMode"

                      class="el-icon-circle-plus"
                      style="margin-left:2px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.esicCustomerCreateReqVO.push({
                        name: '',
                        involveTime: '',
                      })"
                    />
                    <i
                      v-if="editMode"

                      class="el-icon-remove"
                      style="margin-left:5px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.esicCustomerCreateReqVO.length===1?
                        supplierInfo.esicCustomerCreateReqVO= [
                          {
                            name: '',
                            involveTime: '',
                          }
                        ]
                        :supplierInfo.esicCustomerCreateReqVO.splice(index,1)"
                    />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div
          class="shadow shadowPadding"
        >
          <div class="tableTitle">{{ $t('supplier.salesArea') }}</div>
          <el-form inline label-width="155px" style="padding: 10px 65px;">
            <el-form-item
              v-for="dict in supplierSaleArea"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
              <show-or-edit
                :disabled="!editMode"
                :value="dict.inputValue"
              >
                <el-input-number v-model="dict.inputValue" :max="100" :min="0" class="smallInput" />

              </show-or-edit>
              <span style="margin-left: 5px">
                %
              </span>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div id="category" class="form-title">3.1 <span style="margin-left:5px">{{
        $t('supplier.productInformation')
      }}</span>
        <i
          :style="showDetail4? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail4= !showDetail4"
        />
      </div>
      <div
        v-show="showDetail4"

        v-disable-all="!editMode"
        class="form-main"
        style="padding: 0 82px;"
      >
        <el-form inline label-width="155px">
          <div
            v-for="(item,index) in supplierInfo.productInfoCreateReqVO"
            :key="index"
            class="shadow shadowPadding"
          >
            <el-form-item :label="$t('supplier.nameOfSuppliedProduct')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.name"
              >
                <el-input v-model="item.name" :disabled="!editMode" />
              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.countryOfOrigin')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.countryOrigin"
              >
                <el-input v-model="item.countryOrigin" :disabled="!editMode" />
              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.supplyProductCategory')">
              <show-or-edit
                :dict="DICT_TYPE.COMMON_CATEGORY"
                :disabled="!editMode"
                :value="item.categoryArr.at(-1)"
              >
                <el-cascader
                  v-model="item.categoryArr"
                  :disabled="!editMode"
                  :options="categoryList"
                  :placeholder="$t('common.pleaseSelect')"
                  :props="{ value: 'id',label:'name'}"
                  :show-all-levels="false"
                  class="smallSelect"
                  clearable
                  filterable
                />
              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.monthlyProduction')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.monthlyOutput"
              >
                <el-input
                  v-model="item.monthlyOutput"
                  :disabled="!editMode"
                  :placeholder="$t('supplier.pleaseEnterQuantityAndUnit')"
                />
              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.selfMadeOutsourced')">
              <show-or-edit
                :dict="DICT_TYPE.SUPPLIER_HOMEMADE_OUTSOURCED"
                :disabled="!editMode"
                :value="item.productionWay"
              >
                <el-select v-model="item.productionWay" :disabled="!editMode" class="smallSelect" filterable>
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_HOMEMADE_OUTSOURCED)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>

              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.productionCycle')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.productionCycle"
              >
                <el-input v-model="item.productionCycle" :disabled="!editMode" />
              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.ownershipOfIntellectualProperty')">
              <show-or-edit
                :dict="DICT_TYPE.SUPPLIER_OWNERSHIP_OF_INTELLECTUAL_PROPERTY"
                :disabled="!editMode"
                :value="item.propertyOwnership"
              >
                <el-select v-model="item.propertyOwnership" :disabled="!editMode" class="smallSelect" filterable>
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_OWNERSHIP_OF_INTELLECTUAL_PROPERTY)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>

              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.proportionOfSales')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.salesPercent"
              >
                <el-input-number v-model="item.salesPercent" :disabled="!editMode" :max="100" :min="0" :precision="2" />
              </show-or-edit>
              %
              <i
                v-if="editMode"

                class="el-icon-circle-plus"
                style="margin-left:2px;margin-top:7px;font-size: 18px;cursor: pointer"
                @click="supplierInfo.productInfoCreateReqVO.push({
                  category: '',
                  categoryArr: '',
                  countryOrigin: '',
                  monthlyOutput: '',
                  productionCycle: '',
                  salesPercent: '',
                  productionWay: '',
                  propertyOwnership: ''
                })"
              />
              <i
                v-if="editMode"

                class="el-icon-remove"
                style="margin-left:5px;font-size: 18px;cursor: pointer"
                @click="supplierInfo.productInfoCreateReqVO.length===1?
                  supplierInfo.productInfoCreateReqVO= [
                    {
                      category: '',
                      categoryArr: '',
                      countryOrigin: '',
                      monthlyOutput: '',
                      productionCycle: '',
                      salesPercent: '',
                      productionWay: '',
                      propertyOwnership: ''
                    }
                  ]
                  :supplierInfo.productInfoCreateReqVO.splice(index,1)"
              />
            </el-form-item>

          </div>
        </el-form>
      </div>

      <div class="form-title">3.2 <span style="margin-left:5px">{{
        $t('supplier.productAndProcessProcessCapability')
      }}</span>
        <i
          :style="showDetail5? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail5= !showDetail5"
        />
      </div>
      <div
        v-show="showDetail5"
        v-disable-all="!editMode"
        class="form-main"
      >

        <div
          class="shadow shadowPadding"
        >
          <div class="tableTitle">{{
            $t('supplier.mainSuppliersAndMaterials')
          }}
          </div>
          <div class="tablePadding">
            <el-table :data="supplierInfo.rawMaterialCreateReqVO">
              <el-table-column :label="$t('supplier.brandOfRawMaterials')" prop="rawMaterialBrand">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.rawMaterialBrand"
                    class="rawMaterialFlex"
                  >
                    <el-input v-model="scope.row.rawMaterialBrand" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.rawMaterialSupplier')" prop="rawMaterialSupplier">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.rawMaterialSupplier"
                    class="rawMaterialFlex"
                  >
                    <el-input v-model="scope.row.rawMaterialSupplier" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.materialsAndSpecifications')" prop="specifications">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.specifications"
                    class="rawMaterialFlex"
                  >
                    <el-input v-model="scope.row.specifications" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.countryOfOrigin')" prop="countryOrigin">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.countryOrigin"
                    class="rawMaterialFlex"
                  >
                    <el-input v-model="scope.row.countryOrigin" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.proportionInRawMaterialProcurement')" prop="rawMaterialPercent">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.rawMaterialPercent"
                    class="rawMaterialFlex"
                  >
                    <el-input-number
                      v-model="scope.row.rawMaterialPercent"
                      :disabled="!editMode"
                      :max="100"
                      :min="0"
                    />
                  </show-or-edit>%
                </template>
              </el-table-column>
              <el-table-column prop="op" width="80">
                <template slot-scope="scope">
                  <div style="flex: 0 0 5%;display:flex;width: 60px;text-align: right">
                    <i
                      v-if="editMode"

                      class="el-icon-circle-plus"
                      style="margin-left:2px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.rawMaterialCreateReqVO.push({
                        countryOrigin: '',
                        rawMaterialBrand: '',
                        rawMaterialPercent: '',
                        rawMaterialSupplier: '',
                        specifications: ''
                      })"
                    />
                    <i
                      v-if="editMode"

                      class="el-icon-remove"
                      style="margin-left:5px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.rawMaterialCreateReqVO.length===1?
                        supplierInfo.rawMaterialCreateReqVO= [
                          {
                            countryOrigin: '',
                            rawMaterialBrand: '',
                            rawMaterialPercent: '',
                            rawMaterialSupplier: '',
                            specifications: ''
                          }
                        ]
                        :supplierInfo.rawMaterialCreateReqVO.splice(index,1)"
                    />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div
          class="shadow shadowPadding"
        >
          <div class="tableTitle">
            {{ $t('supplier.listOfMainEquipmenttopTen') }}
            <span style="margin: 0 5px;font-weight: 400">{{ $t('supplier.batchUpload') }}</span>
            <el-button
              v-if="editMode"
              v-hasPermi="['supplier:equipment:import']"
              class="uploadBtn"
              icon="el-icon-plus"
              plain
              style="padding: 5px 9px"
              type="primary"
              @click="uploadVisible = true"
            />
          </div>
          <div class="tablePadding">
            <el-table :data="supplierInfo.equipmentCreateReqVO">
              <el-table-column :label="$t('supplier.equipmentName')" prop="name" />
              <el-table-column :label="$t('material.brand')" prop="brand" />
              <el-table-column :label="$t('supplier.model')" prop="manufacturerSn" />
              <el-table-column :label="$t('supplier.quantity')" prop="quantity" />
              <el-table-column :label="$t('common.remarks')" prop="remark" />
              <el-table-column :label="$t('supplier.purchaseDate')" prop="purchaseDate" />
              <el-table-column :label="$t('supplier.purchasePriceOfEquipment')" prop="purchasePrice" />
            </el-table>
          </div>
        </div>
        <div
          class="shadow shadowPadding"
        >
          <div style="padding: 20px 56px; display: flex;justify-content: space-between;font-size: 14px">
            <div>
              <div style="padding-left:14px;font-weight: bold;margin-bottom: 10px">{{
                $t('supplier.addedCategory')
              }}
              </div>
              <span style="margin-right: 10px">{{ $t('material.productCategory') }}</span>
              <show-or-edit
                :dict="DICT_TYPE.COMMON_CATEGORY"
                :disabled="!editMode"
                :value="supplierInfo.processCapabilityCreateReqVO.productCategoryArr.at(-1)"
              >
                <el-cascader
                  v-model="supplierInfo.processCapabilityCreateReqVO.productCategoryArr"
                  :disabled="!editMode"
                  :options="categoryList"
                  :placeholder="$t('common.pleaseSelect')"
                  :props="{ value: 'id',label:'name'}"
                  :show-all-levels="false"
                  class="smallSelect"
                  filterable
                  @change="changeProcessCapability"
                />
              </show-or-edit></div>
            <div style="width: 200px">
              <div style="font-weight:bold;text-align: center;margin-bottom: 15px">{{
                $t('supplier.processCapability')
              }}
              </div>
              <div style="text-align: center">
                <el-button
                  v-if="supplierInfo.processCapabilityCreateReqVO.businessDictRelStr"
                  :disabled="!editMode"
                  type="text"
                  @click="clickProcessCapability"
                >
                  <div v-if="displayProcessCapability" class="ellipsis-line">
                    {{ supplierInfo.processCapabilityCreateReqVO.businessDictRelStr }}
                  </div>
                </el-button>
                <el-button
                  v-else
                  :disabled="!editMode"
                  icon="el-icon-edit"
                  style="font-size: 18px;"
                  type="text"
                  @click="clickProcessCapability"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="padding: 20px 56px; display: flex;justify-content: space-between;font-size: 14px">
        <span style="width: 80px; margin-right: 10px">{{ $t('supplier.modifyRemarks') }}</span>
        <el-input v-model="supplierInfo.remark" :disabled="!editMode" :placeholder="$t('common.pleaseEnter')" type="textarea" style="margin-bottom: 40px" />
      </div>

      <div v-if="!supplierInfo.processInstanceId&&editMode" class="fixedBottom">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button
          :loading="loadingButton"
          type="primary"
          @click="submitSupplierInfo()"
        >{{ $t('common.submit') }}
        </el-button>
      </div>
    </div>
    <el-dialog
      v-if="brandVisible"
      :visible.sync="brandVisible"
    >
      <brand
        :scope="supplierInfo.processCapabilityCreateReqVO.businessDictRelStr"
        @closeBrand="closeBrand"
      />
    </el-dialog>
    <el-dialog
      :title="$t('common.batchCreation')"
      :visible.sync="uploadVisible"
      width="600px"
    >
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div style="text-align: center">
        <el-upload
          ref="upload"
          :action="upload.url"
          :auto-upload="false"
          :before-upload="beforeUploadXls"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelMainEquipmentUpload">{{ $t('common.cancel') }}</el-button>
        <el-button
          v-hasPermi="['supplier:equipment:export']"
          icon="el-icon-download"
          @click="downloadTemplate"
        >
          {{ $t('supplier.templateDownload') }}
        </el-button>
        <el-button v-hasPermi="['supplier:equipment:import']" type="primary" @click="handleUpload">
          {{ $t('common.confirm') }}
        </el-button>
      </div>
    </el-dialog>

    <bpm-process-instance
      v-if="supplierInfo.processInstanceId&&!viewOnly"
      :process-instance-id="supplierInfo.processInstanceId"
      @rejectAuditEvent="rejectAudit"
      @submitAuditEvent="submitSupplierInfo"
      @approvalAuditEvent="submitSupplierInfo"
      @closeAuditEvent="closeAuditEvent"
    />
  </div>
</template>

<script>
import Sticky from '@/components/Sticky'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import BpmProcessInstance from '@/components/BpmProcessInstance/index.vue'
import {
  createBaseInfoView,
  exportEquip,
  getEditBaseInfoView, readTianYanChaInfo,
  rejectBaseInfoView
} from '@/api/supplier/info'
import brand from '@/views/supplier/info/brand'
import { getBaseHeader, handleAuthorized } from '@/utils/request'
import { getTreeMap } from '@/utils'
import $modal from '@/plugins/modal'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { cancelRecord } from '@/api/supplier/infoChangeRecord'

export default {
  name: 'BaseInfo',
  components: {
    ShowOrEdit,
    brand,
    Sticky, BpmProcessInstance
  },
  data() {
    return {
      // 涉及到数据库更新操作的按钮操作增加loading操作，防止重复操作
      loadingButton: false,
      // 制程能力为非电子类时，已选择的品牌是否展示
      displayProcessCapability: false,
      key: '',
      // 只获取启用状态
      categoryList: [],
      // 所有状态
      allCategoryList: [],
      supplierNameAndNo: '',
      supplierInfo: {
        id: '',
        recordId: null,
        reamrk: '',
        establishedTime: undefined,
        associatedCompanyCreateReqVO: [
          {
            relationship: '',
            countryRegion: '',
            street: '',
            provinceCity: '',
            zipCode: '',
            province: []
          }
        ],
        buildProperty: '',
        cleanRoom: 0,
        cleanRoomLevel: '',
        code: '',
        companyCategory: '',
        registeredCapital: '',
        registeredCapitalCurrency: '',
        companyNature: '',
        companyCurrency: '',
        isSupplier: true,
        contactCreateReqVO: [
          {
            name: '',
            position: '',
            phone: '',
            email: '',
            division: [],
            businessDictRelStr: ''
          }
        ],
        countryRegion: '',
        coversArea: 0,
        creditCode: '',
        customerCreateReqVO: [
          {
            industry: '',
            name: '',
            proportion: '',
            terminalCustomer: ''
          }
        ],
        esicCustomerCreateReqVO: [
          {
            name:'',
            involveTime: null
          }
        ],
        dictRelCreateReqVO: [],
        email: '',
        emsOther: '',
        // 主要设备的前十条数据
        equipmentCreateReqVO: [],
        // 主要设备全部数据
        equipmentCreateReqAllListVO: [],
        // 主要设备上传的文件id，如果没有新上传数据，则为空
        equipmentFileId: null,
        exportQualification: '',
        factoryAddressCreateReqVO: [
          {
            countryRegion: '',
            street: '',
            provinceCity: '',
            zipCode: '',
            province: []
          }
        ],
        fax: '',
        laboratoryEquipment: '',
        legalPerson: '',
        name: '',
        nameShort: '',
        phone: '',
        position: '',
        processCapabilityCreateReqVO: {
          productCategory: '',
          productCategoryArr: [],
          businessDictRelStr: ''
        },
        productInfoCreateReqVO: [
          {
            name: '',
            categoryArr: [],
            category: '',
            countryOrigin: '',
            monthlyOutput: '',
            productionCycle: '',
            salesPercent: '',
            productionWay: '',
            propertyOwnership: ''
          }
        ],
        productionArea: 0,
        provinceCity: '',
        rawMaterialCreateReqVO: [
          {
            countryOrigin: '',
            rawMaterialBrand: '',
            rawMaterialPercent: '',
            rawMaterialSupplier: '',
            specifications: ''
          }
        ],
        reason: '',
        registeredAddress: '',
        shareholdersCreateReqVO: [
          {
            name: '',
            stake: ''
          }
        ],
        sourcing: '',
        status: 0,
        street: '',
        telephone: '',
        turnoverCreateReqVO: [
          {
            currency: '',
            turnover: '',
            unit: '',
            year: new Date().getFullYear()
          },
          {
            currency: '',
            turnover: '',
            unit: '',
            year: new Date().getFullYear() - 1
          }, {
            currency: '',
            turnover: '',
            unit: '',
            year: new Date().getFullYear() - 2
          }
        ],
        webSite: '',
        zipCode: ''
      },
      supplierInfoRule: {
        email: [{

          pattern: /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
          message: this.$t('supplier.incorrectMailboxFormat'),
          trigger: 'blur'
        }],
        nameShort: [{ required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('supplier.pleaseEnterFullSupplierName'), trigger: 'blur' }],
        nameEn: [{ required: true, message: this.$t('supplier.pleaseEnterFullSupplierName'), trigger: 'blur' }]
      },
      region: getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(item => item.type === 'country'),
      province: [],
      manageSystem: [],
      supplierSaleArea: this.formatDictValue(DICT_TYPE.SUPPLIER_SALE_AREA),
      supplierDev: this.formatDictValue(DICT_TYPE.SUPPLIER_QUALIFICATIONS_OF_DEVELOPER),
      supplierCertificate: this.formatDictValue(DICT_TYPE.SUPPLIER_SPECIAL_WORK_QUALIFICATION_CERTIFICATE),
      supplierStaff: this.formatDictValue(DICT_TYPE.SUPPLIER_STAFF),
      brandVisible: false,
      editMode: true,
      abilityRow: {},
      uploadVisible: false,
      supplierId: null,
      recordId: null,
      viewOnly: '',
      upload: {
        headers: getBaseHeader(),
        url: '',
        isUploading: false
      },
      showUpload: false,
      uploadUrl: '',
      headers: getBaseHeader(),
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null,
        columns: [
          {
            tableName: 'supplier_base_info',
            keyColumns: ['creditCode']
          }
        ]
      },
      supplierShow: true,
      fileType: '',
      showDetail: false,
      showDetail1: false,
      showDetail2: false,
      showDetail3: false,
      showDetail4: false,
      showDetail5: false,
      showDetail6: false,
      showDetail7: false,
      showDetail8: false,
      showDetail9: false,
      showDetail0: false,
      showDetail11: false
    }
  },
  created() {
    this.recordId = this.$route.params.id
    if (this.recordId === '0') {
      this.recordId = null
    }
    this.supplierId = this.$route.query.supplierId
    this.viewOnly = this.$route.query.viewOnly
    if (this.viewOnly === 'true') {
      this.viewOnly = true
    } else if (this.viewOnly === 'false') {
      this.viewOnly = false
    }
    if (!this.supplierId) {
      this.supplierId = this.$store.getters.supplierId
    }
    this.init()
    this.getSupplierBase()
  },
  mounted() {
    this.getCategories()
    this.recordId = this.$route.params.id
    if (this.recordId === '0') {
      this.recordId = null
    }
    this.supplierId = this.$route.query.supplierId
    this.viewOnly = this.$route.query.viewOnly
    if (this.viewOnly === 'true') {
      this.viewOnly = true
    } else if (this.viewOnly === 'false') {
      this.viewOnly = false
    }
    if (!this.supplierId) {
      this.supplierId = this.$store.getters.supplierId
    }
    this.getSupplierBase()
  },
  beforeDestroy() {
    window.onscroll = null
  },
  methods: {
    // 制程能力品类事件改变触发
    changeProcessCapability(value) {
      if (value.includes(52)) {
        this.displayProcessCapability = true
      } else {
        this.displayProcessCapability = false
        this.$message.warning(this.$t('common.notNeedInput'))
      }
    },
    // 点击 制程能力时的判断方法
    clickProcessCapability() {
      // 第一阶段 非电子类品类点击则提示 ‘暂无填写要求’
      if (!this.supplierInfo.processCapabilityCreateReqVO.productCategoryArr.includes(52)) {
        this.$message.warning(this.$t('common.notNeedInput'))
        return
      }
      this.brandVisible = true
    },
    init() {
      this.upload.url = process.env.VUE_APP_BASE_API + `/admin-api/supplier/base-info/edit-import-equipment?supplierId=${this.supplierId}`
      this.getConfigKey('file.type.common').then(response => {
        this.fileType = response.data
      })
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY), 'id'))
      this.allCategoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, false), 'id'))
    },
    getSupplierBase() {
      getEditBaseInfoView({
        recordId: this.recordId,
        supplierId: this.supplierId
      }).then(res => {
        this.manageSystem = []
        this.province = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(a => a.parentId === res.data.countryRegion)
        res.data.factoryAddressCreateReqVO?.map(item => {
          item.province = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(a => a.parentId === item.countryRegion)
        })
        res.data.associatedCompanyCreateReqVO?.map(item => {
          item.province = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(a => a.parentId === item.countryRegion)
        })
        res.data.contactCreateReqVO?.map(item => {
          item.division = item.businessDictRelStr ? item.businessDictRelStr.split(',') : []
        })
        res.data.productInfoCreateReqVO?.map(item => {
          item.categoryArr = item.category ? getTreeMap(item.category, this.allCategoryList) : []
        })
        res.data.processCapabilityCreateReqVO.productCategoryArr =
          getTreeMap(res.data.processCapabilityCreateReqVO.productCategory, this.allCategoryList)
        if (res.data.processCapabilityCreateReqVO.productCategory === 52) {
          this.displayProcessCapability = true
        } else {
          this.displayProcessCapability = this.hasNodeWithId(this.categoryList.filter(v => v.id === 52)[0], res.data.processCapabilityCreateReqVO.productCategory)
        }
        const searchInput = (arr, dict, item) => {
          return arr.find(dic => dic.dictDataValue === item.value && dic.dictTypeValue === dict)?.value
        }
        this.supplierSaleArea.forEach(item => {
          item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.SUPPLIER_SALE_AREA, item)
        })
        this.supplierDev.forEach(item => {
          item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.SUPPLIER_QUALIFICATIONS_OF_DEVELOPER, item)
        })
        this.supplierCertificate.forEach(item => {
          item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.SUPPLIER_SPECIAL_WORK_QUALIFICATION_CERTIFICATE, item)
        })
        this.supplierStaff.forEach(item => {
          item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.SUPPLIER_STAFF, item)
        })
        res.data.dictRelCreateReqVO.map(item => {
          if (item.dictTypeValue === DICT_TYPE.ENTERPRISE_MANAGEMENT_SYSTEM && item.value === 'true') {
            this.manageSystem.push(item.dictDataValue)
          }
        })
        if (res.data.turnoverCreateReqVO.length < 3) {
          res.data.turnoverCreateReqVO = [
            {
              currency: '',
              turnover: '',
              unit: '',
              year: new Date().getFullYear()
            },
            {
              currency: '',
              turnover: '',
              unit: '',
              year: new Date().getFullYear() - 1
            }, {
              currency: '',
              turnover: '',
              unit: '',
              year: new Date().getFullYear() - 2
            }
          ]
        }
        this.supplierInfo = res.data

        if (this.supplierInfo.code) {
          this.supplierNameAndNo = this.supplierInfo.name + ' - ' + this.supplierInfo.code
        } else {
          this.supplierNameAndNo = this.supplierInfo.name
        }

        if (this.recordId) {
          this.editMode = false
        }
      })
    },
    closeBrand(str) {
      this.brandVisible = false
      this.supplierInfo.processCapabilityCreateReqVO.businessDictRelStr = str
    },
    // 供应商信息提交
    submitSupplierInfo(taskId, processInstanceId, processInstanceReason) {
      let pass = true
      let errField = {}
      for (const item of ['baseInfo', 'personInfo']) {
        this.$refs[item].validate((valid, object) => {
          if (!valid) {
            errField = { ...errField, ...object }
            pass = false
          }
        })
      }
      const contactEmailValid = []
      for (const k in this.supplierInfo.contactCreateReqVO) {
        this.$refs[`contactItem${k}`].validate((valid, object) => {
          if (!valid) {
            contactEmailValid.push(`contactItem${k}`)
          }
        })
      }
      if (contactEmailValid.length) {
        this.$refs[contactEmailValid].$el.scrollIntoView({
          block: 'center',
          behavior: 'smooth'
        })
        this.$message.error(this.$t('supplier.pleaseCompleteTheInformation'))
        return false
      }
      const contactDivisionValid = []
      for (const k in this.supplierInfo.contactCreateReqVO) {
        this.$refs[`contactItemDivision${k}`].validate((valid, object) => {
          if (!valid) {
            contactDivisionValid.push(`contactItemDivision${k}`)
          }
        })
      }
      if (contactDivisionValid.length) {
        this.$refs[contactDivisionValid].$el.scrollIntoView({
          block: 'center',
          behavior: 'smooth'
        })
        this.$message.error(this.$t('supplier.pleaseCompleteTheInformation'))
        return false
      }
      if (!pass) {
        // 定位代码
        this.$refs[Object.keys(errField)[0]].$el.scrollIntoView({
          block: 'center',
          behavior: 'smooth'
        })
        this.$message.error(this.$t('supplier.pleaseCompleteTheInformation'))
        return false
      }
      if (!this.supplierInfo.contactCreateReqVO.every(item => item.name && item.division.length > 0)) {
        this.$refs.mainContact.$el.scrollIntoView({
          block: 'center',
          behavior: 'smooth'
        })
        this.$message.error(this.$t('supplier.mainContactNameAndContactDivisionAreRequired'))
        return false
      }
      if (!this.supplierInfo.contactCreateReqVO.some(item => item.division.includes('the_main_contact'))) {
        this.$refs.mainContact.$el.scrollIntoView({
          block: 'center',
          behavior: 'smooth'
        })
        this.$message.error(this.$t('supplier.thereMustBeAtLeastOnePrimaryContactTheDivisionOfLaborIsThePrimaryContact'))
        return false
      }

      const flatDictData = (arr, dictName) => {
        return arr.map(item => {
          return {
            value: item.inputValue,
            dictDataValue: item.value,
            dictTypeValue: dictName,
            reason: this.supplierInfo.reason
          }
        })
      }
      this.supplierInfo.productInfoCreateReqVO.map(item => {
        item.category = item.categoryArr.at(-1)
      })
      this.supplierInfo.processCapabilityCreateReqVO.productCategory = this.supplierInfo.processCapabilityCreateReqVO.productCategoryArr.at(-1)
      this.supplierInfo.contactCreateReqVO.map(item => {
        item.businessDictRelStr = item.division.join(',')
      })
      this.supplierInfo.dictRelCreateReqVO = [
        ...flatDictData(this.supplierSaleArea, DICT_TYPE.SUPPLIER_SALE_AREA),
        ...flatDictData(this.supplierDev, DICT_TYPE.SUPPLIER_QUALIFICATIONS_OF_DEVELOPER),
        ...flatDictData(this.supplierCertificate, DICT_TYPE.SUPPLIER_SPECIAL_WORK_QUALIFICATION_CERTIFICATE),
        ...flatDictData(this.supplierStaff, DICT_TYPE.SUPPLIER_STAFF),
        ...this.manageSystem.map(item => {
          return {
            value: true,
            dictDataValue: item,
            dictTypeValue: DICT_TYPE.ENTERPRISE_MANAGEMENT_SYSTEM
          }
        })
      ]
      this.supplierInfo.recordId = this.recordId
      createBaseInfoView({ ...this.supplierInfo, processInstanceId, taskId, processInstanceReason }).then(res => {
        if (this.$store.state.user.id === -1) {
          removeToken()
          location.href = getPath('/login')
        } else {
          this.$tab.closePage()
        }
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    },
    closeAuditEvent() {
      cancelRecord(this.recordId).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.$tab.closeOpenPage('/supplier/info-change-record')
      })
    },
    // 取消操作
    cancel() {
      this.$tab.closePage()
    },
    formatDictValue(type) {
      return getDictDatas(type).map(item => {
        this.$set(item, 'inputValue', '')
        this.$set(item, 'reason', '')
        return {
          ...item
        }
      })
    },
    changeRegion(val, item) {
      if (item) {
        item.province = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(a => a.parentId === val)
        item.provinceCity = ''
      } else {
        this.province = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(a => a.parentId === val)
        this.supplierInfo.provinceCity = ''
      }
    },
    hasNodeWithId(node, targetId) {
      if (node) {
        if (node.id === targetId) {
          return true
        }
        if (node?.children) {
          for (const child of node.children) {
            if (this.hasNodeWithId(child, targetId)) {
              return true
            }
          }
        }
        return false
      }
    },
    cpAddress(item) {
      item.countryRegion = this.supplierInfo.countryRegion
      item.street = this.supplierInfo.street
      item.provinceCity = this.supplierInfo.provinceCity
      item.province = this.province
      item.zipCode = this.supplierInfo.zipCode
    },
    // 上传时的钩子方法，不允许点击上传组件
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 主要设备清单取消-需要删除当前已经上传的文件
    cancelMainEquipmentUpload() {
      this.uploadVisible = false
      this.$refs.upload.clearFiles()
      this.supplierInfo.equipmentFileId = null
    },
    // 上传成功后的钩子方法
    handleFileSuccess(response, file, fileList) {
      // 放在第一行，否则下次点击该上传组件失效
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code === 401) {
        return handleAuthorized()
      } else if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 该上传api处理excel导入错误不会返回错误码，是将错误信息放在返回对象中体现，so需要手动判断
      if (response.data.failureEquipments || response.data.checkErrorEquipments) {
        this.$message.error(this.$t('supplier.failedToUploadTheFilePleaseCheckTheFileFormat'))
        this.uploadVisible = false
      } else {
        this.supplierInfo.equipmentCreateReqVO = response.data.equipmentRespVO
        this.supplierInfo.equipmentCreateReqAllListVO = response.data.equipmentRespAllListVO
        this.supplierInfo.equipmentFileId = response.data.equipmentFileId
        this.$message.success(this.$t('common.uploadSucceeded'))
        this.uploadVisible = false
      }
    },
    handleUpload() {
      this.uploadVisible = false
      this.showUpload = false
      this.$refs.upload.submit()
    },
    downloadTemplate() {
      exportEquip({
        supplierId: this.supplierId
      }).then(res => {
        this.$download.excel(res, this.$t('supplier.deviceInformationxls'))
      })
    },
    beforeUploadXls(file) {
      if (file.size > 5242880) {
        $modal.msgError(this.$t('common.uploadFileSizeCannotExceedm'))
        return false
      }
      if (!['xls', 'xlsx'].includes(file.name.slice(file.name.lastIndexOf('.') + 1))) {
        $modal.msgError(this.$t('supplier.unsupportedFileFormat'))
        return false
      }
    },
    cpStreet() {
      const index = this.supplierInfo.registeredAddress?.indexOf('市')
      if (index > -1) {
        this.supplierInfo.street = this.supplierInfo.registeredAddress.slice(index + 1)
      } else {
        this.supplierInfo.street = this.supplierInfo.registeredAddress
      }
    },
    rejectAudit(taskId, processInstanceId, processInstanceReason) {
      this.supplierInfo.processInstanceId = processInstanceId
      rejectBaseInfoView({
        ...this.supplierInfo,
        taskId,
        processInstanceReason
      }).then(res => {
        if (res) {
          this.$message.success(this.$t('sl.successfullyReturned'))
          setTimeout(() => {
            this.$tab.closePage()
          }, 3000)
        }
      })
    },
    getTYCData() {
      readTianYanChaInfo({
        supplierName: this.supplierInfo.name
      }).then(res => {
        if (res.data != null) {
          this.supplierInfo.registeredAddress = res.data.registeredAddress
          this.supplierInfo.creditCode = res.data.creditCode
          this.supplierInfo.registeredCapital = res.data.registeredCapitalOfCurrency
          this.supplierInfo.registeredCapitalCurrency = res.data.registeredCapitalCurrency
          this.supplierInfo.legalPerson = res.data.legalPerson
          this.supplierInfo.webSite = res.data.webSite
          this.supplierInfo.establishedTime = res.data.establishedTime
        } else {
          this.$message.info(this.$t('supplier.noInformationRelatedToTianyanchaWasObtained'))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.baseInfo {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
    .el-form-item__content{
      width: calc(100% - 155px);
    }
  }
}

.associatedCompany {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}

.shadow {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}

.supplierStaff {
  ::v-deep .el-form-item {
    width: 100%;
    margin-right: 0;
    padding-right: 10px;
  }
}

::v-deep .el-input__inner {
  color: #606266 !important;
  //background: #ffffff!important;
}

.verticalMiddle {
  display: flex;
  align-items: center
}

::v-deep .descriptionLabel {
  width: 145px;
  font-weight: bold;
  display: inline-block;
  text-align: right;
}

.upload p {
  line-height: 12px;
}

.required:before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

.supplier {
  .tableTitle {
    padding-left: 70px;
    font-size: 15px;
    font-weight: bold;
    margin: 20px 0;
  }
  .tablePadding{
    padding: 0 20px
  }

  .shadowPadding {
    padding: 5px 0;
    margin: 15px
  }

  .supplier-progress {
    height: 3px;
    width: 50%;
    background-color: #323232;
  }

  margin: 0 auto;
  width: 1000px;
  background: #c1c1c1;
  padding: 20px;

  .active {
    background-color: #4996b8;
    color: #fff
  }

  &-header {
    font-size: 16px;
    width: 100%;
    padding-bottom: 25px;
    margin-bottom: 20px;

    &-right {
      text-align: right;
      font-size: 12px;
      color: #173b75;
      margin-right: 20px
    }
  ;

    &-btn {
      cursor: pointer;
      padding: 10px 15px;
      display: inline-block;
      background-color: #cecece;
      color: #fff
    }
  ;
  }

  .smallInput {
    width: 161px;
  }

  .smallSelect {
    width: 188px;
  }

  .bigBaseInput {
    width: 533px
  }

  .bigInput {
    width: 591px
  }

  .colCenter {
    text-align: center;
  }

  .form-title {
    border-left: 6px solid #376092;
    margin: 10px 0;
    padding: 10px 30px;
    font-size: 16px;
    font-weight: bold;
    background-color: #f1f1f1;
  }

  // 上传输入框的文件列表样式调整#文字加粗
  ::v-deep .el-upload-list__item-name {
    font-weight: 600;
  }

  .centerRow {
    ::v-deep .el-form-item__content {
      width: calc(100% - 139px);
      text-align: center;
    }
  }
  .ellipsis-line {
    width: 248px;
    overflow: hidden;
    text-overflow: ellipsis; //文本溢出显示省略号
    white-space: nowrap; //文本不会换行
  }

  .shadow {
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }

  .rawMaterialFlex {
    flex: 0 1 15%;
    text-align: center;
  }

  .customItem {
    flex: 0 1 25%;
    text-align: center;
  }

  .form-main {
    .product {
      display: flex;
      min-height: 150px;

      .product-left {
        flex: 0 1 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 5px 10px;

        .product-title {
          flex: none;
          font-weight: bold;
          text-align: center;
          word-break: break-all;
          font-size: 20px;
          margin: 10px 0;
        }

        .product-detail {
          word-break: break-all;
          flex: auto;
          text-align: center;
        }
      }

      .product-right {
        justify-content: center;
        align-items: center;
        flex: 0 1 50%;
        border-left: 1px #dcdcdc solid;
        display: flex;
        flex-wrap: wrap;
      }
    }

  }

  .fixedBottom {
    position: fixed;
    width: calc(100% - 231px);
    bottom: 20px;
    display: flex;
    justify-content: center;
    margin-top: 40px;
    right: 30px;
  }
}
</style>
