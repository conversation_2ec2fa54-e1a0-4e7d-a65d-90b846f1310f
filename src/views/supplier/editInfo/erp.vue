<template>
  <div style="padding:  15px 20px">
    <div>
      <show-or-edit
        disabled="true"
        :value="supplierNameAndNo"
        style="font-size: 16px; font-weight: 700; color: rgba(73, 150, 184, 0.99); padding-left: 10px;"
      >
        <el-input v-model="supplierNameAndNo" />
      </show-or-edit>
    </div>
    <common-card>
      <div v-if="!viewOnly && !authAndSlModuleEnable" style="margin: 10px 0">
        <el-button
          type="primary"
          @click="application.companyRelRespVOList.push({
            companyId:'',
            paymentType:'',
            reconciliationAccount:'',
            currency:'',
            supplierId
          })"
        >{{ $t('supplier.newLine') }}
        </el-button>
        <el-button @click="delErpCompany">{{ $t('supplier.deleteSelected') }}</el-button>
      </div>
      <vxe-table
        ref="companyRelRespVOList"
        :data="application.companyRelRespVOList"
        :row-key="true"
      >
        <vxe-column
          type="checkbox"
          width="55"
        />

        <vxe-column
          :title="$t('system.companyCode')"
          field="companyId"
        >
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly || authAndSlModuleEnable"
              :value="scope.row.companyId"
              :dict="DICT_TYPE.COMMON_COMPANY"
            >
              <el-select v-model="scope.row.companyId" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_COMPANY)"
                  :key="dict.value"
                  :label="dict.code+'-'+dict.name"
                  :value="dict.id"
                />
              </el-select>
            </show-or-edit>
          </template>
        </vxe-column>
        <vxe-column
          :title="$t('supplier.termOfPayment')"
          field="paymentType"
        >
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly"
              :value="scope.row.paymentType"
              :dict="DICT_TYPE.SUPPLIER_PAYMENT_TERM"
            >
              <el-select v-model="scope.row.paymentType" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_TERM)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </template>
        </vxe-column>
        <vxe-column
          :title="$t('supplier.reconciliationAccount')"
          field="reconciliationAccount"
        >
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly"
              :value="scope.row.reconciliationAccount"
              :dict="DICT_TYPE.SUPPLIER_CONTROLLER_ACCOUNT"
            >
              <el-select v-model="scope.row.reconciliationAccount" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CONTROLLER_ACCOUNT)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </template>
        </vxe-column>

        <vxe-column
          :title="$t('auth.supplierAccountGroup')"
          field="accountGroup"
        >
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly"
              :value="scope.row.accountGroup"
              :dict="DICT_TYPE.SUPPLIER_ACCOUNT_GROUP"
            >
              <el-select v-model="scope.row.accountGroup" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_ACCOUNT_GROUP)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </template>
        </vxe-column>

        <vxe-column
          :title="$t('material.procurementGroup')"
          field="pgId"
        >
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly"
              :value="scope.row.pgId"
              :dict="DICT_TYPE.SYSTEM_PURCHASE_GROUP"
            >
              <el-select v-model="scope.row.pgId" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_PURCHASE_GROUP)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="Number(dict.value)"
                />
              </el-select>
            </show-or-edit>
          </template>
        </vxe-column>

        <vxe-column
          :title="$t('supplier.manufacturerNature')"
          field="manufacturerNature"
        >
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly"
              :value="scope.row.manufacturerNature"
              :dict="DICT_TYPE.AUTH_SUPPLIER_MANUFACTURER_NATURE"
            >
              <el-select v-model="scope.row.manufacturerNature" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_MANUFACTURER_NATURE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </template>
        </vxe-column>

        <vxe-column
          :title="$t('system.currency')"
          field="currency"
        >
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly"
              :value="scope.row.currency"
              :dict="DICT_TYPE.COMMON_CURRENCY"
            >
              <el-select v-model="scope.row.currency" clearable transfer>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                  :key="dict.value"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>
            </show-or-edit>
          </template>
        </vxe-column>
      </vxe-table>
    </common-card>
    <common-card>

      <div v-if="!viewOnly && !authAndSlModuleEnable" style="margin: 10px 0">
        <el-button
          type="primary"
          @click="application.purchaseOrgRelRespVOList.push({
            orgId:'',
            currency:'',
            invoiceCheck:'',
            supplierLevel:'',
            supplierId,
            categoryIds:[]

          })"
        >{{ $t('supplier.newLine') }}
        </el-button>
        <el-button @click="delErpPurchase">{{ $t('supplier.deleteSelected') }}</el-button>
      </div>

      <vxe-table
        ref="purchaseOrgRelRespVOList"
        :data="application.purchaseOrgRelRespVOList"
        :row-key="true"
      >
        <vxe-column
          type="checkbox"
          width="55"
        />
        <vxe-column :title="$t('supplier.purchasingOrganization')">
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly || authAndSlModuleEnable"
              :value="scope.row.orgId"
              :dict="DICT_TYPE.COMMON_PURCHASEORG"
            >
              <el-select v-model="scope.row.orgId">
                <el-option
                  v-for="dict in getDictDatas('purchaseOrg')"
                  :key="dict.value"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>
            </show-or-edit>
          </template>
        </vxe-column>
        <vxe-column :title="$t('system.currency')">
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly"
              :value="scope.row.currency"
              :dict="DICT_TYPE.COMMON_CURRENCY"
            >
              <el-select v-model="scope.row.currency" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                  :key="dict.value"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>
            </show-or-edit>
          </template>
        </vxe-column>
        <vxe-column :title="$t('supplier.invoiceVerificationBasedOnReceipt')">
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly"
              :value="scope.row.invoiceCheck"
              :dict="DICT_TYPE.COMMON_Y_N"
            >
              <el-select v-model="scope.row.invoiceCheck">
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_Y_N)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </template>
        </vxe-column>
        <vxe-column :title="$t('supplier.supplierEchelonLevel')">
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly"
              :value="scope.row.supplierLevel"
              :dict="DICT_TYPE.SUPPLIER_TIER_LEVEL"
            >
              <el-select v-model="scope.row.supplierLevel">
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_TIER_LEVEL)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </template>
        </vxe-column>

        <vxe-column :title="$t('auth.categoryAttribution')">
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly"
              :value="scope.row.category"
              :custom-list="categoryList"
            >
              <el-cascader
                v-model="scope.row.category"
                :options="categoryList"
                :placeholder="$t('material.category')"
                :props="{ value: 'id',label:'name',multiple :true}"
                class="input"
                clearable
                filterable
              />
            </show-or-edit>
          </template>
        </vxe-column>

        <vxe-column :title="$t('auth.supplierGrade')">
          <template #default="scope">
            <show-or-edit
              :disabled="viewOnly"
              :value="scope.row.supplierGrade"
              :dict="DICT_TYPE.AUTH_SUPPLIER_SUPPLIER_GRADE"
            >
              <el-select v-model="scope.row.supplierGrade">
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_SUPPLIER_GRADE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </template>
        </vxe-column>

      </vxe-table>
    </common-card>

    <div style="margin-top: 10px;display: flex">
      {{ $t('supplier.modifyRemarks') }}
      <show-or-edit
        style="flex: 1 0 50%;margin-left: 8px;"
        :disabled="viewOnly"
        :value="application.remark"
      >
        <el-input v-model="application.remark" type="textarea" style="margin-bottom: 40px" />
      </show-or-edit>
    </div>
    <bpm-process-instance
      v-if="application.processInstanceId&&!viewOnly"
      :process-instance-id="application.processInstanceId"
      @rejectAuditEvent="rejectAudit"
      @submitAuditEvent="submitAudit"
      @closeAuditEvent="closeAuditEvent"
    />
    <div
      v-if="!application.processInstanceId&&!viewOnly"
      class="fixedBottom"
    >
      <el-button @click="$tab.closePage()">{{ $t('sp.cancel') }}</el-button>
      <el-button type="primary" @click="submitAudit()">{{ $t('common.submit') }}</el-button>
    </div>
  </div>
</template>
<script>
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { detailErpView, rejectErpView, submitErpView, enableErpInfo } from '@/api/supplier/info'
import BpmProcessInstance from '@/components/BpmProcessInstance/index.vue'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { cancelRecord } from '@/api/supplier/infoChangeRecord'
import { getTreeMap } from '@/utils'

export default {
  name: 'Erp/:id',
  components: { ShowOrEdit, BpmProcessInstance },
  data() {
    return {
      supplierNameAndNo: '',
      application: {
        supplierName: '',
        processInstanceId: '',
        companyRelRespVOList: [],
        purchaseOrgRelRespVOList: [],
        remark: ''
      },
      categoryList: [],
      supplierId: null,
      viewOnly: false,
      authAndSlModuleEnable: false // 认证和分级模块是否同时启用
    }
  },
  mounted() {
    this.supplierId = this.$route.query.supplierId
    this.viewOnly = this.$route.query.viewOnly === 'true'
    this.init()
    this.getCategories()
    this.doGetAuthAndSlModuleEnable()
  },
  methods: {
    // 认证和分级模块是否在当前项目中同时启用。
    // Y-则公司和采购组织不允许在供应商模块 新增、删除
    doGetAuthAndSlModuleEnable() {
      enableErpInfo().then(res => {
        this.authAndSlModuleEnable = res.data
      })
    },
    init() {
      detailErpView({
        supplierId: this.supplierId,
        recordId: this.$route.params.id === '0' ? null : this.$route.params.id
      }).then(res => {
        this.application = res.data
        this.application.purchaseOrgRelRespVOList.map(item => {
          item.category = item.categoryIds.map(a => {
            return getTreeMap(a, this.categoryList)
          })
        })
        if (this.application.supplierCode){
          this.supplierNameAndNo = this.application.supplierName + ' - ' + this.application.supplierCode
        }else {
          this.supplierNameAndNo = this.application.supplierName
        }
      })
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY), 'id'))
    },
    async delErpCompany() {
      const data = await this.$refs.companyRelRespVOList.removeCheckboxRow()
      this.application.companyRelRespVOList = this.application.companyRelRespVOList.filter(item =>
        !data.rows.map(a => a._X_ROW_KEY).includes(item._X_ROW_KEY)
      )
    },
    async delErpPurchase() {
      const data = await this.$refs.purchaseOrgRelRespVOList.removeCheckboxRow()
      this.application.purchaseOrgRelRespVOList = this.application.purchaseOrgRelRespVOList.filter(item =>
        !data.rows.map(a => a._X_ROW_KEY).includes(item._X_ROW_KEY)
      )
    },
    rejectAudit(taskId, processInstanceId, processInstanceReason) {
      this.application.purchaseOrgRelRespVOList.forEach(a => {
        a.categoryIds = a.category?.map(a => a.at(-1))
      })
      rejectErpView({
        ...this.application,
        taskId, processInstanceId, processInstanceReason,
        supplierId: this.supplierId
      }).then(res => {
        this.$tab.closePage()
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    },
    closeAuditEvent() {
      cancelRecord(this.$route.params.id).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.$tab.closeOpenPage('/supplier/info-change-record')
      })
    },
    submitAudit(taskId, processInstanceId, processInstanceReason) {
      this.application.purchaseOrgRelRespVOList.forEach(a => {
        a.categoryIds = a.category?.map(a => a.at(-1))
      })
      submitErpView({
        ...this.application,
        supplierId: this.supplierId,
        taskId,
        processInstanceId, processInstanceReason
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.$tab.closePage()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.fixedBottom {
  position: fixed;
  width: calc(100% - 231px);
  bottom: 20px;
  display: flex;
  justify-content: center;
  margin-top: 40px;
  right: 30px;
}
</style>
