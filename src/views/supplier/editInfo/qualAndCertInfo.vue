<template>
  <div class="supplier">
    <div v-if="supplierShow" style="background-color: white">
      <div>
        <show-or-edit
          disabled="true"
          :value="supplierNameAndNo"
          style="font-size: 16px; font-weight: 700; color: rgba(73, 150, 184, 0.99); padding-left: 10px;"
        >
          <el-input v-model="supplierNameAndNo" />
        </show-or-edit>
      </div>

      <div id="quality" class="form-title">4.1 <span
        style="margin-left:5px"
      >{{ $t('supplier.enterpriseManagementSystem') }}</span>
        <i
          :style="showDetail6? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail6= !showDetail6"
        />
      </div>
      <div
        v-show="showDetail6"
        v-disable-all="!editMode"
        class="form-main"
        style="padding: 0 200px"
      >
        <el-checkbox-group v-model="manageSystem" filterable>
          <el-checkbox
            v-for="dict in getDictDatas(DICT_TYPE.ENTERPRISE_MANAGEMENT_SYSTEM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
            border
            style="margin-left: 50px;margin-top: 3px;width: 70px;margin-right: 20px;"
          />
        </el-checkbox-group>
        <div style="display: flex;align-items:center;margin-top: 10px">
          <span style="width:40px;font-size:14px;margin-right: 10px">{{ $t('supplier.other') }}</span>

          <show-or-edit
            :disabled="!editMode"
            :value="supplierInfo.emsOther"
          >

            <el-input v-model="supplierInfo.emsOther" style="width: 75%" />
          </show-or-edit>
        </div>
      </div>

      <div class="form-title">4.2 <span style="margin-left:5px">{{
          $t('supplier.qualificationAndCertification')
        }}</span>
        <i
          :style="showDetail7? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail7= !showDetail7"
        />
      </div>
      <div v-show="showDetail7" v-disable-all="!editMode" class="form-main" style="padding: 0 161px 0 194px">
        <el-form label-width="220px">
          <el-form-item
            v-for="dict in qualificationAndCertification"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <div
              style="margin-left: 50px"
            >
              <el-radio-group
                v-model="dict.inputValue"
              >
                <el-radio
                  v-for="re in getDictDatas(DICT_TYPE.QUALIFICATION_AND_CERTIFICATION_RESULT)"
                  :key="re.value"
                  :label="re.value"
                >{{ re.label }}
                </el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
          <el-form-item v-if="flightReasonVisible" :label="$t('supplier.reason')">
            <el-input v-model="cetiAndQualForm.reason" :disabled="!editMode" style="width: 336px" />
          </el-form-item>
          <el-form-item :label="$t('supplier.cleanRoom')">
            <el-input-number v-model.number="cetiAndQualForm.cleanRoom" :disabled="!editMode" :min="0" style="width: 336px" />
            <span style="margin-left: 5px">{{ $t('supplier.squareMeter') }}</span>
          </el-form-item>
          <el-form-item :label="$t('supplier.cleanRoomGrade')">
            <el-select
              v-model="cetiAndQualForm.cleanRoomLevel"
              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
              :disabled="!editMode"
              style="width: 336px"
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CLEAN_ROOM_CLASS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('supplier.laboratoryEquipment')">
            <el-input v-model="cetiAndQualForm.laboratoryEquipment" :disabled="!editMode" style="width: 336px" />
          </el-form-item>
        </el-form>
      </div>

      <div style="padding: 20px 56px; display: flex;justify-content: space-between;font-size: 14px">
        <span style="width: 80px; margin-right: 10px">{{ $t('supplier.modifyRemarks') }}</span>
        <el-input v-model="supplierInfo.remark" :disabled="!editMode" :placeholder="$t('common.pleaseEnter')" type="textarea" style="margin-bottom: 40px" />
      </div>

      <div v-if="!supplierInfo.processInstanceId&&editMode" class="fixedBottom">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button
          :loading="loadingButton"
          type="primary"
          @click="submitSupplierInfo()"
        >{{ $t('common.submit') }}
        </el-button>
      </div>
    </div>

    <bpm-process-instance
      v-if="supplierInfo.processInstanceId&&!viewOnly"
      :process-instance-id="supplierInfo.processInstanceId"
      @rejectAuditEvent="rejectAudit"
      @submitAuditEvent="submitSupplierInfo"
      @approvalAuditEvent="submitSupplierInfo"
      @closeAuditEvent="closeAuditEvent"
    />
  </div>
</template>

<script>
import Sticky from '@/components/Sticky'
import {DICT_TYPE, getDictDatas} from '@/utils/dict'
import BpmProcessInstance from '@/components/BpmProcessInstance/index.vue'
import {
  createSystemAndQualInfoView,
  getEditSystemAndQualInfoView,
  rejectSystemAndQualInfoView
} from '@/api/supplier/info'
import {getBaseHeader} from '@/utils/request'
import {deepClone} from '@/utils'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { cancelRecord } from '@/api/supplier/infoChangeRecord'

export default {
  name: "Qualandcertinfo/:id",
  components: {
    ShowOrEdit,
    Sticky,
    BpmProcessInstance
  },
  data() {
    return {
      total: 0,
      list: [],
      // 涉及到数据库更新操作的按钮操作增加loading操作，防止重复操作
      loadingButton: false,
      key: '',
      supplierNameAndNo: '',
      supplierInfo: {
        supplierName: '',
        supplierId: '',
        recordId: null,
        reamrk: '',
        cleanRoom: 0,
        cleanRoomLevel: '',
        dictRelCreateReqVO: [],
        emsOther: '',
        laboratoryEquipment: ''
      },
      manageSystem: [],
      qualificationAndCertification: this.formatDictValue(DICT_TYPE.QUALIFICATION_AND_CERTIFICATION),
      qualificationAndCertificationStatic: this.formatDictValue(DICT_TYPE.QUALIFICATION_AND_CERTIFICATION),
      editMode: true,
      supplierId: null,
      recordId: null,
      viewOnly: '',
      qualificationAndCertificationVisible: false,
      cetiAndQualForm: {
        cleanRoom: 0,
        cleanRoomLevel: '',
        dictRelCreateReqVO: [],
        laboratoryEquipment: '',
        supplierId: null
      },
      cetiAndQualFormStatic: {},
      headers: getBaseHeader(),
      supplierShow: true,
      showDetail6: true,
      showDetail7: true
    }
  },
  computed: {
    flightReasonVisible() {
      //  是否飞行检查单独处理 wtf
      return this.qualificationAndCertification?.find(item => item.value === 'Do you accept flight inspection')?.inputValue === 'not_involve'
    }
  },
  created() {
    this.recordId = this.$route.params.id
    if (this.recordId === '0'){
      this.recordId = null
    }
    this.supplierId = this.$route.query.supplierId
    this.viewOnly = this.$route.query.viewOnly
    if (this.viewOnly === 'true'){
      this.viewOnly = true
    }else if (this.viewOnly === 'false'){
      this.viewOnly = false
    }

    if (!this.supplierId) {
      this.supplierId = this.$store.getters.supplierId
    }
    this.getSupplierBase()
  },
  mounted() {
    this.recordId = this.$route.params.id
    if (this.recordId === '0'){
      this.recordId = null
    }
    this.supplierId = this.$route.query.supplierId
    this.viewOnly = this.$route.query.viewOnly
    if (this.viewOnly === 'true'){
      this.viewOnly = true
    }else if (this.viewOnly === 'false'){
      this.viewOnly = false
    }
    if (!this.supplierId) {
      this.supplierId = this.$store.getters.supplierId
    }
    this.getSupplierBase()
  },
  beforeDestroy() {
    window.onscroll = null
  },
  methods: {
    getSupplierBase() {
      getEditSystemAndQualInfoView({
        recordId: this.recordId,
        supplierId: this.supplierId
      }).then(res => {
        this.manageSystem = []

        const searchInput = (arr, dict, item) => {
          return arr.find(dic => dic.dictDataValue === item.value && dic.dictTypeValue === dict)?.value
        }
        res.data.dictRelCreateReqVO.map(item => {
          if (item.dictTypeValue === DICT_TYPE.ENTERPRISE_MANAGEMENT_SYSTEM && item.value === 'true') {
            this.manageSystem.push(item.dictDataValue)
          }
        })

        res.data.reason = res.data.dictRelCreateReqVO?.find(item => item.dictDataValue === 'Do you accept flight inspection')?.reason
        this.qualificationAndCertification.forEach(item => {
          item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.QUALIFICATION_AND_CERTIFICATION, item)
        })
        this.qualificationAndCertificationStatic.forEach(item => {
          item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.QUALIFICATION_AND_CERTIFICATION, item)
        })
        this.cetiAndQualFormStatic = deepClone(res.data)
        this.cetiAndQualForm = res.data

        this.supplierInfo = res.data

        if (this.supplierInfo.supplierCode){
          this.supplierNameAndNo = this.supplierInfo.supplierName + ' - ' + this.supplierInfo.supplierCode
        }else {
          this.supplierNameAndNo = this.supplierInfo.supplierName
        }

        if (this.recordId){
          this.editMode = false
        }
      })
    },
    closeAuditEvent() {
      cancelRecord(this.recordId).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.$tab.closeOpenPage('/supplier/info-change-record')
      })
    },
    // 供应商信息提交
    submitSupplierInfo(taskId, processInstanceId, processInstanceReason) {
      const flatDictData = (arr, dictName) => {
        return arr.map(item => {
          return {
            value: item.inputValue,
            dictDataValue: item.value,
            dictTypeValue: dictName,
            reason: this.cetiAndQualForm.reason
          }
        })
      }
      this.supplierInfo.dictRelCreateReqVO = [
        ...flatDictData(this.qualificationAndCertification, DICT_TYPE.QUALIFICATION_AND_CERTIFICATION),
        ...this.manageSystem.map(item => {
          return {
            value: true,
            dictDataValue: item,
            dictTypeValue: DICT_TYPE.ENTERPRISE_MANAGEMENT_SYSTEM
          }
        })
      ]

      this.supplierInfo.recordId = this.recordId
      this.supplierInfo.supplierId = this.supplierId
      this.supplierInfo.cleanRoom = this.cetiAndQualForm.cleanRoom
      this.supplierInfo.cleanRoomLevel = this.cetiAndQualForm.cleanRoomLevel
      this.supplierInfo.laboratoryEquipment = this.cetiAndQualForm.laboratoryEquipment
      createSystemAndQualInfoView({ ...this.supplierInfo, processInstanceId, taskId, processInstanceReason }).then(res => {
        if (res) {
          this.$message.success(this.$t('supplier.submittedSuccessfully'))
          this.$tab.closePage()
        }
      })
    },
    // 顶部取消操作
    cancel() {
      this.$tab.closePage()
    },
    formatDictValue(type) {
      return getDictDatas(type).map(item => {
        this.$set(item, 'inputValue', '')
        this.$set(item, 'reason', '')
        return {
          ...item
        }
      })
    },
    rejectAudit(taskId, processInstanceId, processInstanceReason) {
      rejectSystemAndQualInfoView({
        ...this.supplierInfo,
        taskId,
        processInstanceReason
      }).then(res => {
        if (res) {
          this.$message.success(this.$t('sl.successfullyReturned'))
          setTimeout(() => {
            this.$tab.closePage()
          }, 3000)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.baseInfo {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
    .el-form-item__content{
      width: calc(100% - 155px);
    }
  }
}

.associatedCompany {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}

.shadow {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}

.supplierStaff {
  ::v-deep .el-form-item {
    width: 100%;
    margin-right: 0;
    padding-right: 10px;
  }
}

::v-deep .el-input__inner {
  color: #606266 !important;
  //background: #ffffff!important;
}

.verticalMiddle {
  display: flex;
  align-items: center
}

::v-deep .descriptionLabel {
  width: 145px;
  font-weight: bold;
  display: inline-block;
  text-align: right;
}

.upload p {
  line-height: 12px;
}

.required:before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

.supplier {
  .tableTitle{
    padding-left: 70px;font-size:15px;font-weight: bold;margin: 20px 0;
  }
  .tablePadding{
    padding: 0 20px
  }
  .shadowPadding{
    padding: 5px 0 ;margin: 15px
  }

  .supplier-progress {
    height: 3px;
    width: 50%;
    background-color: #323232;
  }

  margin: 0 auto;
  width: 1000px;
  background: #c1c1c1;
  padding: 20px;

  .active {
    background-color: #4996b8;
    color: #fff
  }

  &-header {
    font-size: 16px;
    width: 100%;
    padding-bottom: 25px;
    margin-bottom: 20px;

    &-right {
      text-align: right;
      font-size: 12px;
      color: #173b75;
      margin-right: 20px
    }
  ;

    &-btn {
      cursor: pointer;
      padding: 10px 15px;
      display: inline-block;
      background-color: #cecece;
      color: #fff
    }
  ;
  }

  .smallInput {
    width: 161px;
  }

  .smallSelect {
    width: 188px;
  }

  .bigBaseInput {
    width: 533px
  }

  .bigInput {
    width: 591px
  }

  .colCenter {
    text-align: center;
  }

  .form-title {
    border-left: 6px solid #376092;
    margin: 10px 0;
    padding: 10px 30px;
    font-size: 16px;
    font-weight: bold;
    background-color: #f1f1f1;
  }

  // 上传输入框的文件列表样式调整#文字加粗
  ::v-deep .el-upload-list__item-name {
    font-weight: 600;
  }

  .centerRow{
    ::v-deep .el-form-item__content{
      width: calc(100% - 139px);
      text-align: center;
    }
  }
  .ellipsis-line {
    width: 248px;
    overflow: hidden;
    text-overflow: ellipsis; //文本溢出显示省略号
    white-space: nowrap; //文本不会换行
  }

  .shadow {
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }

  .rawMaterialFlex {
    flex: 0 1 15%;
    text-align: center;
  }

  .customItem {
    flex: 0 1 25%;
    text-align: center;
  }

  .form-main {
    .product {
      display: flex;
      min-height: 150px;

      .product-left {
        flex: 0 1 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 5px 10px;

        .product-title {
          flex: none;
          font-weight: bold;
          text-align: center;
          word-break: break-all;
          font-size: 20px;
          margin: 10px 0;
        }

        .product-detail {
          word-break: break-all;
          flex: auto;
          text-align: center;
        }
      }

      .product-right {
        justify-content: center;
        align-items: center;
        flex: 0 1 50%;
        border-left: 1px #dcdcdc solid;
        display: flex;
        flex-wrap: wrap;
      }
    }
  }

  .fixedBottom {
    position: fixed;
    width: calc(100% - 231px);
    bottom: 20px;
    display: flex;
    justify-content: center;
    margin-top: 40px;
    right: 30px;
  }
}
</style>
