<template>
  <div>
    <el-button
      plain
      type="primary"
      @click="dialogVisible = true"
    >{{ title }}</el-button>

    <el-dialog
      :visible.sync="dialogVisible"
      :title="title"
      width="500px"
    >
      <el-form ref="additionForm" :rules="rule" :model="addition">
        <el-form-item :label="$t('supplier.supplementaryContent')" prop="recordTypes">
          <el-select v-model="addition.recordTypes" multiple clearable>
            <el-option
              v-for="dict in supplierChangeType"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('auth.supplementaryNotes')">
          <el-input v-model="addition.additionalNotes" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="cancel">{{ $t('sp.cancel') }}</el-button>
        <el-button type="primary" @click="submit">{{ $t('common.submit') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { inviteShare } from '@/api/supplier/info'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'

export default {
  name: 'RequestAddition',
  props: {
    levelStatus: {
      type: String
    },
    supplierId: {
      type: Number,
      default: null // 供应商id
    },
    title: {
      type: String,
      default: null // dialog的标题
    }
  },
  data() {
    return {
      // 补充意见下拉框
      supplierChangeType: [],
      dialogVisible: false,
      addition: {
        recordTypes: [],
        supplierId: null,
        additionalNotes: ''
      },
      rule: {
        recordTypes: [{
          required: true,
          trigger: 'blur',
          message: this.$t('common.pleaseSelect')
        }]
      }
    }
  },
  watch: {
    levelStatus: {
      handler() {
        if (['S0', 'S1'].includes(this.levelStatus)) {
          this.supplierChangeType = getDictDatas(DICT_TYPE.SUPPLIER_CHANGE_TYPE).filter(a => a.value !== 'erp_view')
        } else {
          this.supplierChangeType = getDictDatas(DICT_TYPE.SUPPLIER_CHANGE_TYPE).filter(i => i.extendedValue === '2')
        }
      },
      immediate: true
    }
  },
  methods: {
    submit() {
      this.$refs['additionForm'].validate(valid => {
        if (valid) {
          this.addition.supplierId = this.supplierId
          inviteShare(this.addition).then(res => {
            this.$message.success(this.$t('order.operationSucceeded'))
            this.dialogVisible = false
            this.addition = {
              recordTypes: [],
              supplierId: null,
              additionalNotes: ''
            }
          })
        }
      })
    },
    cancel() {
      this.dialogVisible = false
      this.addition = {
        recordTypes: [],
        supplierId: null,
        additionalNotes: ''
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
