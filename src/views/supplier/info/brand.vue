<template>
  <div class="brandList">
    {{ $t('supplier.selectedBrands') }}：
    <el-button size="mini" style="margin-right: 10px" type="primary" @click="tags = []">{{ $t('common.emptyAll') }}</el-button>
    <el-tag
      v-for="(tag,index) in tags"
      :key="index"
      :disable-transitions="true"
      closable
      style="margin: 0 3px"

      @close="tags.splice(index,1)"
    >
      {{ tag }}
    </el-tag>
    <!--    <el-input v-model="searchKey" style="width: 600px" placeholder="请输入" />-->
    <!--    <el-button type="primary" style="margin-left: 10px" @click="init">查询</el-button>-->
    <div style="margin: 10px 0" />
    <el-link
      v-for="(item,index) in Object.keys(brandList)"
      :key="index"
      style="margin: 0 5px"
      @click="filterBrand(item)"
    >{{ item }}
    </el-link>
    <el-scrollbar style="height: 50vh">
      <div v-for="(item,index) in Object.keys(brandList)" :id="item" :key="index" class="brandList-body">
        <div>{{ item }}</div>
        <el-button
          v-for="i in brandList[item]"
          style="margin-left: 10px"
          type="text"
          @click="selectBrand(i)"
        >{{ i.name }}
        </el-button>
      </div>

    </el-scrollbar>
    <div style="text-align: right">
      <el-button type="primary" @click="confirmBrand">{{ $t('order.determine') }}</el-button>
    </div>

  </div>
</template>

<script>
import { getBrandList } from '@/api/supplier/brand'

export default {
  name: 'Brandlist',
  props: ['scope'],
  data() {
    return {
      searchKey: '',
      customBrand: true,
      customBrandName: '',
      keyList: [],
      brandList: {},
      filterBrandList: [],
      tags: []
    }
  },
  mounted() {
    if (this.scope) {
      this.tags = this.scope.split(',')
    }
    this.init()
  },
  methods: {
    async init() {
      const data = await getBrandList({})
      this.brandList = data.data
      this.filterBrand(Object.keys(this.brandList)[0])
    },
    filterBrand(key) {
      document.getElementById(key).scrollIntoView({
        behavior: 'smooth'
      })
    },
    selectBrand(item) {
      this.tags.push(item.name)
      this.tags = [...new Set(this.tags)]
    },
    confirmBrand() {
      this.$emit('closeBrand', this.tags.join(','))
    }
  }
}
</script>

<style lang="scss" scoped>
.brandList {
  &-body {
    margin: 0 10px;
  }
}
</style>
