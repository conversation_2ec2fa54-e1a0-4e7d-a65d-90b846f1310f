<template>
  <div class="form-title">{{ indexNo }} <span style="margin-left:5px">{{ title }}</span>
    <i
      v-if="isFold"
      class="el-icon-arrow-up arrowClass"
      :style="showDetailTemp? '':{transform: 'rotate(180deg)'}"
      @click="clickShow"
    />
  </div>
</template>

<script>

export default {
  props: {
    title: {
      type: String
    },
    indexNo: {
      type: String

    },
    showDetail: {
      type: Boolean,
      default: false
    },
    isFold: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showDetailTemp: false
    }
  },
  mounted() {
    if (this.showDetail) {
      this.showDetailTemp = this.showDetail
    }
  },
  methods: {
    clickShow() {
      this.showDetailTemp = !this.showDetailTemp
      this.$emit('update:showDetail', this.showDetailTemp)
    }
  }
}
</script>

<style lang="scss" scoped>
.arrowClass {
  font-size: 18px;
  cursor: pointer;
  float: right;
  transition: transform .3s;
  margin-left: 7px;
  color: #4185A3;
  font-weight: bold;
}

.form-title {
  border-left: 6px solid #376092;
  margin: 10px 0;
  padding: 10px 30px;
  font-size: 16px;
  font-weight: bold;
  background-color: #f1f1f1;
}
</style>
