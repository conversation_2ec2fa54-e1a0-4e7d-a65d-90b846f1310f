<template>
  <div class="supplier">
    <sticky :sticky-top="$store.getters.fixedHeader&&$store.getters.nickname !=='anonymous' ? 86 : 0" :z-index="100">
      <div class="supplier-header" style="background:#fff;">
        <div style="text-align:center;margin-top:10px; position: relative; top: 12px;">
          <div
            :class="{active: getCurentPosition === 0}"
            class="supplier-header-btn"
            @click="toMark(basicPos)"
          >
            <a>{{ $t('supplier.essentialInformation') }}</a>
          </div>
          <div
            :class="{active: getCurentPosition === 1}"
            class="supplier-header-btn"
            @click="toMark(businessPos)"
          >
            <a>{{ $t('supplier.businessInformation') }}</a>
          </div>
          <div
            :class="{active: getCurentPosition === 2}"
            class="supplier-header-btn"
            @click="toMark(categroyPos)"
          >
            <a>{{ $t('supplier.productsAndProcesses') }}</a>

          </div>
          <div
            :class="{active: getCurentPosition === 3}"
            class="supplier-header-btn"
            @click="toMark(qualityPos)"
          >
            <a>{{ $t('supplier.managementSystem') }}</a>
          </div>
          <div
            :class="{active: getCurentPosition === 4}"
            class="supplier-header-btn"
            @click="toMark(documentPos)"
          >
            <a>{{ $t('supplier.documentLibrary') }}</a>
          </div>
          <div
            v-show="!supplierInfo.isSupplier"
            :class="{active: getCurentPosition === 5}"
            class="supplier-header-btn"
            @click="toMark(erpPos)"
          >
            <a>{{ $t('supplier.erpLoginInformation') }}</a>
          </div>
          <div
            class="supplier-header-btn"
            style="background: transparent;color: #4996b8"
          >
            <span style="margin-right: 15px"> {{ $t('supplier.completed') }}{{ infoPercent }}%</span>
            <el-button
              v-if="editMode"
              plain
              size="mini"
              @click="cancel"
            >{{
              $t('common.cancel')
            }}
            </el-button>
            <el-button
              v-if="!editMode&&editPer"
              v-hasPermi="['supplier:base-info:update']"
              plain
              size="mini"
              type="primary"
              @click="enterEdit"
            >{{ $t('supplier.enterEdit') }}
            </el-button>
            <el-button
              v-if="editPer&&editMode"
              v-hasPermi="['supplier:base-info:submit']"
              size="mini"
              type="primary"
              @click="submitSupplierInfo"
            >{{ $t('common.submit') }}
            </el-button>
            <el-button
              v-if="editPer&&editMode"
              v-hasPermi="['supplier:base-info:update']"
              plain
              size="mini"
              @click="saveSupplierInfoEvent"
            >{{ $t('common.save') }}
            </el-button>

          </div>

        </div>
      </div>
    </sticky>

    <div v-if="supplierShow" style="background-color: white">
      <div id="basic" class="form-title">1.1 <span style="margin-left:5px">{{
        $t('supplier.companyInformation')
      }}</span>
        <div style="float: right">
          <el-button
            v-if="viewOnly && supplierInfo.status === 2"
            v-hasPermi="['supplier:edit:baseinfo']"
            plain
            size="mini"
            type="primary"
            @click="$router.push(`/supplier/BaseInfo/0?supplierId=${supplierId}&viewOnly=false`)"
          >{{ $t('supplier.modifyBasicInformation') }}
          </el-button>
        </div>
      </div>
      <div style="padding: 10px 126px">
        <el-form
          ref="baseInfo"
          :model="supplierInfo"
          :rules="supplierInfoRule"
          class="baseInfo"
          inline
          label-width="155px"
        >
          <el-form-item
            ref="name"
            :label="$t('supplier.supplierName')"
            prop="name"
          >
            <show-or-edit
              :disabled="lockWhenIsResource||!editMode"
              :value="supplierInfo.name"
            >
              <el-input v-model="supplierInfo.name" />
            </show-or-edit>
            <el-button
              v-if="editPer&&editMode&&$store.getters.creditModule"
              size="mini"
              type="primary"
              @click="getTYCData()"
            >{{
              $t('supplier.obtainTianyancha')
            }}
            </el-button>
          </el-form-item>
          <el-form-item ref="nameEn" :label="$t('supplier.supplierNameEn')" prop="nameEn">
            <show-or-edit :value="supplierInfo.nameEn"
                          :disabled="!editMode"
            >
              <el-input v-model="supplierInfo.nameEn" />
            </show-or-edit>
          </el-form-item>
          <!--          20221019 供应商简称和编码仅登记展示，不可编辑。待其他模块对接过来-->
          <el-form-item ref="nameShort" :label="$t('supplier.shortNameOfSupplier')" prop="nameShort">
            <show-or-edit :value="supplierInfo.nameShort"
                          :disabled="!editMode"
            >
              <el-input v-model="supplierInfo.nameShort" />
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.supplierCode')">
            <show-or-edit :value="supplierInfo.code"
                          :disabled="!editMode"
            >
              <el-input v-model="supplierInfo.code" />
            </show-or-edit>
          </el-form-item>
          <el-form-item
            ref="registeredAddress"
            :label="$t('supplier.registeredAddressOfTheCompany')"
            prop="registeredAddress"
          >
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.registeredAddress"
            >
              <el-input v-model="supplierInfo.registeredAddress" />
            </show-or-edit>
          </el-form-item>
          <el-form-item
              ref="registeredAddressSub"
              :label="$t('supplier.registeredAddressOfTheCompany2')"
              prop="registeredAddressSub"
          >
            <show-or-edit
                :disabled="!editMode"
                :value="supplierInfo.registeredAddressSub"
            >
              <el-input v-model="supplierInfo.registeredAddressSub" />
            </show-or-edit>
          </el-form-item>

          <el-form-item
            ref="countryRegion"
            :label="$t('supplier.countryregion')"
            prop="countryRegion"
          >
            <show-or-edit
              :dict="DICT_TYPE.COMMON_COUNTRY"
              :disabled="!editMode"
              :value="supplierInfo.countryRegion"
            >
              <el-select
                v-model="supplierInfo.countryRegion"
                class="smallSelect"
                clearable
                filterable
                remote
                @change="(val)=>{changeRegion(val)}"
              >
                <el-option v-for="re in region" :key="re.keyId" :label="re.name" :value="re.id" />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            ref="creditCode"
            :label="$t('supplier.unifiedSocialCreditCode')"
            prop="creditCode"
          >
            <show-or-edit
              :disabled="lockWhenIsResource||!editMode"
              :value="supplierInfo.creditCode"
            >
              <el-input v-model="supplierInfo.creditCode" />
            </show-or-edit>
          </el-form-item>

          <el-form-item
            ref="provinceCity"
            :label="$t('supplier.provinceCity')"
            prop="provinceCity"
          >
            <show-or-edit
              :dict="DICT_TYPE.COMMON_COUNTRY"
              :disabled="!editMode"
              :value="supplierInfo.provinceCity"
            >
              <el-select v-model="supplierInfo.provinceCity" class="smallSelect" filterable>
                <el-option v-for="re in province" :key="re.keyId" :label="re.name" :value="re.id" />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            ref="registeredCapital"
            :label="$t('supplier.registeredCapital')"
            prop="registeredCapital"
          >
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.registeredCapital"
            >
              <vxe-input v-model="supplierInfo.registeredCapital" class="smallSelect" max="99999999999.99" min="0" type="number" />
            </show-or-edit>
          </el-form-item>

          <el-form-item
            ref="street"
            :label="$t('supplier.street')"
            prop="street"
          >
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.street"
            >
              <el-input v-model="supplierInfo.street" style="width: 129px" />
              <el-button v-if="editPer&&editMode" size="mini" type="primary" @click="cpStreet()">{{
                $t('supplier.ditto')
              }}
              </el-button>
            </show-or-edit>
          </el-form-item>
          <el-form-item
            ref="registeredCapitalCurrency"
            :label="$t('supplier.registeredCapitalCurrency')"
            prop="registeredCapitalCurrency"
          >
            <show-or-edit
              :dict="DICT_TYPE.COMMON_CURRENCY"
              :disabled="!editMode"
              :value="supplierInfo.registeredCapitalCurrency"
            >

              <el-select
                v-model="supplierInfo.registeredCapitalCurrency"
                class="smallSelect"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>

          <el-form-item
            ref="companyCategory"
            :label="$t('supplier.companyType')"
            prop="companyCategory"
          >
            <show-or-edit
              :dict="DICT_TYPE.SUPPLIER_TYPE"
              :disabled="!editMode"
              :value="supplierInfo.companyCategory"
            >

              <el-select
                v-model="supplierInfo.companyCategory"
                :placeholder="$t('supplier.pleaseSelectStatus')"
                class="smallSelect"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>

          <el-form-item :label="$t('supplier.companyWebsite')" style="width: 100%">
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.webSite"
            >
              <el-input v-model="supplierInfo.webSite" class="bigBaseInput" />
            </show-or-edit>
          </el-form-item>

          <el-form-item :label="$t('supplier.zipCode')">
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.zipCode"
            >

              <el-input v-model="supplierInfo.zipCode" />

            </show-or-edit>
          </el-form-item>
          <div>
            <el-form-item ref="establishedTime" :label="$t('supplier.establishedTime')" prop="establishedTime">
              <show-or-edit
                :disabled="!editMode"
                :value="supplierInfo.establishedTime"
                type="Date"
              >
                <el-date-picker
                  v-model="supplierInfo.establishedTime"
                  :placeholder="$t('common.pleaseSelectADate')"
                  class="smallSelect"
                  type="date"
                />
              </show-or-edit>
            </el-form-item>
            <span
              style="float: right;color: #4185A3;cursor:pointer;"
              @click="showDetail= !showDetail"
            >{{ $t('system.more') }}
              <i
                :style="showDetail? '':{transform: 'rotate(180deg)'}"
                class="el-icon-arrow-up"
                style="font-size: 18px;float: right;transition: transform .3s;
                margin-left: 7px;
          font-weight: bold;"
              />
            </span>
          </div>

          <!--          <el-form-item-->
          <!--            ref="telephone"-->
          <!--            :label="$t('supplier.switchboard')"-->
          <!--            prop="telephone"-->
          <!--          >-->
          <!--            <show-or-edit-->
          <!--              :disabled="!editMode"-->
          <!--              :value="supplierInfo.telephone"-->
          <!--            >-->
          <!--              <el-input v-model="supplierInfo.telephone" />-->
          <!--            </show-or-edit>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item-->
          <!--            ref="fax"-->
          <!--            :label="$t('supplier.generalFax')"-->
          <!--            prop="fax"-->
          <!--          >-->
          <!--            <show-or-edit-->
          <!--              :disabled="!editMode"-->
          <!--              :value="supplierInfo.fax"-->
          <!--            >-->
          <!--              <el-input v-model="supplierInfo.fax" />-->
          <!--            </show-or-edit>-->
          <!--          </el-form-item>-->

          <div
            v-for="(item,index) in supplierInfo.factoryAddressCreateReqVO"
            v-show="showDetail"
            :key="index"
            class="shadow"
            style="padding: 10px 0 5px 0;margin: 10px 0 "
          >
            <div>
              <el-form-item :label="$t('supplier.factoryAddress')">
                <div class="verticalMiddle">
                  <el-button v-if="editPer&&editMode" size="mini" type="primary" @click="cpAddress(item)">{{
                    $t('supplier.ditto')
                  }}
                  </el-button>
                  <i
                    v-if="editMode"
                    class="el-icon-circle-plus"
                    style="margin-left:10px;font-size: 18px;cursor: pointer"
                    @click="supplierInfo.factoryAddressCreateReqVO.push({
                      countryRegion: '',
                      street: '',
                      provinceCity: '',
                      zipCode: '',
                      province:[]

                    })"
                  />
                  <i
                    v-if="editMode"
                    class="el-icon-remove"
                    style="margin-left:10px;font-size: 18px;cursor: pointer"
                    @click="supplierInfo.factoryAddressCreateReqVO.length===1?
                      supplierInfo.factoryAddressCreateReqVO= [
                        {
                          countryRegion: '',
                          street: '',
                          provinceCity: '',
                          zipCode: '',
                          province: []
                        }
                      ]
                      :supplierInfo.factoryAddressCreateReqVO.splice(index,1)"
                  />
                </div>
              </el-form-item>
            </div>
            <el-form-item :label="$t('supplier.countryregion')">
              <show-or-edit
                :dict="DICT_TYPE.COMMON_COUNTRY"
                :disabled="!editMode"
                :value="item.countryRegion"
              >
                <el-select
                  ref="mainFactory"
                  v-model="item.countryRegion"
                  class="smallSelect"
                  clearable
                  filterable
                  remote
                  @change="(val)=>{changeRegion(val,item)}"
                >
                  <el-option v-for="re in region" :key="re.keyId" :label="re.name" :value="re.id" />
                </el-select>
              </show-or-edit>

            </el-form-item>
            <el-form-item :label="$t('supplier.street')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.street"
              >
                <el-input v-model="item.street" />

              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.provinceCity')">
              <show-or-edit
                :dict="DICT_TYPE.COMMON_COUNTRY"
                :disabled="!editMode"
                :value="item.provinceCity"
              >
                <el-select v-model="item.provinceCity" :disabled="!editMode" class="smallSelect" filterable>
                  <el-option v-for="re in item.province" :key="re.keyId" :label="re.name" :value="re.id" />
                </el-select>
              </show-or-edit>

            </el-form-item>
            <el-form-item :label="$t('supplier.zipCode')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.zipCode"
              >
                <el-input v-model="item.zipCode" :disabled="!editMode" />

              </show-or-edit>
            </el-form-item>
          </div>

        </el-form>
      </div>
      <div class="form-title">1.2 <span style="margin-left:5px">{{ $t('supplier.relatedCompanyInformation') }}</span>
        <i
          :style="showDetail1? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail1= !showDetail1"
        />
      </div>
      <div
        v-show="showDetail1"

        v-disable-all="!editMode"
        class="form-main"
        style="padding: 10px 126px"
      >
        <el-form class="associatedCompany" inline label-width="155px">
          <div
            v-for="(item,index) in supplierInfo.associatedCompanyCreateReqVO"
            :key="index"
            class="shadow"
            style="padding: 10px 0 5px 0;margin: 10px 0 "
          >
            <div>
              <el-form-item :label="$t('system.companyName')" style="width: 100%">
                <show-or-edit
                  :disabled="!editMode"
                  :value="item.name"
                >
                  <div class="verticalMiddle">

                    <el-input v-model="item.name" />
                    <i
                      v-if="editMode"

                      class="el-icon-circle-plus"
                      style="margin-left:10px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.associatedCompanyCreateReqVO.push({
                        relationship: '',
                        countryRegion: '',
                        street: '',
                        provinceCity: '',
                        zipCode: '',
                        province: []
                      })"
                    />
                    <i
                      v-if="editMode"

                      class="el-icon-remove"
                      style="margin-left:10px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.associatedCompanyCreateReqVO.length===1?
                        supplierInfo.associatedCompanyCreateReqVO= [
                          {
                            relationship: '',
                            countryRegion: '',
                            street: '',
                            provinceCity: '',
                            zipCode: '',
                            province: []
                          }
                        ]
                        :supplierInfo.associatedCompanyCreateReqVO.splice(index,1)"
                    />

                  </div>
                </show-or-edit>

              </el-form-item>
            </div>

            <el-form-item :label="$t('supplier.countryregion')">
              <show-or-edit
                :dict="DICT_TYPE.COMMON_COUNTRY"
                :disabled="!editMode"
                :value="item.countryRegion"
              >
                <el-select
                  v-model="item.countryRegion"
                  :disabled="!editMode"
                  class="smallSelect"
                  clearable
                  filterable
                  remote
                  @change="(val)=>{changeRegion(val,item)}"
                >
                  <el-option v-for="re in region" :key="re.keyId" :label="re.name" :value="re.id" />
                </el-select>
              </show-or-edit>

            </el-form-item>
            <el-form-item :label="$t('supplier.relationshipWithTheMainCompany')">
              <show-or-edit
                :dict="DICT_TYPE.SUPPLIER_WITH_SUPPLIER_REL"
                :disabled="!editMode"
                :value="item.relationship"
              >
                <el-select
                  v-model="item.relationship"
                  :disabled="!editMode"
                  :placeholder="$t('supplier.pleaseSelectStatus')"
                  class="smallSelect"
                  clearable
                >
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_WITH_SUPPLIER_REL)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>

              </show-or-edit>
            </el-form-item>

            <el-form-item :label="$t('supplier.provincesAndCities')">
              <show-or-edit
                :dict="DICT_TYPE.COMMON_COUNTRY"
                :disabled="!editMode"
                :value="item.provinceCity"
              >
                <el-select v-model="item.provinceCity" :disabled="!editMode" class="smallSelect">
                  <el-option v-for="re in item.province" :key="re.keyId" :label="re.name" :value="re.id" />
                </el-select>
              </show-or-edit>

            </el-form-item>
            <el-form-item :label="$t('supplier.street')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.zipCode"
              >
                <el-input v-model="item.street" :disabled="!editMode" />

              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.zipCode')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.zipCode"
              >
                <el-input v-model="item.zipCode" :disabled="!editMode" />

              </show-or-edit>
            </el-form-item>

          </div>
        </el-form>

      </div>

      <div class="form-title">1.3 <span style="margin-left:5px">{{ $t('supplier.personnelInformation') }}</span></div>
      <div v-disable-all="!editMode" class="form-main">
        <el-form
          ref="personInfo"
          :model="supplierInfo"
          :rules="supplierInfoRule"
          class="shadow"
          inline
          label-width="185px"
          style="padding: 10px 0 5px 0;margin: 15px "
        >

          <el-form-item
            ref="legalPerson"
            :label="$t('supplier.legalRepresentative')"
            prop="legalPerson"
          >
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.legalPerson"
            >
              <el-input v-model="supplierInfo.legalPerson" />
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.mailbox')" label-width="50px" prop="notRequiredEmail">
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.email"
            >
              <el-input v-model="supplierInfo.email" />
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.position')">
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.position"
            >
              <el-input v-model="supplierInfo.position" />
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.telephone')" label-width="50px">
            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.phone"
            >
              <el-input v-model="supplierInfo.phone" />
            </show-or-edit>
          </el-form-item>

        </el-form>
        <div class="shadow shadowPadding">
          <div class="required tableTitle">
            {{ $t('supplier.primaryContact') }}
          </div>
          <div class="tablePadding">
            <el-table
              :data="supplierInfo.contactCreateReqVO"
            >
              <el-table-column :label="$t('supplier.fullName')" prop="name">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.name"
                  >
                    <el-input ref="mainContact" v-model="scope.row.name" />
                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.position')" prop="position">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.position"
                  >
                    <el-input v-model="scope.row.position" />
                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.contactNumber')" prop="phone">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.phone"
                  >
                    <el-input v-model="scope.row.phone" />
                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column min-width="110" prop="email">
                <template #header>
                  <div class="required">{{ $t('supplier.email') }}</div>
                </template>
                <template slot-scope="scope">
                  <el-form :ref="`contactItem${scope.$index}`" :model="scope.row">
                    <el-form-item
                      :rules=" { required: true, type: 'email', message: $t('supplier.pleaseEnterTheCorrectEmailAddress'), trigger: ['blur'] } "
                      prop="email"
                      style="width: 100%;margin: auto !important;"
                    >
                      <show-or-edit
                        :disabled="!editMode"
                        :value="scope.row.email"
                      >
                        <el-input v-model="scope.row.email" />
                      </show-or-edit>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.contactDivision')" min-width="160" prop="division">
                <template #header>
                  <div class="required">{{ $t('supplier.contactDivision') }}</div>
                </template>
                <template slot-scope="scope">
                  <el-form :ref="`contactItemDivision${scope.$index}`" :model="scope.row">
                    <el-form-item
                      :rules=" { required: true, message: $t('common.pleaseEnter'), trigger: [ 'change'] } "
                      prop="division"
                      style="width: 100%;margin: auto !important;"
                    >
                      <show-or-edit
                        :dict="DICT_TYPE.SUPPLIER_CONTACT_DIVISION"
                        :disabled="!editMode"
                        :value="scope.row.division"
                      >
                        <el-select v-model="scope.row.division" clearable multiple :placeholder="$t('supplier.multipleOptionsAvailable')" style="width:100%">
                          <el-option
                            v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CONTACT_DIVISION)"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>
                      </show-or-edit>
                    </el-form-item>
                  </el-form>

                </template>
              </el-table-column>
              <el-table-column :v-if="editMode" prop="op" width="80">
                <template slot-scope="scope">
                  <div style="text-align: right;margin-top: 5px">
                    <i
                      v-if="editMode"
                      class="el-icon-circle-plus"
                      style="margin-left:2px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.contactCreateReqVO.push({name: '',position: '',phone: '',email: '',division: [],businessDictRelStr: ''})"
                    />
                    <i
                      v-if="editMode"
                      class="el-icon-remove"
                      style="margin-left:5px;font-size: 18px;cursor: pointer"
                      @click="
                        supplierInfo.contactCreateReqVO.length===1?
                          supplierInfo.contactCreateReqVO=[{name: '',position: '',phone: '',email: '',division: [],businessDictRelStr: ''}]:supplierInfo.contactCreateReqVO.splice(scope.$index,1)"
                    />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="shadow shadowPadding">
          <div class="tableTitle">{{ $t('supplier.shareholderInformation') }}
            <span style="float: right;color: #4185A3;cursor:pointer;font-weight: 400;margin-right: 50px" @click="showDetail0= !showDetail0">
              {{ $t('system.more') }}
              <i
                :style="showDetail0? '':{transform: 'rotate(180deg)'}"
                class="el-icon-arrow-up"
                style="font-size: 18px;float: right;transition: transform .3s;margin-left: 7px;font-weight: bold;"
              />
            </span>
          </div>
          <div v-show="showDetail0" class="tablePadding">
            <el-table :data="supplierInfo.shareholdersCreateReqVO">
              <el-table-column :label="$t('supplier.nameDepartment') " prop="name">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.name"
                  >
                    <el-input v-model="scope.row.name" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.shareProportion') " prop="stake">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.stake"
                  >
                    <el-input-number
                      v-model="scope.row.stake"
                      :max="100"
                      :min="0"
                      class="smallInput"
                      style="margin-right: 5px"
                    />
                  </show-or-edit>
                  %
                </template>
              </el-table-column>
              <el-table-column :v-if="editMode" prop="op" width="80">
                <div style="text-align: right;margin-top: 5px">
                  <i
                    v-if="editMode"
                    class="el-icon-circle-plus"
                    style="margin-left:2px;font-size: 18px;cursor: pointer"
                    @click="supplierInfo.shareholdersCreateReqVO.push({stake: '',name: '',})"
                  />
                  <i
                    v-if="editMode"
                    class="el-icon-remove"
                    style="margin-left:5px;font-size: 18px;cursor: pointer"
                    @click="supplierInfo.shareholdersCreateReqVO.length===1?supplierInfo.shareholdersCreateReqVO=[{name: '',stake: ''}]
                      :supplierInfo.shareholdersCreateReqVO.splice(index,1)"
                  />
                </div>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="shadow shadowPadding">
          <div class="tableTitle">{{ $t('supplier.staffInfo') }}
            <span style="float: right;color: #4185A3;cursor:pointer;font-weight: 400;margin-right: 50px" @click="showDetail11= !showDetail11">
              {{ $t('system.more') }}
              <i
                :style="showDetail11? '':{transform: 'rotate(180deg)'}"
                class="el-icon-arrow-up"
                style="font-size: 18px;float: right;transition: transform .3s;margin-left: 7px;font-weight: bold;"
              />
            </span>
          </div>
          <div v-show="showDetail11" style="margin:10px 0;padding-left: 70px;display: flex;justify-content: space-around;font-size: 14px;">
            <div>{{ $t('supplier.numberOfEmployees') }}</div>
            <div>{{ $t('supplier.qualificationCertificateOfSpecialTypeOfWork') }}</div>
            <div>{{ $t('supplier.rDPersonnelQualifications') }}</div>
          </div>
          <el-form v-show="showDetail11" class="supplierStaff" inline label-width="139px">
            <el-row>
              <el-col :span="8">
                <el-form-item
                  v-for="dict in supplierStaff"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                  class="centerRow"
                  style="font-weight: normal;width: 100%"
                >

                  <show-or-edit
                    :disabled="!editMode"
                    :value="dict.inputValue"
                  >
                    <el-input-number v-model="dict.inputValue" :min="0" class="smallInput" />

                  </show-or-edit>

                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  v-for="dict in supplierCertificate"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                  class="centerRow"
                >
                  <show-or-edit
                    :disabled="!editMode"
                    :value="dict.inputValue"
                  >
                    <el-input-number v-model="dict.inputValue" :min="0" class="smallInput" />

                  </show-or-edit>

                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  v-for="dict in supplierDev "
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                  class="centerRow"
                >
                  <show-or-edit
                    :disabled="!editMode"
                    :value="dict.inputValue"
                  >
                    <el-input-number
                      v-model="dict.inputValue"
                      :max="100"
                      :min="0"
                      style="width: 145px"
                    />
                  </show-or-edit>

                  %

                </el-form-item>
              </el-col>

            </el-row>

          </el-form>
        </div>
      </div>
      <div class="form-title">1.4
        <span style="margin-left:5px">{{ $t('supplier.paymentInformation') }}</span>
        <div style="float: right">
          <!--
【XUERES-910】验证该供应商目前是否处于准入申请提交审核阶段
  1.外部用户进入认证审批后一律不可以修改
  2.内部用户走按钮权限配置
  默认允许编辑，仅外部用户进入判断
-->
          <el-button
            v-hasPermi="['supplier:base-info:query']"
            plain
            size="mini"
            type="primary"
            @click="log.open=true"
          >
            {{ $t('supplier.modificationRecord') }}
          </el-button>
          <el-button
            v-if="editPer && paymentInfo.paymentInfoEdit"
            v-hasPermi="['supplier:payment-info:update']"
            plain
            size="mini"
            type="primary"
            @click="paymentVisible = true"
          >{{ $t('supplier.editControlledInformation') }}
          </el-button>
          <el-button
            v-if="viewOnly && supplierInfo.status === 2"
            v-hasPermi="['supplier:edit:bankinfo']"
            plain
            size="mini"
            type="primary"
            @click="$router.push(`/supplier/bank/0?supplierId=${supplierId}&viewOnly=false`)"
          >{{ $t('supplier.modifyBankInformation') }}
          </el-button>
        </div>
      </div>
      <div v-disable-all="!editMode" class="form-main" style="padding: 10px 126px">
        <el-descriptions :column="2" label-class-name="descriptionLabel" size="medium">
          <el-descriptions-item :label="$t('supplier.unifiedSocialCreditCode')">
            {{ supplierInfo.creditCode }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.paymentMethod')">
            <dict-tag :type="DICT_TYPE.SUPPLIER_TERMS_OF_PAYMENT" :value="paymentInfoStatic.paymentType" />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.taxRate')">
            <dict-tag :type="DICT_TYPE.SUPPLIER_RATE" :value="paymentInfoStatic.taxRate" />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.invoiceType')">
            <dict-tag :type="DICT_TYPE.SUPPLIER_INVOICE_TYPE" :value="paymentInfoStatic.invoiceType" />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.termOfPayment')">
            <dict-tag :type="DICT_TYPE.SUPPLIER_PAYMENT_DAYS" :value="paymentInfoStatic.accountDay" />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.serviceChargePayer')">
            <dict-tag :type="DICT_TYPE.SUPPLIER_FEE_BEARER" :value="paymentInfoStatic.feeBearer" />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.deliveryTerms')">
            <dict-tag :type="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION" :value="paymentInfoStatic.deliveryType" />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.countryregion')">
            <dict-tag :type="DICT_TYPE.COMMON_COUNTRY" :value="supplierInfo.countryRegion" />
          </el-descriptions-item>

        </el-descriptions>
        <el-descriptions
          v-for="(item,index) in paymentInfoStatic.bankInfoCreateReqVO"
          :key="index"
          :column="2"
          label-class-name="descriptionLabel"
          size="medium"
        >
          <el-descriptions-item :label="$t('system.currency')">
            <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="item.currency" />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.invoiceAddress')">
            {{ item.invoiceAddress }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.bankOfDeposit')" :span="2">
            {{ item.bankName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.branchBranchName')" :span="2">
            {{ item.subbranchName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.bankAccount')" :span="2">
            {{ item.bankAccount }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.accountName')" :span="2">
            {{ item.accountName }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="SWIFT Code">
            {{ item.swiftCode }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.bankNo')" :span="2">
            {{ item.bankNo }}
          </el-descriptions-item>

          <el-descriptions-item :label="$t('supplier.bankInformationWithOfficialSeal')" :span="2">
            <div style="display: flex;justify-content: space-between">

              <!--              <el-upload-->
              <!--                class="upload-demo"-->
              <!--                :action="uploadUrl"-->
              <!--                :headers="upload.headers"-->
              <!--                :before-upload="beforeUpload"-->
              <!--                :on-remove="(file,fileList)=>onRemove(file,bankFileList,item)"-->
              <!--                :on-preview="onPreview"-->
              <!--                :on-success="(response, file, fileList)=>onBankInfoFileSuccess(response, file, item)"-->
              <!--                multiple-->
              <!--                :limit="5"-->
              <!--                :show-file-list="false"-->
              <!--                :file-list="item.fileRelList"-->
              <!--              >-->
              <!--                <el-button-->
              <!--                  v-if="!viewOnly"-->
              <!--                  class="uploadBtn"-->
              <!--                  size="small"-->
              <!--                  plain-->
              <!--                  icon="el-icon-plus"-->
              <!--                  type="primary"-->
              <!--                />-->
              <!--              </el-upload>-->
              <div style="margin-left: 15px">

                {{ $t('scar.viewAttachments') }}
                <el-button
                  class="uploadBtn"
                  size="small"
                  style="padding: 5px 9px"
                  :disabled="item.fileRelList?.length===0"
                  plain
                  :type="item.fileRelList?.length?'primary':''"
                  @click="item.showFile=true"
                >
                  {{ item.fileRelList?.length }}
                </el-button>

                <el-dialog
                  v-if="item.showFile"
                  :visible.sync="item.showFile"
                  :title="$t('scar.viewAttachments')"
                  append-to-body
                  width="400px"
                >
                  <el-upload
                    class="upload-show"
                    :action="uploadUrl"
                    :headers="upload.headers"
                    :before-upload="beforeUpload"
                    :on-remove="(file,fileList)=>onRemove(file,bankFileList,item)"
                    :on-preview="onPreview"
                    :on-success="(response, file, fileList)=>onBankInfoFileSuccess(response, file, item,)"
                    multiple
                    :limit="5"
                    :file-list="item.fileRelList"
                  />
                  <div slot="footer">
                    <el-button type="primary" @click="item.showFile=false">确定</el-button>
                  </div>

                </el-dialog>
              </div>
            </div>

          </el-descriptions-item>

        </el-descriptions>

      </div>

      <div id="business" class="form-title">2.1 <span style="margin-left:5px">{{
        $t('supplier.businessInformation')
      }}</span>
        <i
          :style="showDetail2? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail2= !showDetail2"
        /></div>
      <div
        v-show="showDetail2"

        v-disable-all="!editMode"
        class="form-main"
      >
        <el-form class="shadow" inline label-width="155px" style="padding: 0 100px">
          <el-form-item :label="$t('supplier.areaCovered')">

            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.coversArea"
            >

              <el-input-number v-model="supplierInfo.coversArea" :min="0" class="smallInput" />

            </show-or-edit>

            {{ $t('supplier.squareMeter') }}
          </el-form-item>
          <el-form-item :label="$t('supplier.propertyRightOfPlant')">
            <show-or-edit
              :dict="DICT_TYPE.SUPPLIER_BUILDING_PROPERTY_RIGHT"
              :disabled="!editMode"
              :value="supplierInfo.buildProperty"
            >
              <el-select
                v-model="supplierInfo.buildProperty"

                :placeholder="$t('supplier.pleaseSelectStatus')"
                class="smallSelect"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_BUILDING_PROPERTY_RIGHT)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.productionArea')">

            <show-or-edit
              :disabled="!editMode"
              :value="supplierInfo.productionArea"
            >

              <el-input-number v-model="supplierInfo.productionArea" :min="0" class="smallInput" />

            </show-or-edit>
            {{ $t('supplier.squareMeter') }}
          </el-form-item>
          <el-form-item :label="$t('supplier.natureOfTheCompany')">
            <show-or-edit
              :dict="DICT_TYPE.SUPPLIER_COMPANY_NATURE"
              :disabled="!editMode"
              :value="supplierInfo.companyNature"
            >
              <el-select
                v-model="supplierInfo.companyNature"
                :disabled="!editMode"
                :placeholder="$t('supplier.pleaseSelectStatus')"
                class="smallSelect"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_COMPANY_NATURE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>

          </el-form-item>
          <el-form-item :label="$t('supplier.importAndExportQualification')">
            <show-or-edit
              :dict="DICT_TYPE.COMMON_Y_N"
              :disabled="!editMode"
              :value="supplierInfo.exportQualification"
            >
              <el-select
                v-model="supplierInfo.exportQualification"
                :disabled="!editMode"
                :placeholder="$t('supplier.pleaseSelectStatus')"
                class="smallSelect"
                clearable
                style="width:161px"
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_Y_N)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>

          </el-form-item>

        </el-form>
      </div>
      <div class="form-title">2.2 <span style="margin-left:5px">{{ $t('supplier.salesInformation') }}</span>
        <i
          :style="showDetail3? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail3= !showDetail3"
        />
      </div>
      <div
        v-show="showDetail3"

        v-disable-all="!editMode"
        class="form-main"
      >
        <div
          class="shadow shadowPadding"
        >
          <div class="tableTitle"> {{ $t('supplier.turnoverinRecentYears') }}</div>
          <div class="tablePadding">
            <el-table :data="supplierInfo.turnoverCreateReqVO">
              <el-table-column :label="$t('supplier.year')" prop="year" />
              <el-table-column :label="$t('supplier.money')" prop="turnover">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.turnover"
                    class="smallInput"
                  >
                    <vxe-input
                      v-model.number="scope.row.turnover"
                      :digits="2"
                      :placeholder="$t('supplier.enterNumber')"
                      class="smallInput"
                      max="999999999.99"
                      min="0"
                      type="float"
                    />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('sp.unit')" prop="unit">
                <template slot-scope="scope">
                  <show-or-edit
                    :dict="DICT_TYPE.SUPPLIER_CURRENCY_UNIT"
                    :disabled="!editMode"
                    :value="scope.row.unit"
                    class="smallInput"
                  >
                    <el-select
                      v-model="scope.row.unit"
                      :disabled="!editMode"
                      class="smallInput"
                      filterable
                      style="margin: 0 10px"
                    >
                      <el-option
                        v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CURRENCY_UNIT)"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('system.currency')" prop="currency">
                <template slot-scope="scope">
                  <show-or-edit
                    :dict="DICT_TYPE.COMMON_CURRENCY"
                    :disabled="!editMode"
                    :value="scope.row.currency"
                    class="smallInput"
                  >
                    <el-select v-model="scope.row.currency" :disabled="!editMode" filterable>
                      <el-option
                        v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                        :key="dict.value"
                        :label="dict.name"
                        :value="dict.id"
                      />
                    </el-select>
                  </show-or-edit>
                </template>
              </el-table-column>
            </el-table>
          </div>

        </div>
        <div
          class="shadow shadowPadding"
        >
          <div class="tableTitle">{{ $t('supplier.mainCustomers') }}</div>
          <div class="tablePadding">
            <el-table :data="supplierInfo.customerCreateReqVO">
              <el-table-column :label="$t('supplier.customerName')" prop="name">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.name"
                    class="customItem"
                  >
                    <el-input v-model="scope.row.name" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.endCustomer')" prop="terminalCustomer">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.terminalCustomer"
                    class="customItem"
                  >
                    <el-input v-model="scope.row.terminalCustomer" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.industry')" prop="industry">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.industry"
                    class="customItem"
                  >
                    <el-input v-model="scope.row.industry" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.proportion')" prop="proportion">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.proportion"
                    class="customItem"
                  >
                    <el-input-number
                      v-model="scope.row.proportion"
                      :max="100"
                      :min="0"
                      style="margin: 0 10px;width: 80%"
                    />

                  </show-or-edit>
                  %
                </template>
              </el-table-column>
              <el-table-column prop="op" width="80">
                <template slot-scope="scope">
                  <div style="flex: 0 0 5%;display:flex;width: 60px;text-align: right">
                    <i
                      v-if="editMode"

                      class="el-icon-circle-plus"
                      style="margin-left:2px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.customerCreateReqVO.push({
                        terminalCustomer: '',
                        name: '',
                        industry: '',
                        proportion: '',
                      })"
                    />
                    <i
                      v-if="editMode"

                      class="el-icon-remove"
                      style="margin-left:5px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.customerCreateReqVO.length===1?
                        supplierInfo.customerCreateReqVO= [
                          {
                            terminalCustomer: '',
                            name: '',
                            industry: '',
                            proportion: '',
                          }
                        ]
                        :supplierInfo.customerCreateReqVO.splice(index,1)"
                    />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div
          class="shadow shadowPadding"
        >
          <div class="tableTitle">{{ $t('supplier.esicCustomers') }}</div>
          <div class="tablePadding">
            <el-table :data="supplierInfo.esicCustomerCreateReqVO">
              <el-table-column :label="$t('supplier.customerName')" prop="name">
                <template slot-scope="scope">
                  <show-or-edit
                    :dict="DICT_TYPE.INCAP_CUSTOMER"
                    :disabled="!editMode"
                    :value="scope.row.name"
                    class="smallInput"
                  >
                    <el-select v-model="scope.row.name" :disabled="!editMode" filterable>
                      <el-option
                        v-for="dict in getDictDatas(DICT_TYPE.INCAP_CUSTOMER)"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.customerIntroductionTime')" prop="involveTime">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.involveTime"
                    type="Date"
                  >
                    <el-date-picker
                      v-model="scope.row.involveTime"
                      :placeholder="$t('common.pleaseSelectADate')"
                      class="smallSelect"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                  </show-or-edit>
                </template>
              </el-table-column>

              <el-table-column prop="op" width="80">
                <template slot-scope="scope">
                  <div style="flex: 0 0 5%;display:flex;width: 60px;text-align: right">
                    <i
                      v-if="editMode"

                      class="el-icon-circle-plus"
                      style="margin-left:2px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.esicCustomerCreateReqVO.push({
                        name: '',
                        involveTime: '',
                      })"
                    />
                    <i
                      v-if="editMode"

                      class="el-icon-remove"
                      style="margin-left:5px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.esicCustomerCreateReqVO.length===1?
                        supplierInfo.esicCustomerCreateReqVO= [
                          {
                            name: '',
                            involveTime: '',
                          }
                        ]
                        :supplierInfo.esicCustomerCreateReqVO.splice(index,1)"
                    />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div
          class="shadow shadowPadding"
        >
          <div class="tableTitle">{{ $t('supplier.salesArea') }}</div>
          <el-form inline label-width="155px" style="padding: 10px 65px;">
            <el-form-item
              v-for="dict in supplierSaleArea"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
              <show-or-edit
                :disabled="!editMode"
                :value="dict.inputValue"
              >

                <el-input-number v-model="dict.inputValue" :max="100" :min="0" class="smallInput" />

              </show-or-edit>
              <span style="margin-left: 5px">
                %
              </span>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div id="category" class="form-title">3.1 <span style="margin-left:5px">{{
        $t('supplier.productInformation')
      }}</span>
        <i
          :style="showDetail4? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail4= !showDetail4"
        />
      </div>
      <div
        v-show="showDetail4"

        v-disable-all="!editMode"
        class="form-main"
        style="padding: 0 82px;"
      >
        <el-form inline label-width="155px">
          <div
            v-for="(item,index) in supplierInfo.productInfoCreateReqVO"
            :key="index"
            class="shadow shadowPadding"
          >
            <el-form-item :label="$t('supplier.nameOfSuppliedProduct')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.name"
              >
                <el-input v-model="item.name" :disabled="!editMode" />
              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.countryOfOrigin')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.countryOrigin"
              >
                <el-input v-model="item.countryOrigin" :disabled="!editMode" />
              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.supplyProductCategory')">
              <show-or-edit
                :dict="DICT_TYPE.COMMON_CATEGORY"
                :disabled="!editMode"
                :value="item.categoryArr.at(-1)"
              >
                <el-cascader
                  v-model="item.categoryArr"
                  :disabled="!editMode"
                  :options="categoryList"
                  :placeholder="$t('common.pleaseSelect')"
                  :props="{ value: 'id',label:'name'}"
                  :show-all-levels="false"
                  class="smallSelect"
                  clearable
                  filterable
                />
              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.monthlyProduction')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.monthlyOutput"
              >
                <el-input
                  v-model="item.monthlyOutput"
                  :disabled="!editMode"
                  :placeholder="$t('supplier.pleaseEnterQuantityAndUnit')"
                />
              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.selfMadeOutsourced')">
              <show-or-edit
                :dict="DICT_TYPE.SUPPLIER_HOMEMADE_OUTSOURCED"
                :disabled="!editMode"
                :value="item.productionWay"
              >
                <el-select v-model="item.productionWay" :disabled="!editMode" class="smallSelect" filterable>
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_HOMEMADE_OUTSOURCED)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>

              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.productionCycle')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.productionCycle"
              >
                <el-input v-model="item.productionCycle" :disabled="!editMode" />
              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.ownershipOfIntellectualProperty')">
              <show-or-edit
                :dict="DICT_TYPE.SUPPLIER_OWNERSHIP_OF_INTELLECTUAL_PROPERTY"
                :disabled="!editMode"
                :value="item.propertyOwnership"
              >
                <el-select v-model="item.propertyOwnership" :disabled="!editMode" class="smallSelect" filterable>
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_OWNERSHIP_OF_INTELLECTUAL_PROPERTY)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>

              </show-or-edit>
            </el-form-item>
            <el-form-item :label="$t('supplier.proportionOfSales')">
              <show-or-edit
                :disabled="!editMode"
                :value="item.salesPercent"
              >
                <el-input-number v-model="item.salesPercent" :disabled="!editMode" :max="100" :min="0" :precision="2" />
              </show-or-edit>
              %
              <i
                v-if="editMode"

                class="el-icon-circle-plus"
                style="margin-left:2px;margin-top:7px;font-size: 18px;cursor: pointer"
                @click="supplierInfo.productInfoCreateReqVO.push({
                  category: '',
                  categoryArr: '',
                  countryOrigin: '',
                  monthlyOutput: '',
                  productionCycle: '',
                  salesPercent: '',
                  productionWay: '',
                  propertyOwnership: ''
                })"
              />
              <i
                v-if="editMode"

                class="el-icon-remove"
                style="margin-left:5px;font-size: 18px;cursor: pointer"
                @click="supplierInfo.productInfoCreateReqVO.length===1?
                  supplierInfo.productInfoCreateReqVO= [
                    {
                      category: '',
                      categoryArr: '',
                      countryOrigin: '',
                      monthlyOutput: '',
                      productionCycle: '',
                      salesPercent: '',
                      productionWay: '',
                      propertyOwnership: ''
                    }
                  ]
                  :supplierInfo.productInfoCreateReqVO.splice(index,1)"
              />
            </el-form-item>

          </div>
        </el-form>
      </div>

      <div class="form-title">3.2 <span style="margin-left:5px">{{
        $t('supplier.productAndProcessProcessCapability')
      }}</span>
        <i
          :style="showDetail5? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail5= !showDetail5"
        />
      </div>
      <div
        v-show="showDetail5"

        v-disable-all="!editMode"
        class="form-main"
      >

        <div
          class="shadow shadowPadding"
        >
          <div class="tableTitle">{{
            $t('supplier.mainSuppliersAndMaterials')
          }}
          </div>
          <div class="tablePadding">
            <el-table :data="supplierInfo.rawMaterialCreateReqVO">
              <el-table-column :label="$t('supplier.brandOfRawMaterials')" prop="rawMaterialBrand">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.rawMaterialBrand"
                    class="rawMaterialFlex"
                  >
                    <el-input v-model="scope.row.rawMaterialBrand" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.rawMaterialSupplier')" prop="rawMaterialSupplier">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.rawMaterialSupplier"
                    class="rawMaterialFlex"
                  >
                    <el-input v-model="scope.row.rawMaterialSupplier" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.materialsAndSpecifications')" prop="specifications">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.specifications"
                    class="rawMaterialFlex"
                  >
                    <el-input v-model="scope.row.specifications" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.countryOfOrigin')" prop="countryOrigin">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.countryOrigin"
                    class="rawMaterialFlex"
                  >
                    <el-input v-model="scope.row.countryOrigin" />

                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.proportionInRawMaterialProcurement')" prop="rawMaterialPercent">
                <template slot-scope="scope">
                  <show-or-edit
                    :disabled="!editMode"
                    :value="scope.row.rawMaterialPercent"
                    class="rawMaterialFlex"
                  >
                    <el-input-number
                      v-model="scope.row.rawMaterialPercent"
                      :disabled="!editMode"
                      :max="100"
                      :min="0"
                    />
                  </show-or-edit>%
                </template>
              </el-table-column>
              <el-table-column prop="op" width="80">
                <template slot-scope="scope">
                  <div style="flex: 0 0 5%;display:flex;width: 60px;text-align: right">
                    <i
                      v-if="editMode"

                      class="el-icon-circle-plus"
                      style="margin-left:2px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.rawMaterialCreateReqVO.push({
                        countryOrigin: '',
                        rawMaterialBrand: '',
                        rawMaterialPercent: '',
                        rawMaterialSupplier: '',
                        specifications: ''
                      })"
                    />
                    <i
                      v-if="editMode"

                      class="el-icon-remove"
                      style="margin-left:5px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.rawMaterialCreateReqVO.length===1?
                        supplierInfo.rawMaterialCreateReqVO= [
                          {
                            countryOrigin: '',
                            rawMaterialBrand: '',
                            rawMaterialPercent: '',
                            rawMaterialSupplier: '',
                            specifications: ''
                          }
                        ]
                        :supplierInfo.rawMaterialCreateReqVO.splice(index,1)"
                    />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div
          class="shadow shadowPadding"
        >
          <div class="tableTitle">
            {{ $t('supplier.listOfMainEquipmenttopTen') }}
            <span style="margin: 0 5px;font-weight: 400">  {{ $t('supplier.batchUpload') }}</span>
            <el-button
              v-if="editPer&&editMode&&(supplierInfo.status===2||supplierInfo.sourcing==='invite')"
              v-hasPermi="['supplier:equipment:import']"
              class="uploadBtn"
              icon="el-icon-plus"
              plain
              style="padding: 5px 9px"
              type="primary"
              @click="uploadVisible = true"
            />
          </div>
          <div class="tablePadding">
            <el-table :data="supplierInfo.equipmentCreateReqVO">
              <el-table-column :label="$t('supplier.equipmentName')" prop="name" />
              <el-table-column :label="$t('material.brand')" prop="brand" />
              <el-table-column :label="$t('supplier.model')" prop="manufacturerSn" />
              <el-table-column :label="$t('supplier.quantity')" prop="quantity" />
              <el-table-column :label="$t('common.remarks')" prop="remark" />
              <el-table-column :label="$t('supplier.purchaseDate')" prop="purchaseDate">
                <!--              <template #default="scope">-->
                <!--                {{ parseTime(scope.row.purchaseDate) }}-->
                <!--              </template>-->
              </el-table-column>
              <el-table-column :label="$t('supplier.purchasePriceOfEquipment')" prop="purchasePrice" />
            </el-table>
          </div>
        </div>
        <div
          class="shadow shadowPadding"
        >
          <div style="padding: 20px 56px; display: flex;justify-content: space-between;font-size: 14px">
            <div>
              <div style="padding-left:14px;font-weight: bold;margin-bottom: 10px">{{
                $t('supplier.addedCategory')
              }}
              </div>
              <span style="margin-right: 10px">{{ $t('material.productCategory') }}</span>
              <show-or-edit
                :dict="DICT_TYPE.COMMON_CATEGORY"
                :disabled="!editPer||!editMode"
                :value="supplierInfo.processCapabilityCreateReqVO.productCategoryArr.at(-1)"
              >
                <el-cascader
                  v-model="supplierInfo.processCapabilityCreateReqVO.productCategoryArr"
                  :disabled="!editPer||!editMode"
                  :options="categoryList"
                  :placeholder="$t('common.pleaseSelect')"
                  :props="{ value: 'id',label:'name'}"
                  :show-all-levels="false"
                  class="smallSelect"
                  filterable
                  @change="changeProcessCapability"
                />
              </show-or-edit></div>
            <div style="width: 200px">
              <div style="font-weight:bold;text-align: center;margin-bottom: 15px">{{
                $t('supplier.processCapability')
              }}
              </div>
              <div style="text-align: center">
                <el-button
                  v-if="supplierInfo.processCapabilityCreateReqVO.businessDictRelStr"
                  :disabled="!editPer||!editMode"
                  type="text"
                  @click="clickProcessCapability"
                >
                  <div v-if="displayProcessCapability" class="ellipsis-line">
                    {{ supplierInfo.processCapabilityCreateReqVO.businessDictRelStr }}
                  </div>
                </el-button>
                <el-button
                  v-else
                  :disabled="!editPer||!editMode"
                  icon="el-icon-edit"
                  style="font-size: 18px;"
                  type="text"
                  @click="clickProcessCapability"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="quality" class="form-title">4.1
        <span style="margin-left:5px">{{ $t('supplier.enterpriseManagementSystem') }}</span>
        <div style="float: right">
          <el-button
            v-if="viewOnly && supplierInfo.status === 2"
            v-hasPermi="['supplier:edit:sysAndCeti']"
            plain
            size="mini"
            type="primary"
            @click="$router.push(`/supplier/QualAndCertInfo/0?supplierId=${supplierId}&viewOnly=false`)"
          >{{ $t('supplier.modifySystemAndQualifications') }}
          </el-button>

          <i
            :style="showDetail6? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
            @click="showDetail6= !showDetail6"
          />
        </div>
      </div>
      <div

        v-show="showDetail6"

        v-disable-all="!editMode"
        class="form-main"
        style="padding: 0 200px"
      >
        <el-checkbox-group v-model="manageSystem" filterable>
          <el-checkbox
            v-for="dict in getDictDatas(DICT_TYPE.ENTERPRISE_MANAGEMENT_SYSTEM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
            border
            style="margin-left: 50px;margin-top: 3px;width: 70px;margin-right: 20px;"
          />
        </el-checkbox-group>
        <div style="display: flex;align-items:center;margin-top: 10px">
          <span style="width:40px;font-size:14px;margin-right: 10px">{{ $t('supplier.other') }}</span>

          <show-or-edit
            :disabled="!editMode"
            :value="supplierInfo.emsOther"
          >

            <el-input v-model="supplierInfo.emsOther" style="width: 75%" />
          </show-or-edit>
        </div>
      </div>
      <div class="form-title">4.2 <span style="margin-left:5px">{{
        $t('supplier.qualificationAndCertification')
      }}</span>
        <i
          :style="showDetail7? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail7= !showDetail7"
        />

        <div style="float: right">
          <el-button
            v-if="editPer"
            v-has-permi="['supplier:qualification-certification:update']"
            plain
            size="mini"
            type="primary"
            @click="qualificationAndCertificationVisible = true"
          >
            {{ $t('supplier.editControlledInformation') }}
          </el-button>
        </div>
      </div>
      <div
        v-show="showDetail7"

        class="form-main"
        style="padding: 0 237px 0 119px"
      >
        <el-form label-width="220px">
          <el-form-item
            v-for="dict in qualificationAndCertificationStatic"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <div style="margin-left: 50px">
              <show-or-edit
                :dict="DICT_TYPE.QUALIFICATION_AND_CERTIFICATION_RESULT"
                :disabled="true"
                :value="dict.inputValue"
              >
                <el-radio-group v-model="dict.inputValue">
                  <el-radio
                    v-for="re in getDictDatas(DICT_TYPE.QUALIFICATION_AND_CERTIFICATION_RESULT)"
                    :key="re.value"
                    :disabled="true"
                    :label="re.value"
                  >{{ re.label }}
                  </el-radio>
                </el-radio-group>
              </show-or-edit>

            </div>
          </el-form-item>
          <el-form-item v-if="flightReasonVisible" :label="$t('supplier.reason')">
            <show-or-edit
              :disabled="true"
              :value="cetiAndQualFormStatic.reason"
            >

              <el-input v-model="cetiAndQualFormStatic.reason" :disabled="true" style="width: 336px" />
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.cleanRoom')">
            <show-or-edit
              :disabled="true"
              :value="cetiAndQualFormStatic.cleanRoom"
            >
              <el-input-number
                v-model="cetiAndQualFormStatic.cleanRoom"
                :disabled="true"
                :min="0"
                style="width: 336px"
              />

            </show-or-edit>
            <span style="margin-left: 5px">{{ $t('supplier.squareMeter') }}</span>
          </el-form-item>
          <el-form-item :label="$t('supplier.cleanRoomGrade')">
            <show-or-edit
              :disabled="true"
              :value="cetiAndQualFormStatic.cleanRoomLevel"
            >
              <el-select
                v-model="cetiAndQualFormStatic.cleanRoomLevel"
                :placeholder="$t('supplier.pleaseSelectStatus')"
                class="smallSelect"
                clearable
                disabled
                style="width: 336px"
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CLEAN_ROOM_CLASS)"
                  :key="dict.value"
                  :disabled="true"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.laboratoryEquipment')">
            <show-or-edit
              :disabled="true"
              :value="cetiAndQualFormStatic.laboratoryEquipment"
            >
              <el-input v-model="cetiAndQualFormStatic.laboratoryEquipment" style="width: 336px" />

            </show-or-edit>
          </el-form-item>
        </el-form>

      </div>
      <div id="document" class="form-title">5.1
        <span style="margin-left:5px">{{ $t('supplier.supplierDocumentLibrary') }}</span>
        <div style="float: right">
          <el-button
            v-if="viewOnly && supplierInfo.status === 2"
            v-hasPermi="['supplier:edit:doc']"
            plain
            size="mini"
            type="primary"
            @click="$router.push(`/supplier/docRepository/0?supplierId=${supplierId}&viewOnly=false`)"
          >{{ $t('supplier.editDocument') }}
          </el-button>
        </div>
      </div>

      <!--      文档库组件-->
      <docRepository v-if="docRepositoryRef&&supplierId" ref="docRepositoryRef" :lock="!editMode" :supplier-id-ref="supplierId" @listdata="docRepositoryListData" />
      <div v-show="!supplierInfo.isSupplier" id="erp" class="form-title">6.1
        <span style="margin-left:5px"> {{ $t('supplier.erpLoginInformationCompanyView') }}</span>
        <div style="float: right">
          <el-button
            v-if="viewOnly && supplierInfo.status === 2"
            v-hasPermi="['supplier:edit:erp']"
            plain
            size="mini"
            type="primary"
            @click="$router.push(`/supplier/erp/0?supplierId=${supplierId}&viewOnly=false`)"
          >{{ $t('supplier.modifyErpInformation') }}
          </el-button>

          <i
            :style="showDetail8? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
            @click="showDetail8= !showDetail8"
          />
        </div>

        <div style="float: right">
          <el-button
            v-if="editPer && !authAndSlModuleEnable"
            v-hasPermi="['supplier:company-rel:update']"
            plain
            size="mini"
            type="primary"
            @click="erpCompanyVisible = true"
          >{{ $t('supplier.editCompanyView') }}
          </el-button>
        </div>
      </div>
      <div v-show="!supplierInfo.isSupplier &&showDetail8" class="form-main">
        <div class="tablePadding">
          <el-table :data="erpCompanyList">
            <el-table-column :label="$t('system.companyCode')" prop="companyId" show-overflow-tooltip>
              <template #default="scope">
                {{ getSupplierCodeAndName(scope.row.companyId) }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('supplier.termOfPayment')" prop="paymentType" show-overflow-tooltip>
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.SUPPLIER_PAYMENT_TERM" :value="scope.row.paymentType" />
              </template>
            </el-table-column>

            <el-table-column
              :label="$t('supplier.reconciliationAccount')"
              prop="reconciliationAccount"
              show-overflow-tooltip
            >
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.SUPPLIER_CONTROLLER_ACCOUNT" :value="scope.row.reconciliationAccount" />
              </template>
            </el-table-column>

            <el-table-column :label="$t('auth.supplierAccountGroup')" prop="accountGroup" show-overflow-tooltip>
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.SUPPLIER_ACCOUNT_GROUP" :value="scope.row.accountGroup" />
              </template>
            </el-table-column>

            <el-table-column :label="$t('material.procurementGroup')" prop="pgId" show-overflow-tooltip>
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.SYSTEM_PURCHASE_GROUP" :value="scope.row.pgId" />
              </template>
            </el-table-column>

            <el-table-column :label="$t('auth.natureOfManufacturer')" prop="manufacturerNature" show-overflow-tooltip>
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.AUTH_SUPPLIER_MANUFACTURER_NATURE" :value="scope.row.manufacturerNature" />
              </template>
            </el-table-column>

            <el-table-column :label="$t('system.currency')" prop="currency" show-overflow-tooltip>
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="scope.row.currency" />
              </template>
            </el-table-column>

            <el-table-column :label="$t('supplier.custom')" prop="extendField1" show-overflow-tooltip />
          </el-table>
        </div>

      </div>
      <div v-show="!supplierInfo.isSupplier" class="form-title">6.2 <span
        style="margin-left:5px"
      >{{ $t('supplier.erpLoginInformationPurchaseOrganizationView') }}</span>
        <i
          :style="showDetail9? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
                margin-left: 7px;
                color: #4185A3;
          font-weight: bold;"
          @click="showDetail9= !showDetail9"
        />

        <div style="float: right">
          <el-button
            v-if="editPer && !authAndSlModuleEnable"
            v-hasPermi="['supplier:purchase-org-rel:update']"
            plain
            size="mini"
            type="primary"
            @click="erpPurchaseVisible = true"
          >{{ $t('supplier.editPurchaseOrganizationView') }}
          </el-button>
        </div>
      </div>
      <div v-show="!supplierInfo.isSupplier&&showDetail9" class="form-main" style="padding-bottom: 20px;">
        <div class="tablePadding">
          <el-table :data="erpPurchaseList">
            <el-table-column :label="$t('supplier.purchasingOrganization')" prop="orgId" show-overflow-tooltip>
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="scope.row.orgId" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('system.currency')" prop="currency" show-overflow-tooltip>
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="scope.row.currency" />
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('supplier.invoiceVerificationBasedOnReceipt')"
              prop="invoiceCheck"
              show-overflow-tooltip
            >
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.COMMON_Y_N" :value="scope.row.invoiceCheck" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('supplier.supplierEchelonLevel')" prop="supplierLevel" show-overflow-tooltip>
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.SUPPLIER_TIER_LEVEL" :value="scope.row.supplierLevel" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('auth.categoryAttribution')" prop="categoryId" show-overflow-tooltip>
              <template #default="scope">
                <div v-for="cate in scope.row.category">
                  <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="cate.at(-1)" />
                </div>
              </template>
            </el-table-column>

            <el-table-column :label="$t('auth.supplierGrade')" prop="supplierGrade" show-overflow-tooltip>
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.AUTH_SUPPLIER_SUPPLIER_GRADE" :value="scope.row.supplierGrade" />
              </template>
            </el-table-column>

            <el-table-column :label="$t('supplier.custom')" prop="extendField1" />
          </el-table>
        </div>

      </div>
    </div>
    <el-dialog
      v-if="paymentVisible"
      :title="$t('supplier.paymentInformation')"
      :visible.sync="paymentVisible"
      width="900px"
    >
      <el-form ref="payment" :model="paymentInfo" :rules="paymentInfoRule" inline label-width="155px">
        <div
          class="shadow shadowPadding"
        >
          <el-form-item :label="$t('supplier.unifiedSocialCreditCode')" prop="creditCode">
            <show-or-edit
              :value="supplierInfo.creditCode"
            >
              <el-input v-model="supplierInfo.creditCode" />
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('supplier.paymentMethod')" prop="paymentType">
            <el-select
              v-model="paymentInfo.paymentType"
              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_TERMS_OF_PAYMENT)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('supplier.taxRate')" prop="taxRate">
            <el-select
              v-model="paymentInfo.taxRate"

              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              filterable
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_RATE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('supplier.invoiceType')">
            <el-select
              v-model="paymentInfo.invoiceType"

              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_INVOICE_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('supplier.termOfPayment')" prop="accountDay" required>
            <el-select
              v-model="paymentInfo.accountDay"

              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('supplier.serviceChargePayer')">
            <el-select
              v-model="paymentInfo.feeBearer"
              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_FEE_BEARER)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('supplier.deliveryTerms')">
            <el-select
              v-model="paymentInfo.deliveryType"
              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('supplier.countryregion')" prop="countryRegion">
            <el-select
              v-model="supplierInfo.countryRegion"
              class="smallSelect"
              clearable
              filterable
              remote
              @change="(val)=>{changeRegion(val)}"
            >
              <el-option v-for="re in region" :key="re.keyId" :label="re.name" :value="re.id" />
            </el-select>
          </el-form-item>
        </div>

        <div
          v-for="(item,index) in paymentInfo.bankInfoCreateReqVO"
          :key="index"
          class="shadow shadowPadding"
        >
          <el-form-item
            :label="$t('system.currency')"
            :prop="`bankInfoCreateReqVO[${index}].currency`"
          >
            <el-select v-model="item.currency" class="smallSelect" filterable>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                :key="dict.value"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('supplier.invoiceAddress')"
            :prop="`bankInfoCreateReqVO[${index}].invoiceAddress`"
          >
            <el-input v-model="item.invoiceAddress" />
          </el-form-item>
          <el-form-item
            :label="$t('supplier.bankOfDeposit')"
            :prop="`bankInfoCreateReqVO[${index}].bankName`"
            :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'change'}"
            style="width: 100%;"
          >
            <el-input v-model="item.bankName" class="bigInput" />
          </el-form-item>
          <el-form-item
            :label="$t('supplier.branchBranchName')"
            :prop="`bankInfoCreateReqVO[${index}].subbranchName`"
            :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'change'}"
            style="width: 100%;"
          >
            <el-input v-model="item.subbranchName" class="bigInput" />
          </el-form-item>
          <el-form-item
            :label="$t('supplier.bankAccount')"
            :prop="`bankInfoCreateReqVO[${index}].bankAccount`"
            :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'change'}"
            style="width: 100%;"
          >
            <el-input v-model="item.bankAccount" class="bigInput" />
          </el-form-item>
          <el-form-item
            :label="$t('supplier.accountName')"
            :prop="`bankInfoCreateReqVO[${index}].accountName`"
            :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'change'}"
            style="width: 100%;"
          >
            <el-input v-model="item.accountName" class="bigInput" />
          </el-form-item>
          <el-form-item label="SWIFT Code" style="width: 100%;">
            <el-input v-model="item.swiftCode" class="bigInput" />
          </el-form-item>
          <el-form-item
            :label="$t('supplier.bankNo')"
            :prop="`bankInfoCreateReqVO[${index}].bankNo`"
            :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'blur'}"
            style="width: 100%;"
          >
            <el-input v-model="item.bankNo" class="bigInput" />
          </el-form-item>
          <el-form-item class="searchItem" :label="$t('supplier.bankInformationWithOfficialSeal')">
            <div style="display: flex;justify-content: space-between">

              <el-upload
                class="upload-demo"
                :action="uploadUrl"
                :headers="upload.headers"
                :before-upload="beforeUpload"
                :on-remove="(file,fileList)=>onRemove(file,bankFileList,item)"
                :on-preview="onPreview"
                :on-success="(response, file, fileList)=>onBankInfoFileSuccess(response, file, item)"
                multiple
                :limit="5"
                :show-file-list="false"
                :file-list="item.fileRelList"
              >
                <el-button
                  v-if="!viewOnly"
                  class="uploadBtn"
                  size="small"
                  plain
                  icon="el-icon-plus"
                  type="primary"
                />
              </el-upload>
              <div style="margin-left: 15px">

                {{ $t('scar.viewAttachments') }}
                <el-button
                  class="uploadBtn"
                  size="small"
                  style="padding: 5px 9px"
                  :disabled="item.fileRelList?.length===0"
                  plain
                  :type="item.fileRelList?.length?'primary':''"
                  @click="item.showFile=true"
                >
                  {{ item.fileRelList?.length }}
                </el-button>

                <el-dialog
                  v-if="item.showFile"
                  :visible.sync="item.showFile"
                  :title="$t('scar.viewAttachments')"
                  append-to-body
                  width="400px"
                >
                  <el-upload
                    class="upload-show"
                    :action="uploadUrl"
                    :headers="upload.headers"
                    :before-upload="beforeUpload"
                    :on-remove="(file,fileList)=>onRemove(file,bankFileList,item)"
                    :on-preview="onPreview"
                    :on-success="(response, file, fileList)=>onBankInfoFileSuccess(response, file, item,)"
                    multiple
                    :limit="5"
                    :file-list="item.fileRelList"
                  />
                  <div slot="footer">
                    <el-button type="primary" @click="item.showFile=false">{{ $t('order.determine') }}</el-button>
                  </div>

                </el-dialog>
              </div>
            </div>

          </el-form-item>
          <span>
            <i
              class="el-icon-circle-plus"
              style="margin-top:8px;margin-left:10px;font-size: 18px;cursor: pointer"
              @click="paymentInfo.bankInfoCreateReqVO.push({
                accountName: '',
                bankAccount: '',
                bankName: '',
                nccSubbranchId: '',
                bankNo: '',
                currency: '',
                invoiceAddress: '',
                subbranchName: '',
                fileRelList: [],
                swiftCode: '',
                showFile: false
              })"
            />
            <i
              v-if="paymentInfo.bankInfoCreateReqVO.length>1"
              class="el-icon-remove"
              style="margin-top:8px;margin-left:10px;font-size: 18px;cursor: pointer"
              @click="paymentInfo.bankInfoCreateReqVO.length===1?
                paymentInfo.bankInfoCreateReqVO= [
                  {
                    accountName: '',
                    bankAccount: '',
                    bankName: '',
                    nccSubbranchId: '',
                    bankNo: '',
                    currency: '',
                    invoiceAddress: '',
                    subbranchName: '',
                    fileRelList: [],
                    swiftCode: '',
                    showFile: false,
                  }
                ]
                :paymentInfo.bankInfoCreateReqVO.splice(index,1)"
            />
          </span>

        </div>

      </el-form>
      <div style="text-align: right">
        <el-button @click="paymentVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button :loading="paymentLoading" type="primary" @click="savePaymentInfo">{{ $t('common.save') }}</el-button>

      </div>
    </el-dialog>
    <el-dialog
      v-if="brandVisible"
      :visible.sync="brandVisible"
    >
      <brand
        :scope="supplierInfo.processCapabilityCreateReqVO.businessDictRelStr"
        @closeBrand="closeBrand"
      />
    </el-dialog>
    <el-dialog
      :title="$t('common.batchCreation')"
      :visible.sync="uploadVisible"
      width="600px"
    >
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div style="text-align: center">
        <el-upload
          ref="upload"
          :action="upload.url"
          :auto-upload="false"
          :before-upload="beforeUploadXls"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelMainEquipmentUpload">{{ $t('common.cancel') }}</el-button>
        <el-button v-if="editPer" v-hasPermi="['supplier:equipment:export']" icon="el-icon-download" @click="downloadTemplate">
          {{ $t('supplier.templateDownload') }}
        </el-button>
        <el-button v-if="editPer" v-hasPermi="['supplier:equipment:import']" type="primary" @click="handleUpload">
          {{ $t('common.confirm') }}
        </el-button>

      </div>
    </el-dialog>
    <el-dialog
      :title="$t('supplier.editErpLoginInformationCompanyView')"
      :visible.sync="erpCompanyVisible"
    >
      <div style="margin: 10px 0">
        <el-button
          type="primary"
          @click="erpCompany.push({
            companyId:'',
            paymentType:'',
            reconciliationAccount:'',
            currency:'',
            supplierId
          })"
        >{{ $t('supplier.newLine') }}
        </el-button>
        <el-button @click="delErpCompany">{{ $t('supplier.deleteSelected') }}</el-button>
      </div>
      <vxe-table
        ref="erpCompany"
        :data="erpCompany"
        :row-key="true"
      >
        <vxe-column
          type="checkbox"
          width="55"
        />

        <vxe-column
          :title="$t('system.companyCode')"
          field="companyId"
        >
          <template #default="scope">
            <el-select v-model="scope.row.companyId" clearable>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_COMPANY)"
                :key="dict.value"
                :label="dict.code+'-'+dict.name"
                :value="dict.id"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column
          :title="$t('supplier.termOfPayment')"
          field="paymentType"
        >
          <template #default="scope">
            <el-select v-model="scope.row.paymentType" clearable>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_TERM)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column
          :title="$t('supplier.reconciliationAccount')"
          field="reconciliationAccount"
        >
          <template #default="scope">
            <el-select v-model="scope.row.reconciliationAccount" clearable>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CONTROLLER_ACCOUNT)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </template>
        </vxe-column>

        <vxe-column
          :title="$t('auth.supplierAccountGroup')"
          field="accountGroup"
        >
          <template #default="scope">
            <el-select v-model="scope.row.accountGroup" clearable>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_ACCOUNT_GROUP)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </template>
        </vxe-column>

        <vxe-column
          :title="$t('material.procurementGroup')"
          field="pgId"
        >
          <template #default="scope">
            <el-select v-model="scope.row.pgId" clearable>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_PURCHASE_GROUP)"
                :key="dict.value"
                :label="dict.label"
                :value="Number(dict.value)"
              />
            </el-select>
          </template>
        </vxe-column>

        <vxe-column
          :title="$t('auth.natureOfManufacturer')"
          field="manufacturerNature"
        >
          <template #default="scope">
            <el-select v-model="scope.row.manufacturerNature" clearable>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_MANUFACTURER_NATURE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </template>
        </vxe-column>

        <vxe-column
          :title="$t('system.currency')"
          field="currency"
        >
          <template #default="scope">
            <el-select v-model="scope.row.currency" clearable transfer>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                :key="dict.value"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </template>
        </vxe-column>
      </vxe-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="erpCompanyVisible =false">{{ $t('common.cancel') }}</el-button>
        <el-button
          type="primary"
          @click="submitErpCompany"
        >{{ $t('common.submit') }}
        </el-button>

      </div>
    </el-dialog>
    <el-dialog
      :title="$t('supplier.editErpLoginInformationPurchaseOrganizationView')"
      :visible.sync="erpPurchaseVisible"
      append-to-body
    >
      <div style="margin: 10px 0">
        <el-button
          type="primary"
          @click="erpPurchase.push({
            orgId:'',
            currency:'',
            invoiceCheck:'',
            supplierLevel:'',
            supplierId,
            categoryIds:[]

          })"
        >{{ $t('supplier.newLine') }}
        </el-button>
        <el-button @click="delErpPurchase">{{ $t('supplier.deleteSelected') }}</el-button>
      </div>

      <vxe-table
        ref="erpPurchase"
        :data="erpPurchase"
        :row-key="true"
      >
        <vxe-column
          type="checkbox"
          width="55"
        />
        <vxe-column :title="$t('supplier.purchasingOrganization')">
          <template #default="scope">
            <el-select v-model="scope.row.orgId">
              <el-option
                v-for="dict in getDictDatas('purchaseOrg')"
                :key="dict.value"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column :title="$t('system.currency')">
          <template #default="scope">
            <el-select v-model="scope.row.currency" clearable>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                :key="dict.value"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column :title="$t('supplier.invoiceVerificationBasedOnReceipt')">
          <template #default="scope">
            <el-select v-model="scope.row.invoiceCheck">
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_Y_N)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column :title="$t('supplier.supplierEchelonLevel')">
          <template #default="scope">
            <el-select v-model="scope.row.supplierLevel">
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_TIER_LEVEL)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </template>
        </vxe-column>

        <vxe-column :title="$t('auth.categoryAttribution')">
          <template #default="scope">
            <el-cascader
              v-model="scope.row.category"
              :options="categoryList"
              :placeholder="$t('material.category')"
              :props="{ value: 'id',label:'name',multiple :true}"
              class="input"
              clearable
              filterable
            />
          </template>
        </vxe-column>

        <vxe-column :title="$t('auth.supplierGrade')">
          <template #default="scope">
            <el-select v-model="scope.row.supplierGrade">
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_SUPPLIER_GRADE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </template>
        </vxe-column>

      </vxe-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="erpPurchaseVisible =false">{{ $t('common.cancel') }}</el-button>
        <el-button
          type="primary"
          @click="submitErpPurchase"
        >{{ $t('common.submit') }}
        </el-button>

      </div>
    </el-dialog>
    <el-dialog
      v-if="qualificationAndCertificationVisible"
      :title="$t('supplier.editorialQualificationAndCertification')"
      :visible.sync="qualificationAndCertificationVisible"
      width="1100px"
    >
      <div class="form-main" style="padding: 0 161px 0 194px">

        <el-form label-width="220px">
          <el-form-item
            v-for="dict in qualificationAndCertification"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <div
              style="margin-left: 50px"
            >
              <el-radio-group
                v-model="dict.inputValue"
              >
                <el-radio
                  v-for="re in getDictDatas(DICT_TYPE.QUALIFICATION_AND_CERTIFICATION_RESULT)"
                  :key="re.value"
                  :label="re.value"
                >{{ re.label }}
                </el-radio>
              </el-radio-group>

            </div>

          </el-form-item>
          <el-form-item v-if="flightReasonVisible" :label="$t('supplier.reason')">

            <el-input v-model="cetiAndQualForm.reason" style="width: 336px" />
          </el-form-item>
          <el-form-item :label="$t('supplier.cleanRoom')">
            <el-input-number v-model.number="cetiAndQualForm.cleanRoom" :min="0" style="width: 336px" />
            <span style="margin-left: 5px">{{ $t('supplier.squareMeter') }}</span>
          </el-form-item>
          <el-form-item :label="$t('supplier.cleanRoomGrade')">
            <el-select
              v-model="cetiAndQualForm.cleanRoomLevel"
              :placeholder="$t('supplier.pleaseSelectStatus')"
              class="smallSelect"
              clearable
              style="width: 336px"
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CLEAN_ROOM_CLASS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('supplier.laboratoryEquipment')">
            <el-input v-model="cetiAndQualForm.laboratoryEquipment" style="width: 336px" />
          </el-form-item>
        </el-form>
        <div slot="footer" style="text-align: center">
          <el-button @click="qualificationAndCertificationVisible =false">{{ $t('common.cancel') }}</el-button>
          <el-button
            type="primary"
            @click="submitQualificationAndCertification"
          >{{ $t('common.submit') }}
          </el-button>

        </div>

      </div>
    </el-dialog>
    <el-dialog
      v-if="log.open"
      :close-on-click-modal="false"
      :title="$t('common.operationRecord')"
      :visible.sync="log.open"
      width="900px"
    >
      <operation-record
        :business-id="log.businessId"
        :columns="log.columns"
        :log-visible.sync="log.open"
      />
    </el-dialog>
    <!--    1.4 加盖公章的银行信息上传弹框-->
    <el-dialog
      :title="$t('supplier.bankInformationWithOfficialSeal')"
      :visible.sync="showUpload"
      append-to-body
      width="500px"
    >

      <el-upload
        ref="upload"
        :action="uploadUrl"
        :auto-upload="false"
        :file-list="bankFileList"
        :headers="headers"
        :on-change="whenFileChange"
        :on-preview="openBankInfoFile"
        :on-remove="handleBankInfoFileRemove"
        :on-success="(response, file, fileList)=>OnSuccess(response, file, fileList,)"
        class="upload-demo"
        drag
        multiple
        style="text-align: center"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showUpload = false">{{ $t('common.cancel') }}</el-button>
        <el-button v-if="editPer" v-hasPermi="['supplier:equipment:import']" type="primary" @click="handleUpload">
          {{ $t('common.confirm') }}
        </el-button>

      </div>
    </el-dialog>

  </div>
</template>

<script>
import docRepository from '@/views/supplier/docRepository'
import Sticky from '@/components/Sticky'
import { throttle } from 'throttle-debounce'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import {
  exportEquip,
  getCompanyRel,
  getPayment,
  getPurchaseRel,
  getQualifAndCert,
  getSupplierBase,
  submitSupplier,
  updateCompanyRel,
  updatePaymentInfo,
  updatePurchaseRel,
  updateQualifAndCert,
  updateSupplierInfo,
  readTianYanChaInfo,
  enableErpInfo
} from '@/api/supplier/info'
import brand from './brand'
import { getBaseHeader, handleAuthorized } from '@/utils/request'
import operationRecord from '@/components/OperationRecord/operationRecord'
import { deepClone, getTreeMap } from '@/utils'
import { deleteDocRepositoryFile, listFileList, repositoryFileBind } from '@/api/supplier/docRepository'
import $modal from '@/plugins/modal'
import { getPath } from '@/utils/ruoyi'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'

export default {
  name: 'Supplierinfo',
  components: {
    ShowOrEdit,
    docRepository,
    brand,
    Sticky,
    operationRecord
  },
  data() {
    return {
      /**
       * 是否为查看页面
       * 1.需要隐藏顶部的编辑按钮
       * 2.展开5个部分的编辑按钮
       */
      viewOnly: false,
      showBankIndex: '',
      addVisible: false,
      addQueryParams: {
        bankName: '',
        swiftCode: '',
        pageNo: 1,
        pageSize: 10,
        sortField: '',
        sortBy: 'ASC'
      },
      addList: [],
      addLoading: false,
      addTotal: 0,
      addGirdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'bankAddIn',
        maxHeight: 800,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns:
          [
            { type: 'radio', width: 55, visible: true },
            { title: this.$t('supplier.bankOfDeposit'), field: 'bankName', visible: true },
            { title: this.$t('supplier.bankBranchCode'), field: 'bankNo', visible: true },
            { title: this.$t('supplier.swiftCode'), field: 'swiftCode', visible: true }
          ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      total: 0,
      list: [],
      // 制程能力为非电子类时，已选择的品牌是否展示
      displayProcessCapability: false,
      // 进入资源库之后，供应商名称、统一信用证字段输入框需要锁死，不允许编辑。
      // 此处的锁定特殊用于名称和社会信用代码。不需要引入其他的判断
      lockWhenIsResource: false,
      /**
       *  ：当前页面的控制字段1；
       * 1.邀请供应商注册 & 要请供应商修改[/supplierEmail/:id] （申请库）
       * 2.供应商自主注册[/supplierRegister/:id] （申请库）
       * 3.位于申请库且属于自主注册时，采购从申请库点击供应商名称进入不可编辑。
       * 用于：总的来讲，该字段为false的场景是第【3】点，自主注册的供应商由采购从申请库进入。控制了全部的按钮级别的编辑（不可编辑）。
       * 包括顶部按钮，erp视图按钮等
       */
      editPer: false,
      /**
       *  ：当前页面的控制字段2；
       * 场景1：采购从【申请库-邀请登记】页面点击供应商名称进入。此情况下属于【采购入口】：此时editPer=true;editMode=false。效果是顶部存在【进入编辑】按钮。
       * 同时独立的编辑部分按钮也存在。包括（银行信息的变更，erp视图的变更、资质认证的变更）
       * 场景2：采购从【申请库-自主登记】页面点击供应商名称进入。此情况下也属于【采购入口】：此时editPer=false;editMode=false。效果是所有的编辑按钮不可见
       * 场景3：【申请库-邀请登记库#邀请修改/资源库-邀请修改】[/supplierEmail/:id] 进入：
       * 供应商位于申请库则：此时editPer=true;editMode=true。效果是底部的公司、采购组织视图隐藏 & 效果是全部的按钮都可以进入编辑
       * 供应商位于资源库则：此时editPer=true;editMode=true。效果是底部的公司、采购组织视图隐藏 & 效果是全部的按钮都可以进入编辑
       *
       * 场景4：【通过系统的注册页面#自主注册】[/supplierRegister/:id] 进入：此时属于【邮件链接进入】
       * 供应商位于申请库（status！=2）则：此时editPer=true;editMode=true。效果是底部的公司、采购组织视图隐藏 & 全部的按钮都可以进入编辑
       * 供应商位于资源库（status =2）则：此时editPer=false;editMode=true。效果是底部的公司、采购组织视图隐藏 & 没有编辑的按钮（可能有bug，默认编辑状态但是没有编辑按钮）
       *
       * 场景5：认证等其他模块点击供应商名称进入【/supplier/supplierinfo】,属于采购入口。此时editPer=true;editMode=false。效果是顶部存在【进入编辑】按钮。
       * 用于：
       */
      editMode: false,
      /**
       * 6.1、6.2部分的展示，当用户是外部供应商时不可见。控制字段是 {@link supplierInfo.isSupplier}
       */
      erpCompany: [],
      erpCompanyList: [],
      erpPurchase: [],
      erpPurchaseList: [],
      key: '',
      // 只获取启用状态
      categoryList: [],
      // 所有状态
      allCategoryList: [],
      operateRecordList: [],
      scrolltop: 0,
      basicPos: null,
      businessPos: null,
      categroyPos: null,
      qualityPos: null,
      qualificationPos: null,
      documentPos: null,
      erpPos: null,
      onSiteAuditResultsPos: null,
      clientHeight: null,
      mark: `#basic`,
      mark0: `#business`,
      mark1: `#category`,
      mark2: `#quality`,
      mark3: `#qualification`,
      supplierInfo: {
        id: '',
        establishedTime: undefined,
        associatedCompanyCreateReqVO: [
          {
            relationship: '',
            countryRegion: '',
            street: '',
            provinceCity: '',
            zipCode: '',
            province: []
          }
        ],
        buildProperty: '',
        cleanRoom: 0,
        cleanRoomLevel: '',
        code: '',
        companyCategory: '',
        registeredCapital: '',
        registeredCapitalCurrency: '',
        companyNature: '',
        companyCurrency: '',
        isSupplier: true,
        contactCreateReqVO: [
          {
            name: '',
            position: '',
            phone: '',
            email: '',
            division: [],
            businessDictRelStr: ''
          }
        ],
        countryRegion: '',
        coversArea: 0,
        creditCode: '',
        customerCreateReqVO: [
          {
            industry: '',
            name: '',
            proportion: '',
            terminalCustomer: ''
          }
        ],
        esicCustomerCreateReqVO: [
          {
            name:'',
            involveTime: null
          }
        ],
        dictRelCreateReqVO: [],
        email: '',
        emsOther: '',
        equipmentCreateReqVO: [],
        exportQualification: '',
        factoryAddressCreateReqVO: [
          {
            countryRegion: '',
            street: '',
            provinceCity: '',
            zipCode: '',
            province: []
          }
        ],
        fax: '',
        infoPercent: 0,
        laboratoryEquipment: '',
        legalPerson: '',
        name: '',
        nameShort: '',
        phone: '',
        position: '',
        processCapabilityCreateReqVO: {
          productCategory: '',
          productCategoryArr: [],
          businessDictRelStr: ''
        },
        productInfoCreateReqVO: [
          {
            name: '',
            categoryArr: [],
            category: '',
            countryOrigin: '',
            monthlyOutput: '',
            productionCycle: '',
            salesPercent: '',
            productionWay: '',
            propertyOwnership: ''
          }
        ],
        productionArea: 0,
        provinceCity: '',
        rawMaterialCreateReqVO: [
          {
            countryOrigin: '',
            rawMaterialBrand: '',
            rawMaterialPercent: '',
            rawMaterialSupplier: '',
            specifications: ''
          }
        ],
        reason: '',
        registeredAddress: '',
        shareholdersCreateReqVO: [
          {
            name: '',
            stake: ''
          }
        ],
        sourcing: '',
        status: 0,
        street: '',
        telephone: '',
        turnoverCreateReqVO: [
          {
            currency: '',
            turnover: '',
            unit: '',
            year: new Date().getFullYear()
          },
          {
            currency: '',
            turnover: '',
            unit: '',
            year: new Date().getFullYear() - 1
          }, {
            currency: '',
            turnover: '',
            unit: '',
            year: new Date().getFullYear() - 2
          }
        ],
        webSite: '',
        zipCode: ''
      },
      // 1.4 银行信息已经上传的文件列表
      bankFileList: [],
      paymentInfo: {
        creditCode: '',
        accountDay: '',
        bankInfoCreateReqVO: [{
          accountName: '',
          bankAccount: '',
          bankName: '',
          nccSubbranchId: '',
          bankNo: '',
          currency: '',
          invoiceAddress: '',
          subbranchName: '',
          swiftCode: '',
          openBankSources: [],
          bankSources: [],
          fileRelList: [],
          showFile: false,
          optionsObj: {}
        }
        ],
        countryRegion: '',
        deliveryType: '',
        feeBearer: '',
        invoiceType: '',
        paymentType: '',
        taxRate: '',
        paymentInfoEdit: false
      },
      supplierInfoRule: {
        email: [{
          pattern: /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
          message: this.$t('supplier.incorrectMailboxFormat'),
          trigger: 'blur'
        }],

        nameShort: [{ required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('supplier.pleaseEnterFullSupplierName'), trigger: 'blur' }],
        nameEn: [{ required: true, message: this.$t('supplier.pleaseEnterFullSupplierName'), trigger: 'blur' }],
      },
      paymentInfoRule: {
        // paymentType: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        // taxRate: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
       /* accountDay: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }]*/
        // countryRegion: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }]
      },
      region: getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(item => item.type === 'country'),
      province: [],
      manageSystem: [],
      qualificationAndCertification: this.formatDictValue(DICT_TYPE.QUALIFICATION_AND_CERTIFICATION),
      qualificationAndCertificationStatic: this.formatDictValue(DICT_TYPE.QUALIFICATION_AND_CERTIFICATION),
      supplierSaleArea: this.formatDictValue(DICT_TYPE.SUPPLIER_SALE_AREA),
      supplierDev: this.formatDictValue(DICT_TYPE.SUPPLIER_QUALIFICATIONS_OF_DEVELOPER),
      supplierCertificate: this.formatDictValue(DICT_TYPE.SUPPLIER_SPECIAL_WORK_QUALIFICATION_CERTIFICATE),
      supplierStaff: this.formatDictValue(DICT_TYPE.SUPPLIER_STAFF),
      paymentVisible: false,
      paymentLoading: false,
      brandVisible: false,
      docRepositoryRef: true,
      docRepositoryList: [],
      abilityRow: {},
      uploadVisible: false,
      supplierId: null,
      upload: {
        headers: getBaseHeader(),
        url: '',
        isUploading: false
      },
      onSiteAuditList: [],
      erpCompanyVisible: false,
      erpPurchaseVisible: false,
      qualificationAndCertificationVisible: false,
      cetiAndQualForm: {
        cleanRoom: 0,
        cleanRoomLevel: '',
        dictRelCreateReqVO: [],
        laboratoryEquipment: '',
        supplierId: null
      },
      cetiAndQualFormStatic: {},
      showUpload: false,
      uploadUrl: '',
      headers: getBaseHeader(),
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null,
        columns: [
          {
            tableName: 'supplier_payment_info',
            keyColumns: ['countryRegion',
              'accountDay', 'deliveryType',
              'feeBearer', 'invoiceType',
              'paymentType', 'taxRate']
          },
          {
            tableName: 'supplier_bank_info',
            keyColumns: ['accountName', 'bankAccount', 'bankName',
              'bankNo',
              'currency',
              'invoiceAddress',
              'subbranchName',
              'swiftCode'
            ]
          },
          {
            tableName: 'supplier_base_info',
            keyColumns: ['creditCode']
          }
        ]
      },
      isCanSave: true,
      supplierShow: true,
      paymentInfoStatic: {
        accountDay: '',
        bankInfoCreateReqVO: [{
          accountName: '',
          bankAccount: '',
          bankName: '',
          bankNo: '',
          currency: '',
          invoiceAddress: '',
          subbranchName: '',
          swiftCode: '',
          nccSubbranchId: ''
        }],
        countryRegion: '',
        deliveryType: '',
        feeBearer: '',
        invoiceType: '',
        paymentType: '',
        taxRate: ''
      },
      fileType: '',
      showDetail: false,
      showDetail1: false,
      showDetail2: false,
      showDetail3: false,
      showDetail4: false,
      showDetail5: false,
      showDetail6: false,
      showDetail7: false,
      showDetail8: false,
      showDetail9: false,
      showDetail0: false,
      showDetail11: false,
      authAndSlModuleEnable: false // 认证和分级模块是否同时启用
    }
  },
  computed: {
    getCurentPosition() {
      const redundant = this.$store.getters.fixedHeader ? 178 : 140

      // 供应商的时候
      if (this.supplierInfo.isSupplier) {
        if (this.scrolltop >= 0 && this.scrolltop + redundant < this.businessPos) {
          return 0
        } else if (this.scrolltop + redundant > this.businessPos && this.scrolltop + redundant < this.categroyPos) {
          return 1
        } else if (this.scrolltop + redundant > this.categroyPos && this.scrolltop + redundant < this.qualityPos) {
          return 2
        } else if (this.scrolltop + redundant > this.qualityPos && this.scrolltop + redundant < this.documentPos - 400) {
          return 3
        } else {
          return 4
        }
      } else {
        if (this.scrolltop >= 0 && this.scrolltop + redundant < this.businessPos) {
          return 0
        } else if (this.scrolltop + redundant > this.businessPos && this.scrolltop + redundant < this.categroyPos) {
          return 1
        } else if (this.scrolltop + redundant > this.categroyPos && this.scrolltop + redundant < this.qualityPos) {
          return 2
        } else if (this.scrolltop + redundant > this.qualityPos && this.scrolltop + redundant < this.documentPos) {
          return 3
        } else if (this.scrolltop + redundant > this.documentPos && this.scrolltop + redundant < this.erpPos) {
          return 4
        } else if (this.scrolltop + redundant > this.erpPos && this.scrolltop + redundant < this.onSiteAuditResultsPos - 300) {
          return 5
        } else {
          return 6
        }
      }
    },
    flightReasonVisible() {
      //  是否飞行检查单独处理 wtf
      return this.qualificationAndCertification?.find(item => item.value === 'Do you accept flight inspection')?.inputValue === 'not_involve'
    },
    infoPercent() {
      if (this.supplierInfo.status === 2) {
        // 资源库内走后端的计算信息完成率
        return this.supplierInfo.infoPercent
      }

      let num = 0
      for (const item of Object.keys(this.supplierInfo)) {
        if (['name',
          'webSite',
          'legalPerson',
          'registeredAddress',
          'creditCode',
          'telephone',
          'countryRegion',
          'provinceCity',
          'street',
          'zipCode',
          'fax',
          'companyCategory',
          'position',
          'email',
          'phone',
          'coversArea',
          'companyNature',
          'exportQualification'
        ].includes(item) && this.supplierInfo[item]) {
          num++
        }
      }
      for (const keysKey of Object.keys(this.cetiAndQualFormStatic)) {
        if ([
          'cleanRoom',
          'cleanRoomLevel',
          'laboratoryEquipment'
        ].includes(keysKey) && this.cetiAndQualFormStatic[keysKey]) {
          num++
        }
      }
      if (this.supplierInfo.factoryAddressCreateReqVO.some(item => item.countryRegion && item.street && item.provinceCity && item.zipCode)) {
        num += 2
      }
      if (this.supplierInfo.contactCreateReqVO.some(item => item.name && item.position && item.phone && item.email && item.division.length)) {
        num += 3
      }
      if (this.supplierInfo.shareholdersCreateReqVO.some(item => item.name && item.stake)) {
        num++
      }
      this.supplierStaff.map(item => {
        if (item.inputValue) {
          num++
        }
      })
      for (const item of Object.keys(this.paymentInfo)) {
        if (!['supplierId', 'id', 'createTime', 'bankInfoCreateReqVO'].includes(item) && this.paymentInfo[item]) {
          num++
        }
      }
      const bankFlag = new Set()
      this.paymentInfo.bankInfoCreateReqVO?.map(item => {
        for (const itemKey in item) {
          if (item[itemKey] && !['supplierId', 'id', 'createTime', 'swiftCode', 'sort'].includes(itemKey)) {
            bankFlag.add(itemKey)
          }
        }
      })
      num += bankFlag.size
      this.supplierInfo.turnoverCreateReqVO.map(item => {
        if (item.currency && item.turnover && item.unit) {
          num++
        }
      })
      if (this.supplierInfo.customerCreateReqVO.some(item => item.industry && item.name && item.proportion && item.terminalCustomer)) {
        num++
      }
      if (this.supplierInfo.esicCustomerCreateReqVO.some(item => item.name && item.involveTime)) {
        num++
      }
      if (this.supplierSaleArea.some(item => item.inputValue)) {
        num++
      }
      if (this.supplierInfo.productInfoCreateReqVO.some(item => item.name && item.category && item.countryOrigin && item.monthlyOutput && item.productionCycle && item.salesPercent && item.productionWay && item.propertyOwnership)) {
        num += 5
      }

      if (this.supplierInfo.rawMaterialCreateReqVO.some(item => item.countryOrigin && item.rawMaterialBrand && item.rawMaterialSupplier && item.specifications)) {
        num++
      }
      if (this.supplierInfo.equipmentCreateReqVO.some(item => item.name && item.brand && item.manufacturerSn && item.quantity && item.remark && item.purchaseDate && item.purchasePrice)) {
        num += 3
      }
      if (this.supplierInfo.processCapabilityCreateReqVO?.businessDictRelStr) {
        num++
      }
      if (this.manageSystem.length) {
        num++
      }
      if (this.onSiteAuditList.length) {
        num++
      }
      this.qualificationAndCertification.map(item => {
        if (item.inputValue) {
          num++
        }
      })
      // return num
      return (num / 0.8).toFixed(0)
    }
  },
  watch: {
    'supplierInfo.creditCode': {
      handler(val) {
        this.paymentInfo.creditCode = val
      }
    },
    'supplierInfo.countryRegion': {
      handler(val) {
        this.paymentInfo.countryRegion = val
      }
    }
  },
  created() {
    this.supplierId = this.$route.params.id
    if (!this.supplierId) {
      this.supplierId = this.$store.getters.supplierId
    }
    this.init()
    this.viewOnly = this.$route.query.viewOnly
    this.doGetSupplierBase()
  },
  mounted() {
    this.viewOnly = this.$route.query.viewOnly
    if (this.$route.path === '/supplier/supplierAccount') {
      // 企业信息登记菜单进入为查看页
      this.viewOnly = true
    }
    this.getCategories()
    this.getFloorDistance()
    this.getPulleyTopDistance()
    this.getPaymentInfo()
    this.getCetiAndQualiForm()
    this.getErpCompany()
    this.getErpPurchase()
    this.supplierId = this.$route.params.id
    if (!this.supplierId) {
      this.supplierId = this.$store.getters.supplierId
    }
    this.doGetSupplierBase()
    this.doGetAuthAndSlModuleEnable()
  },
  beforeDestroy() {
    window.onscroll = null
  },
  methods: {
    // 认证和分级模块是否在当前项目中同时启用。
    // Y-则公司和采购组织不允许在供应商模块 新增、删除
    doGetAuthAndSlModuleEnable() {
      enableErpInfo().then(res => {
        this.authAndSlModuleEnable = res.data
      })
    },
    saveAdd() {
      const data = this.$refs.addList.getRadioRecord()
      if (!data) {
        this.$message.error(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.addVisible = false
      this.paymentInfo.bankInfoCreateReqVO[this.showBankIndex].bankName = data.bankName
      this.paymentInfo.bankInfoCreateReqVO[this.showBankIndex].bankNo = data.bankNo
      this.paymentInfo.bankInfoCreateReqVO[this.showBankIndex].swiftCode = data.swiftCode
    },
    docRepositoryListData(list) {
      this.docRepositoryList = [...list]
    },
    // 社会统一信用代码为中国时添加必填验证
    getCreditCodeRule() {
      this.getConfigKey('system.china').then(response => {
        if (response.data) {
          // 配置的是国家的编码，转换为id集合
          const chinaIds = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(item => response.data.includes(item.code))?.map(item => (item.id))
          if (chinaIds && chinaIds.includes(this.supplierInfo.countryRegion)) {
            this.$set(this.supplierInfoRule, 'creditCode',
              [{ required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }])

            this.$set(this.paymentInfoRule, 'creditCode',
              [{ required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }])
          } else {
            this.$delete(this.supplierInfoRule, 'creditCode')
            this.$delete(this.paymentInfoRule, 'creditCode')
          }
        }
        this.$nextTick(() => {
          this.$refs.baseInfo?.clearValidate()
          this.$refs.personInfo?.clearValidate()
          this.$refs.payment?.clearValidate()
        })
      })
    },
    // 制程能力品类事件改变触发
    changeProcessCapability(value) {
      if (value.includes(52)) {
        this.displayProcessCapability = true
      } else {
        this.displayProcessCapability = false
        this.$message.warning(this.$t('common.notNeedInput'))
      }
    },
    // 点击 制程能力时的判断方法
    clickProcessCapability() {
      // 第一阶段 非电子类品类点击则提示 ‘暂无填写要求’
      if (!this.supplierInfo.processCapabilityCreateReqVO.productCategoryArr.includes(52)) {
        this.$message.warning(this.$t('common.notNeedInput'))
        return
      }
      this.brandVisible = true
    },
    // 1.4 移除银行信息相关的文件时的事件处理
    handleBankInfoFileRemove(file, fileList) {
      if (!file.fileId) {
        // 文件未上传到服务器被删除，则直接移除，不走后端接口
        this.$message.success(this.$t('common.delSuccess'))
      } else {
        deleteDocRepositoryFile(file.fileId).then(() => {
          this.$message.success(this.$t('common.delSuccess'))
        }).catch(() => {
          this.$message.error(this.$t('common.deleteFailed'))
        })
      }
    },
    // 1.4 银行信息上传按钮触发操作
    clickBankInfoUpload() {
      this.showUpload = true
      listFileList({ supplierId: this.supplierId, uploadEntryType: 'supplier_homepage_1.4section' }).then(res => {
        this.bankFileList = res.data
      })
    },
    // 1.4 银行信息上传的文件查看
    openBankInfoFile(file) {
      window.open(file.url)
    },
    init() {
      this.uploadUrl = process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload'
      this.upload.url = process.env.VUE_APP_BASE_API + `/admin-api/supplier/base-info/import-equipment?supplierId=${this.supplierId}`
      this.getConfigKey('file.type.common').then(response => {
        this.fileType = response.data
      })
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY), 'id'))
      this.allCategoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, false), 'id'))
    },
    doGetSupplierBase() {
      getSupplierBase({
        id: this.supplierId
      }).then(res => {
        this.manageSystem = []
        if (this.$route.name.toLowerCase() === '/supplieremail/:id') {
          // 邀请注册入口
          this.editPer = true
        } else if (this.$route.name.toLowerCase() === '/supplierregister/:id') {
          // 供应商注册入口
          if (res.data.status !== 2) {
            this.editPer = true
          }
        } else {
          // 采购入口
          if (res.data.status === 2) {
            this.editPer = true
          } else if (res.data.status === 0 && res.data.sourcing === 'invite') {
            // 属于邀请的供应商 且未提交
            this.editPer = true
          }
        }
        if (this.editPer && res.data.status !== 2) {
          // 进入默认编辑状态
          this.editMode = true
          if (res.data.sourcing === 'invite' && this.$route.name.toLowerCase() !== '/supplieremail/:id') {
            // 此场景为：采购从申请库-邀请登记页面点击供应商名称进入。此情况下：editPer=true;editMode=false.
            this.editMode = false
          }
        }
        // 进入资源库的供应商需要锁住部分字段不可编辑
        if (res.data.status === 2) {
          this.lockWhenIsResource = true
        }
        // 【UFFF-1082】进入资源库的供应商允许点击保存按钮，原因在于提交操作仅点击后即失效了，保存按钮允许多次维护信息。
        // if (res.data.status === 2) {
        //   this.isCanSave = false
        // }
        this.province = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(a => a.parentId === res.data.countryRegion)
        res.data.factoryAddressCreateReqVO?.map(item => {
          item.province = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(a => a.parentId === item.countryRegion)
        })
        res.data.associatedCompanyCreateReqVO?.map(item => {
          item.province = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(a => a.parentId === item.countryRegion)
        })
        res.data.contactCreateReqVO?.map(item => {
          item.division = item.businessDictRelStr ? item.businessDictRelStr.split(',') : []
        })
        res.data.productInfoCreateReqVO?.map(item => {
          item.categoryArr = item.category ? getTreeMap(item.category, this.allCategoryList) : []
        })
        res.data.processCapabilityCreateReqVO.productCategoryArr =
          getTreeMap(res.data.processCapabilityCreateReqVO.productCategory, this.allCategoryList)
        if (res.data.processCapabilityCreateReqVO.productCategory) {
          if (res.data.processCapabilityCreateReqVO.productCategory === 52) {
            this.displayProcessCapability = true
          } else {
            console.log('this.categoryList.filter(v => v.id === 52)', this.categoryList.filter(v => v.id === 52))
            this.displayProcessCapability = this.hasNodeWithId(this.categoryList.filter(v => v.id === 52)[0], res.data.processCapabilityCreateReqVO.productCategory)
          }
        }
        // 2022.8.5要求电子类写死
        // res.data.processCapabilityCreateReqVO.productCategoryArr =
        //   getTreeMap(52, this.categoryList)
        // this.qualificationAndCertification.forEach(item => {
        //   item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.QUALIFICATION_AND_CERTIFICATION, item)
        // })

        const searchInput = (arr, dict, item) => {
          return arr.find(dic => dic.dictDataValue === item.value && dic.dictTypeValue === dict)?.value
        }
        this.supplierSaleArea.forEach(item => {
          item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.SUPPLIER_SALE_AREA, item)
        })
        this.supplierDev.forEach(item => {
          item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.SUPPLIER_QUALIFICATIONS_OF_DEVELOPER, item)
        })
        this.supplierCertificate.forEach(item => {
          item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.SUPPLIER_SPECIAL_WORK_QUALIFICATION_CERTIFICATE, item)
        })
        this.supplierStaff.forEach(item => {
          item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.SUPPLIER_STAFF, item)
        })
        res.data.dictRelCreateReqVO.map(item => {
          if (item.dictTypeValue === DICT_TYPE.ENTERPRISE_MANAGEMENT_SYSTEM && item.value === 'true') {
            this.manageSystem.push(item.dictDataValue)
          }
        })
        if (res.data.turnoverCreateReqVO.length < 3) {
          res.data.turnoverCreateReqVO = [
            {
              currency: '',
              turnover: '',
              unit: '',
              year: new Date().getFullYear()
            },
            {
              currency: '',
              turnover: '',
              unit: '',
              year: new Date().getFullYear() - 1
            }, {
              currency: '',
              turnover: '',
              unit: '',
              year: new Date().getFullYear() - 2
            }
          ]
        }
        this.supplierInfo = res.data
        // this.changeRegion(this.supplierInfo.countryRegion)
        this.getFloorDistance()
        if (this.$route.name.toLowerCase() === '/supplieremail/:id' || this.$route.name.toLowerCase() === '/supplierregister/:id') {
          this.enterEdit()
          this.editMode = true
        }
        // 处理查看页面的按钮逻辑
        // 1.隐藏全部的编辑按钮
        if (this.viewOnly) {
          this.editPer = false
          this.editMode = false
        }
      })
    },
    hasNodeWithId(node, targetId) {
      if (node) {
        if (node.id === targetId) {
          return true
        }
        if (node.children) {
          for (const child of node.children) {
            if (this.hasNodeWithId(child, targetId)) {
              return true
            }
          }
        }
        return false
      }
    },
    enterEdit() {
      this.editMode = !this.editMode
      if (!this.supplierInfo.contactCreateReqVO || this.supplierInfo.contactCreateReqVO.length === 0) {
        this.supplierInfo.contactCreateReqVO.push({
          name: '',
          position: '',
          phone: '',
          email: '',
          division: ['the_main_contact'],
          businessDictRelStr: ''
        })
      } else {
        if (!this.supplierInfo.contactCreateReqVO[0].division || this.supplierInfo.contactCreateReqVO[0].division.length === 0) {
          this.supplierInfo.contactCreateReqVO[0].division = ['the_main_contact']
        }
      }
    },
    closeBrand(str) {
      this.brandVisible = false
      this.supplierInfo.processCapabilityCreateReqVO.businessDictRelStr = str
    },
    getCetiAndQualiForm() {
      const searchInput = (arr, dict, item) => {
        return arr.find(dic => dic.dictDataValue === item.value && dic.dictTypeValue === dict)?.value
      }
      getQualifAndCert({
        supplierId: this.supplierId
      }).then(res => {
        res.data.reason = res.data.dictRelCreateReqVO?.find(item => item.dictDataValue === 'Do you accept flight inspection')?.reason
        this.qualificationAndCertification.forEach(item => {
          item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.QUALIFICATION_AND_CERTIFICATION, item)
        })
        this.qualificationAndCertificationStatic.forEach(item => {
          item.inputValue = searchInput(res.data.dictRelCreateReqVO, DICT_TYPE.QUALIFICATION_AND_CERTIFICATION, item)
        })
        this.cetiAndQualFormStatic = deepClone(res.data)
        this.cetiAndQualForm = res.data
      })
    },
    getErpCompany() {
      getCompanyRel({
        supplierId: this.supplierId
      }).then(res => {
        this.erpCompany = res.data
        this.erpCompanyList = deepClone(res.data)
      })
    },
    getErpPurchase() {
      getPurchaseRel({
        supplierId: this.supplierId
      }).then(res => {
        res.data.map(item => {
          item.category = item.categoryIds.map(a => {
            return getTreeMap(a, this.allCategoryList)
          })
        })
        this.erpPurchase = res.data
        this.erpPurchaseList = deepClone(res.data)
      })
    },
    getPaymentInfo() {
      getPayment({
        supplierId: this.supplierId
      }).then(res => {
        res.data.supplierId = this.supplierId
        res.data.bankInfoCreateReqVO.forEach(a => {
          a.showFile = false
          if (!a.fileRelList) {
            a.fileRelList = []
          }
        })
        this.paymentInfoStatic = deepClone(res.data)
        this.paymentInfo = { ...this.paymentInfo, ...res.data }
        this.log.businessId = res.data.id
      })
    },
    // 保存1.4银行信息
    // extra 20221019Lisa补充 选择 币种为非人民币时， SWIFT code字段必填
    savePaymentInfo() {
      this.$refs.payment.validate((valid) => {
        if (valid) {
          this.paymentLoading = true
          // extra 校验 本币已经按照公司维度设置了，这边的逻辑其实和本币无关。只有非RMB的才需要SWIFT Code，所以不是本币概念。
          this.getConfigKey('system.currency.rmb').then(response => {
            // 有配置的时候，验证选择外币时，SWIFT Code为必填项
            if (response.data) {
              // 配置的是人民币的编码，转换为id集合
              const rmbIds = getDictDatas(DICT_TYPE.COMMON_CURRENCY).filter(item => response.data.includes(item.code)).map(item => (item.id))
              if (rmbIds) {
                for (const i of this.paymentInfo.bankInfoCreateReqVO) {
                  if (!i.currency) {
                    continue
                  }
                  if (!rmbIds.includes(i.currency)) {
                    if (!i.swiftCode) {
                      this.$message.error(this.$t('supplier.whenSelectingAForeignCurrencySwifCodeIsARequiredField'))
                      this.paymentLoading = false
                      return
                    }
                  }
                }
              }
            }
            this.paymentInfo.creditCode = this.supplierInfo.creditCode
            this.paymentInfo.countryRegion = this.supplierInfo.countryRegion
            updatePaymentInfo(this.paymentInfo, {
              supplierId: this.supplierId
            }).then(res => {
              this.$message.success(this.$t('common.savedSuccessfully'))
              this.getPaymentInfo()
              this.paymentLoading = false
              this.paymentVisible = false
              // eslint-disable-next-line handle-callback-err
            }).catch(err => {
              this.paymentLoading = false
            })
          })
        }
      })
    },
    async saveSupplierInfo(obj) {
      let pass = true
      let errField = {}
      if (obj) {
        for (const item of ['baseInfo', 'personInfo']) {
          this.$refs[item].validate((valid, object) => {
            if (!valid) {
              errField = { ...errField, ...object }
              pass = false
            }
          })
        }
        const contactEmailValid = []
        for (const k in this.supplierInfo.contactCreateReqVO) {
          this.$refs[`contactItem${k}`].validate((valid, object) => {
            if (!valid) {
              contactEmailValid.push(`contactItem${k}`)
            }
          })
          // this.$refs[`contactItem${k}`].clearValidate()
        }
        if (contactEmailValid.length) {
          this.$refs[contactEmailValid].$el.scrollIntoView({
            block: 'center',
            behavior: 'smooth'
          })
          this.$message.error(this.$t('supplier.pleaseCompleteTheInformation'))
          return false
        }
        const contactDivisionValid = []
        for (const k in this.supplierInfo.contactCreateReqVO) {
          this.$refs[`contactItemDivision${k}`].validate((valid, object) => {
            if (!valid) {
              contactDivisionValid.push(`contactItemDivision${k}`)
            }
          })
          // this.$refs[`contactItemDivision${k}`].clearValidate()
        }
        if (contactDivisionValid.length) {
          this.$refs[contactDivisionValid].$el.scrollIntoView({
            block: 'center',
            behavior: 'smooth'
          })
          this.$message.error(this.$t('supplier.pleaseCompleteTheInformation'))
          return false
        }
        if (!pass) {
          // 定位代码
          this.$refs[Object.keys(errField)[0]].$el.scrollIntoView({
            block: 'center',
            behavior: 'smooth'
          })
          this.$message.error(this.$t('supplier.pleaseCompleteTheInformation'))
          return false
        }
        if (!this.supplierInfo.contactCreateReqVO.every(item => item.name && item.division.length > 0)) {
          this.$refs.mainContact.$el.scrollIntoView({
            block: 'center',
            behavior: 'smooth'
          })
          this.$message.error(this.$t('supplier.mainContactNameAndContactDivisionAreRequired'))
          return false
        }
        if (!this.supplierInfo.contactCreateReqVO.some(item => item.division.includes('the_main_contact'))) {
          this.$refs.mainContact.$el.scrollIntoView({
            block: 'center',
            behavior: 'smooth'
          })
          this.$message.error(this.$t('supplier.thereMustBeAtLeastOnePrimaryContactTheDivisionOfLaborIsThePrimaryContact'))
          return false
        }
      }
      const flatDictData = (arr, dictName) => {
        return arr.map(item => {
          return {
            value: item.inputValue,
            dictDataValue: item.value,
            dictTypeValue: dictName,
            reason: this.supplierInfo.reason
          }
        })
      }
      this.supplierInfo.productInfoCreateReqVO.map(item => {
        item.category = item.categoryArr.at(-1)
      })
      this.supplierInfo.processCapabilityCreateReqVO.productCategory = this.supplierInfo.processCapabilityCreateReqVO.productCategoryArr.at(-1)
      this.supplierInfo.contactCreateReqVO.map(item => {
        item.businessDictRelStr = item.division.join(',')
      })
      this.supplierInfo.dictRelCreateReqVO = [
        // ...flatDictData(this.qualificationAndCertification, DICT_TYPE.QUALIFICATION_AND_CERTIFICATION),
        ...flatDictData(this.supplierSaleArea, DICT_TYPE.SUPPLIER_SALE_AREA),
        ...flatDictData(this.supplierDev, DICT_TYPE.SUPPLIER_QUALIFICATIONS_OF_DEVELOPER),
        ...flatDictData(this.supplierCertificate, DICT_TYPE.SUPPLIER_SPECIAL_WORK_QUALIFICATION_CERTIFICATE),
        ...flatDictData(this.supplierStaff, DICT_TYPE.SUPPLIER_STAFF),
        ...this.manageSystem.map(item => {
          return {
            value: true,
            dictDataValue: item,
            dictTypeValue: DICT_TYPE.ENTERPRISE_MANAGEMENT_SYSTEM
          }
        })
      ]
      if (this.supplierInfo.status === 2) {
        // 资源库内走后端的计算信息完成率
      } else {
        // 资源库之前走页面的计算
        this.supplierInfo.infoPercent = this.infoPercent
      }
      return updateSupplierInfo({ ...this.supplierInfo, authScoreRecordCreateReqVO: this.onSiteAuditList })
    },
    async saveSupplierInfoEvent() {
      const data = await this.saveSupplierInfo(false)
      if (data) {
        this.$message.success(this.$t('common.savedSuccessfully'))
      }
    },
    // 供应商信息提交
    async submitSupplierInfo() {
      const data = await this.saveSupplierInfo(true)
      if (data) {
        submitSupplier({
          id: this.supplierId
        }).then(res => {
          if (this.$store.getters.userId === -1) {
            this.$message.success(this.$t('supplier.HaveSuccessfullySubmitted'))
          } else {
            this.$message.success(this.$t('supplier.submittedSuccessfully'))
          }

          // 匿名供应商提交后 需要隐藏顶部的进入编辑按钮；1.资源库进入提交后编辑按钮存在；2.供应商账户进入提交后编辑按钮存在
          this.editMode = false
          if (this.$store.state.user.id === -1) {
            this.editPer = false
            this.$store.dispatch('LogOut').then(() => {
              location.href = getPath('/index')
            })
          }
        })
      }
    },
    // 顶部取消操作
    cancel() {
      this.editMode = false
      // 清除必填项的校验
      this.$refs.baseInfo?.clearValidate()
      this.$refs.personInfo?.clearValidate()
    },
    submitQualificationAndCertification() {
      const flatDictData = (arr, dictName) => {
        return arr.map(item => {
          return {
            value: item.inputValue,
            dictDataValue: item.value,
            dictTypeValue: dictName,
            reason: this.cetiAndQualForm.reason
          }
        })
      }
      this.cetiAndQualForm.dictRelCreateReqVO = flatDictData(this.qualificationAndCertification, DICT_TYPE.QUALIFICATION_AND_CERTIFICATION)
      updateQualifAndCert(this.cetiAndQualForm, {
        supplierId: this.supplierId
      }).then(res => {
        this.getCetiAndQualiForm()
        this.qualificationAndCertificationVisible = false
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
      })
    },
    formatDictValue(type) {
      return getDictDatas(type).map(item => {
        this.$set(item, 'inputValue', '')
        this.$set(item, 'reason', '')
        return {
          ...item
        }
      })
    },
    changeRegion(val, item) {
      if (item) {
        item.province = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(a => a.parentId === val)
        item.provinceCity = ''
      } else {
        // 国家切换需要对社会统一信用代码为中国时添加必填验证
        this.getCreditCodeRule()
        this.province = getDictDatas(DICT_TYPE.COMMON_COUNTRY).filter(a => a.parentId === val)
        this.supplierInfo.provinceCity = ''
      }
    },
    cpAddress(item) {
      item.countryRegion = this.supplierInfo.countryRegion
      item.street = this.supplierInfo.street
      item.provinceCity = this.supplierInfo.provinceCity
      item.province = this.province
      item.zipCode = this.supplierInfo.zipCode
    },
    async delErpCompany() {
      const data = await this.$refs.erpCompany.removeCheckboxRow()
      this.erpCompany = this.erpCompany.filter(item =>
        !data.rows.map(a => a._X_ROW_KEY).includes(item._X_ROW_KEY)
      )
    },
    async delErpPurchase() {
      const data = await this.$refs.erpPurchase.removeCheckboxRow()
      this.erpPurchase = this.erpPurchase.filter(item =>
        !data.rows.map(a => a._X_ROW_KEY).includes(item._X_ROW_KEY)
      )
    },
    submitErpCompany() {
      updateCompanyRel(this.erpCompany, {
        supplierId: this.supplierId
      }).then(res => {
        this.getErpCompany()
        this.erpCompanyVisible = false
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
      })
    },
    submitErpPurchase() {
      const postData = this.erpPurchase.map(item => {
        item.categoryIds = item.category?.map(a => a.at(-1))
        return item
      })
      updatePurchaseRel(postData, {
        supplierId: this.supplierId
      }).then(res => {
        this.getErpPurchase()
        this.erpPurchaseVisible = false
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
      })
    },

    getPulleyTopDistance() {
      window.onscroll = throttle(100, true, () => {
        this.getFloorDistance()
        this.clientHeight = document.querySelector('.supplier').clientHeight - document.body.offsetHeight + 40
        this.scrolltop = document.documentElement.scrollTop || document.body.scrollTop
      })
    },
    getFloorDistance() {
      this.$nextTick(() => {
        // 获取锚点及屏幕高度
        this.clientHeight = document.querySelector('.supplier').clientHeight - document.body.offsetHeight + 40
        this.basicPos = document.getElementById('basic').offsetTop
        this.businessPos = document.getElementById('business').offsetTop
        this.categroyPos = document.getElementById('category').offsetTop
        this.qualityPos = document.getElementById('quality').offsetTop
        this.documentPos = document.getElementById('document').offsetTop
        this.erpPos = document.getElementById('erp').offsetTop
      })
    },
    toMark(val) {
      document.documentElement.scrollTop = val - 100
    },
    // 上传时的钩子方法，不允许点击上传组件
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 主要设备清单取消-需要删除当前已经上传的文件
    cancelMainEquipmentUpload() {
      this.uploadVisible = false
      this.$refs.upload.clearFiles()
    },
    // 上传成功后的钩子方法
    handleFileSuccess(response, file, fileList) {
      // 放在第一行，否则下次点击该上传组件失效
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code === 401) {
        return handleAuthorized()
      } else if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 该上传api处理excel导入错误不会返回错误码，是将错误信息放在返回对象中体现，so需要手动判断
      if (response.data.failureEquipments || response.data.checkErrorEquipments) {
        this.$message.error(this.$t('supplier.failedToUploadTheFilePleaseCheckTheFileFormat'))
        this.uploadVisible = false
      } else {
        this.supplierInfo.equipmentCreateReqVO = response.data.equipmentRespVO
        this.$message.success(this.$t('common.uploadSucceeded'))
        this.uploadVisible = false
        this.reloadDocRepository()
      }
    },
    reloadDocRepository() {
      this.docRepositoryRef = false
      this.$nextTick(() => {
        this.docRepositoryRef = true
        // this.$refs.docRepositoryRef.value.getList()
      })
    },
    handleUpload() {
      this.uploadVisible = false
      this.showUpload = false
      this.$refs.upload.submit()
    },
    OnSuccess(response, file, fileList) {
      if (response.code === 401) {
        return handleAuthorized()
      } else if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      repositoryFileBind({
        files: [
          {
            fileId: response.data.id,
            name: file.name,
            size: file.size
          }
        ],
        supplierId: Number(this.supplierId),
        uploadEntryType: 'supplier_homepage_1.4section'

      }).then(res => {
        this.$modal.msgSuccess(this.$t(this.$t('common.uploadSucceeded')))
        this.reloadDocRepository()
      })
    },
    downloadTemplate() {
      exportEquip({
        supplierId: this.supplierId
      }).then(res => {
        this.$download.excel(res, this.$t('supplier.deviceInformationxls'))
      })
    },
    // 文件上传框添加文件时触发。校验失败则移除文件
    whenFileChange(file, fileList) {
      if (file.size > process.env.VUE_APP_FILESIZE * 1024 * 1024) {
        $modal.msgError(this.$t('common.uploadFileSizeCannotExceedm', { fileSize: process.env.VUE_APP_FILESIZE }))
        this.$refs.upload.abort(file)
        return false
      }

      if (!this.fileType.split(',').includes(file.name.slice(file.name.lastIndexOf('.') + 1))) {
        $modal.msgError(this.$t('supplier.unsupportedFileFormat'))
        var indexOf = fileList.indexOf(file)
        fileList.splice(indexOf, 1)
        this.$refs.upload.abort(file)
        return false
      }
    },
    beforeUploadXls(file) {
      if (file.size > process.env.VUE_APP_FILESIZE * 1024 * 1024) {
        $modal.msgError(this.$t('common.uploadFileSizeCannotExceedm', { fileSize: process.env.VUE_APP_FILESIZE }))
        return false
      }
      if (!['xls', 'xlsx'].includes(file.name.slice(file.name.lastIndexOf('.') + 1))) {
        $modal.msgError(this.$t('supplier.unsupportedFileFormat'))
        return false
      }
    },
    getSupplierCodeAndName(supplierId) {
      const data = getDictDatas(DICT_TYPE.COMMON_COMPANY).find(item => item.id == supplierId)
      return data?.code + '-' + data?.name
    },
    cpStreet() {
      const index = this.supplierInfo.registeredAddress?.indexOf('市')
      if (index > -1) {
        this.supplierInfo.street = this.supplierInfo.registeredAddress.slice(index + 1)
      } else {
        this.supplierInfo.street = this.supplierInfo.registeredAddress
      }
    },
    beforeUpload(file) {
      if (file.size > 5242880) {
        $modal.msgError(this.$t('common.uploadFileSizeCannotExceedm'))
        return false
      }
      if (this.bankFileList.length > 5) {
        $modal.msgError(this.$t('order.onlyUpToAttachmentsAreSupported'))
        return false
      }
    },
    onRemove(file, fileList, item) {
      item.fileRelList.splice(item.fileRelList.find(a => a.id === file.id), 1)
    },
    onBankInfoFileSuccess(response, file, item) {
      item.fileRelList.push({
        name: file.name,
        url: response.data.url,
        fileId: response.data.id
      })
    },
    onPreview(file) {
      window.open(file.url)
    },
    getTYCData() {
      readTianYanChaInfo({
        supplierName: this.supplierInfo.name
      }).then(res => {
        if (res.data != null) {
          this.supplierInfo.registeredAddress = res.data.registeredAddress
          this.supplierInfo.creditCode = res.data.creditCode
          this.supplierInfo.registeredCapital = res.data.registeredCapitalOfCurrency
          this.supplierInfo.registeredCapitalCurrency = res.data.registeredCapitalCurrency
          this.supplierInfo.legalPerson = res.data.legalPerson
          this.supplierInfo.webSite = res.data.webSite
          this.supplierInfo.establishedTime = res.data.establishedTime
        } else {
          this.$message.info(this.$t('supplier.noInformationRelatedToTianyanchaWasObtained'))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.baseInfo {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;

    .el-form-item__content {
      width: calc(100% - 155px);
    }
  }
}

.associatedCompany {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}

.shadow {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}

.supplierStaff {
  ::v-deep .el-form-item {
    width: 100%;
    margin-right: 0;
    padding-right: 10px;
  }
}

::v-deep .el-input__inner {
  color: #606266 !important;
  //background: #ffffff!important;
}

.verticalMiddle {
  display: flex;
  align-items: center
}

::v-deep .descriptionLabel {
  width: 145px;
  font-weight: bold;
  display: inline-block;
  text-align: right;
}

.upload p {
  line-height: 12px;
}

.required:before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

.supplier {
  .tableTitle {
    padding-left: 70px;
    font-size: 15px;
    font-weight: bold;
    margin: 20px 0;
  }

  .tablePadding {
    padding: 0 20px
  }

  .shadowPadding {
    padding: 5px 0;
    margin: 15px
  }

  .supplier-progress {
    height: 3px;
    width: 50%;
    background-color: #323232;
  }

  margin: 0 auto;
  width: 1000px;
  background: #c1c1c1;
  padding: 20px;

  .active {
    background-color: #4996b8;
    color: #fff
  }

  &-header {
    font-size: 16px;
    width: 100%;
    padding-bottom: 25px;
    margin-bottom: 20px;

    &-right {
      text-align: right;
      font-size: 12px;
      color: #173b75;
      margin-right: 20px
    }
  ;

    &-btn {
      cursor: pointer;
      padding: 10px 15px;
      display: inline-block;
      background-color: #cecece;
      color: #fff
    }
  ;
  }

  .smallInput {
    width: 161px;
  }

  .smallSelect {
    width: 188px;
  }

  .bigBaseInput {
    width: 533px
  }

  .bigInput {
    width: 591px
  }

  .colCenter {
    text-align: center;
  }

  .form-title {
    border-left: 6px solid #376092;
    margin: 10px 0;
    padding: 10px 30px;
    font-size: 16px;
    font-weight: bold;
    background-color: #f1f1f1;
  }

  // 上传输入框的文件列表样式调整#文字加粗
  ::v-deep .el-upload-list__item-name {
    font-weight: 600;
  }

  .centerRow {
    ::v-deep .el-form-item__content {
      width: calc(100% - 139px);
      text-align: center;
    }
  }

  .ellipsis-line {
    width: 248px;
    overflow: hidden;
    text-overflow: ellipsis; //文本溢出显示省略号
    white-space: nowrap; //文本不会换行
  }

  .shadow {
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }

  .rawMaterialFlex {
    flex: 0 1 15%;
    text-align: center;
  }

  .customItem {
    flex: 0 1 25%;
    text-align: center;
  }

  .form-main {
    .product {
      display: flex;
      min-height: 150px;

      .product-left {
        flex: 0 1 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 5px 10px;

        .product-title {
          flex: none;
          font-weight: bold;
          text-align: center;
          word-break: break-all;
          font-size: 20px;
          margin: 10px 0;
        }

        .product-detail {
          word-break: break-all;
          flex: auto;
          text-align: center;
        }
      }

      .product-right {
        justify-content: center;
        align-items: center;
        flex: 0 1 50%;
        border-left: 1px #dcdcdc solid;
        display: flex;
        flex-wrap: wrap;
      }
    }

  }

}

</style>
