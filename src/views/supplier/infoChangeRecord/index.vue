<template>
  <div class="app-container">
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.searchText"
        :placeholder="$t('supplier.pleaseEnterTheSupplierNameSupplierCodeAbbreviationAndApplicationNumber')"
        clearable
        style="flex: 0 1 40%"
        @keyup.enter.native="queryParams.pageNo = 1;getList();"
      />
      <el-button type="primary" plain @click="queryParams.pageNo = 1;getList();">{{ $t('common.search') }}</el-button>
      <el-button plain style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button type="text">
          {{ $t('common.advancedSearch') }}
        </el-button>
        <i
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>
    </div>

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="120px" size="small">
      <el-form-item :label="$t('sl.applicant')" class="searchItem" prop="applyUserId">
        <el-select
          v-model="queryParams.applyUserId"
          :placeholder="$t('sl.pleaseSelectTheApplicant')"
          class="searchValue"
          clearable
          filterable
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.status')" class="searchItem" prop="status">
        <el-select
          v-model="queryParams.status"
          :placeholder="$t('supplier.pleaseSelectStatus')"
          class="searchValue"
          clearable
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CHANGE_RECORD_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('auth.documentType')" class="searchItem" prop="recordType">
        <el-select
          v-model="queryParams.recordType"
          :placeholder="$t('supplier.pleaseSelectTheDocumentType')"
          class="searchValue"
          clearable
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CHANGE_TYPE).filter(i => i.value !== 'doc_repository')"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('sl.todoPerson')" class="searchItem" prop="assigneeUserId">
        <el-select
          v-model="queryParams.assigneeUserId"
          :placeholder="$t('sl.pleaseSelectATodoPerson')"
          class="searchValue"
          clearable
          filterable
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('common.createTime')">
        <el-date-picker
          v-model="queryParams.time"
          class="searchValue"
          type="daterange"
          value-format="yyyy-MM-dd"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
        />
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['sl:application:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          plain
          type="primary"
          @click="handleExport"
        > {{ $t('order.download') }}
        </el-button>
      </el-col>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.canApproval&&scope.row.status=='waiting_process'"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleApprove(scope.row,false)"
          >{{ $t('sl.approval') }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('sl.applicant')" align="left" prop="applyUserName" />
      <el-table-column :label="$t('common.createTime')" align="left" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('supplier.modifyTheApplicationNumber')" align="left" prop="recordNo">
        <template slot-scope="scope">
          <copy-button
            @click="handleApprove(scope.row,true)"
          >{{ scope.row.recordNo }}
          </copy-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.status')" align="left" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SUPPLIER_CHANGE_RECORD_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('auth.documentType')" align="left" prop="recordType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SUPPLIER_CHANGE_TYPE" :value="scope.row.recordType" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('sl.todoPerson')" align="left" prop="assigneeUsers" />
      <el-table-column :label="$t('supplier.supplierCode')" align="left" prop="code" />
      <el-table-column :label="$t('auth.supplierName')" align="left" prop="name" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('supplier.shortNameOfSupplier')" align="left" prop="nameShort" :show-overflow-tooltip="true" />
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { exportInfoChangeRecordExcel, getInfoChangeRecordPage } from '@/api/supplier/infoChangeRecord'

export default {
  name: 'Infochangerecord',
  data() {
    return {
      parseTime,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 申请单主列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        searchText: null,
        recordType: null,
        applyUserId: null,
        assigneeUserId: null,
        status: null,
        time: [],
        beginCreateTime: null,
        endCreateTime: null
      }
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getDictDatas,
    /** 查询列表 */
    getList() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginCreateTime = this.queryParams.time[0] ? this.queryParams.time[0] + ' 00:00:00' : undefined
        this.queryParams.endCreateTime = this.queryParams.time[1] ? this.queryParams.time[1] + ' 23:59:59' : undefined
      }
      // 处理查询参数
      const params = {...this.queryParams}
      // 执行查询
      getInfoChangeRecordPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.searchText = ''
      this.queryParams.time = []
      this.queryParams.beginCreateTime = null
      this.queryParams.endCreateTime = null
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /**
     * 审批路由和查看单据路由
     * @param row   行信息
     * @param readOnly true:查看;false：审批
     */
    handleApprove(row, readOnly) {
      console.log(row)
      //
      if (row.recordType === 'base_info') { // 基础信息
        this.$router.push(`/supplier/BaseInfo/${row.id}?recordNo=${row.recordNo}&supplierId=${row.supplierId}&viewOnly=${readOnly}`)
      } else if (row.recordType === 'sys_and_ceti') { // 系统与资质
        this.$router.push(`/supplier/QualAndCertInfo/${row.id}?recordNo=${row.recordNo}&supplierId=${row.supplierId}&viewOnly=${readOnly}`)
      } else if (row.recordType === 'bank_info') { // 银行信息
        this.$router.push(`/supplier/bank/${row.id}?recordNo=${row.recordNo}&supplierId=${row.supplierId}&viewOnly=${readOnly}`)
      } else if (row.recordType === 'erp_view') { // ERP视图
        this.$router.push(`/supplier/erp/${row.id}?recordNo=${row.recordNo}&supplierId=${row.supplierId}&viewOnly=${readOnly}`)
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.queryParams.time?.length) {
        this.queryParams.beginCreateTime = this.queryParams.time[0] ? this.queryParams.time[0] + ' 00:00:00' : undefined
        this.queryParams.endCreateTime = this.queryParams.time[1] ? this.queryParams.time[1] + ' 23:59:59' : undefined
      }
      // 处理查询参数
      const params = {...this.queryParams}
      params.pageNo = undefined
      params.pageSize = undefined
      // 执行导出
      this.$modal.confirm(this.$t('supplier.areYouSureToExportAllData')).then(() => {
        this.exportLoading = true
        return exportInfoChangeRecordExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('supplier.supplierInformationRegistrationApplicationRecordXlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.searchValue {
  width: 80%;
}
</style>
