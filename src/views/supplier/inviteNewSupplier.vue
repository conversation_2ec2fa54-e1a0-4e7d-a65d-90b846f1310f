<template>
  <el-dialog :title="title" :visible.sync="visible" append-to-body width="640px">
    <el-form ref="inviteNewForm" :model="form" :rules="rules" label-width="130px">
      <el-form-item :label="$t('supplier.contacts')" prop="contact">
        <el-input v-model="form.contact" :placeholder="$t('supplier.contacts')" />
      </el-form-item>
      <el-form-item :label="$t('supplier.mailingAddress')" prop="email">
        <el-input v-model="form.email" :placeholder="$t('supplier.mailingAddress')" />
      </el-form-item>
      <el-form-item :label="$t('supplier.supplierName')" prop="name">
        <el-input v-model="form.name" :placeholder="$t('supplier.supplierName')" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
      <el-button type="primary" @click="sendInviteEmail">{{ $t('supplier.sendInvitationEmail') }}</el-button>

    </div>
  </el-dialog>
</template>
<script>
import { checkBeforeInvite, inviteSupplier } from '@/api/supplier/applyRepository'
import event from '@/views/dashboard/mixins/event'

export default {
  name: 'Invitenewsupplier', // 邀请新供应商组件
  mixins: [event],

  props: {
    // 父组件来源：apply-申请库；resource-资源库
    type: {
      required: true,
      type: String
    }
  },
  data() {
    return {
      // 遮罩层
      visible: false,
      title: this.$t('supplier.inviteNewSuppliers'),
      // 表单参数
      form: {
        contact: '',
        email: '',
        name: ''
      },
      // 表单校验
      rules: {
        contact: [{ required: true, message: this.$t('supplier.contacts'), trigger: 'blur' }],
        email: [{
          required: true,
          pattern: /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
          message: this.$t('supplier.incorrectMailboxFormat'),
          trigger: 'blur'
        }],
        name: [{ required: true, message: this.$t('supplier.supplierName'), trigger: 'blur' }]
      }
    }
  },
  methods: {
    // 发送邀请邮件
    sendInviteEmail() {
      this.$refs['inviteNewForm'].validate(valid => {
        if (valid) {
          const newType = this.type === 'apply' ? this.$t('supplier.registrationApplicationLibrary') : this.$t('supplier.supplierResourceLibrary')
          // 增加公司名重复校验
          checkBeforeInvite({ 'type': this.type, 'name': this.form.name }).then(res => {
            if (!res.data) { // true- 校验通过
              this.doSendEmail()
            } else { // 存在重复数据，在此确认
              this.$confirm(`${this.$t('supplier.theSystemHasDetectedTheCompanyName')}${this.form.name} ${this.$t('supplier.duplicateCompanyNameWithExistingSystem')}<br/>` +
                `${this.$t('supplier.theDuplicateDataIsLocatedAt')}${newType}<br/><br/>` +

                this.$t('supplier.pleaseConfirmIfYouStillWantToSendTheInvitationEmail'), this.$t('supplier.tips'), {
                dangerouslyUseHTMLString: true,
                confirmButtonText: this.$t('supplier.sendInvitationEmail'),
                cancelButtonText: this.$t('common.cancel'),
                type: 'warning'
              }).then(() => this.doSendEmail())
            }
          })
        }
      })
    },
    // 发送邮件
    doSendEmail() {
      const params = { ...this.form }
      inviteSupplier(params).then(res => {
        this.$message.success(this.$t('supplier.emailLinkSentSuccessfully'))
        this.$emit('refresh')
      }).catch(() => {
        this.$emit('refresh')
        // this.$message.error(this.$t('supplier.emailLinkSendingFailed'))
      })
      this.cancel()
    },
    // 取消发送
    cancel() {
      this.$refs['inviteNewForm'].resetFields()
      this.visible = false
    }
  }
}
</script>

<style scoped>

</style>
