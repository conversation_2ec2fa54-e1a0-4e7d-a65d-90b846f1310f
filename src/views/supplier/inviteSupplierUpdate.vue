<template>
  <div>
    <el-dialog :before-close="cancel" :title="title" :visible.sync="visible" append-to-body width="500px">
      <div>
        <span style="margin-top: 10px;">{{ $t('supplier.youAreAboutToShareTitle') }}</span>
        <el-divider />
        <el-form ref="form" :model="addition" label-width="100px" inline label-position="left">
          <el-form-item :label="$t('supplier.modifyContent')" prop="recordTypes">
            <el-select v-model="addition.recordTypes" multiple clearable>
              <el-option
                v-for="dict in supplierChangeType"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('supplier.invitationEmailAddress')" prop="emailList">
            <div v-for="(item,index) in addition.emailList" :key="index">
              <el-input v-model="addition.emailList[index]" style="width: 200px" />
              <i
                class="el-icon-circle-plus"
                style="margin-left:10px;font-size: 18px;cursor: pointer"
                @click="addition.emailList.push('')"
              />
              <i
                v-if="index !==0"
                class="el-icon-remove"
                style="margin-left:10px;font-size: 18px;cursor: pointer"
                @click="addition.emailList.splice(index,1)"
              />
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="sendEmail">{{ $t('common.submit') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="$t('common.shareInvitationLink')"
      :visible.sync="shareInvitationLink.open"
      append-to-body
      width="500px"
    >
      <el-form :model="shareInvitationLink" label-width="130px">
        <div v-for="item in shareInvitationLink.supplierInvitationAddress">
          <span>{{ item }}</span>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button
          v-clipboard:copy="shareInvitationLink.supplierInvitationAddress.join('\n')"
          v-clipboard:success="copyCancel"
          type="primary">{{ $t('order.copy') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { inviteShare } from '@/api/supplier/info'
import {DICT_TYPE, getDictDatas} from "../../utils/dict";

export default {
  name: 'Invitesupplierupdate',
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  props: {
    levelStatus: {
      type: String
    }
  },
  // 邀请供应商修改组件
  data() {
    return {
      // 遮罩层
      visible: false,
      title: this.$t('supplier.shareLinksToSuppliers'),
      supplierChangeType: [],
      // 邀请修改的供应商存在主要联系人的邮箱，则需要再次进行默认展示
      addition: {
        recordTypes: [],
        emailList: [],
        supplierId: null,
      },
      // 变更供应商状态
      shareInvitationLink: {
        // 是否显示弹出层
        open: false,
        // 供应商名称
        supplierInvitationAddress: []
      }
    }
  },
  watch: {
    levelStatus: {
      handler() {
        if (['S0', 'S1'].includes(this.levelStatus)) {
          this.supplierChangeType = getDictDatas(DICT_TYPE.SUPPLIER_CHANGE_TYPE).filter(a => a.value !== 'erp_view')
        } else {
          this.supplierChangeType = getDictDatas(DICT_TYPE.SUPPLIER_CHANGE_TYPE).filter(i => i.extendedValue === '2')
        }
      },
      immediate: true
    }
  },
  methods: {
    getDictDatas,
    // 发送邮件给供应商进行修改
    sendEmail() {
      if (this.addition.emailList.filter(email => email !== undefined && email !== '').length === 0) {
        this.$message.error(this.$t('supplier.pleaseEnterEmail'))
        return
      }
      // 校验邮箱格式
      if (!this.addition.emailList.every(email => /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(email))) {
        this.$message.error(this.$t('supplier.incorrectMailboxFormat'))
        return
      }

      inviteShare(this.addition).then(res => {
        this.shareInvitationLink.open = true
        this.shareInvitationLink.supplierInvitationAddress = res.data
      }).catch(() => {
        this.$message.error(this.$t('supplier.sendingInvitationModificationLinkFailed'))
      })
      this.cancel()
    },
    // 取消发送
    cancel(copy) {
      this.shareInvitationLink.open = false
      this.$refs['form'].resetFields()
      this.visible = false
      this.addition.emailList = []
    },
    // 复制后取消
    copyCancel() {
      this.$modal.msgSuccess(this.$t('system.successfullyCopied'))
      this.shareInvitationLink.open = false
      this.$refs['form'].resetFields()
      this.visible = false
      this.addition.emailList = []
    }
  }
}
</script>

<style scoped>

</style>
