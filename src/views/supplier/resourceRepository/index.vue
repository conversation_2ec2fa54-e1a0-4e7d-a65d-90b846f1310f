<template>
  <div class="app-container">
    <div
      v-has-permi="['supplier:resource:search']"
      style="text-align: right;color: #689CB7;font-size: 14px;cursor:pointer;"

      @click="showHead =!showHead"
    >
      {{ $t('common.statisticians') }}
      <i
        class="el-icon-s-data"
        style="font-size: 20px;"
      />
    </div>
    <Transition name="scale">

      <div v-show="showHead" style="display: flex;justify-content: center;min-width: 1000px;margin: 20px 0">
        <div v-loading="statisticsLoading" style="display: flex;width: 1000px;justify-content: space-between">
          <div class="realTab">
            <div style="display: flex;justify-content: space-between">
              <span>{{ $t('supplier.qualifiedSupplier') }}</span>

            </div>
            <div class="realNum">{{ statisticsObj.qualified }}</div>
          </div>
          <div class="realTab">
            <div style="display: flex;justify-content: space-between">
              <span>{{ $t('supplier.totalNumberOfSupplierResources') }}</span>

            </div>
            <div class="realNum">{{ statisticsObj.allCount }}</div>
          </div>
          <div class="realTab">
            <div style="display: flex;justify-content: space-between">
              <span>{{ $t('supplier.ytdNewlyIntroduced') }}</span>
            </div>
            <div class="realNum">{{ statisticsObj.introduction }}</div>
          </div>
          <div class="realTab">
            <div style="display: flex;justify-content: space-between">
              <span>{{ $t('supplier.ytdNewAccess') }}</span>
            </div>
            <div class="realNum">{{ statisticsObj.admittance }}</div>
          </div>
          <div
            style="display: flex;align-items: flex-end;"
          >
            <el-button type="text" @click="$router.push('/supplier/analysis')">{{ $t('system.more') }}</el-button>
          </div>
        </div>

      </div>
    </Transition>

    <!-- 搜索工作栏 -->
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.search"
        :placeholder="$t('supplier.pleaseEnterTheSupplierNameSearch')"
        clearable
        style="flex: 0 1 35%"
        @keyup.enter.native="handleQuery"
      />
      <el-button v-hasPermi="['supplier:resource:search']" plain type="primary" @click="handleQuery">
        {{ $t('common.search') }}
      </el-button>
      <el-button
        v-hasPermi="['supplier:resource:search']"
        style="margin-left: 0"
        size="mini"
        @click="resetQuery"
      >
        {{ $t('common.reset') }}
      </el-button>
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button v-hasPermi="['supplier:resource:search']" type="text">
          {{ $t('common.advancedSearch') }}
        </el-button>
        <i
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>
    </div>

    <!--    高级搜索的form-->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="96px" size="small">
      <el-form-item :label="$t('common.sourcing')" class="searchItem" prop="sourcingIds">
        <el-select v-model="queryParams.sourcingIds" class="searchValue" clearable filterable multiple>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('material.category')" class="searchItem" prop="category">
        <el-cascader
          v-model="queryParams.category"
          :options="categoryList"
          :placeholder="$t('material.category')"
          :props="{ value: 'id',label:'name',multiple :true}"
          class="searchValue"
          clearable
          filterable
        />
      </el-form-item>
      <el-form-item :label="$t('supplier.companyType')" class="searchItem" prop="companyCategory">
        <el-select v-model="queryParams.companyCategory" class="searchValue" clearable filterable>
          <el-option
            v-for="dict in companyCategoryList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="$t('supplier.level')"
        class="searchItem"
        prop="level"
      >
        <el-select
          v-model="queryParams.level"
          class="searchValue"
          clearable
          filterable
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_LEVEL_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('supplier.country')" class="searchItem" prop="countryIds">
        <el-cascader
          v-model="queryParams.country"
          :options="countryList"
          :placeholder="$t('supplier.pleaseSelectACountry')"
          :props="{ value: 'id',label:'name',multiple :true, checkStrictly:true}"
          :show-all-levels="false"
          class="searchValue"
          clearable
          filterable
          @change="changeOnSearch"
        />
      </el-form-item>
      <el-form-item :label="$t('supplier.nameOfSuppliedProduct')" class="searchItem" prop="productInfoCreateReqVOName">
        <el-input
          v-model="queryParams.productInfoCreateReqVOName"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          class="searchValue"
        />
      </el-form-item>
      <el-form-item :label="$t('supplier.brandOfRawMaterials')" class="searchItem" prop="rawMaterialCreateReqVORawMaterialBrand">
        <el-input
          v-model="queryParams.rawMaterialCreateReqVORawMaterialBrand"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          class="searchValue"
        />
      </el-form-item>
      <el-form-item
        v-if="$store.getters.creditModule"
        :label="$t('auth.creditScore')"
        class="searchItem"
      >
        <el-input-number
          v-model="queryParams.creditScoreStart"
          class="searchValue0"
          clearable
        />{{ $t('order.to') }}
        <el-input-number
          v-model="queryParams.creditScoreEnd"
          class="searchValue0"
          clearable
        />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('Customer')" prop="customer" style="width: 66%">
        <el-radio-group v-model="queryParams.customer" @change="getList()">
          <el-radio :label="null">{{ $t('order.whole') }}</el-radio>
          <el-radio v-for="customer in $store.getters.incapCustomers" :key="customer" :label="customer">
            {{ customer }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div style="padding-top: 20px">
      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8">

        <el-col :span="1.5" />
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['supplier:apply:invite']"
            :loading="exportLoading"
            icon="el-icon-plus"
            size="mini"
            type="primary"
            @click="openInviteModal"
          >{{ $t('supplier.inviteNewSuppliers') }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['supplier:resource:import']"
            :loading="exportLoading"
            icon="el-icon-upload2"
            size="mini"
            type="primary"
            plain
            @click="handleImport"
          >{{ $t('common.batchCreation') }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['supplier:resource:export']"
            plain
            :loading="exportLoading"
            :title="$t('supplier.clickToExportAllTheDataDisplayedInTheList')"
            icon="el-icon-download"
            size="mini"
            type="primary"
            @click="handleExport"
          >{{ $t('common.batchExport') }}
          </el-button>
        </el-col>
<!--        <el-col :span="1.5">-->
<!--          <el-button-->
<!--            v-if="$store.getters.creditModule"-->
<!--            v-hasPermi="['supplier:resource:export']"-->
<!--            plain-->
<!--            :loading="exportLoading"-->
<!--            :title="$t('点击可导出列表中已选择的供应商征信报告')"-->
<!--            icon="el-icon-upload"-->
<!--            size="mini"-->
<!--            type="primary"-->
<!--            @click="handleExportCreditReport"-->
<!--          >{{ $t('征信风险评估') }}-->
<!--          </el-button>-->
<!--        </el-col>-->
        <el-tooltip :content="$t('supplier.clickToExportAllTheDataDisplayedInTheList')" placement="top-start" />
        <right-toolbar
          :custom-columns.sync="girdOption.columns"
          :list-id="girdOption.id"
          :show-search.sync="showSearch"
          @queryTable="getList"
        />
      </el-row>
    </div>

    <!-- 列表 -->
    <vxe-grid
      ref="resourceTable"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
    >
      <template #supplierCode="{row}">
        <el-button type="text" @click="$router.push(`/supplier/cooperHis/${row.id}?supplierId=${row.id}&viewOnly=false`)">{{ row.code }}</el-button>
      </template>
      <template #companyCategory="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_COMPANY_NATURE" :value="row.companyCategory" />
      </template>
      <template #sourcingIds="{row}">
        <span>{{ formatterSourcingIds(row.sourcingIds) }}</span>
      </template>
      <template #levelStatus="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_LEVEL_STATUS" :value="row.levelStatus" />
      </template>

      <template #name="{row}">
        <copyButton type="text" @click="$router.push(`/supplier/supplierinfo/${row.id}?supplierName=${row.name}&viewOnly=true`)">{{ row.name }}</copyButton>
      </template>
      <template #operate="{row}">
        <OperateDropDown
          :menu-item="[
            {
              name: $t('supplier.creditInformation'),
              show: $store.getters.permissions.includes('supplier:resource:credit'),
              action: ()=>handleExportCreditReport(row),
              para:{}
            },
            {
              name: $t('supplier.invitationModification'),
              show: $store.getters.permissions.includes('supplier:resource:invite:modify'),
              action: (row) => openInviteUpdateModal(row),
              para:row
            },
            // KS:KSN-457(资源库- 变更状态  按钮取消)
            {
               name: $t('supplier.changeSupplierLevel'),
               show: $store.getters.permissions.includes('supplier:resource:level-status'),
               action: (row) => updateSupplierLevelStatus(row),
               para:row
             },
            {
              name: $t('supplier.supplierHistory'),
              show: $store.getters.permissions.includes('supplier:edit:baseinfo'),
              action: (row)=>$router.push(`/supplier/cooperHis/${row.id}?supplierId=${row.id}&name=${row.name}&viewOnly=false`),
              para:row
            },

            {
              name: $t('supplier.modifyBasicInformation'),
              show: $store.getters.permissions.includes('supplier:edit:baseinfo'),
              action: (row)=>$router.push(`/supplier/BaseInfo/0?supplierId=${row.id}&viewOnly=false`),
              para:row
            },
            {
              name: $t('supplier.modifyBankInformation'),
              show: $store.getters.permissions.includes('supplier:edit:bankinfo'),
              action: (row)=>$router.push(`/supplier/bank/0?supplierId=${row.id}&viewOnly=false`),
              para:row
            },
            {
              name: $t('supplier.modifyTheSystemAndQualifications'),
              show: $store.getters.permissions.includes('supplier:edit:sysAndCeti'),
              action: (row)=>$router.push(`/supplier/QualAndCertInfo/0?supplierId=${row.id}&viewOnly=false`),
              para:row
            },
            {
              name: $t('supplier.editDocument'),
              show: $store.getters.permissions.includes('supplier:edit:doc'),
              action: (row)=>$router.push(`/supplier/docRepository/0?supplierId=${row.id}`),
              para:row
            },
            {
              name: $t('supplier.modifyErpInformation'),
              show: $store.getters.permissions.includes('supplier:edit:erp'),
              action: (row)=>$router.push(`/supplier/erp/0?supplierId=${row.id}&viewOnly=false`),
              para:row
            }
          ]"
        /></template>
      <template #creditScore="{row}">
        <el-button
          type="text"
          @click="$router.push(`/supplier/report?supplierId=${row.id}&creditId=${row.creditId}&supplierName=${row.name}`)"
        >
          <!--    @click="downLoadCreditReport(row.creditFileUrl)"  下载   -->
          <span :style="`text-decoration: underline;color:${row.creditResult? '':'red'}`">{{ row.creditScore }}</span></el-button>
      </template>
    </vxe-grid>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
    <!--邀请供应商注册-->
    <InviteNewSupplier ref="inviteNewComponent" :type="'resource'" />
    <!--邀请供应商修改-->
    <InviteSupplierUpdate v-if="levelStatus" ref="inviteUpdateComponent" :level-status="levelStatus" />

    <!-- 供应商导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
        <p style="color: red">
          {{ $t('supplier.uploadSupplierWarning') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url"
          :auto-upload="false"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

    <!-- 变更供应商状态对话框 -->
    <el-dialog
      :title="$t('supplier.changeSupplierLevel')"
      :visible.sync="supplierStatus.open"
      append-to-body
      width="500px"
    >
      <el-form :model="supplierStatus" label-width="130px">
        <el-form-item :label="$t('supplier.supplierName')">
          <el-input v-model="supplierStatus.supplierName" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('supplier.level')">
          <el-select v-model="supplierStatus.levelStatus" filterable style="width: 100%">
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_LEVEL_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
              :disabled="dict.extendedValue === '0'"
            />
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="supplierStatus.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitSupplierStatus"> {{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  exportExcel,
  getResourceSupplierPage,
  getResourceSupplierStatistics,
  importSupplierTemplate,
  updateSupplierStatus
} from '@/api/supplier/resourceRepository'
import { DICT_TYPE, getDictDataLabel, getDictDatas } from '@/utils/dict'
import { parseTime } from '@/utils/ruoyi'
import InviteNewSupplier from '../inviteNewSupplier'
import InviteSupplierUpdate from '../inviteSupplierUpdate'
import { getBaseHeader } from '@/utils/request'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { createSupplierCreditReport } from '@/api/supplier/credit'

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Resource',
  components: { InviteNewSupplier, InviteSupplierUpdate, OperateDropDown },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 供应商列表
      list: [],
      // 弹出层标题
      title: '',
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        customer: null,
        // 顶部模糊搜索条件
        search: '',
        sourcing: '',
        rawMaterialCreateReqVORawMaterialBrand: '',
        productInfoCreateReqVOName: '',
        sourcingIds: [],
        category: [],
        country: [],
        companyCategory: '',
        level: '',
        countryIds: []
      },
      // 供应商导入参数
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/supplier/resource/import'
      },
      // 变更供应商状态
      supplierStatus: {
        // 是否显示弹出层
        open: false,
        // 供应商名称
        supplierName: '',
        // 供应商状态
        levelStatus: undefined,
        id: undefined
      },
      categoryList: [],
      countryList: [],
      // 表单参数
      form: {},
      // 公司类别字典集合
      companyCategoryList: getDictDatas(DICT_TYPE.SUPPLIER_TYPE, 0),
      girdOption: {
        id: 'resourceRepository',
        align: 'left',
        border: true,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isCurrent: true,
          isHover: true
        },
        keepSource: false,
        columns: [
          // todo 多选框选择时和按钮联动，待 功能按钮确定时开发
          { type: 'checkbox', width: 30, fixed: 'left' },
          {
            field: 'operate',
            slots: { default: 'operate' }, fixed: 'left',
            showOverflow: false,
            title: this.$t('common.operate'), visible: true, width: 35
          },
          {
            field: 'name',
            slots: { default: 'name' }, fixed: 'left',
            title: this.$t('supplier.supplierName'),
            visible: true, width: 170
          },
          {
            field: 'nameEn',
            title: this.$t('supplier.supplierNameEn'),
            visible: true, width: 170
          },
          { field: 'code', title: this.$t('supplier.supplierCode'),
            slots: { default: 'supplierCode' },
            visible: true, width: 170 },
          {
            field: 'involveTime',
            title: this.$t('supplier.introductionTime'),
            visible: true, width: 170,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          {
            field: 'sourcingIds',
            slots: { default: 'sourcingIds' },
            title: this.$t('supplier.demandFulfillmentPurchase'),
            visible: true,
            width: 170
          },
          {
            field: 'levelStatus',
            slots: { default: 'levelStatus' },
            title: this.$t('supplier.levelStatus'),
            visible: true, width: 170
          },
          { field: 'nameShort', title: this.$t('supplier.shortNameOfSupplier'), visible: true, width: 170 },
          { field: 'creditScore',
            slots: { default: 'creditScore' }, title: this.$t('auth.creditScore'), visible: true, width: 80 },
          {
            field: 'creditDate',
            title: this.$t('supplier.creditEvaluationDate'),
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true, width: 150
          },
          {
            field: 'authCategory',
            formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.COMMON_CATEGORY, cellValue?.split(',')) : '',
            title: this.$t('supplier.certifiedCategory'),
            visible: true, width: 170
          },
          {
            field: 'productCategory',
            formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.COMMON_CATEGORY, cellValue?.split(',')) : '',
            title: this.$t('supplier.supplyCategory'),
            visible: true, width: 170
          },
          {
            field: 'supplierHighestLevel',
            formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.SL_SUPPLIER_LEVEL, cellValue) : '',
            title: this.$t('supplier.suppliersHighestLevel'),
            visible: true, width: 170
          },
          { field: 'companyNature', title: this.$t('supplier.natureOfTheCompany'),
            formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.SUPPLIER_COMPANY_NATURE, cellValue) : '',
            visible: true, width: 170 },
          {
            field: 'companyCategory',
            title: this.$t('supplier.companyType'),
            formatter: ({ cellValue }) => cellValue ? this.convertToLabel(cellValue) : '',
            visible: true, width: 170
          },

          { field: 'contactName', title: this.$t('supplier.primaryContact'), visible: true, width: 170 },

          { field: 'phone', title: this.$t('supplier.mainContactInformation'), visible: true, width: 170 },
          { field: 'mainEmail', title: this.$t('supplier.emailAddress'), visible: true, width: 170 },
          {
            field: 'registeredAddress',
            title: this.$t('supplier.registeredAddressOfTheCompany'),
            visible: true,
            width: 170
          },
          { field: 'infoPercent', title: this.$t('supplier.informationCompletionRate'), visible: true, width: 170 },
          {
            field: 'certificateTime',
            title: this.$t('supplier.certificationCompletionTime'),
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true, width: 170
          }
        ],
        sortConfig: {
          remote: true
        },
        slots: {
          // 自定义工具栏模板
          buttons: 'toolbar_buttons'
        }
      },
      statisticsLoading: false,
      statisticsObj: {
        qualified: 0,
        admittance: 0,
        introduction: 0,
        allCount: 0
      },
      showHead: false,
      // 当前需要邀请修改的供应商的级别状态
      levelStatus: ''
    }
  },
  created() {
    this.getCountries()
    this.getList()
    this.getResourceSupplierStatistics()
  },
  mounted() {
    this.getCategories()
    if (!this.$store.getters.creditModule) {
      this.girdOption.columns = this.girdOption.columns.filter(a => !['creditScore', 'creditDate'].includes(a.field))
    }
  },
  methods: {
    // 搜索框的级联change事件
    changeOnSearch() {
      if (this.queryParams.country.length === 0) {
        this.queryParams.countryIds = []
      }
      if (this.queryParams.category.length === 0) {
        this.queryParams.categoryIds = []
      }
    },
    // 字典类型从value转成label进行展示
    convertToLabel(value) {
      for (const index in getDictDatas(DICT_TYPE.SUPPLIER_TYPE)) {
        if (this.companyCategoryList[index].value === value) {
          return this.companyCategoryList[index].label
        }
      }
    },
    /** 修改供应商状态 */
    submitSupplierStatus() {
      updateSupplierStatus(this.supplierStatus).then(response => {
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.supplierStatus.open = false
        this.getList()
      })
    },
    /** 弹出变更供应商状态 */
    updateSupplierLevelStatus(row) {
      this.supplierStatus.open = true
      this.supplierStatus.id = row.id
      this.supplierStatus.supplierName = row.name
      this.supplierStatus.levelStatus = row.levelStatus
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$modal.confirm(this.$t('supplier.areYouSureToExportTheSupplierDataItem')).then(() => {
        this.exportLoading = true
        return exportExcel(queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('supplier.supplierExportFileXls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    handleExportCreditReport(row) {
      createSupplierCreditReport({ supplierId: row.id, supplierName: row.name }).then(res => {
        if (res.data > 0) {
          this.$message.success(this.$t('supplier.successfullyGeneratedCreditReport'))
          this.getList()
        } else {
          if (res.data === 0) {
            this.$message.warning(this.$t('supplier.thisSupplierIsUnableToObtainCreditInformationFromThirdParties'))
          } else {
            this.$message.error(this.$t('supplier.failedToGenerateCreditReport'))
          }
        }
        this.exportLoading = false
      })
    },
    downLoadCreditReport(url) {
      if (url === null || url === '') {
        alert('未找到供应商征信报告')
        return
      } else {
        window.open(url, '_blank')
      }
    },
    /** 下载模板操作 */
    importTemplate() {
      importSupplierTemplate().then(response => {
        this.$download.excel(response, '供应商导入模板.xlsx')
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createSuppliers) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createSuppliers.length
      }
      if (data.failureSuppliers) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureSuppliers).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    /** 批量创建 */
    handleImport() {
      this.upload.title = this.$t('common.batchCreation')
      this.upload.open = true
    },
    // 跳转至天眼查官网
    jump() {
      var fullUrl = 'https://www.tianyancha.com/search'
      // 获取选中的第一个公司名称
      if (this.$refs.resourceTable.getCheckboxRecords.length > 0) {
        const name = this.$refs.resourceTable.getCheckboxRecords[0].name
        if (name !== undefined) {
          fullUrl = fullUrl + '?key=' + name
        }
      }
      window.open(fullUrl)
    },
    // 打开邀请新供应商弹框
    openInviteModal() {
      this.$refs.inviteNewComponent.visible = true
    },
    // 打开供应商修改弹框
    openInviteUpdateModal(row) {
      this.levelStatus = row.levelStatus
      this.$nextTick(() => {
        this.$refs.inviteUpdateComponent.visible = true
        // 添加默认显示的供应商邮件
        this.$refs.inviteUpdateComponent.addition.emailList.push(row.mainEmail)
        this.$refs.inviteUpdateComponent.addition.supplierId = row.id
      })
    },
    // 获取品类
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    // 获取国家
    getCountries() {
      this.countryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_COUNTRY, 0), 'id'))
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询品类
      this.queryParams.categoryIds = this.queryParams.category.map(item => item?.at(-1))
      this.queryParams.countryIds = this.queryParams.country.map(item => item?.at(-1))

      const params = { ...this.queryParams }
      // 执行查询
      getResourceSupplierPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    getResourceSupplierStatistics() {
      // 执行查询
      this.statisticsLoading = true
      getResourceSupplierStatistics().then(response => {
        this.statisticsObj = response.data
        this.statisticsLoading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        letterSort: undefined,
        status: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        // 顶部模糊搜索条件
        search: '',
        customer: '',
        sourcing: '',
        rawMaterialCreateReqVORawMaterialBrand: '',
        productInfoCreateReqVOName: '',
        category: [],
        country: [],
        companyCategory: '',
        level: '',
        countryIds: [],
        creditScoreStart: undefined,
        creditScoreEnd: undefined
      }
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 格式化寻源采购
    formatterSourcingIds(cellVal) {
      if (!cellVal || cellVal.length === 0) {
        return
      }
      var dictData = getDictDatas(DICT_TYPE.COMMON_USERS)
      const sourcingIds = []
      cellVal?.forEach(sourcingId => sourcingIds.push(dictData.filter(dictData => dictData.id === sourcingId)[0]?.name))
      return sourcingIds.join(',')
    }
  }
}
</script>
<style lang="scss" scoped>
.realTab {
  width: 220px;
  height: 90px;
  font-size: 16px;
  background: rgb(242, 244, 249);
  color: #000000;
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding: 21px;
}
.realNum{
  color: #327da1;
  font-size: 25px;
}
.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 96px);
  }
}

.searchValue {
  width: 80%;
}
.searchValue0{
width: 39%;
}
.scale-leave-active {
  -webkit-animation: scale-down-tr 0.4s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  animation: scale-down-tr 0.4s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}

.scale-enter-active {
  -webkit-animation: scale-up-tr 0.4s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
  animation: scale-up-tr 0.4s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
}

@-webkit-keyframes scale-up-tr {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@keyframes scale-up-tr {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@-webkit-keyframes scale-down-tr {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@keyframes scale-down-tr {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

</style>
