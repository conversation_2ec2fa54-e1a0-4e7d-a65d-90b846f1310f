<template>
  <div class="supplier">
    <div style="background-color: white">

      <div id="basic" class="form-title"><span style="margin-left:5px">{{ $t('supplier.companyInformation') }}</span></div>
      <div style="padding: 10px 66px">
        <el-form
          ref="baseInfo"
          :model="supplierInfo"
          :rules="supplierInfoRule"
          class="baseInfo"
          inline
          label-width="175px"
        >
          <el-form-item ref="name" :label="$t('Supplier Chinese Name')" prop="name">
            <show-or-edit :value="supplierInfo.name">
              <el-input v-model="supplierInfo.name" />
            </show-or-edit>
          </el-form-item>
          <el-form-item ref="name" :label="$t('Supplier English Name')" prop="name">
            <show-or-edit :value="supplierInfo.nameEn">
              <el-input v-model="supplierInfo.nameEn" />
            </show-or-edit>
          </el-form-item>
          <el-form-item ref="nameShort" :label="$t('Supplier ShortName')" prop="nameShort">
            <show-or-edit :value="supplierInfo.nameShort">
              <el-input v-model="supplierInfo.nameShort" />
            </show-or-edit>
          </el-form-item>
          <el-form-item ref="code" :label="$t('Vendor ID')" prop="code">
            <show-or-edit :value="supplierInfo.code">
              <el-input v-model="supplierInfo.code" />
            </show-or-edit>
          </el-form-item>
          <!--          供应商状态-->
          <el-form-item ref="levelStatus" :label="$t('Status')" prop="levelStatus">
            <show-or-edit :dict="DICT_TYPE.SUPPLIER_LEVEL_STATUS" :value="supplierInfo.levelStatus">
              <el-select v-model="supplierInfo.levelStatus" class="smallSelect" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_LEVEL_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>
          </el-form-item>
          <!--          Type-->
          <el-form-item ref="companyCategory" :label="$t('Type')" prop="companyCategory">
            <show-or-edit :dict="DICT_TYPE.SUPPLIER_TYPE" :value="supplierInfo.companyCategory">
              <el-select v-model="supplierInfo.companyCategory" :placeholder="$t('supplier.pleaseSelectStatus')" class="smallSelect" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>
          <!--          Nature-->
          <el-form-item ref="companyNature" :label="$t('Nature')" prop="companyNature">
            <show-or-edit :dict="DICT_TYPE.SUPPLIER_COMPANY_NATURE" :value="supplierInfo.companyNature">
              <el-select v-model="supplierInfo.companyNature" :placeholder="$t('supplier.pleaseSelectStatus')" class="smallSelect" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_COMPANY_NATURE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>
          <!--          Address1-->
          <el-form-item ref="registeredAddress" :label="$t('Address1')" prop="registeredAddress">
            <show-or-edit :value="supplierInfo.registeredAddress">
              <el-input v-model="supplierInfo.registeredAddress" />
            </show-or-edit>
          </el-form-item>
          <!--          Register Date-->
          <el-form-item ref="involveTime" :label="$t('Register Date')" prop="involveTime">
            <show-or-edit :value="supplierInfo.involveTime" type="Date">
              <el-date-picker
                v-model="supplierInfo.involveTime"
                :placeholder="$t('common.pleaseSelectADate')"
                class="smallSelect"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </show-or-edit>
          </el-form-item>
          <!--          Address2-->
          <el-form-item ref="registeredAddressSub" :label="$t('Address2')" prop="registeredAddressSub">
            <show-or-edit :value="supplierInfo.registeredAddressSub">
              <el-input v-model="supplierInfo.registeredAddressSub" />
            </show-or-edit>
          </el-form-item>
          <!--Category-->
          <el-form-item :label="$t('Category')" prop="category">
            <show-or-edit
              :dict="DICT_TYPE.COMMON_CATEGORY"
              :value="supplierInfo.category"
            >
              <cascading-category
                :original-value.sync="supplierInfo.category"
                :multiple="false"
              />
            </show-or-edit>
          </el-form-item>

          <!--          Del Term-->
          <el-form-item ref="deliveryType" :label="$t('Del Term')" prop="deliveryType">
            <show-or-edit :dict="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION" :value="supplierInfo.deliveryType">
              <el-select v-model="supplierInfo.deliveryType" :placeholder="$t('supplier.pleaseSelectStatus')" class="smallSelect" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>
          <!--          Payment Term-->
          <el-form-item ref="accountDay" :label="$t('Payment Term')" prop="accountDay">
            <show-or-edit :dict="DICT_TYPE.SUPPLIER_PAYMENT_DAYS" :value="supplierInfo.accountDay">
              <el-select v-model="supplierInfo.accountDay" :placeholder="$t('supplier.pleaseSelectStatus')" class="smallSelect" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>
        </el-form>
      </div>

      <!--      主要联系人配置-->
      <div class="form-title"><span style="margin-left:5px">{{ $t('supplier.personnelInformation') }}</span></div>
      <div class="form-main">
        <div class="shadow shadowPadding">
          <div class="tableTitle">
            {{ $t('supplier.primaryContact') }}
          </div>
          <div class="tablePadding">
            <el-table :data="supplierInfo.contactCreateReqVO">
              <el-table-column :label="$t('Contact')" prop="name">
                <template slot-scope="scope">
                  <show-or-edit :value="scope.row.name">
                    <el-input ref="mainContact" v-model="scope.row.name" />
                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column :label="$t('Tel.')" prop="phone">
                <template slot-scope="scope">
                  <show-or-edit :value="scope.row.phone">
                    <el-input v-model="scope.row.phone" />
                  </show-or-edit>
                </template>
              </el-table-column>
              <el-table-column min-width="110" prop="email">
                <template #header>
                  <div>{{ $t('Email') }}</div>
                </template>
                <template slot-scope="scope">
                  <el-form :ref="`contactItem${scope.$index}`" :model="scope.row">
                    <el-form-item
                      prop="email"
                      style="width: 100%;margin: auto !important;"
                    >
                      <show-or-edit :value="scope.row.email">
                        <el-input v-model="scope.row.email" />
                      </show-or-edit>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column :label="$t('supplier.contactDivision')" min-width="160" prop="divisionList">
                <template #header>
                  <div>{{ $t('supplier.contactDivision') }}</div>
                </template>
                <template slot-scope="scope">
                  <el-form :ref="`contactItemDivision${scope.$index}`" :model="scope.row">
                    <el-form-item
                      prop="divisionList"
                      style="width: 100%;margin: auto !important;"
                    >
                      <show-or-edit :dict="DICT_TYPE.SUPPLIER_CONTACT_DIVISION" :value="scope.row.divisionList">
                        <el-select v-model="scope.row.divisionList" clearable filterable multiple :placeholder="$t('supplier.multipleOptionsAvailable')" style="width:100%">
                          <el-option
                            v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CONTACT_DIVISION)"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>
                      </show-or-edit>
                    </el-form-item>
                  </el-form>

                </template>
              </el-table-column>
              <el-table-column prop="op" width="80">
                <template slot-scope="scope">
                  <div style="text-align: right;margin-top: 5px">
                    <i
                      class="el-icon-circle-plus"
                      style="margin-left:2px;font-size: 18px;cursor: pointer"
                      @click="supplierInfo.contactCreateReqVO.push({name: '',phone: '',email: '',divisionList: []})"
                    />
                    <i
                      class="el-icon-remove"
                      style="margin-left:5px;font-size: 18px;cursor: pointer"
                      @click="
                        supplierInfo.contactCreateReqVO.length===1?
                          supplierInfo.contactCreateReqVO=[{name: '',phone: '',email: '',divisionList: []}]:supplierInfo.contactCreateReqVO.splice(scope.$index,1)"
                    />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div class="fixedBottom">
        <el-button
          plain
          @click="doCancel"
        >{{ $t('sp.cancel') }}
        </el-button>
        <el-button
          plain
          class="commonBtn"
          type="primary"
          @click="doSaveSimpleBaseInfo"
        >{{ $t('common.save') }}
        </el-button>
      </div>
    </div>

  </div>
</template>

<script>
import Sticky from '@/components/Sticky'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import BpmProcessInstance from '@/components/BpmProcessInstance/index.vue'
import brand from '@/views/supplier/info/brand'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { getSupplierBaseInfo, saveSimpleBaseInfo } from '@/api/supplier/simplesupplier'

export default {
  name: 'Simpleinfo/:id',
  components: {
    ShowOrEdit,
    brand,
    Sticky, BpmProcessInstance
  },
  data() {
    return {
      supplierInfo: {
        id: '',
        code: '', // Vendor ID
        involveTime: undefined, // Register Date
        name: '', // Supplier FullName
        nameEn: '', // Supplier 英文名称
        nameShort: '', // Supplier ShortName
        levelStatus: '', // Status
        category: null,
        companyCategory: '', // Type
        companyNature: '', // Nature
        registeredAddress: '', // Address1
        registeredAddressSub: '', // Address2
        deliveryType: '', // Del Term
        accountDay: '', // Payment Term
        contactCreateReqVO: [ // 联系人集合
          {
            name: '',
            position: '',
            email: '',
            divisionList: [],
            businessDictRelStr: ''
          }
        ]
      },
      supplierInfoRule: {
        name: [{ required: true, message: this.$t('supplier.pleaseEnterFullSupplierName'), trigger: 'blur' }],
        nameShort: [{ required: true, message: this.$t('auth.pleaseEnterTheSuppliersShortName'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (this.$route.params.id !== '0') {
        getSupplierBaseInfo(this.$route.params.id).then(res => {

          res.data.contactCreateReqVO?.map(item => {
            item.divisionList = item.businessDictRelStr ? item.businessDictRelStr.split(',') : []
          })

          if (res.data.contactCreateReqVO?.length === 0) {
            this.supplierInfo.contactCreateReqVO.push({ name: '', phone: '', email: '', divisionList: [], businessDictRelStr: '' })
          }
          this.supplierInfo = res.data
        })
      }
    },
    // 新增供应商数据保存
    doSaveSimpleBaseInfo() {
      this.$refs['baseInfo'].validate(valid => {
        if (valid) {
         /*  // 主要联系人的校验
          const invalidContactEmail = []
          for (const k in this.supplierInfo.contactCreateReqVO) {
            this.$refs[`contactItem${k}`].validate((valid, object) => {
              if (!valid) {
                invalidContactEmail.push(`contactItem${k}`)
              }
            })
          }
          if (invalidContactEmail.length) {
            this.$refs[invalidContactEmail].$el.scrollIntoView({
              block: 'center',
              behavior: 'smooth'
            })
            this.$message.error(this.$t('supplier.pleaseCompleteTheInformation'))
            return false
          }
          const invalidContactDivision = []
          for (const k in this.supplierInfo.contactCreateReqVO) {
            this.$refs[`contactItemDivision${k}`].validate((valid, object) => {
              if (!valid) {
                invalidContactDivision.push(`contactItemDivision${k}`)
              }
            })
          }
          if (invalidContactDivision.length) {
            this.$refs[invalidContactDivision].$el.scrollIntoView({
              block: 'center',
              behavior: 'smooth'
            })
            this.$message.error(this.$t('supplier.pleaseCompleteTheInformation'))
            return false
          }
          if (!this.supplierInfo.contactCreateReqVO.every(item => item.name && item.divisionList.length > 0)) {
            this.$refs.mainContact.$el.scrollIntoView({
              block: 'center',
              behavior: 'smooth'
            })
            this.$message.error(this.$t('supplier.mainContactNameAndContactDivisionAreRequired'))
            return false
          }
          if (!this.supplierInfo.contactCreateReqVO.some(item => item.divisionList.includes('the_main_contact'))) {
            this.$refs.mainContact.$el.scrollIntoView({
              block: 'center',
              behavior: 'smooth'
            })
            this.$message.error(this.$t('supplier.thereMustBeAtLeastOnePrimaryContactTheDivisionOfLaborIsThePrimaryContact'))
            return false
          } */

          this.supplierInfo.contactCreateReqVO.map(item => {
            item.businessDictRelStr = item.divisionList.join(',')
          })
          // 保存简版供应商
          saveSimpleBaseInfo(this.supplierInfo).then(res => {
            this.$modal.msgSuccess(this.$t('common.savedSuccessfully'))

            // 新增对象后刷新页面路由，供应商名称作为tag名称
            // 编辑对象不需要刷新页面路由
            if (this.$route.params.id === '0') {
              this.$tab.closeOpenPage({ path: `/supplier/simple/baseinfo/simpleinfo/${res.data}?supplierName=${this.supplierInfo.name}` })
            }
          })
        }
      })
    },
    // 取消按钮
    doCancel() {
      this.$tab.closeOpenPage('/supplier/simple/baseinfo/simpleBaseinfo')
    }
  }
}
</script>

<style lang="scss" scoped>
.baseInfo {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;

    .el-form-item__content {
      width: calc(100% - 175px);
    }
  }
}

.associatedCompany {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}
.el-cascader{
  width:100%;
}
.shadow {
  ::v-deep .el-form-item {
    width: 50%;
    margin-right: 0;
    padding-right: 10px;
  }
}

.supplierStaff {
  ::v-deep .el-form-item {
    width: 100%;
    margin-right: 0;
    padding-right: 10px;
  }
}

::v-deep .el-input__inner {
  color: #606266 !important;
  //background: #ffffff!important;
}

.verticalMiddle {
  display: flex;
  align-items: center
}

::v-deep .descriptionLabel {
  width: 145px;
  font-weight: bold;
  display: inline-block;
  text-align: right;
}

.upload p {
  line-height: 12px;
}

.required:before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

.supplier {
  .tableTitle {
    padding-left: 70px;
    font-size: 15px;
    font-weight: bold;
    margin: 20px 0;
  }

  .tablePadding {
    padding: 0 20px
  }

  .shadowPadding {
    padding: 5px 0;
    margin: 15px
  }

  .supplier-progress {
    height: 3px;
    width: 50%;
    background-color: #323232;
  }

  margin: 0 auto;
  width: 1000px;
  background: #c1c1c1;
  padding: 20px;

  .active {
    background-color: #4996b8;
    color: #fff
  }

  &-header {
    font-size: 16px;
    width: 100%;
    padding-bottom: 25px;
    margin-bottom: 20px;

    &-right {
      text-align: right;
      font-size: 12px;
      color: #173b75;
      margin-right: 20px
    }
  ;

    &-btn {
      cursor: pointer;
      padding: 10px 15px;
      display: inline-block;
      background-color: #cecece;
      color: #fff
    }
  ;
  }

  .smallInput {
    width: 188px;
  }

  .smallSelect {
    width: 228px;
  }

  .bigBaseInput {
    width: 533px
  }

  .bigInput {
    width: 591px
  }

  .colCenter {
    text-align: center;
  }

  .form-title {
    border-left: 6px solid #376092;
    margin: 10px 0;
    padding: 10px 30px;
    font-size: 16px;
    font-weight: bold;
    background-color: #f1f1f1;
  }

  // 上传输入框的文件列表样式调整#文字加粗
  ::v-deep .el-upload-list__item-name {
    font-weight: 600;
  }

  .centerRow {
    ::v-deep .el-form-item__content {
      width: calc(100% - 139px);
      text-align: center;
    }
  }

  .ellipsis-line {
    width: 248px;
    overflow: hidden;
    text-overflow: ellipsis; //文本溢出显示省略号
    white-space: nowrap; //文本不会换行
  }

  .shadow {
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }

  .rawMaterialFlex {
    flex: 0 1 15%;
    text-align: center;
  }

  .customItem {
    flex: 0 1 25%;
    text-align: center;
  }

  .form-main {
    .product {
      display: flex;
      min-height: 150px;

      .product-left {
        flex: 0 1 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 5px 10px;

        .product-title {
          flex: none;
          font-weight: bold;
          text-align: center;
          word-break: break-all;
          font-size: 20px;
          margin: 10px 0;
        }

        .product-detail {
          word-break: break-all;
          flex: auto;
          text-align: center;
        }
      }

      .product-right {
        justify-content: center;
        align-items: center;
        flex: 0 1 50%;
        border-left: 1px #dcdcdc solid;
        display: flex;
        flex-wrap: wrap;
      }
    }

  }

  .fixedBottom {
    position: fixed;
    width: calc(100% - 231px);
    bottom: 20px;
    display: flex;
    justify-content: center;
    margin-top: 40px;
    right: 30px;
  }
}
</style>
