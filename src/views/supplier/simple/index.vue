<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.search"
        :placeholder="$t('Please enter supplier Name')"
        clearable
        style="flex: 0 1 35%"
        @keyup.enter.native="handleQuery"
      />
      <el-button v-hasPermi="['supplier:simple:query']" plain type="primary" @click="handleQuery">
        {{ $t('common.search') }}
      </el-button>
      <el-button
        v-hasPermi="['supplier:simple:query']"
        style="margin-left: 0"
        size="mini"
        @click="resetQuery"
      >
        {{ $t('common.reset') }}
      </el-button>
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button v-hasPermi="['supplier:simple:query']" type="text">
          {{ $t('common.advancedSearch') }}
        </el-button>
        <i
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>
    </div>

    <!--    高级搜索的form-->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="130px" size="small">
      <el-form-item :label="$t('Category')" class="searchItem" prop="categoryIds">
        <cascading-category
          class="searchValue"
          :original-value.sync="queryParams.categoryIds"
        />
      </el-form-item>
      <el-form-item :label="$t('Status')" class="searchItem" prop="levelStatus">
        <el-select
          v-model="queryParams.levelStatus"
          class="searchValue"
          clearable
          filterable
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_LEVEL_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('Del Term')" prop="deliveryType">
        <el-select v-model="queryParams.deliveryType" class="searchValue" clearable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_DELIVERY_CONDITION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('Payment Term')" prop="accountDay">
        <el-select v-model="queryParams.accountDay" class="searchValue" clearable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_PAYMENT_DAYS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div style="padding-top: 20px">
      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8">

        <el-col :span="1.5" />
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['supplier:simple:save']"
            :loading="exportLoading"
            icon="el-icon-plus"
            size="mini"
            type="primary"
            @click="$router.push(`/supplier/simple/baseinfo/simpleinfo/0`)"
          >{{ $t('supplier.addNewSupplier') }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            :loading="exportLoading"
            icon="el-icon-upload2"
            size="mini"
            type="primary"
            v-hasPermi="['supplier:simple:save']"
            plain
            @click="handleImport"
          >{{ $t('common.batchCreation') }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            :loading="exportLoading"
            :title="$t('supplier.clickToExportAllTheDataDisplayedInTheList')"
            icon="el-icon-download"
            size="mini"
            type="primary"
            v-hasPermi="['supplier:simple:query']"
            @click="handleExport"
          >{{ $t('common.batchExport') }}
          </el-button>
        </el-col>
        <el-tooltip :content="$t('supplier.clickToExportAllTheDataDisplayedInTheList')" placement="top-start" />
        <right-toolbar
          :custom-columns.sync="girdOption.columns"
          :list-id="girdOption.id"
          :show-search.sync="showSearch"
          :only-custom="false"
          @queryTable="getList"
        />
      </el-row>
    </div>

    <!-- 列表 -->
    <vxe-grid
      ref="resourceTable"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
    >
      <template #levelStatus="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_LEVEL_STATUS" :value="row.levelStatus" />
      </template>
      <template #companyCategory="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_TYPE" :value="row.companyCategory" />
      </template>
      <template #companyNature="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_COMPANY_NATURE" :value="row.companyNature" />
      </template>
      <template #deliveryType="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_DELIVERY_CONDITION" :value="row.deliveryType" />
      </template>
      <template #accountDay="{row}">
        <dict-tag :type="DICT_TYPE.SUPPLIER_PAYMENT_DAYS" :value="row.accountDay" />
      </template>
      <template #category="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.category" />
      </template>
      <template #name="{row}">
        <copyButton type="text" @click="$router.push(`/supplier/simple/baseinfo/simpleinfo/${row.id}?supplierName=${row.name}`)">{{ row.name }}</copyButton>
      </template>
    </vxe-grid>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 供应商导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url"
          :auto-upload="false"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="doDownloadTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" :loading="upload.isUploading" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { getBaseHeader } from '@/utils/request'
import { exportSupplierBaseInfoExcel, getImportTemplate, getSupplierBaseInfoPage } from '@/api/supplier/simplesupplier'

export default {
  name: 'Simplebaseinfo',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 供应商列表
      list: [],
      // 弹出层标题
      title: '',
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        // 顶部模糊搜索条件
        search: '',
        categoryIds: [],
        levelStatus: '',
        deliveryType: '',
        accountDay: ''
      },
      // 供应商导入参数
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/supplier/base-info/import'
      },
      // 表单参数
      form: {},
      girdOption: {
        id: 'simpleBaseInfo',
        align: 'left',
        border: true,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isCurrent: true,
          isHover: true
        },
        keepSource: false,
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          { field: 'code', title: this.$t('Vendor ID'), visible: true, width: 170 },
          {
            field: 'involveTime',
            title: this.$t('Register Date'),
            visible: true, width: 170,
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : ''
          },
          { field: 'nameShort', title: this.$t('Supplier ShortName'), visible: true, width: 170 },
          { field: 'name', slots: { default: 'name' }, fixed: 'left', title: this.$t('Supplier Chinese Name'), visible: true, width: 170 },
          { field: 'nameEn', fixed: 'left', title: this.$t('Supplier English Name'), visible: true, width: 170 },
          { field: 'levelStatus', slots: { default: 'levelStatus' }, title: this.$t('Status'), visible: true, width: 170 },
          { field: 'category', slots: { default: 'category' }, title: this.$t('Category'), visible: true, width: 100 },
          {
            field: 'companyCategory',
            title: this.$t('Type'),
            slots: { default: 'companyCategory' },
            visible: true, width: 170
          },
          {
            field: 'companyNature',
            title: this.$t('Nature'),
            slots: { default: 'companyNature' },
            visible: true, width: 170
          },
          { field: 'registeredAddress', title: this.$t('Address1'), visible: true, width: 170 },
          { field: 'registeredAddressSub', title: this.$t('Address2'), visible: true, width: 170 },
          { field: 'contactName', title: this.$t('Contact'), visible: true, width: 170 },
          { field: 'contactPhone', title: this.$t('Tel.'), visible: true, width: 170 },
          { field: 'contactEmail', title: this.$t('Email'), visible: true, width: 170 },
          { field: 'deliveryType', slots: { default: 'deliveryType' }, title: this.$t('Del Term'), visible: true, width: 170 },
          { field: 'accountDay', slots: { default: 'accountDay' }, title: this.$t('Payment Term'), visible: true, width: 170 }
        ],
        sortConfig: {
          remote: true
        },
        slots: {
          // 自定义工具栏模板
          buttons: 'toolbar_buttons'
        }
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getSupplierBaseInfoPage({ ...this.queryParams }).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$modal.confirm(this.$t('supplier.areYouSureToExportTheSupplierDataItem')).then(() => {
        this.exportLoading = true
        return exportSupplierBaseInfoExcel(queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('supplier.supplierExportFileXls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createSuppliers) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createSuppliers.length
      }
      if (data.failureSuppliers) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureSuppliers).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    /** 批量创建 */
    handleImport() {
      this.upload.title = this.$t('common.batchCreation')
      this.upload.open = true
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        // 顶部模糊搜索条件
        search: '',
        categoryIds: [],
        levelStatus: '',
        deliveryType: '',
        accountDay: ''
      }
      this.resetForm('queryForm')
      this.handleQuery()
    },
    handleUpload() {
      this.uploadVisible = false
      this.showUpload = false
      this.$refs.upload.submit()
    },
    /** 下载模板操作 */
    doDownloadTemplate() {
      getImportTemplate().then(res => {
        this.$download.excel(res, this.$t('supplier.simplifiedSupplierImportTemplateXlsx'))
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.realTab {
  width: 220px;
  height: 90px;
  font-size: 16px;
  background: rgb(242, 244, 249);
  color: #000000;
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding: 21px;
}
.realNum{
  color: #327da1;
  font-size: 25px;
}
.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 130px);
  }
}

.searchValue {
  width: 80%;
}
.scale-leave-active {
  -webkit-animation: scale-down-tr 0.4s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  animation: scale-down-tr 0.4s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}

.scale-enter-active {
  -webkit-animation: scale-up-tr 0.4s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
  animation: scale-up-tr 0.4s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
}

@-webkit-keyframes scale-up-tr {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@keyframes scale-up-tr {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@-webkit-keyframes scale-down-tr {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@keyframes scale-down-tr {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

</style>
