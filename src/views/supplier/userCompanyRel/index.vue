<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('supplier.searchCriteria')" prop="supplier">
        <el-input
          v-model="queryParams.supplier"
          :placeholder="$t('supplier.pleaseEnterTheSupplierNameAbbreviationCode')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('order.company')" prop="companyId">
        <el-select
          v-model="queryParams.companyId"
          class="searchValue"
          clearable
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_COMPANY)"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('supplier.user')" prop="userIds">
        <el-select v-model="queryParams.userIds" class="searchValue" filterable multiple>
          <el-option v-for="item in userList" :key="item.id" :label="item.nickname" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>
    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['supplier:user-company-rel:save']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleConfig"
        >{{ handleConfigOperateName }}
        </el-button>
      </el-col>
      <!--        批量创建-->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['supplier:user-company-rel:import']"
          :loading="exportLoading"
          icon="el-icon-upload2"
          size="mini"
          type="primary"
          @click="handleImport"
        >{{ $t('supplier.batchImport') }}
        </el-button>
      </el-col>
      <!--        批量导出-->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['supplier:user-company-rel:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          type="primary"
          @click="handleExport"
        >{{ $t('common.batchExport') }}
        </el-button>
      </el-col>

      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table
      ref="multipleTable"
      v-loading="loading"
      :data="list"
      @selection-change="handleSelectionChange"
      @row-click="clickRow"
    >
      <el-table-column
        type="selection"
        width="30"
      />
      <el-table-column :label="$t('supplier.supplierName')" align="left" prop="supplierName" />
      <el-table-column :label="$t('supplier.shortNameOfSupplier')" align="left" prop="supplierShortName" />
      <el-table-column :label="$t('supplier.supplierCode')" align="left" prop="supplierCode" />
      <el-table-column :label="$t('order.company')" align="left" prop="companyId">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="scope.row.companyId" />
        </template>
      </el-table-column>
      <el-table-column v-if="type==='Sourcing'" :label="$t('common.sourcing')" align="left" prop="users" />
      <el-table-column v-if="type==='Buyer'" :label="$t('common.buyer')" align="left" prop="users" />
      <el-table-column v-if="type==='SQE'" align="center" :label="$t('SQE')" prop="users" width="180" />
      <el-table-column v-if="type==='Finance'" align="center" :label="$t('financial.finance')" prop="users" width="180" />

    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item :label="$t('supplier.user')" prop="userIds">
          <el-select v-model="form.userIds" class="searchValue" filterable multiple>
            <el-option v-for="item in userList" :key="item.id" :label="item.nickname" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url + '?type=' + upload.type"
          :auto-upload="false"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>

        <el-button
          v-hasPermi="['supplier:user-company-rel:export']"
          :loading="exportLoading"
          type="primary"
          plain
          @click="handleExport"
        >{{ $t('common.downloadTemplate') }}
        </el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { exportUserRelExcel, getUserRelPage, getUsers, saveUserRel } from '@/api/supplier/userCompanyRel'
import { getPurchaseOrgCache } from '@/api/system/purchaseOrg'
import { getUsersCache } from '@/api/system/user'
import { getBaseHeader } from '@/utils/request'

export default {
  name: 'Usercompanyrel',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应商与人员关系列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 当前维护的人员类型
      type: '',
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        supplier: null,
        companyId: null,
        userIds: [],
        type: ''
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 人员类型:Sourcing
        type: '',
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/supplier/user-company-rel/import'
      },
      // 表单参数
      form: {
        userIds: []
      },
      orgList: [],
      userList: [],
      // 选中数组值
      multipleSelection: [],
      // 配置操作名称（根据传入的type动态切换）
      handleConfigOperateName: ''
    }
  },
  async mounted() {
    await this.getOrgList()
    await this.getUserList()
    this.getList()
    this.type = this.$route.params.type
    if (this.type === 'SQE') {
      this.handleConfigOperateName = this.$t('scar.sqeConfiguration')
    } else if (this.type === 'Sourcing') {
      this.handleConfigOperateName = this.$t('supplier.procurementConfiguration')
    } else if (this.type === 'Buyer') {
      this.handleConfigOperateName = this.$t('supplier.buyerConfiguration')
    } else if (this.type === 'Finance') {
      this.handleConfigOperateName = this.$t('financial.financeConfiguration')
    }
  },
  methods: {
    /** 批量创建 */
    handleImport() {
      this.upload.title = this.$t('common.batchCreation')
      this.upload.open = true
      this.upload.type = this.type
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createUsers) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createUsers.length
      }
      if (data.failureUsers) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureUsers).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    clickRow(row) {
      this.$refs.multipleTable.toggleRowSelection(row)
    },
    /**
     * 选择框
     * @param val
     */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /**
     * 获取人员列表
     */
    getUserList() {
      getUsersCache({
        status: 0,
        isExternal: 1
      }).then(response => {
        this.userList = []
        this.userList.push(...response.data)
      })
    },
    /**
     * 获取采购组织列表
     */
    getOrgList() {
      getPurchaseOrgCache({
        status: 0
      }).then(response => {
        this.orgList = []
        this.orgList.push(...response.data)
      })
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      this.queryParams.type = this.$route.params.type
      // 处理查询参数
      const params = { ...this.queryParams }
      // 执行查询
      getUserRelPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        supplierId: undefined,
        companyId: undefined,
        userId: undefined,
        userIds: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleConfig() {
      if (this.multipleSelection == null || this.multipleSelection.length === 0) {
        this.$modal.msgError(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.reset()
      this.open = true
      this.title = this.$t('supplier.configureSupplierAndPersonnelRelationship')
      // 选择一条数据的时候，默认带出来选择的人员
      if (this.multipleSelection.length === 1) {
        // 获得拥有的人员集合
        getUsers({
          supplierId: this.multipleSelection[0].supplierId,
          type: this.$route.params.type,
          companyId: this.multipleSelection[0].companyId
        }).then(response => {
          // 设置选中
          this.form.userIds = response.data
        })
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        this.form.type = this.$route.params.type
        this.form.supplierIds = this.multipleSelection.map(i => {
          return {
            supplierId: i.supplierId,
            companyId: i.companyId
          }
        })
        // 修改的提交
        saveUserRel(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      this.queryParams.type = this.$route.params.type
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      // 执行导出
      this.exportLoading = true
      exportUserRelExcel(params).then(res => {
        this.$download.excel(res, this.$t('supplier.supplierPersonnelRelationshipxls'))
        this.exportLoading = false
      })
      // this.$modal.confirm(this.$t('supplier.areYouSureToExportAllSupplierUserRel')).then(() => {
      //   this.exportLoading = true
      //   return exportUserRelExcel(params)
      // }).then(response => {
      //   this.$download.excel(response, this.$t('supplier.supplierPersonnelRelationshipxls'))
      //   this.exportLoading = false
      // }).catch(() => {})
    }
  }
}
</script>
