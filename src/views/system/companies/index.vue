<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <div v-show="false">
      <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
        <el-form-item :label="$t('system.companyName')" prop="bankName">
          <el-input v-model="queryParams.companyName" :placeholder="$t('system.companyNameEnter')" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button
            v-has-permi="['system:companies:query']"
            type="primary"
            icon="el-icon-search"
            plain
            @click="handleQuery"
          >{{ $t('common.search') }}</el-button>
          <el-button
            v-has-permi="['system:companies:query']"
            icon="el-icon-refresh"
            @click="resetQuery"
          >{{ $t('common.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 列表 -->
    <el-table v-if="showTable" v-loading="loading" :data="list">
      <el-table-column :label="$t('system.companyName')" align="center" prop="companyName" />
      <el-table-column :label="$t('order.companyLogo')" align="center">
        <template slot-scope="scope">
          <el-avatar shape="square" :src="scope.row.logo" fit="contain" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('order.companySeal')" align="center" prop="sealPath">
        <template slot-scope="scope">
          <el-avatar shape="square" :src="scope.row.sealPath" fit="contain" />
        </template>
      </el-table-column>>
      <el-table-column :label="$t('order.bankOfDeposit')" align="center" prop="bankName" />
      <el-table-column :label="$t('supplier.bankAccount')" align="center" prop="bankAccount" />
      <el-table-column :label="$t('system.billingAddress')" align="center" prop="billingAddress" />
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:companies:update']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item :label="$t('order.companyLogo')" prop="logo">
          <el-upload
            class="avatar-uploader"
            :headers="headers"
            :action="logoUrl"
            :show-file-list="false"
            :on-success="handleLogoSuccess"
            :before-upload="beforeLogoUpload"
          >
            <img v-if="form.logo" :src="form.logo" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('order.companySeal')" prop="sealPath">
          <el-upload
            class="avatar-uploader"
            :headers="headers"
            :action="uploadFileUrl"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="form.sealPath" :src="form.sealPath" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('system.billingAddress')" prop="billingAddress">
          <el-input v-model="form.billingAddress" :placeholder="$t('system.pleaseEnterBillingAddress')" />
        </el-form-item>
        <el-form-item :label="$t('order.bankOfDeposit')" prop="bankName">
          <el-input v-model="form.bankName" :placeholder="$t('order.pleaseEnterTheDepositBank')" />
        </el-form-item>
        <el-form-item :label="$t('supplier.bankAccount')" prop="bankAccount">
          <el-input v-model="form.bankAccount" :placeholder="$t('order.pleaseEnterTheBankAccount')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="cancel"
        >{{ $t('common.cancel') }}</el-button>
        <el-button
          v-hasPermi="['system:companies:update']"
          type="primary"
          @click="submitForm"
        >{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAccessToken } from '@/utils/auth'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { createCompanies, getCompanies, getCompaniesPage, updateCompanies } from '@/api/system/companies'

export default {
  name: 'Stampconfig',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公司表信息列表
      list: [],
      showTable: true,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        companyName: null // 公司名称
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      headers: { Authorization: 'Bearer ' + getAccessToken() }, // 设置上传的请求头部
      uploadFileUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload', // 请求地址
      logoUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload' // 请求地址

    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      this.showTable = false
      // 处理查询参数
      const params = { ...this.queryParams }
      // 执行查询
      getCompaniesPage(params).then(response => {
        this.list = response.data.list
        // 从缓存里获取公司名称
        const companyCache = getDictDatas(DICT_TYPE.COMMON_COMPANY)
        for (let i = 0; i < this.list.length; i++) {
          this.list[i].companyName = companyCache.filter(item => item.id === this.list[i].deptId)[0]?.name
        }
        this.total = response.data.total
        this.showTable = true
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        companyName: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      if (row.id) {
        const id = row.id
        getCompanies(id).then(response => {
          this.form = response.data
          this.open = true
          this.title = this.$t('order.modifyCompanyTableInformation')
        })
      } else {
        this.open = true
        this.title = this.$t('order.modifyCompanyTableInformation')

        this.form.deptId = row.deptId
      }
    },
    /** 提交按钮 */
    submitForm() {
      console.log(this.form)
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateCompanies(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.getList()
            this.open = false
          })
          return
        }
        // 添加的提交
        createCompanies(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    handleAvatarSuccess(res, file) {
      this.form.sealPath = res.data.url
      this.$forceUpdate()
    },
    beforeAvatarUpload(file) {
      if (!['jpg', 'png'].includes(file.name.slice(file.name.lastIndexOf('.') + 1))) {
        this.$message.error(this.$t('supplier.unsupportedFileFormat'))
        return false
      }
    },
    handleLogoSuccess(res, file) {
      this.form.logo = res.data.url
      this.$forceUpdate()
    },
    beforeLogoUpload(file) {
      if (!['jpg', 'png'].includes(file.name.slice(file.name.lastIndexOf('.') + 1))) {
        this.$message.error(this.$t('supplier.unsupportedFileFormat'))
        return false
      }
    }
  }
}
</script>
<style>
.avatar{
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
</style>
