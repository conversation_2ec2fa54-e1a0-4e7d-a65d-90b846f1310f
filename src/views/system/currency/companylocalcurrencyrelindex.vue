<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('order.company')" prop="companyId">
        <el-select v-model="queryParams.companyId" :placeholder="$t('请选择')" clearable @keyup.enter.native="handleQuery">
          <el-option
            v-for="dict in companyDatas"
            :key="parseInt(dict.id)"
            :label="dict.name"
            :value="parseInt(dict.id)"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.companysLocalCurrency')" prop="localCurrency">
        <el-select
          v-model="queryParams.localCurrency"
          :placeholder="$t('common.pleaseSelect')"
          clearable
          @keyup.enter.native="handleQuery"
        >
          <el-option
            v-for="dict in currencyDatas"
            :key="parseInt(dict.id)"
            :label="dict.name"
            :value="parseInt(dict.id)"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:company-local-currency-rel:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('order.company')" align="center" prop="companyId">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="scope.row.companyId" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.companysLocalCurrency')" align="center" prop="localCurrency">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="scope.row.localCurrency" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('supplier.year')" align="center" prop="year" />
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:company-local-currency-rel:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('order.company')" prop="companyId">
          <el-select v-model="form.companyId" :disabled="isUpdate" :placeholder="$t('common.pleaseSelect')" clearable>
            <el-option
              v-for="dict in companyDatas"
              :key="parseInt(dict.id)"
              :label="dict.name"
              :value="parseInt(dict.id)"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.companysLocalCurrency')" prop="localCurrency">
          <el-select v-model="form.localCurrency" :placeholder="$t('common.pleaseSelect')" clearable @keyup.enter.native="handleQuery">
            <el-option
              v-for="dict in currencyDatas"
              :key="parseInt(dict.id)"
              :label="dict.name"
              :value="parseInt(dict.id)"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('supplier.year')" prop="year">
          <el-input-number v-model="form.year" :disabled="isUpdate" :placeholder="$t('system.pleaseEnterTheYear')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createCompanyLocalCurrencyRel,
  getCompanyLocalCurrencyRel,
  getCompanyLocalCurrencyRelPage,
  updateCompanyLocalCurrencyRel
} from '@/api/system/companyLocalCurrencyRel'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'

export default {
  name: 'Companylocalcurrencyrel',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公司本币列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        companyId: null,
        localCurrency: null,
        year: null
      },
      // 表单参数
      form: {},
      // 新增还是变更
      isUpdate: false,
      // 表单校验
      rules: {
        companyId: [{ required: true, message: this.$t('system.companyCannotBeEmpty'), trigger: 'blur' }],
        localCurrency: [{ required: true, message: this.$t('system.theCompanysLocalCurrencyCannotBeEmpty'), trigger: 'blur' }],
        year: [{ required: true, message: this.$t('system.yearCannotBeEmpty'), trigger: 'blur' }]
      },
      companyDatas: getDictDatas(DICT_TYPE.COMMON_COMPANY),
      currencyDatas: getDictDatas(DICT_TYPE.COMMON_CURRENCY)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      // 执行查询
      getCompanyLocalCurrencyRelPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        companyId: undefined,
        localCurrency: undefined,
        year: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.isUpdate = false
      this.open = true
      this.title = this.$t('system.addCompanyLocalCurrency')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getCompanyLocalCurrencyRel(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyTheCompanysLocalCurrency')
        this.isUpdate = true
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateCompanyLocalCurrencyRel(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createCompanyLocalCurrencyRel(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    }
  }
}
</script>
