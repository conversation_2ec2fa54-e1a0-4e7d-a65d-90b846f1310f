<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.code')" prop="code">
        <el-input v-model="queryParams.code" :placeholder="$t('system.pleaseEnterTheCode')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.name')" prop="name">
        <el-input
          v-model="queryParams.name"
          :placeholder="$t('common.pleaseEnterAName')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('supplier.pleaseSelectStatus')" clearable size="small">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:custom-form:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('system.code')" align="center" prop="code" />
      <el-table-column :label="$t('common.name')" align="center" prop="name" />
      <el-table-column :label="$t('supplier.displayOrder')" align="center" prop="sort" />
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:custom-form:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:custom-form:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('system.code')" prop="code">
          <el-input v-model="form.code" :placeholder="$t('system.pleaseEnterTheCode')" />
        </el-form-item>
        <el-row>
          <el-col v-for="(item,i) in form.translations" :key="i" :span="12">
            <el-form-item
              :label="$t('system.tableName')"
              :prop="`translations[${i}].translation`"
              :rules="{required: true, message: $t('common.pleaseEnterAName'), trigger: 'blur'}"
            >
              <el-input v-model="item.translation">
                <svg-icon slot="suffix" :icon-class="item.locale" />
              </el-input>
            </el-form-item>
          </el-col>

        </el-row>
        <el-form-item :label="$t('supplier.displayOrder')" prop="sort">
          <el-input v-model="form.sort" :placeholder="$t('system.pleaseEnterTheDisplayOrder')" type="number" min="1" step="1" @onkeyup="value=value.replace(/\D|^0/g,'')" />
        </el-form-item>
        <el-form-item :label="$t('common.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="parseInt(dict.value)"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createCustomForm,
  deleteCustomForm,
  getCustomForm,
  getCustomFormPage,
  updateCustomForm
} from '@/api/system/customForm'
import { addTranslation } from '@/utils/i18n'

export default {
  name: 'Customform',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 自定义表单表,建议表单模板仅在同一个模块内使用列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        code: null,
        name: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        code: [{ required: true, message: this.$t('system.codeCannotBeEmpty'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('system.nameCannotBeEmpty'), trigger: 'blur' }],
        nameLocalId: [{ required: true, message: this.$t('system.multilingualIdCannotBeEmpty'), trigger: 'blur' }],
        sort: [{ required: true, message: this.$t('system.displayOrderCannotBeEmpty'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      // 执行查询
      getCustomFormPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        code: undefined,
        name: undefined,
        nameLocalId: undefined,
        sort: undefined,
        status: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      addTranslation(this.form)
      this.title = this.$t('system.newForm')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getCustomForm(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyForm')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.form.name = this.form.translations?.find(item => item.locale === 'zh').translation
        // 修改的提交
        if (this.form.id != null) {
          updateCustomForm(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createCustomForm(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      const code = row.code
      this.$modal.confirm('是否确认删除自定义表单表,建议表单模板仅在同一个模块内使用编号为' + code + '的数据项?').then(() => {
        return deleteCustomForm(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      })
    }
  }
}
</script>
