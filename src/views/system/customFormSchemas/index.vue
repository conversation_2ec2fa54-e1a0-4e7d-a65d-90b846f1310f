<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.form')" prop="formId">
        <el-select
          v-model="queryParams.formId"
          :placeholder="$t('system.pleaseSelectAForm')"
          clearable
          @keyup.enter.native="handleQuery"
        >
          <el-option
            v-for="item in formList?.filter(i=>i.status===0)"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>

      </el-form-item>
      <el-form-item :label="$t('system.title')" prop="sectionId">
        <el-select
          v-model="queryParams.sectionId"
          :placeholder="$t('system.pleaseSelectATitle')"
          clearable
          @keyup.enter.native="handleQuery"
        >
          <el-option
            v-for="item in sectionList?.filter(i=>i.status===0)"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>

      </el-form-item>

      <el-form-item :label="$t('system.code')" prop="code">
        <el-input v-model="queryParams.code" :placeholder="$t('system.pleaseEnterTheCode')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('supplier.pleaseSelectStatus')" clearable size="small">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:custom-form-schemas:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :formatter="formFormatter" :label="$t('system.form')" align="center" prop="formId" />
      <el-table-column :formatter="sectionFormatter" :label="$t('system.title')" align="center" prop="sectionId" />
      <el-table-column :label="$t('system.code')" align="center" prop="code" />
      <el-table-column :label="$t('system.columnName')" align="center" prop="name" />
      <el-table-column :label="$t('system.columnType')" align="center" prop="attrType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_CUSTOM_FORM_COLUMN_TYPE" :value="scope.row.attrType" />
        </template>
      </el-table-column>
      <el-table-column :formatter="dictTypeFormatter" :label="$t('system.associativeDictionary')" align="center" prop="dictType" />
      <el-table-column :label="$t('system.required')" align="center" prop="isMandatory">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_Y_N" :value="scope.row.isMandatory" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('supplier.displayOrder')" align="center" prop="sort" />
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:custom-form-schemas:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:custom-form-schemas:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('system.form')" prop="formId">
          <el-select
            v-model="form.formId"
            :placeholder="$t('system.pleaseSelectAForm')"
            clearable
            @change="getSelectSection"
            @keyup.enter.native="handleQuery"
          >
            <el-option
              v-for="item in formList?.filter(i=>i.status===0)"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.title')" prop="sectionId">
          <el-select
            v-model="form.sectionId"
            :placeholder="$t('system.pleaseSelectATitle')"
            clearable
            @keyup.enter.native="handleQuery"
          >
            <el-option
              v-for="item in selectSectionList?.filter(i=>i.status===0)"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>

        </el-form-item>
        <el-form-item :label="$t('system.code')" prop="code">
          <el-input v-model="form.code" :placeholder="$t('system.pleaseEnterTheCode')" />
        </el-form-item>
        <el-row>
          <el-col v-for="(item,i) in form.translations" :key="i" :span="12">
            <el-form-item
              :label="$t('system.columnName')"
              :prop="`translations[${i}].translation`"
              :rules="{required: true, message: $t('common.pleaseEnterAName'), trigger: 'blur'}"
            >
              <el-input v-model="item.translation">
                <svg-icon slot="suffix" :icon-class="item.locale" />
              </el-input>
            </el-form-item>
          </el-col>

        </el-row>

        <el-form-item
          :label="$t('system.columnType')"
          prop="attrType"
        >
          <el-select v-model="form.attrType" :placeholder="$t('system.pleaseSelectAColumnType')" clearable>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_CUSTOM_FORM_COLUMN_TYPE)"
              :key="dict.label"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('system.dataDictionary')"
          prop="dictType"
        >
          <el-select v-model="form.dictType" clearable>
            <el-option
              v-for="dict in dictType"
              :key="dict.nameLocalId"
              :label="dict.name"
              :value="dict.type"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.required')" prop="isMandatory">
          <el-radio-group v-model="form.isMandatory">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_Y_N)"
              :key="parseInt(dict.value)"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('common.sort')" prop="sort">
          <el-input v-model="form.sort" :placeholder="$t('system.pleaseEnterSorting')" type="number" min="1" step="1" @onkeyup="value=value.replace(/\D|^0/g,'')" />
        </el-form-item>
        <el-form-item :label="$t('common.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="parseInt(dict.value)"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>

        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createCustomFormSchemas,
  deleteCustomFormSchemas,
  getCustomFormSchemas,
  getCustomFormSchemasPage,
  updateCustomFormSchemas
} from '@/api/system/customFormSchemas'
import { getCustomFormList } from '@/api/system/customForm'
import { getCustomSectionList } from '@/api/system/customSection'
import { listAll } from '@/api/system/dict/type'
import { addTranslation } from '@/utils/i18n'

export default {
  name: 'Customformschemas',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 自定义表单列规则列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        formId: null,
        sectionId: null,
        code: null,
        name: null,
        nameLocalId: null,
        attrType: null,
        dictType: null,
        isMandatory: null,
        sort: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        formId: [{ required: true, message: this.$t('system.theFormCannotBeEmpty'), trigger: 'blur' }],

        dictType: { validator: this.dictTypePass, trigger: 'blur' },
        sectionId: [{ required: true, message: this.$t('system.theTitleCannotBeEmpty'), trigger: 'blur' }],
        code: [{ required: true, message: this.$t('system.codeCannotBeEmpty'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('system.columnNameCannotBeEmpty'), trigger: 'blur' }],
        attrType: [{ required: true, message: this.$t('system.columnTypeCannotBeEmpty'), trigger: 'change' }],
        isMandatory: [{ required: true, message: this.$t('system.requiredOrNotCannotBeBlank'), trigger: 'blur' }],
        sort: [{ required: true, message: this.$t('system.sortCannotBeEmpty'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }]
      },
      selectSectionList: [],
      sectionList: [],
      formList: [],
      dictType: []
    }
  },
  created() {
    this.getDictType()
    this.getForm()
    this.getSection()
    this.getList()
  },
  methods: {
    getDictType() {
      listAll().then(res => {
        this.dictType = res.data
      })
    },
    /** 获取form */
    getForm() {
      getCustomFormList().then(response => {
        this.formList = response.data
      })
    },
    /** 获取section */
    getSection() {
      getCustomSectionList().then(response => {
        this.sectionList = response.data
        this.selectSectionList = response.data
      })
    },
    getSelectSection(formId) {
      this.form.sectionId = ''
      getCustomSectionList({
        formId
      }).then(response => {
        this.selectSectionList = response.data
      })
    },

    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getCustomFormSchemasPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        formId: undefined,
        sectionId: undefined,
        code: undefined,
        name: undefined,
        nameLocalId: undefined,
        attrType: undefined,
        dictType: undefined,
        isMandatory: undefined,
        sort: undefined,
        status: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      addTranslation(this.form)
      this.open = true
      this.title = this.$t('system.addACustomFormColumnRule')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getCustomFormSchemas(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyCustomFormColumnRules')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.form.name = this.form.translations?.find(item => item.locale === 'zh').translation
        if (!['checkbox', 'radio'].includes(this.form.attrType)) {
          this.form.dictType = null
        }
        // 修改的提交
        if (this.form.id != null) {
          updateCustomFormSchemas(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createCustomFormSchemas(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      const code = row.code
      this.$modal.confirm('是否确认删除自定义表单列规则编号为' + code + '的数据项?').then(() => {
        return deleteCustomFormSchemas(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    formFormatter(row, column, cellValue) {
      return this.formList.find(item => item.id === cellValue)?.name
    },
    sectionFormatter(row, column, cellValue) {
      return this.sectionList.find(item => item.id === cellValue)?.name
    },
    dictTypeFormatter(row, column, cellValue) {
      return this.dictType.find(item => item.type === cellValue)?.name
    },
    dictTypePass(rule, value, callback) {
      if (['checkbox', 'radio'].includes(this.form.attrType) && !value) {
        callback(new Error(this.$t('system.pleaseSelectTheAssociatedDataDictionary')))
      } else {
        callback()
      }
    }
  }
}
</script>
