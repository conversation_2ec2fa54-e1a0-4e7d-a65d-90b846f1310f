<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" size="small">
      <el-form-item :label="$t('system.departmentName')" prop="name">
        <el-input
          v-model="queryParams.name"
          :placeholder="$t('system.pleaseEnterTheDepartmentName')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.departmentCode')" prop="code">
        <el-input
          v-model="queryParams.code"
          :placeholder="$t('system.pleaseEnterTheDepartmentCode')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('system.menuStatus')" clearable>
          <el-option
            v-for="dict in statusDictDatas"
            :key="parseInt(dict.value)"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dept:create']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >{{ $t('common.add') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="el-icon-sort" plain size="mini" type="info" @click="toggleExpandAll">{{ $t('common.expandCollapse') }}</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="deptList"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      row-key="id"
    >
      <el-table-column :label="$t('system.departmentName')" prop="name" width="260" />
      <el-table-column :label="$t('system.departmentCode')" prop="code" width="120" />
      <el-table-column :formatter="userNicknameFormat" :label="$t('system.personInCharge')" prop="leader" width="120" />
      <el-table-column :label="$t('common.sort')" prop="sort" width="200" />
      <el-table-column :label="$t('system.departmentType')" prop="type" width="100">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_DEPT_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.status')" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.createTime')"
        align="center"
        prop="createTime"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:dept:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:dept:create']"
            icon="el-icon-plus"
            size="mini"
            type="text"
            @click="handleAdd(scope.row)"
          >{{ $t('common.add') }}
          </el-button>
          <el-button
            v-if="scope.row.parentId !== 0"
            v-hasPermi="['system:dept:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改部门对话框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('system.superiorDepartment')" :rules="getParentIdRule()" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :normalizer="normalizer"
                :options="deptOptions"
                :placeholder="$t('system.selectSuperiorDepartment')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.departmentName')" prop="name">
              <el-input v-model="form.name" :placeholder="$t('system.pleaseEnterTheDepartmentName')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.departmentCode')" prop="code">
              <el-input v-model="form.code" :placeholder="$t('system.pleaseEnterTheDepartmentCode')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.departmentType')" prop="type">
              <el-select
                v-model="form.type"
                :placeholder="$t('system.pleaseSelectADepartmentType')"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in deptTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.personInCharge')" prop="leaderUserId">
              <el-select
                v-model="form.leaderUserId"
                :placeholder="$t('system.pleaseEnterTheResponsiblePerson')"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in users"
                  :key="parseInt(item.id)"
                  :label="item.nickname"
                  :value="parseInt(item.id)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('supplier.contactNumber')" prop="phone">
              <el-input
                v-model="form.phone"
                :placeholder="$t('system.pleaseEnterTheContactNumber')"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('supplier.mailbox')" prop="email">
              <el-input
                v-model="form.email"
                :placeholder="$t('supplier.pleaseEnterEmail')"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.departmentStatus')" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="parseInt(dict.value)">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.showSort')" prop="sort">
              <el-input-number v-model="form.sort" :min="0" controls-position="right" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addDept, delDept, getDept, listDept, updateDept } from '@/api/system/dept'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

import { CommonStatusEnum } from '@/utils/constants'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { listSimpleUsers } from '@/api/system/user'

export default {
  name: 'Dept',
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表格树数据
      deptList: [],
      // 部门树选项
      deptOptions: [],
      // 用户下拉列表
      users: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 是否展开
      expand: false,
      // 状态数据字典
      statusOptions: [],
      // 查询参数
      queryParams: {
        name: undefined,
        code: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: this.$t('system.departmentNameCannotBeEmpty'), trigger: 'blur' }
        ],
        code: [
          { required: true, message: this.$t('system.departmentCodeCannotBeEmpty'), trigger: 'blur' }
        ],
        sort: [
          { required: true, message: this.$t('system.displaySortingCannotBeEmpty'), trigger: 'blur' }
        ],
        type: [
          { required: true, message: this.$t('system.departmentTypeCannotBeEmpty'), trigger: 'blur' }
        ],
        email: [
          {
            type: 'email',
            message: this.$t('supplier.pleaseEnterTheCorrectEmailAddress'),
            trigger: ['blur', 'change']
          }
        ],
        phone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: this.$t('system.pleaseEnterTheCorrectMobilePhoneNumber'),
            trigger: 'blur'
          }
        ],
        status: [
          { required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }
        ]
      },
      parentRules: { required: true, message: this.$t('system.superiorDepartmentCannotBeBlank'), trigger: 'blur' },
      // 枚举
      CommonStatusEnum: CommonStatusEnum,
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS),
      deptTypes: getDictDatas(DICT_TYPE.SYSTEM_DEPT_TYPE)
    }
  },
  created() {
    this.getList()
    // 获得用户列表
    listSimpleUsers().then(response => {
      this.users = response.data
    })
  },
  methods: {
    /**
     * 获取父节点的验证规则
     */
    getParentIdRule() {
      if (this.form.isParent === true) {
        return null
      } else {
        return this.parentRules
      }
    },
    /** 查询部门列表 */
    getList() {
      this.loading = true
      listDept(this.queryParams).then(response => {
        this.deptList = this.handleTree(response.data, 'id')
        this.loading = false
      })
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    // 用户昵称展示
    userNicknameFormat(row, column) {
      if (!row.leaderUserId) {
        return this.$t('system.notSet')
      }
      for (const user of this.users) {
        if (row.leaderUserId === user.id) {
          return user.nickname
        }
      }
      return '未知【' + row.leaderUserId + '】'
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        parentId: undefined,
        name: undefined,
        sort: undefined,
        leaderUserId: undefined,
        phone: undefined,
        email: undefined,
        status: CommonStatusEnum.ENABLE
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      if (row !== undefined) {
        this.form.parentId = row.id
      }
      this.open = true
      this.title = this.$t('system.addDepartment')
      listDept().then(response => {
        this.deptOptions = []
        const dept = { id: 0, name: this.$t('system.toplevel'), children: [] }
        dept.children = this.handleTree(response.data, 'id')
        this.deptOptions.push(dept)
      })
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false
      this.isExpandAll = !this.isExpandAll
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      getDept(row.id).then(response => {
        this.form = response.data
        this.form.isParent = false
        if (this.form.parentId === 0) { // 无父部门时，标记为 undefined，避免展示为 Unknown
          this.form.parentId = undefined
          // 顶层节点禁止修改父节点、名称和编码
          this.form.isParent = true
        }
        this.open = true
        this.title = this.$t('system.editDepartment')
      })
      listDept(row.id).then(response => {
        this.deptOptions = []
        const dept = { id: 0, name: this.$t('system.toplevel'), children: [] }
        dept.children = this.handleTree(response.data, 'id')
        this.deptOptions.push(dept)
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateDept(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
              this.open = false
              this.getList()
            })
          } else {
            addDept(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('common.addSuccess'))
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm(this.$t('system.areYouSureToDeleteTheNameAs') + row.name + '"的数据项?').then(function() {
        return delDept(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    }
  }
}
</script>
