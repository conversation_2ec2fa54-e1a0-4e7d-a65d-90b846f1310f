<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('order.dictionaryName')" prop="dictType">
        <el-select v-model="queryParams.dictType">
          <el-option v-for="item in typeOptions" :key="item.id" :label="item.name" :value="item.type" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('supplier.dictionaryLabel')" prop="label">
        <el-input
          v-model="queryParams.label"
          :placeholder="$t('system.pleaseEnterTheDictionaryLabel')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('system.dataStatus')" clearable>
          <el-option v-for="dict in statusDictDatas" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          type="warning"
          @click="handleExport"
        >{{ $t('common.export') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column :label="$t('system.dictionaryCoding')" align="center" prop="id" />
      <el-table-column :label="$t('supplier.dictionaryLabel')" align="center" prop="label" />
      <el-table-column :label="$t('supplier.dictionaryKeyValue')" align="center" prop="value" />
      <el-table-column :label="$t('system.dictionaryId')" prop="type" width="80">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_ROLE_TYPE" :value="scope.row.customMark" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.dictionarySort')" align="center" prop="sort" />
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.extend_value')" align="center" prop="extendedValue">
        <template slot-scope="scope">
          <dict-tag v-if="[DICT_TYPE.SUPPLIER_CHANGE_TYPE].includes(scope.row.dictType)" :type="DICT_TYPE.SYSTEM_ROLE_IS_EXTERNAL" :value="scope.row.extendedValue" />
          <span v-else>{{ scope.row.extendedValue }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.colorType')" align="center" prop="colorType" />
      <!--      <el-table-column align="center" label="CSS Class" prop="cssClass" />-->
      <el-table-column :label="$t('common.remarks')" :show-overflow-tooltip="true" align="center" prop="remark" />
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:dict:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:dict:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item :label="$t('system.dictionaryType')">
          <el-input v-model="form.dictType" :disabled="true" />
        </el-form-item>
        <el-row v-for="(item,i) in form.translations">
          <el-form-item
            :label="$t('system.dataLabel')"
            :prop="`translations[${i}].translation`"
            :rules="{required: true, message: $t('system.pleaseEnterTheDataLabel'), trigger: 'blur'}"
          >
            <el-input v-model="item.translation">
              <svg-icon slot="suffix" :icon-class="item.locale" />
            </el-input>
          </el-form-item>
        </el-row>
        <el-form-item :label="$t('system.dataKeyValue')" prop="value">
          <el-input
            v-model="form.value"
            :disabled="form.customMark === 1"
            :placeholder="$t('system.pleaseEnterTheDataKeyValue')"
          />
        </el-form-item>
        <el-form-item
          v-if="[DICT_TYPE.SUPPLIER_RATE,DICT_TYPE.SL_SUPPLIER_LEVEL,DICT_TYPE.SUPPLIER_TERMS_OF_PAYMENT].includes(form.dictType)"
          :label="extendedValueTitle"
          prop="extendedValue"
        >
          <el-input
            v-model="form.extendedValue"
            :placeholder="$t('system.pleaseEnterTheDataKeyValue')"
          />
        </el-form-item>
        <el-form-item :label="$t('system.showSort')" prop="sort">
          <el-input-number v-model="form.sort" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item :label="$t('common.status')" prop="status">
          <el-radio-group v-model="form.status" :disabled="form.customMark===1">
            <el-radio v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="parseInt(dict.value)">
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('system.colorType')" prop="colorType">
          <el-select v-model="form.colorType">
            <el-option
              v-for="item in colorTypeOptions"
              :key="item.value"
              :label="item.label + '(' + item.value + ')'"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="CSS Class" prop="cssClass">
          <el-input
            v-model="form.cssClass"
            :placeholder="$t('system.pleaseEnterCssClass')"
          />
        </el-form-item>
        <el-form-item :label="$t('common.remarks')" prop="remark">
          <el-input
            v-model="form.remark"
            :placeholder="$t('rfq.pleaseEnterTheContent')"
            type="textarea"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addData, delData, exportData, getData, listData, updateData } from '@/api/system/dict/data'
import { getType, listAllSimple } from '@/api/system/dict/type'

import { CommonStatusEnum } from '@/utils/constants'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { addTranslation } from '@/utils/i18n'

export default {
  name: 'Data',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 字典表格数据
      dataList: [],
      // 默认字典类型
      defaultDictType: '',
      // 弹出层标题
      title: '',
      // 扩展字段描述
      extendedValueTitle: '',
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 类型数据字典
      typeOptions: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        dictName: undefined,
        dictType: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        label: [
          { required: true, message: this.$t('system.dataLabelCannotBeEmpty'), trigger: 'blur' }
        ],
        value: [
          { required: true, message: this.$t('system.dataKeyValueCannotBeEmpty'), trigger: 'blur' }
        ],
        sort: [
          { required: true, message: this.$t('system.dataOrderCannotBeEmpty'), trigger: 'blur' }
        ]
      },
      // 数据标签回显样式
      colorTypeOptions: [{
        value: 'default',
        label: this.$t('system.default')
      }, {
        value: 'primary',
        label: this.$t('system.main')
      }, {
        value: 'success',
        label: this.$t('system.success')
      }, {
        value: 'info',
        label: this.$t('system.information')
      }, {
        value: 'warning',
        label: this.$t('system.warning')
      }, {
        value: 'danger',
        label: this.$t('system.danger')
      }
      ],
      // 枚举
      CommonStatusEnum: CommonStatusEnum,
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS)
    }
  },
  created() {
    const dictId = this.$route.params && this.$route.params.dictId
    this.getType(dictId)
    this.getTypeList()
  },
  methods: {
    /** 查询字典类型详细 */
    getType(dictId) {
      getType(dictId).then(response => {
        this.queryParams.dictType = response.data.type
        this.defaultDictType = response.data.type
        this.getList()
      })
    },
    /** 查询字典类型列表 */
    getTypeList() {
      listAllSimple({ typeLevel: 'system' }).then(response => {
        this.typeOptions = response.data
      })
    },
    /** 查询字典数据列表 */
    getList() {
      this.loading = true
      listData(this.queryParams).then(response => {
        this.dataList = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        label: undefined,
        value: undefined,
        sort: 0,
        status: CommonStatusEnum.ENABLE,
        colorType: 'default',
        cssClass: undefined,
        remark: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.dictType = this.defaultDictType
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      // 新增时初始化中文翻译框
      addTranslation(this.form)
      this.open = true
      this.title = this.$t('system.addDictionaryData')
      this.extendedValueTitle = this.$t('system.extendedValue')
      this.form.dictType = this.queryParams.dictType
      if (DICT_TYPE.SL_SUPPLIER_LEVEL === this.form.dictType) {
        this.extendedValueTitle = this.$t('system.levelLevelhigherAndHigher')
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getData(id).then(response => {
        this.form = response.data
        if (this.form.translations?.length == 0) {
          // 新增时初始化中文翻译框
          addTranslation(this.form)
        }
        this.open = true
        this.title = this.$t('system.modifyDictionaryData')
        this.extendedValueTitle = this.$t('system.extendedValue')
        console.log(DICT_TYPE.SL_SUPPLIER_LEVEL)
        if (DICT_TYPE.SL_SUPPLIER_LEVEL === this.form.dictType) {
          this.extendedValueTitle = this.$t('system.levelLevelhigherAndHigher')
        }
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.label = this.form.translations?.find(item => item.locale === 'zh').translation
          if (this.form.id !== undefined) {
            updateData(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
              this.open = false
              this.getList()
            })
          } else {
            addData(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('common.addSuccess'))
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id
      this.$modal.confirm('是否确认删除字典编码为"' + ids + '"的数据项?').then(function() {
        return delData(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$modal.confirm(this.$t('system.areYouSureToExportAllDataItems')).then(() => {
        this.exportLoading = true
        return exportData(queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('system.dictionaryDataxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>
