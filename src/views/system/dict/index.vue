<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item :label="$t('order.dictionaryName')" prop="name">
        <el-input v-model="queryParams.name" :placeholder="$t('system.pleaseEnterTheDictionaryName')" clearable style="width: 240px" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.dictionaryType')" prop="type">
        <el-input v-model="queryParams.type" :placeholder="$t('system.pleaseEnterTheDictionaryType')" clearable style="width: 240px" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('system.dictionaryStatus')" clearable style="width: 240px">
          <el-option v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="dict.label" :value="parseInt(dict.value)" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.createTime')">
        <el-date-picker
          v-model="dateRangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
        />
      </el-form-item>
      <el-form-item>
        <el-button plain type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:create']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >{{ $t('common.add') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:export']"
          type="warning"
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
        >{{ $t('common.export') }}</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="typeList">
      <el-table-column :label="$t('system.dictionaryNo')" align="center" prop="id" />
      <el-table-column :label="$t('order.dictionaryName')" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('system.dictionaryType')" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <router-link :to="'/dict/type/data/' + scope.row.id" class="link-type">
            <span>{{ scope.row.type }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.dictionaryId')" prop="type" width="80">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_ROLE_TYPE" :value="scope.row.customMark" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.remarks')" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:dict:update']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}</el-button>
          <el-button
            v-if="scope.row.customMark===2"
            v-hasPermi="['system:dict:updateMark']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="editMark(scope.row,1)"
          >{{ $t('system.builtin') }}</el-button>
          <el-button
            v-else
            v-hasPermi="['system:dict:updateMark']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="editMark(scope.row,2)"
          >{{ $t('system.custom') }}</el-button>
          <!--          <el-button-->
          <!--            v-hasPermi="['system:dict:delete']"-->
          <!--            size="mini"-->
          <!--            type="text"-->
          <!--            icon="el-icon-delete"-->
          <!--            @click="handleDelete(scope.row)"-->
          <!--          >{{ $t('common.del') }}</el-button>-->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row v-for="(item,i) in form.translations">
          <el-form-item
            :label="$t('order.dictionaryName')"
            :prop="`translations[${i}].translation`"
            :rules="{required: true, message: $t('system.pleaseEnterTheDictionaryName'), trigger: 'blur'}"
          >
            <el-input v-model="item.translation">
              <svg-icon slot="suffix" :icon-class="item.locale" />
            </el-input>
          </el-form-item>
        </el-row>
        <!--        <el-form-item label="字典名称" prop="name">-->
        <!--          <el-input v-model="form.name" placeholder="请输入字典名称" />-->
        <!--        </el-form-item>-->
        <el-form-item :label="$t('system.dictionaryType')" prop="type">
          <el-input v-model="form.type" :disabled="typeof form.id !== 'undefined'" :placeholder="$t('system.pleaseEnterTheDictionaryType')" />
        </el-form-item>
        <el-form-item :label="$t('common.status')" prop="status">
          <el-radio-group v-model="form.status" :disabled="form.customMark===1">
            <el-radio v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="parseInt(dict.value)">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('common.remarks')" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :placeholder="$t('rfq.pleaseEnterTheContent')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addType, delType, exportType, getType, listType, updateMark, updateType } from '@/api/system/dict/type'

import { CommonStatusEnum } from '@/utils/constants'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { addTranslation } from '@/utils/i18n'

export default {
  name: 'Dict',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 字典表格数据
      typeList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 日期范围
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: undefined,
        type: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: this.$t('system.dictionaryNameCannotBeEmpty'), trigger: 'blur' }
        ],
        type: [
          { required: true, message: this.$t('system.dictionaryTypeCannotBeEmpty'), trigger: 'blur' }
        ]
      },
      // 字典类型级别
      dictTypeLevel: 'system',
      // 枚举
      CommonStatusEnum: CommonStatusEnum,
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询字典类型列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 系统级别字典增加 字典类型级别参数
      params['typeLevel'] = this.dictTypeLevel
      // 执行查询
      listType(params).then(response => {
        this.typeList = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        type: undefined,
        status: CommonStatusEnum.ENABLE,
        remark: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      // 新增时初始化中文翻译框
      addTranslation(this.form)
      this.open = true
      this.title = this.$t('system.addDictionaryType')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getType(id).then(response => {
        this.form = response.data
        if (this.form.translations?.length === 0) {
          // 新增时初始化中文翻译框
          addTranslation(this.form)
        }
        this.open = true
        this.title = this.$t('system.modifyDictionaryType')
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.name = this.form.translations?.find(item => item.locale === 'zh').translation
          // 增加 字典类型级别参数
          this.form.typeLevel = this.dictTypeLevel
          if (this.form.id !== undefined) {
            if (this.form.status === 1) {
              this.$modal.confirm('状态选择关闭会导致该字典下的数据状态默认变更为关闭，是否确认？', '提示').then(() => {
                updateType(this.form).then(response => {
                  this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
                  this.open = false
                  this.getList()
                })
              }).catch(() => {})
            } else {
              updateType(this.form).then(response => {
                this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
                this.open = false
                this.getList()
              })
            }
          } else {
            addType(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('common.addSuccess'))
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm(this.$t('system.areYouSureToDeleteTheDictionaryWhoseNumberIs') + ids + '"的数据项?').then(function() {
        return delType(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行导出
      this.$modal.confirm(this.$t('system.areYouSureToExportAllDictionaryTypeDataItems')).then(() => {
        this.exportLoading = true
        return exportType(params)
      }).then(response => {
        this.$download.excel(response, this.$t('system.dictionaryTypexls'))
        this.exportLoading = false
      }).catch(() => {})
    },
    editMark(obj1, num1) {
      updateMark({
        ...obj1,
        customMark: num1
      }).then(res => {
        obj1.customMark = num1
        this.$modal.msgSuccess(this.$t('common.updateSuccessful'))
      })
    }
  }
}
</script>
