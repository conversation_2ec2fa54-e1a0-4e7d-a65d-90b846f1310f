<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="100px" size="small">
      <el-form-item :label="$t('system.errorCodeType')" prop="type">
        <el-select v-model="queryParams.type" :placeholder="$t('system.pleaseSelectTheErrorCodeType')" clearable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_ERROR_CODE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.appName')" prop="applicationName">
        <el-input
          v-model="queryParams.applicationName"
          :placeholder="$t('system.pleaseEnterTheApplicationName')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.errorCode')" prop="code">
        <el-input
          v-model="queryParams.code"
          :placeholder="$t('system.pleaseEnterTheErrorCode')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.errorCodePrompt')" prop="message">
        <el-input
          v-model="queryParams.message"
          :placeholder="$t('system.pleaseEnterTheErrorCodePrompt')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.createTime')">
        <el-date-picker
          v-model="dateRangeCreateTime"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:error-code:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:error-code:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >{{ $t('common.export') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('order.number')" align="center" prop="id" />
      <el-table-column :label="$t('system.type')" align="center" prop="type" width="80">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_ERROR_CODE_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.appName')" align="center" prop="applicationName" width="200" />
      <el-table-column :label="$t('system.errorCode')" align="center" prop="code" width="120" />
      <el-table-column :label="$t('system.errorCodePrompt')" align="center" prop="message" width="300" />
      <el-table-column :label="$t('common.remarks')" align="center" prop="memo" width="200" />
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:error-code:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:error-code:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item :label="$t('system.appName')" prop="applicationName">
          <el-input v-model="form.applicationName" :placeholder="$t('system.pleaseEnterTheApplicationName')" :disabled="form.id" maxlength="50" />
        </el-form-item>
        <el-form-item :label="$t('system.errorCode')" prop="code">
          <el-input v-model="form.code" :placeholder="$t('system.pleaseEnterTheErrorCode')" type="number" min="1" step="1" :disabled="form.id" maxlength="10" @onkeyup="value=value.replace(/\D|^0/g,'')" />
        </el-form-item>
        <el-form-item :label="$t('system.errorCodePrompt')" prop="message">
          <el-input v-model="form.message" :placeholder="$t('system.pleaseEnterTheErrorCodePrompt')" maxlength="512" />
        </el-form-item>
        <el-form-item :label="$t('common.remarks')" prop="memo">
          <el-input v-model="form.memo" :placeholder="$t('rfq.pleaseEnterComments')" maxlength="512" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createErrorCode,
  deleteErrorCode,
  exportErrorCodeExcel,
  getErrorCode,
  getErrorCodePage,
  updateErrorCode
} from '@/api/system/errorCode'

export default {
  name: 'Errorcode',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 错误码列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        type: null,
        applicationName: null,
        code: null,
        message: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        applicationName: [{
          required: true,
          message: this.$t('system.theApplicationNameCannotBeEmpty'),
          trigger: 'blur'
        }],
        code: [{ required: true, message: this.$t('system.errorCodeCannotBeEmpty'), trigger: 'blur' }],
        message: [{ required: true, message: this.$t('system.theErrorCodePromptCannotBeEmpty'), trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getErrorCodePage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        applicationName: undefined,
        code: undefined,
        message: undefined,
        memo: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addErrorCode')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getErrorCode(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyErrorCode')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateErrorCode(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createErrorCode(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除错误码编号为"' + id + '"的数据项?').then(function() {
        return deleteErrorCode(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行导出
      this.$modal.confirm(this.$t('system.areYouSureToExportAllErrorCodeDataItems')).then(() => {
        this.exportLoading = true
        return exportErrorCodeExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('system.errorCodexls'))
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>
