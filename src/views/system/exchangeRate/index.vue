<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.currency')" prop="fromCurrency">
        <el-select v-model="queryParams.fromCurrency" :placeholder="$t('order.pleaseSelectACurrency')" clearable>
          <el-option
            v-for="dict in currencyList"
            :key="parseInt(dict.id)"
            :label="dict.name"
            :value="parseInt(dict.id)"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.exchangeRateStartTime')" label-width="100px">
        <el-date-picker
          v-model="dateRangeStartTime"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item :label="$t('system.exchangeRateDeadline')" label-width="110px">
        <el-date-picker
          v-model="dateRangeEndTime"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('supplier.pleaseSelectStatus')" clearable size="small">
          <el-option
            v-for="dict in statusDictDatas"
            :key="parseInt(dict.value)"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:exchange-rate:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:exchange-rate:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >{{ $t('common.export') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('system.currency')" align="left" prop="fromCurrency">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="scope.row.fromCurrency" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.conversionAmount')" align="center" prop="fromUnit" />
      <el-table-column :label="$t('system.homeCurrency')" align="center" prop="toCurrency">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="scope.row.toCurrency" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('rfq.exchangeRate')" align="center" prop="rate" />
      <el-table-column :label="$t('system.exchangeRateStartTime')" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.exchangeRateDeadline')" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('supplier.displayOrder')" align="center" prop="sort" />
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:exchange-rate:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:exchange-rate:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="700px">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('system.currency')" prop="fromCurrency">
              <el-select v-model="form.fromCurrency" :placeholder="$t('order.pleaseSelectACurrency')" clearable>
                <el-option
                  v-for="dict in currencyList"
                  :key="parseInt(dict.id)"
                  :label="dict.name"
                  :value="parseInt(dict.id)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.homeCurrency')" prop="toCurrency">
              <el-select
                v-model="form.toCurrency"
                :placeholder="$t('system.pleaseSelectTheCurrencyAfterConversion')"
                clearable
              >
                <el-option
                  v-for="dict in currencyList"
                  :key="parseInt(dict.id)"
                  :label="dict.name"
                  :value="parseInt(dict.id)"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('system.startTime')" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                :placeholder="$t('system.selectExchangeRateStartTime')"
                clearable
                type="date"
                value-format="timestamp"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.deadline')" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                :placeholder="$t('system.selectExchangeRateDeadline')"
                clearable
                type="date"
                value-format="timestamp"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.conversionAmount')" prop="fromUnit">
              <vxe-input
                v-model="form.fromUnit"
                :placeholder="$t('system.pleaseEnterTheAmountOfTheCurrencyToBeConverted')"
                type="integer"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('rfq.exchangeRate')" prop="rate">
              <el-input v-model="form.rate" :placeholder="$t('system.pleaseEnterTheExchangeRate')" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('supplier.displayOrder')" prop="sort">
              <el-input-number v-model="form.sort" :placeholder="$t('system.pleaseEnterTheDisplayOrder')" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('common.status')" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="parseInt(dict.value)"
                >{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createExchangeRate,
  deleteExchangeRate,
  exportExchangeRateExcel,
  getExchangeRate,
  getExchangeRatePage,
  updateExchangeRate
} from '@/api/system/exchangeRate'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'

export default {
  name: 'Exchangerate',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 汇率列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeStartTime: [],
      dateRangeEndTime: [],
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        fromCurrency: null,
        fromUnit: null,
        toCurrency: null,
        rate: null,
        sort: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        fromCurrency: [{
          required: true,
          message: this.$t('system.theCurrencyToBeConvertedCannotBeBlank'),
          trigger: 'blur'
        }],
        fromUnit: [{
          required: true,
          message: this.$t('system.theAmountOfTheCurrencyToBeConvertedCannotBeBlank'),
          trigger: 'blur'
        }],
        toCurrency: [{ required: true, message: this.$t('system.theFunctionalCurrencyDoesNotExist'), trigger: 'blur' }],
        rate: [{ required: true, message: this.$t('system.exchangeRateCannotBeBlank'), trigger: 'blur' }],
        startTime: [{ required: true, message: this.$t('system.startTimeOfExchangeRateCannotBeBlank'), trigger: 'blur' }],
        endTime: [{ required: true, message: this.$t('system.exchangeRateDeadlineCannotBeBlank'), trigger: 'blur' }],
        sort: [{ required: true, message: this.$t('system.displayOrderCannotBeEmpty'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }]
      },
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS),
      // 币种列表
      currencyList: getDictDatas(DICT_TYPE.COMMON_CURRENCY)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeStartTime, 'startTime')
      this.addBeginAndEndTime(params, this.dateRangeEndTime, 'endTime')
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getExchangeRatePage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        fromCurrency: undefined,
        fromUnit: undefined,
        toCurrency: undefined,
        rate: undefined,
        startTime: undefined,
        endTime: undefined,
        sort: undefined,
        status: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeStartTime = []
      this.dateRangeEndTime = []
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addExchangeRate')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      getExchangeRate(row.id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyExchangeRate')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateExchangeRate(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createExchangeRate(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除汇率编号为"' + id + '"的数据项?').then(function() {
        return deleteExchangeRate(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeStartTime, 'startTime')
      this.addBeginAndEndTime(params, this.dateRangeEndTime, 'endTime')
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行导出
      this.$modal.confirm(this.$t('system.areYouSureToExportAllExchangeRateDataItems')).then(() => {
        this.exportLoading = true
        return exportExchangeRateExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('system.exchangeRatexls'))
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>
