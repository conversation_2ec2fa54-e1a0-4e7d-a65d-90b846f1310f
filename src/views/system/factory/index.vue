<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.factoryCode')" prop="code">
        <el-input v-model="queryParams.code" clearable :placeholder="$t('system.pleaseEnterTheFactoryCode')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.factoryName')" prop="name">
        <el-input v-model="queryParams.name" clearable :placeholder="$t('system.pleaseEnterTheFactoryName')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" clearable :placeholder="$t('supplier.pleaseSelectStatus')" size="small">
          <el-option
            v-for="dict in statusDictDatas"
            :key="parseInt(dict.value)"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.createTime')">
        <el-date-picker
          v-model="dateRangeCreateTime"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:factory:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:factory:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >{{ $t('common.export') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        align="left"
        :label="$t('system.factoryCode')"
        prop="code"
      />
      <el-table-column
        align="center"
        :label="$t('system.factoryName')"
        prop="name"
      />
      <el-table-column
        align="center"
        :label="$t('公司地址')"
        prop="address"
      />
      <el-table-column
        align="center"
        :label="$t('system.companyName')"
        prop="name"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="scope.row.companyId" />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :label="$t('supplier.displayOrder')"
        prop="sort"
      />
      <el-table-column
        align="center"
        :label="$t('common.status')"
        prop="status"
      >
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        :label="$t('common.createTime')"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        :label="$t('common.operate')"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:factory:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:factory:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('system.factoryCode')" prop="code">
          <el-input v-model="form.code" :placeholder="$t('system.pleaseEnterTheFactoryCode')" />
        </el-form-item>
        <el-form-item :label="$t('system.factoryName')" prop="name">
          <el-input v-model="form.name" :placeholder="$t('system.pleaseEnterTheFactoryName')" />
        </el-form-item>
        <el-form-item :label="$t('工厂地址')" prop="address">
          <el-input v-model="form.address" :placeholder="$t('请输入工厂地址')" />
        </el-form-item>
        <el-form-item :label="$t('system.affiliatedCompanies')" prop="companyId">
          <el-select v-model="form.companyId">
            <el-option v-for="item in companyLists" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('supplier.displayOrder')" prop="sort">
          <el-input-number v-model="form.sort" :placeholder="$t('system.pleaseEnterTheDisplayOrder')" />
        </el-form-item>
        <el-form-item :label="$t('common.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createFactory,
  deleteFactory,
  exportFactoryExcel,
  getFactory,
  getFactoryPage,
  updateFactory
} from '@/api/system/factory'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getCompanyLists } from '@/api/system/companies'

export default {
  name: 'Factory',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工厂列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      companyLists: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        code: null,
        companyId: null,
        name: null,
        sort: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        code: [{ required: true, message: this.$t('system.factoryCodeCannotBeEmpty'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('system.theFactoryNameCannotBeEmpty'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('system.associatedCompanyCannotBeBlank'), trigger: 'change' }],
        sort: [{ required: true, message: this.$t('system.displayOrderCannotBeEmpty'), trigger: 'blur' }]
      },
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS)
    }
  },
  created() {
    this.getList()
    this.getCompanyLists()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getFactoryPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    getCompanyLists() {
      getCompanyLists().then(item => {
        this.companyLists = item.data
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        code: undefined,
        companyId: undefined,
        name: undefined,
        address: undefined,
        sort: undefined,
        status: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addFactory')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getFactory(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyFactory')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateFactory(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createFactory(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      const code = row.code
      this.$modal.confirm('是否确认删除工厂编号为"' + code + '"的数据项?').then(function() {
        return deleteFactory(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行导出
      this.$modal.confirm(this.$t('system.areYouSureToExportAllFactoryDataItems')).then(() => {
        this.exportLoading = true
        return exportFactoryExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('system.factoryxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>
