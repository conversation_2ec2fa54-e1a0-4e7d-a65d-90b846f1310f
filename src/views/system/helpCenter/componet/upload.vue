<template>
  <div>
    <el-button v-if="!disabled" type="primary" @click="showFile">{{ $t('common.uploadFile') }}
      <span v-show="fileLength">({{ fileLength }})</span>
    </el-button>
    <el-button v-else style="margin-right: 5px" type="text" @click="showFileContent">{{ $t('rfq.enclosure') }}({{ originNum }})</el-button>
    <el-dialog
      v-if="visible"
      :title="disabled? $t('scar.seeFile'):$t('common.uploadFile')"
      :visible.sync="visible"
      width="500px"
      append-to-body
      @close="submitFileForm"
    >
      <el-upload
        ref="upload"
        :headers="headers"
        class="upload-container"
        :action="url"
        :on-success="handleFileSuccess"
        :file-list="fileList"
        :on-remove="handleRemove"
        :disabled="disabled"
        :on-preview="onPreview"
        :drag="!disabled"
        multiple
      >
        <i v-if="!disabled" class="el-icon-upload" />
        <div v-if="!disabled" class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="!disabled"
          type="primary"
          @click="submitFileForm"
        >  {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getAccessToken } from '@/utils/auth'

export default {
  name: 'HelpCenterFile',
  inject: ['helpCenterInfo'],
  props: {
    originNum: {
      type: Number
    },
    businessType: {
      type: String
    },
    disabled: {
      type: Boolean
    }
  },
  data() {
    return {
      visible: null,
      isUploading: false,
      url: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      data: {},
      fileList: [],
      // 一次批量上传的临时文件集合
      tempList: [],
      delIds: []
    }
  },
  computed: {
    helpCenter() {
      return this.helpCenterInfo()
    },
    fileLength() {
      return this.helpCenter.fileList?.length
    }
  },
  methods: {
    handleFileSuccess(response, file) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
        this.$refs.upload.clearFiles()
        return
      }
      this.tempList.push({
        businessType: 'help_center',
        businessId: this.helpCenter.id,
        fileId: response.data.id,
        fileName: file.name,
        fileUrl: response.data.url
      })
    },
    submitFileForm() {
      this.fileList = this.fileList.filter(a => !this.delIds.includes(a.fileId))
      this.helpCenter.fileList = [...this.fileList, ...this.tempList]
      // 一次批量上传的临时文件集合需要在此处置空
      this.tempList = []
      this.visible = false
    },
    handleRemove(file) {
      if (file.fileId) {
        this.delIds.push(file.fileId)
      } else {
        const index = this.fileList.findIndex(a => a.fileId === file.response.data.id)
        this.tempList.splice(index, 1)
      }
    },
    showUpload() {
      if (!this.helpCenter.id) {
        this.$message.error('请先保存当前填写内容')
      } else {
        this.showFile()
      }
    },
    onPreview(file) {
      if (file.fileUrl) {
        window.open(file.fileUrl)
      }
    },
    showFile() {
      this.visible = true
      const a = this.helpCenter.fileList
      a.forEach(b => {
        b.name = b?.fileName
      })
      this.fileList = a
    },
    showFileContent() {
      this.$emit('getFile')
      this.showFile()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body{
  padding-top: 15px;
}
</style>
