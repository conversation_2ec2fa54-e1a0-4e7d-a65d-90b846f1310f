<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true">

      <el-form-item :label="$t('system.title')" prop="title">
        <el-input v-model="queryParams.title" :placeholder="$t('system.pleaseEnterATitle')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item v-if="$store.getters.isExternal!==2" :label="$t('system.isTheScopeOfDisclosureOpenToThePublic')" prop="scope">
        <el-select v-model="queryParams.scope" :placeholder="$t('common.pleaseSelect')" clearable>
          <el-option v-for="dict in yesOrNo" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>

    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:help-center:create']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <!--      <el-table-column :label="$t('rfq.serialNo')"  prop="id" width="100" />-->
      <el-table-column :label="$t('system.title')" prop="title" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-button
            style="text-decoration: underline"
            type="text"
            @click="showDetail(scope.row)"
          >{{ scope.row.title }}</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.isTheScopeOfDisclosureOpenToThePublic')" prop="scope">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_Y_N" :value="scope.row.scope" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column key="operate" :label="$t('common.operate')" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <helpCenterFile
            v-if="scope.row.fileList.length"
            :origin-num="scope.row.fileList.length"
            :disabled="true"
            :business-type="'help_center'"
            style="display: inline-block"
            @getFile="handleFile(scope.row)"
          />

          <el-button
            v-hasPermi="['system:help-center:update']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['system:help-center:delete']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="helpCenterForm" :model="helpCenterForm" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('system.title')" prop="title">
              <el-input v-model="helpCenterForm.title" show-word-limit maxlength="50" :placeholder="$t('system.pleaseEnterATitle')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.isTheScopeOfDisclosureOpenToThePublic')">
              <el-radio-group v-model="helpCenterForm.scope">
                <el-radio v-for="dict in yesOrNo" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('common.status')">
              <el-radio-group v-model="helpCenterForm.status">
                <el-radio
                  v-for="dict in statusDictDatas"
                  :key="parseInt(dict.value)"
                  :label="parseInt(dict.value)"
                >{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('rfq.enclosure')">
              <helpCenterFile
                :business-type="'help_center'"
              />
            </el-form-item>
            <el-form-item :label="$t('system.content')">
              <editor v-model="helpCenterForm.content" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
    <el-dialog
      v-if="detailVisible"
      :title="helpCenterForm.title"
      width="900px"
      :visible.sync="detailVisible"
    >
      <div class="ql-editor" v-html="helpCenterForm.content" />
      <el-descriptions v-if="helpCenterForm.fileList.length" style="padding-left: 12px" :column="1" :colon="false" label-class-name="labelTitle">
        <el-descriptions-item
          :label="$t('附件')"
          :label-style="{'font-weight':'bold'}"
          :content-style="{'display':'flex','flex-wrap':'wrap','align-items':'flex-start'}"
        >
          <div v-for="item in helpCenterForm.fileList" style="width: 100%;align-self: flex-start;">
            <el-button style="text-decoration: underline" type="text" @click="openFile(item.fileUrl)">{{
              item.fileName
            }}
            </el-button>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { getHelpCenterPage, getHelpCenter, createHelpCenter, updateHelpCenter, deleteHelpCenter } from '@/api/system/helpCenter'
import { CommonStatusEnum } from '@/utils/constants'
import { getDictDatas, DICT_TYPE } from '@/utils/dict'
import Editor from '@/components/Editor'

export default {
  name: 'Helpcenter',
  components: {
    Editor,
    helpCenterFile: () => import('./componet/upload.vue')
  },
  provide() {
    return {
      helpCenterInfo: () => this.helpCenterForm
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 帮助中心列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        title: null,
        content: null,
        scope: null,
        status: null
      },
      // 表单参数
      helpCenterForm: {
        title: '',
        content: '',
        scope: '',
        status: null,
        fileList: []
      },
      // 表单校验
      rules: {
        title: [{
          required: true,
          message: this.$t('system.theTitleCannotBeEmpty'), trigger: 'blur' }],
        content: [{
          required: true,
          message: this.$t('system.contentCannotBeEmpty'), trigger: 'blur' }],
        scope: [{
          required: true,
          message: this.$t('system.theRangeCannotBeEmpty'), trigger: 'blur' }],
        status: [{
          required: true,
          message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }]
      },
      // 枚举
      CommonStatusEnum: CommonStatusEnum,
      // 数据字典
      scopeDictDatas: getDictDatas(DICT_TYPE.SYSTEM_ROLE_IS_EXTERNAL),
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS),
      // 是否
      yesOrNo: getDictDatas(DICT_TYPE.COMMON_Y_N).reverse(),
      detailVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    openFile(url) {
      window.open(url)
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getHelpCenterPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.helpCenterForm = {
        id: undefined,
        title: '',
        content: '',
        scope: '',
        status: null,
        fileList: []
      }
      this.resetForm('helpCenterForm')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        title: null,
        content: null,
        scope: '',
        status: null
      }
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addHelpCenter')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getHelpCenter(id).then(response => {
        this.helpCenterForm = response.data
        this.open = true
        this.title = this.$t('system.modifyHelpCenter')
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['helpCenterForm'].validate(valid => {
        if (valid) {
          if (this.helpCenterForm.id !== undefined) {
            updateHelpCenter({ ...this.helpCenterForm,
              scope: this.helpCenterForm.scope
            }).then(response => {
              this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
              this.open = false
              this.getList()
            })
          } else {
            createHelpCenter({ ...this.helpCenterForm,
              scope: this.helpCenterForm.scope
            }).then(response => {
              this.$modal.msgSuccess(this.$t('common.addSuccess'))
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      const title = row.title || this.title
      this.$modal.confirm('是否确认删除帮助中心标题为"' + title + '"的数据项?').then(function() {
        return deleteHelpCenter(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {})
    },
    showDetail(row) {
      this.detailVisible = true
      this.helpCenterForm = row
    },
    handleFile(row) {
      this.helpCenterForm = row
    }
  }
}
</script>
