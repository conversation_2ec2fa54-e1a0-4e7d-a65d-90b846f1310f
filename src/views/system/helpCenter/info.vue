<template>
  <div class="helpCenter">
    <common-card
      :title="$t('system.helpMessageCenter')"
    >
      <div class="helpCenter-body">
        <p v-html="helpCenterInfo.content" />
        <el-upload
          ref="upload"
          v-has-permi="['system:help-center:query']"
          :headers="headers"
          class="upload-container"
          :file-list="helpCenterInfo.fileList"
          :action="url"
          :disabled="true"
        />
      </div>
    </common-card>

  </div>
</template>

<script>
import { getAccessToken } from '@/utils/auth'
import { getHelpCenter } from '@/api/system/helpCenter'
import { openNoticeFile } from '@/api/system/notice'

export default {
  name: 'Info',
  props: ['helpCenterId'],
  data() {
    return {
      helpCenterInfo: {
        title: '',
        type: null,
        content: '',
        status: null,
        fileList: [],
        scope: ''
      },
      url: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      data: {},
      fileList: [],
      hasRead: true
    }
  },
  computed: {
    hasUpload() {
      return this.helpCenterInfo.fileList.length !== 0
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      getHelpCenter({
        id: this.noticeId
      }).then(res => {
        res.data.fileList.forEach(a => {
          a.name = a.fileName
          a.url = a.filePath
        })
        this.noticeInfo = res.data
      })
    },
    closeWindow() {
      window.close()
    },
    readFile() {
      openNoticeFile({
        id: this.noticeId
      })
    }
  }
}

</script>

<style lang="scss" scoped>
::v-deep .el-upload--text{
  text-align: left;
}
.helpCenter{
  &-body{
    padding: 0 70px;
  }
}
</style>

