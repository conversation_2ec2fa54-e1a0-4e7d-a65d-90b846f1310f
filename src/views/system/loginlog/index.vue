<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.loginAddress')" prop="userIp">
        <el-input
          v-model="queryParams.userIp"
          clearable
          :placeholder="$t('system.pleaseEnterTheLoginAddress')"
          style="width: 240px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.loginAccount')" prop="username">
        <el-input
          v-model="queryParams.username"
          clearable
          :placeholder="$t('system.pleaseEnterTheLoginAccount')"
          style="width: 240px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" clearable :placeholder="$t('system.result')" style="width: 240px">
          <el-option :key="true" :value="true" :label="$t('system.success')" />
          <el-option :key="false" :value="false" :label="$t('system.fail')" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.loginTime')">
        <el-date-picker
          v-model="dateRange"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:login-log:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          type="warning"
          @click="handleExport"
        >{{ $t('common.export') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="list">
      <el-table-column
        align="center"
        :label="$t('system.accessNumber')"
        prop="id"
      />
      <el-table-column
        align="center"
        :label="$t('system.logType')"
        prop="logType"
        width="120"
      >
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_LOGIN_TYPE" :value="scope.row.logType" />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :label="$t('system.loginAccount')"
        prop="username"
      />
      <el-table-column
        :show-overflow-tooltip="true"
        align="center"
        :label="$t('system.loginAddress')"
        prop="userIp"
        width="130"
      />
      <el-table-column :show-overflow-tooltip="true" align="center" label="userAgent" prop="userAgent" width="400" />
      <el-table-column
        prop="status"
      >
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_LOGIN_RESULT" :value="scope.row.result" />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :label="$t('system.loginDate')"
        prop="loginTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { list, exportLoginLog } from '@/api/system/loginlog'

export default {
  name: 'Logininfor',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 状态数据字典
      statusOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        userIp: undefined,
        username: undefined,
        status: undefined
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询登录日志列表 */
    getList() {
      this.loading = true
      list(this.addDateRange(this.queryParams, [
        this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined,
        this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined
      ])).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      }
      )
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$modal.confirm(this.$t('system.areYouSureToExportAllOperationLogDataItems')).then(() => {
        this.exportLoading = true
        return exportLoginLog(queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('system.loginLogxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>

