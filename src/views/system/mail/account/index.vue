<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('supplier.mailbox')" prop="mail">
        <el-input v-model="queryParams.mail" clearable :placeholder="$t('supplier.pleaseEnterEmail')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.userName')" prop="username">
        <el-input v-model="queryParams.username" clearable :placeholder="$t('system.enterOneUserName')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:mail-account:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" :label="$t('order.number')" prop="id" />
      <el-table-column align="center" :label="$t('supplier.mailbox')" prop="mail" />
      <el-table-column align="center" :label="$t('system.userName')" prop="username" />
      <el-table-column align="center" :label="$t('system.smtpServerDomainName')" prop="host" />
      <el-table-column align="center" :label="$t('system.smtpServerPort')" prop="port" />
      <el-table-column align="center" :label="$t('system.isSslEnabled')" prop="sslEnable">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.sslEnable" />
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('common.createTime')" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" :label="$t('common.operate')">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:mail-account:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:mail-account:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item :label="$t('supplier.mailbox')" prop="mail">
          <el-input v-model="form.mail" :placeholder="$t('supplier.pleaseEnterEmail')" maxlength="255" />
        </el-form-item>
        <el-form-item :label="$t('system.userName')" prop="username">
          <el-input v-model="form.username" :placeholder="$t('system.pleaseEnterYourUsernameItUsuallyMatchesYourEmailAddress')" maxlength="255" />
        </el-form-item>
        <el-form-item :label="$t('common.password')" prop="password">
          <el-input v-model="form.password" :placeholder="$t('system.pleaseEnterPassword')" maxlength="255" />
        </el-form-item>
        <el-form-item :label="$t('system.smtpServerDomainName')" prop="host">
          <el-input v-model="form.host" :placeholder="$t('system.pleaseEnterTheSmtpServerDomainName')" maxlength="255" />
        </el-form-item>
        <el-form-item :label="$t('system.smtpServerPort')" prop="port">
          <el-input v-model="form.port" :placeholder="$t('system.pleaseEnterTheSmtpServerPort')" type="number" min="1" step="1" max="65536" maxlength="5" @onkeyup="value=value.replace(/\D|^0/g,'')" />
        </el-form-item>
        <el-form-item :label="$t('system.isSslEnabled')" prop="sslEnable">
          <el-radio-group v-model="form.sslEnable">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
              :key="dict.value"
              :label="dict.value === 'true'"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createMailAccount,
  deleteMailAccount,
  getMailAccount,
  getMailAccountPage,
  updateMailAccount
} from '@/api/system/mail/account'

export default {
  name: 'Mailaccount',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 邮箱账号列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        mail: null,
        username: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mail: [{ required: true, message: this.$t('system.emailCannotBeEmpty'), trigger: 'blur' }],
        username: [{ required: true, message: this.$t('system.theUsernameCannotBeEmpty'), trigger: 'blur' }],
        password: [{ required: true, message: this.$t('system.passwordCannotBeEmpty'), trigger: 'blur' }],
        host: [{ required: true, message: this.$t('system.theSmtpServerDomainNameCannotBeEmpty'), trigger: 'blur' }],
        port: [{ required: true, message: this.$t('system.theSmtpServerPortCannotBeEmpty'), trigger: 'blur' }],
        sslEnable: [{ required: true, message: this.$t('system.whetherToEnableSslCannotBeEmpty'), trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getMailAccountPage(this.queryParams).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        mail: undefined,
        username: undefined,
        password: undefined,
        host: undefined,
        port: undefined,
        sslEnable: true
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addEmailAccount')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getMailAccount(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyEmailAccount')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateMailAccount(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createMailAccount(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除邮箱账号编号为"' + id + '"的数据项?').then(function() {
        return deleteMailAccount(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    }
  }
}
</script>
