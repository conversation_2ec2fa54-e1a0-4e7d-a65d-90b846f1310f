<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.receivingEmail')" prop="toMail">
        <el-input
          v-model="queryParams.toMail"
          :placeholder="$t('system.pleaseEnterTheReceivingEmailAddress')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.emailAccount')" prop="accountId">
        <el-select v-model="queryParams.accountId" :placeholder="$t('system.pleaseEnterYourEmailAccount')" clearable>
          <el-option v-for="account in accountOptions" :key="account.id" :label="account.mail" :value="account.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.emailTitle')" prop="templateTitle">
        <el-input
          v-model="queryParams.templateTitle"
          :placeholder="$t('system.pleaseEnterTheEmailTitle')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.sendStatus')" prop="sendStatus">
        <el-select v-model="queryParams.sendStatus" :placeholder="$t('system.pleaseSelectTheSendingStatus')" clearable size="small">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_MAIL_SEND_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.sendingTime')" prop="sendTime">
        <el-date-picker
          v-model="queryParams.sendTime"
          :default-time="['00:00:00', '23:59:59']"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-button type="primary" @click="batchResend">批量重发</el-button>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table ref="table" v-loading="loading" :data="list">
      <el-table-column type="selection" width="30"></el-table-column>
      <el-table-column :label="$t('order.number')" align="center" prop="id" />
      <el-table-column :label="$t('system.sendingTime')" align="center" prop="sendTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.sendTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.receivingEmail')" align="center" prop="toMail" width="200">
        <template #default="scope">
          <div>{{ scope.row.toMail }}</div>
          <div v-if="scope.row.userType && scope.row.userId">
            <dict-tag :type="DICT_TYPE.USER_TYPE" :value="scope.row.userType" />
            {{ '(' + scope.row.userId + ')' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.emailTitle')" align="center" prop="templateTitle" />
      <el-table-column :label="$t('system.sendStatus')" align="center" prop="sendStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_MAIL_SEND_STATUS" :value="scope.row.sendStatus" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.emailAccount')" align="center" prop="fromMail" />
      <el-table-column :label="$t('system.templateNo')" align="center" prop="templateId" />
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:mail-log:query']"
            icon="el-icon-view"
            size="mini"
            type="text"
            @click="handleView(scope.row)"
          >{{ $t('system.detailed') }}
          </el-button>
          <el-button
            v-hasPermi="['system:mail-log:query']"
            icon="el-icon-plus"
            size="mini"
            type="text"
            @click="handleResend(scope.row)"
          >{{ $t('system.retransmission') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 邮件日志详细-->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" append-to-body width="700px">
      <el-form ref="form" :model="form" label-width="160px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('system.logPrimaryKey')">{{ form.id }}</el-form-item>
            <el-form-item :label="$t('system.userNo')">{{ form.userId }}</el-form-item>
            <el-form-item :label="$t('system.customerType')">
              <dict-tag :type="DICT_TYPE.USER_TYPE" :value="form.userType" />
            </el-form-item>
            <el-form-item :label="$t('system.receivingEmailAddress')">{{ form.toMail }}</el-form-item>
            <el-form-item :label="$t('system.ccEmailAddress')">{{ form.receiverCc }}</el-form-item>
            <el-form-item :label="$t('system.emailAccountNumber')">{{ form.accountId }}</el-form-item>
            <el-form-item :label="$t('system.sendEmailAddress')">{{ form.fromMail }}</el-form-item>
            <el-form-item :label="$t('system.templateNo')">{{ form.templateId }}</el-form-item>
            <el-form-item :label="$t('system.templateCode')">{{ form.templateCode }}</el-form-item>
            <el-form-item :label="$t('system.templateSenderName')">{{ form.templateNickname }}</el-form-item>
            <el-form-item :label="$t('system.emailTitle')">{{ form.templateTitle }}</el-form-item>
            <el-form-item :label="$t('system.emailContent')">
              <editor v-model="form.templateContent" :min-height="192" read-only />
            </el-form-item>
            <el-form-item :label="$t('system.emailParameters')">{{ form.templateParams }}</el-form-item>
            <el-form-item :label="$t('system.emailTitleParameters')">{{ form.templateTitleParams }}</el-form-item>
            <el-form-item :label="$t('system.attachmentParameters')">{{ form.attachment }}</el-form-item>
            <el-form-item :label="$t('system.sendStatus')">
              <dict-tag :type="DICT_TYPE.SYSTEM_MAIL_SEND_STATUS" :value="form.sendStatus" />
            </el-form-item>
            <el-form-item :label="$t('system.sendingTime')">{{ parseTime(form.sendTime) }}</el-form-item>
            <el-form-item :label="$t('system.sendAndReturnMessageNumber')">{{ form.sendMessageId }}</el-form-item>
            <el-form-item :label="$t('system.sendingException')">{{ form.sendException }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">{{ $t('order.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMailLogPage, resendMsgLog } from '@/api/system/mail/log'
import Editor from '@/components/Editor'
import { getSimpleMailAccountList } from '@/api/system/mail/account'
import {batchResendMsgLog} from "../../../../api/system/mail/log";

export default {
  name: 'Maillog',
  components: {
    Editor
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 邮件日志列表
      list: [],
      // 弹出层标题
      title: this.$t('system.emailSendingLogDetails'),
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        userId: null,
        userType: null,
        toMail: null,
        accountId: null,
        templateTitle: null,
        sendStatus: null,
        sendTime: []
      },
      // 表单参数
      form: {},
      // 邮箱账号
      accountOptions: []
    }
  },
  created() {
    this.getList()
    // 获得邮箱账号列表
    getSimpleMailAccountList().then(response => {
      this.accountOptions = response.data
    })
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getMailLogPage(this.queryParams).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        userId: undefined,
        userType: undefined,
        toMail: undefined,
        receiverCc: undefined,
        accountId: undefined,
        fromMail: undefined,
        templateId: undefined,
        templateCode: undefined,
        templateNickname: undefined,
        templateTitle: undefined,
        templateContent: undefined,
        templateParams: undefined,
        templateTitleParams: undefined,
        attachment: undefined,
        sendStatus: undefined,
        sendTime: undefined,
        sendMessageId: undefined,
        sendException: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true
      this.form = row
    },
    /** 重发按钮操作 */
    handleResend(row) {
      const id = row.id
      this.$modal.confirm(this.$t('system.areYouSureToResendTheMessageLog')).then(function() {
        return resendMsgLog(id)
        // return true;
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('system.resendSucceeded'))
      }).catch(() => {
      })
    },
    batchResend() {
      const ids = this.$refs.table.selection.map(item => item.id)
      if (ids.length === 0) {
        this.$message.warning(this.$t('system.pleaseSelect'))
        return
      }
      this.$modal.confirm(this.$t('system.areYouSureToResendTheMessageLog')).then(() => {
        return batchResendMsgLog(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('system.resendSucceeded'))
      }).catch(() => {
      })
    }
  }
}
</script>
