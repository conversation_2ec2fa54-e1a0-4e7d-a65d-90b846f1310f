<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('rfq.templateName')" prop="name">
        <el-input v-model="queryParams.name" clearable :placeholder="$t('system.pleaseEnterATemplateName')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.templateCode')" prop="code">
        <el-input v-model="queryParams.code" clearable :placeholder="$t('system.pleaseEnterTheTemplateCode')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.emailAccount')" prop="accountId">
        <el-select v-model="queryParams.accountId" clearable :placeholder="$t('system.pleaseEnterYourEmailAccount')">
          <el-option v-for="account in accountOptions" :key="account.id" :label="account.mail" :value="account.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.openState')" prop="status">
        <el-select v-model="queryParams.status" clearable :placeholder="$t('system.pleaseSelectTheOpenState')" size="small">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.createTime')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="['00:00:00', '23:59:59']"
          end-placeholder="结束日期"
          range-separator="-"
          start-placeholder="开始日期"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:mail-template:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" :label="$t('system.templateCode')" prop="code" />
      <el-table-column align="center" :label="$t('rfq.templateName')" prop="name" />
      <el-table-column :label="$t('system.templateLanguage')" align="center" prop="locale">
        <template slot-scope="scope">
          {{ translationLanguages.find(item => item.locale === scope.row.locale).name }}
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('system.templateTitle')" prop="title" />
      <el-table-column :show-overflow-tooltip="true" align="center" :label="$t('system.templateContent')" prop="content" />
      <el-table-column align="center" :label="$t('system.emailAccount')" prop="accountId" width="200">
        <template #default="scope">
          {{ accountOptions.find(account => account.id === scope.row.accountId)?.mail }}
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('system.nameOfSender')" prop="nickname" />
      <el-table-column align="center" :label="$t('system.openState')" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('供应商标记')" prop="supplierFlag">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.supplierFlag" />
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('common.createTime')" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" :label="$t('common.operate')" width="150">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:mail-template:send-mail']"
            icon="el-icon-share"
            size="mini"
            type="text"
            @click="handleSend(scope.row)"
          >{{ $t('system.test') }}
          </el-button>
          <el-button
            v-hasPermi="['system:mail-template:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:mail-template:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog v-dialogDrag v-if="open" :title="title" :visible.sync="open" append-to-body width="700px">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item :label="$t('rfq.templateName')" prop="name">
          <el-input v-model="form.name" :placeholder="$t('system.pleaseEnterATemplateName')" />
        </el-form-item>
        <el-form-item :label="$t('system.templateCode')" prop="code">
          <el-input v-model="form.code" :placeholder="$t('system.pleaseEnterTheTemplateCode')" />
        </el-form-item>
        <el-form-item :label="$t('system.templateLanguage')" prop="locale">
          <el-select v-model="form.locale" :placeholder="$t('system.pleaseSelectATemplateLanguage')">
            <el-option
              v-for="item in translationLanguages"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="item.locale"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.emailAccount')" prop="accountId">
          <el-select v-model="form.accountId" :placeholder="$t('system.pleaseEnterYourEmailAccount')">
            <el-option v-for="account in accountOptions" :key="account.id" :label="account.mail" :value="account.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.nameOfSender')" prop="nickname">
          <el-input v-model="form.nickname" :placeholder="$t('system.pleaseEnterTheSendersName')" />
        </el-form-item>
        <el-form-item :label="$t('system.templateTitle')" prop="title">
          <el-input v-model="form.title" :placeholder="$t('system.pleaseEnterTheTemplateTitle')" />
        </el-form-item>
        <el-form-item :label="$t('system.templateContent')">
          <tinymce v-model="form.content" :height="192" />
        </el-form-item>
        <el-form-item :label="$t('system.openState')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('供应商标记')" prop="status">
          <el-radio-group v-model="form.supplierFlag">
            <el-radio
                v-for="dict in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
                :key="dict.value"
                :label="dict.value === 'true'"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('common.remarks')" prop="remark">
          <el-input v-model="form.remark" :placeholder="$t('rfq.pleaseEnterComments')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>

    <!-- 对话框(发送邮件) -->
    <el-dialog :visible.sync="sendOpen" append-to-body :title="$t('system.testSendingEmails')" width="500px">
      <el-form ref="sendForm" :model="sendForm" :rules="sendRules" label-width="140px">
        <el-form-item :label="$t('system.templateContent')" prop="content">
          <editor v-model="sendForm.content" :min-height="192" readonly />
        </el-form-item>
        <el-form-item :label="$t('system.recipientEmail')" prop="mail">
          <el-input v-model="sendForm.mail" :placeholder="$t('system.pleaseEnterTheRecipientEmailAddress')" />
        </el-form-item>
        <el-form-item
          v-for="param in sendForm.params"
          :key="param"
          :label="'参数 {' + param + '}'"
          :prop="'templateParams.' + param"
        >
          <el-input v-model="sendForm.templateParams[param]" :placeholder="'请输入 ' + param + ' 参数'" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelSend">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitSendForm">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createMailTemplate,
  deleteMailTemplate,
  getMailTemplate,
  getMailTemplatePage,
  sendMail,
  updateMailTemplate
} from '@/api/system/mail/template'
import Editor from '@/components/Editor'
import { CommonStatusEnum } from '@/utils/constants'
import { getSimpleMailAccountList } from '@/api/system/mail/account'
import { listTranslationLanguages } from '@/api/system/translationLanguage'

export default {
  name: 'Mailtemplate',
  components: {
    Editor
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 邮件模版列表
      list: [],
      // 弹出层标题
      title: '',
      // translationLanguage下拉列表
      translationLanguages: [],
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        code: null,
        accountId: null,
        status: null,
        createTime: []
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: this.$t('system.templateNameCannotBeEmpty'), trigger: 'blur' }],
        locale: [{ required: true, message: this.$t('system.templateLanguageCannotBeEmpty'), trigger: 'change' }],
        code: [{ required: true, message: this.$t('system.templateCodeCannotBeEmpty'), trigger: 'blur' }],
        accountId: [{ required: true, message: this.$t('system.emailAccountCannotBeEmpty'), trigger: 'blur' }],
        title: [{ required: true, message: this.$t('system.templateTitleCannotBeEmpty'), trigger: 'blur' }],
        content: [{ required: true, message: this.$t('system.templateContentCannotBeEmpty'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('system.theOpeningStatusCannotBeEmpty'), trigger: 'blur' }],
        supplierFlag: [{ required: true, message: this.$t('供应商标记不能为空'), trigger: 'blur' }]
      },
      // 邮箱账号
      accountOptions: [],

      // 发送邮箱
      sendOpen: false,
      sendForm: {
        params: [] // 模板的参数列表
      },
      sendRules: {
        mail: [{ required: true, message: this.$t('system.theRecipientEmailCannotBeEmpty'), trigger: 'blur' }],
        templateCode: [{ required: true, message: this.$t('system.templateCodeCannotBeEmpty'), trigger: 'blur' }],
        templateParams: {}
      }
    }
  },
  created() {
    this.getList()
    listTranslationLanguages().then(response => {
      this.translationLanguages = response.data
    })
    // 获得邮箱账号列表
    getSimpleMailAccountList().then(response => {
      this.accountOptions = response.data
    })
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getMailTemplatePage(this.queryParams).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        code: undefined,
        accountId: undefined,
        nickname: undefined,
        title: undefined,
        content: undefined,
        status: CommonStatusEnum.ENABLE,
        supplierFlag: false,
        remark: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addEmailTemplate')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getMailTemplate(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyEmailTemplate')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateMailTemplate(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createMailTemplate(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除邮件模版编码为"' + row.code + '"的数据项?').then(function() {
        return deleteMailTemplate(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 发送短息按钮 */
    handleSend(row) {
      this.resetSend(row)
      // 设置参数
      this.sendForm.content = row.content
      this.sendForm.locale = row.locale
      this.sendForm.params = row.params
      this.sendForm.templateCode = row.code
      this.sendForm.templateParams = row.params.reduce(function(obj, item) {
        obj[item] = undefined
        return obj
      }, {})
      // 根据 row 重置 rules
      this.sendRules.templateParams = row.params.reduce(function(obj, item) {
        obj[item] = { required: true, message: '参数 ' + item + ' 不能为空', trigger: 'change' }
        return obj
      }, {})
      // 设置打开
      this.sendOpen = true
    },
    /** 重置发送邮箱的表单 */
    resetSend() {
      // 根据 row 重置表单
      this.sendForm = {
        content: undefined,
        params: undefined,
        mail: undefined,
        templateCode: undefined,
        templateParams: {}
      }
      this.resetForm('sendForm')
    },
    /** 取消发送邮箱 */
    cancelSend() {
      this.sendOpen = false
      this.resetSend()
    },
    /** 提交按钮 */
    submitSendForm() {
      this.$refs['sendForm'].validate(valid => {
        if (!valid) {
          return
        }
        // 添加的提交
        sendMail(this.sendForm).then(response => {
          this.$modal.msgSuccess(this.$t('system.submitAndSendSuccessfullySeeSendingLogNoForSendingResults') + response.data)
          this.sendOpen = false
        })
      })
    }
  }
}
</script>
