<template>
  <div class="app-container">

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:mail-remind-config:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        :formatter="convertBusinessName"
        :label="$t('system.businessName')"
        align="center"
        prop="businessName"
      />
      <el-table-column
        :formatter="convertFrequencyType"
        :label="$t('system.frequencyType')"
        align="center"
        prop="frequencyType"
      />
      <el-table-column :label="$t('system.frequencyInterval')" align="center" prop="frequencyInterval" />
      <el-table-column :label="$t('system.sendingTime')" align="center" prop="sendTime" />
      <el-table-column
        :formatter="convertRemindType"
        :label="$t('system.reminderType')"
        align="center"
        prop="remindType"
      />
      <el-table-column :label="$t('system.scheduledDays')" align="center" prop="remindTypeDays" />
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:mail-remind-config:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:mail-remind-config:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('system.businessName')" prop="businessName">
          <el-select v-model="form.businessName" :placeholder="$t('system.pleaseSelectTheEmailReminderBusinessName')">
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_MAIL_REMIND_NAME)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.frequencyType')" prop="frequencyType">
          <el-select v-model="form.frequencyType" :placeholder="$t('system.pleaseSelectAFrequencyType')">
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_MAIL_REMIND_FREQUENCY_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.frequencyInterval')" prop="frequencyInterval">
          <el-input-number
            v-model="form.frequencyInterval"
            :placeholder="$t('system.pleaseEnterTheFrequencyIntervalTheDefaultIs')"
          />
        </el-form-item>
        <el-form-item :label="$t('system.sendingTime')" prop="sendTime">
          <el-time-select
            v-model="form.sendTime"
            :editable="false"
            :picker-options="{
              start: '08:00',
              step: '01:00',
              end: '18:00',
              format: 'HH'
            }"
            :placeholder="$t('system.pleaseSelectTheSendingTime')"
            value-format="HH"
          />
        </el-form-item>
        <el-form-item :label="$t('system.reminderType')" prop="remindType">
          <el-select v-model="form.remindType" :placeholder="$t('system.pleaseSelectAReminderType')" clearable>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_MAIL_REMIND_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.scheduledDays')" prop="remindTypeDays">
          <el-input
            v-model="form.remindTypeDays"
            :placeholder="$t('system.pleaseEnterTheReminderTypeDaysTheDefaultIs')"
          />
        </el-form-item>
        <el-form-item :label="$t('common.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :label="parseInt(dict.value)"
              >{{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createMailRemindConfig,
  deleteMailRemindConfig,
  getMailRemindConfig,
  getMailRemindConfigPage,
  updateMailRemindConfig
} from '@/api/system/mailRemindConfig'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'

export default {
  name: 'Mailremindconfig',
  components: {},
  data() {
    const typeValidate = (rule, value, callback) => {
      switch (this.form.frequencyType) {
        case 'once_every_x_hours':
          if (value !== '' && (value < 1 || value > 23)) {
            callback(new Error(this.$t('system.pleaseEnterANumberFromTo')))
          } else {
            callback()
          }
          break
        case 'once_every_x_months':
          if (value !== '' && (value < 1 || value > 12)) {
            callback(new Error(this.$t('system.pleaseEnterTheNumber')))
          } else {
            callback()
          }
          break
        case 'once_every_week_x':
          if (value !== '' && (value < 1 || value > 7)) {
            callback(new Error(this.$t('system.pleaseEnterANumberFromTo')))
          } else {
            callback()
          }
          break
        default:
          callback()
          break
      }
    }
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 邮件提醒配置列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeSendTime: [],
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        status: null,
        businessName: null,
        frequencyType: null,
        frequencyInterval: null,
        remindType: null,
        remindTypeDays: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        businessName: [{ required: true, message: this.$t('system.businessNameCannotBeEmpty'), trigger: 'blur' }],
        frequencyType: [{ required: true, message: this.$t('system.frequencyTypeCannotBeEmpty'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }],
        frequencyInterval: [{ validator: typeValidate, trigger: 'blur' }]
      },
      // 数据字典 开启/关闭
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS),
      // 数据字典 邮件提醒业务名称
      remindNameDictDatas: getDictDatas(DICT_TYPE.SYSTEM_MAIL_REMIND_NAME),
      // 数据字典 频率类型
      frequencyTypeDictDatas: getDictDatas(DICT_TYPE.SYSTEM_MAIL_REMIND_FREQUENCY_TYPE),
      // 数据字典 提醒类型
      remindTypeDictDatas: getDictDatas(DICT_TYPE.SYSTEM_MAIL_REMIND_TYPE)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeSendTime, 'sendTime')
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getMailRemindConfigPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        status: undefined,
        businessName: undefined,
        frequencyType: undefined,
        frequencyInterval: undefined,
        sendTime: undefined,
        remindType: undefined,
        remindTypeDays: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeSendTime = []
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addEmailReminderConfiguration')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getMailRemindConfig(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyEmailReminderConfiguration')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateMailRemindConfig(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createMailRemindConfig(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm(this.$t('system.areYouSureToDeleteTheEmailReminderConfigurationNumberAs') + id + this.$t('material.dataItemOf')).then(function() {
        return deleteMailRemindConfig(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    // 转换业务名称
    convertBusinessName(row) {
      return this.remindNameDictDatas.filter(typeItem => typeItem.value === row.businessName)[0]?.label
    },
    // 转换频率类型
    convertFrequencyType(row) {
      return this.frequencyTypeDictDatas.filter(typeItem => typeItem.value === row.frequencyType)[0]?.label
    },
    // 转换提醒类型
    convertRemindType(row) {
      if (row.remindType != null && row.remindType !== '') {
        return this.remindTypeDictDatas.filter(typeItem => typeItem.value === row.remindType)[0]?.label
      }
    }
  }
}
</script>
