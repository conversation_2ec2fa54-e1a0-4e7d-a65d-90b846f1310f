<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.moduleName')" prop="name">
        <el-input v-model="queryParams.name" clearable :placeholder="$t('system.pleaseEnterTheModuleName')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.moduleCode')" prop="code">
        <el-input v-model="queryParams.code" clearable :placeholder="$t('system.pleaseEnterTheModuleCode')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:module:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        align="center"
        :label="$t('system.moduleName')"
        prop="name"
      />
      <el-table-column
        align="center"
        :label="$t('system.moduleCode')"
        prop="code"
      />
      <el-table-column
        align="center"
        :label="$t('order.edition')"
        prop="version"
      />
      <el-table-column
        align="center"
        :label="$t('system.moduleDescription')"
        prop="description"
      />
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        :label="$t('common.operate')"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:module:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:module-permission:assign-module-permission']"
            icon="el-icon-circle-check"
            size="mini"
            type="text"
            @click="handleDataScope(scope.row)"
          >{{ $t('system.dataPermissionRules') }}
          </el-button>
          <el-button
            v-hasPermi="['system:module:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item :label="$t('system.moduleCode')" prop="code">
          <el-input v-model="form.code" :placeholder="$t('system.pleaseEnterTheModuleCode')" maxlength="50" :disabled="form.id " />
        </el-form-item>
        <el-form-item :label="$t('system.moduleName')" prop="name">
          <el-input v-model="form.name" :placeholder="$t('system.pleaseEnterTheModuleName')" maxlength="50" />
        </el-form-item>
        <el-form-item :label="$t('system.moduleDescription')">
          <el-input v-model="form.description" :placeholder="$t('system.pleaseEnterTheModuleDescription')" maxlength="255" />
        </el-form-item>
        <el-form-item :label="$t('order.edition')" prop="version">
          <el-input v-model="form.version" :placeholder="$t('system.pleaseEnterTheVersion')" maxlength="10" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!-- 分配数据权限规则 -->
    <el-dialog :title="$t('system.assignDataPermissionRules')" :visible.sync="openPermission" width="500px" append-to-body>
      <el-form ref="formPermission" :model="form" label-width="80px">
        <el-form-item :label="$t('system.moduleName')">
          <el-input v-model="form.name" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('system.moduleCode')">
          <el-input v-model="form.moduleCode" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('system.dataPermission')">
          <el-select v-model="form.permissionRuleIds" multiple :placeholder="$t('common.pleaseSelect')">
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.SYSTEM_DATA_SCOPE,0)"
              :key="parseInt(item.value)"
              :label="item.label"
              :value="parseInt(item.value)"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelPermission">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitPermission">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  assignModulePermission,
  createModule,
  deleteModule,
  exportModuleExcel,
  getModule,
  getModulePage, getModulePermission,
  updateModule
} from '@/api/system/module'

export default {
  name: 'Module',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 系统模块列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      openPermission: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        nameLocalId: null,
        version: null,
        description: null,
        code: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: this.$t('system.moduleNameCannotBeEmpty'), trigger: 'blur' }],
        nameLocalId: [{ required: true, message: this.$t('system.multiLanguageCannotBeEmpty'), trigger: 'blur' }],
        code: [{ required: true, message: this.$t('system.moduleCodeCannotBeEmpty'), trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 分配数据权限规则操作 */
    handleDataScope(row) {
      this.reset()
      // 处理了 form 的角色 name 和 code 的展示
      this.form.name = row.name
      this.form.moduleCode = row.code
      // 打开弹窗
      this.openPermission = true
      // 获得已经选择的数据权限规则
      getModulePermission(row.code).then(response => {
        // 设置选中
        this.form.permissionRuleIds = response.data
      })
    },
    /** 提交按钮 */
    submitPermission: function() {
      this.$refs['formPermission'].validate(valid => {
        if (valid) {
          assignModulePermission(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.openPermission = false
            this.getList()
          })
        }
      })
    },
    /** 取消按钮 */
    cancelPermission() {
      this.openPermission = false
      this.reset()
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getModulePage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        nameLocalId: undefined,
        version: undefined,
        description: undefined,
        code: undefined,
        moduleCode: undefined,
        permissionRuleIds: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addSystemModule')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getModule(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifySystemModule')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateModule(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createModule(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除系统模块编号为"' + id + '"的数据项?').then(function() {
        return deleteModule(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行导出
      this.$modal.confirm(this.$t('system.areYouSureToExportAllSystemModuleDataItems')).then(() => {
        this.exportLoading = true
        return exportModuleExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('system.systemModulexls'))
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>
