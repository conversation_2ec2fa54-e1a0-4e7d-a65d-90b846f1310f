<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item :label="$t('system.configurationItem')" prop="configType">
        <el-select v-model="queryParams.configType" :placeholder="$t('system.pleaseSelectAConfigurationItem')" clearable size="small">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_MODULE_REPORT_REL_CONFIG)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          v-has-permi="['order:config:query']"
          type="primary"
          icon="el-icon-search"
          @click="handleQuery"
        >{{ $t('common.search') }}</el-button>
        <el-button
          v-has-permi="['order:config:query']"
          icon="el-icon-refresh"
          @click="resetQuery"
        >{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">

      <el-table-column :label="$t('system.configurationItem')" align="center" prop="label" />

      <el-table-column :label="$t('system.valueOfConfigurationItem')" align="center" prop="value">
        <template slot-scope="scope">
          {{ showReportName(scope.row.value) }}
        </template>
      </el-table-column>

      <el-table-column :label="$t('supplier.updateTime')" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['order:config:update']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item :label="$t('system.configurationItem')" prop="configType">
          <el-select v-model="form.configType" disabled>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_MODULE_REPORT_REL_CONFIG)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.valueOfConfigurationItem')" prop="value">
          <el-select v-model="form.value" filterable :placeholder="$t('order.pleaseSelectAReport')" clearable size="small">
            <el-option v-for="(report, i) in reportList" :key="i" :label="report.name" :value="report.id + ''" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-has-permi="['order:config:update']"
          @click="cancel"
        >{{ $t('common.cancel') }}</el-button>
        <el-button
          v-has-permi="['order:config:update']"
          type="primary"
          @click="submitForm"
        >{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { updateConfig, getConfigPage } from '@/api/system/config'
import { getReportNames } from '@/api/visualization/report'

export default {
  name: 'Config',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单通用配置列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        configType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        configType: [{ required: true, message: this.$t('system.configurationItemCannotBeEmpty'), trigger: 'change' }]
      },
      // 报表列表
      reportList: []
    }
  },
  created() {
    this.getList()
    this.getReportNames()
  },
  methods: {
    // 根据报表的id展示报表名称
    showReportName(reportIdStr) {
      if (reportIdStr) {
        return this.reportList.filter(i => i.id === parseInt(reportIdStr))?.[0].name
      }
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getConfigPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        configType: undefined,
        value: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 获取报表列表
    getReportNames() {
      getReportNames({ template: false }).then(res => {
        if (res.data.length > 0) {
          res.data.map(j => {
            j.name = j.name.replace('.ureport.xml', '')
            return j
          })
        }
        this.reportList = res.data
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.form.id = row.id
      this.form.configType = row.configType
      this.form.value = row.value
      // 有值的时候进行类型转换
      if (this.form.value) {
        // 送货单报表模板的时候，值是整数
        if (this.form.configType === 'delivery_note_template') {
          this.form.value = parseInt(this.form.value)
        }
      }
      this.open = true
      this.title = this.$t('system.modifyOrderGeneralConfiguration')
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        updateConfig(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          this.open = false
          this.getList()
        })
      })
    }
  }
}
</script>
