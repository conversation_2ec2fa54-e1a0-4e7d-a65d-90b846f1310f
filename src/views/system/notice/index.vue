<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true">
      <el-form-item :label="$t('system.title')" prop="title">
        <el-input v-model="queryParams.title" :placeholder="$t('system.title')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.type')" prop="type">
        <el-select v-model="queryParams.type" :placeholder="$t('system.type')" clearable>
          <el-option v-for="dict in noticeTypeDictDatas" :key="parseInt(dict.value)" :label="dict.label" :value="parseInt(dict.value)" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="$store.getters.isExternal!==2" :label="$t('system.isTheScopeOfDisclosureOpenToThePublic')" prop="scope">
        <el-select v-model="queryParams.scope" :placeholder="$t('common.pleaseSelect')" clearable>
          <el-option v-for="dict in yesOrNo" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:notice:create']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          s
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="noticeList">
      <!--      <el-table-column :label="$t('rfq.serialNo')"  prop="id" width="100" />-->
      <el-table-column :label="$t('system.title')" prop="title" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-button
            style="text-decoration: underline"
            type="text"
            @click="showDetail(scope.row)"
          >{{ scope.row.title }}</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.type')" prop="type">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_NOTICE_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.isTheScopeOfDisclosureOpenToThePublic')" prop="scope">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_Y_N" :value="scope.row.scope" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.status')" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.creator')">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="scope.row.creator" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column key="operate" :label="$t('common.operate')" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <noticeFile
            v-if="scope.row.fileList.length"
            :origin-num="scope.row.fileList.length"
            :disabled="true"
            :business-type="'notice'"
            style="display: inline-block"
            @getFile="handleFile(scope.row)"
          />
          <el-button
            v-hasPermi="['system:notice:update']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['system:notice:delete']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="noticeForm" :model="noticeForm" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('system.title')" prop="title">
              <el-input v-model="noticeForm.title" show-word-limit maxlength="50" :placeholder="$t('system.title')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.type')" prop="type">
              <el-select v-model="noticeForm.type" :placeholder="$t('common.pleaseSelect')">
                <el-option
                  v-for="dict in noticeTypeDictDatas"
                  :key="parseInt(dict.value)"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.isTheScopeOfDisclosureOpenToThePublic')" prop="scope">
              <el-radio-group v-model="noticeForm.scope">
                <el-radio v-for="dict in yesOrNo" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('common.status')">
              <el-radio-group v-model="noticeForm.status">
                <el-radio
                  v-for="dict in statusDictDatas"
                  :key="parseInt(dict.value)"
                  :label="parseInt(dict.value)"
                >{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件">
              <noticeFile
                :business-type="'notice'"
              />
            </el-form-item>
            <el-form-item :label="$t('system.content')">
              <editor v-model="noticeForm.content" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
    <el-dialog
      v-if="detailVisible"
      :title="noticeForm.title"
      width="1200px"
      :visible.sync="detailVisible"
    >
      <div class="ql-editor" v-html="noticeForm.content" />
      <el-descriptions v-if="noticeForm.fileList.length" style="padding-left: 12px" :column="1" :colon="false" label-class-name="labelTitle">
        <el-descriptions-item
          :label="$t('附件')"
          :label-style="{'font-weight':'bold'}"
          :content-style="{'display':'flex','flex-wrap':'wrap','align-items':'flex-start'}"
        >
          <div v-for="item in noticeForm.fileList" style="width: 100%;align-self: flex-start;">
            <el-button style="text-decoration: underline" type="text" @click="openFile(item.fileUrl)">{{
              item.fileName
            }}
            </el-button>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { listNotice, getNotice, delNotice, addNotice, updateNotice } from '@/api/system/notice'
import Editor from '@/components/Editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'

import { CommonStatusEnum } from '@/utils/constants'
import { getDictDatas, DICT_TYPE } from '@/utils/dict'

export default {
  name: 'Notice',
  components: {
    Editor,
    noticeFile: () => import('./componet/upload.vue')
  },
  provide() {
    return {
      noticeInfo: () => this.noticeForm
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        scope: '',
        title: undefined,
        createBy: undefined,
        status: undefined
      },
      // 表单参数
      noticeForm: {
        title: '',
        type: null,
        content: '',
        status: null,
        scope: '',
        fileList: []
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: this.$t('system.theTitleCannotBeEmpty'), trigger: 'blur' }
        ],
        type: [
          { required: true, message: this.$t('system.typeCannotBeEmpty'), trigger: 'change' }
        ],
        scope: [{
          required: true,
          message: this.$t('system.theRangeCannotBeEmpty'), trigger: 'blur' }
        ]
      },

      // 枚举
      CommonStatusEnum: CommonStatusEnum,
      // 数据字典
      noticeTypeDictDatas: getDictDatas(DICT_TYPE.SYSTEM_NOTICE_TYPE),
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS),
      // 数据字典
      scopeDictDatas: getDictDatas(DICT_TYPE.SYSTEM_ROLE_IS_EXTERNAL),
      // 是否
      yesOrNo: getDictDatas(DICT_TYPE.COMMON_Y_N).reverse(),
      detailVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    openFile(url) {
      window.open(url)
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true
      listNotice(this.queryParams).then(response => {
        this.noticeList = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.noticeForm = {
        id: undefined,
        title: '',
        type: null,
        content: '',
        status: CommonStatusEnum.ENABLE,
        fileList: []
      }
      this.resetForm('noticeForm')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addAnnouncement')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getNotice({
        id: id
      }).then(response => {
        this.noticeForm = response.data
        this.open = true
        this.title = this.$t('system.modifyAnnouncement')
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['noticeForm'].validate(valid => {
        if (valid) {
          if (this.noticeForm.id !== undefined) {
            updateNotice({ ...this.noticeForm,
              scope: this.noticeForm.scope }).then(response => {
              this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
              this.open = false
              this.getList()
            })
          } else {
            addNotice({ ...this.noticeForm,
              scope: this.noticeForm.scope }).then(response => {
              this.$modal.msgSuccess(this.$t('common.addSuccess'))
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      const title = row.title || this.title
      this.$modal.confirm('是否确认删除公告标题为"' + title + '"的数据项?').then(function() {
        return delNotice(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {})
    },
    showDetail(row) {
      this.detailVisible = true
      this.noticeForm = row
    },
    handleFile(row) {
      this.noticeForm = row
    }
  }
}
</script>
