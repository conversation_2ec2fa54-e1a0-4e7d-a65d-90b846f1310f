<template>
  <div class="notice">
    <common-card
      :title="$t('system.announcement')"
    >
      <div class="notice-body">
        <p v-html="noticeInfo.content" />
        <el-upload
          ref="upload"
          v-has-permi="['system:notice:query']"
          :headers="headers"
          class="upload-container"
          :file-list="noticeInfo.fileList"
          :action="url"
          :disabled="true"
        />
      </div>
    </common-card>

  </div>
</template>

<script>
import { getAccessToken } from '@/utils/auth'
import { getNotice, openNoticeFile } from '@/api/system/notice'

export default {
  name: 'Info',
  props: ['noticeId'],
  data() {
    return {
      noticeInfo: {
        title: '',
        type: null,
        content: '',
        status: null,
        fileList: []
      },
      url: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      data: {},
      fileList: [],
      hasRead: true
    }
  },
  computed: {
    hasUpload() {
      return this.noticeInfo.fileList.length !== 0
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      getNotice({
        id: this.noticeId
      }).then(res => {
        res.data.fileList.forEach(a => {
          a.name = a.fileName
          a.url = a.filePath
        })
        this.noticeInfo = res.data
      })
    },
    closeWindow() {
      window.close()
    },
    readFile() {
      openNoticeFile({
        id: this.noticeId
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-upload--text{
  text-align: left;
}
.notice{
  &-body{
    padding: 0 70px;
  }
}
</style>

