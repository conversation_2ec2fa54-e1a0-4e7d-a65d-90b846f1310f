<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.userNo')" prop="userId">
        <el-select v-model="queryParams.userId" clearable :placeholder="$t('system.pleaseSelectAUser')" size="small" filterable @keyup.enter.native="handleQuery">
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.COMMON_USERS)"
            :key="item.value"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.templateCode')" prop="templateCode">
        <el-input v-model="queryParams.templateCode" clearable :placeholder="$t('system.pleaseEnterTheTemplateCode')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.templateType')" prop="templateType">
        <el-select v-model="queryParams.templateType" clearable :placeholder="$t('system.pleaseSelectTheTemplateType')" size="small">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.createTime')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="['00:00:00', '23:59:59']"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column align="left" :label="$t('order.number')" prop="id" />
      <el-table-column align="left" :label="$t('system.userNo')" prop="userId">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="scope.row.userId" />
        </template>
      </el-table-column>
      <el-table-column align="left" :label="$t('system.templateCode')" prop="templateCode" />
      <el-table-column align="left" :label="$t('system.nameOfSender')" prop="templateNickname" />
      <el-table-column :show-overflow-tooltip="true" align="left" :label="$t('system.templateTitle')" prop="title" />
      <el-table-column align="left" :label="$t('system.templateType')" prop="templateType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE" :value="scope.row.templateType" />
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('system.hasItBeenRead')" prop="readStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.readStatus" />
        </template>
      </el-table-column>
      <el-table-column align="left" :label="$t('system.readingTime')" prop="readTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.readTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="left" :label="$t('common.createTime')" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" :label="$t('common.operate')">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:notify-message:query']"
            icon="el-icon-view"
            size="mini"
            type="text"
            @click="handleView(scope.row)"
          >{{ $t('system.detailed') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 站内信详细-->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" append-to-body width="700px">
      <el-form ref="form" :model="form" label-width="160px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('system.logPrimaryKey')">{{ form.id }}</el-form-item>
            <el-form-item :label="$t('system.sendingTime')">{{ parseTime(form.createTime) }}</el-form-item>
            <el-form-item :label="$t('system.userNo')"> <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="form.userId" /></el-form-item>
            <el-form-item :label="$t('system.templateNo')">{{ form.templateId }}</el-form-item>
            <el-form-item :label="$t('system.templateCode')">{{ form.templateCode }}</el-form-item>
            <el-form-item :label="$t('system.templateTitle')">{{ form.title }}</el-form-item>
            <el-form-item :label="$t('system.templateType')">
              <dict-tag :type="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE" :value="form.templateType" />
            </el-form-item>
            <el-form-item :label="$t('system.templateSenderName')">{{ form.templateNickname }}</el-form-item>
            <el-form-item :label="$t('system.templateContent')">
              <editor v-model="form.templateContent" :min-height="192" read-only />
            </el-form-item>
            <el-form-item :label="$t('system.templateParameters')">{{ form.templateParams }}</el-form-item>
            <el-form-item :label="$t('system.titleParameters')">{{ form.templateTitleParams }}</el-form-item>
            <el-form-item v-if="files" :label="$t('rfq.enclosure')">
              <el-upload
                ref="upload"
                :headers="headers"
                class="upload-container"
                :file-list="files"
                :on-preview="onPreview"
                :disabled="true"
                multiple
              />
            </el-form-item>
            <el-form-item :label="$t('system.hasItBeenRead')">
              <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="form.readStatus" />
            </el-form-item>
            <el-form-item :label="$t('system.readingTime')">{{ parseTime(form.readTime) }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">{{ $t('order.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getNotifyMessagePage } from '@/api/system/notify/message'
import Editor from '@/components/Editor/index.vue'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getFileDetailByIds } from '@/api/infra/file'
import { getAccessToken } from '@/utils/auth'
import { parseTime } from '../../../../utils/ruoyi'

export default {
  name: 'Notifymessage',
  components: { Editor },
  data() {
    return {
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 站内信消息列表
      list: [],
      // 弹出层标题
      title: this.$t('system.detailedInformationWithinTheStation'),
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        userId: null,
        userType: null,
        templateCode: null,
        templateType: null,
        createTime: []
      },
      files: null,
      // 表单参数
      form: {}
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  created() {
    this.getList()
  },
  methods: {
    parseTime,
    getDictDatas,
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getNotifyMessagePage(this.queryParams).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    onPreview(file) {
      if (file.url) {
        window.open(file.url)
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true
      this.files = null
      if (row.fileIds) {
        const obj = {
          fileIds: row.fileIds.join(',')
        }
        getFileDetailByIds(obj).then(res => {
          if (res.data) {
            this.files = res.data
          }
        })
      }
      this.form = row
    }
  }
}
</script>
