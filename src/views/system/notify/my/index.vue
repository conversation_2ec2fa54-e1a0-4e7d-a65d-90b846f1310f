<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.title')" prop="title">
        <el-input v-model="queryParams.title" clearable :placeholder="$t('system.pleaseEnterATitle')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.hasItBeenRead')" prop="readStatus">
        <el-select v-model="queryParams.readStatus" clearable :placeholder="$t('supplier.pleaseSelectStatus')">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.sendingTime')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="['00:00:00', '23:59:59']"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="el-icon-plus" plain size="mini" type="primary" @click="handleUpdateList">{{ $t('system.markAsRead') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="el-icon-plus" plain size="mini" type="primary" @click="handleUpdateAll">{{ $t('system.allRead') }}</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table ref="tables" v-loading="loading" :data="list">
      <el-table-column type="selection" width="30" />
      <el-table-column align="left" :label="$t('system.sender')" prop="templateNickname" width="120" />
      <el-table-column align="left" :label="$t('system.sendingTime')" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" align="center" :label="$t('system.templateTitle')" prop="title" />
      <el-table-column align="left" :label="$t('system.templateType')" prop="templateType" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE" :value="scope.row.templateType" />
        </template>
      </el-table-column>
      <el-table-column align="left" :label="$t('system.hasItBeenRead')" prop="readStatus" width="80">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.readStatus" />
        </template>
      </el-table-column>
      <el-table-column align="left" :label="$t('system.readingTime')" prop="readTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.readTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" :label="$t('common.operate')" width="150">
        <template #default="scope">
          <el-button
            icon="el-icon-view"
            size="mini"
            type="text"
            @click="handleView(scope.row)"
          >{{ $t('system.detailed') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
    <!-- 站内信详细-->
    <el-dialog v-dialogDrag :title="title" :visible.sync="open" append-to-body width="900px">
      <el-form ref="form" :model="form" label-width="160px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('system.sendingTime')">{{ parseTime(form.createTime) }}</el-form-item>
            <el-form-item :label="$t('system.templateType')">
              <dict-tag :type="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE" :value="form.templateType" />
            </el-form-item>
            <el-form-item :label="$t('system.templateSenderName')">{{ form.templateNickname }}</el-form-item>
            <el-form-item :label="$t('system.templateContent')">
              <editor v-model="form.templateContent" :min-height="192" read-only />
            </el-form-item>
            <el-form-item v-if="files" :label="$t('rfq.enclosure')">
              <el-upload
                ref="upload"
                class="upload-container"
                :file-list="files"
                :on-preview="onPreview"
                :disabled="true"
                multiple
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">{{ $t('order.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMyNotifyMessagePage, updateAllNotifyMessageRead, updateNotifyMessageRead } from '@/api/system/notify/message'
import { getFileDetailByIds } from '@/api/infra/file'
import Editor from '@/components/Editor/index.vue'
import { DICT_TYPE } from '@/utils/dict'
import { parseTime } from '../../../../utils/ruoyi'

export default {
  name: 'Mynotify',
  components: { Editor },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 是否显示弹出层
      open: false,
      files: null,
      // 弹出层标题
      title: '站内信详细',
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表单参数
      form: {},
      // 我的站内信列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        title: null,
        readStatus: null,
        createTime: []
      }
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  created() {
    this.getList()
  },
  methods: {
    parseTime,
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getMyNotifyMessagePage(this.queryParams).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    handleUpdateList() {
      const list = this.$refs['tables'].selection
      if (list.length === 0) {
        return
      }
      this.handleUpdate(list.map(v => v.id), true)
    },
    handleUpdate(ids, reload) {
      updateNotifyMessageRead(ids).then(response => {
        if (reload) {
          this.$modal.msgSuccess(this.$t('system.markReadSuccessfully'))
          this.getList()
        }
      })
    },
    handleUpdateAll() {
      updateAllNotifyMessageRead().then(response => {
        this.$modal.msgSuccess(this.$t('system.allReadSuccessfully'))
        this.getList()
      })
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true
      this.files = null
      this.title = row.title
      this.handleUpdate([row.id], false)
      if (row.fileIds) {
        const obj = {
          fileIds: row.fileIds.join(',')
        }
        getFileDetailByIds(obj).then(res => {
          if (res.data) {
            this.files = res.data
          }
        })
      }
      this.form = row
    },
    onPreview(file) {
      if (file.url) {
        window.open(file.url)
      }
    }
  }
}
</script>
