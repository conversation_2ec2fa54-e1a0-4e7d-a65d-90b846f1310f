<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('rfq.templateName')" prop="name">
        <el-input v-model="queryParams.name" clearable :placeholder="$t('system.pleaseEnterATemplateName')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.templateEncoding')" prop="code">
        <el-input v-model="queryParams.code" clearable :placeholder="$t('system.pleaseEnterTheTemplateCode')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" clearable :placeholder="$t('supplier.pleaseSelectStatus')" size="small">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.createTime')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="['00:00:00', '23:59:59']"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:notify-template:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" :label="$t('system.templateCode')" prop="code" />
      <el-table-column align="center" :label="$t('rfq.templateName')" prop="name" />
      <el-table-column align="center" :label="$t('system.type')" prop="type">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('system.nameOfSender')" prop="nickname" />
      <el-table-column :label="$t('system.templateLanguage')" align="center" prop="locale">
        <template slot-scope="scope">
          {{ translationLanguages.find(item => item.locale === scope.row.locale).name }}
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('system.templateTitle')" prop="title" />
      <el-table-column :show-overflow-tooltip="true" align="center" :label="$t('system.templateContent')" prop="content" />
      <el-table-column align="center" :label="$t('system.openState')" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('common.remarks')" prop="remark" />
      <el-table-column align="center" :label="$t('common.createTime')" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" :label="$t('common.operate')" width="150">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:notify-template:send-notify']"
            icon="el-icon-share"
            size="mini"
            type="text"
            @click="handleSendNotify(scope.row)"
          >{{ $t('system.test') }}
          </el-button>
          <el-button
            v-hasPermi="['system:notify-template:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:notify-template:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="700px">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item :label="$t('system.templateEncoding')" prop="code">
          <el-input v-model="form.code" :placeholder="$t('system.pleaseEnterTheTemplateCode')" />
        </el-form-item>
        <el-form-item :label="$t('rfq.templateName')" prop="name">
          <el-input v-model="form.name" :placeholder="$t('system.pleaseEnterTheTemplateName')" />
        </el-form-item>
        <el-form-item :label="$t('system.sendersName')" prop="nickname">
          <el-input v-model="form.nickname" :placeholder="$t('system.pleaseEnterTheSendersName')" />
        </el-form-item>
        <el-form-item :label="$t('system.templateLanguage')" prop="locale">
          <el-select v-model="form.locale" :placeholder="$t('system.pleaseSelectATemplateLanguage')">
            <el-option
              v-for="item in translationLanguages"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="item.locale"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.templateTitle')" prop="title">
          <el-input v-model="form.title" :placeholder="$t('system.pleaseEnterTheTemplateTitle')" />
        </el-form-item>
        <el-form-item :label="$t('system.templateContent')">
          <editor v-model="form.content" :min-height="192" />
        </el-form-item>
        <el-form-item :label="$t('system.type')" prop="type">
          <el-select v-model="form.type" :placeholder="$t('system.pleaseSelectTheType')">
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.openState')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('common.remarks')" prop="remark">
          <el-input v-model="form.remark" :placeholder="$t('rfq.pleaseEnterComments')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 对话框(发送站内信) -->
    <el-dialog :visible.sync="sendNotifyOpen" append-to-body :title="$t('system.sendInternalMessages')" width="500px">
      <el-form ref="sendNotifyForm" :model="sendNotifyForm" :rules="sendNotifyRules" label-width="140px">
        <el-form-item :label="$t('system.templateContent')" prop="content">
          <el-input v-model="sendNotifyForm.content" :placeholder="$t('system.pleaseEnterTheTemplateContent')" readonly type="textarea" />
        </el-form-item>

        <el-form-item :label="$t('system.recipient')" prop="userId">
          <el-select v-model="sendNotifyForm.userId" clearable :placeholder="$t('system.pleaseEnterTheRecipient')" style="width: 100%">
            <el-option
              v-for="item in users"
              :key="parseInt(item.id)"
              :label="item.nickname"
              :value="parseInt(item.id)"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-for="param in sendNotifyForm.params"
          :key="param"
          :label="'参数 {' + param + '}'"
          :prop="'templateParams.' + param"
        >
          <el-input v-model="sendNotifyForm.templateParams[param]" :placeholder="'请输入 ' + param + ' 参数'" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitSendNotifyForm">{{ $t('order.determine') }}</el-button>
        <el-button @click="cancelSendNotify">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  createNotifyTemplate,
  deleteNotifyTemplate,
  getNotifyTemplate,
  getNotifyTemplatePage,
  sendNotify,
  updateNotifyTemplate
} from '@/api/system/notify/template'
import { listSimpleUsers } from '@/api/system/user'
import { CommonStatusEnum } from '@/utils/constants'
import Editor from '@/components/Editor/index.vue'
import { listTranslationLanguages } from '@/api/system/translationLanguage'

export default {
  name: 'Notifytemplate',
  components: { Editor },
  data() {
    return {
      // translationLanguage下拉列表
      translationLanguages: [],
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 短信模板列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        status: null,
        code: null,
        title: null,
        createTime: []
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: this.$t('system.templateNameCannotBeEmpty'), trigger: 'blur' }],
        code: [{ required: true, message: this.$t('system.templateCodeCannotBeEmpty'), trigger: 'blur' }],
        nickname: [{ required: true, message: this.$t('system.theSenderNameCannotBeEmpty'), trigger: 'blur' }],
        locale: [{ required: true, message: this.$t('system.languageEncodingCannotBeEmpty'), trigger: 'blur' }],
        title: [{ required: true, message: this.$t('system.theTitleCannotBeEmpty'), trigger: 'blur' }],
        content: [{ required: true, message: this.$t('system.theTemplateContentCannotBeEmpty'), trigger: 'blur' }],
        type: [{ required: true, message: this.$t('system.typeCannotBeEmpty'), trigger: 'change' }],
        status: [{ required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }]
      },
      // 用户列表
      users: [],
      // 发送短信
      sendNotifyOpen: false,
      sendNotifyForm: {
        params: [] // 模板的参数列表
      },
      sendNotifyRules: {
        userId: [{ required: true, message: this.$t('system.theRecipientCannotBeEmpty'), trigger: 'blur' }],
        templateCode: [{ required: true, message: this.$t('system.templateCodeCannotBeEmpty'), trigger: 'blur' }],
        templateParams: {}
      }
    }
  },
  created() {
    this.getList()
    listTranslationLanguages().then(response => {
      this.translationLanguages = response.data
    })
    // 获得用户列表
    listSimpleUsers().then(response => {
      this.users = response.data
    })
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getNotifyTemplatePage(this.queryParams).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        code: undefined,
        nickname: undefined,
        content: undefined,
        type: undefined,
        params: undefined,
        status: CommonStatusEnum.ENABLE,
        remark: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addAnInternalMessageTemplate')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getNotifyTemplate(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyTheOnsiteMessageTemplate')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateNotifyTemplate(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createNotifyTemplate(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除站内信模板编号为"' + id + '"的数据项?').then(function() {
        return deleteNotifyTemplate(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 发送站内信按钮 */
    handleSendNotify(row) {
      this.resetSendNotify(row)
      // 设置参数
      this.sendNotifyForm.content = row.content
      this.sendNotifyForm.params = row.params
      this.sendNotifyForm.templateCode = row.code
      this.sendNotifyForm.locale = row.locale
      this.sendNotifyForm.templateParams = row.params.reduce(function(obj, item) {
        obj[item] = undefined
        return obj
      }, {})
      // 根据 row 重置 rules
      this.sendNotifyRules.templateParams = row.params.reduce(function(obj, item) {
        obj[item] = { required: true, message: '参数 ' + item + ' 不能为空', trigger: 'change' }
        return obj
      }, {})
      // 设置打开
      this.sendNotifyOpen = true
    },
    /** 重置发送站内信的表单 */
    resetSendNotify() {
      // 根据 row 重置表单
      this.sendNotifyForm = {
        content: undefined,
        params: undefined,
        userId: undefined,
        locale: undefined,
        templateCode: undefined,
        templateParams: {}
      }
      this.resetForm('sendNotifyForm')
    },
    /** 取消发送站内信 */
    cancelSendNotify() {
      this.sendNotifyOpen = false
      this.resetSendNotify()
    },
    /** 提交按钮 */
    submitSendNotifyForm() {
      this.$refs['sendNotifyForm'].validate(valid => {
        if (!valid) {
          return
        }
        // 添加的提交
        sendNotify(this.sendNotifyForm).then(response => {
          this.$modal.msgSuccess(this.$t('system.submitAndSendSuccessfullySeeSendingLogNoForSendingResults') + response.data)
          this.sendNotifyOpen = false
        })
      })
    }
  }
}
</script>
