<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.appName')" prop="name">
        <el-input
          v-model="queryParams.name"
          :placeholder="$t('system.pleaseEnterTheApplicationName')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('supplier.pleaseSelectStatus')" clearable size="small">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:oauth2-client:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('system.clientNo')" align="center" prop="clientId" />
      <el-table-column :label="$t('system.clientKey')" align="center" prop="secret" />
      <el-table-column :label="$t('system.appName')" align="center" prop="name" />
      <el-table-column :label="$t('system.applicationIcon')" align="center" prop="logo">
        <template slot-scope="scope">
          <img :src="scope.row.logo" height="40px" width="40px">
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('system.theValidityPeriodOfTheAccessToken')"
        align="center"
        prop="accessTokenValiditySeconds"
      >
        <template slot-scope="scope">{{ scope.row.accessTokenValiditySeconds }} 秒</template>
      </el-table-column>
      <el-table-column
        :label="$t('system.theValidityPeriodOfTheRefreshToken')"
        align="center"
        prop="refreshTokenValiditySeconds"
      >
        <template slot-scope="scope">{{ scope.row.refreshTokenValiditySeconds }} 秒</template>
      </el-table-column>
      <el-table-column :label="$t('system.authorizationType')" align="center" prop="authorizedGrantTypes">
        <template slot-scope="scope">
          <el-tag
            v-for="(authorizedGrantType, index) in scope.row.authorizedGrantTypes"
            :key="index"
            :disable-transitions="true"
            :index="index"
          >
            {{ authorizedGrantType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:oauth2-client:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >修改
          </el-button>
          <el-button
            v-hasPermi="['system:oauth2-client:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="700px">
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <el-form-item :label="$t('system.clientNo')" prop="secret">
          <el-input v-model="form.clientId" :placeholder="$t('system.pleaseEnterTheClientNumber')" />
        </el-form-item>
        <el-form-item :label="$t('system.clientKey')" prop="secret">
          <el-input v-model="form.secret" :placeholder="$t('system.pleaseEnterTheClientKey')" />
        </el-form-item>
        <el-form-item :label="$t('system.appName')" prop="name">
          <el-input v-model="form.name" :placeholder="$t('system.pleaseEnterTheApplicationName')" />
        </el-form-item>
        <el-form-item :label="$t('system.applicationIcon')">
          <imageUpload v-model="form.logo" :limit="1" />
        </el-form-item>
        <el-form-item :label="$t('system.applicationDescription')">
          <el-input
            v-model="form.description"
            :placeholder="$t('system.pleaseEnterTheApplicationName')"
            type="textarea"
          />
        </el-form-item>
        <el-form-item :label="$t('common.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('system.theValidityPeriodOfTheAccessToken')" prop="accessTokenValiditySeconds">
          <el-input-number v-model="form.accessTokenValiditySeconds" :placeholder="$t('system.unitSecond')" />
        </el-form-item>
        <el-form-item :label="$t('system.theValidityPeriodOfTheRefreshToken')" prop="refreshTokenValiditySeconds">
          <el-input-number v-model="form.refreshTokenValiditySeconds" :placeholder="$t('system.unitSecond')" />
        </el-form-item>
        <el-form-item :label="$t('system.authorizationType')" prop="authorizedGrantTypes">
          <el-select
            v-model="form.authorizedGrantTypes"
            :placeholder="$t('system.pleaseEnterTheAuthorizationType')"
            filterable
            multiple
            style="width: 500px"
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_OAUTH2_GRANT_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.scopeOfAuthorization')" prop="scopes">
          <el-select
            v-model="form.scopes"
            :placeholder="$t('system.pleaseEnterTheScopeOfAuthorization')"
            allow-create
            filterable
            multiple
            style="width: 500px"
          >
            <el-option v-for="scope in form.scopes" :key="scope" :label="scope" :value="scope" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.automaticAuthorizationScope')" prop="autoApproveScopes">
          <el-select
            v-model="form.autoApproveScopes"
            :placeholder="$t('system.pleaseEnterTheScopeOfAuthorization')"
            filterable
            multiple
            style="width: 500px"
          >
            <el-option v-for="scope in form.scopes" :key="scope" :label="scope" :value="scope" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.redirectableUriAddress')" prop="redirectUris">
          <el-select
            v-model="form.redirectUris"
            :placeholder="$t('system.pleaseEnterARedirectableUriAddress')"
            allow-create
            filterable
            multiple
            style="width: 500px"
          >
            <el-option
              v-for="redirectUri in form.redirectUris"
              :key="redirectUri"
              :label="redirectUri"
              :value="redirectUri"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.jurisdiction')" prop="authorities">
          <el-select
            v-model="form.authorities"
            :placeholder="$t('system.pleaseEnterPermission')"
            allow-create
            filterable
            multiple
            style="width: 500px"
          >
            <el-option v-for="authority in form.authorities" :key="authority" :label="authority" :value="authority" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.resources')" prop="resourceIds">
          <el-select
            v-model="form.resourceIds"
            :placeholder="$t('system.pleaseEnterResources')"
            allow-create
            filterable
            multiple
            style="width: 500px"
          >
            <el-option
              v-for="resourceId in form.resourceIds"
              :key="resourceId"
              :label="resourceId"
              :value="resourceId"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.additionalInformation')" prop="additionalInformation">
          <el-input
            v-model="form.additionalInformation"
            :placeholder="$t('system.pleaseEnterAdditionalInformationJsonFormatData')"
            type="textarea"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createOAuth2Client,
  deleteOAuth2Client,
  getOAuth2Client,
  getOAuth2ClientPage,
  updateOAuth2Client
} from '@/api/system/oauth2/oauth2Client'
import ImageUpload from '@/components/ImageUpload'
import Editor from '@/components/Editor'
import { CommonStatusEnum } from '@/utils/constants'
import FileUpload from '@/components/FileUpload'

export default {
  name: 'Oauth2client',
  components: {
    FileUpload,
    ImageUpload,
    Editor
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // OAuth2 客户端列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        clientId: [{ required: true, message: this.$t('system.theClientNumberCannotBeEmpty'), trigger: 'blur' }],
        secret: [{ required: true, message: this.$t('system.theClientKeyCannotBeEmpty'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('system.theApplicationNameCannotBeEmpty'), trigger: 'blur' }],
        logo: [{ required: true, message: this.$t('system.theApplicationIconCannotBeEmpty'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }],
        accessTokenValiditySeconds: [{
          required: true,
          message: this.$t('system.theValidityPeriodOfTheAccessTokenCannotBeEmpty'),
          trigger: 'blur'
        }],
        refreshTokenValiditySeconds: [{
          required: true,
          message: this.$t('system.theValidityPeriodOfTheRefreshTokenCannotBeEmpty'),
          trigger: 'blur'
        }],
        redirectUris: [{
          required: true,
          message: this.$t('system.redirectableUriAddressCannotBeEmpty'),
          trigger: 'blur'
        }],
        authorizedGrantTypes: [{
          required: true,
          message: this.$t('system.authorizationTypeCannotBeEmpty'),
          trigger: 'blur'
        }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      // 执行查询
      getOAuth2ClientPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        clientId: undefined,
        secret: undefined,
        name: undefined,
        logo: undefined,
        description: undefined,
        status: CommonStatusEnum.ENABLE,
        accessTokenValiditySeconds: 30 * 60,
        refreshTokenValiditySeconds: 30 * 24 * 60,
        redirectUris: [],
        authorizedGrantTypes: [],
        scopes: [],
        autoApproveScopes: [],
        authorities: [],
        resourceIds: [],
        additionalInformation: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addOauthClient')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getOAuth2Client(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyOauthClient')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateOAuth2Client(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createOAuth2Client(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除客户端编号为"' + row.clientId + '"的数据项?').then(function() {
        return deleteOAuth2Client(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    }
  }
}
</script>
