<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.userNo')" prop="userId">
        <el-input
          v-model="queryParams.userId"
          :placeholder="$t('system.pleaseEnterTheUserNumber')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.userName')" prop="userId">
        <el-input
          v-model="queryParams.userName"
          :placeholder="$t('system.pleaseEnterTheUserName')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.customerType')" prop="userType">
        <el-select v-model="queryParams.userType" :placeholder="$t('system.pleaseSelectAUserType')" clearable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.USER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>

    </el-form>
    <el-table v-loading="loading" :data="list" style="width: 100%;">
      <el-table-column :label="$t('system.accessToken')" align="center" prop="accessToken" width="300" />
      <el-table-column :label="$t('system.refreshToken')" align="center" prop="refreshToken" width="300" />
      <el-table-column :label="$t('system.userNo')" align="center" prop="userId" />
      <el-table-column :label="$t('system.userName')" align="center" prop="userName" />
      <el-table-column :label="$t('system.customerType')" align="center" prop="userType" width="100">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.USER_TYPE" :value="scope.row.userType" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.expirationTime')" align="center" prop="expiresTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expiresTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:oauth2-token:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleForceLogout(scope.row)"
          >{{ $t('system.forcedRetreat') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { deleteAccessToken, getAccessTokenPage } from '@/api/system/oauth2/oauth2Token'

export default {
  name: 'Oauth2token',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        userId: undefined,
        userType: undefined,
        userName: undefined
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询登录日志列表 */
    getList() {
      this.loading = true
      getAccessTokenPage(this.queryParams).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 强退按钮操作 */
    handleForceLogout(row) {
      this.$modal.confirm('是否确认强退令牌为"' + row.accessToken + '"的数据项?').then(function() {
        return deleteAccessToken(row.accessToken)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('system.forcedWithdrawalSucceeded'))
      }).catch(() => {
      })
    }
  }
}
</script>

