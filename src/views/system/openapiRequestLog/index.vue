<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item :label="$t('system.interfaceName')" prop="requestDesc">
        <el-input v-model="queryParams.requestDesc" :placeholder="$t('system.pleaseEnterTheInterfaceName')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.requestTime')">
        <el-date-picker
          v-model="dateRangeRequestTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
        />
      </el-form-item>
      <el-form-item :label="$t('system.responseTime')">
        <el-date-picker
          v-model="dateRangeRequestEndTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
        />
      </el-form-item>
      <el-form-item :label="$t('system.accurateQuery')" prop="keyColumn1">
        <el-input v-model="queryParams.keyColumn1" :placeholder="$t('system.pleaseEnterThePreciseQueryField')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.createTime')">
        <el-date-picker
          v-model="dateRangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        />
      </el-form-item>
      <el-form-item :label="$t('请求参数')" prop="requestParams">
        <el-input v-model="queryParams.requestParams" :placeholder="$t('请输入请求参数')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('响应返回')" prop="response">
        <el-input v-model="queryParams.response" :placeholder="$t('请输入响应返回')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['open:api-request-log:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
        >{{ $t('common.export') }}</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('system.interfaceName')" align="center" prop="requestDesc" width="200" />
      <el-table-column :label="$t('system.userIp')" align="center" prop="userIp" />
      <el-table-column :label="$t('system.startRequestTime')" align="center" prop="requestTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.requestTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.responseReturnTime')" align="center" prop="requestEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.requestEndTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.requestParameters')" align="center" width="200" prop="requestParams">
        <template slot-scope="scope">
          <el-button type="text" size="mini" style="text-decoration: underline;" @click="open=true;content=scope.row.requestParams">
            {{ scope.row.requestParams }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.responseReturn')" align="center" prop="response">
        <template slot-scope="scope">
          <el-button type="text" size="mini" style="text-decoration: underline;" @click="open=true;content=scope.row.response">
            {{ scope.row.response }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.accurateQueryFieldegOrderModuleOrderNumber')" align="center" prop="keyColumn1" />
      <el-table-column :label="$t('system.accurateQueryField')" align="center" prop="keyColumn2" />
      <el-table-column :label="$t('system.dataProcessingStatus')" align="center" prop="handleStatus">
        <template slot-scope="scope">
          <dict-tag :value="scope.row.handleStatus" :type="DICT_TYPE.SRM_OPEN_HANDLE_STATUS" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog :title="$t('auth.see')" :visible.sync="open" append-to-body width="400px">
      <div style="padding: 30px;">{{ content }}</div>
      <div slot="footer" class="dialog-footer">
        <el-button v-clipboard:copy="content" v-clipboard:success="()=>$modal.msgSuccess($t('system.successfullyCopied'))" type="primary">{{ $t('order.copy') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getApiRequestLogPage, exportApiRequestLogExcel } from '@/api/open/apiRequestLog'

export default {
  name: 'Apirequestlog',
  components: {
  },
  data() {
    return {
      content: '',
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // srm三方对接请求参数记录日志表（包含三方call srm & srm call三方）列表
      list: [],
      // 是否显示弹出层
      open: false,
      dateRangeRequestTime: [],
      dateRangeRequestEndTime: [],
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        requestDesc: null,
        userIp: null,
        requestParams: null,
        response: null,
        keyColumn1: null,
        keyColumn2: null,
        handleStatus: null,
        retry: null
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeRequestTime, 'requestTime')
      this.addBeginAndEndTime(params, this.dateRangeRequestEndTime, 'requestEndTime')
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getApiRequestLogPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeRequestTime = []
      this.dateRangeRequestEndTime = []
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeRequestTime, 'requestTime')
      this.addBeginAndEndTime(params, this.dateRangeRequestEndTime, 'requestEndTime')
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行导出
      this.$modal.confirm(this.$t('system.areYouSureToExportAllDockingRequestLogs')).then(() => {
        this.exportLoading = true
        return exportApiRequestLogExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('system.dockingRequestLogXlsx'))
        this.exportLoading = false
      }).catch(() => {})
    }
  }
}
</script>
