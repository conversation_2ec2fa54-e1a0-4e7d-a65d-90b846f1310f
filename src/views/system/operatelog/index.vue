<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item :label="$t('system.systemModule')" prop="module">
        <el-input
          v-model="queryParams.module"
          :placeholder="$t('system.pleaseEnterTheSystemModule')"
          clearable
          style="width: 240px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.operators')" prop="userNickname">
        <el-input
          v-model="queryParams.userNickname"
          :placeholder="$t('system.pleaseEnterTheOperator')"
          clearable
          style="width: 240px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.type')" prop="type">
        <el-select v-model="queryParams.type" :placeholder="$t('system.operationType')" clearable style="width: 240px">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_OPERATE_TYPE)"
            :key="parseInt(dict.value)"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="success">
        <el-select v-model="queryParams.success" :placeholder="$t('system.operationStatus')" clearable style="width: 240px">
          <el-option :key="true" :label="$t('system.success')" :value="true" />
          <el-option :key="false" :label="$t('system.fail')" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.operationTime')">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:operate-log:export']"
          type="warning"
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
        >{{ $t('common.export') }}</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('system.logNo')" align="center" prop="id" />
      <el-table-column :label="$t('system.operationModule')" align="center" prop="module" />
      <el-table-column :label="$t('system.operationName')" align="center" prop="name" width="180" />
      <el-table-column :label="$t('system.operationType')" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_OPERATE_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.operator')" align="center" prop="userNickname" />
      <el-table-column :label="$t('system.operationResults')" align="center" prop="status">
        <template slot-scope="scope">
          <span>{{ scope.row.resultCode === 0 ? $t('system.success') : $t('system.fail') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.operationDate')" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.executionDuration')" align="center" prop="startTime">
        <template slot-scope="scope">
          <span>{{ scope.row.duration }}  ms</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:operate-log:query']"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row,scope.index)"
          >{{ $t('system.detailed') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 操作日志详细 -->
    <el-dialog :title="$t('system.accessLogDetails')" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px" size="mini">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('system.logPrimaryKey')">{{ form.id }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.linkTracking')">{{ form.traceId }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.userInformation')">{{ form.userId }} | {{ form.userNickname }} | {{ form.userIp }} | {{ form.userAgent }} </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.operationInformation')">
              {{ form.module }} | {{ form.name }}
              <dict-tag :type="DICT_TYPE.SYSTEM_OPERATE_TYPE" :value="form.type" />
              <br> {{ form.content }}
              <br> {{ form.exts }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.requestInformation')">{{ form.requestMethod }} | {{ form.requestUrl }} </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.methodName')">{{ form.javaMethod }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.methodParameters')">{{ form.javaMethodArgs }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.startTime')">
              {{ parseTime(form.startTime) }} | {{ form.duration }} ms
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.operationResults')">
              <div v-if="form.resultCode === 0">{{ $t('system.normal') }} | {{ form.resultData }} </div>
              <div v-else-if="form.resultCode > 0">{{ $t('system.fail') }} | {{ form.resultCode }} || {{ form.resultMsg }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">{{ $t('order.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOperateLog, exportOperateLog } from '@/api/system/operatelog'

export default {
  name: 'Operlog',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 是否显示弹出层
      open: false,
      // 类型数据字典
      typeOptions: [],
      // 类型数据字典
      statusOptions: [],
      // 日期范围
      dateRange: [],
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        module: undefined,
        userNickname: undefined,
        businessType: undefined,
        success: undefined
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询登录日志 */
    getList() {
      this.loading = true
      listOperateLog(this.addDateRange(this.queryParams, [
        this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined,
        this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined
      ])).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      }
      )
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true
      this.form = row
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.addDateRange(this.queryParams, [
        this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined,
        this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined
      ])
      this.$modal.confirm(this.$t('system.areYouSureToExportAllOperationLogDataItems')).then(() => {
        this.exportLoading = true
        return exportOperateLog(queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('system.operationLogxls'))
        this.exportLoading = false
      }).catch(() => {})
    }
  }
}
</script>

