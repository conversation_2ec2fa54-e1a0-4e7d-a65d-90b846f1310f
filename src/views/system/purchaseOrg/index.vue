<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item :label="$t('system.code')" prop="code">
        <el-input v-model="queryParams.code" :placeholder="$t('system.pleaseEnterTheCode')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.name')" prop="name">
        <el-input v-model="queryParams.name" :placeholder="$t('common.pleaseEnterAName')" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('supplier.pleaseSelectStatus')" clearable size="small">
          <el-option v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="dict.label" :value="parseInt(dict.value)" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.createTime')">
        <el-date-picker
          v-model="dateRangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:purchase-org:create']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >{{ $t('common.add') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:purchase-org:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
        >{{ $t('common.export') }}</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('system.code')" align="center" prop="code" />
      <el-table-column :label="$t('common.name')" align="center" prop="name" />
      <el-table-column :label="$t('supplier.displayOrder')" align="center" prop="sort" />
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:purchase-org:update']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}</el-button>
          <el-button
            v-has-permi="['system:purchase-org:bind-factories']"
            size="mini"
            type="text"
            icon="el-icon-setting"
            @click="handleFactories(scope.row)"
          >{{ $t('system.bindingFactory') }}</el-button>
          <el-button
            v-hasPermi="['system:purchase-org:delete']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('system.code')" prop="code">
          <el-input v-model="form.code" :placeholder="$t('system.pleaseEnterTheCode')" />
        </el-form-item>
        <el-form-item :label="$t('common.name')" prop="name">
          <el-input v-model="form.name" :placeholder="$t('common.pleaseEnterAName')" />
        </el-form-item>
        <el-form-item :label="$t('supplier.displayOrder')" prop="sort">
          <el-input-number v-model="form.sort" :placeholder="$t('system.pleaseEnterTheDisplayOrder')" />
        </el-form-item>
        <el-form-item :label="$t('common.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!--    采购组织绑定工厂的弹出框-->
    <el-dialog :title="title" :visible.sync="openFactories" append-to-body width="500px">
      <el-form :model="form" label-width="80px">
        <el-form-item :label="$t('system.code')">
          <el-input v-model="form.code" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('common.name')">
          <el-input v-model="form.name" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('system.bindingFactory')">
          <el-checkbox v-model="factoryNodeAll" @change="handleCheckedTreeNodeAll($event)">全选/全不选</el-checkbox>
          <el-tree
            ref="factory"
            :data="factoryOptions"
            :props="defaultProps"
            class="tree-border"
            empty-text="加载中，请稍后"
            node-key="id"
            show-checkbox
          />
        </el-form-item>
      </el-form>

      <!--operations-->
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelFactory">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="bindFactory">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

  </div>
</template>

<script>
import { createPurchaseOrg, updatePurchaseOrg, listFactoryIds, bindFactories, deletePurchaseOrg, getPurchaseOrg, getPurchaseOrgPage, exportPurchaseOrgExcel } from '@/api/system/purchaseOrg'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { listFactoryFromCache } from '@/api/system/factory'

export default {
  name: 'Purchaseorg',
  components: {
  },
  data() {
    return {
      // 绑定工厂相关-begin

      // 是否显示弹出层（菜单权限）
      openFactories: false,
      factoryExpand: false,
      factoryNodeAll: false,
      // 工厂列表
      factoryOptions: [],
      defaultProps: {
        id: 'id',
        label: 'name',
        children: []
      },
      // 绑定工厂相关-end

      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购组织列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        code: null,
        name: null,
        sort: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        code: [{ required: true, message: this.$t('system.codeCannotBeEmpty'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('system.nameCannotBeEmpty'), trigger: 'blur' }],
        sort: [{ required: true, message: this.$t('system.displayOrderCannotBeEmpty'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }]
      },
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value) {
      this.$refs.factory.setCheckedNodes(value ? this.factoryOptions : [])
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getPurchaseOrgPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      // 工厂弹出框相关
      this.factoryExpand = false
      this.factoryNodeAll = false

      this.form = {
        id: undefined,
        code: undefined,
        name: undefined,
        sort: undefined,
        status: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.form.status = 0
      this.title = this.$t('system.addPurchaseOrganization')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getPurchaseOrg(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyPurchaseOrganization')
      })
    },
    /** 采购组织绑定工厂操作 */
    handleFactories(row) {
      this.reset()
      this.form.id = row.id
      this.form.name = row.name
      this.form.code = row.code
      // open dialog
      this.openFactories = true
      // list factory
      listFactoryFromCache({
        status: 0
      }).then(response => {
        // 处理 factoryOptions 参数
        this.factoryOptions = []
        for (let i = 0; i < response.data.length; i++) {
          response.data[i].parentId = 0 // default parentId value,cant ignore
        }
        this.factoryOptions.push(...this.handleTree(response.data, 'id'))
        listFactoryIds(row.id).then(response => {
          this.$refs.factory.setCheckedKeys(response.data)
        })
      })
    },
    // 提交绑定的工厂
    bindFactory() {
      if (this.form.id !== undefined) {
        bindFactories({
          orgId: this.form.id,
          factoryIds: [...this.$refs.factory.getCheckedKeys()]
        }).then(response => {
          this.$modal.msgSuccess(this.$t('system.bindingSucceeded'))
          this.openFactories = false
          this.getList()
        })
      }
    },
    // 取消绑定工厂弹出框
    cancelFactory() {
      this.openFactories = false
      this.reset()
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updatePurchaseOrg(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createPurchaseOrg(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除采购组织编号为"' + id + '"的数据项?').then(function() {
        return deletePurchaseOrg(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行导出
      this.$modal.confirm(this.$t('system.areYouSureToExportAllPurchaseOrganizationDataItems')).then(() => {
        this.exportLoading = true
        return exportPurchaseOrgExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('system.purchaseOrganizationxls'))
        this.exportLoading = false
      }).catch(() => {})
    }
  }
}
</script>
