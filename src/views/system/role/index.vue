<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams">
      <el-form-item :label="$t('system.roleName')" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          :placeholder="$t('system.pleaseEnterTheRoleName')"
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.roleId')" prop="code">
        <el-input
          v-model="queryParams.code"
          clearable
          :placeholder="$t('system.pleaseEnterTheRoleId')"
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" clearable :placeholder="$t('system.roleStatus')" size="small" style="width: 240px">
          <el-option
            v-for="dict in statusDictDatas"
            :key="parseInt(dict.value)"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.createTime')">
        <el-date-picker
          v-model="dateRange"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          size="small"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="roleList">
      <el-table-column :label="$t('system.roleNo')" prop="id" width="120" />
      <el-table-column :show-overflow-tooltip="true" :label="$t('system.roleName')" prop="name" width="150" />
      <el-table-column :show-overflow-tooltip="true" :label="$t('system.roleId')" prop="code" width="150" />
      <el-table-column :label="$t('system.roleType')" prop="type" width="80">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_ROLE_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.insideAndOutsideTheRole')" prop="isExternal" width="100">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_ROLE_IS_EXTERNAL" :value="scope.row.isExternal" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('supplier.displayOrder')" prop="sort" width="100" />
      <el-table-column
        align="center"
        :label="$t('common.status')"
        width="100"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :label="$t('common.createTime')"
        prop="createTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        :label="$t('common.operate')"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:role:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleDesensitization(scope.row)"
          >{{ $t('价格加密') }}
          </el-button>
          <el-button
            v-hasPermi="['system:role:copy']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="copyRole(scope.row)"
          >{{ $t('system.copyRole') }}
          </el-button>
          <el-button
            v-hasPermi="['system:permission:assign-role-menu']"
            icon="el-icon-circle-check"
            size="mini"
            type="text"
            @click="handleMenu(scope.row)"
          >{{ $t('system.menuPermissions') }}
          </el-button>
          <el-button
            v-hasPermi="['system:permission:assign-role-data-scope']"
            icon="el-icon-circle-check"
            size="mini"
            type="text"
            @click="handleDataScope(scope.row)"
          >{{ $t('system.dataPermission') }}
          </el-button>
          <el-button
            v-if="scope.row.type!==1"
            v-hasPermi="['system:role:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="openCopy" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-form-item :label="$t('system.roleName')" prop="name">
          <el-input v-model="form.name" :placeholder="$t('system.pleaseEnterTheRoleName')" />
        </el-form-item>
        <el-form-item :label="$t('system.roleId')" prop="code">
          <el-input v-model="form.code" :placeholder="$t('system.pleaseEnterTheRoleId')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelCopyRole">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-form-item :label="$t('system.roleName')" prop="name">
          <el-input v-model="form.name" :placeholder="$t('system.pleaseEnterTheRoleName')" />
        </el-form-item>
        <el-form-item :label="$t('system.roleId')" prop="code">
          <el-input v-model="form.code" :placeholder="$t('system.pleaseEnterTheRoleId')" />
        </el-form-item>
        <el-form-item :label="$t('system.insideAndOutsideTheRole')" prop="isExternal">
          <el-radio-group v-model="form.isExternal">
            <el-radio v-for="dict in isExternalDatas" :key="parseInt(dict.value)" :label="parseInt(dict.value)">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('管理员标记')" prop="isAdmin">
          <el-switch
            v-model="form.isAdmin"
            :active-value="0"
            :inactive-value="1"
          />
        </el-form-item>
        <el-form-item :label="$t('system.roleOrder')" prop="sort">
          <el-input-number v-model="form.sort" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item :label="$t('common.remarks')">
          <el-input v-model="form.remark" :placeholder="$t('rfq.pleaseEnterTheContent')" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!-- 分配角色的数据权限对话框 -->
    <el-dialog :visible.sync="openDataScope" append-to-body :title="$t('system.assignDataPermission')" width="800px">
      <el-form :model="form" label-width="80px">
        <el-form-item :label="$t('system.roleName')">
          <el-input v-model="form.name" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('system.roleId')">
          <el-input v-model="form.code" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('system.scopeOfAuthority')">
          <el-table max-height="600px" :data="form.roleModulePermission">
            <el-table-column
              width="130"
              :label="$t('system.moduleName')"
              prop="name"
            />

            <el-table-column :label="$t('system.dataPermissionRules')">
              <template #default="scope">
                <el-checkbox-group v-model="scope.row.permissionRules">
                  <el-checkbox v-for="item in getPermissionRules(scope.row.modulePermissionRules)" :key="parseInt(item.value)" :label="parseInt(item.value)" @change="checked=>changeLick(checked, parseInt(item.value),scope.row)"> {{ item.label }}</el-checkbox>
                </el-checkbox-group>
              </template>
            </el-table-column>

          </el-table>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDataScope">{{ $t('common.cancel') }}</el-button>
        <el-button v-if="rowType" type="primary" @click="submitDataScope">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!-- 指定部门 -->
    <el-dialog :visible.sync="openSpecified" append-to-body :title="openSpecifiedTitle" width="500px">
      <el-form :model="specifiedForm" label-width="80px">
        <el-form-item v-show="specifiedForm.type === SysDataScopeEnum.DEPT_CUSTOM" :label="$t('system.dataPermission')">
          <el-checkbox :checked="!specifiedForm.deptCheckStrictly" @change="handleCheckedTreeConnect($event, 'dept')">
            {{ $t('system.parentChildLinkageselectParentNodeAutomaticallySelectChildNode') }}
          </el-checkbox>
          <el-checkbox v-model="deptExpand" @change="handleCheckedTreeExpand($event, 'dept')">{{ $t('common.expandCollapse') }}</el-checkbox>
          <el-checkbox v-model="deptNodeAll" @change="handleCheckedTreeNodeAll($event, 'dept')">{{ $t('system.selectAllselectNone') }}</el-checkbox>
          <el-tree
            ref="dept"
            :check-strictly="specifiedForm.deptCheckStrictly"
            :data="deptOptions"
            :props="defaultProps"
            class="tree-border"
            default-expand-all
            empty-text="加载中，请稍后"
            node-key="id"
            show-checkbox
          />
        </el-form-item>
        <el-form-item v-show="specifiedForm.type === SysDataScopeEnum.FACTORY_CUSTOM" :label="$t('system.dataPermission')">
          <el-checkbox v-model="deptNodeAll" @change="handleCheckedTreeNodeAll($event, 'factory')">{{ $t('system.selectAllselectNone') }}</el-checkbox>
          <el-tree
            ref="factory"
            :data="factoryOptions"
            :props="defaultProps"
            class="tree-border"
            empty-text="加载中，请稍后"
            node-key="id"
            show-checkbox
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelSpecified">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitSpecified">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
    <!-- 分配角色的菜单权限对话框 -->
    <el-dialog :title="title" :visible.sync="openMenu" append-to-body width="500px">
      <el-form :model="form" label-width="80px">
        <el-form-item :label="$t('system.roleName')">
          <el-input v-model="form.name" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('system.roleId')">
          <el-input v-model="form.code" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('system.menuPermissions')">
          <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">{{ $t('common.expandCollapse') }}</el-checkbox>
          <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">{{ $t('system.selectAllselectNone') }}</el-checkbox>
          <el-tree
            ref="menu"
            :check-strictly="form.menuCheckStrictly"
            :data="menuOptions"
            :props="defaultProps"
            class="tree-border"
            empty-text="加载中，请稍后"
            node-key="id"
            show-checkbox
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelMenu">{{ $t('common.cancel') }}</el-button>
        <el-button v-if="rowType" type="primary" @click="submitMenu">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!-- 价格加密对话框 -->
    <el-dialog :title="title" :visible.sync="openDesensitization" append-to-body width="500px">
      <el-form :model="form2" label-width="80px">
        <el-form-item :label="$t('system.priceEncryption')">
          <el-input :placeholder="$t('system.priceEncryption')" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('system.module')">
          <el-select v-model="form2.moduleCode" :placeholder="$t('system.pleaseSelect')" multiple clearable style="width: 100%">
            <el-option
              v-for="item in moduleList"
              :key="item.id"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.isItEncrypted')">
          <el-radio-group v-model="form2.priceDesensitization">
            <el-radio
              v-for="dict in statusDictDatas"
              :key="parseInt(dict.value)"
              :label="parseInt(dict.value)"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDesensitization">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="doSaveModuleDesensitization()">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getAllModule } from '@/api/system/module'
import {
  addRole,
  changeRoleStatus, copyRole,
  delRole,
  exportRole,
  getRole,
  listRole,
  updateRole,
  saveModuleDataDesensitization,
  getRoleModuleDesensitizationConfig
} from '@/api/system/role'
import { listSimpleMenus } from '@/api/system/menu'
import {
  assignRoleMenu,
  listRoleMenus,
  assignRoleDataScope,
  getRoleModulePermissionList
} from '@/api/system/permission'
import { listSimpleDepts } from '@/api/system/dept'
import { CommonStatusEnum, SystemDataScopeEnum } from '@/utils/constants'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { listFactoryFromCache } from '@/api/system/factory'
import store from '@/store'

export default {
  name: 'Role',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（角色复制）
      openCopy: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 是否显示弹出层（菜单权限）
      openMenu: false,
      // 是否打开价格加密弹出层
      openDesensitization: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [], // 部门属性结构
      depts: [], // 部门列表
      factoryOptions: [], // 工厂
      factorys: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: undefined,
        code: undefined,
        status: undefined
      },
      form2: {
        priceDesensitization: 1,
        moduleCode: [],
        roleId: 0
      },
      // 表单参数
      form: {
        oldIsExternal: undefined,
        oldId: undefined
      },
      defaultProps: {
        label: 'name',
        children: 'children'
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: this.$t('system.roleNameCannotBeEmpty'), trigger: 'blur' }
        ],
        code: [
          { required: true, message: this.$t('system.roleIdCannotBeEmpty'), trigger: 'blur' }
        ],
        sort: [
          { required: true, message: this.$t('system.theRoleOrderCannotBeEmpty'), trigger: 'blur' }
        ],
        isExternal: [
          { required: true, message: this.$t('system.roleInternalAndExternalTagsCannotBeEmpty'), trigger: 'blur' }
        ]
      },
      openSpecified: false,
      openSpecifiedTitle: undefined,
      specifiedForm: {
      },
      moduleList: [{
        name: '',
        code: '',
        id: 0
      }],
      // 枚举
      SysCommonStatusEnum: CommonStatusEnum,
      SysDataScopeEnum: SystemDataScopeEnum,
      // 数据字典
      roleTypeDictDatas: getDictDatas(DICT_TYPE.SYSTEM_ROLE_TYPE),
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS),
      isExternalDatas: getDictDatas(DICT_TYPE.SYSTEM_ROLE_IS_EXTERNAL),
      rowType: false
    }
  },
  created() {
    this.getList()
    getAllModule().then(res => {
      this.moduleList = res.data
    })
  },
  methods: {
    handleDesensitization(row) {
      const roleId = row.id
      getRoleModuleDesensitizationConfig(roleId).then(response => {
        if (response.data.length === 0) {
          this.form2.priceDesensitization = 1// 默认加密
          this.form2.moduleCode = []
        } else {
          this.form2 = response.data[0]
          this.form2.priceDesensitization = response.data[0].priceDesensitization ? 1 : 0 // 默认加密
          this.form2.moduleCode = response.data.map(v => v.code)
        }
        this.form2.roleId = roleId
        this.form2 = { ...this.form2 }
        this.openDesensitization = true
        this.title = this.$t('system.dataEncryption')
      })
    },
    /**
     * 维护模块的数据加密
     */
    doSaveModuleDesensitization() {
      if (this.form2.moduleCode.length === 0) {
        this.$modal.msgError(this.$t('system.pleaseSelectModule'))
        return
      }
      const arr = []
      for (const moduleCodeElement of this.form2.moduleCode) {
        arr.push({
          roleId: this.form2.roleId,
          moduleCode: moduleCodeElement,
          desensitizationValue: 'price_desensitization', // 价格类型的固定值
          enable: this.form2.priceDesensitization
        })
      }
      saveModuleDataDesensitization(arr).then(res => {
        this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
        this.openDesensitization = false
      })
    },
    /**
     * 针对指定数据的弹出
     * @param checked 是否选中
     * @param key  选中的value;value [2,7];
     */
    changeLick(checked, key, row) {
      // 当选中的时候
      if (checked) {
        switch (key) {
          // 指定部门的时候
          case SystemDataScopeEnum.DEPT_CUSTOM:
            this.openSpecified = true
            this.openSpecifiedTitle = this.$t('system.departmentSelection')
            this.specifiedForm.type = key
            this.specifiedForm.moduleCode = row.code
            // 获得部门列表
            listSimpleDepts().then(response => {
              // 处理 deptOptions 参数
              this.deptOptions = []
              this.deptOptions.push(...this.handleTree(response.data, 'id'))
              this.depts = response.data
              const permissionRule = this.form.permissionRuleSpecifiedIds.find(item => item.permissionRuleId === this.specifiedForm.type && item.moduleCode === this.specifiedForm.moduleCode)
              if (permissionRule) {
                this.$refs.dept.setCheckedKeys(permissionRule.permissionRuleSpecifiedIds, false)
              }
            })
            break
          // 指定工厂的时候
          case SystemDataScopeEnum.FACTORY_CUSTOM:
            this.openSpecified = true
            this.openSpecifiedTitle = this.$t('system.factorySelection')
            this.specifiedForm.type = key
            this.specifiedForm.moduleCode = row.code
            // list factory
            listFactoryFromCache({
              status: 0
            }).then(response => {
              // 处理 factoryOptions 参数
              this.factoryOptions = []
              for (let i = 0; i < response.data.length; i++) {
                response.data[i].parentId = 0 // default parentId value,cant ignore
              }
              this.factoryOptions.push(...this.handleTree(response.data, 'id'))
              this.factorys = response.data
              const permissionRule = this.form.permissionRuleSpecifiedIds.find(item => item.permissionRuleId === key && item.moduleCode === row.code)
              if (permissionRule) {
                this.$refs.factory.setCheckedKeys(permissionRule.permissionRuleSpecifiedIds, false)
              }
            })
            break

          default:
            break
        }
      }
    },
    /**
     * 对数据权限规则的数据范围进行限制
     * @param modulePermissionRules
     * @returns {*|Array|*[]}
     */
    getPermissionRules(modulePermissionRules) {
      const data = getDictDatas(DICT_TYPE.SYSTEM_DATA_SCOPE, 0)
      if (modulePermissionRules) {
        const newB = new Set(modulePermissionRules)
        return Array.from(new Set([...data].filter(x => newB.has(parseInt(x.value)))))
      }
      return data
    },
    /** 查询角色列表 */
    getList() {
      this.loading = true
      listRole(this.addDateRange(this.queryParams, [
        this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined,
        this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined
      ])).then(
        response => {
          this.roleList = response.data.list
          this.total = response.data.total
          this.loading = false
        }
      )
    },
    // 角色状态修改
    handleStatusChange(row) {
      // 此时，row 已经变成目标状态了，所以可以直接提交请求和提示
      const text = row.status === CommonStatusEnum.ENABLE ? this.$t('common.enable') : this.$t('system.deactivate')
      this.$modal.confirm('确认要"' + text + '""' + row.name + '"角色吗?').then(function() {
        return changeRoleStatus(row.id, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function() {
        // 异常时，需要将 row.status 状态重置回之前的
        row.status = row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE
          : CommonStatusEnum.ENABLE
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 取消按钮（角色复制）
    cancelCopyRole() {
      this.openCopy = false
      this.reset()
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false
      this.reset()
    },
    // 取消按钮（菜单权限）
    cancelDesensitization() {
      this.openDesensitization = false
      this.reset()
    },
    // 取消按钮（菜单权限）
    cancelMenu() {
      this.openMenu = false
      this.reset()
    },
    // 取消指定数据
    cancelSpecified() {
      this.openSpecified = false
    },
    // 表单重置
    reset() {
      if (this.$refs.menu !== undefined) {
        this.$refs.menu.setCheckedKeys([])
      }
      this.menuExpand = false
      this.menuNodeAll = false
      this.deptExpand = true
      this.deptNodeAll = false
      this.form = {
        id: undefined,
        name: undefined,
        code: undefined,
        sort: 0,
        deptIds: [],
        menuIds: [],
        dataScope: undefined,
        deptCheckStrictly: false,
        menuCheckStrictly: true,
        remark: undefined,
        roleModulePermission: [],
        // 指定数据的集合
        permissionRuleSpecifiedIds: []

      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type === 'menu') {
        const treeList = this.menuOptions
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value
        }
      } else if (type === 'dept') {
        const treeList = this.deptOptions
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type === 'menu') {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : [])
      } else if (type === 'dept') {
        // this.$refs.dept.setCheckedNodes(value ? this.deptOptions: []);
        this.$refs.dept.setCheckedNodes(value ? this.depts : [])
      } else if (type === 'factory') {
        // this.$refs.dept.setCheckedNodes(value ? this.deptOptions: []);
        this.$refs.factory.setCheckedNodes(value ? this.factorys : [])
      }
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      if (type === 'menu') {
        this.form.menuCheckStrictly = value
      } else if (type === 'dept') {
        this.form.deptCheckStrictly = !value
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addRole')
    },
    /** 复制角色按钮操作 */
    copyRole(row) {
      this.reset()
      const id = row.id
      this.form.oldId = id
      getRole(id).then(response => {
        this.form.sort = response.data.sort
        this.form.isExternal = response.data.isExternal
        this.openCopy = true
        this.title = this.$t('system.addRole')
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getRole(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyRole')
      })
    },
    /** 分配菜单权限操作 */
    handleMenu(row) {
      this.reset()
      const id = row.id
      // 处理了 form 的角色 name 和 code 的展示
      this.form.id = id
      this.form.name = row.name
      this.form.code = row.code
      this.rowType = store.getters.roles.includes('super_admin') || row.type !== 1
      // 打开弹窗
      this.openMenu = true
      // 获得菜单列表
      listSimpleMenus().then(response => {
        // 处理 menuOptions 参数
        this.menuOptions = []
        this.menuOptions.push(...this.handleTree(response.data, 'id'))
        // 获取角色拥有的菜单权限
        listRoleMenus(id).then(response => {
          // 设置为严格，避免设置父节点自动选中子节点，解决半选中问题
          this.form.menuCheckStrictly = true
          // 设置选中
          this.$refs.menu.setCheckedKeys(response.data)
          // 设置为非严格，继续使用半选中
          this.form.menuCheckStrictly = false
        })
      })
    },
    /** 分配数据权限操作 */
    handleDataScope(row) {
      this.reset()
      // 处理了 form 的角色 name 和 code 的展示
      this.form.id = row.id
      this.form.name = row.name
      this.form.code = row.code
      this.rowType = store.getters.roles.includes('super_admin') || row.type !== 1
      // 打开弹窗
      this.openDataScope = true
      this.form.permissionRuleSpecifiedIds = []
      // 获得部门列表
      getRoleModulePermissionList(row.id).then(response => {
        this.form.roleModulePermission = response.data
        if (this.form.roleModulePermission) {
          this.form.roleModulePermission.map((item) => {
            if (item.permissionRuleList && item.permissionRuleList.length > 0) { this.form.permissionRuleSpecifiedIds.push(...item.permissionRuleList) }
          })
        }
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.oldId !== undefined) {
            copyRole(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('system.successfullyCopied'))
              this.openCopy = false
              this.getList()
            })
          } else {
            if (this.form.id !== undefined) {
              if (this.form.oldIsExternal !== undefined && this.form.oldIsExternal !== this.form.isExternal && this.form.isExternal === 1) {
                const text = this.$t('system.changeRoleIsExternal')
                this.$modal.confirm(text).then(() => {
                  updateRole(this.form).then(response => {
                    this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
                    this.open = false
                    this.getList()
                  })
                })
              } else {
                updateRole(this.form).then(response => {
                  this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
                  this.open = false
                  this.getList()
                })
              }
            } else {
              addRole(this.form).then(response => {
                this.$modal.msgSuccess(this.$t('common.addSuccess'))
                this.open = false
                this.getList()
              })
            }
          }
        }
      })
    },
    /** 提交指定（数据权限） */
    submitSpecified: function() {
      if (this.form.id !== undefined) {
        const permissionRule = this.form.permissionRuleSpecifiedIds.find(item => item.permissionRuleId === this.specifiedForm.type && item.moduleCode === this.specifiedForm.moduleCode)
        let permissionRuleSpecifiedIds = []
        // 指定部门的时候选择的数据
        if (this.specifiedForm.type === SystemDataScopeEnum.DEPT_CUSTOM) {
          permissionRuleSpecifiedIds = this.$refs.dept.getCheckedKeys()
        } else if (this.specifiedForm.type === SystemDataScopeEnum.FACTORY_CUSTOM) { // 指定工厂的时候，选择的数据
          permissionRuleSpecifiedIds = this.$refs.factory.getCheckedKeys()
        }
        if (permissionRule) {
          permissionRule.permissionRuleSpecifiedIds = permissionRuleSpecifiedIds
        } else {
          this.form.permissionRuleSpecifiedIds.push({
            moduleCode: this.specifiedForm.moduleCode, permissionRuleId: this.specifiedForm.type,
            permissionRuleSpecifiedIds: permissionRuleSpecifiedIds
          })
        }
        this.$modal.msgSuccess(this.$t('common.savedSuccessfully'))
        this.openSpecified = false
      }
    },
    /** 提交按钮（数据权限） */
    submitDataScope: function() {
      if (this.form.id !== undefined) {
        assignRoleDataScope({
          roleId: this.form.id,
          roleModulePermissionRespVOs: this.form.roleModulePermission,
          permissionRuleList: this.form.permissionRuleSpecifiedIds
        }).then(response => {
          this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          this.openDataScope = false
          this.getList()
        })
      }
    },
    /** 提交按钮（菜单权限） */
    submitMenu: function() {
      if (this.form.id !== undefined) {
        assignRoleMenu({
          roleId: this.form.id,
          menuIds: [...this.$refs.menu.getCheckedKeys(), ...this.$refs.menu.getHalfCheckedKeys()]
        }).then(response => {
          this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          this.openMenu = false
          this.getList()
        })
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.rowType = row.type === 1
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除角色编号为"' + ids + '"的数据项?').then(function() {
        return delRole(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$modal.confirm(this.$t('system.areYouSureToExportAllRoleDataItems')).then(function() {
        this.exportLoading = true
        return exportRole(queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('system.roleDataxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>
