<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.sensitiveWords')" prop="name">
        <el-input
          v-model="queryParams.name"
          :placeholder="$t('system.pleaseEnterASensitiveWord')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.label')" prop="tag">
        <el-select
          v-model="queryParams.tag"
          :placeholder="$t('system.pleaseSelectALabel')"
          clearable
          @keyup.enter.native="handleQuery"
        >
          <el-option v-for="tag in tags" :key="tag" :label="tag" :value="tag" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('system.pleaseSelectTheEnablingStatus')" clearable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.createTime')">
        <el-date-picker
          v-model="dateRangeCreateTime"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          size="small"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:sensitive-word:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:sensitive-word:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >{{ $t('common.export') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="el-icon-document-checked" plain size="mini" type="success" @click="handleTest">
          {{ $t('system.test') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('order.number')" align="center" prop="id" />
      <el-table-column :label="$t('system.sensitiveWords')" align="center" prop="name" />
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('order.describe')" align="center" prop="description" />
      <el-table-column :label="$t('system.label')" align="center" prop="tags">
        <template slot-scope="scope">
          <el-tag v-for="(tag, index) in scope.row.tags" :key="index" :disable-transitions="true" :index="index">
            {{ tag }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:sensitive-word:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:sensitive-word:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('system.sensitiveWords')" prop="name">
          <el-input v-model="form.name" :placeholder="$t('system.pleaseEnterASensitiveWord')" />
        </el-form-item>
        <el-form-item :label="$t('common.status')">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('common.remarks')" prop="description">
          <el-input
            v-model="form.description"
            :placeholder="$t('rfq.pleaseEnterTheContent')"
            type="textarea"
          />
        </el-form-item>
        <el-form-item :label="$t('system.label')" prop="tags">
          <el-select
            v-model="form.tags"
            :placeholder="$t('system.pleaseSelectTheArticleTag')"
            allow-create
            filterable
            multiple
            style="width: 380px"
          >
            <el-option v-for="tag in tags" :key="tag" :label="tag" :value="tag" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!-- 对话框(测试敏感词) -->
    <el-dialog :title="$t('system.detectSensitiveWords')" :visible.sync="testOpen" append-to-body width="500px">
      <el-form ref="testForm" :model="testForm" :rules="testRules" label-width="80px">
        <el-form-item :label="$t('system.text')" prop="text">
          <el-input
            v-model="testForm.text"
            :placeholder="$t('system.pleaseEnterTestText')"
            type="textarea"
          />
        </el-form-item>
        <el-form-item :label="$t('system.label')" prop="tags">
          <el-select
            v-model="testForm.tags"
            :placeholder="$t('system.pleaseSelectALabel')"
            multiple
            style="width: 380px"
          >
            <el-option v-for="tag in tags" :key="tag" :label="tag" :value="tag" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelTest">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitTestForm">{{ $t('system.testing') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createSensitiveWord,
  deleteSensitiveWord,
  exportSensitiveWordExcel,
  getSensitiveWord,
  getSensitiveWordPage,
  getSensitiveWordTags,
  updateSensitiveWord,
  validateText
} from '@/api/system/sensitiveWord'
import { CommonStatusEnum } from '@/utils/constants'

export default {
  name: 'Sensitiveword',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 敏感词列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      testOpen: false,
      dateRangeCreateTime: [],
      tags: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        tag: null
      },
      // 表单参数
      form: {},
      // 表单参数
      testForm: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: this.$t('system.sensitiveWordsCannotBeEmpty'), trigger: 'blur' }],
        tags: [{ required: true, message: this.$t('system.labelCannotBeEmpty'), trigger: 'blur' }]
      },
      testRules: {
        text: [{ required: true, message: this.$t('system.testTextCannotBeEmpty'), trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getTags()
    this.getList()
  },
  methods: {
    /** 初始化标签select*/
    getTags() {
      getSensitiveWordTags().then(response => {
        this.tags = response.data
      })
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getSensitiveWordPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 取消按钮 */
    cancelTest() {
      this.resetTest()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        description: undefined,
        tags: undefined,
        status: CommonStatusEnum.ENABLE
      }
      this.resetForm('form')
    },
    /** 表单重置 */
    resetTest() {
      this.testForm = {
        text: undefined,
        tags: undefined
      }
      this.resetForm('testForm')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addSensitiveWords')
    },
    /** 测试敏感词按钮操作 */
    handleTest() {
      this.resetTest()
      this.testOpen = true
      this.titleTest = this.$t('system.detectWhetherTheTextContainsSensitiveWords')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getSensitiveWord(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifySensitiveWords')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateSensitiveWord(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          }).catch(err => {
          })
          return
        }
        // 添加的提交
        createSensitiveWord(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        }).catch(err => {
        })
      })
    },
    /** 测试文本2提交按钮 */
    submitTestForm() {
      this.$refs['testForm'].validate(valid => {
        if (!valid) {
          return
        }
        validateText(this.testForm).then(response => {
          if (response.data.length === 0) {
            this.$modal.msgSuccess(this.$t('system.doesNotContainSensitiveWords'))
            return
          }
          this.$modal.msgWarning('包含敏感词：' + response.data.join(', '))
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除敏感词编号为"' + id + '"的数据项?').then(function() {
        return deleteSensitiveWord(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行导出
      this.$modal.confirm(this.$t('system.areYouSureToExportAllSensitiveWordDataItems')).then(() => {
        this.exportLoading = true
        return exportSensitiveWordExcel(params)
      }).then(response => {
        this.$download.excel(response, '${table.classComment}.xlsx')
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss">
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
