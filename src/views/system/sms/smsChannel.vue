<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('system.smsSignature')" prop="signature">
        <el-input
          v-model="queryParams.signature"
          :placeholder="$t('system.pleaseEnterSmsSignature')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.enableStatus')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('system.pleaseSelectTheEnablingStatus')" clearable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.createTime')">
        <el-date-picker
          v-model="dateRangeCreateTime"
          end-placeholder="结束日期"
          range-separator="-"
          start-placeholder="开始日期"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:sms-channel:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('order.number')" align="center" prop="id" />
      <el-table-column :label="$t('system.smsSignature')" align="center" prop="signature" />
      <el-table-column :label="$t('system.channelCode')" align="center" prop="code">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_SMS_CHANNEL_CODE" :value="scope.row.code" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.enableStatus')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      >
      <el-table-column :label="$t('common.remarks')" align="center" prop="remark" />
      <el-table-column :label="$t('system.smsApiAccount')" align="center" prop="apiKey" />
      <el-table-column :label="$t('system.keyOfSmsApi')" align="center" prop="apiSecret" />
      <el-table-column :label="$t('system.smsSendingCallbackUrl')" align="center" prop="callbackUrl" />
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:sms-channel:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:sms-channel:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-form-item :label="$t('system.smsSignature')" prop="signature">
          <el-input v-model="form.signature" :placeholder="$t('system.pleaseEnterSmsSignature')" />
        </el-form-item>
        <el-form-item :label="$t('system.channelCode')" prop="code">
          <el-select v-model="form.code" :placeholder="$t('system.pleaseSelectAChannelCode')" clearable>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_SMS_CHANNEL_CODE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('common.enableStatus')">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('common.remarks')" prop="remark">
          <el-input v-model="form.remark" :placeholder="$t('rfq.pleaseEnterComments')" />
        </el-form-item>
        <el-form-item :label="$t('system.smsApiAccount')" prop="apiKey">
          <el-input v-model="form.apiKey" :placeholder="$t('system.pleaseEnterTheAccountOfSmsApi')" />
        </el-form-item>
        <el-form-item :label="$t('system.keyOfSmsApi')" prop="apiSecret">
          <el-input v-model="form.apiSecret" :placeholder="$t('system.pleaseEnterTheKeyOfSmsApi')" />
        </el-form-item>
        <el-form-item :label="$t('system.smsSendingCallbackUrl')" prop="callbackUrl">
          <el-input v-model="form.callbackUrl" :placeholder="$t('system.pleaseEnterTheSmsSendingCallbackUrl')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createSmsChannel,
  deleteSmsChannel,
  getSmsChannel,
  getSmsChannelPage,
  updateSmsChannel
} from '@/api/system/sms/smsChannel'

export default {
  name: 'Smschannel',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 短信渠道列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        signature: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        signature: [{ required: true, message: this.$t('system.smsSignatureCannotBeEmpty'), trigger: 'blur' }],
        code: [{ required: true, message: this.$t('system.channelCodeCannotBeEmpty'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('system.theEnablingStatusCannotBeEmpty'), trigger: 'blur' }],
        apiKey: [{ required: true, message: this.$t('system.theAccountOfSmsApiCannotBeEmpty'), trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getSmsChannelPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        signature: undefined,
        code: undefined,
        status: undefined,
        remark: undefined,
        apiKey: undefined,
        apiSecret: undefined,
        callbackUrl: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addSmsChannel')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getSmsChannel(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifySmsChannel')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateSmsChannel(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createSmsChannel(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除短信渠道编号为"' + id + '"的数据项?').then(function() {
        return deleteSmsChannel(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    }
  }
}
</script>
