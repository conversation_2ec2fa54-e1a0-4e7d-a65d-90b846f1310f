<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="100px" size="small">
      <el-form-item :label="$t('common.cellphoneNumber')" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          :placeholder="$t('system.pleaseEnterYourMobileNumber')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.smsChannel')" prop="channelId">
        <el-select v-model="queryParams.channelId" :placeholder="$t('system.pleaseSelectSmsChannel')" clearable>
          <el-option
            v-for="channel in channelOptions"
            :key="channel.id"
            :label="channel.signature + '【' + getDictDataLabel(DICT_TYPE.SYSTEM_SMS_CHANNEL_CODE, channel.code) + '】'"
            :value="channel.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.templateNo')" prop="templateId">
        <el-input
          v-model="queryParams.templateId"
          :placeholder="$t('system.pleaseEnterTheTemplateNumber')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.sendStatus')" prop="sendStatus">
        <el-select v-model="queryParams.sendStatus" :placeholder="$t('system.pleaseSelectTheSendingStatus')" clearable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_SMS_SEND_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.sendingTime')">
        <el-date-picker
          v-model="dateRangeSendTime"
          end-placeholder="结束日期"
          range-separator="-"
          start-placeholder="开始日期"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item :label="$t('system.receivingStatus')" prop="receiveStatus">
        <el-select
          v-model="queryParams.receiveStatus"
          :placeholder="$t('system.pleaseSelectTheReceivingStatus')"
          clearable
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_SMS_RECEIVE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.receivingTime')">
        <el-date-picker
          v-model="dateRangeReceiveTime"
          end-placeholder="结束日期"
          range-separator="-"
          start-placeholder="开始日期"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:sms-log:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:sms-log:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t('order.number')" align="center" prop="id" />
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.cellphoneNumber')" align="center" prop="mobile" width="120">
        <template slot-scope="scope">
          <div>{{ scope.row.mobile }}</div>
          <div v-if="scope.row.userType && scope.row.userId">
            <dict-tag :type="DICT_TYPE.USER_TYPE" :value="scope.row.userType" />
            {{ '(' + scope.row.userId + ')' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.smsContent')" align="center" prop="templateContent" width="300" />
      <el-table-column :label="$t('system.sendStatus')" align="center" width="180">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_SMS_SEND_STATUS" :value="scope.row.sendStatus" />
          <div>{{ parseTime(scope.row.sendTime) }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.receivingStatus')" align="center" width="180">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_SMS_RECEIVE_STATUS" :value="scope.row.receiveStatus" />
          <div>{{ parseTime(scope.row.receiveTime) }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.smsChannel')" align="center" width="120">
        <template slot-scope="scope">
          <div>{{ formatChannelSignature(scope.row.channelId) }}</div>
          <dict-tag :type="DICT_TYPE.SYSTEM_SMS_CHANNEL_CODE" :value="scope.row.channelCode" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.templateNo')" align="center" prop="templateId" />
      <el-table-column :label="$t('system.smsType')" align="center" prop="templateType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_SMS_TEMPLATE_TYPE" :value="scope.row.templateType" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:sms-log:query']"
            icon="el-icon-view"
            size="mini"
            type="text"
            @click="handleView(scope.row,scope.index)"
          >详细
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 短信日志详细 -->
    <el-dialog :title="$t('system.smsLogDetails')" :visible.sync="open" append-to-body width="700px">
      <el-form ref="form" :model="form" label-width="140px" size="mini">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('system.logPrimaryKey')">{{ form.id }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.smsChannel')">
              {{ formatChannelSignature(form.channelId) }}
              <dict-tag :type="DICT_TYPE.SYSTEM_SMS_CHANNEL_CODE" :value="form.channelCode" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.smsTemplate')">
              {{ form.templateId }} | {{ form.templateCode }}
              <dict-tag :type="DICT_TYPE.SYSTEM_SMS_TEMPLATE_TYPE" :value="form.templateType" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.apiTemplateNumber')">{{ form.apiTemplateId }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.userInformation')">{{ form.mobile }}
              <span v-if="form.userType && form.userId">
                <dict-tag :type="DICT_TYPE.USER_TYPE" :value="form.userType" />({{ form.userId }})
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.smsContent')">{{ form.templateContent }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.smsParameters')">{{ form.templateParams }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('common.createTime')">{{ parseTime(form.createTime) }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.sendStatus')">
              <dict-tag :type="DICT_TYPE.SYSTEM_SMS_SEND_STATUS" :value="form.sendStatus" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.sendingTime')">{{ parseTime(form.sendTime) }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.sendResults')">{{ form.sendCode }} | {{ form.sendMsg }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.apiSendResults')">{{ form.apiSendCode }} | {{
              form.apiSendMsg
            }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.apiSmsNo')">{{ form.apiSerialNo }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.apiRequestNumber')">{{ form.apiRequestId }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.receivingStatus')">
              <dict-tag :type="DICT_TYPE.SYSTEM_SMS_RECEIVE_STATUS" :value="form.receiveStatus" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.receivingTime')">{{ parseTime(form.receiveTime) }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.apiReceivingResults')">{{ form.apiReceiveCode }} | {{ form.apiReceiveMsg }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { exportSmsLogExcel, getSmsLogPage } from '@/api/system/sms/smsLog'
import { getSimpleSmsChannels } from '@/api/system/sms/smsChannel'

export default {
  name: 'Smslog',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 短信日志列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeSendTime: [],
      dateRangeReceiveTime: [],
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        channelId: null,
        templateId: null,
        mobile: null,
        sendStatus: null,
        receiveStatus: null
      },
      // 短信渠道
      channelOptions: []
    }
  },
  created() {
    this.getList()
    // 获得短信渠道
    getSimpleSmsChannels().then(response => {
      this.channelOptions = response.data
    })
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeSendTime, 'sendTime')
      this.addBeginAndEndTime(params, this.dateRangeReceiveTime, 'receiveTime')
      // 执行查询
      getSmsLogPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeSendTime = []
      this.dateRangeReceiveTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeSendTime, 'sendTime')
      this.addBeginAndEndTime(params, this.dateRangeReceiveTime, 'receiveTime')
      // 执行导出
      this.$modal.confirm(this.$t('system.areYouSureToExportAllSmsLogDataItems')).then(() => {
        this.exportLoading = true
        return exportSmsLogExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('system.smsLogxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true
      this.form = row
    },
    /** 格式化短信渠道 */
    formatChannelSignature(channelId) {
      for (const channel of this.channelOptions) {
        if (channel.id === channelId) {
          return channel.signature
        }
      }
      return '找不到签名：' + channelId
    }
  }
}
</script>
