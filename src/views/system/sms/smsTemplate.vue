<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="150px" size="small">
      <el-form-item :label="$t($t('system.smsType'))" prop="type">
        <el-select v-model="queryParams.type" :placeholder="$t($t('system.pleaseSelectAMessageType'))" clearable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_SMS_TEMPLATE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t($t('system.openState'))" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t($t('system.pleaseSelectTheOpenState'))" clearable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t($t('system.templateCode'))" prop="code">
        <el-input
          v-model="queryParams.code"
          :placeholder="$t($t('system.pleaseEnterTheTemplateCode'))"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t($t('system.templateNumberOfSmsApi'))" prop="apiTemplateId">
        <el-input
          v-model="queryParams.apiTemplateId"
          :placeholder="$t($t('system.pleaseEnterTheTemplateNumberOfSmsApi'))"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t($t('system.smsChannel'))" prop="channelId">
        <el-select v-model="queryParams.channelId" :placeholder="$t($t('system.pleaseSelectSmsChannel'))" clearable>
          <el-option
            v-for="channel in channelOptions"
            :key="channel.id"
            :label="channel.signature + '【' + getDictDataLabel(DICT_TYPE.SYSTEM_SMS_CHANNEL_CODE, channel.code) + '】'"
            :value="channel.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t($t('common.createTime'))">
        <el-date-picker
          v-model="dateRangeCreateTime"
          end-placeholder="结束日期"
          range-separator="-"
          start-placeholder="开始日期"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t($t('common.search')) }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t($t('common.reset')) }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:sms-template:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t($t('common.add')) }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:sms-template:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >{{ $t($t('common.export')) }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="$t($t('system.templateCode'))" align="center" prop="code" />
      <el-table-column :label="$t($t('rfq.templateName'))" align="center" prop="name" />
      <el-table-column :label="$t($t('system.templateContent'))" align="center" prop="content" width="300" />
      <el-table-column :label="$t($t('system.smsType'))" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_SMS_TEMPLATE_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="$t($t('system.openState'))" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t($t('common.remarks'))" align="center" prop="remark" />
      <el-table-column
        :label="$t($t('system.templateNumberOfSmsApi'))"
        align="center"
        prop="apiTemplateId"
        width="180"
      />
      <el-table-column :label="$t($t('system.smsChannel'))" align="center" width="120">
        <template slot-scope="scope">
          <div>{{ formatChannelSignature(scope.row.channelId) }}</div>
          <dict-tag :type="DICT_TYPE.SYSTEM_SMS_CHANNEL_CODE" :value="scope.row.channelCode" />
        </template>
      </el-table-column>
      <el-table-column :label="$t($t('common.createTime'))" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t($t('common.operate'))"
        align="center"
        class-name="small-padding fixed-width"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:sms-template:send-sms']"
            icon="el-icon-share"
            size="mini"
            type="text"
            @click="handleSendSms(scope.row)"
          >{{ $t($t('system.test')) }}
          </el-button>
          <el-button
            v-hasPermi="['system:sms-template:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t($t('common.modify')) }}
          </el-button>
          <el-button
            v-hasPermi="['system:sms-template:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t($t('common.del')) }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item :label="$t($t('system.smsChannelNumber'))" prop="channelId">
          <el-select v-model="form.channelId" :placeholder="$t($t('system.pleaseSelectTheSmsChannelNumber'))">
            <el-option
              v-for="channel in channelOptions"
              :key="channel.id"
              :label="channel.signature + '【' + getDictDataLabel(DICT_TYPE.SYSTEM_SMS_CHANNEL_CODE, channel.code) + '】'"
              :value="channel.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t($t('system.smsType'))" prop="type">
          <el-select v-model="form.type" :placeholder="$t($t('system.pleaseSelectAMessageType'))">
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_SMS_TEMPLATE_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t($t('system.templateNo'))" prop="code">
          <el-input v-model="form.code" :placeholder="$t($t('system.pleaseEnterTheTemplateNumber'))" />
        </el-form-item>
        <el-form-item :label="$t($t('rfq.templateName'))" prop="name">
          <el-input v-model="form.name" :placeholder="$t($t('system.pleaseEnterATemplateName'))" />
        </el-form-item>
        <el-form-item :label="$t($t('system.templateContent'))" prop="content">
          <el-input
            v-model="form.content"
            :placeholder="$t($t('system.pleaseEnterTheTemplateContent'))"
            type="textarea"
          />
        </el-form-item>
        <el-form-item :label="$t($t('system.openState'))" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t($t('system.smsApiTemplateNumber'))" prop="apiTemplateId">
          <el-input v-model="form.apiTemplateId" :placeholder="$t($t('system.pleaseEnterTheTemplateNumberOfSmsApi'))" />
        </el-form-item>
        <el-form-item :label="$t($t('common.remarks'))" prop="remark">
          <el-input v-model="form.remark" :placeholder="$t($t('rfq.pleaseEnterComments'))" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t($t('common.cancel')) }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t($t('order.determine')) }}</el-button>

      </div>
    </el-dialog>

    <!-- 对话框(发送短信) -->
    <el-dialog :title="$t($t('system.testSendingSms'))" :visible.sync="sendSmsOpen" append-to-body width="500px">
      <el-form ref="sendSmsForm" :model="sendSmsForm" :rules="sendSmsRules" label-width="140px">
        <el-form-item :label="$t($t('system.templateContent'))" prop="content">
          <el-input
            v-model="sendSmsForm.content"
            :placeholder="$t($t('system.pleaseEnterTheTemplateContent'))"
            readonly
            type="textarea"
          />
        </el-form-item>
        <el-form-item :label="$t($t('common.cellphoneNumber'))" prop="mobile">
          <el-input v-model="sendSmsForm.mobile" :placeholder="$t($t('system.pleaseEnterYourMobileNumber'))" />
        </el-form-item>
        <el-form-item
          v-for="param in sendSmsForm.params"
          :key="param"
          :label="'参数 {' + param + '}'"
          :prop="'templateParams.' + param"
        >
          <el-input v-model="sendSmsForm.templateParams[param]" :placeholder="'请输入 ' + param + ' 参数'" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelSendSms">{{ $t($t('common.cancel')) }}</el-button>
        <el-button type="primary" @click="submitSendSmsForm">{{ $t($t('order.determine')) }}</el-button>

      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  createSmsTemplate,
  deleteSmsTemplate,
  exportSmsTemplateExcel,
  getSmsTemplate,
  getSmsTemplatePage,
  sendSms,
  updateSmsTemplate
} from '@/api/system/sms/smsTemplate'
import { getSimpleSmsChannels } from '@/api/system/sms/smsChannel'

export default {
  name: 'Smstemplate',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 短信模板列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        type: null,
        status: null,
        code: null,
        content: null,
        apiTemplateId: null,
        channelId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        type: [{ required: true, message: this.$t('system.smsTypeCannotBeEmpty'), trigger: 'change' }],
        status: [{ required: true, message: this.$t('system.theOpeningStatusCannotBeEmpty'), trigger: 'blur' }],
        code: [{ required: true, message: this.$t('system.templateCodeCannotBeEmpty'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('system.templateNameCannotBeEmpty'), trigger: 'blur' }],
        content: [{ required: true, message: this.$t('system.templateContentCannotBeEmpty'), trigger: 'blur' }],
        apiTemplateId: [{
          required: true,
          message: this.$t('system.theTemplateNumberOfSmsApiCannotBeEmpty'),
          trigger: 'blur'
        }],
        channelId: [{ required: true, message: this.$t('system.smsChannelNumberCannotBeEmpty'), trigger: 'change' }]
      },
      // 短信渠道
      channelOptions: [],
      // 发送短信
      sendSmsOpen: false,
      sendSmsForm: {
        params: [] // 模板的参数列表
      },
      sendSmsRules: {
        mobile: [{ required: true, message: this.$t('system.mobilePhoneCannotBeEmpty'), trigger: 'blur' }],
        templateCode: [{ required: true, message: this.$t('system.mobilePhoneCannotBeEmpty'), trigger: 'blur' }],
        templateParams: {}
      }
    }
  },
  created() {
    this.getList()
    // 获得短信渠道
    getSimpleSmsChannels().then(response => {
      this.channelOptions = response.data
    })
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getSmsTemplatePage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        type: undefined,
        status: undefined,
        code: undefined,
        name: undefined,
        content: undefined,
        remark: undefined,
        apiTemplateId: undefined,
        channelId: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('system.addSmsTemplate')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getSmsTemplate(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifySmsTemplate')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateSmsTemplate(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createSmsTemplate(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除短信模板编号为"' + id + '"的数据项?').then(function() {
        return deleteSmsTemplate(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行导出
      this.$modal.confirm(this.$t('system.areYouSureToExportAllSmsTemplateDataItems'), this.$t('system.warning')).then(() => {
        this.exportLoading = true
        return exportSmsTemplateExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('system.smsTemplatexls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    /** 发送短息按钮 */
    handleSendSms(row) {
      this.resetSendSms(row)
      // 设置参数
      this.sendSmsForm.content = row.content
      this.sendSmsForm.params = row.params
      this.sendSmsForm.templateCode = row.code
      this.sendSmsForm.templateParams = row.params.reduce(function(obj, item) {
        obj[item] = undefined
        return obj
      }, {})
      // 根据 row 重置 rules
      this.sendSmsRules.templateParams = row.params.reduce(function(obj, item) {
        obj[item] = { required: true, message: '参数 ' + item + ' 不能为空', trigger: 'change' }
        return obj
      }, {})
      // 设置打开
      this.sendSmsOpen = true
    },
    /** 重置发送短信的表单 */
    resetSendSms() {
      // 根据 row 重置表单
      this.sendSmsForm = {
        content: undefined,
        params: undefined,
        mobile: undefined,
        templateCode: undefined,
        templateParams: {}
      }
      this.resetForm('sendSmsForm')
    },
    /** 取消发送短信 */
    cancelSendSms() {
      this.sendSmsOpen = false
      this.resetSendSms()
    },
    /** 提交按钮 */
    submitSendSmsForm() {
      this.$refs['sendSmsForm'].validate(valid => {
        if (!valid) {
          return
        }
        // 添加的提交
        sendSms(this.sendSmsForm).then(response => {
          this.$modal.msgSuccess(this.$t('system.submitAndSendSuccessfullySeeSendingLogNoForSendingResults') + response.data)
          this.sendSmsOpen = false
        })
      })
    },
    /** 格式化短信渠道 */
    formatChannelSignature(channelId) {
      for (const channel of this.channelOptions) {
        if (channel.id === channelId) {
          return channel.signature
        }
      }
      return '找不到签名：' + channelId
    }
  }
}
</script>
