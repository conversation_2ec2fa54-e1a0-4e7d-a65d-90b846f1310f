<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="110px" size="small">
      <el-form-item :label="$t('system.labelCoding')" prop="code">
        <el-input
          v-model="queryParams.code"
          :placeholder="$t('system.pleaseEnterTheCodeOfTheLabel')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.language')" prop="locale">

        <el-select
          v-model="queryParams.locale"
          :placeholder="$t('system.pleaseSelectALanguage')"
          clearable
          @keyup.enter.native="handleQuery"
        >
          <el-option v-for="item in translationList" :key="item.id" :label="item.name" :value="item.locale" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.labelLanguageDescription')" prop="translation">
        <el-input
          v-model="queryParams.translation"
          :placeholder="$t('system.pleaseEnterTheLabelLanguageDescription')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('system.modular')" prop="moduleId">
        <el-select
          v-model="queryParams.moduleId"
          :placeholder="$t('system.pleaseSelectAModule')"
          clearable
          @keyup.enter.native="handleQuery"
        >
          <el-option v-for="item in moduleList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          v-hasPermi="['system:translation-label:query']"
          icon="el-icon-search"
          type="primary"
          @click="handleQuery"
        >{{ $t('common.search') }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:translation-label:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        :label="$t('system.code')"
        align="center"
        prop="code"
      />
      <el-table-column
        :label="$t('system.language')"
        align="center"
        prop="localeName"
      />
      <el-table-column
        :label="$t('common.name')"
        align="center"
        prop="translation"
      />
      <el-table-column
        :label="$t('system.modular')"
        align="center"
        prop="moduleName"
      />
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:translation-label:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:translation-label:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-form-item :label="$t('system.labelCoding')" prop="code">
          <el-input v-model="form.code" :disabled="codeDisabled" :placeholder="$t('system.pleaseEnterTheCodeOfTheLabel')" maxlength="100" />
        </el-form-item>
        <el-form-item :label="$t('system.language')" prop="locale">
          <el-select
            v-model="form.locale"
            :placeholder="$t('system.pleaseSelectALanguage')"
            clearable
            style="width: 100%"
            @keyup.enter.native="handleQuery"
          >
            <el-option
              v-for="item in translationList"
              :key="item.id"
              :label="item.name"
              :value="item.locale"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('system.labelLanguageDescription')" prop="translation">
          <el-input v-model="form.translation" :placeholder="$t('system.pleaseEnterTheLabelLanguageDescription')" maxlength="1000" />
        </el-form-item>
        <el-form-item :label="$t('system.modular')" prop="moduleId">
          <el-select
            v-model="form.moduleId"
            :placeholder="$t('system.pleaseSelectAModule')"
            clearable
            style="width: 100%"
            @keyup.enter.native="handleQuery"
          >
            <el-option v-for="item in moduleList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createTranslationLabel,
  deleteTranslationLabel,
  exportTranslationLabelExcel,
  getAllTranslationLabel,
  getTranslationLabel,
  getTranslationLabelPage,
  updateTranslationLabel
} from '@/api/system/translationLabel'
import { getAllModule } from '@/api/system/module'

export default {
  name: 'Translationlabel',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 系统标签多语言翻译表（租户通用标签翻译数据）列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        code: null,
        locale: null,
        translation: null,
        moduleId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        code: [{ required: true, message: this.$t('system.theTagCodeCannotBeEmpty'), trigger: 'blur' }],
        locale: [{ required: true, message: this.$t('system.languageCannotBeEmpty'), trigger: 'blur' }],
        translation: [{
          required: true,
          message: this.$t('system.theLabelLanguageDescriptionCannotBeEmpty'),
          trigger: 'blur'
        }],
        moduleId: [{ required: true, message: this.$t('system.moduleIdCannotBeEmpty'), trigger: 'blur' }]
      },
      moduleList: [],
      translationList: [],
      codeDisabled: false
    }
  },
  created() {
    this.getSelectOption()
    this.getList()
  },
  methods: {
    getSelectOption() {
      getAllModule().then(res => {
        this.moduleList = res.data
      })
      getAllTranslationLabel().then(res => {
        this.translationList = res.data
      })
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getTranslationLabelPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        code: undefined,
        locale: undefined,
        translation: undefined,
        moduleId: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.codeDisabled = false
      this.reset()
      this.open = true
      this.title = this.$t('system.addSystemLabelMultilingualTranslationTabletenantGeneralLabelTranslationData')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.codeDisabled = true
      this.reset()
      const id = row.id
      getTranslationLabel(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyTheMultilingualTranslationTableOfSystemLabelstenantsCommonLabelTranslationData')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        // 修改的提交
        if (this.form.id != null) {
          updateTranslationLabel(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createTranslationLabel(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      const code = row.code
      this.$modal.confirm('是否确认删除系统标签多语言翻译表（租户通用标签翻译数据）编号为"' + code + '"的数据项?').then(function() {
        return deleteTranslationLabel(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行导出
      this.$modal.confirm(this.$t('system.toExportAllSystemLabelMultilingualTranslationTablestenantCommonLabelTranslationDataDataItems')).then(() => {
        this.exportLoading = true
        return exportTranslationLabelExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('system.systemLabelMultilingualTranslationTabletenantGeneralLabelTranslationDataXls'))
        this.exportLoading = false
      }).catch(() => {
      })
    }
  }
}
</script>
