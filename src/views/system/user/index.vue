<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input v-model="deptName" :placeholder="$t('system.pleaseEnterTheDepartmentName')" clearable size="small"
                    prefix-icon="el-icon-search" style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
              ref="tree"
              :data="deptOptions"
              :props="defaultProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              default-expand-all
              highlight-current
              @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true"
                 label-width="68px"
        >
          <el-form-item :label="$t('common.userName')" prop="nickname">
            <el-input
                v-model="queryParams.nickname"
                clearable
                :placeholder="$t('system.pleaseEnterTheUserName')"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item :label="$t('system.userId')" prop="userCode">
            <el-input
                v-model="queryParams.userCode"
                clearable
                :placeholder="$t('system.pleaseEnterTheUserId')"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item :label="$t('system.userAccount')" prop="username">
            <el-input
                v-model="queryParams.username"
                :placeholder="$t('system.pleaseEnterTheAccountNumber')"
                clearable
                style="width: 240px"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item :label="$t('common.status')" prop="status">
            <el-select v-model="queryParams.status" :placeholder="$t('system.userStatus')" clearable
                       style="width: 240px"
            >
              <el-option v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="dict.label"
                         :value="parseInt(dict.value)"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('common.createTime')">
            <el-date-picker
                v-model="dateRange"
                style="width: 240px"
                value-format="yyyy-MM-dd"
                type="daterange"
                :end-placeholder="$t('common.endDate')"
                :range-separator="$t('order.to')"
                :start-placeholder="$t('common.startDate')"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                v-hasPermi="['system:user:create']"
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
            >{{ $t('common.add') }}
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                v-hasPermi="['system:user-leader-rel:save']"
                type="info"
                plain
                icon="el-icon-s-check"
                size="mini"
                @click="handleUserLeader"
            >{{ $t('system.assignParent') }}
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                plain
                size="mini"
                @click="handleImport"
            >{{ $t('导入') }}
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                plain
                size="mini"
                @click="handleCustomer"
            >{{ $t('分配客户') }}
            </el-button>
          </el-col>
          <right-toolbar :show-search.sync="showSearch" :columns="columns" @queryTable="getList"/>
        </el-row>

        <el-table ref="multipleUserTable" v-loading="loading" border :data="userList"
                  @selection-change="handleSelectionChange" @row-click="clickRow"
        >
          <el-table-column
              type="selection"
              width="30"
          />
          <el-table-column
              v-if="columns[0].visible"
              key="id"
              :label="$t('system.userNo')"
              align="center"
              prop="id"
          />
          <el-table-column
              v-if="columns[1].visible"
              key="nickname"
              :label="$t('common.userName')"
              align="center"
              prop="nickname"
              :show-overflow-tooltip="true"
          />
          <el-table-column
              v-if="columns[2].visible"
              key="userCode"
              :show-overflow-tooltip="true"
              align="center"
              :label="$t('system.userId')"
              prop="userCode"
          />
          <el-table-column
              v-if="columns[3].visible"
              key="username"
              :label="$t('system.userAccount')"
              align="center"
              prop="username"
              :show-overflow-tooltip="true"
          />
          <el-table-column
              v-if="columns[4].visible"
              key="leaderName"
              :label="$t('system.superiorLeaders')"
              align="center"
              prop="leaderName"
              width="120"
          />
          <el-table-column
              v-if="columns[5].visible"
              key="deptName"
              :label="$t('system.department')"
              align="center"
              prop="dept.name"
              :show-overflow-tooltip="true"
          />
          <el-table-column
              v-if="columns[6].visible"
              key="mobile"
              :label="$t('system.phoneNumber')"
              align="center"
              prop="mobile"
              width="120"
          />
          <el-table-column
              v-if="columns[7].visible"
              key="passwordExpiredPeriod"
              :label="$t('密码失效周期（天）')"
              align="center"
              prop="passwordExpiredPeriod"
              width="100"
          />
          <el-table-column
              v-if="columns[8].visible"
              key="passwordExpiredTime"
              :label="$t('密码到期时间')"
              align="center"
              prop="passwordExpiredTime"
              width="160"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.passwordExpiredTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
              v-if="columns[9].visible"
              key="status"
              :label="$t('common.status')"
              align="center"
          >
            <template slot-scope="scope">
              <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
                         @change="handleStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column v-if="columns[10].visible" :label="$t('common.createTime')" align="center" prop="createTime"
                           width="160"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns[11].visible"
            key="incapCustomer"
            :label="$t('incap客户')"
            align="center"
            prop="incapCustomer"
            width="120"
          />
          <el-table-column :label="$t('common.operate')" align="center" width="160"
                           class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                  v-hasPermi="['system:user:update']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
              >{{ $t('common.modify') }}
              </el-button>
              <el-dropdown
                  v-hasPermi="['system:user:delete', 'system:user:update-password', 'system:permission:assign-user-role', 'system:user-purchase-org:assign-user-purchase-org']"
                  @command="(command) => handleCommand(command, scope.$index, scope.row)"
              >
                <span class="el-dropdown-link">
                  <i class="el-icon-d-arrow-right el-icon--right"/>{{ $t('system.more') }}
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                      v-if="scope.row.id !== 1"
                      v-hasPermi="['system:user:delete']"
                      command="handleDelete"
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                  >{{ $t('common.del') }}
                  </el-dropdown-item>
                  <el-dropdown-item
                      v-if="!scope.row.username"
                      v-hasPermi="['system:user:create-account']"
                      command="handleCreateAccount"
                      size="mini"
                      type="text"
                      icon="el-icon-key"
                  >{{ $t('system.createAccount') }}
                  </el-dropdown-item>
                  <el-dropdown-item
                      v-if="scope.row.username&&systemPasswordUpdate"
                      v-hasPermi="['system:user:update-password']"
                      command="handleResetPwd"
                      size="mini"
                      type="text"
                      icon="el-icon-key"
                  >{{ $t('system.resetPassword') }}
                  </el-dropdown-item>
                  <el-dropdown-item
                      v-hasPermi="['system:permission:assign-user-role']"
                      command="handleRole"
                      size="mini"
                      type="text"
                      icon="el-icon-circle-check"
                  >{{ $t('system.assignRoles') }}
                  </el-dropdown-item>
                  <el-dropdown-item
                      v-hasPermi="['system:user-purchase-org:assign-user-purchase-org']"
                      command="handleOrg"
                      icon="el-icon-s-grid"
                      size="mini"
                      type="text"
                  >{{ $t('system.assignPurchaseOrganization') }}
                  </el-dropdown-item>
                  <el-dropdown-item
                      v-hasPermi="['system:user-company:assign-user-company']"
                      command="handleCompany"
                      icon="el-icon-s-grid"
                      size="mini"
                      type="text"
                  >{{ $t('system.assignResponsibleCompanies') }}
                  </el-dropdown-item>
                  <!-- 仅管理员、超级管理员允许操作-->
                  <el-dropdown-item
                      v-hasPermi="['system:mock:user']"
                      command="mockAccount"
                      icon="el-icon-s-grid"
                      size="mini"
                      type="text"
                  >{{ $t('supplier.simulatedAccount') }}
                  </el-dropdown-item>
                  <el-dropdown-item
                      v-hasPermi="['system:user:save-handoff']"
                      command="handoverPersonnel"
                      icon="el-icon-s-grid"
                      size="mini"
                      type="text"
                  >{{ $t('system.personnelHandover') }}
                  </el-dropdown-item>
                  <el-dropdown-item
                      v-hasPermi="['system:user:setPasswordExpiredPeriod']"
                      command="setPasswordExpiredPeriod"
                      v-if="scope.row.passwordExpiredPeriod"
                      icon="el-icon-s-grid"
                      size="mini"
                      type="text"
                  >{{ $t('设置密码失效周期') }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNo"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />
      </el-col>
    </el-row>
    <el-dialog :title="upload.title" :visible.sync="upload.open" append-to-body width="400px">
      <div class="text-center">
        <el-upload
            ref="upload"
            :action="upload.url"
            :auto-upload="false"
            :disabled="upload.isUploading"
            :headers="upload.headers"
            :limit="1"
            :on-preview="onPreview"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            accept=".xlsx, .xls"
            class="small-padding"
            drag
        >
          <i class="el-icon-upload"/>
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button plain type="primary" @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改参数配置对话框 -->
    <el-dialog v-if="open" :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('common.userName')" prop="nickname">
              <el-input v-model="form.nickname" :placeholder="$t('system.pleaseEnterTheUserName')"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.department')" prop="deptId">
              <treeselect
                  ref="treeselect"
                  v-model="form.deptId"
                  :options="deptOptions"
                  :show-count="true"
                  :clearable="false"
                  :placeholder="$t('system.pleaseSelectTheDepartment')"
                  :normalizer="normalizer"
                  @open="expandNodeBySelectedItem"
              >
                <label slot="option-label" slot-scope="{node,labelClassName}" :class="labelClassName"
                       :title="node.label"
                >
                  {{ node.label }}
                </label>
              </treeselect>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('system.userId')" prop="nickname">
              <el-input v-model="form.userCode" :placeholder="$t('system.pleaseEnterTheUserId')"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.userGender')">
              <el-select v-model="form.sex" :placeholder="$t('common.pleaseSelect')">
                <el-option v-for="dict in sexDictDatas" :key="parseInt(dict.value)" :label="dict.label"
                           :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('system.phoneNumber')" prop="mobile">
              <el-input v-model="form.mobile" :placeholder="$t('system.pleaseEnterYourMobileNumber')" maxlength="11"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('supplier.mailbox')" prop="email">
              <el-input v-model="form.email" :placeholder="$t('supplier.pleaseEnterEmail')" maxlength="50"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>

          <el-col :span="12">
            <el-form-item :label="$t('system.post')">
              <el-select v-model="form.postIds" multiple :placeholder="$t('common.pleaseSelect')">
                <el-option
                    v-for="item in postOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('common.remarks')">
              <el-input
                  v-model="form.remark"
                  type="textarea"
                  :placeholder="$t('rfq.pleaseEnterTheContent')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!-- 初始化账户-->
    <el-dialog :title="title" :visible.sync="openAccount" width="600px" append-to-body>
      <el-form ref="formAccount" :model="form" :rules="accountRules" label-width="160px">
        <el-form-item :label="$t('system.userId')">
          <el-input v-model="form.userCode" :disabled="true"/>
        </el-form-item>
        <el-form-item :label="$t('common.userName')">
          <el-input v-model="form.nickname" :disabled="true"/>
        </el-form-item>
        <el-form-item :label="$t('system.userAccount')" prop="username">
          <el-input v-model="form.username" :placeholder="$t('system.pleaseEnterTheUserAccount')"/>
        </el-form-item>
        <el-form-item :label="$t('system.userPassword')" prop="password">
          <el-input v-model="form.password" :placeholder="$t('system.pleaseEnterTheUserPassword')" type="password"
                    show-password
          />
        </el-form-item>
        <el-form-item :label="$t('密码失效周期（天）')" prop="passwordExpiredPeriod">
          <el-input-number v-model="form.passwordExpiredPeriod" :placeholder="$t('请输入密码失效周期（天）')" :min="1"
                           :precision="0" style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAccount">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitAccount">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!-- 重置密码-->
    <el-dialog :title="title" :visible.sync="openResetPwd" width="600px" append-to-body>
      <el-form ref="formResetPwd" :model="form" :rules="resetPwdRules" label-width="80px">
        <el-form-item :label="$t('system.userId')">
          <el-input v-model="form.userCode" :disabled="true"/>
        </el-form-item>
        <el-form-item :label="$t('common.userName')">
          <el-input v-model="form.nickname" :disabled="true"/>
        </el-form-item>
        <el-form-item :label="$t('system.userAccount')">
          <el-input v-model="form.username" :disabled="true"/>
        </el-form-item>
        <el-form-item :label="$t('system.userPassword')" prop="password">
          <el-input v-model="form.password" :placeholder="$t('system.pleaseEnterTheUserPassword')" type="password"
                    show-password
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelResetPwd">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitResetPwd">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!-- 分配角色 -->
    <el-dialog :title="$t('system.assignRoles')" :visible.sync="openRole" width="500px" append-to-body>
      <el-form :model="form" label-width="80px">
        <el-form-item :label="$t('system.userId')">
          <el-input v-model="form.userCode" :disabled="true"/>
        </el-form-item>
        <el-form-item :label="$t('common.userName')">
          <el-input v-model="form.nickname" :disabled="true"/>
        </el-form-item>
        <el-form-item :label="$t('system.role')">
          <el-select v-model="form.roleIds" :placeholder="$t('common.pleaseSelect')" class="searchValue" clearable
                     filterable multiple
          >
            <el-option
                v-for="item in roleOptions"
                :key="parseInt(item.id)"
                :label="item.name"
                :value="parseInt(item.id)"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelRole">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitRole">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!--赋予采购组织页面-->
    <el-dialog :visible.sync="openOrg" append-to-body :title="$t('system.assignPurchaseOrganization')" width="500px">
      <el-form :model="form" label-width="80px">
        <el-form-item :label="$t('system.userId')">
          <el-input v-model="form.userCode" :disabled="true"/>
        </el-form-item>
        <el-form-item :label="$t('common.userName')">
          <el-input v-model="form.nickname" :disabled="true"/>
        </el-form-item>
        <el-form-item :label="$t('supplier.purchasingOrganization')">
          <el-select v-model="form.purchaseOrgs" :placeholder="$t('common.pleaseSelect')" class="searchValue" clearable
                     filterable multiple
          >
            <el-option
                v-for="item in orgOptions"
                :key="parseInt(item.id)"
                :label="item.name"
                :value="parseInt(item.id)"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelOrg">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitOrg">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
    <!--赋予公司页面-->
    <el-dialog :visible.sync="openCompany" append-to-body :title="$t('system.responsibleForTheCompany')" width="500px">
      <el-form :model="form" label-width="80px">
        <el-form-item :label="$t('system.userId')">
          <el-input v-model="form.userCode" :disabled="true"/>
        </el-form-item>
        <el-form-item :label="$t('common.userName')">
          <el-input v-model="form.nickname" :disabled="true"/>
        </el-form-item>
        <el-form-item :label="$t('system.responsibleForTheCompany')">
          <el-select v-model="form.companyIds" :placeholder="$t('common.pleaseSelect')" class="searchValue" clearable
                     filterable multiple
          >
            <el-option
                v-for="item in companyOptions"
                :key="parseInt(item.id)"
                :label="item.name"
                :value="parseInt(item.id)"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelCompany">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitCompany">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
    <!-- 分配上级 -->
    <el-dialog :title="$t('system.assignParent')" :visible.sync="openLeader" width="500px" append-to-body>
      <el-form ref="leaderform" :model="form" label-width="80px">
        <el-form-item :label="$t('supplier.user')" prop="userIds">
          <el-select v-model="form.leaderId" class="searchValue" clearable filterable>
            <el-option v-for="item in leaderList" :key="item.id" :label="item.nickname" :value="item.id"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelLeader">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="saveLeader">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
    <!-- 分配上级 -->
    <el-dialog :title="$t('分配incap客户')" :visible.sync="openCustomerAssign" width="500px" append-to-body>
      <el-form ref="incapCustomerform" :model="form" label-width="80px">
        <el-form-item :label="$t('客户')" prop="incapCustomerList">
          <el-select v-model="form.incapCustomerList" class="searchValue" clearable filterable multiple>
            <el-option v-for="item in getDictDatas(DICT_TYPE.INCAP_CUSTOMER)" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelCustomerAssign">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="saveCustomerAssign">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
        v-if="handoverPersonnelVisible"
        width="1200px"
        :title="$t('system.personnelHandover')"
        :visible.sync="handoverPersonnelVisible"
    >
      <el-tabs v-model="activeTab" @tab-click="handleClick">
        <el-tab-pane :label="$t('system.configurationHandover')" :name="DICT_TYPE.SYSTEM_USER_CONFIGURE_HANDOFFS"/>
        <el-tab-pane :label="$t('system.pendingHandover')" :name="DICT_TYPE.SYSTEM_USER_PENDING_HANDOFFS"/>
        <el-tab-pane :label="$t('system.documentHandover')" :name="DICT_TYPE.SYSTEM_USER_DOCUMENT_HANDOFFS"/>
        <el-table :data="handoffTable">
          <el-table-column :label="$t('common.name')" prop="label"/>
          <el-table-column :label="$t('order.describe')" width="250">
            <template #default="scope">
              {{ handoffRemarkList.get(scope.row.value) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('common.operate')">
            <template #default="scope">
              <el-select v-model="scope.row.operationType">
                <el-option
                    v-for="item in handoffsOperationType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>

            </template>
          </el-table-column>
          <el-table-column :label="$t('system.recipient')">
            <template #default="scope">
              <el-select v-if="scope.row.operationType === 'transfer'" v-model="scope.row.userId" class="searchValue"
                         clearable filterable
              >
                <el-option
                    v-for="item in getDictDatas(DICT_TYPE.COMMON_USERS)"
                    :key="item.value"
                    :label="item.name"
                    :value="item.id"
                />
              </el-select>
            </template>

          </el-table-column>
        </el-table>
      </el-tabs>
      <div slot="footer">
        <el-button @click="handoverPersonnelVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitHandoff">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addUser, assignUserOrgs,
  changeUserStatus, createAccount,
  delUser,
  exportUser,
  getUser, getUserOrgs, getUsersCache,
  importTemplate, listByTypeDict,
  listUser,
  resetUserPwd, saveHandoff, saveUserLeaderRel,
  updateUser,
  setPasswordExpiredPeriod,
  incapAssignCustomer
} from '@/api/system/user'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { getDeptCode, listSimpleDepts } from '@/api/system/dept'
import { listSimplePosts } from '@/api/system/post'
import { CommonStatusEnum } from '@/utils/constants'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { assignUserRole, listUserRoles } from '@/api/system/permission'
import { listSimpleRoles } from '@/api/system/role'
import { getBaseHeader } from '@/utils/request'
import { getPurchaseOrgCache } from '@/api/system/purchaseOrg'
import { setMockUserToken } from '@/utils/auth'
import { mockAccount } from '@/api/login'
import { assignUserCompanys, getUserCompanys } from '@/api/system/userCompany/userCompany'
import { getConfigKey } from '@/api/infra/config'

export default {
  name: 'User',
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // 性别状态字典
      sexOptions: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 采购组织选项
      orgOptions: [],
      // 公司选项
      companyOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/system/user/import'
      },
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        username: undefined,
        nickname: undefined,
        userCode: undefined,
        status: undefined,
        deptId: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: this.$t('system.userNo'), visible: true },
        { key: 1, label: this.$t('common.userName'), visible: true },
        { key: 2, label: this.$t('system.userId'), visible: true },
        { key: 3, label: this.$t('system.userAccount'), visible: true },
        { key: 4, label: this.$t('system.superiorLeaders'), visible: true },
        { key: 5, label: this.$t('system.department'), visible: true },
        { key: 6, label: this.$t('system.phoneNumber'), visible: true },
        { key: 7, label: this.$t('密码失效周期（天）'), visible: true },
        { key: 8, label: this.$t('密码到期时间'), visible: true },
        { key: 9, label: this.$t('common.status'), visible: true },
        { key: 10, label: this.$t('common.createTime'), visible: true },
        { key: 11, label: this.$t('incap客户'), visible: true }
      ],
      // 表单校验
      accountRules: {
        username: [
          { required: true, message: this.$t('system.theUserAccountCannotBeEmpty'), trigger: 'blur' }
        ],
        passwordExpiredPeriod: [
          { required: true, message: this.$t('请输入密码失效周期（天）'), trigger: 'blur' }
        ],
        password: [
          { required: true, message: this.$t('system.userPasswordCannotBeEmpty'), trigger: 'blur' },
          {
            pattern: /^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/,
            message: this.$t('system.thePasswordShouldHaveSymbol')
          }
        ]
      },
      // 重置密码校验
      resetPwdRules: {
        password: [
          { required: true, message: this.$t('system.userPasswordCannotBeEmpty'), trigger: 'blur' },
          {
            pattern: /^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/,
            message: this.$t('system.thePasswordShouldHaveSymbol')
          }
        ]
      },
      // 表单校验
      rules: {
        nickname: [
          { required: true, message: this.$t('system.userNameCannotBeEmpty'), trigger: 'blur' }
        ],
        userCode: [
          { required: true, message: this.$t('system.userIdCannotBeBlank'), trigger: 'blur' }
        ],
        deptId: [
          { required: true, message: this.$t('system.pleaseSelectTheDepartment'), trigger: 'change' }
        ],
        email: [
          {
            type: 'email',
            message: this.$t('supplier.pleaseEnterTheCorrectEmailAddress'),
            trigger: ['blur', 'change']
          }
        ],
        mobile: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: this.$t('system.pleaseEnterTheCorrectMobilePhoneNumber'),
            trigger: 'blur'
          }
        ]
      },
      // 是否显示弹出层（角色权限）
      openRole: false,
      openOrg: false,
      openCompany: false,
      openAccount: false,
      openLeader: false,
      openResetPwd: false,
      // 客户分配的控制prop
      openCustomerAssign: false,
      // 枚举
      SysCommonStatusEnum: CommonStatusEnum,
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS),
      sexDictDatas: getDictDatas(DICT_TYPE.SYSTEM_USER_SEX),
      // 选中数组值
      multipleSelection: [],
      leaderList: [],
      // 打开模拟用户下拉框
      openMockDialog: false,
      handoverPersonnelVisible: false,
      activeTab: DICT_TYPE.SYSTEM_USER_CONFIGURE_HANDOFFS,
      handoffTable: [],
      // 操作类型数据源
      handoffsOperationType: [],
      handoffRemarkList: new Map(),
      defPasswordExpiredPeriod: 1,
      systemPasswordUpdate: true
    }
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getList()
    this.getTreeselect()
    this.getConfigKey('sys.user.init-password').then(response => {
      this.initPassword = response.data
    })
    this.getLeaderList()
    this.getHandoffRemark()
    getConfigKey('sys.user.defPasswordExpiredPeriod').then(response => {
      this.defPasswordExpiredPeriod = response.data
    })
    this.getConfigKey('system.password.update').then(response => {
      if (this.$store.getters.isExternal === 1) {
        console.log('外部用户')
        this.systemPasswordUpdate = response.data === 'true'
      }
    })
  },
  methods: {
    // 模拟账户
    // 1.仅支持选中一个用户
    doMockAccount(row) {
      this.openMockDialog = false
      mockAccount(row.id).then(res => {
        this.$store.dispatch('ClearRoles')
        setMockUserToken(res.data)
        this.$tab.closeAllPage().then(() => {
          this.$router.push('/index')
        })
      })
    },
    /** 保存分配的上级 */
    saveLeader() {
      this.$refs['leaderform'].validate(valid => {
        this.form.userIds = this.multipleSelection.map(i => {
          return i.id
        })
        console.log(this.form.userIds)
        // 修改的提交
        saveUserLeaderRel(this.form).then(response => {
          this.$modal.msgSuccess(this.$t(this.$t('common.savedSuccessfully')))
          this.openLeader = false
          this.getList()
        })
      })
    },
    /** 保存分配的incap客户 */
    saveCustomerAssign() {
      this.$refs['incapCustomerform'].validate(valid => {
        this.form.userIds = this.multipleSelection.map(i => {
          return i.id
        })
        // 修改的提交
        incapAssignCustomer(this.form).then(response => {
          this.$modal.msgSuccess(this.$t(this.$t('common.savedSuccessfully')))
          this.openCustomerAssign = false
          this.getList()
        })
      })
    },
    /**
     * 获取上级人员列表
     */
    getLeaderList() {
      getUsersCache({
        status: 0,
        isExternal: 1
      }).then(response => {
        this.leaderList = []
        this.leaderList.push(...response.data)
      })
    },
    clickRow(row) {
      this.$refs.multipleUserTable.toggleRowSelection(row)
    },
    /**
     * 选择框
     * @param val
     */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /**
     * incap order tracker: 分配客户
     */
    handleCustomer() {
      if (this.multipleSelection == null || this.multipleSelection.length === 0) {
        this.$modal.msgError(this.$t(this.$t('supplier.pleaseSelectData')))
        return
      }
      this.reset()
      this.openCustomerAssign = true
    },
    /** 分配上级 */
    handleUserLeader() {
      if (this.multipleSelection == null || this.multipleSelection.length === 0) {
        this.$modal.msgError(this.$t(this.$t('supplier.pleaseSelectData')))
        return
      }
      this.reset()
      this.openLeader = true
    },
    // 更多操作
    handleCommand(command, index, row) {
      switch (command) {
        case 'handleUpdate':
          this.handleUpdate(row)// 修改客户信息
          break
        case 'handleDelete':
          this.handleDelete(row)// 红号变更
          break
        case 'handleResetPwd':
          this.handleResetPwd(row)
          break
        case 'handleCreateAccount':
          this.handleCreateAccount(row)
          break
        case 'handleRole':
          this.handleRole(row)
          break
        case 'handleOrg':
          this.handleOrg(row)
          break
        case 'handleCompany':
          this.handleCompany(row)
          break
        case 'mockAccount':
          this.doMockAccount(row)
          break
        case 'handoverPersonnel':
          this.showHandoverPersonnel(row)
          break
        case 'setPasswordExpiredPeriod':
          this.setPasswordExpiredPeriod(row)
          break
        default:
          break
      }
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true
      listUser(this.addDateRange(this.queryParams, [
        this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined,
        this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined
      ])).then(response => {
        this.userList = response.data.list
        this.total = response.data.total
        this.loading = false
        // 新增用户后call 列表刷新，但是上级领导集合没有刷新。因此需要收到刷新
        this.getLeaderList()
      })
    },
    /** 查询部门下拉树结构 + 岗位下拉 */
    getTreeselect() {
      listSimpleDepts().then(response => {
        // 处理 deptOptions 参数
        this.deptOptions = []
        this.deptOptions.push(...this.handleTree(response.data, 'id'))
      })
      listSimplePosts().then(response => {
        // 处理 postOptions 参数
        this.postOptions = []
        this.postOptions.push(...response.data)
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id
      this.getList()
    },
    // 用户状态修改
    handleStatusChange(row) {
      const text = row.status === CommonStatusEnum.ENABLE ? this.$t('common.enable') : this.$t('system.deactivate')
      this.$modal.confirm('确认要"' + text + '""' + row.username + '"用户吗?').then(function() {
        return changeUserStatus(row.id, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + this.$t('system.success'))
      }).catch(function() {
        row.status = row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE
            : CommonStatusEnum.ENABLE
      })
    },
    // 取消按钮
    cancelAccount() {
      this.openAccount = false
      this.reset()
    },
    // 取消按钮
    cancelResetPwd() {
      this.openResetPwd = false
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 取消按钮（角色权限）
    cancelRole() {
      this.openRole = false
      this.reset()
    },
    // 取消按钮
    cancelLeader() {
      this.openLeader = false
      this.reset()
    },
    // 取消按钮
    cancelCustomerAssign() {
      this.openCustomerAssign = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        deptId: undefined,
        username: undefined,
        nickname: undefined,
        passwordExpiredPeriod: undefined,
        password: undefined,
        mobile: undefined,
        email: undefined,
        sex: undefined,
        status: '0',
        remark: undefined,
        postIds: [],
        roleIds: [],
        purchaseOrgs: [],
        companyIds: [],
        userIds: [],
        leaderId: undefined,
        incapCustomerList: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.queryParams.deptId = ''
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      // 获得下拉数据
      // this.getTreeselect()
      // 打开表单，并设置初始化
      this.open = true
      this.title = this.$t('system.addUser')
      this.form.password = this.initPassword
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      // this.getTreeselect()
      const id = row.id
      getUser(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('system.modifyUser')
        this.form.password = ''
      })
    },
    /** 创建账户 */
    handleCreateAccount(row) {
      this.reset()
      const id = row.id
      // 处理了 form 的用户 username 和 nickname 的展示
      this.form.id = id
      this.form.userCode = row.userCode
      this.form.nickname = row.nickname
      this.form.passwordExpiredPeriod = row.passwordExpiredPeriod ? row.passwordExpiredPeriod : this.defPasswordExpiredPeriod
      this.title = this.$t('system.createAccount')
      this.openAccount = true
    },
    setPasswordExpiredPeriod(row) {
      this.$prompt(this.$t('请输入密码失效周期（天）'), this.$t('提示'), {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: row.passwordExpiredPeriod,
        inputValidator: (value) => {
          if (value === undefined || value === null || value === '') {
            return this.$t('请输入密码失效周期（天）')
          }
          const regex = new RegExp('^[1-9]\\d*$')
          const isPositiveInteger = regex.test(value)
          if (!isPositiveInteger) {
            return this.$t('密码失效周期（天）格式不正确')
          }
          if (Number(value) < 1) {
            return this.$t('密码失效周期（天）不能小于1')
          }
        }
      }).then(({ value }) => {
        setPasswordExpiredPeriod({id:row.id, passwordExpiredPeriod:value}).then(() => {
          this.$message.success(this.$t('common.updateSuccessful'))
          this.getList()
        })
      })
    },
    /** 分配用户角色操作 */
    handleRole(row) {
      this.reset()
      const id = row.id
      // 处理了 form 的用户 username 和 nickname 的展示
      this.form.id = id
      this.form.userCode = row.userCode
      this.form.nickname = row.nickname
      // 打开弹窗
      this.openRole = true
      // 获得角色列表
      listSimpleRoles().then(response => {
        // 处理 roleOptions 参数
        this.roleOptions = []
        this.roleOptions.push(...response.data)
      })
      // 获得角色拥有的菜单集合
      listUserRoles(id).then(response => {
        // 设置选中
        this.form.roleIds = response.data
      })
    },
    /** 提交账户按钮 */
    submitAccount: function() {
      this.$refs['formAccount'].validate(valid => {
        if (valid) {
          createAccount(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.createdSuccessfully'))
            this.openAccount = false
            this.getList()
          })
        }
      })
    },
    /** 重置密码 */
    handleResetPwd(row) {
      this.reset()
      const id = row.id
      // 处理了 form 的用户 username 和 nickname 的展示
      this.form.id = id
      this.form.userCode = row.userCode
      this.form.nickname = row.nickname
      this.form.username = row.username
      this.title = this.$t('system.resetPassword')
      this.openResetPwd = true
    },
    /** 提交账户按钮 */
    submitResetPwd: function() {
      this.$refs['formResetPwd'].validate(valid => {
        if (valid) {
          resetUserPwd(this.form.id, this.form.password).then(response => {
            this.$modal.msgSuccess('修改成功，新密码是：' + this.form.password)
            this.openResetPwd = false
          })
        }
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateUser(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
              this.open = false
              this.getList()
            })
          } else {
            addUser(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('common.addSuccess'))
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 提交按钮（角色权限） */
    submitRole: function() {
      if (this.form.id !== undefined) {
        assignUserRole({
          userId: this.form.id,
          roleIds: this.form.roleIds
        }).then(response => {
          this.$modal.msgSuccess(this.$t('system.roleAssignmentSucceeded'))
          this.openRole = false
          this.getList()
        })
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除用户编号为"' + ids + '"的数据项?').then(function() {
        return delUser(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.addDateRange(this.queryParams, [
        this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined,
        this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined
      ])
      this.$modal.confirm(this.$t('system.areYouSureToExportAllUserDataItems')).then(() => {
        this.exportLoading = true
        return exportUser(queryParams)
      }).then(response => {
        this.$download.excel(response, this.$t('system.userDataxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = this.$t('system.userImport')
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.$download.excel(response, this.$t('system.userImportTemplatexls'))
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    onPreview(file) {
      this.downloadFile(file)
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      // 拼接提示语
      const data = response.data
      if (data.filePath) {
        let text = this.$t('common.numberOfValidationFailures') + '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
        this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      }
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    // 格式化部门的下拉框
    normalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    handleOrg(row) {
      this.reset()
      const id = row.id
      // 处理了 form 的用户 username 和 nickname 的展示
      this.form.id = id
      this.form.userCode = row.userCode
      this.form.nickname = row.nickname
      this.openOrg = true
      getPurchaseOrgCache({
        status: 0
      }).then(response => {
        this.orgOptions = []
        this.orgOptions.push(...response.data)
        getUserOrgs({ userId: id }).then(res => {
          // 设置选中
          this.form.purchaseOrgs = res.data
        })
      })
    },

    submitOrg: function() {
      if (this.form.id !== undefined) {
        assignUserOrgs({
          userId: this.form.id,
          purchaseOrgs: this.form.purchaseOrgs
        }).then(response => {
          this.$modal.msgSuccess(this.$t('system.successfullyAssignedPurchaseOrganization'))
          this.openOrg = false
          this.getList()
        })
      }
    },
    cancelOrg() {
      this.openOrg = false
      this.reset()
    },
    handleCompany(row) {
      this.reset()
      const id = row.id
      // 处理了 form 的用户 username 和 nickname 的展示
      this.form.id = id
      this.form.userCode = row.userCode
      this.form.nickname = row.nickname
      this.openCompany = true
      getDeptCode({
        status: 0
      }).then(response => {
        this.companyOptions = []
        this.companyOptions.push(...response.data)
        getUserCompanys({ userId: id }).then(res => {
          // 设置选中
          this.form.companyIds = res.data
        })
      })
    },

    submitCompany: function() {
      if (this.form.id !== undefined) {
        assignUserCompanys({
          userId: this.form.id,
          companyIds: this.form.companyIds
        }).then(response => {
          this.$modal.msgSuccess(this.$t('system.successfullyAllocatedCompany'))
          this.openCompany = false
          this.getList()
        })
      }
    },
    cancelCompany() {
      this.openCompany = false
      this.reset()
    },
    getHandoffRemark() {
      Promise.all(
          [
            listByTypeDict({ type: DICT_TYPE.SYSTEM_USER_DOCUMENT_HANDOFFS }),
            listByTypeDict({ type: DICT_TYPE.SYSTEM_USER_PENDING_HANDOFFS }),
            listByTypeDict({ type: DICT_TYPE.SYSTEM_USER_CONFIGURE_HANDOFFS })
          ]
      ).then(res => {
        this.handoffRemarkList = new Map()
        res.forEach(a => {
          a?.data.forEach(b => {
            this.handoffRemarkList.set(b.value, b.remark)
          })
        })
      })
    },
    showHandoverPersonnel(row) {
      this.handoverPersonnelVisible = true
      this.fromUserId = row.id
      this.handleClick(this.activeTab)
    },
    handleClick(tab, event) {
      this.handoffsOperationType = getDictDatas(DICT_TYPE.SYSTEM_USER_HANDOFFS_OPERATION_TYPE)
      if (this.activeTab === DICT_TYPE.SYSTEM_USER_PENDING_HANDOFFS || this.activeTab === DICT_TYPE.SYSTEM_USER_DOCUMENT_HANDOFFS) {
        this.handoffsOperationType = this.handoffsOperationType.filter(i => i.value !== 'delete')
      }
      this.handoffTable = getDictDatas(this.activeTab).map(a => {
        return {
          ...a,
          operationType: '',
          userId: '',
          fromUserId: this.fromUserId,
          type: this.activeTab
        }
      })
    },
    submitHandoff() {
      saveHandoff(this.handoffTable).then(a => {
        this.handoverPersonnelVisible = false
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    },
    expandNodeBySelectedItem() {
      const { treeselect } = this.$refs

      // 定义一个递归函数，用于将给定节点的所有父节点展开
      const expandParents = (node) => {
        if (node.parentNode) {
          node.parentNode.isExpanded = true
          expandParents(node.parentNode)
        }
      }

      if (treeselect && this.form.deptId) {
        // 遍历所有的树节点
        treeselect.traverseAllNodesByIndex(node => {
          // 只处理有子节点的节点
          if (node.isBranch) {
            // 检查子节点中是否有id等于form.deptId的节点
            const shouldExpand = (node.children || []).some(child => child.id === this.form.deptId)
            // 如果有，那么将这个节点以及它的所有父节点展开
            if (shouldExpand) {
              node.isExpanded = true
              expandParents(node)
            }
          }
        })
      }
    }

  }
}
</script>
