<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>{{ $t('system.personalInformation') }}</span>
          </div>
          <div>
            <div class="text-center">
              <userAvatar :user="user" />
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                <div class="pull-left">
                  <svg-icon icon-class="user" />
                  {{ $t('system.loginAccount') }}
                </div>
                <div class="pull-right">{{ user.username }}</div>
              </li>
              <li class="list-group-item">
                <div class="pull-left">
                  <svg-icon icon-class="phone" />
                  {{ $t('system.phoneNumber') }}
                </div>
                <div class="pull-right">{{ user.mobile }}</div>
              </li>
              <li class="list-group-item">
                <div class="pull-left">
                  <svg-icon icon-class="email" />
                  {{ $t('system.userEmail') }}
                </div>
                <div class="pull-right">{{ user.email }}</div>
              </li>
              <li class="list-group-item">
                <div class="pull-left">
                  <svg-icon icon-class="tree" />
                  {{ $t('system.department') }}
                </div>
                <div v-if="user.dept" class="pull-right">{{ user.dept.name }}</div>
              </li>
              <li class="list-group-item">
                <div class="pull-left">
                  <svg-icon icon-class="tree" />
                  {{ $t('system.position') }}
                </div>
                <div v-if="user.posts" class="pull-right">{{ user.posts.map(post => post.name).join(',') }}</div>
              </li>
              <li class="list-group-item">
                <div class="pull-left">
                  <svg-icon icon-class="peoples" />
                  {{ $t('system.role') }}
                </div>
                <div v-if="user.roles" class="pull-right">{{ user.roles.map(role => role.name).join(',') }}</div>
              </li>
              <li class="list-group-item">
                <div class="pull-left">
                  <svg-icon icon-class="date" />
                  {{ $t('common.creationDate') }}
                </div>
                <div class="pull-right">{{ parseTime(user.createTime) }}</div>
              </li>
              <li class="list-group-item">
                <div class="pull-left">
                  <svg-icon icon-class="date" />
                  {{ $t('密码到期时间') }}
                </div>
                <div class="pull-right">{{ parseTime(store.getters.passwordExpiredTime) }}</div>
              </li>
              <li v-if="user.supplierId !== null">
                <div class="pull-right">
                  <el-button
                    v-has-permi="['supplier:base-info:query']"
                    type="text"
                    @click="$router.push('/supplier/supplierAccount')"
                  >
                    {{ $t('system.enterpriseInformationRegistration') }}
                  </el-button></div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <div slot="header" class="clearfix">
            <span>{{ $t('system.basicInformation') }}</span>
          </div>
          <el-tabs v-model="activeTab">
            <el-tab-pane :label="$t('system.basicInformation')" name="userinfo">
              <userInfo :user="user" />
            </el-tab-pane>
            <el-tab-pane :label="$t('common.changePassword')" name="resetPwd" v-if="systemPasswordUpdate">
              <resetPwd :user="user" />
            </el-tab-pane>
            <el-tab-pane v-if="socialSwitch" :label="$t('system.socialInformation')" name="userSocial">
              <userSocial
                :get-user="getUser"
                :set-active-tab="setActiveTab"
                :user="user"
                @getUserProfile="getUser"
              />
            </el-tab-pane>
            <el-tab-pane :label="$t('system.individualSignature')" name="userSeal">
              <userSeal :user="user" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import userAvatar from './userAvatar'
import userInfo from './userInfo'
import resetPwd from './resetPwd'
import userSocial from './userSocial'
import userSeal from './userSeal'
import { getUserProfile } from '@/api/system/user'
import store from '../../../../store'

export default {
  name: 'Profile',
  computed: {
    store() {
      return store
    }
  },
  components: { userAvatar, userInfo, resetPwd, userSocial, userSeal },
  data() {
    return {
      user: {},
      roleGroup: {},
      postGroup: {},
      activeTab: 'userinfo',
      socialSwitch: process.env.VUE_APP_SOCIAL_LOGIN === 'true',
      systemPasswordUpdate: true

    }
  },
  created() {
    this.getUser()
    this.getConfigKey('system.password.update').then(response => {
      if (this.$store.getters.isExternal === 1) {
        console.log('外部用户')
        this.systemPasswordUpdate = response.data === 'true'
      }
    })
  },
  methods: {
    getUser() {
      getUserProfile().then(response => {
        this.user = response.data
      })
    },
    setActiveTab(activeTab) {
      this.activeTab = activeTab
    }
  }
}
</script>
