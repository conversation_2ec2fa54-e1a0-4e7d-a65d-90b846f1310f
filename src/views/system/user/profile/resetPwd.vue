<template>
  <el-form ref="form" :model="user" :rules="rules" label-width="80px">
    <el-form-item :label="$t('system.oldPassword')" prop="oldPassword">
      <el-input v-model="user.oldPassword" :placeholder="$t('system.pleaseEnterTheOldPassword')" type="password" show-password />
    </el-form-item>
    <el-form-item :label="$t('system.newPassword')" prop="newPassword">
      <el-input v-model="user.newPassword" :placeholder="$t('system.pleaseEnterANewPassword')" type="password" show-password />
    </el-form-item>
    <el-form-item :label="$t('common.confirmPassword')" prop="confirmPassword">
      <el-input v-model="user.confirmPassword" :placeholder="$t('system.pleaseConfirmThePassword')" type="password" show-password />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">{{ $t('common.save') }}</el-button>
      <el-button type="danger" size="mini" @click="close">{{ $t('order.close') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserPwd } from '@/api/system/user'

export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error(this.$t('common.thePasswordsEnteredTwiceAreInconsistent')))
      } else {
        callback()
      }
    }
    return {
      test: '1test',
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined
      },
      // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: this.$t('system.theOldPasswordCannotBeEmpty'), trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: this.$t('system.theNewPasswordCannotBeEmpty'), trigger: 'blur' },
          {
            pattern: /^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/,
            message: this.$t('system.thePasswordShouldHaveSymbol')
          }
        ],
        confirmPassword: [
          { required: true, message: this.$t('system.confirmPasswordCannotBeEmpty'), trigger: 'blur' },
          { required: true, validator: equalToPassword, trigger: 'blur' },
          {
            pattern: /^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/,
            message: this.$t('system.thePasswordShouldHaveSymbol')
          }
        ]
      }
    }
  },
  methods: {
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          updateUserPwd(this.user.oldPassword, this.user.newPassword).then(
            response => {
              this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            }
          )
        }
      })
    },
    close() {
      this.$tab.closePage()
    }
  }
}
</script>
