<template>
  <el-form ref="form" :model="user" :rules="rules" label-width="80px">
    <el-form-item :label="$t('common.userName')" prop="nickName">
      <el-input v-model="user.nickname" />
    </el-form-item>
    <el-form-item :label="$t('system.phoneNumber')" prop="mobile">
      <el-input v-model="user.mobile" maxlength="11" />
    </el-form-item>
    <el-form-item :label="$t('supplier.mailbox')" prop="email">
      <el-input v-model="user.email" maxlength="50" />
    </el-form-item>
    <el-form-item>
      <span style="color: red">
        {{ $t('supplier.Prompt') }}</span>
    </el-form-item>
    <el-form-item :label="$t('system.gender')">
      <el-radio-group v-model="user.sex">
        <el-radio :label="1">{{ $t('system.male') }}</el-radio>
        <el-radio :label="2">{{ $t('system.female') }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item>
      <el-button size="mini" type="primary" @click="submit">{{ $t('common.save') }}</el-button>
      <el-button size="mini" type="danger" @click="close">{{ $t('order.close') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>

import userSeal from './userSeal'
import { updateUserProfile } from '@/api/system/user'

export default {
  components: { userSeal },
  props: {
    user: {
      type: Object
    }
  },
  data() {
    return {
      // 表单校验
      rules: {
        nickname: [
          { required: true, message: this.$t('system.userNicknameCannotBeEmpty'), trigger: 'blur' }
        ],
        email: [
          { required: true, message: this.$t('system.emailAddressCannotBeEmpty'), trigger: 'blur' },
          {
            type: 'email',
            message: this.$t('supplier.pleaseEnterTheCorrectEmailAddress'),
            trigger: ['blur', 'change']
          }
        ]
      /*   mobile: [
          { required: true, message: this.$t('system.mobileNumberCannotBeEmpty'), trigger: 'blur' },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: this.$t('system.pleaseEnterTheCorrectMobilePhoneNumber'),
            trigger: 'blur'
          }
        ] */
      }
    }
  },
  methods: {
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          updateUserProfile(this.user).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          })
        }
      })
    },
    close() {
      this.$tab.closePage()
    }
  }
}
</script>
