<template>
  <div>
    <div
      class="user-info-head"
      @click="editCropper()"
    ><img
      class="img-circle img-lg"
      :title="$t('system.clickToUploadTheSeal')"
      :src="options.img"
    ></div>
    <el-dialog
      :title="title"
      :visible.sync="open"
      append-to-body
      width="800px"
      @close="closeDialog()"
      @opened="modalOpened"
    >
      <el-row>
        <el-col :md="12" :style="{height: '350px'}" :xs="24">
          <vue-cropper
            v-if="visible"
            ref="cropper"
            output-type="png"
            :auto-crop="options.autoCrop"
            :auto-crop-height="options.autoCropHeight"
            :auto-crop-width="options.autoCropWidth"
            :fixed-box="options.fixedBox"
            :img="options.img"
            :info="true"
            @realTime="realTime"
          />
        </el-col>
        <el-col :md="12" :style="{height: '350px'}" :xs="24">
          <div class="avatar-upload-preview">
            <img :src="previews.url" :style="previews.img">
          </div>
        </el-col>
      </el-row>
      <br>
      <el-row>
        <el-col :lg="2" :md="2">
          <el-upload :before-upload="beforeUpload" :http-request="requestUpload" :show-file-list="false" action="#">
            <el-button size="small">
              {{ $t('system.choice') }}
              <i class="el-icon-upload el-icon--right" />
            </el-button>
          </el-upload>
        </el-col>
        <el-col :lg="{span: 1, offset: 2}" :md="2">
          <el-button icon="el-icon-plus" size="small" @click="changeScale(1)" />
        </el-col>
        <el-col :lg="{span: 1, offset: 1}" :md="2">
          <el-button icon="el-icon-minus" size="small" @click="changeScale(-1)" />
        </el-col>
        <el-col :lg="{span: 1, offset: 1}" :md="2">
          <el-button icon="el-icon-refresh-left" size="small" @click="rotateLeft()" />
        </el-col>
        <el-col :lg="{span: 1, offset: 1}" :md="2">
          <el-button icon="el-icon-refresh-right" size="small" @click="rotateRight()" />
        </el-col>
        <el-col :lg="{span: 2, offset: 6}" :md="2">
          <el-button size="small" type="primary" @click="uploadImg()">提 交</el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import store from '@/store'
import { VueCropper } from 'vue-cropper'
import { updateUserSeal } from '@/api/system/user'

export default {
  components: { VueCropper },
  props: {
    user: {
      type: Object
    },
    setSealTab: { // 设置激活的
      type: Function
    }
  },
  data() {
    return {
      // 是否显示弹出层
      open: false,
      // 是否显示cropper
      visible: false,
      // 弹出层标题
      title: this.$t('system.revisedSeal'),
      options: {
        img: store.getters.sealPath, // 裁剪图片的地址
        autoCrop: true, // 是否默认生成截图框
        autoCropWidth: 200, // 默认生成截图框宽度
        autoCropHeight: 200, // 默认生成截图框高度
        fixedBox: true // 固定截图框大小 不允许改变
      },
      previews: {}
    }
  },
  methods: {
    // 编辑头像
    editCropper() {
      this.open = true
    },
    // 打开弹出层结束时的回调
    modalOpened() {
      this.visible = true
    },
    // 覆盖默认的上传行为
    requestUpload() {
    },
    // 向左旋转
    rotateLeft() {
      this.$refs.cropper.rotateLeft()
    },
    // 向右旋转
    rotateRight() {
      this.$refs.cropper.rotateRight()
    },
    // 图片缩放
    changeScale(num) {
      num = num || 1
      this.$refs.cropper.changeScale(num)
    },
    // 上传预处理
    beforeUpload(file) {
      if (file.type.indexOf('image/') == -1) {
        this.$modal.msgError(this.$t('system.theFileFormatIsIncorrectPleaseUploadAFileWithAPictureTypeSuchAsJpgOrPngSuffix'))
      } else {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          this.options.img = reader.result
        }
      }
    },
    // 上传图片
    uploadImg() {
      this.$refs.cropper.getCropBlob(data => {
        const formData = new FormData()
        formData.append('sealFile', data)
        updateUserSeal(formData).then(resp => {
          this.open = false
          // this.options.img = process.env.VUE_APP_BASE_API + response.imgUrl;
          store.commit('SET_SEALPATH', resp.data)
          this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          this.visible = false
        })
      })
    },
    // 实时预览
    realTime(data) {
      this.previews = data
    },
    // 取消截图，关闭对话框
    closeDialog() {
      this.options.img = store.getters.sealPath
    }
  }
}
</script>
<style lang="scss" scoped>
.user-info-head {
  position: relative;
  display: inline-block;
  height: 120px;
}

.user-info-head:hover:after {
  content: '+';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  color: #eee;
  background: rgba(0, 0, 0, 0.5);
  font-size: 24px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: pointer;
  line-height: 110px;
  border-radius: 50%;
}
</style>
