<template>
  <div>
    <el-table :data="socialUsers" :show-header="false">
      <el-table-column :label="$t('system.socialPlatform')" align="left" width="120">
        <template slot-scope="scope">
          <img style="height:20px;vertical-align: middle;" :src="scope.row.img"> {{ scope.row.title }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="left">
        <template slot-scope="scope">
          <div v-if="scope.row.openid">
            {{ $t('system.bound') }}
            <el-button size="large" type="text" @click="unbind(scope.row)">{{ $t('system.unbind') }}</el-button>
          </div>
          <div v-else>
            {{ $t('system.unbound') }}
            <el-button size="large" type="text" @click="getBindCode">{{ $t('system.binding') }}</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-if="codeVisible"
      :title="$t('system.scanCodeBinding')"
      width="470px"
      :visible.sync="codeVisible"
      :before-close="closeCode"
    >
      <div style="justify-content: center;display: flex">
        <div style="width: 250px;height: 250px;position:relative;">
          <img v-if="codeUrl" alt="" :src="codeUrl" style="width: 250px;height: 250px">
          <el-skeleton v-else>
            <template slot="template">
              <div style="display: flex;justify-content: center;">
                <el-skeleton-item variant="image" style="width: 240px; height: 240px;" />
              </div>
            </template>
          </el-skeleton>
          <div v-show="!codeExpire" class="invalid-code">
            <span>{{ $t('system.theQrCodeHasExpired') }}，</span>
            <el-button type="text" @click="getBindCode">{{ $t('common.reload') }}</el-button>
          </div>
        </div>

      </div>
      <div style="text-align: center">
        <el-button style="margin-top: 15px" type="primary" @click="closeCode">{{ $t('order.close') }}</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>

import { SystemUserSocialTypeEnum } from '@/utils/constants'
import { checkWxBind, getWxMpBindCode, socialAuthRedirect } from '@/api/login'
import { socialBind, socialUnbind } from '@/api/system/socialUser'

export default {
  props: {
    user: {
      type: Object
    },
    getUser: { // 刷新用户
      type: Function
    },
    setActiveTab: { // 设置激活的
      type: Function
    }
  },
  data() {
    return {
      codeVisible: false,
      codeUrl: '',
      timer: '',
      codeExpire: true,
      timeOut: null
    }
  },
  computed: {
    socialUsers() {
      const socialUsers = []
      for (const i in SystemUserSocialTypeEnum) {
        const socialUser = { ...SystemUserSocialTypeEnum[i] }
        socialUsers.push(socialUser)
        if (this.user.socialUsers) {
          for (const j in this.user.socialUsers) {
            if (socialUser.type === this.user.socialUsers[j].type) {
              socialUser.openid = this.user.socialUsers[j].openid
              break
            }
          }
        }
      }
      return socialUsers
    }
  },
  created() {
    // 社交绑定
    const type = this.$route.query.type
    const code = this.$route.query.code
    const state = this.$route.query.state
    if (!code) {
      return
    }
    socialBind(type, code, state).then(resp => {
      this.$modal.msgSuccess(this.$t('system.bindingSucceeded'))
      this.$router.replace('/user/profile')
      // 调用父组件, 刷新
      this.getUser()
      this.setActiveTab('userSocial')
    })
  },
  beforeDestroy() {
    clearInterval(this.timer)
    clearTimeout(this.timeOut)
  },
  methods: {
    getBindCode() {
      this.codeVisible = true
      getWxMpBindCode(this.$store.getters.userId).then(res => {
        this.codeUrl = res.data
        this.codeExpire = true
        this.setCodeExpire()
        this.timer = setInterval(() => {
          this.checkBind()
        }, 1000)
      })
    },
    bind(socialUser) {
      // 计算 redirectUri
      const redirectUri = location.origin + '/user/profile?type=' + socialUser.type
      // 进行跳转
      socialAuthRedirect(socialUser.type, encodeURIComponent(redirectUri)).then((res) => {
        // console.log(res.url);
        window.location.href = res.data
      })
    },
    unbind(socialUser) {
      socialUnbind(socialUser.type, socialUser.openid).then(resp => {
        this.$modal.msgSuccess(this.$t('system.unbindingSucceeded'))
        socialUser.openid = undefined
      })
    },
    close() {
      this.$tab.closePage()
    },
    closeCode() {
      clearInterval(this.timer)
      clearTimeout(this.timeOut)
      this.codeVisible = false
    },
    checkBind() {
      checkWxBind(this.$store.getters.userId).then(res => {
        if (res.data) {
          this.closeCode()
          this.$emit('getUserProfile')
        }
      })
    },
    setCodeExpire() {
      this.timeOut = setTimeout(() => {
        this.codeExpire = false
        clearInterval(this.timer)
        clearTimeout(this.timeOut)
      }, 60000)
    }
  }
}
</script>
<style lang="scss">
.invalid-code{
  top: 0;
  text-align: center;
  position: absolute;
  width: 250px;
  height: 250px;
  background: rgba(255,255,255,0.95);
  line-height: 250px;
}

</style>
