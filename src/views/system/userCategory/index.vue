<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="80px" size="small">
      <el-form-item :label="$t('material.category')" prop="name">
        <el-input v-model="queryParams.name" clearable :placeholder="$t('rfq.pleaseEnterCategoryCodename')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" clearable :placeholder="$t('common.status')">
          <el-option
            v-for="dict in statusDictDatas"
            :key="parseInt(dict.value)"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('supplier.purchasingOrganization')" prop="orgId">
        <el-select v-model="queryParams.orgId" class="searchValue">
          <el-option v-for="item in orgList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="handleConfigOperateName" prop="userIds" label-width="100px">
        <el-select v-model="queryParams.userIds" class="searchValue" filterable multiple>
          <el-option v-for="item in userList" :key="item.id" :label="item.nickname" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:user-category:assign-user-categories']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleConfig()"
        >{{ handleConfigOperateName }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table
      v-if="refreshTable"
      ref="multipleTable"
      v-loading="loading"
      :data="list"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      row-key="id"
      @selection-change="handleSelectionChange"
      @select="handleSelect"
      @select-all="handleSelectAll"
    >
      <el-table-column type="selection" width="30" />
      <el-table-column align="left" :label="$t('common.categoryCode')" prop="code" />
      <el-table-column align="center" :label="$t('common.name')" prop="name" />
      <el-table-column align="center" :label="$t('common.status')" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('supplier.purchasingOrganization')" prop="orgId">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="scope.row.orgId" />
        </template>
      </el-table-column>
      <el-table-column v-if="type==='SQE'" align="center" :label="$t('SQE')" prop="users" width="180" />
      <el-table-column v-if="type==='Sourcing'" align="center" :label="$t('common.sourcing')" prop="users" width="180" />
    </el-table>

    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item :label="$t('supplier.user')" prop="userIds">
          <el-select v-model="form.userIds" class="searchValue" filterable multiple>
            <el-option v-for="item in userList" :key="item.id" :label="item.nickname" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getUsersCache } from '@/api/system/user'
import { getPurchaseOrgCache } from '@/api/system/purchaseOrg'
import { assignUserCategories, getUserCategoryList } from '@/api/system/userCategory'

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Categories',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 品类树选项
      categoryOptions: [],
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 品类列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 是否显示弹出层
      open: false,
      // 当前维护的人员类型
      type: '',
      // 查询参数
      queryParams: {
        code: null,
        name: null,
        userIds: [],
        orgId: null,
        status: null,
        type: '',
        pageNo: 1
      },
      // 表单参数
      form: {},
      orgList: [],
      userList: [],
      // 选中数组值
      multipleSelection: [],
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS),
      // 配置操作名称（根据传入的type动态切换）
      handleConfigOperateName: ''
    }
  },
  created() {
    this.getOrgList()
    this.getUserList()
    this.type = this.$route.params.type
    if (this.type === 'SQE') {
      this.handleConfigOperateName = this.$t('scar.sqeConfiguration')
    } else if (this.type === 'Sourcing') {
      this.handleConfigOperateName = this.$t('supplier.procurementConfiguration')
    }
  },
  methods: {
    handleSelectAll() {
      const isAllSelected = this.$refs.multipleTable.store.states.isAllSelected
      const _handleSelectAll = data => {
        data.forEach(item => {
          item.select
          this.$refs.multipleTable.toggleRowSelection(item, isAllSelected)
          _handleSelectAll(item.children || [])
        })
      }
      _handleSelectAll(this.list)
    },
    handleSelect(selection, current) {
      // 判断selection中是否存在current,若是存在那么就代表是被勾选上了,若是不存在代表是取消勾选了
      const isChecked = !!selection.find(item => item.id === current.id)
      // 如果当前项被取消勾选
      if (!isChecked) {
        // 那么其所有的祖先也应该被取消勾选
        this.uncheckedParents(selection, current)
        // 那么其所有的后代也应该被取消勾选
        this.toggleCheckedChildrens(selection, current, false)
      } else {
        // 如果当前项被勾选
        // 那么若同一组的元素都被勾选了,那么父元素将也被勾选,依次往上类推
        this.checkedParents(selection)
        // 那么其所有的后代都要被勾选
        this.toggleCheckedChildrens(selection, current, true)
      }
    },
    uncheckedParents(selection, item) {
      const _uncheckedParents = data => {
        return data.find(element => {
          if (element.id === item.id) {
            return true
          } else if (_uncheckedParents(element.children || [])) {
            this.$refs.multipleTable.toggleRowSelection(element, false)
            for (let i = selection.length - 1; i >= 0; i--) {
              if (selection[i].id === element.id) {
                selection.splice(i, 1)
                break
              }
            }
            return true
          } else {
            return false
          }
        })
      }
      _uncheckedParents(this.list)
    },
    toggleCheckedChildrens(selection, item, isChecked) {
      const _toggleCheckedChildrens = data => {
        data.find(element => {
          this.$refs.multipleTable.toggleRowSelection(element, isChecked)
          if (isChecked && !selection.find(item => item.id === element.id)) {
            selection.push(element)
          } else if (!isChecked && selection.find(item => item.id === element.id)) {
            for (let i = selection.length - 1; i >= 0; i--) {
              if (selection[i].id === element.id) {
                selection.splice(i, 1)
                break
              }
            }
          }
          _toggleCheckedChildrens(element.children || [])
        })
      }
      _toggleCheckedChildrens(item.children || [])
    },
    checkedParents(selection) {
      const _checkedParents = element => {
        const children = element.children
        if (children && children.length) {
          const allChildrenChecked = children.every(child => {
            return _checkedParents(child)
          })
          if (allChildrenChecked) {
            this.$refs.multipleTable.toggleRowSelection(element, true)
            if (!selection.find(item => item.id === element.id)) {
              selection.push(element)
            }
          }
        }
        return selection.find(item => item.id === element.id)
      }
      this.list.forEach(element => {
        _checkedParents(element)
      })
    },
    /**
     * 选择框
     * @param val
     */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /**
     * 获取人员列表
     */
    getUserList() {
      getUsersCache({
        status: 0,
        isExternal: 1
      }).then(response => {
        this.userList = []
        this.userList.push(...response.data)
      })
    },
    /**
     * 获取采购组织列表
     */
    getOrgList() {
      getPurchaseOrgCache({
        status: 0
      }).then(response => {
        this.orgList = []
        this.orgList.push(...response.data)
        if (this.orgList) {
          this.queryParams.orgId = this.orgList[0].id
          this.getList()
        }
      })
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      this.queryParams.type = this.$route.params.type
      // 执行查询
      getUserCategoryList({ ...this.queryParams }).then(response => {
        this.list = this.handleTree(response.data, 'id')
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 转换品类数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    /** 表单重置 */
    reset() {
      this.form = {
        userIds: []
      }
      this.resetForm('form')
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false
      this.isExpandAll = !this.isExpandAll
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.userIds = []
      // 查询参数
      this.queryParams.code = null
      this.queryParams.name = null
      this.queryParams.userIds = []
      this.queryParams.status = null
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleConfig(row) {
      if (this.multipleSelection == null || this.multipleSelection.length === 0) {
        this.$modal.msgError(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.title = this.$t('配置品类和人员关系')
      this.reset()
      this.open = true
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        this.form.categoryIds = this.multipleSelection.map(i => {
          return i.id
        })
        this.form.type = this.$route.params.type
        this.form.orgId = this.multipleSelection.map(i => {
          return i.orgId
        })[0]
        assignUserCategories(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          this.open = false
          this.getList()
        })
      })
    }
  }
}
</script>
