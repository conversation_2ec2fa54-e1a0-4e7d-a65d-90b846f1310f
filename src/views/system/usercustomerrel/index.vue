<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('Customer')" prop="companyId">
        <el-select
          v-model="queryParams.customer"
          class="searchValue"
          clearable
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.OM_INCAP_CUSTOMER)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('supplier.user')" prop="userIds">
        <el-select v-model="queryParams.userIds" class="searchValue" filterable multiple>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS)"
            :key="dict.id"
            :label="dict.nickname"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>
    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:user-customer-rel:save']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleConfig"
        >{{ $t('配置Buyer') }}
        </el-button>
      </el-col>

      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table
      ref="multipleTable"
      v-loading="loading"
      :data="list"
      @selection-change="handleSelectionChange"
      @row-click="clickRow"
    >
      <el-table-column
        type="selection"
        width="30"
      />
      <el-table-column :label="$t('Customer')" align="left" prop="customer">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.OM_INCAP_CUSTOMER" :value="scope.row.customer" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('Buyer')" align="left" prop="userId">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="scope.row.userId" />
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item :label="$t('supplier.user')" prop="userIds">
          <el-select v-model="form.userId" class="searchValue" filterable>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS)"
              :key="dict.id"
              :label="dict.nickname"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUsersCache } from '@/api/system/user'
import { getBaseHeader } from '@/utils/request'
import { createUserCustomerRel, getUserCustomerRelPage } from '@/api/system/usercustomerrel/userCustomerRel'

export default {
  name: 'Usercustomerrel',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应商与人员关系列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 当前维护的人员类型
      type: '',
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        customer: null,
        userIds: [],
        type: 'Buyer'
      },

      // 表单参数
      form: {
        userId: []
      },
      // 选中数组值
      multipleSelection: [],
      // 配置操作名称（根据传入的type动态切换）
      handleConfigOperateName: ''
    }
  },
  async mounted() {
    this.getList()
    this.handleConfigOperateName = this.$t('Buyer')
  },
  methods: {
    clickRow(row) {
      this.$refs.multipleTable.toggleRowSelection(row)
    },
    /**
     * 选择框
     * @param val
     */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      this.queryParams.type = this.$route.params.type
      // 处理查询参数
      const params = { ...this.queryParams }
      // 执行查询
      getUserCustomerRelPage(params).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        customer: '',
        userId: undefined,
        userIds: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.queryParams.type = 'Buyer'
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleConfig() {
      if (this.multipleSelection == null || this.multipleSelection.length === 0) {
        this.$modal.msgError(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.reset()
      this.open = true
      this.title = this.$t('客户与人员配置')
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        this.form.customers = this.multipleSelection.map(i=>i.customer)
        // 修改的提交
        createUserCustomerRel(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
          this.open = false
          this.getList()
        })
      })
    }
  }
}
</script>
