<template>
  <div class="tqcdmHome">
    <el-card class="commonCard">
      <div style="font-size: 18px;font-weight: 700;margin-bottom: 10px">
        {{ $t('tqcdm.createANewTqcdmEvaluationTask') }}
      </div>
      <div class="tqcdmHome-tag">{{ $t('supplier.essentialInformation') }}</div>
      <el-form ref="evaluateInfo" :model="evaluateInfo" :rules="evaluateRules" inline label-width="170px">
        <el-form-item :label="$t('system.taskName')" class="loginInfoItem" prop="taskName">
          <show-or-edit
            :value="evaluateInfo.taskName"
            :disabled="viewOnly"
          >
            <el-input v-model="evaluateInfo.taskName " class="searchValue" />
          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('tqcdm.ttechnicalReviewer')"
          class="loginInfoItem"
          prop="tRelUser"
        >
          <show-or-edit
            :value="evaluateInfo.tRelUser"
            :dict="DICT_TYPE.COMMON_USERS"
            :disabled="viewOnly"
          >
            <el-select
              v-model="evaluateInfo.tRelUser"
              class="searchValue"
              :disabled="viewOnly"
              :placeholder="$t('auth.pleaseSelectADataSource')"
              clearable
              filterable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS)"
                :key="dict.value"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item :label="$t('tqcdm.targetCompletionTime')" class="loginInfoItem" prop="taskCompleteTime">
          <el-date-picker
            v-model="evaluateInfo.taskCompleteTime"
            class="searchValue"
            clearable
            :placeholder="$t('tqcdm.pleaseSelectTheTargetCompletionTime')"
            size="small"
            type="date"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item
          :label="$t('tqcdm.qqualityReviewer')"
          class="loginInfoItem"
          prop="qRelUser"
        >
          <show-or-edit
            :value="evaluateInfo.qRelUser"
            :dict="DICT_TYPE.COMMON_USERS"
            :disabled="viewOnly"
          >
            <el-select
              v-model="evaluateInfo.qRelUser"
              class="searchValue"
              :disabled="viewOnly"
              :placeholder="$t('auth.pleaseSelectADataSource')"
              filterable
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS)"
                :key="dict.value"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('tqcdm.evaluateCategory')"
          class="loginInfoItem"
          prop="evaluateCategoryId"
        >
          <cascading-category
            :multiple="false"
            :disabled="fieldLockByAuthInvoke || fieldLockByTqcdmInvoke"
            class="searchValue"
            :original-value.sync="evaluateInfo.evaluateCategoryId"
            @change="doGetTqcdmTemplateCategoryRel"
          />
        </el-form-item>
        <el-form-item
          :label="$t('tqcdm.ccostReviewer')"
          class="loginInfoItem"
          prop="cRelUser"
        >
          <show-or-edit
            :value="evaluateInfo.cRelUser"
            :dict="DICT_TYPE.COMMON_USERS"
            :disabled="viewOnly"
          >
            <el-select
              v-model="evaluateInfo.cRelUser"
              class="searchValue"
              :disabled="viewOnly"
              filterable
              :placeholder="$t('auth.pleaseSelectADataSource')"
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS)"
                :key="dict.value"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item :label="$t('tqcdm.evaluateSuppliers')" class="loginInfoItem" prop="evaluateSupplierId">
          <limit-select
            v-model="evaluateInfo.evaluateSupplierId"
            :placeholder="$t('scar.pleaseEnterKeywordsForFuzzySearch')"
            class="searchValue"
            multiple
            :disabled="fieldLockByAuthInvoke || fieldLockByTqcdmInvoke"
            :multiple-limit="10"
            clearable
            filterable
            :remote-method="doListSupplier"
            remote
            @clear="clearEvaluateSupplier"
          >
            <el-option
              v-for="item in evaluateSuppliers"
              :key="item.supplierId"
              :label="item.name"
              :value="item.supplierId"
            />
          </limit-select>
        </el-form-item>
        <el-form-item
          :label="$t('tqcdm.ddeliveryReviewer')"
          class="loginInfoItem"
          prop="dRelUser"
        >
          <show-or-edit
            :value="evaluateInfo.dRelUser"
            :dict="DICT_TYPE.COMMON_USERS"
            filterable
            :disabled="viewOnly"
          >
            <el-select
              v-model="evaluateInfo.dRelUser"
              class="searchValue"
              :disabled="viewOnly"
              :placeholder="$t('auth.pleaseSelectADataSource')"
              clearable
              filterable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS)"
                :key="dict.value"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('order.company')"
          prop="companyId"
          class="loginInfoItem"
        >
          <show-or-edit
            :value="evaluateInfo.companyId"
            :dict="DICT_TYPE.COMMON_COMPANY"
            :disabled="viewOnly"
          >
            <el-select
              v-model="evaluateInfo.companyId"
              class="searchValue"
              :disabled="viewOnly"
              filterable
              :placeholder="$t('auth.pleaseSelectADataSource')"
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_COMPANY)"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </show-or-edit>

        </el-form-item>
        <el-form-item
          :label="$t('tqcdm.mmanagementReviewer')"
          class="loginInfoItem"
          prop="mRelUser"
        >
          <show-or-edit
            :value="evaluateInfo.mRelUser"
            :dict="DICT_TYPE.COMMON_USERS"
            :disabled="viewOnly"
          >
            <el-select
              v-model="evaluateInfo.mRelUser"
              class="searchValue"
              :disabled="viewOnly"
              filterable
              :placeholder="$t('auth.pleaseSelectADataSource')"
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS)"
                :key="dict.value"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item :label="$t('tqcdm.evaluationRequirementsExplanation')" class="loginInfoItem" prop="otherContent">
          <show-or-edit
            :value="evaluateInfo.evaluateRequirement"
            :disabled="viewOnly"
          >
            <el-input
              v-model="evaluateInfo.evaluateRequirement"
              class="searchValue"
              :disabled="viewOnly"
              :rows="4"
              maxlength="500"
              show-word-limit
              type="textarea"
            />
          </show-or-edit>

        </el-form-item>
      </el-form>

      <div v-if="evaluateInfo.evaluateCategoryId">
        <div class="tqcdmHome-tag">{{ $t('tqcdm.templateInformation') }}</div>
        <el-form ref="evaluateInfo0" :model="evaluateInfo" label-width="170px">
          <el-form-item
            :label="$t('tqcdm.selectEvaluationTemplate')"
            class="loginInfoItem"
            prop="versionId"
            :rules="{
              required : true,
              trigger: 'change',
              message: '请选择'
            }"
          >
            <show-or-edit
              :value="evaluateInfo.versionId"
              type="Custom"
              :custom-list="evaluateTemplateList.data"
              :disabled="viewOnly"
            >
              <div style="display: flex">
                <t-select-table
                  ref="selectTable"
                  :table="evaluateTemplateList"
                  :columns="evaluateTemplateList.columns"
                  :default-select-val="defaultSelectLevel"
                  :max-height="540"
                  :keywords="{ label: 'name', value: 'versionId' }"
                  style="width: 65%;"
                  @radioChange="radioChange"
                />
                <el-link style="margin-left: 10px" type="primary" @click="$router.push('/tqcdm/config/config')">{{ $t('tqcdm.configureTemplate')}}</el-link>
              </div>
            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('tqcdm.addReferenceSuppliers')" class="loginInfoItem" prop="referenceSupplierId">
            <el-popover
              placement="bottom-start"
              width="445"
              trigger="click"
            >
              <el-table
                ref="referenceSuppliersList"
                :data="referenceSuppliersList"
                :max-height="300"
                :default-sort="{
                  prop: 'hasTradeInSixMonth',
                  order: 'descending'
                }"
                @selection-change="referenceSupplierChange"
              >
                <el-table-column width="30" type="selection" />
                <el-table-column width="160" property="supplierName" :label="$t('auth.supplierName')" />
                <el-table-column width="105" property="createTime" :label="$t('tqcdm.assessmentDate')" sortable>
                  <template #default="scope">
                    {{ dayjs(scope.row.createTime).format('YYYY-MM-DD') }}
                  </template>
                </el-table-column>
                <el-table-column align="center" width="120" property="hasTradeInSixMonth" :label="$t('tqcdm.tradingWithinSixMonths')" sortable>
                  <template #default="scope">
                    <dict-tag :value="scope.row.hasTradeInSixMonth" :type="DICT_TYPE.INFRA_BOOLEAN_STRING" />
                  </template>
                </el-table-column>
              </el-table>
              <el-select
                ref="select"
                slot="reference"
                v-model="evaluateInfo.referenceSupplierId"
                class="supplierSelect"
                style="width: 65%;"
                filterable
                clearable
                multiple
                :multiple-limit="9"
                :popper-append-to-body="false"
                @remove-tag="removeTag"
                @clear="$refs.referenceSuppliersList.clearSelection()"
              >
                <el-option
                  v-for="item in referenceSuppliersList"
                  :label="item.supplierName"
                  :value="item.supplierId +'-'+ item.projectId"
                />
              </el-select>
            </el-popover>
          </el-form-item>
        </el-form>
      </div>

      <div v-if="evaluateInfo.evaluateCategoryId" style="display: flex;justify-content: center;align-items: center;">
        <el-button plain type="primary" @click="doStartEvaluate">{{ $t('tqcdm.initiateEvaluation') }}</el-button>
      </div>
    </el-card>

  </div>
</template>

<script>
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { getSupplierName } from '@/api/supplier/account'
import { prepareReferenceSupplierEvaluate, prepareTemplateEvaluate, startEvaluate } from '@/api/tqcdm'
import dayjs from 'dayjs'
import LimitSelect from '../../../components/LimitSelect/index.vue'

export default {
  name: 'Create/:evaluateid',
  components: {
    LimitSelect,
    ShowOrEdit
  },
  data() {
    return {
      viewOnly: false,
      authId: undefined,
      // 评估项目表单校验
      evaluateRules: {
        taskName: [{ required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }],
        taskCompleteTime: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'blur' }],
        tRelUser: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        qRelUser: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        cRelUser: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        dRelUser: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        mRelUser: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        evaluateCategoryId: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        evaluateSupplierId: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        companyId: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'blur' }],
        versionId: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'blur' }]
      },
      evaluateInfo: {
        taskName: '', // 任务名称
        taskCompleteTime: '', // 目标完成时间
        tRelUser: '', // T（技术）评审人
        qRelUser: '', // Q（质量）评审人
        cRelUser: '', // C（成本）评审人
        dRelUser: '', // D（交付）评审人
        mRelUser: '', // M（管理）评审人
        evaluateCategoryId: '', // 评估品类id
        evaluateSupplierId: [], // 评估供应商（多个，（评估供应商 + 参考供应商） 最多10个）
        referenceSupplierId: [], // 参考供应商（多个，（评估供应商 + 参考供应商） 最多10个）
        companyId: '', // 公司
        evaluateRequirement: '', // 评估要求说明
        versionId: '' // 模板版本id
      },
      evaluateSuppliers: [], // 待评估的供应商下拉集合
      referenceSuppliers: [], // 待参考的供应商下拉集合
      categoryList: [],
      evaluateTemplateList: {
        data: [],
        columns: [
          { label: '模板名称', width: '149px', prop: 'templateName' },
          { label: '版本', width: '30px', prop: 'version', noShowTip: true },
          { label: '创建日期', width: '80px', prop: 'createTime' }
        ]
      },
      referenceSuppliersList: [],
      defaultSelectLevel: [],
      fieldLockByAuthInvoke: false, // 认证模块触发评估项目则需要将带入的 品类+供应商字段disable。
      fieldLockByTqcdmInvoke: false, // TQCDM模块触发评估项目则需要将带入的 更多的字段disable。
      searchSupplier: ''
    }
  },
  created() {
    if (this.$route.query.authId) {
      // 认证模块触发的回显关键字段（品类+供应商）
      this.authId = this.$route.query.authId
      this.evaluateInfo.evaluateCategoryId = parseInt(this.$route.query.categoryId)
      this.doGetTqcdmTemplateCategoryRel(this.evaluateInfo.evaluateCategoryId)
      this.evaluateInfo.evaluateSupplierId.push(parseInt(this.$route.query.supplierId))
      // 用于供应商的回显
      this.evaluateSuppliers.push({
        supplierId: parseInt(this.$route.query.supplierId),
        name: this.$route.query.supplierName
      })
      this.fieldLockByAuthInvoke = true
    }
    if (this.$route.query.invokeTqcdmId) {
      // TQCDM结果列表-重新评估 触发的回显关键字段 （供应商，评估模板-版本，品类，公司，评审人）

      // 品类 + 评估模板
      this.evaluateInfo.evaluateCategoryId = parseInt(this.$route.query.categoryId)
      this.doGetTqcdmTemplateCategoryRel(this.evaluateInfo.evaluateCategoryId)

      // 供应商
      this.evaluateInfo.evaluateSupplierId.push(parseInt(this.$route.query.supplierId))
      // 用于供应商的回显
      this.evaluateSuppliers.push({
        supplierId: parseInt(this.$route.query.supplierId),
        name: this.$route.query.supplierName
      })

      // 公司
      this.evaluateInfo.companyId = parseInt(this.$route.query.companyId)

      // 评审人
      this.evaluateInfo.tRelUser = parseInt(this.$route.query.tUser)
      this.evaluateInfo.qRelUser = parseInt(this.$route.query.qUser)
      this.evaluateInfo.cRelUser = parseInt(this.$route.query.cUser)
      this.evaluateInfo.dRelUser = parseInt(this.$route.query.dUser)
      this.evaluateInfo.mRelUser = parseInt(this.$route.query.mUser)

      this.fieldLockByTqcdmInvoke = true
    }
  },
  mounted() {
    this.viewOnly = this.$route.query.viewOnly === 'true'
    this.getCategories()
  },
  methods: {
    dayjs,
    getDictDatas,
    // 获取评估品类关联的TQCDM模板下拉集合
    doGetTqcdmTemplateCategoryRel(val) {
      if (val) {
        prepareTemplateEvaluate({ categoryId: val }).then(res => {
          res.data.forEach(a => {
            a.name = `${a.templateName}_${a.version}_${dayjs(a.createTime).format('YYYYMMDD')}`
            a.createTime = dayjs(a.createTime).format('YYYY.MM.DD')
          })
          this.evaluateTemplateList.data = res.data
          this.$refs.selectTable.clear()
          this.defaultSelectLevel = []
          if (this.$route.query.versionId) {
            this.$nextTick(() => {
              this.evaluateInfo.versionId = parseInt(this.$route.query.versionId)
              this.defaultSelectLevel = [this.evaluateInfo.versionId]
              this.doGetReferenceSupplier(this.evaluateInfo.versionId)
            })
          } else {
            this.evaluateInfo.versionId = ''
          }
        })
      }
    },
    // 获取待评估的供应商下拉集合
    doListSupplier(query) {
      if (query) {
        getSupplierName({ supplierName: query }).then(res => {
          this.evaluateSuppliers = res.data.map(item => {
            return {
              supplierId: item.slice(0, item.indexOf('-')),
              name: item.slice(item.lastIndexOf('-') + 1)
            }
          })
        })
      }
    },
    /**
     * 获取待参考的供应商下拉集合
     * @param versionId 模板的版本id（最细粒度）
     * @param supplierName 此参数可以忽略，前端通过下拉模板列表的change事件获得全部的关联的供应商，search功能在前端做就行
     */
    doGetReferenceSupplier(versionId, supplierName) {
      if (!versionId) {
        this.referenceSuppliersList = []
        return
      }
      prepareReferenceSupplierEvaluate({
        versionId: versionId,
        supplierName: supplierName
      }).then(res => {
        this.referenceSuppliersList = res.data
      })
    },
    // clear 待评估的供应商下拉集合
    clearEvaluateSupplier() {
      this.referenceSuppliers = []
      this.evaluateInfo.evaluateSupplierId = []
    },
    // clear 待参考的供应商下拉集合
    clearReferenceSupplier() {
      // this.evaluateSuppliers = []
      this.evaluateInfo.referenceSupplierId = []
    },
    // 获取品类
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    /**
     * 发起TQCDM评估项目
     * 1- （评估供应商 + 参考供应商） 最多10个
     * 2- 必填项卡控
     */
    doStartEvaluate() {
      if ((this.evaluateInfo.evaluateSupplierId?.length + this.evaluateInfo.referenceSupplierId?.length) > 10) {
        this.$message.warning(this.$t('tqcdm.theTotalNumberOfEvaluatedSuppliersAndReferenceSuppliersCannotExceed'))
        return
      }
      this.$refs.evaluateInfo0.validate(valid => {
        if (!valid) {
          return
        }
      })
      this.$refs.evaluateInfo.validate(valid => {
        if (valid) {
          //  1- 组装 模板名称等冗余字段
          var tqcdmTemplate = this.evaluateTemplateList.data?.find(i => i.versionId === this.evaluateInfo.versionId)
          if (tqcdmTemplate) {
            this.evaluateInfo.evaluateTemplateId = tqcdmTemplate.templateId
            this.evaluateInfo.evaluateTemplateName = tqcdmTemplate.templateName
            this.evaluateInfo.evaluateTemplateVersion = tqcdmTemplate.version
            this.evaluateInfo.evaluateTemplateVersionId = tqcdmTemplate.versionId
          }
          //  2- 组装 评估供应商集合
          this.evaluateInfo.evaluateSupplierList = this.evaluateInfo.evaluateSupplierId.map(i => {
            return {
              supplierId: Number(i)
            }
          })

          // //  3- 组装 参考供应商集合
          this.evaluateInfo.referenceSupplierList = this.evaluateInfo.referenceSupplierId.map(i => {
            return {
              referenceSupplierId: Number(i.split('-').at(0)),
              referenceProjectId: Number(i.split('-').at(1))
            }
          })

          if (this.authId) {
            this.evaluateInfo.authId = this.authId
          }
          startEvaluate({ ...this.evaluateInfo }).then(res => {
            this.$message.success(this.$t('supplier.submittedSuccessfully'))
            this.emitter.emit('refreshTqcdm')
            this.$tab.closeOpenPage(`/tqcdm/tqcdmindex`)
          })
        }
      })
    },
    async radioChange(args, str1) {
      this.evaluateInfo.versionId = args.versionId
      this.doGetReferenceSupplier(args.versionId)
    },

    referenceSupplierChange(selection) {
      this.evaluateInfo.referenceSupplierId = selection.map(a => a.supplierId + '-' + a.projectId)
    },
    removeTag(val) {
      this.$refs.referenceSuppliersList.toggleRowSelection(this.referenceSuppliersList
        .find(a => String(a.supplierId) === val.split('-').at(0) && String(a.projectId) === val.split('-').at(1)), false)
    }
  }
}

</script>

<style scoped lang="scss">

.tqcdmHome {
  padding: 25px 15px;
  &-tag{
    font-size: 14px;
    font-weight: bold;
    margin: 15px 0 15px 12px;
    position: relative;
  }
  &-tag::before{
    content: "";
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: #4d93b9;
  }
}

.commonCard {
  margin: 10px 0;
}

.loginInfoItem {
  width: 45%;
  margin-right: 0;
  ::v-deep .el-form-item__content{
    width: calc(100% - 170px);
  }
}
.searchValue{
  width: 60%;
}
.supplierSelect{
  ::v-deep .el-select-dropdown{
    display: none;
  }
}

</style>
