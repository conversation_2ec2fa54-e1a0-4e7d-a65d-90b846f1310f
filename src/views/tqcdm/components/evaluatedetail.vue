<template>
  <div class="tqcdmHome">
    <el-card class="commonCard">
      <div>
        <span style="font-size: 18px;font-weight: bold">
          {{ evaluateInfo.taskName }}
          <dict-tag :type="DICT_TYPE.TQCDM_TQCDM_EVALUATE_PROJECT_STATUS" :value="evaluateInfo.status" />
        </span>
      </div>
      <span>

        <div style="margin: 10px 0">
          <span style="color: #929292">{{$t('tqcdm.evaluateSuppliers')}}</span>
          <span style="color:#383838;">{{ evaluateInfo.evaluateSupplierList?.map(i => i.supplierName).join(' | ') }} </span>
        </div>

      </span>
      <div>
        <el-descriptions
          :column="2"
          style="background: #F9F9F9;padding: 10px 50px;margin: 10px 0"
          :content-style="{background:'#F9F9F9 '}"
          :label-style="{background: '#F9F9F9 ',width: '200px',color:'#92929'}"
        >
          <el-descriptions-item :label="$t('material.category')" :span="1">
            <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="evaluateInfo.evaluateCategoryId" />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('tqcdm.ttechnicalReviewer')" :span="1">
            <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="evaluateInfo.tRelUser" />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('tqcdm.evaluationTemplate')" :span="1">
            {{ evaluateInfo.evaluateTemplateName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('tqcdm.qqualityReviewer')" :span="1">
            <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="evaluateInfo.qRelUser" />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('tqcdm.targetCompletionDate')" :span="1">
            {{ evaluateInfo.taskCompleteTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('tqcdm.ccostReviewer')" :span="1">
            <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="evaluateInfo.cRelUser" />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('auth.authenticationDocumentCode')" :span="1">
            {{ evaluateInfo.taskNo }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('tqcdm.ddeliveryReviewer')" :span="1">
            <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="evaluateInfo.dRelUser" />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('common.creationDate')" :span="1">
            {{ evaluateInfo.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('tqcdm.mmanagementReviewer')" :span="1">
            <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="evaluateInfo.mRelUser" />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('rfq.createdBy')" :span="1">
            <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="evaluateInfo.creator" />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('order.company')" :span="1">
            <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="evaluateInfo.companyId" />
          </el-descriptions-item>

        </el-descriptions>
      </div>

      <!--      评估项-->
      <el-tabs
        ref="anchor"
        v-model="selectedTab"
        :nav-list="filteredNav"
        @tab-click="changeTab"
      >
        <el-tab-pane v-for="item in filteredNav" :name="item.id" :label="item.label" />
      </el-tabs>

      <div style="margin: 8px;display: flex;justify-content: space-between">
        <div>
          <span v-if="evaluateInfo.status === 'waiting_evaluated'">
            <el-button v-if="evaluateInfo.referenceSupplierList.length > 0" plain type="primary" @click="showCompare">{{$t('tqcdm.viewReferenceSuppliers')}}</el-button>
            <el-button v-if="!viewOnly" plain type="primary" @click="batchSatis">{{$t('tqcdm.batchSatisfaction')}}</el-button>
            <el-button v-if="!viewOnly" plain type="primary" @click="doSupplierInfoCompareDownload">{{$t('tqcdm.basicInformationComparisonDownload')}}</el-button>

          </span>
        </div>
        <el-button
          v-if="!viewOnly
            && selectedTab!=='all'
            && !waitingEvaluatedButtonVisible
            &&evaluateInfo.tqcdmTypeStatusList.find(a=>a.tqcdmType=== selectedTab)?.status === 'over_evaluated'"
          type="danger"
          plain
          @click="revokeProject"
        >{{$t('tqcdm.withdraw')}}</el-button>
      </div>
      <div
        v-loading="loading"
        style="display: flex;"
      >
        <el-scrollbar
          v-if="evaluateInfo.evaluateSupplierList.length >1&&evaluateInfo.status === 'waiting_evaluated'"
          style="height: 100%;flex: 0 0 200px"
        >
          <el-menu
            style="height: 100%;"
            background-color="#F9F9F9"
            text-color="#565656"
            active-text-color="#383838"
            :default-active="String(selectedSupplier)"
          >
            <el-menu-item
              v-for="item in evaluateInfo.evaluateSupplierList"
              :index="String(item.supplierId)"
              @click="changeMenu(item)"
            >
              <span
                slot="title"
                style="display: flex; justify-content: space-between; align-items: center;"
                :style="{ color: item.pass ? '' : 'red' }"
              >
                <!-- 供应商名称部分 -->
                <div
                  style="flex: 1 1 auto; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
                >
                  {{ item.supplierName }}
                </div>

                <!-- 括号部分 -->
                <div v-if="evaluateInfo.status === 'waiting_evaluated'" style="flex: 0 0 auto;">
                  ({{ item.completed }}/{{ evaluateList.length }})
                </div>
              </span>

            </el-menu-item>
          </el-menu>
        </el-scrollbar>

        <to-eval-table
          v-if="freshTable"
          :view-only="evaluateInfo.status !== 'waiting_evaluated' || viewOnly"
          style="flex: 1 1 95%;position:relative;overflow: hidden"
          :supplier="evalSupplier"
          :data="evaluateList"
        />

      </div>

      <div v-if="['waiting_confirmed','completed','revoked'].includes(evaluateInfo.status)">
        <div class="tqcdmHome-tag">{{$t('tqcdm.summaryInformation')}}</div>
        <el-form ref="evaluateInfo" :model="evaluateInfo" label-width="170px">
          <el-form-item :label="$t('tqcdm.abilitySatisfaction')">
            <div style="display: flex;flex-wrap: wrap">

              <span v-for="(item,index) in evaluateInfo.evaluateSupplierList" style="display: flex">
                {{ item.supplierName }}
                <span
                  class="circle fulfilled"
                  :class="satisfaction(item.supplierId)"
                />
                {{ item.capacityPercent*100 }}%
                <span v-if="evaluateInfo.evaluateSupplierList.length -1 !== index" style="color: #939393;margin: 0 8px">|</span>
              </span>
              <div style="margin-left: 200px">{{ $t('tqcdm.viewEvaluationAttachment') }}</div>
              <el-button
                :disabled="fileList?.length===0"
                class="uploadBtn"
                plain
                size="small"
                style="padding: 5px 9px"
                @click="showFile=true"
              >
                {{ fileList?.length }}
              </el-button>
            </div>

          </el-form-item>
          <el-form-item :label="$t('tqcdm.evaluationSummaryOpinion')">
            <show-or-edit
              :value="evaluateInfo.evaluateSummary"
              :disabled="evaluateInfo.status !== 'waiting_confirmed' ||viewOnly"
            >
              <el-input
                v-model="evaluateInfo.evaluateSummary"
                type="textarea"
                show-word-limit
                :maxlength="300"
                :rows="3"
                placeholder="请输入内容"
              />
            </show-or-edit>

            <el-checkbox v-if="evaluateInfo.status === 'waiting_confirmed'&&!viewOnly" v-model="evaluateInfo.pdfIncludeReferenceSupplier">生成PDF报告时，附带参考供应商信息</el-checkbox>
          </el-form-item>
        </el-form>

      </div>

      <div style="display: flex;justify-content: center       ;align-items: center;margin-top: 15px">
        <el-descriptions :column="8" label-class-name="purchaseRequire">
          <el-descriptions-item v-if="['waiting_evaluated'].includes(evaluateInfo.status)" :label="$t('tqcdm.uploadEvaluationAttachment')">
            <el-upload
              :action="uploadUrl"
              :disabled="false"
              :file-list="fileList?.filter(i => this.selectedTab === i.tqcdmType)"
              :headers="getBaseHeader()"
              :limit="5"
              :on-preview="onPreview"
              :on-remove="onRemove"
              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
              :show-file-list="false"
              multiple
            >
              <el-button
                class="uploadBtn"
                icon="el-icon-plus"
                plain
                size="mini"
                type="primary"
              />

            </el-upload>
          </el-descriptions-item>
          <el-descriptions-item v-if="['waiting_evaluated'].includes(evaluateInfo.status)" :span="1" :label="$t('tqcdm.viewEvaluationAttachment')">
            <el-button
              :disabled="fileList?.filter(i => this.selectedTab === i.tqcdmType)?.length===0"
              :type="fileList?.filter(i => this.selectedTab === i.tqcdmType)?.length?'primary':''"
              class="uploadBtn"
              plain
              size="small"
              style="padding: 5px 9px"
              @click="showFile=true"
            >
              {{ fileList?.filter(i => this.selectedTab === i.tqcdmType)?.length }}
            </el-button>

          </el-descriptions-item>
          <el-descriptions-item :span="1" :label="$t('tqcdm.viewAndCompareAttachments')">
            <el-button
              :disabled="fileList?.filter(i => i.tqcdmType === '')?.length===0"
              :type="fileList?.filter(i => i.tqcdmType === '')?.length?'primary':''"
              class="uploadBtn"
              plain
              size="small"
              style="padding: 5px 9px"
              @click="showFile=true;showCompareFile=true"
            >
              {{ fileList?.filter(i => i.tqcdmType === '')?.length }}
            </el-button>

          </el-descriptions-item>
          <el-descriptions-item :label-style="{width: 0}" :span="2">
            <div
              v-if="evaluateInfo.evaluateCategoryId && evaluateInfo.status === 'waiting_evaluated' &&!viewOnly&&
          ['waiting_evaluated', 'over_evaluated'].includes(evaluateInfo.tqcdmTypeStatusList.find(a=>a.tqcdmType=== selectedTab)?.status)"
              style="display: flex;justify-content: center;align-items: center;margin-top: 15px"
            >
              <el-button
                v-if="waitingEvaluatedButtonVisible"
                :loading="loading"
                type="primary"
                @click="submitEval"
              >{{ $t('提交'+ selectedTab.toUpperCase() + '评估') }}
              </el-button>
              <el-button
                v-if="waitingEvaluatedButtonVisible"
                :loading="loading"
                plain
                type="primary"
                @click="saveEval()"
              >{{ $t('common.save') }}
              </el-button>
            </div>
            <div
              v-if="evaluateInfo.status === 'waiting_confirmed' && !viewOnly"
              style="display: flex;justify-content: center;align-items: center;margin-top: 15px;padding-left: 40%"
            >
              <el-button :loading="loading" type="primary" @click="submitOverview(true)">{{ $t('common.submit') }}</el-button>
              <el-button :loading="loading" plain type="primary" @click="submitOverview(false)">{{ $t('common.save') }}</el-button>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <!--            查看附件-->
    <el-dialog
      v-if="showFile"
      :visible.sync="showFile"
      :title="$t('scar.viewAttachments')"
      width="400px"
    >
      <el-upload
        :action="uploadUrl"
        :disabled="viewOnly"
        :file-list="selectedTab !== 'all' ? (showCompareFile ? fileList?.filter(i => i.tqcdmType === '') : fileList?.filter(i => this.selectedTab === i.tqcdmType)) : fileList"
        :headers="getBaseHeader()"
        :limit="5"
        :on-preview="onPreview"
        :on-remove="onRemove"
        :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,)"
        multiple
      />
      <div slot="footer">
        <el-button type="primary" @click="showFile=false;showCompareFile=false">{{ $t('order.close') }}</el-button>
      </div>

    </el-dialog>

    <el-dialog
      v-if="compareVisible"
      width="1000px"
      :visible.sync="compareVisible"
      :title="`参考供应商(${evaluateInfo.referenceSupplierList.length})`"
    >
      <el-tabs v-model="compareSelected" @tab-click="showCompare">
        <el-tab-pane
          v-for="item in navList"
          :label="item.label"
          :name="item.id"
        >
          <el-table :data="compareList">
            <el-table-column label="" type="index" prop="nameLocal" width="30px" align="center" />
            <el-table-column :label="$t('tqcdm.evaluationPointDescription')" prop="nameLocal" width="200px" />
            <el-table-column v-for="(item,index) in evaluateInfo.referenceSupplierList" prop="weight" align="center">
              <template #header="scope">
                <el-button
                  style="text-decoration: underline"
                  type="text"
                  @click="$router.push(`/supplier/supplierinfo/${item.referenceSupplierId}?supplierName=${item.supplierName}&viewOnly=true`)"
                > {{ item.supplierName }}</el-button>
              </template>
              <el-table-column :label="$t('tqcdm.whetherItMeetsTheRequirements')">
                <template #default="scope">
                  <span
                    class="circle fulfilled"
                    :class="satisfactionMap[scope.row.evaluateSupplier[index].evaluateResult]"
                  />
                </template>
              </el-table-column>
              <el-table-column :label="$t('common.remarks')" prop="remarks">
                <template #default="scope">
                  {{ scope.row.evaluateSupplier[index].remarks || '-' }}
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" />
    </el-dialog>
    <el-dialog
      v-if="batchVisible"
      title="完全满足"
      :visible.sync="batchVisible"
      width="400px"
    >
      {{ $t('当前这一供应商所有未评估项将批量评估为' +
      '完全满足 是否确认操作？')}}
      <el-button type="text" @click="notAgain">{{$t('tqcdm.noMorePrompts')}}</el-button>
      <div slot="footer">
        <el-button @click="batchVisible=false">{{$t('sp.cancel')}}</el-button>
        <el-button type="primary" @click="batchInput">{{$t('common.confirm')}}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import { getBaseHeader } from '@/utils/request'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import {
  detailEvaluate,
  detailOfEvaluateSupplierAndTqcdmType,
  getReferenceSupplierDetail,
  getTemplateDetail,
  noMoreTip,
  saveEvaluateResult,
  specTqcdmReject,
  specTqcdmSubmit,
  summarySubmit,
  saveFileRel,
  delFileRel,
  supplierInfoCompareDownload
} from '@/api/tqcdm'
import Anchor from '@/components/Anchor/index.vue'
import ToEvalTable from './toEvalTable.vue'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'

export default {
  name: 'Tqcdmdetail/:evaluateid',
  components: {
    ToEvalTable,
    ShowOrEdit,
    Anchor
  },
  data() {
    return {
      getBaseHeader,
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin-api/infra/file/upload',
      // 包含tqcdm类型的列表集合，基于上面的选择类型进行filter
      // type = this.selectedTab
      fileList: [],
      viewOnly: false,
      showFile: false,
      showCompareFile: false,
      evaluateInfo: {
        evaluateSupplierList: [],
        tqcdmTypeStatusList: []
      },
      templateList: [],
      navList: [
        {
          label: this.$t('tqcdm.ttechnicalEvaluation'),
          id: 't'
        },
        {
          label: this.$t('tqcdm.qqualityEvaluation'),
          id: 'q'
        },
        {
          label: this.$t('tqcdm.ccostEvaluation'),
          id: 'c'
        },
        {
          label: this.$t('tqcdm.ddeliveryEvaluation'),
          id: 'd'
        },
        {
          label: this.$t('tqcdm.mmanagementEvaluation'),
          id: 'm'
        }

      ],
      selectedSupplier: null,
      tEvaluateList: [],
      qEvaluateList: [],
      cEvaluateList: [],
      dEvaluateList: [],
      mEvaluateList: [],
      selectedSupplierName: '',
      loading: false,
      selectedTab: '',
      evaluateList: [],
      freshTable: true,
      compareVisible: false,
      compareSelected: '',
      compareList: [],
      result: [],
      satisfactionMap: {
        'match-all': 'fulfilled-selected',
        'match-part': 'portion-selected',
        'no-match': 'unsatisfactory-selected',
        '': 'default'
      },
      batchVisible: false,
      // 待评估状态时的底部状态按钮需要和 【撤回】按钮形成互斥效果
      waitingEvaluatedButtonVisible: true
    }
  },
  computed: {
    filteredNav() {
      const a = this.navList.filter(nav => this.evaluateInfo.tqcdmTypeStatusList.some(a => a.tqcdmType === nav.id))
      if (this.evaluateInfo.status === 'waiting_evaluated') {
        if (this.viewOnly) {
          return a
        } else {
          return this.navList.filter(nav => this.evaluateInfo.tqcdmTypeStatusList.some(a => a.tqcdmType === nav.id && a.relUser === this.$store.getters.userId))
        }
      } else {
        return [{
          label: this.$t('tqcdm.summary'),
          id: 'all'
        },
          ...a]
      }
    },
    totalScore() {
      return this.templateList.reduce((acc, cur) => acc + cur.weight, 0)
    },
    evalSupplier() {
      return this.evaluateInfo.evaluateSupplierList.filter(d => this.evaluateInfo.status !== 'waiting_evaluated' ? true : d.supplierId === this.selectedSupplier)
    }
  },
  mounted() {
    this.viewOnly = this.$route.query.viewOnly === 'true'
    this.doGetEvaluateDetail(1)
  },
  methods: {
    onPreview(file) {
      if (file.url) {
        window.open(file.url)
      }
      if (file?.response?.data.url) {
        window.open(file.response.data.url)
      }
    },
    onRemove(file, fileList) {
      this.fileList.splice(this.fileList.findIndex(a => a.id === file.id), 1)
      if (file.id) {
        delFileRel(file.id).then(res => {
          this.$message({
            message: this.$t('common.delSuccess'),
            type: 'success'
          })
        })
      }
    },
    onSuccess(response, file, fileList) {
      var f = {
        fileId: response.data.id,
        name: response.data.name,
        url: response.data.url,
        projectId: this.$route.params.evaluateId,
        tqcdmType: this.selectedTab,
        businessValue: 'evaluate_type'
      }
      saveFileRel(f).then(res => {
        this.fileList.push(f)
        this.$message.success(this.$t('common.uploadSucceeded'))
      })
    },
    // 获取评估项目详情
    doGetEvaluateDetail(init) {
      detailEvaluate({
        id: this.$route.params.evaluateId
      }).then(res => {
        this.evaluateInfo = res.data
        this.fileList = res.data.fileRelList
        // 特殊处理。待确认状态下查看 附件时，需要处理文件名称，否则无法确认文件名称属于哪个 评估项
        if (this.evaluateInfo.status !== 'waiting_evaluated' && this.fileList?.length > 0) {
          this.fileList.forEach(f => {
            var label = getDictDatas(DICT_TYPE.TQCDM_TQCDM_TYPE).filter(d => d.value === f.tqcdmType)?.[0].label
            if (label) {
              f.name = label + f.name
            }
          })
        }
        if (init) {
          this.selectedSupplier = res.data.evaluateSupplierList.at(0).supplierId
          this.selectedSupplierName = res.data.evaluateSupplierList.at(0).supplierName
          this.selectedTab = this.filteredNav.at(0).id
          this.compareSelected = this.filteredNav.at(0).id
        }
        this.init(this.selectedTab)
      })
    },
    async doGetTemplateDetail() {
      const res = await getTemplateDetail({
        projectId: this.evaluateInfo.id,
        versionId: this.evaluateInfo.evaluateTemplateVersionId
      })
      this.templateList = res.data
    },
    // 获得TQCDM评估项目详情-供应商+tqcdm类型的结果列表s
    async doGetSupplierEvaluateResultByType(supplierId, tqcdmType) {
      const res = await detailOfEvaluateSupplierAndTqcdmType({
        projectId: this.$route.params.evaluateId,
        supplierId
      })
      this.result = res.data
    },
    async init(type = 't') {
      this.loading = true
      await Promise.all([this.doGetTemplateDetail(), this.doGetSupplierEvaluateResultByType()])
      const filterAndMapTemplates = (type, supplierId, supplierName) => {
        return this.templateList
          .filter(i => type !== 'all' ? i.tqcdmType === type : true)
          .map(a => {
            const common = {
              ...a,
              evaluateSupplier:
                this.evaluateInfo.evaluateSupplierList.filter(
                  d => this.evaluateInfo.status !== 'waiting_evaluated' ? true
                    : d.supplierId === this.selectedSupplier
                ).map(c => {
                  const answer = this.result.find(b => b.templateDetailId === a.id && c.supplierId === b.supplierId)
                  return {
                    projectId: this.$route.params.evaluateId,
                    supplierId: c.supplierId,
                    supplierName: c.supplierName,
                    templateDetailId: a.id,
                    templateDetailName: a.nameLocal,
                    tqcdmType: type,
                    evaluateResult: answer?.evaluateResult || '',
                    remarks: answer?.remarks || ''
                  }
                })
            }
            return common
          })
      }
      this.loading = false
      this.evaluateList = filterAndMapTemplates(type, this.selectedSupplier, this.selectedSupplierName)
      if (this.evaluateInfo.status === 'waiting_evaluated') {
        this.validPass()
      }

      const index = this.evaluateInfo.tqcdmTypeStatusList.findIndex(a => a.tqcdmType === this.selectedTab)
      if (index >= 0 && this.evaluateInfo.tqcdmTypeStatusList[index].status === 'over_evaluated') {
        this.waitingEvaluatedButtonVisible = false
      } else {
        this.waitingEvaluatedButtonVisible = true
      }
    },
    validPass() {
      this.evaluateInfo.evaluateSupplierList.forEach(c => {
        c.completed = this.templateList.filter(i => i.tqcdmType === this.selectedTab).reduce((acc, cur) => {
          const answer = this.result.find(b => b.templateDetailId === cur.id && b.supplierId === c.supplierId)
          if (answer?.evaluateResult) {
            acc++
          }
          return acc
        }, 0)
        c.pass = this.templateList.filter(i => i.tqcdmType === this.selectedTab && i.required).every(a => {
          return this.result.find(b => b.templateDetailId === a.id && b.supplierId === c.supplierId)?.evaluateResult
        })
      })
    },
    async saveEval(sumbit) {
      this.loading = true
      const data = await saveEvaluateResult({
        projectId: this.$route.params.evaluateId,
        evaluateResultList: this.evaluateList.map(a => a.evaluateSupplier).flat(1)
      })
      this.loading = false
      if (!sumbit) {
        this.doGetEvaluateDetail()
        this.$message.success(this.$t('tqcdm.saved'))
      }
    },
    async submitEval() {
      this.loading = true
      const temp = this.evaluateInfo.evaluateSupplierList.find(a => a.supplierId === Number(this.selectedSupplier))
      temp.pass = this.evaluateList.every(a => a.required ? a.evaluateSupplier.at(0).evaluateResult : true)
      temp.completed = this.evaluateList.reduce((acc, cur) => {
        if (cur.evaluateSupplier.at(0).evaluateResult) {
          acc++
        }
        return acc
      }, 0)
      if (!this.evaluateInfo.evaluateSupplierList.every(a => a.pass)) {
        this.$message.error(this.$t('tqcdm.pleaseCompleteAllEvaluations'))
        this.loading = false
        return
      }
      await this.saveEval('sumbit')

      specTqcdmSubmit({
        projectId: this.$route.params.evaluateId,
        tqcdmType: this.selectedTab
      }).then(res => {
        const index = this.evaluateInfo.tqcdmTypeStatusList.findIndex(a => a.tqcdmType === this.selectedTab)
        this.evaluateInfo.tqcdmTypeStatusList[index].status = 'over_evaluated'
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
        // 隐藏底部的操作按钮
        this.waitingEvaluatedButtonVisible = false
        if (this.evaluateInfo.tqcdmTypeStatusList.every(a => a.status === 'over_evaluated')) {
          this.emitter.emit('refreshTqcdm')
          this.$tab.closeOpenPage('/tqcdm/tqcdmindex')
        }
        this.loading = false
      })
    },
    async changeMenu(item) {
      this.selectedSupplier = item.supplierId
      this.selectedSupplierName = item.supplierName
      this.freshTable = false
      if (!this.viewOnly) {
        await this.saveEval(true)
      }
      await this.init(this.selectedTab)
      this.freshTable = true

      const index = this.evaluateInfo.tqcdmTypeStatusList.findIndex(a => a.tqcdmType === this.selectedTab)
      if (index >= 0 && this.evaluateInfo.tqcdmTypeStatusList[index].status === 'over_evaluated') {
        this.waitingEvaluatedButtonVisible = false
      } else {
        this.waitingEvaluatedButtonVisible = true
      }
    },
    // 关闭当前tab并打开列表页面
    revokeProject() {
      this.$confirm(`是否要撤回${this.navList.find(a => a.id === this.selectedTab).label}评估结果`, '', {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('sp.cancel'),
        confirmButtonClass: 'el-button--danger is-plain'
      }).then(() => {
        specTqcdmReject({
          projectId: this.$route.params.evaluateId,
          tqcdmType: this.selectedTab
        }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.waitingEvaluatedButtonVisible = true
          this.emitter.emit('refreshTqcdm')
          this.$tab.closeOpenPage(`/tqcdm/tqcdmindex`)
        })
      })
    },
    changeTab(name) {
      this.init(this.selectedTab)
    },
    satisfaction(supplierId) {
      const supplierResult = this.result?.filter(a => a.supplierId === supplierId)
      if (supplierResult.some(d => d.evaluateResult === 'no-match')) {
        return 'unsatisfactory-selected'
      } else if (supplierResult.some(d => d.evaluateResult === 'match-part')) {
        return 'portion-selected'
      } else {
        return 'fulfilled-selected'
      }
    },
    submitOverview(submit) {
      this.loading = true
      summarySubmit({
        ...this.evaluateInfo,
        projectId: this.$route.params.evaluateId,
        submit
      }).then(res => {
        if (submit) {
          this.$tab.closeOpenPage('/tqcdm/tqcdmindex')
          this.emitter.emit('refreshTqcdm')
        } else {
          this.$message.success(this.$t('common.savedSuccessfully'))
        }
        this.loading = false
      })
    },
    showCompare() {
      this.compareVisible = true
      getReferenceSupplierDetail({ id: this.$route.params.evaluateId }).then(res => {
        const result = Object.values(res.data.resultDetailRespVOS).flat(1)
        this.compareList = this.templateList.filter(a => a.tqcdmType === this.compareSelected).map(b => {
          return {
            ...b,
            evaluateSupplier: this.evaluateInfo.referenceSupplierList.map(c => {
              const answer = result.find(d => d.templateDetailId === b.id && c.referenceSupplierId === d.supplierId)
              return {
                evaluateResult: answer?.evaluateResult || '',
                remarks: answer?.remarks || ''
              }
            })
          }
        })
      })
    },
    // 基础信息对比下载
    // 1. 点击【基础信息对比下载】，自动下载文件。
    // 2. 下载文件样式见附件
    // 3. 对比文件自动保存至任务的附件。汇总页也可查看该附件，并进行提交。如有用户（有多人可以评价）重新点下载，存在附件里的文件需被覆盖，仅留一份。
    doSupplierInfoCompareDownload() {
      supplierInfoCompareDownload(this.evaluateInfo.id).then(res => {
        this.$message.success(this.$t('tqcdm.downloadSuccessful'))
        window.open(res.data)
        this.doGetEvaluateDetail()
      })
    },
    batchSatis() {
      if (this.evaluateInfo.tipByBatchOperate) {
        this.batchInput()
      } else {
        this.batchVisible = true
      }
    },
    notAgain() {
      noMoreTip()
      this.evaluateInfo.tipByBatchOperate = true
      this.batchInput()
    },
    batchInput() {
      this.batchVisible = false
      this.evaluateList.forEach(a => {
        a.evaluateSupplier.forEach(b => {
          if (!b.evaluateResult) {
            b.evaluateResult = 'match-all'
          }
        })
      })
    }
  }
}

</script>

<style scoped lang="scss">

.tqcdmHome {
  padding: 25px 15px;
}
::v-deep .el-descriptions__body {
  background: #F9F9F9 ;
}
.commonCard {
  margin: 10px 0;
}

.loginInfoItem {
  width: 45%;
  margin-right: 0;
  ::v-deep .el-form-item__content{
    width: calc(100% - 170px);
  }
}
.searchValue{
  width: 60%;
}

::v-deep .el-menu-item.is-active{
  //color: #383838!important;
  font-weight: 600!important;
  color: #000!important;
  opacity: 0.8!important; /* 整个元素的透明度设置为0.8 */
}
::v-deep .el-menu-item:hover, .el-menu-item:focus{
  background: rgba(73,150,184,0.20)!important;
}

::v-deep .el-scrollbar__view{
  overflow-y: hidden;
}
.tqcdmHome {
  padding: 25px 15px;
  &-tag{
    font-size: 14px;
    font-weight: bold;
    margin: 15px 0 15px 12px;
    position: relative;
  }
  &-tag::before{
    content: "";
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: #4d93b9;
  }
}

.circle {
  margin: 3px;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background-color: white;
  font-size: 20px;
}

.fulfilled{
  //border: 2px solid #27ae60;
  &-selected{
    background-color: #67C23A;
  }
}

.portion{
  //border: 2px solid #ff8f3b;
  &-selected{
    background-color: #FF994F;
  }
}

.unsatisfactory{
  //border: 2px solid #FA5151;
  &-selected{
    background-color: #FA5151;
  }
}
.default{
  border: 2px solid #939393;
  //background-color: #F9F9F9;
}

::v-deep .purchaseRequire {
  width: 120px;
  justify-content: flex-end;
}
</style>
