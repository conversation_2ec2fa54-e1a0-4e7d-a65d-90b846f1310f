<template>
  <el-table
    :data="data"
    max-height="560"
    :cell-style="cellStyle"
  >
    <el-table-column label="" type="index" prop="nameLocal" width="50px" align="center" />
    <el-table-column :label="$t('tqcdm.evaluationPointDescription')" prop="nameLocal" min-width="200px">
      <template slot-scope="scope">
        <span :class="{required: scope.row.required === true}">{{ scope.row.nameLocal }}</span>
      </template>
    </el-table-column>
    <el-table-column v-if="viewOnly" :label="$t('tqcdm.category')" prop="weight" width="110px" align="center">
      <template slot-scope="scope">
        <dict-tag :type="DICT_TYPE.TQCDM_TQCDM_TYPE" :value="scope.row.tqcdmType" />
      </template>
    </el-table-column>
    <el-table-column :label="$t('tender.weight')" prop="weight" width="50px" align="center">
      <template slot-scope="scope">
        <span>{{ scope.row.weight }}</span>
      </template>
    </el-table-column>
    <el-table-column v-for="(item,index) in supplier" v-if="!viewOnly" prop="weight" align="center">
      <template #header="scope">
        <el-button
          style="text-decoration: underline"
          type="text"
          @click="$router.push(`/supplier/supplierinfo/${item.supplierId}?supplierName=${item.supplierName}&viewOnly=true`)"
        > {{ item.supplierName }}</el-button>
      </template>
      <el-table-column :label="$t('tqcdm.completelySatisfied')" prop="satisfaction" width="100px" align="center">
        <template slot-scope="scope">
          <div class="center-circle">
            <span
              class="circle fulfilled"
              :class="{
                'fulfilled-selected' :scope.row.evaluateSupplier[index].evaluateResult === 'match-all'
              }"
              @click="scope.row.evaluateSupplier[index].evaluateResult = 'match-all'"
            /></div>

        </template>
      </el-table-column>
      <el-table-column :label="$t('tqcdm.partialSatisfactionAndControllableRisk')" prop="satisfaction" width="100px" align="center">
        <template slot-scope="scope">
          <div class="center-circle">

            <div
              class="circle portion"
              :class="{
                'portion-selected' :scope.row.evaluateSupplier[index].evaluateResult === 'match-part'
              }"
              @click="scope.row.evaluateSupplier[index].evaluateResult = 'match-part'"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tqcdm.notSatisfiedOrHighRisk')" prop="satisfaction" width="100px" align="center">
        <template slot-scope="scope">
          <div class="center-circle">

            <div
              class="circle unsatisfactory"

              :class="{
                'unsatisfactory-selected' :scope.row.evaluateSupplier[index].evaluateResult === 'no-match'
              }"
              @click="scope.row.evaluateSupplier[index].evaluateResult = 'no-match'"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.remarks')" prop="remark" min-width="120">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.evaluateSupplier[index].remarks"
            :autosize="{ minRows: 2, maxRows: 6 }"
            resize="none"
            :rows="1"
            style="min-width: 115px;width: 100%"
            type="textarea"
            :maxlength="500"
          />
        </template>
      </el-table-column>
    </el-table-column>

    <el-table-column v-for="(item,index) in supplier" v-if="viewOnly" prop="weight" align="center">
      <template #header="scope">
        <el-button
          style="text-decoration: underline"
          type="text"
          @click="$router.push(`/supplier/supplierinfo/${item.supplierId}?supplierName=${item.supplierName}&viewOnly=true`)"
        > {{ item.supplierName }}</el-button>
      </template>
      <el-table-column :label="$t('tqcdm.whetherItMeetsTheRequirements')">
        <template #default="scope">
          <span
            class="circle"
            style="cursor:default"
            :class="satisfactionMap[scope.row.evaluateSupplier[index].evaluateResult]"
          />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.remarks')" prop="remarks">
        <template #default="scope">
          {{ scope.row.evaluateSupplier[index].remarks || '-' }}
        </template>
      </el-table-column>
    </el-table-column>

  </el-table>

</template>
<script>
export default {
  name: 'ToEvalTable',
  props: {
    data: { type: Array, default: () => [], required: false },
    supplier: { type: Array, required: true },
    viewOnly: { type: Boolean, default: false }
  },
  data() {
    return {
      satisfactionMap: {
        'match-all': 'fulfilled-selected-disable',
        'match-part': 'portion-selected-disable',
        'no-match': 'unsatisfactory-selected-disable',
        '': 'default'
      }
    }
  },
  methods: {
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (row.required && column.property === 'satisfaction') {
        return {
          background: '#4996b81a'
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.center-circle{
  display: flex;
  justify-content: center;
}
.circle {
  margin: 3px 0;
  cursor: pointer;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background-color: white;
  font-size: 20px;
}

.fulfilled{
  border: 2px solid #27ae60;
  &-selected{
    background-color: #D2EDC4;
    &-disable{
      background-color: #67C23A;

    }
  }
}

.portion{
  border: 2px solid #ff8f3b;
  &-selected{
    background-color: #ffd5b4;
    &-disable{
      background-color: #FF994F;

    }
  }
}

.unsatisfactory{
  border: 2px solid #FA5151;
  &-selected{
    background-color: #ffc3c3;
    &-disable{
      background-color: #FA5151;

    }
  }
}
.default{
  border: 2px solid #939393;
}

.required::before{
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

</style>
