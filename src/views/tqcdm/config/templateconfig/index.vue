<template>
  <!--总览页面-->
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.templateName"
        style="flex: 0 1 40%"
        :placeholder="$t('common.pleaseEnter')"
        clearable
        @keyup.enter.native="doSearch"
      />
      <el-button plain type="primary" @click="queryParams.pageNo = 1;doSearch();">{{ $t('common.search') }}</el-button>
      <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>

    </div>
    <!-- 列表 -->
    <vxe-grid
      ref="configTable"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
    >
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="row.status" />
      </template>
      <template #categoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId?.split(',')" />
      </template>
      <template #toolbar_buttons>
        <el-row :gutter="10" style="width: 100%" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" size="mini" @click="open=true;title='新增评估模板'">
              {{ $t('common.add') }}
            </el-button>
          </el-col>
          <right-toolbar :list-id="girdOption.id" :show-search.sync="showSearch" :custom-columns.sync="girdOption.columns" @queryTable="doSearch" />
        </el-row>
      </template>
      <template #operate="{row}">
        <OperateDropDown
          :menu-item="[
            {
              name: $t('common.modify'),
              show: true,
              action: (row)=>$router.push(`/tqcdm/config/detail/${row.versionId}?templateName=${row.templateName}&version=${row.version}&createTime=${row.createTime}&templateId=${row.templateId}`),
              para:row
            },
          ]"
        />
      </template>
    </vxe-grid>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="doSearch"
    />

    <!--    TQCDM评估模板新增修改dialog-->
    <el-dialog :title="title" :visible.sync="open" width="500px" :modal-append-to-body="false">
      <el-form
        ref="tqcdmTemplateForm"
        :rules="{
          templateName: [
            { required: true, message: $t('common.pleaseEnter'), trigger: 'blur' }
          ],
          categoryIds: [
            { required: true, message: $t('common.pleaseEnter'), trigger: 'blur' }
          ],

        }"
        :model="tqcdmTemplateForm"
        label-width="100px"
      >
        <el-form-item :label="$t('rfq.templateName')" prop="templateName">
          <el-input v-model="tqcdmTemplateForm.templateName" :placeholder="$t('common.pleaseEnter')" clearable />
        </el-form-item>
        <el-form-item :label="$t('tqcdm.applicableCategories')" prop="categoryIds">
          <cascading-category
            :multiple="true"
            :disabled="false"
            style="width: 100%"
            :original-value.sync="tqcdmTemplateForm.categoryIds"
          />
        </el-form-item>
        <el-form-item :label="$t('tqcdm.versionDescription')" prop="remark">
          <el-input v-model="tqcdmTemplateForm.remark" :placeholder="$t('common.pleaseEnter')" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="doSaveTemplateConfig">{{ $t('common.confirm') }}</el-button>
        <el-button @click="doCancel">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { pageTqcdmTemplate } from '@/api/tqcdm'
import OperateDropDown from '@/components/OperateDropDown/index.vue'

export default {
  name: 'Templateconfig',
  components: {
    OperateDropDown
  },
  data() {
    return {
      // 涉及到数据库更新操作的按钮操作增加loading操作，防止重复操作
      loadingButton: false,
      loading: false,
      total: 0,
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      showSearch: false,
      categoryList: [],
      tqcdmTemplateForm: {
        categoryIds: [], // 适用的品类
        remark: '', // 版本说明
        templateName: '', // 模板名称
        status: 0
      },
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        templateName: '' // 顶部模糊搜索
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'config',
        minHeight: 0,
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          { title: this.$t('rfq.templateName'), field: 'templateName', visible: true, width: 390 },
          { title: this.$t('tqcdm.applicableCategories'), slots: { default: 'categoryId' }, field: 'categoryId', visible: true, width: 300 },
          { title: this.$t('tqcdm.versionNumber'), field: 'version', visible: true, width: 220 },
          { title: this.$t('common.status'), slots: { default: 'status' }, field: 'status', visible: true, width: 170 },
          { title: this.$t('tqcdm.versionDescription'), field: 'remark', visible: true, width: 400 },
          { title: this.$t('common.createTime'), field: 'createTime',
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true, width: 220 },
          { field: 'operate', slots: { default: 'operate' }, fixed: 'right', title: this.$t('common.operate'), visible: true, showOverflow: false, width: 80 }
        ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      }
    }
  },
  mounted() {
    this.doSearch()
    this.getCategories()
  },
  methods: {
    getDictDatas,
    // 搜索接口
    doSearch() {
      this.loading = true
      pageTqcdmTemplate(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      }).catch(_ => {

      })
    },
    // 单据列表重置并搜索
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        templateName: '' // 顶部模糊搜索
      }
      this.doSearch()
    },
    /**
     * 创建TQCDM模板配置
     *
     * 创建模板新建后，应该出现的是评估模板详情页面。当用户完成第一次提交内容后，才是版本1.
     * 当用户从【修改】按钮进入，只要变更内容提交，就会升级到版本2.
     */
    doSaveTemplateConfig() {
      this.$refs.tqcdmTemplateForm.validate(valid => {
        if (valid) {
          this.open = false
          var cids = this.tqcdmTemplateForm.categoryIds.join(',')
          var name = this.tqcdmTemplateForm.templateName
          var remark = this.tqcdmTemplateForm.remark
          this.$nextTick(()=>{
            this.$router.push((`/tqcdm/config/detail/0?templateName=${name}&version=1&remark=${remark}&cids=${cids}`))
          })
        }
      })
    },
    // dialog 撤销
    doCancel() {
      this.open = false
      this.tqcdmTemplateForm = {
        categoryIds: [], // 适用的品类
        remark: '', // 版本说明
        templateName: '', // 模板名称
        status: 0
      }
    },
    // 获取品类
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    }
  }
}

</script>

<style scoped lang="scss">
.searchItem{
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 170px);
  }
}
.searchValue{
  width: 95%;
}
</style>
