<template>
  <!--总览页面-->
  <div class="app-container">
    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('tqcdm.tqcdmAssessmentTemplateDetails') }}
        <i
          :style="{transform: 'rotate(180deg)'}"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
        />
      </div>

      <!-- 列表 -->
      <vxe-grid
        ref="detailConfigTable"
        :data="list"
        v-bind="girdOption"
        keep-source
        @edit-closed="debouncedEditClosed"
      >

        <template #tqcdmTypeEdit="{row}">
          <el-select
            v-model="row.tqcdmType"
            :placeholder="$t('common.pleaseSelect')"
            clearable
            style="width: 90%"
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.TQCDM_TQCDM_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
        <template #nameLocalEdit="{row}">
          <el-input
            v-model="row.nameLocal"
            style="width: 90%"
            :placeholder="$t('common.pleaseEnter')"
            autosize
            :maxlength="100"
          />
        </template>
        <template #requiredEdit="{row}">
          <el-select
            v-model="row.required"
            :placeholder="$t('common.pleaseSelect')"
            clearable
            style="width: 90%"
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
        <template #weightEdit="{row}">
          <vxe-input
            v-model="row.weight"
            max="999"
            type="integer"
            style="width: 90%"
            :placeholder="$t('common.pleaseEnter')"
          />
        </template>

        <template #toolbar_buttons>
          <el-row :gutter="10" style="width: 100%" class="mb8">
            <el-col :span="1.5">

              <el-button type="primary" size="mini" @click="createNewDetail">
                {{ $t('tqcdm.addEvaluationPoints') }}
              </el-button>
              <el-button type="primary" size="mini" @click="upload.open=true">
                {{ $t('supplier.batchUpload') }}
              </el-button>
              模板名称：{{ $route.query.templateName }}
              版本：{{ $route.query.version }}
              创建时间：{{ $route.query.createTime?dayjs(Number($route.query.createTime)).format('YYYY-MM-DD HH:mm:ss'):dayjs().format('YYYY-MM-DD HH:mm:ss') }}
            </el-col>

          </el-row>
        </template>
        <template #operate="{row, rowIndex}">
          <el-button type="text" @click="delTemplateDetail(row.id, rowIndex)">
            {{ $t('common.del') }}
          </el-button>
        </template>
      </vxe-grid>

      <el-form ref="templateForm" style="padding-top: 20px" :model="templateForm" :inline="true" label-width="100px">
        <el-form-item class="searchItem" :label="$t('tqcdm.applicableCategories')" prop="categoryIds">
          <cascading-category
            :multiple="true"
            :disabled="false"
            style="width: 95%"
            :original-value.sync="templateForm.categoryIds"
          />
        </el-form-item>
        <el-form-item class="searchItem" :label="$t('tqcdm.versionDescription')" prop="remark">
          <el-input
            v-model="templateForm.remark"
            class="searchValue"
            :placeholder="$t('common.pleaseEnter')"
            clearable
            :rows="3"
            maxlength="100"
            show-word-limit
            type="textarea"
          />
        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('common.status')"
          prop="status"
          :rules="{required: true, message: $t('system.statusCannotBeEmpty'), trigger: 'blur'}"
        >
          <el-radio-group v-model="templateForm.status">
            <el-radio v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="parseInt(dict.value)">
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div style="display: flex;justify-content: center;align-items: center;padding-top: 20px">
        <el-button plain type="primary" @click="doSaveTemplateConfig">{{ $t('order.determine') }}</el-button>
        <el-button plain type="primary" @click="$router.push('/tqcdm/config/config')">{{ $t('sp.cancel') }}</el-button>
      </div>
    </el-card>

    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          class="small-padding"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileImportSuccess"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { DICT_TYPE, getDictDataLabel, getDictDatas } from '@/utils/dict'
import { getTemplateDetail, saveTemplateDetailConfig, detailTqcdmTemplate, deleteTemplateDetail, downloadTemplate } from '@/api/tqcdm'
import { debounce } from 'throttle-debounce'
import dayjs from 'dayjs'
import { getBaseHeader } from '@/utils/request'
import {now} from "xe-utils";

export default {
  name: 'Detail/:id',
  data() {
    return {
      list: [],
      debouncedEditClosed: debounce(50, this.editClosed), // 设置防抖延迟时间，单位毫秒
      categoryList: [],
      // 上传相关
      upload: {
        title: this.$t('tqcdm.templateDataUpload'),
        open: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        isUploading: false,
        loading: false,
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/tqcdm/category-template-detail-config/import'
      },
      versionId: this.$route.params.id,
      templateForm: {
        categoryIds: [], // 适用的品类
        remark: '', // 版本说明
        status: 0 // 状态
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'detailconfig',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        editConfig: {
          trigger: 'click',
          mode: 'row',
          showUpdateStatus: true,
          autoClear: true // 自动清除编辑不能取消，否则不能进入edit-closed方法
        },
        rowConfig: {
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          { title: this.$t('rfq.serialNo'), field: 'sort', visible: true, width: 300 },
          { title: this.$t('tqcdm.category'), slots: { default: 'tqcdmTypeEdit' }, field: 'tqcdmType', visible: true, width: 300 },
          { title: this.$t('tqcdm.evaluationPointDescription'), slots: { default: 'nameLocalEdit' }, field: 'nameLocal', visible: true, width: 400 },
          { title: this.$t('tqcdm.requiredItem'), slots: { default: 'requiredEdit' }, field: 'required', visible: true, width: 170 },
          { title: this.$t('tender.weight'), slots: { default: 'weightEdit' }, field: 'weight', visible: true, width: 400 },
          { field: 'operate', slots: { default: 'operate' }, fixed: 'right', title: this.$t('common.operate'), visible: true, showOverflow: false, width: 120 }
        ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      storeCategory: [],
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS)
    }
  },
  computed: {
    dayjs() {
      return dayjs
    }
  },
  watch: {
    'templateForm.categoryIds': {
      handler(val) {
        if (val) {
          let flag = false

          this.storeCategory?.forEach(item => {
            if (!val.includes(item)) {
              flag = true
            }
          })
          if (flag) {
            this.$message.warning(this.$t('tqcdm.toAvoidAffectingTheOriginalTaskOrderOperationsCategoryDeletionIsNotAllowedAndOnlyOperationsCanBeAdded'))
            this.templateForm.categoryIds = [...new Set([...this.storeCategory, ...val])]
          }
        }
      }

    }
  },
  created() {
    if (this.$route.params.id !== '0') {
      this.doSearch()
      this.doGetTemplateDetail()
    }else {
      // 初始化部分字段
      // 品类
      // 备注
      this.storeCategory = this.$route.query.cids?.split(',').map(i => parseInt(i))
      this.templateForm.categoryIds = this.$route.query.cids?.split(',').map(i => parseInt(i))
      this.templateForm.remark = this.$route.query.remark
    }
    this.getCategories()
  },
  methods: {
    now,
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileImportSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }

      var maxsort = 0
      if (this.list && this.list?.length > 0) {
        maxsort = this.list.at(this.list.length - 1).sort
      }
      response.data.forEach(a => {
        a.sort = ++maxsort
        a.required = a.required + ''
      })

      this.list.push(...response.data)
      this.$message.success(this.$t('rfq.importSucceeded'))
    },
    importTemplate() {
      downloadTemplate().then(response => {
        this.$download.excel(response, this.$t('tqcdm.evaluationTemplateXlsx'))
      })
    },
    submitFileForm() {
      this.$refs.upload.submit()
    },
    getDictDatas,
    // 获取本次版本的模板详情
    doGetTemplateDetail() {
      detailTqcdmTemplate({
        versionId: this.versionId
      }).then(res => {
        this.storeCategory = res.data?.categoryId.split(',').map(i => parseInt(i))
        this.templateForm.categoryIds = res.data?.categoryId.split(',').map(i => parseInt(i))
        this.templateForm.remark = res.data?.remark
        this.templateForm.status = res.data?.status
      })
    },
    // 搜索接口
    doSearch() {
      getTemplateDetail({
        versionId: this.versionId
      }).then(res => {
        this.list = res.data
        this.list.forEach(i => {
          i.required = i.required + ''
        })
      }).catch(_ => {

      })
    },
    // 新增评估点。仅前端交互
    createNewDetail() {
      var size = this.list?.length
      const newItem = {
        sort: size + 1,
        tqcdmType: '',
        nameLocal: undefined,
        required: undefined,
        weight: ''
      }
      this.list.splice(size + 1, 0, newItem)
    },
    // 行编辑触发
    async editClosed({ row, rowIndex }) {
      if (rowIndex < 0) {
        return
      }
      const $table = this.$refs.detailConfigTable
      this.errMap = await $table.validate([row]).catch(errMap => errMap)
      if (this.errMap) {
        await $table.reloadRow(row)
      } else {
        // 恢复当前行为数据库的值
        // $table.revertData(row)
      }
    },
    // 创建TQCDM模板配置
    doSaveTemplateConfig() {
      this.list.forEach(i => {
        i.versionId = this.$route.params.id
        i.templateId = this.$route.query.templateId
      })

      var reqVO = {
        reqVOList: this.list,
        categoryIds: this.templateForm.categoryIds,
        remark: this.templateForm.remark,
        status: this.templateForm.status
      }

      if (this.$route.params.id !== '0') {
        // 评估模板列表的【修改】按钮进入
        reqVO.versionId = this.$route.params.id
        reqVO.templateId = this.$route.query.templateId
      }else {
        // 评估模板的【新建】按钮进入
        // 需要额外初始化模板对象
        reqVO.templateName = this.$route.query.templateName
      }

      saveTemplateDetailConfig({ ...reqVO }).then(res => {
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
        this.$tab.closeOpenPage('/tqcdm/config/config')
        this.versionId = res.data
        this.doSearch()
      })
    },
    // 获取品类
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    // 模板明细删除
    delTemplateDetail(id, rowIndex) {
      // 物理删除
      // 其余的交给 确认去提交删除操作
      this.list.splice(rowIndex, 1)
      this.$message.success(this.$t('common.delSuccess'))
    }
  }
}

</script>

<style scoped lang="scss">

.commonCard {
  margin: 10px 0;
}
.searchItem{
  width: 40%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 170px);
  }
}
.searchValue{
  width: 95%;
}
</style>
