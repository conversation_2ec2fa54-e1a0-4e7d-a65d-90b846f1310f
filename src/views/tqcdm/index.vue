<template>
  <!--总览页面-->
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.searchText"
        style="flex: 0 1 40%"
        :placeholder="$t('tqcdm.taskNameSupplierName')"
        clearable
        @keyup.enter.native="doSearch"
      />
      <el-button plain type="primary" @click="queryParams.pageNo = 1;doSearch();">{{ $t('common.search') }}</el-button>
      <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>

      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button type="text">
          {{ $t('common.advancedSearch') }}
        </el-button>
        <i
          class="el-icon-arrow-up"
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>

    </div>
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="170px">
      <el-form-item class="searchItem" :label="$t('auth.authenticationDocumentCode')">
        <el-input v-model="queryParams.taskNo" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('system.taskName')">
        <el-input v-model="queryParams.taskName" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('order.company')">
        <el-select v-model="queryParams.companyId" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.COMMON_COMPANY, 0)"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('common.status')">
        <el-select v-model="queryParams.status" multiple class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.TQCDM_TQCDM_EVALUATE_PROJECT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('rfq.createdBy')">
        <el-select v-model="queryParams.creator" class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS)"
            :key="dict.value"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('scar.currentHandler')">
        <el-select v-model="queryParams.currentProcessor" multiple class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS)"
            :key="dict.value"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('order.timeType')" prop="dateType">
        <el-select v-model="queryParams.dateType" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.TQCDM_TQCDM_EVALUATE_SEARCH_DATE_TYPE)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" label=" ">
        <el-date-picker
          v-model="queryParams.time"
          class="searchValue"
          type="daterange"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('auth.supplierName')">
        <el-input v-model="queryParams.supplierName" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('material.category')" prop="category">
        <el-cascader
          v-model="queryParams.category"
          class="searchValue"
          filterable
          clearable
          :props="{ value: 'id',label:'name',multiple :true}"
          :options="categoryList"
          :placeholder="$t('material.category')"
        />
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <vxe-grid
      ref="tqcdmTable"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
    >
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.TQCDM_TQCDM_EVALUATE_PROJECT_STATUS" :value="row.status" />
      </template>
      <template #taskNo="{row}">
        <copyButton
          type="text"
          @click="$router.push(`/tqcdm/evaluate/detail/${row.id}?no=${row.taskNo}&viewOnly=true`)"
        >
          {{ row.taskNo }}
        </copyButton>
      </template>
      <template #tstatus="{row}">
        <dict-tag v-if="row.tstatus !== '' && row.tstatus !== null && row.tstatus !== '-'" :type="DICT_TYPE.TQCDM_TQCDM_EVALUATE_ITEM_STATUS" :value="row.tstatus" />
        <span v-else>
          -
        </span>
      </template>
      <template #qstatus="{row}">
        <dict-tag v-if="row.qstatus !== '' && row.qstatus !== null && row.qstatus !== '-'" :type="DICT_TYPE.TQCDM_TQCDM_EVALUATE_ITEM_STATUS" :value="row.qstatus" />
        <span v-else>
          -
        </span>
      </template>
      <template #cstatus="{row}">
        <dict-tag v-if="row.cstatus !== '' && row.cstatus !== null && row.cstatus !== '-'" :type="DICT_TYPE.TQCDM_TQCDM_EVALUATE_ITEM_STATUS" :value="row.cstatus" />
        <span v-else>
          -
        </span>
      </template>
      <template #dstatus="{row}">
        <dict-tag v-if="row.dstatus !== '' && row.dstatus !== null && row.dstatus !== '-'" :type="DICT_TYPE.TQCDM_TQCDM_EVALUATE_ITEM_STATUS" :value="row.dstatus" />
        <span v-else>
          -
        </span>
      </template>
      <template #mstatus="{row}">
        <dict-tag v-if="row.mstatus !== '' && row.mstatus !== null && row.mstatus !== '-'" :type="DICT_TYPE.TQCDM_TQCDM_EVALUATE_ITEM_STATUS" :value="row.mstatus" />
        <span v-else>
          -
        </span>
      </template>
      <template #evaluateCategoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.evaluateCategoryId" />
      </template>
      <template #creator="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.creator" />
      </template>
      <template #companyId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="row.companyId" />
      </template>
      <template #toolbar_buttons>
        <el-row :gutter="10" style="width: 100%" class="mb8">
          <el-col :span="1.5">

            <el-button type="primary" size="mini" @click="$router.push('/tqcdm/evaluate/create/0?viewOnly=false')">
              {{ $t('tqcdm.createANewEvaluation') }}
            </el-button>
          </el-col>

          <el-col :span="1.5">
            <el-button
              type="primary"
              size="mini"
              plain
              icon="el-icon-download"
              :loading="loadingButton"
              @click="doExportPageRecord()"
            >  {{ $t('order.download') }}</el-button>
          </el-col>
          <right-toolbar :list-id="girdOption.id" :show-search.sync="showSearch" :custom-columns.sync="girdOption.columns" @queryTable="doSearch" />
        </el-row>
      </template>
      <template #operate="{row}">
        <OperateDropDown
          :menu-item="[
            {
              // 当前办理人可见 && 点击进入评价页
              name: $t('tqcdm.evaluate'),
              show: ('waiting_evaluated' === row.status && row.currentProcessorList.includes($store.getters.userId)),
              action: (row)=>$router.push(`/tqcdm/evaluate/detail/${row.id}?no=${row.taskNo}&viewOnly=false`),
              para:row
            },
            {
              // 评估项目状态为【待确认】 && 创建人可见 && 点击进入评价汇总页
              name: $t('common.confirm'),
              show: ('waiting_confirmed' === row.status && row.creator === $store.getters.userId),
              action: (row)=>$router.push(`/tqcdm/evaluate/detail/${row.id}?no=${row.taskNo}&viewOnly=false`),
              para:row
            },
            {
              //撤销
              // 创建人可操作
              name: $t('rfq.revoke'),
              show: 'completed'!== row.status && $store.getters.permissions.includes('tqcdm:evaluate-supplier-record:cancel') && row.creator === $store.getters.userId ,
              action: (row) => doCancelEvaluate(row),
              para:row
            },
            {
              // 当前办理人可见 && 点击进入评价页
              name: $t('auth.downloadReport'),
              show: ('completed'=== row.status),
              action: (row) => doDownloadReport(row),
              para:row
            },
          ]"
        />
      </template>
    </vxe-grid>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="doSearch"
    />

  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE, getDictDataLabel, getDictDatas } from '@/utils/dict'
import { cancelEvaluate, exportEvaluateRecord, pageEvaluateRecord, downloadReport } from '@/api/tqcdm'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import event from '@/views/dashboard/mixins/event'

export default {
  name: 'Tqcdmindex',
  components: {
    OperateDropDown
  },
  mixins: [event],
  data() {
    return {
      // 涉及到数据库更新操作的按钮操作增加loading操作，防止重复操作
      loadingButton: false,
      loading: false,
      total: 0,
      list: [],
      showSearch: false,
      categoryList: [],
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        searchText: '', // 顶部模糊搜索
        taskNo: '', // 单据号
        taskName: '', // 单据名称
        companyId: '', // 公司
        status: [], // 状态
        creator: '', // 创建人
        currentProcessor: [], // 当前办理人
        dateType: '', // 时间类型
        time: [], // 时间
        supplierName: '', // 供应商名称
        category: [],
        sortBy: '',
        sortField: ''
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'tqcdm',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          { title: this.$t('system.taskName'), field: 'taskName', visible: true, width: 110 },
          { title: this.$t('auth.authenticationDocumentCode'), slots: { default: 'taskNo' }, field: 'taskNo', visible: true, width: 135 },
          { title: this.$t('common.creationDate'), field: 'createTime',
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true, width: 135 },
          { title: this.$t('system.taskStatus'), slots: { default: 'status' }, field: 'status', visible: true, width: 135 },
          { title: this.$t('tqcdm.ttechnicalStatus'), slots: { default: 'tstatus' }, field: 'tstatus', visible: true, width: 135 },
          { title: this.$t('tqcdm.qqualityStatus'), slots: { default: 'qstatus' }, field: 'qstatus', visible: true, width: 135 },
          { title: this.$t('tqcdm.ccostStatus'), slots: { default: 'cstatus' }, field: 'cstatus', visible: true, width: 135 },
          { title: this.$t('tqcdm.ddeliveryStatus'), slots: { default: 'dstatus' }, field: 'dstatus', visible: true, width: 135 },
          { title: this.$t('tqcdm.mmanagementStatus'), slots: { default: 'mstatus' }, field: 'mstatus', visible: true, width: 135 },
          { title: this.$t('material.category'), slots: { default: 'evaluateCategoryId' }, field: 'evaluateCategoryId', visible: true, width: 135 },
          { title: this.$t('auth.supplierName'), field: 'supplierName', visible: true, width: 135 },
          { title: this.$t('tqcdm.evaluationTemplate'), field: 'evaluateTemplateName', visible: true, width: 135 },
          { title: this.$t('order.edition'), field: 'evaluateTemplateVersion', visible: true, width: 135 },
          { title: this.$t('scar.completionTime'), field: 'completeTime',
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true, width: 135 },
          { title: this.$t('rfq.createdBy'), slots: { default: 'creator' }, field: 'creator', visible: true, width: 135 },
          { title: this.$t('scar.currentHandler'), field: 'currentProcessor', visible: true, width: 135 },
          { title: this.$t('order.company'), slots: { default: 'companyId' }, field: 'companyId', visible: true, width: 135 },
          { title: this.$t('financial.relatedDocuments'), field: 'relRecordNo', visible: true, width: 135 },
          { field: 'operate', slots: { default: 'operate' }, fixed: 'left', title: this.$t('common.operate'), visible: true, showOverflow: false, width: 35 }
        ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      }
    }
  },
  mounted() {
    this.doSearch()
    this.getCategories()
    this.emitter.on('refreshTqcdm', () => {
      this.doSearch()
    })
  },
  beforeDestroy() {
    this.emitter.off('refreshTqcdm')
  },
  methods: {
    getDictDatas,
    // 搜索接口
    doSearch() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      if (this.queryParams.category) {
        this.queryParams.categoryIds = this.queryParams.category.map(item => item?.at(-1))
      }

      pageEvaluateRecord(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      }).catch(_ => {

      })
    },
    // 单据列表重置并搜索
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        searchText: '', // 顶部模糊搜索
        taskNo: '', // 单据号
        taskName: '', // 单据名称
        companyId: '', // 公司
        status: [], // 状态
        creator: '', // 创建人
        currentProcessor: [], // 当前办理人
        dateType: '', // 时间类型
        time: [], // 时间
        supplierName: '', // 供应商名称
        category: [],
        sortBy: '',
        sortField: ''
      }
      this.doSearch()
    },
    // 能力矩阵报告下载
    doDownloadReport(row) {
      downloadReport({
        projectId: row.id
      }).then(res => {
        if (res.data) {
          window.open(res.data.url)
        }
      })
    },
    // 撤销评估项目
    doCancelEvaluate(row) {
      this.$modal.confirm(this.$t('scar.areYouSureYouWantToCancelTheCurrentDocument')).then(() => {
        cancelEvaluate({
          evaluateId: row.id
        }).then(res => {
          this.doSearch()
        })
      })
    },
    // 导出单据数据
    async doExportPageRecord(test) {
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      if (this.queryParams.category) {
        this.queryParams.categoryIds = this.queryParams.category.map(item => item?.at(-1))
      }

      this.loadingButton = true
      const res = await exportEvaluateRecord(this.queryParams)
      this.$download.excel(res, this.$t('tqcdm.tqcdmEvaluationProjectList'))
      this.loadingButton = false
    },
    // 获取品类
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    }
  }
}

</script>

<style scoped lang="scss">
.searchItem{
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 170px);
  }
}
.searchValue{
  width: 95%;
}
</style>
