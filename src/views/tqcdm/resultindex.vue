<template>
  <!--总览页面-->
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.searchText"
        style="flex: 0 1 40%"
        :placeholder="$t('auth.supplierName')"
        clearable
        @keyup.enter.native="doSearch"
      />
      <el-button plain type="primary" @click="queryParams.pageNo = 1;doSearch();">{{ $t('common.search') }}</el-button>
      <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>

      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button type="text">
          {{ $t('common.advancedSearch') }}
        </el-button>
        <i
          class="el-icon-arrow-up"
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>

    </div>
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="170px">
      <el-form-item class="searchItem" :label="$t('auth.authenticationDocumentCode')">
        <el-input v-model="queryParams.taskNo" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('auth.supplierName')">
        <el-input v-model="queryParams.supplierName" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('order.company')">
        <el-select v-model="queryParams.companyId" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.COMMON_COMPANY, 0)"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('tqcdm.evaluationTemplate')">
        <el-select v-model="queryParams.evaluateVersionId" class="searchValue" clearable filterable>
          <el-option
            v-for="item in templateConfigList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('rfq.createdBy')">
        <el-select v-model="queryParams.creator" class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS)"
            :key="dict.value"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('order.timeType')" prop="dateType">
        <el-select v-model="queryParams.dateType" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.TQCDM_TQCDM_EVALUATE_SEARCH_DATE_TYPE)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" label=" ">
        <el-date-picker
          v-model="queryParams.time"
          class="searchValue"
          type="daterange"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('material.category')" prop="category">
        <el-cascader
          v-model="queryParams.category"
          class="searchValue"
          filterable
          clearable
          :props="{ value: 'id',label:'name',multiple :true}"
          :options="categoryList"
          :placeholder="$t('material.category')"
        />
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <vxe-grid
      ref="tqcdmResultTable"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
    >
      <template #evaluateCategoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.evaluateCategoryId" />
      </template>
      <template #capacityPercent="{row}">
        <div style="display: flex;align-items: center">
          <span
            class="circle fulfilled"
            :class="satisfaction(row.evaluateResultColor)"
          />
          <span style="margin-left: 5px">{{ row.capacityPercent }}</span>
        </div>
      </template>
      <template #companyId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="row.companyId" />
      </template>
      <template #toolbar_buttons>
        <el-row :gutter="10" style="width: 100%" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" size="mini" @click="$router.push('/tqcdm/evaluate/create/0?viewOnly=false')">
              {{ $t('tqcdm.createANewEvaluation') }}
            </el-button>
          </el-col>

          <el-col :span="1.5">
            <el-button
              type="primary"
              size="mini"
              plain
              icon="el-icon-download"
              :loading="loadingButton"
              @click="doExportPageRecord()"
            >  {{ $t('order.download') }}</el-button>
          </el-col>
          <right-toolbar :list-id="girdOption.id" :show-search.sync="showSearch" :custom-columns.sync="girdOption.columns" @queryTable="doSearch" />
        </el-row>
      </template>
      <template #operate="{row}">
        <OperateDropDown
          :menu-item="[
            {
              name: $t('tqcdm.reEvaluate'),
              show: true,
              action: (row)=>$router.push(`/tqcdm/evaluate/create/0?viewOnly=false&tUser=${row.trelUser}&qUser=${row.qrelUser}&cUser=${row.crelUser}&dUser=${row.drelUser}&mUser=${row.mrelUser}&versionId=${row.versionId}&companyId=${row.companyId}&invokeTqcdmId=${row.id}&categoryId=${row.evaluateCategoryId}&supplierId=${row.supplierId}&supplierName=${row.supplierName}`),
              para:row
            },
          ]"
        />
      </template>
    </vxe-grid>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="doSearch"
    />

  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { exportEvaluateResultRecord, pageEvaluateResultRecord, pageTqcdmTemplate } from '@/api/tqcdm'
import OperateDropDown from '@/components/OperateDropDown/index.vue'

export default {
  name: 'Tqcdmresultindex',
  components: {
    OperateDropDown
  },
  data() {
    return {
      // 涉及到数据库更新操作的按钮操作增加loading操作，防止重复操作
      loadingButton: false,
      loading: false,
      total: 0,
      list: [],
      showSearch: false,
      categoryList: [],
      templateConfigList: [],
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        searchText: '', // 顶部模糊搜索
        taskNo: '', // 单据号
        supplierName: '', // 供应商名称
        companyId: '', // 公司
        evaluateVersionId: '', // 评估模板
        creator: '', // 创建人
        dateType: '', // 时间类型
        time: [], // 时间
        category: [],
        sortBy: '',
        sortField: ''
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'tqcdmresult',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          { title: this.$t('auth.supplierName'), field: 'supplierName', visible: true, width: 220 },
          { title: this.$t('auth.authenticationDocumentCode'), field: 'taskNo', visible: true, width: 220 },
          { title: this.$t('scar.completionDate'), field: 'taskCompleteTime',
            formatter: ({ cellValue }) => cellValue ? parseTime(cellValue, '{y}-{m}-{d}') : '',
            visible: true, width: 135 },
          { title: this.$t('tqcdm.abilitySatisfaction'), slots: { default: 'capacityPercent' }, field: 'capacityPercent', visible: true, width: 180 },
          { title: this.$t('material.category'), slots: { default: 'evaluateCategoryId' }, field: 'evaluateCategoryId', visible: true, width: 220 },
          { title: this.$t('tqcdm.evaluationTemplate'), field: 'evaluateTemplateName', visible: true, width: 220 },
          { title: this.$t('order.edition'), field: 'evaluateTemplateVersion', visible: true, width: 175 },
          { title: this.$t('tqcdm.evaluationSummaryOpinion'), field: 'evaluateSummary', visible: true, width: 260 },
          { title: this.$t('order.company'), slots: { default: 'companyId' }, field: 'companyId', visible: true, width: 220 },
          { field: 'operate', slots: { default: 'operate' }, fixed: 'left', title: this.$t('common.operate'), visible: true, showOverflow: false, width: 35 }
        ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      }
    }
  },
  mounted() {
    this.doSearch()
    this.getCategories()
    this.doGetDropdownTemplateList()
  },
  methods: {
    // 获取下拉的评估模板集合
    doGetDropdownTemplateList() {
      pageTqcdmTemplate({
        pageNo: null,
        pageSize: null
      }).then(res => {
        this.templateConfigList = res.data.list?.map(i => {
          return {
            id: i.versionId,
            name: i.templateName + '; 版本：' + i.version
          }
        })
      })
    },
    getDictDatas,
    // 搜索接口
    doSearch() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      if (this.queryParams.category) {
        this.queryParams.categoryIds = this.queryParams.category.map(item => item?.at(-1))
      }

      pageEvaluateResultRecord(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      }).catch(_ => {

      })
    },
    // 单据列表重置并搜索
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        searchText: '', // 顶部模糊搜索
        taskNo: '', // 单据号
        taskName: '', // 单据名称
        companyId: '', // 公司
        status: [], // 状态
        creator: '', // 创建人
        currentProcessor: [], // 当前办理人
        dateType: '', // 时间类型
        time: [], // 时间
        supplierName: '', // 供应商名称
        category: [],
        sortBy: '',
        sortField: ''
      }
      this.doSearch()
    },
    // 导出单据数据
    async doExportPageRecord(test) {
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      if (this.queryParams.category) {
        this.queryParams.categoryIds = this.queryParams.category.map(item => item?.at(-1))
      }

      this.loadingButton = true
      const res = await exportEvaluateResultRecord(this.queryParams)
      this.$download.excel(res, this.$t('tqcdm.listOfSupplierCapabilityMatrixEvaluationResults'))
      this.loadingButton = false
    },
    // 获取品类
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    // 能力满足度颜色
    satisfaction(color) {
      if (color === 'Red') {
        return 'unsatisfactory-selected'
      } else if (color === 'Yellow') {
        return 'portion-selected'
      } else {
        return 'fulfilled-selected'
      }
    }
  }
}

</script>

<style scoped lang="scss">

.circle {
  margin: 3px;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background-color: white;
  font-size: 20px;
}

.fulfilled{
  //border: 2px solid #27ae60;
  &-selected{
    background-color: #67C23A;
  }
}

.portion{
  //border: 2px solid #ff8f3b;
  &-selected{
    background-color: #FF994F;
  }
}

.unsatisfactory{
  //border: 2px solid #FA5151;
  &-selected{
    background-color: #FA5151;
  }
}
.default{
  border: 2px solid #939393;
  //background-color: #F9F9F9;
}
.searchItem{
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 170px);
  }
}
.searchValue{
  width: 95%;
}
</style>
