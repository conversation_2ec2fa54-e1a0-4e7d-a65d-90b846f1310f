<template>
  <div class="app-container">

    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ '请选择供应商' }}
        <slot name="header" />
      </div>
      <el-select v-model="selectedSupplierId">
        <el-option
          v-for="(item, index) in mockSupplierList"
          :key="index"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-card>

    <el-card v-if="selectedSupplierId" class="commonCard">
      <div slot="header" class="mainTab">
        {{ '项目维度单人多组聊天' }}
        <slot name="header" />
      </div>
      <el-descriptions title="聊天信息">
        <el-descriptions-item :label="'甲方: '" :span="4">
          {{ $store.getters.nickname }}
        </el-descriptions-item>
        <el-descriptions-item :label="'供应商: '" :span="4">
          {{ mockSupplierList.filter(i => i.id === selectedSupplierId)[0].name }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card v-if="selectedSupplierId" class="commonCard">
      <div slot="header" class="mainTab">
        {{ 'ws聊天' }}
        <slot name="header" />
      </div>

      <el-descriptions title="聊天设置" :column="1" border>
        <el-descriptions-item label="WebSocket连接地址">
          <el-tag size="small"> {{ socketUrl }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作">
          <el-button type="primary" @click="connect" :disabled="ws&&ws.readyState===1">
            {{ ws && ws.readyState === 1 ? "已连接" : "连接" }}
          </el-button>
          <el-button type="danger" @click="doExit">断开</el-button>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card>
      <div slot="header" class="mainTab">
        {{ '聊天内容' }}
        <slot name="header" />
      </div>
      <el-form label-width="120px">
        <el-form-item label="发送内容" size="small">
          <el-input v-model="sentMsg" type="textarea" :rows="5"/>
        </el-form-item>
        <el-form-item label="" size="small">
          <el-button type="success" @click="send">发送消息</el-button>
        </el-form-item>
        <el-form-item label="接收内容" size="small">
          <el-input v-model="acceptMsg" type="textarea" :rows="12" disabled/>
        </el-form-item>
        <el-form-item label="" size="small">
          <el-button type="info" @click="acceptMsg=''">清空消息</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getNowDateTime } from '@/utils/ruoyi'

export default {
  data() {
    return {
      selectedSupplierId: 8,
      mockSupplierList: [
        {
          'id': 8,
          'name': '0816测试供应商03'
        },
        {
          'id': 9,
          'name': 'bruce测试供应商002'
        }
      ],
      sentMsg: '', // 发送信息
      acceptMsg: '', // 接收信息
      ws: null,
      socketUrl: ''
    }
  },
  methods: {
    // connect to ws
    connect() {
      this.socketUrl = 'ws:localhost:48080/websocket/chat' + '?userId=' + this.$store.getters.userId + '&supplierId=' + this.selectedSupplierId + '&projectId=100&is_supplier=false'
      this.acceptMsg = this.acceptMsg + '\n**********************开始尝试连接**********************\n'
      this.ws = this.createSocket(this.socketUrl)
      if (this.ws) {
        this.content = this.acceptMsg + '\n**********************连接成功**********************\n'
        this.acceptMsg = this.acceptMsg + '接收时间：' + getNowDateTime() + '\n' + event.data + '\n'
      }
      // 注册监听事件
      window.addEventListener('onmessageWS', this.getChat)
    },
    // 获取聊天信息
    getChat(data) {
      console.log('get message from websocket:>>>>>>>>', data.detail.data)
      this.acceptMsg = this.acceptMsg + data.detail.data + '\n'
    },
    // 退出连接
    doExit() {
      this.acceptMsg = this.acceptMsg + '**********************连接关闭**********************\n'
      this.exit(this.ws)
    },
    // 发送消息
    send() {
      if (!this.ws || this.ws.readyState !== 1) {
        this.$modal.msgError('未连接到服务器')
        return
      }
      if (!this.sentMsg) {
        this.$modal.msgError('请输入发送内容')
        return
      }
      this.ws.send(this.sentMsg)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

.commonCard{
  margin: 10px 0 ;
}
</style>

