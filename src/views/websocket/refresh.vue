<template>

  <div class="app-container">
    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ '即时刷新测试websocket' }}
        <slot name="header" />
      </div>
      <el-descriptions title="排名刷新历史">
        <el-descriptions-item :label="'排名'" :span="4">
          第 1 名
        </el-descriptions-item>
        <el-descriptions-item :label="'名称'" :span="4">
          {{ win.name }}
        </el-descriptions-item>
        <el-descriptions-item :label="'得分'" :span="4">
          {{ win.score }}
        </el-descriptions-item>
      </el-descriptions>

    </el-card>
    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ '数据结果' }}
        <slot name="header" />
      </div>
      <el-table v-loading="loading" :data="historyRanks">
        <el-table-column label="排名" align="center" prop="index" />
        <el-table-column label="名称" align="center" prop="name" />
        <el-table-column label="分数" align="center" prop="score" />

      </el-table>
    </el-card>
    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ '更新排名' }}
        <slot name="header" />
      </div>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="排名">
          <el-select v-model="form.index">
            <el-option
              v-for="(item, index) in indexList"
              :key="index"
              :label="'第' + item + '名'"
              :value="item"
            />

          </el-select>
        </el-form-item>
        <el-form-item label="更新得分">
          <el-input-number v-model="form.score" :max="100" />
        </el-form-item>
        <el-button @click="doUpdateScore">更新</el-button>
      </el-form>

    </el-card>
  </div>

</template>

<script>

import { updateScore } from '@/api/infra/translationer'

export default {
  name: 'Refresh',
  data() {
    return {
      // 遮罩层
      loading: true,
      historyRanks: [],
      indexList: [],
      refreshNotice: '' // 前端主动刷新列表的标识（watch 不为空时则主动刷新）
    }
  },
  mounted() {
    this.socketUrl = 'ws:localhost:48080/websocket/message' + '?userId=' + this.$store.getters.userId
    // connect to ws
    this.createSocket(this.socketUrl)
    // 注册监听事件
    window.addEventListener('onmessageWS', this.getSocketData)
  },
  watch: {
    refreshNotice: {
      handler(val) {
        if (val) {
          // todo 刷新列表
          // todo 重置标识位
          this.refreshNotice = null
        }
      }
    }
  },
  beforeDestroy() {
    // exit ws
    this.exit()
  },
  methods: {
    /**
     * 通知前端去刷新排名
     * @param refreshNotice server端通知前端页面需要刷新的标识。
     */
    getSocketData(data) {
      console.log('get message from websocket:>>>>>>>>', data.detail.data)
      this.refreshNotice = JSON.parse(data.detail.data)
    },
    reRank() {
      if (this.historyRanks) {
        this.loading = false
        this.win.name = this.historyRanks[0].name
        this.win.score = this.historyRanks[0].score
      }
    },
    doUpdateScore() {
      updateScore(this.form.index - 1, this.form.score)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

.commonCard{
  margin: 10px 0 ;
}
</style>
