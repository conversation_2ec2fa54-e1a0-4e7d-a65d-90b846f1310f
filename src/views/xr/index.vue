<template>

  <div class="container">
    <div></div>
  </div>
  <!--
该页面作为雪融单点登录的重定向中间页面
1.https://test.xuerong.com:9120/user/login?app={srm_app_id}}&redirect=https://localhost/xr/demo
1.1. 【/xr/redirect】路径对应此vue页面

2.【1】跳转至雪榕的鉴权页面登录，登录成功后返回如下地址
2.1. https://localhost/xr/redirect?token={雪榕的token}

3.从此开始就是srm开始处理的时机。拿到【2.1】处返回的token后请求srm后端的请求 【【GET】/system/oauth2/srm/token】，
由后端根据 雪榕的token请求雪榕提供的人员api，获取token对应的人员信息，之后srm根据id（工号）去system_users表去匹配，
如果存在则根据此用户生成srm系统内的token并跳转至指定的页面（暂无）即可正常使用。

4.此页面的路由注册在 router/index.js 下 可以正常执行。
TODO 雪榕对接在第【1】步返回的地址有误，其token参数是以 & 拼接，而不是？开头
经测试：需要在我们的中转地址后面随便拼接个参数，类似 /xr/demo?1=1即可。对方暂时未改。
-->
</template>

<script>

import { getSrmTokenForXuerong } from '@/api/login'

export default {
  name: 'Index',
  beforeCreate() {
    // 获取雪榕返回的token
    console.log('xrToken', this.$route.query.token)
    var xrToken = this.$route.query.token

    getSrmTokenForXuerong(xrToken).then(res => {
      location.href = res.data
    })
  }
}
</script>

<style scoped>

</style>
